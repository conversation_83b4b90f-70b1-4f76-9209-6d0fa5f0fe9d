from django import template
from decimal import Decimal
import decimal

register = template.Library()

@register.filter
def sub(value, arg):
    """Subtract the arg from the value."""
    try:
        # Convert to Decimal for precise arithmetic
        if value is None:
            value = Decimal('0')
        if arg is None:
            arg = Decimal('0')
        
        # Convert to Decimal if they're not already
        if not isinstance(value, Decimal):
            value = Decimal(str(value))
        if not isinstance(arg, Decimal):
            arg = Decimal(str(arg))
            
        return value - arg
    except (ValueError, TypeError, decimal.InvalidOperation):
        return 0

@register.filter
def add_decimal(value, arg):
    """Add two decimal values."""
    try:
        # Convert to Decimal for precise arithmetic
        if value is None:
            value = Decimal('0')
        if arg is None:
            arg = Decimal('0')
        
        # Convert to Decimal if they're not already
        if not isinstance(value, Decimal):
            value = Decimal(str(value))
        if not isinstance(arg, Decimal):
            arg = Decimal(str(arg))
            
        return value + arg
    except (ValueError, TypeError, decimal.InvalidOperation):
        return 0

@register.filter
def multiply_decimal(value, arg):
    """Multiply two decimal values."""
    try:
        # Convert to Decimal for precise arithmetic
        if value is None:
            value = Decimal('0')
        if arg is None:
            arg = Decimal('0')
        
        # Convert to Decimal if they're not already
        if not isinstance(value, Decimal):
            value = Decimal(str(value))
        if not isinstance(arg, Decimal):
            arg = Decimal(str(arg))
            
        return value * arg
    except (ValueError, TypeError, decimal.InvalidOperation):
        return 0

@register.filter
def abs_value(value):
    """Return the absolute value."""
    try:
        if value is None:
            return Decimal('0')
        if not isinstance(value, Decimal):
            value = Decimal(str(value))
        return abs(value)
    except (ValueError, TypeError, decimal.InvalidOperation):
        return 0
