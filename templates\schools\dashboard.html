{# D:\school_fees_saas_v2\templates\schools\dashboard.html #}
{% extends "tenant_base.html" %}
{% load static core_tags humanize common_tags i18n %}

{% block tenant_page_title %}{{ view_title|default:"Admin Dashboard" }}{% endblock %}

{% block extra_tenant_css %}
    {{ block.super }}
    <style>
        /* --- General Dashboard Enhancements --- */
        .dashboard-section-header {
            font-weight: 600;
            color: #495057; /* Slightly muted header color */
            margin-bottom: 1.5rem; /* Consistent spacing below section headers */
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #dee2e6;
        }

        /* --- Trial Counter Enhancements --- */
        .trial-counter {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
            color: white;
            animation: pulse-glow 2s infinite;
        }

        .trial-counter.critical {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            animation: urgent-pulse 1s infinite;
        }

        .trial-counter.warning {
            background: linear-gradient(135deg, #ffa726 0%, #ff7043 100%);
        }

        @keyframes pulse-glow {
            0%, 100% { box-shadow: 0 0 20px rgba(102, 126, 234, 0.3); }
            50% { box-shadow: 0 0 30px rgba(102, 126, 234, 0.5); }
        }

        @keyframes urgent-pulse {
            0%, 100% {
                box-shadow: 0 0 25px rgba(255, 107, 107, 0.5);
                transform: scale(1);
            }
            50% {
                box-shadow: 0 0 35px rgba(255, 107, 107, 0.8);
                transform: scale(1.02);
            }
        }

        /* --- Stat Card Enhancements --- */
        .dashboard-stat-card {
            transition: all .3s ease-in-out; /* Smoother transition */
            border: 1px solid #e9ecef; /* Softer border */
            border-left-width: 5px !important; /* Prominent accent */
            border-radius: 0.375rem; /* Bootstrap's default card radius */
            margin-bottom: 1.5rem; /* Consistent with other cards */
        }
        .dashboard-stat-card:hover {
            transform: translateY(-4px) scale(1.01); /* More noticeable hover */
            box-shadow: 0 .8rem 1.5rem rgba(0,0,0,.1) !important;
        }
        .dashboard-stat-card .card-body {
            padding: 1.5rem; /* More internal padding */
        }
        .stat-icon {
            font-size: 2.2rem; /* Slightly smaller for balance */
            opacity: 0.6;
            width: 50px; /* Fixed width for alignment */
            height: 50px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background-color: rgba(0,0,0,0.05); /* Subtle background for icon */
        }
        .stat-value {
            font-size: 2.25rem; /* Larger value */
            font-weight: 600; /* Slightly less bold for modern feel */
            color: var(--bs-body-color);
        }
        .stat-label {
            font-size: 0.8rem;
            color: #6c757d;
            text-transform: uppercase;
            letter-spacing: 0.75px; /* More letter spacing */
        }
        .dashboard-stat-card .card-footer {
            font-size: 0.8rem;
            background-color: transparent; /* Cleaner footer */
            border-top: 1px solid #e9ecef;
            padding: 0.75rem 1.25rem;
            font-weight: 500;
        }
        .dashboard-stat-card .card-footer:hover {
            background-color: rgba(0,0,0,0.03);
        }
        .dashboard-stat-card .stretched-link::after { /* Ensure hover covers card */
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
            z-index: 1;
            content: "";
        }
        
        /* Specific Stat Card Colors (using Bootstrap variable shades if possible) */
        .dashboard-stat-card.border-primary .stat-icon { color: var(--bs-primary); background-color: var(--bs-primary-bg-subtle); }
        .dashboard-stat-card.border-danger  .stat-icon { color: var(--bs-danger);  background-color: var(--bs-danger-bg-subtle);  }
        .dashboard-stat-card.border-success .stat-icon { color: var(--bs-success); background-color: var(--bs-success-bg-subtle); }
        .dashboard-stat-card.border-info    .stat-icon { color: var(--bs-info);    background-color: var(--bs-info-bg-subtle);    }


        /* --- Quick Actions List --- */
        .quick-actions-list .list-group-item {
            border-left-width: 0px; /* Remove default, use hover effect instead */
            transition: all 0.2s ease-in-out;
            font-weight: 500;
        }
        .quick-actions-list .list-group-item:hover {
            background-color: var(--bs-primary-bg-subtle);
            color: var(--bs-primary-text-emphasis);
            padding-left: 1.5rem; /* Indent on hover */
        }
        .quick-action-icon {
            font-size: 1.2rem;
            width: 28px; /* Align icons */
            margin-right: 0.75rem !important;
        }

        /* --- Management Cards (Announcements, Activities, Alerts) --- */
        .management-card .card-header {
            background-color: transparent; /* Cleaner header */
            border-bottom: 1px solid #e9ecef;
            padding: 0.75rem 1.25rem;
            font-weight: 600;
        }
        .management-card .card-body {
            padding: 0.5rem 0; /* Adjust if list group handles padding */
        }
        .management-card .list-group-item {
            border-left: 0;
            border-right: 0;
        }
        .management-card .card-footer {
            background-color: transparent;
            border-top: 1px solid #e9ecef;
        }
        .activity-list .list-group-item, .alerts-list .list-group-item {
            padding-top: 0.75rem;
            padding-bottom: 0.75rem;
        }
        .activity-list .fw-bold { font-size: 0.9rem; }
        .alerts-list .alert {
            border-radius: 0.25rem; /* Softer radius for inline alerts */
        }
        .page-header { /* Replaces the border-bottom on the h1/p block */
            padding-bottom: 1rem;
            margin-bottom: 1.5rem !important;
            border-bottom: 1px solid #dee2e6;
        }
    </style>
{% endblock %}

{% block tenant_specific_content %}
<div class="container-fluid"> {# /* Using container-fluid as in your template */ #}
    
    <div class="d-flex justify-content-between align-items-center pt-3 pb-2 mb-4 page-header flex-wrap">
        <div>
            <h1 class="h2 mb-0">{{ view_title|default:"Dashboard" }}</h1>
            <p class="text-muted small mb-0">Welcome, {{ request.user.get_full_name|default:request.user.email }}! Overview for {{ request.tenant.name }}.</p>
        </div>
        {% if school_profile and perms.schools.change_schoolprofile %}
            <a href="{% url 'schools:profile_update' %}" class="btn btn-outline-secondary btn-sm mt-2 mt-md-0">
                <i class="bi bi-pencil-square me-1"></i> Edit School Profile
            </a>
        {% endif %}
    </div>

    {% include "partials/_messages.html" %}

    {# --- Trial Period Counter --- #}
    {% if trial_active %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="alert alert-{% if trial_urgency == 'critical' %}danger{% elif trial_urgency == 'warning' %}warning{% elif trial_urgency == 'caution' %}info{% else %}success{% endif %} border-0 shadow-sm">
                <div class="d-flex align-items-center">
                    <div class="me-3">
                        <i class="bi bi-{% if trial_urgency == 'critical' %}exclamation-triangle-fill{% elif trial_urgency == 'warning' %}clock-fill{% else %}info-circle-fill{% endif %} fs-2"></i>
                    </div>
                    <div class="flex-grow-1">
                        <h5 class="alert-heading mb-1">
                            {% if trial_urgency == 'critical' %}
                                🚨 Trial Ending Soon!
                            {% elif trial_urgency == 'warning' %}
                                ⚠️ Trial Period Notice
                            {% else %}
                                ℹ️ Trial Period Active
                            {% endif %}
                        </h5>
                        <p class="mb-2">
                            {% if trial_days_remaining > 0 %}
                                Your trial expires in <strong>{{ trial_days_remaining }} day{{ trial_days_remaining|pluralize }}</strong>
                                {% if trial_days_remaining <= 1 and trial_hours_remaining > 0 %}
                                    and <strong>{{ trial_hours_remaining }} hour{{ trial_hours_remaining|pluralize }}</strong>
                                {% endif %}
                            {% else %}
                                Your trial expires in <strong>{{ trial_hours_remaining }} hour{{ trial_hours_remaining|pluralize }}</strong>
                            {% endif %}
                            ({{ trial_end_date|date:"M d, Y \a\t g:i A" }})
                        </p>
                        {% if trial_urgency == 'critical' or trial_urgency == 'warning' %}
                        <div class="d-flex gap-2">
                            <a href="{% url 'subscriptions:plan_selection' %}" class="btn btn-{% if trial_urgency == 'critical' %}danger{% else %}warning{% endif %} btn-sm">
                                <i class="bi bi-credit-card me-1"></i>Upgrade Now
                            </a>
                            <a href="{% url 'subscriptions:pricing_page' %}" class="btn btn-outline-secondary btn-sm">
                                <i class="bi bi-info-circle me-1"></i>View Plans
                            </a>
                        </div>
                        {% endif %}
                    </div>
                    <div class="text-end">
                        <div class="fs-1 fw-bold">{{ trial_days_remaining }}</div>
                        <small class="text-muted">day{{ trial_days_remaining|pluralize }} left</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% elif trial_expired %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="alert alert-danger border-0 shadow-sm">
                <div class="d-flex align-items-center">
                    <div class="me-3">
                        <i class="bi bi-exclamation-triangle-fill fs-2"></i>
                    </div>
                    <div class="flex-grow-1">
                        <h5 class="alert-heading mb-1">🚨 Trial Period Expired</h5>
                        <p class="mb-2">Your trial period has ended. Please upgrade to continue using all features.</p>
                        <div class="d-flex gap-2">
                            <a href="{% url 'subscriptions:plan_selection' %}" class="btn btn-danger btn-sm">
                                <i class="bi bi-credit-card me-1"></i>Upgrade Now
                            </a>
                            <a href="mailto:<EMAIL>" class="btn btn-outline-secondary btn-sm">
                                <i class="bi bi-question-circle me-1"></i>Contact Support
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    {# --- Quick Stats Section --- #}
    {% if stats %}
    <h2 class="mb-3 h4 dashboard-section-header">{% trans "Key Statistics" %}</h2>
    <div class="row g-3 mb-4">
        <div class="col-sm-6 col-lg-3"> {# Changed to col-lg-3 for 4 cards #}
            <div class="card dashboard-stat-card border-primary h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="stat-icon me-3"><i class="bi bi-people-fill"></i></div>
                        <div>
                            <div class="stat-value">{{ stats.active_students|default:"0"|intcomma }}</div>
                            <div class="stat-label">{% trans "Active Students" %}</div>
                        </div>
                    </div>
                </div>
                {% if perms.students.view_student %}
                    <a href="{% url 'students:student_list' %}" class="card-footer text-primary stretched-link text-decoration-none">View Students <i class="bi bi-arrow-right-short"></i></a>
                {% endif %}
            </div>
        </div>
        <div class="col-sm-6 col-lg-3">
            <div class="card dashboard-stat-card border-danger h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="stat-icon me-3"><i class="bi bi-file-earmark-ruled-fill"></i></div>
                        <div>
                            <div class="stat-value">{{ stats.due_invoices_count|default:"0"|intcomma }}</div>
                            <div class="stat-label">{% trans "Open Invoices" %}</div>
                            <small class="text-muted d-block mt-1">Total Due: {{ school_profile.currency_symbol|default:'$' }}{{ stats.due_invoices_total|default:"0.00"|floatformat:2|intcomma }}</small>
                        </div>
                    </div>
                </div>
                {% if perms.reporting.view_outstanding_fees_report %}
                    <a href="{% url 'reporting:outstanding_fees_report' %}?status=SENT&status=PARTIALLY_PAID&status=OVERDUE" class="card-footer text-danger stretched-link text-decoration-none">View Outstanding <i class="bi bi-arrow-right-short"></i></a>
                {% endif %}
            </div>
        </div>
        <div class="col-sm-6 col-lg-3">
            <div class="card dashboard-stat-card border-success h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="stat-icon me-3"><i class="bi bi-wallet2"></i></div>
                        <div>
                            <div class="stat-value">{{ school_profile.currency_symbol|default:'$' }}{{ stats.collections_this_month|default:"0.00"|floatformat:2|intcomma }}</div>
                            <div class="stat-label">{% trans "Collections (This Month)" %}</div>
                        </div>
                    </div>
                </div>
                {% if perms.reporting.view_collection_report %}
                    <a href="{% url 'reporting:collection_report' %}" class="card-footer text-success stretched-link text-decoration-none">View Collections <i class="bi bi-arrow-right-short"></i></a>
                {% endif %}
            </div>
        </div>
        <div class="col-sm-6 col-lg-3">
            <div class="card dashboard-stat-card border-info h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="stat-icon me-3"><i class="bi bi-person-workspace"></i></div>
                        <div>
                            <div class="stat-value">{{ stats.active_staff_count|default:"0"|intcomma }}</div>
                            <div class="stat-label">{% trans "Active Staff" %}</div>
                        </div>
                    </div>
                </div>
                {% if perms.schools.view_staffuser %}
                    <a href="{% url 'schools:staff_list' %}" class="card-footer text-info stretched-link text-decoration-none">Manage Staff <i class="bi bi-arrow-right-short"></i></a>
                {% endif %}
            </div>
        </div>
    </div>
    {% else %}
        <div class="alert alert-light text-muted border">Dashboard statistics are currently loading or unavailable.</div>
    {% endif %}

    <div class="row">
        {# Column 1: Announcements & Quick Actions #}
        <div class="col-lg-7 mb-4">
            <!-- Recent Announcements Card -->
            <div class="card shadow-sm mb-4 dashboard-card">
                <div class="card-header management-card">
                    <h5 class="mb-0"><i class="bi bi-megaphone-fill me-2 text-primary"></i>{% trans "Recent Announcements" %}</h5>
                </div>
                {% if recent_announcements %}
                    <div class="list-group list-group-flush">
                        {% for announcement in recent_announcements %}
                        <a href="{% url 'announcements:detail' pk=announcement.pk %}" class="list-group-item list-group-item-action py-3">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1 fw-semibold">{{ announcement.title|truncatechars:70 }}</h6>
                                <small class="text-muted flex-shrink-0 ms-2">{{ announcement.publish_date|naturaltime }}</small>
                            </div>
                            <small class="mb-1 text-muted d-block">{{ announcement.content|striptags|truncatewords:18 }}</small>
                        </a>
                        {% endfor %}
                    </div>
                    {% if recent_announcements.count > 0 %} {# Only show if there are any recent announcements fetched #}
                    <div class="card-footer text-center">
                        <a href="{% url 'announcements:list' %}" class="text-decoration-none">{% trans "View All Announcements" %} <i class="bi bi-chevron-right small"></i></a>
                    </div>
                    {% endif %}
                {% else %}
                    <div class="card-body text-center text-muted p-4">
                        <i class="bi bi-info-circle fs-3 d-block mb-2"></i>
                        {% trans "No recent announcements." %}
                    </div>
                {% endif %}
            </div>

            <!-- Quick Actions -->
            <h2 class="mb-3 h4 dashboard-section-header">{% trans "Quick Actions" %}</h2>
            <div class="list-group shadow-sm quick-actions-list">
                {% if perms.students.add_student %}
                    <a href="{% url 'students:student_create' %}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center py-3">
                        <span><i class="bi bi-person-plus-fill quick-action-icon text-primary"></i>Add New Student</span>
                        <i class="bi bi-chevron-right text-muted small"></i>
                    </a>
                {% endif %}
                {% if perms.payments.add_payment %}
                    <a href="{% url 'payments:record_payment' %}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center py-3">
                        <span><i class="bi bi-cash-coin quick-action-icon text-success"></i>Record Payment Received</span>
                        <i class="bi bi-chevron-right text-muted small"></i>
                    </a>
                {% endif %}
                {% if perms.fees.add_invoice %}
                    <a href="{% url 'fees:invoice_create' %}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center py-3">
                        <span><i class="bi bi-receipt-cutoff quick-action-icon text-info"></i>Create Invoice</span>
                        <i class="bi bi-chevron-right text-muted small"></i>
                    </a>
                {% endif %}
                {% if perms.finance.add_expense %}
                    <a href="{% url 'finance:expense_create' %}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center py-3">
                        <span><i class="bi bi-cart-dash-fill quick-action-icon text-danger"></i>Record Expense</span>
                        <i class="bi bi-chevron-right text-muted small"></i>
                    </a>
                {% endif %}
                {% if perms.hr.add_leaverequest and request.user|has_group:"Teacher,Staff" %}
                    <a href="{% url 'hr:staff_leaverequest_apply' %}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center py-3">
                        <span><i class="bi bi-calendar-plus-fill quick-action-icon text-warning"></i>Apply for Leave</span>
                        <i class="bi bi-chevron-right text-muted small"></i>
                    </a>
                {% endif %}
                {% if not perms.students.add_student and not perms.fees.add_invoice and not perms.payments.add_payment and not perms.finance.add_expense %}
                    <div class="list-group-item text-muted py-3 text-center">No quick actions available for your current role.</div>
                {% endif %}
            </div>
        </div>

        {# Column 2: Recent Activities & Alerts #}
        <div class="col-lg-5 mb-4">
            <div class="card shadow-sm mb-4 dashboard-card">
                <div class="card-header management-card">
                    <h5 class="mb-0"><i class="bi bi-activity me-2"></i>{% trans "Recent Activities" %}</h5>
                </div>
                <div class="card-body p-0">
                    {% if recent_activities %}
                        <ul class="list-group list-group-flush activity-list">
                            {% for activity in recent_activities %}
                                <li class="list-group-item py-2 px-3">
                                    <div class="fw-bold small">{{ activity.description|truncatechars:75 }}</div>
                                    <small class="text-muted">
                                        {% if activity.actor %}{{ activity.actor.get_full_name|default:activity.actor.email|truncatechars:20 }} - {% endif %}
                                        {{ activity.timestamp|naturaltime }}
                                        {% if activity.target and activity.target_url and activity.target_url != "#" %}
                                            (Related: <a href="{{ activity.target_url }}" class="text-decoration-none">{{ activity.target_str|truncatechars:25 }}</a>)
                                        {% elif activity.target %}
                                            (Related: {{ activity.target_str|truncatechars:25 }})
                                        {% endif %}
                                    </small>
                                </li>
                            {% endfor %}
                        </ul>
                    {% else %}
                        <div class="card-body text-center text-muted p-4"><i class="bi bi-info-circle fs-3 d-block mb-2"></i>No recent activities to display.</div>
                    {% endif %}
                </div>
                {% if recent_activities.count > 0 and perms.portal_admin.view_adminactivitylog %}
                <div class="card-footer text-center">
                    <a href="{% url 'portal_admin:activity_log_list' %}" class="text-decoration-none">{% trans "View Full Activity Log" %} <i class="bi bi-chevron-right small"></i></a>
                </div>
                {% endif %}
            </div>
            
            <div class="card shadow-sm dashboard-card">
                <div class="card-header management-card">
                    <h5 class="mb-0"><i class="bi bi-bell-fill me-2 text-danger"></i>{% trans "Alerts & System Notifications" %}</h5>
                </div>
                <div class="card-body">
                    {% if alerts %}
                        <ul class="list-group list-group-flush alerts-list px-0">
                        {% for alert in alerts %}
                            <li class="list-group-item px-0 py-1"> {# Reduced padding for list items #}
                                <div class="alert alert-{{ alert.level_tag|default:'warning' }} py-2 px-3 d-flex align-items-center mb-1 fs-sm" role="alert">
                                    <i class="bi bi-{{ alert.icon_class|default:'exclamation-triangle-fill' }} me-2 fs-5"></i>
                                    <div>
                                        {{ alert.message|safe }}
                                        {% if alert.timestamp %}<br><small class="text-muted">{{ alert.timestamp|timesince }} ago</small>{% endif %}
                                    </div>
                                </div>
                            </li>
                        {% endfor %}
                        </ul>
                    {% else %}
                        <p class="text-muted p-3 text-center">No current alerts or system notifications.</p>
                    {% endif %}
                </div>
                {% if perms.communication.view_notification %} 
                <div class="card-footer text-center">
                    <a href="#" class="text-decoration-none disabled">View All Notifications (TBD)</a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    <div style="height: 60px;"></div> {# Extra space before fixed footer #}
</div>
{% endblock tenant_specific_content %}












{% comment %} {# D:\school_fees_saas_v2\templates\schools\dashboard.html #}
{% extends "tenant_base.html" %}

{% load static core_tags humanize common_tags i18n %} 

{% block tenant_page_title %}{{ view_title|default:"Dashboard" }}{% endblock %}

{% block extra_tenant_css %}
    {{ block.super }}
    <style>
        .dashboard-stat-card {
            transition: transform .2s ease-out, box-shadow .2s ease-out;
            border-left-width: 4px !important; /* Ensure high specificity */
            margin-bottom: 1.5rem;
        }
        .dashboard-stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 .5rem 1rem rgba(0,0,0,.15)!important;
        }
        .stat-icon { font-size: 2.5rem; /* Slightly larger icon */ opacity: 0.7; }
        .stat-value { font-size: 2rem; font-weight: 700; }
        .stat-label { font-size: 0.9rem; color: #6c757d; text-transform: uppercase; letter-spacing: 0.5px; }
        .card-footer { font-size: 0.85rem; background-color: rgba(0,0,0,0.02) !important; }
        .card-footer:hover { background-color: rgba(0,0,0,0.05) !important; }
        
        .quick-actions-list .list-group-item { border-left-width: 4px; border-left-color: transparent; }
        .quick-actions-list .list-group-item:hover { border-left-color: var(--bs-primary); background-color: #f8f9fa; }
        .quick-action-icon { font-size: 1.3rem; width: 30px; /* Align icons */ }

        .management-card .card-header { background-color: #f8f9fa; border-bottom: 1px solid #e9ecef;}
        .activity-list .list-group-item, .alerts-list .list-group-item { padding-left:0; padding-right:0; }
    </style>
{% endblock %}

{% block tenant_specific_content %} {# Or block content if your tenant_base provides main-content-area wrapper inside 'content' #}
<div class="container-fluid"> {# Using container-fluid as in your template #}
    
    <div class="d-flex justify-content-between align-items-center pt-3 pb-2 mb-3 border-bottom flex-wrap">
        <div>
            <h1 class="h2 mb-0">{{ view_title|default:"Dashboard" }}</h1>
            <p class="text-muted small mb-0">Welcome, {{ request.user.get_full_name|default:request.user.email }}! Overview for {{ request.tenant.name }}.</p>
        </div>
        {# school_profile is available via request.tenant.schoolprofile from context processor #}
        {% if school_profile and perms.schools.change_schoolprofile %}
            <a href="{% url 'schools:profile_update' %}" class="btn btn-outline-secondary btn-sm mt-2 mt-md-0">
                <i class="bi bi-pencil-square me-1"></i> Edit School Profile
            </a>
        {% endif %}
    </div>

    {% include "partials/_messages.html" %} {# Assumes _messages.html is in a 'partials' dir in one of your template DIRS #}

    {# --- Quick Stats Section --- #}
    {% if stats %}
    <div class="row g-3 mb-4">
        <div class="col-sm-6 col-xl-3">
            <div class="card dashboard-stat-card border-primary h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="stat-icon text-primary me-3"><i class="bi bi-people-fill"></i></div>
                        <div>
                            <div class="stat-value">{{ stats.active_students|default:"0"|intcomma }}</div>
                            <div class="stat-label">{% trans "Active Students" %}</div>
                        </div>
                    </div>
                </div>
                {% if perms.students.view_student %}
                    <a href="{% url 'students:student_list' %}" class="card-footer text-primary stretched-link text-decoration-none">View Students <i class="bi bi-arrow-right-short"></i></a>
                {% endif %}
            </div>
        </div>
        <div class="col-sm-6 col-xl-3">
            <div class="card dashboard-stat-card border-danger h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="stat-icon text-danger me-3"><i class="bi bi-file-earmark-ruled-fill"></i></div>
                        <div>
                            <div class="stat-value">{{ stats.due_invoices_count|default:"0"|intcomma }}</div>
                            <div class="stat-label">{% trans "Open Invoices" %}</div>
                            <small class="text-muted d-block">Total Due: {{ school_profile.currency_symbol|default:'$' }}{{ stats.due_invoices_total|default:"0.00"|floatformat:2|intcomma }}</small>
                        </div>
                    </div>
                </div>
                {% if perms.reporting.view_outstanding_fees_report %}
                    <a href="{% url 'reporting:outstanding_fees_report' %}?status=SENT&status=PARTIALLY_PAID&status=OVERDUE" class="card-footer text-danger stretched-link text-decoration-none">View Outstanding <i class="bi bi-arrow-right-short"></i></a>
                {% endif %}
            </div>
        </div>
        <div class="col-sm-6 col-xl-3">
            <div class="card dashboard-stat-card border-success h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="stat-icon text-success me-3"><i class="bi bi-wallet2"></i></div>
                        <div>
                            <div class="stat-value">{{ school_profile.currency_symbol|default:'$' }}{{ stats.collections_this_month|default:"0.00"|floatformat:2|intcomma }}</div>
                            <div class="stat-label">{% trans "Collections (This Month)" %}</div>
                        </div>
                    </div>
                </div>
                {% if perms.reporting.view_collection_report %}
                    <a href="{% url 'reporting:collection_report' %}" class="card-footer text-success stretched-link text-decoration-none">View Collections <i class="bi bi-arrow-right-short"></i></a>
                {% endif %}
            </div>
        </div>
        <div class="col-sm-6 col-xl-3">
            <div class="card dashboard-stat-card border-info h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="stat-icon text-info me-3"><i class="bi bi-person-workspace"></i></div>
                        <div>
                            <div class="stat-value">{{ stats.active_staff_count|default:"0"|intcomma }}</div> {# Add 'active_staff_count' to view context #}
                            <div class="stat-label">{% trans "Active Staff" %}</div>
                        </div>
                    </div>
                </div>
                {% if perms.schools.view_staffuser %}
                    <a href="{% url 'schools:staff_list' %}" class="card-footer text-info stretched-link text-decoration-none">Manage Staff <i class="bi bi-arrow-right-short"></i></a>
                {% endif %}
            </div>
        </div>
    </div>
    {% else %}
        <div class="alert alert-light text-muted border">Dashboard statistics are currently loading or unavailable.</div>
    {% endif %}

    <div class="row">
        {# Column 1: Announcements & Quick Actions #}
        <div class="col-lg-7 mb-4">
            <!-- Recent Announcements Card -->
            <div class="card shadow-sm mb-4 dashboard-card">
                <div class="card-header management-card">
                    <h5 class="mb-0"><i class="bi bi-megaphone-fill me-2 text-primary"></i>{% trans "Recent Announcements" %}</h5>
                </div>
                {% if recent_announcements %}
                    <div class="list-group list-group-flush">
                        {% for announcement in recent_announcements %}
                        <a href="{% url 'announcements:detail' pk=announcement.pk %}" class="list-group-item list-group-item-action">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">{{ announcement.title|truncatechars:70 }}</h6>
                                <small class="text-muted">{{ announcement.publish_date|naturaltime }}</small>
                            </div>
                            <small class="mb-1 text-muted d-block">{{ announcement.content|striptags|truncatewords:15 }}</small>
                        </a>
                        {% endfor %}
                    </div>
                    {% if recent_announcements.count > 0 %}
                    <div class="card-footer text-center bg-light">
                        <a href="{% url 'announcements:list' %}">{% trans "View All Announcements" %} »</a>
                    </div>
                    {% endif %}
                {% else %}
                    <div class="card-body text-center text-muted p-4">
                        <i class="bi bi-info-circle fs-3 d-block mb-2"></i>
                        {% trans "No recent announcements." %}
                    </div>
                {% endif %}
            </div>

            <!-- Quick Actions -->
            <div class="card shadow-sm dashboard-card">
                <div class="card-header management-card">
                    <h5 class="mb-0"><i class="bi bi-lightning-fill me-2 text-success"></i>{% trans "Quick Actions" %}</h5>
                </div>
                <div class="list-group list-group-flush quick-actions-list">
                    {% if perms.students.add_student %}
                        <a href="{% url 'students:student_create' %}" class="list-group-item list-group-item-action py-3"><i class="bi bi-person-plus-fill quick-action-icon text-primary me-2"></i>Add New Student</a>
                    {% endif %}
                    {% if perms.payments.add_payment %}
                        <a href="{% url 'payments:record_payment' %}" class="list-group-item list-group-item-action py-3"><i class="bi bi-cash-coin quick-action-icon text-success me-2"></i>Record Payment Received</a>
                    {% endif %}
                    {% if perms.fees.add_invoice %}
                        <a href="{% url 'fees:invoice_create' %}" class="list-group-item list-group-item-action py-3"><i class="bi bi-receipt-cutoff quick-action-icon text-info me-2"></i>Create Invoice</a>
                    {% endif %}
                    {% if perms.finance.add_expense %}
                        <a href="{% url 'finance:expense_create' %}" class="list-group-item list-group-item-action py-3"><i class="bi bi-cart-dash-fill quick-action-icon text-danger me-2"></i>Record Expense</a>
                    {% endif %}
                    {% if perms.hr.add_leaverequest and request.user|has_group:"Teacher,Staff" %} {# Example for multiple groups #}
                        <a href="{% url 'hr:staff_leaverequest_apply' %}" class="list-group-item list-group-item-action py-3"><i class="bi bi-calendar-plus-fill quick-action-icon text-warning me-2"></i>Apply for Leave</a>
                    {% endif %}
                    {% if not perms.students.add_student and not perms.fees.add_invoice and not perms.payments.add_payment and not perms.finance.add_expense %}
                        <div class="list-group-item text-muted py-3">No quick actions available for your role.</div>
                    {% endif %}
                </div>
            </div>
        </div>

        {# Column 2: Recent Activities & Alerts #}
        <div class="col-lg-5 mb-4">
            <div class="card shadow-sm mb-4 dashboard-card h-100"> {# Try to make cards in this column same height #}
                <div class="card-header management-card">
                    <h5 class="mb-0"><i class="bi bi-activity me-2"></i>{% trans "Recent Activities" %}</h5>
                </div>
                <div class="card-body p-0">
                    {% if recent_activities %}
                        <ul class="list-group list-group-flush activity-list">
                            {% for activity in recent_activities %}
                                <li class="list-group-item py-2">
                                    <div class="fw-bold small">{{ activity.description|truncatechars:80 }}</div>
                                    <small class="text-muted">
                                        {% if activity.actor %}{{ activity.actor.get_full_name|default:activity.actor.email|truncatechars:20 }} - {% endif %}
                                        {{ activity.timestamp|naturaltime }}
                                        {% if activity.target and activity.target_url and activity.target_url != "#" %}
                                            (Related: <a href="{{ activity.target_url }}" class="text-decoration-none">{{ activity.target_str|truncatechars:30 }}</a>)
                                        {% elif activity.target %}
                                            (Related: {{ activity.target_str|truncatechars:30 }})
                                        {% endif %}
                                    </small>
                                </li>
                            {% endfor %}
                        </ul>
                    {% else %}
                        <div class="p-3 text-muted text-center"><i class="bi bi-info-circle fs-3 d-block mb-2"></i>No recent activities.</div>
                    {% endif %}
                </div>
                {% if recent_activities and perms.portal_admin.view_adminactivitylog %}
                <div class="card-footer text-center bg-light">
                    <a href="{% url 'portal_admin:activity_log_list' %}" class="btn btn-sm btn-outline-secondary">{% trans "View Full Activity Log" %}</a>
                </div>
                {% endif %}
            </div>
            
            {# Alerts & Notifications Card - This is a placeholder for now #} 
            <div class="card shadow-sm dashboard-card">
                <div class="card-header management-card">
                    <h5 class="mb-0"><i class="bi bi-bell-fill me-2 text-danger"></i>{% trans "Alerts & Notifications" %}</h5>
                </div>
                <div class="card-body">
                    {% if alerts %}
                        <ul class="list-group list-group-flush alerts-list">
                        {% for alert in alerts %}
                            <li class="list-group-item">
                                <div class="alert alert-{{ alert.level_tag|default:'warning' }} py-2 px-3 d-flex align-items-center mb-1" role="alert">
                                    <i class="bi bi-{{ alert.icon_class|default:'exclamation-triangle-fill' }} me-2 fs-5"></i>
                                    <div>
                                        {{ alert.message|safe }}
                                        {% if alert.timestamp %}<br><small class="text-muted">{{ alert.timestamp|timesince }} ago</small>{% endif %}
                                    </div>
                                </div>
                            </li>
                        {% endfor %}
                        </ul>
                    {% else %}
                        <p class="text-muted p-3 text-center">No current alerts.</p>
                    {% endif %}
                </div>
                {% if perms.communication.view_notification %} 
                <div class="card-footer text-center bg-light">
                    <a href="#" class="btn btn-sm btn-outline-secondary disabled">View All Notifications (TBD)</a>
                </div>
                {% endif %}
            </div>
        <br/><br/><br/> <br/><br/><br/>
        </div>
    </div>

</div>
{% endblock tenant_specific_content %} {% endcomment %}


