#!/usr/bin/env python
"""
Fix Migration Dependency Issue
Fixes the hr.0013 dependency on hr.0012_add_missing_leavetype_fields_production

Usage: python fix_migration_dependency.py
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from django.db import connection
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_migration_state():
    """Check current migration state"""
    logger.info("=== CHECKING MIGRATION STATE ===")
    
    with connection.cursor() as cursor:
        # Check HR migrations
        cursor.execute("""
            SELECT name FROM django_migrations 
            WHERE app = 'hr' 
            ORDER BY applied;
        """)
        hr_migrations = [row[0] for row in cursor.fetchall()]
        
        logger.info(f"HR migrations applied: {hr_migrations}")
        
        # Check specific problematic migrations
        has_0012_production = '0012_add_missing_leavetype_fields_production' in hr_migrations
        has_0013 = '0013_add_missing_employeeprofile_fields' in hr_migrations
        
        logger.info(f"Has 0012_production: {has_0012_production}")
        logger.info(f"Has 0013: {has_0013}")
        
        return has_0012_production, has_0013

def fix_migration_dependency():
    """Fix the migration dependency issue"""
    logger.info("=== FIXING MIGRATION DEPENDENCY ===")
    
    has_0012_production, has_0013 = check_migration_state()
    
    try:
        with connection.cursor() as cursor:
            if has_0013 and not has_0012_production:
                # Case 1: 0013 is applied but 0012_production is not
                logger.info("Fixing: 0013 applied but 0012_production missing")
                
                # Mark 0012_production as applied
                cursor.execute("""
                    INSERT INTO django_migrations (app, name, applied) 
                    VALUES ('hr', '0012_add_missing_leavetype_fields_production', NOW())
                    ON CONFLICT (app, name) DO NOTHING;
                """)
                
                logger.info("✅ Marked 0012_production as applied")
                
            elif not has_0013 and not has_0012_production:
                # Case 2: Neither is applied
                logger.info("Neither migration applied - this is fine")
                
            elif has_0012_production and has_0013:
                # Case 3: Both are applied - this is correct
                logger.info("Both migrations applied - this is correct")
                
            else:
                # Case 4: 0012_production applied but not 0013
                logger.info("0012_production applied but not 0013 - this is fine")
        
        return True
        
    except Exception as e:
        logger.error(f"Failed to fix migration dependency: {e}")
        return False

def clean_old_migration_records():
    """Clean up any old migration records that might conflict"""
    logger.info("=== CLEANING OLD MIGRATION RECORDS ===")
    
    try:
        with connection.cursor() as cursor:
            # Remove old 0012 migration if it exists
            cursor.execute("""
                DELETE FROM django_migrations 
                WHERE app = 'hr' AND name = '0012_add_missing_leavetype_fields';
            """)
            
            rows_deleted = cursor.rowcount
            if rows_deleted > 0:
                logger.info(f"✅ Removed {rows_deleted} old 0012 migration record(s)")
            else:
                logger.info("No old 0012 migration records to remove")
        
        return True
        
    except Exception as e:
        logger.error(f"Failed to clean old migration records: {e}")
        return False

def verify_hr_table_structure():
    """Verify HR table has the required columns"""
    logger.info("=== VERIFYING HR TABLE STRUCTURE ===")
    
    try:
        with connection.cursor() as cursor:
            # Check if hr_leavetype table exists
            cursor.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_name = 'hr_leavetype'
                );
            """)
            
            if not cursor.fetchone()[0]:
                logger.error("❌ hr_leavetype table does not exist")
                return False
            
            # Check for required columns
            required_columns = [
                'max_days_per_year_grant',
                'accrual_frequency',
                'accrual_rate',
                'max_accrual_balance',
                'prorate_accrual'
            ]
            
            cursor.execute("""
                SELECT column_name FROM information_schema.columns 
                WHERE table_name = 'hr_leavetype'
            """)
            existing_columns = [row[0] for row in cursor.fetchall()]
            
            missing_columns = [col for col in required_columns if col not in existing_columns]
            
            if missing_columns:
                logger.warning(f"⚠️ Missing columns: {missing_columns}")
                
                # Add missing columns
                for column in missing_columns:
                    if column == 'max_days_per_year_grant':
                        cursor.execute("""
                            ALTER TABLE hr_leavetype 
                            ADD COLUMN IF NOT EXISTS max_days_per_year_grant DECIMAL(5,2) NULL
                        """)
                    elif column == 'accrual_frequency':
                        cursor.execute("""
                            ALTER TABLE hr_leavetype 
                            ADD COLUMN IF NOT EXISTS accrual_frequency VARCHAR(20) DEFAULT 'NONE' NOT NULL
                        """)
                    elif column == 'accrual_rate':
                        cursor.execute("""
                            ALTER TABLE hr_leavetype 
                            ADD COLUMN IF NOT EXISTS accrual_rate DECIMAL(5,2) DEFAULT 0.00 NOT NULL
                        """)
                    elif column == 'max_accrual_balance':
                        cursor.execute("""
                            ALTER TABLE hr_leavetype 
                            ADD COLUMN IF NOT EXISTS max_accrual_balance INTEGER NULL
                        """)
                    elif column == 'prorate_accrual':
                        cursor.execute("""
                            ALTER TABLE hr_leavetype 
                            ADD COLUMN IF NOT EXISTS prorate_accrual BOOLEAN DEFAULT TRUE NOT NULL
                        """)
                
                logger.info("✅ Added missing columns to hr_leavetype")
            else:
                logger.info("✅ All required columns exist in hr_leavetype")
        
        return True
        
    except Exception as e:
        logger.error(f"Failed to verify HR table structure: {e}")
        return False

def test_migration_consistency():
    """Test that migrations are now consistent"""
    logger.info("=== TESTING MIGRATION CONSISTENCY ===")
    
    try:
        from django.core.management import call_command
        from django.db.migrations.loader import MigrationLoader
        from django.db.migrations.executor import MigrationExecutor
        
        # Test migration loader
        loader = MigrationLoader(connection)
        executor = MigrationExecutor(connection)
        
        # This will raise an exception if migrations are inconsistent
        loader.check_consistent_history(connection)
        
        logger.info("✅ Migration history is consistent")
        return True
        
    except Exception as e:
        logger.error(f"Migration consistency test failed: {e}")
        return False

def main():
    """Run the complete migration dependency fix"""
    logger.info("=== FIXING MIGRATION DEPENDENCY ISSUE ===")
    
    steps = [
        ("Check Migration State", check_migration_state),
        ("Clean Old Migration Records", clean_old_migration_records),
        ("Fix Migration Dependency", fix_migration_dependency),
        ("Verify HR Table Structure", verify_hr_table_structure),
        ("Test Migration Consistency", test_migration_consistency),
    ]
    
    for step_name, step_func in steps:
        logger.info(f"\n--- {step_name} ---")
        try:
            result = step_func()
            if result is False:
                logger.error(f"❌ {step_name} failed")
                return False
        except Exception as e:
            logger.error(f"❌ {step_name} error: {e}")
            return False
    
    logger.info("\n🎉 MIGRATION DEPENDENCY FIXED!")
    logger.info("Registration should now work completely.")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
