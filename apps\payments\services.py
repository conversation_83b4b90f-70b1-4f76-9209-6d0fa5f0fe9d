# D:\school_fees_saas_v2\apps\payments\services.py
from decimal import Decimal
from django.db import transaction, IntegrityError
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

# Ensure correct import paths
from apps.fees.models import Invoice
from apps.payments.models import Payment, PaymentAllocation # You'll need to define these models
from apps.accounting.models import JournalEntry, JournalLine, Account # For JEs
from apps.students.models import ParentUser # Or wherever ParentUser is
# from apps.schoo

from django.views.generic.base import View # <<< --- ADD THIS IMPORT ---
from django.views.generic.edit import FormView # You used this for SelectInvoicesForPaymentView

# ... other imports like models, forms, mixins, messages, logger, timezone, Decimal ...
#from django.http import HttpResponseForbidden, HttpResponse # For other views potentially
from django.db.models import F, Q #, Sum, Coalesce, DecimalField
from decimal import Decimal
from django.contrib import messages

import logging
logger = logging.getLogger(__name__)

def get_coa_account(account_code_or_name, tenant_specific=True, tenant=None):
    """
    Helper to get a ChartOfAccount instance.
    Adjust based on how COA is scoped (schema or tenant FK).
    """
    # This is a simplified lookup. Robust lookup might involve tenant context.
    # For now, assumes direct lookup by code or name within current schema.
    try:
        if isinstance(account_code_or_name, int) or account_code_or_name.isdigit():
            return Account.objects.get(account_code=account_code_or_name)
        return Account.objects.get(name__iexact=account_code_or_name)
    except Account.DoesNotExist:
        logger.error(f"ChartOfAccount with code/name '{account_code_or_name}' not found.")
        return None
    except Account.MultipleObjectsReturned:
        logger.error(f"Multiple ChartOfAccounts found for code/name '{account_code_or_name}'. Using first by code then name.")
        acc = Account.objects.filter(account_code=account_code_or_name).first()
        if not acc:
            acc = Account.objects.filter(name__iexact=account_code_or_name).first()
        return acc


import logging
from decimal import Decimal
from django.db import transaction
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django.db.models import F, Q

# --- Import necessary models ---
from apps.students.models import ParentUser
from apps.fees.models import Invoice
from apps.payments.models import Payment, PaymentAllocation, PaymentMethod
from apps.accounting.models import JournalEntry, JournalEntryItem as JournalEntryLine, Account

logger = logging.getLogger(__name__)

@transaction.atomic
def record_parent_payment(
    *, # Use keyword-only arguments for clarity
    parent_user: ParentUser,
    invoice_pks_to_pay: list,
    amount_paid_by_parent: Decimal,
    payment_method_code: str,
    transaction_reference: str,
    payment_date: timezone.datetime.date = None,
    notes: str = None
) -> (bool, str, Payment | None):
    """
    Records a payment from a parent, allocates it to selected invoices,
    updates invoice statuses, and creates the corresponding journal entry.
    This is an atomic operation.

    Returns:
        (success: bool, message: str, payment_instance: Payment | None)
    """
    if not payment_date:
        payment_date = timezone.now().date()

    if not invoice_pks_to_pay or amount_paid_by_parent <= Decimal('0.00'):
        return False, _("No invoices were selected or the payment amount was zero."), None

    # 1. --- VALIDATE AND FETCH DATA ---

    # Fetch the PaymentMethod object from its code
    try:
        payment_method = PaymentMethod.objects.get(type=payment_method_code, is_active=True)
    except PaymentMethod.DoesNotExist:
        logger.error(f"record_parent_payment: Payment method with code '{payment_method_code}' not found or is inactive.")
        return False, _("The selected payment method is not available."), None
    except PaymentMethod.MultipleObjectsReturned:
        logger.error(f"record_parent_payment: Multiple active payment methods found for code '{payment_method_code}'. Using the first one.")
        payment_method = PaymentMethod.objects.filter(type=payment_method_code, is_active=True).first()

    # Fetch the invoices, ensuring they are valid for this parent
    children_pks = parent_user.children.values_list('pk', flat=True)
    outstanding_statuses = [Invoice.InvoiceStatus.SENT, Invoice.InvoiceStatus.PARTIALLY_PAID, Invoice.InvoiceStatus.OVERDUE]
    
    valid_invoices = Invoice.objects.filter(
        pk__in=invoice_pks_to_pay,
        student_id__in=children_pks,
        status__in=outstanding_statuses
    ).order_by('due_date', 'pk') # Pay oldest due first

    if not valid_invoices.exists():
        logger.warning(f"No valid outstanding invoices found for parent {parent_user.email} from PKS {invoice_pks_to_pay}.")
        return False, _("The selected invoices are no longer available for payment."), None

    # Cap payment amount at what is actually due for the selected invoices
    total_due_for_selection = sum(inv.balance_due for inv in valid_invoices)
    amount_to_process = min(amount_paid_by_parent, total_due_for_selection)

    # 2. --- CREATE PAYMENT & ALLOCATIONS ---

    payment_instance = Payment.objects.create(
        parent_payer=parent_user,
        amount=amount_to_process,
        payment_date=payment_date,
        payment_method=payment_method, # Pass the object, not the code
        transaction_reference=transaction_reference,
        status=Payment.STATUS_COMPLETED, # Assume completed
        notes=notes or f"Online payment for {len(valid_invoices)} invoice(s).",
        created_by=None,  # Parent payments don't have a staff creator
        unallocated_amount=amount_to_process # Initially, the whole amount is unallocated
    )
    logger.info(f"Payment record P-{payment_instance.pk} created for {parent_user.email}, amount {amount_to_process}.")

    amount_remaining_to_allocate = amount_to_process
    for invoice in valid_invoices:
        if amount_remaining_to_allocate <= 0:
            break
        
        allocation_amount = min(amount_remaining_to_allocate, invoice.balance_due)
        
        if allocation_amount > 0:
            PaymentAllocation.objects.create(
                payment=payment_instance,
                invoice=invoice,
                amount_allocated=allocation_amount
            )
            # The signal on PaymentAllocation will now handle updating the invoice.
            # No need to do it manually here!
            amount_remaining_to_allocate -= allocation_amount
            logger.info(f"Created allocation of {allocation_amount} for Payment P-{payment_instance.pk} to Invoice I-{invoice.pk}.")

    # Update the final unallocated amount on the payment record
    payment_instance.unallocated_amount = amount_remaining_to_allocate
    payment_instance.save(update_fields=['unallocated_amount'])
    
    # 3. --- CREATE JOURNAL ENTRY --- (Placeholder for now)
    # create_payment_journal_entry(payment_instance) # It's good to move this to another function

    message = _("Payment of {amount} was successfully recorded and applied.").format(amount=f"{payment_instance.amount:.2f}")
    if amount_remaining_to_allocate > 0:
        message += _(" An overpayment of {unallocated_amount} has been credited to your account.").format(unallocated_amount=f"{amount_remaining_to_allocate:.2f}")

    return True, message, payment_instance

# @transaction.atomic # Ensure all database operations succeed or fail together
# def record_parent_payment(parent_user: ParentUser, 
#                             tenant, # The tenant (School) instance
#                             invoice_pks_to_pay: list, 
#                             amount_paid_by_parent: Decimal,
#                             payment_method_code: str, # e.g., "ONLINE_MOCK", "CASH"
#                             transaction_reference: str,
#                             payment_date: timezone.datetime.date = None,
#                             notes: str = None):
#     """
#     Records a payment made by a parent, allocates it to selected invoices,
#     updates invoice statuses, and creates journal entries.
#     """
#     if not payment_date:
#         payment_date = timezone.now().date()

#     if not invoice_pks_to_pay or amount_paid_by_parent <= Decimal('0.00'):
#         logger.warning("record_parent_payment: No invoices selected or zero amount paid.")
#         return False, _("No invoices selected or zero amount to pay."), None

#     # Fetch the actual invoices to be paid, ensuring they belong to the parent's children
#     # and are still outstanding.
#     # This is a critical validation step.
#     children_pks = parent_user.children.filter(is_active=True).values_list('pk', flat=True)
#     valid_invoices = Invoice.objects.filter(
#         pk__in=invoice_pks_to_pay,
#         student_id__in=children_pks,
#         status__in=[
#             Invoice.InvoiceStatus.SENT,
#             Invoice.InvoiceStatus.PARTIALLY_PAID,
#             Invoice.InvoiceStatus.OVERDUE
#         ]
#     ).exclude(Q(total_amount__lte=F('amount_paid'))).order_by('due_date', 'pk') # Pay oldest first

#     if not valid_invoices.exists():
#         logger.warning(f"record_parent_payment: No valid outstanding invoices found for parent {parent_user.email} from PKS {invoice_pks_to_pay}.")
#         return False, _("No valid outstanding invoices found for payment."), None
    
#     # Verify total amount paid by parent does not exceed total due for selected valid invoices
#     total_due_for_selected_valid_invoices = sum(inv.balance_due for inv in valid_invoices)
#     if amount_paid_by_parent > total_due_for_selected_valid_invoices:
#         amount_to_process = total_due_for_selected_valid_invoices # Cap at what's due
#         logger.warning(f"record_parent_payment: Parent {parent_user.email} paid {amount_paid_by_parent} but only {total_due_for_selected_valid_invoices} is due for selected invoices. Processing capped amount.")
#     else:
#         amount_to_process = amount_paid_by_parent


#     # 1. Create Payment Record
#     payment_instance = Payment.objects.create(
#         parent=parent_user, # Assuming Payment model has 'parent' FK
#         # student=None, # If payment is general for parent, not specific student
#         payment_date=payment_date,
#         amount_paid=amount_to_process,
#         payment_method=payment_method_code, # You'll need to map this or ensure Payment model has choices
#         reference_number=transaction_reference,
#         status=Payment.STATUS_COMPLETED, # Assuming mock payment is completed
#         notes=notes or _(f"Online payment for {len(valid_invoices)} invoice(s)."),
#         # tenant=tenant, # If Payment model has direct tenant FK
#         created_by=parent_user # Or system user if preferred for gateway payments
#     )
#     logger.info(f"Payment record P-{payment_instance.pk} created for {parent_user.email}, amount {amount_to_process}.")

#     # 2. Allocate Payment to Invoices and Update Invoice Statuses
#     amount_remaining_to_allocate = amount_to_process
#     allocated_invoices_info = []

#     for invoice in valid_invoices:
#         if amount_remaining_to_allocate <= Decimal('0.00'):
#             break
        
#         payable_on_invoice = invoice.balance_due
#         amount_to_allocate_to_this_invoice = min(amount_remaining_to_allocate, payable_on_invoice)

#         if amount_to_allocate_to_this_invoice > Decimal('0.00'):
#             PaymentAllocation.objects.create(
#                 payment=payment_instance,
#                 invoice=invoice,
#                 amount_allocated=amount_to_allocate_to_this_invoice
#             )
#             invoice.amount_paid += amount_to_allocate_to_this_invoice
            
#             # Update invoice status
#             if invoice.balance_due <= Decimal('0.00'): # Use property after updating amount_paid
#                 invoice.status = Invoice.InvoiceStatus.PAID
#             else:
#                 invoice.status = Invoice.InvoiceStatus.PARTIALLY_PAID
#             invoice.save(update_fields=['amount_paid', 'status', 'updated_at'])
            
#             allocated_invoices_info.append(f"Inv-{invoice.invoice_number} (Allocated: {amount_to_allocate_to_this_invoice})")
#             amount_remaining_to_allocate -= amount_to_allocate_to_this_invoice
#             logger.info(f"Allocated {amount_to_allocate_to_this_invoice} to Invoice I-{invoice.pk}. New balance: {invoice.balance_due}. New Status: {invoice.status}")

#     if amount_remaining_to_allocate > Decimal('0.00'):
#         # This means parent paid more than total due of selected invoices, or more than total due of ALL their outstanding invoices
#         # Handle this as unallocated credit on the parent's account (requires ParentAccount/Credit model)
#         # For now, log it. In a real system, this needs careful handling.
#         payment_instance.unallocated_amount = amount_remaining_to_allocate # Add this field to Payment model if needed
#         payment_instance.notes += _(f" Unallocated amount: {amount_remaining_to_allocate}.")
#         payment_instance.save(update_fields=['unallocated_amount', 'notes'])
#         logger.warning(f"Payment P-{payment_instance.pk} has unallocated amount: {amount_remaining_to_allocate} for parent {parent_user.email}.")
#         messages.warning(request, _(f"Your payment included an overpayment of {amount_remaining_to_allocate}. This will be credited to your account."))


#     # 3. Create Journal Entry for the Payment
#     #    Debit: Bank/Cash Account (e.g., "Online Payments Clearing", "Main Bank Account")
#     #    Credit: Accounts Receivable (for each student whose invoice was paid)
#     # This JE should sum up credits to AR per student if multiple invoices for one student were paid.
#     # Or, simpler: one credit to a general AR control account if you don't track AR per student in GL.
#     # For now, let's do a simple JE: Debit Bank, Credit General Accounts Receivable.
    
#     # Ensure these accounts exist in ChartOfAccount for the tenant
#     # These COA codes/names should come from settings or a tenant configuration model
#     bank_account_code = getattr(settings, 'COA_BANK_FOR_ONLINE_PAYMENTS', '1010') # Example Bank Account Code
#     ar_account_code = getattr(settings, 'COA_ACCOUNTS_RECEIVABLE', '1200')       # Example AR Account Code

#     bank_account = get_coa_account(bank_account_code)
#     accounts_receivable_account = get_coa_account(ar_account_code)

#     if bank_account and accounts_receivable_account:
#         je_description = _(f"Payment received from parent {parent_user.get_full_name()} via {payment_method_code}. Ref: {transaction_reference}. Payment ID: P-{payment_instance.pk}")
        
#         je = JournalEntry.objects.create(
#             date=payment_date,
#             description=je_description[:255],
#             entry_type=JournalEntry.EntryType.PAYMENT,
#             status=JournalEntry.EntryStatus.POSTED, # Payments usually auto-post
#             created_by=parent_user, # Or a system user
#             reference_number=transaction_reference
#             # tenant=tenant, # If direct FK
#         )
#         # Debit Bank Account
#         JournalEntryLine.objects.create(
#             journal_entry=je,
#             account=bank_account,
#             debit_amount=amount_to_process, # Total amount processed by payment
#             credit_amount=Decimal('0.00')
#         )
#         # Credit Accounts Receivable
#         JournalEntryLine.objects.create(
#             journal_entry=je,
#             account=accounts_receivable_account,
#             debit_amount=Decimal('0.00'),
#             credit_amount=amount_to_process # Total amount processed
#         )
#         logger.info(f"Journal Entry JE-{je.pk} created for Payment P-{payment_instance.pk}.")
#     else:
#         logger.error(f"COA setup issue: Bank ({bank_account_code}) or AR ({ar_account_code}) account not found. JE not created for Payment P-{payment_instance.pk}.")
#         # This is a critical issue. You might want to prevent payment completion or flag for admin.
#         # For now, payment is recorded, but JE is missing.
#         return False, _("Payment recorded, but an accounting configuration error occurred. Please notify administration."), payment_instance


#     return True, _("Payment successfully recorded and allocated."), payment_instance



