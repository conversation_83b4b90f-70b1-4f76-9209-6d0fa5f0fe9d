#!/usr/bin/env python
"""
Fix Fox Data Constraints
Fix the data constraint issues in fox schema

Usage: python fix_fox_data_constraints.py
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from django.db import connection
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def fix_fox_data_constraints():
    """Fix data constraint issues in fox schema"""
    
    logger.info("=== FIXING FOX DATA CONSTRAINTS ===")
    
    try:
        with connection.cursor() as cursor:
            cursor.execute('SET search_path TO "fox"')
            
            # 1. Fix hr_leavetype data
            try:
                # Clear existing data first
                cursor.execute("DELETE FROM hr_leavetype")
                
                # Insert with proper values for all required fields
                cursor.execute("""
                    INSERT INTO hr_leavetype (
                        name, description, max_days_per_year, is_paid, requires_approval, 
                        is_active, accrual_frequency, created_at, updated_at
                    ) VALUES
                    ('Annual Leave', 'Annual vacation leave', 21, TRUE, TRUE, TRUE, 'YEARLY', NOW(), NOW()),
                    ('Sick Leave', 'Medical leave', 10, TRUE, FALSE, TRUE, 'YEARLY', NOW(), NOW()),
                    ('Maternity Leave', 'Maternity leave', 90, TRUE, TRUE, TRUE, 'ONCE', NOW(), NOW()),
                    ('Emergency Leave', 'Emergency leave', 3, FALSE, TRUE, TRUE, 'YEARLY', NOW(), NOW())
                """)
                logger.info("  ✅ Fixed hr_leavetype data in fox")
                
            except Exception as e:
                logger.error(f"  ❌ Failed to fix hr_leavetype: {e}")
            
            # 2. Fix payments_paymentmethod data
            try:
                # Clear existing data first
                cursor.execute("DELETE FROM payments_paymentmethod")
                
                # Insert with proper values for all required fields
                cursor.execute("""
                    INSERT INTO payments_paymentmethod (
                        name, description, is_active, type, created_at, updated_at
                    ) VALUES
                    ('Cash', 'Cash payment', TRUE, 'CASH', NOW(), NOW()),
                    ('Bank Transfer', 'Bank transfer payment', TRUE, 'BANK_TRANSFER', NOW(), NOW()),
                    ('Mobile Money', 'Mobile money payment', TRUE, 'MOBILE_MONEY', NOW(), NOW()),
                    ('Cheque', 'Cheque payment', TRUE, 'CHEQUE', NOW(), NOW())
                """)
                logger.info("  ✅ Fixed payments_paymentmethod data in fox")
                
            except Exception as e:
                logger.error(f"  ❌ Failed to fix payments_paymentmethod: {e}")
            
            # 3. Fix fees_concessiontype data
            try:
                # Clear existing data first
                cursor.execute("DELETE FROM fees_concessiontype")
                
                # Insert with proper values for all required fields
                cursor.execute("""
                    INSERT INTO fees_concessiontype (
                        name, description, type, value, is_active, created_at, updated_at
                    ) VALUES
                    ('Sibling Discount', 'Discount for siblings', 'PERCENTAGE', 10.00, TRUE, NOW(), NOW()),
                    ('Staff Child Discount', 'Discount for staff children', 'PERCENTAGE', 50.00, TRUE, NOW(), NOW()),
                    ('Merit Scholarship', 'Merit-based scholarship', 'PERCENTAGE', 25.00, TRUE, NOW(), NOW())
                """)
                logger.info("  ✅ Fixed fees_concessiontype data in fox")
                
            except Exception as e:
                logger.error(f"  ❌ Failed to fix fees_concessiontype: {e}")
            
            # 4. Verify the fixes
            logger.info("Verifying fixes...")
            
            verification_tables = ['hr_leavetype', 'payments_paymentmethod', 'fees_concessiontype']
            
            for table in verification_tables:
                try:
                    cursor.execute(f"SELECT COUNT(*) FROM {table}")
                    count = cursor.fetchone()[0]
                    logger.info(f"  ✅ {table}: {count} records")
                except Exception as e:
                    logger.error(f"  ❌ {table}: {e}")
            
            logger.info("✅ Fox data constraints fix completed!")
            
    except Exception as e:
        logger.error(f"❌ Failed to fix fox data constraints: {e}")

def main():
    """Main function"""
    logger.info("=== FOX DATA CONSTRAINTS FIX ===")
    
    try:
        fix_fox_data_constraints()
        
        logger.info("\n🎉 FOX DATA CONSTRAINTS FIX COMPLETE!")
        logger.info("Fox schema should now have:")
        logger.info("- Proper leave types with all required fields")
        logger.info("- Payment methods with correct type values")
        logger.info("- Concession types with proper value fields")
        logger.info("- No constraint violations")
        
        return True
        
    except Exception as e:
        logger.error(f"Fox data constraints fix failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
