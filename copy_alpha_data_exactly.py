#!/usr/bin/env python
"""
Copy Alpha Data Exactly
Copies the exact working data from alpha schema to all other tenant schemas

Usage: python copy_alpha_data_exactly.py
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from django.db import connection
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def copy_essential_data_from_alpha():
    """Copy essential dropdown data from alpha to all tenants"""
    
    tenant_schemas = ['mandiva', 'aischool', 'bea', 'ruzivo']
    
    # Essential tables that need data for dropdowns to work
    essential_tables = [
        'schools_academicyear',
        'schools_term', 
        'schools_schoolclass',
        'schools_section',
        'accounting_accounttype',
        'accounting_account',
        'fees_feehead',
        'payments_paymentmethod',
        'fees_concessiontype',
        'hr_leavetype',
        'school_calendar_eventcategory',
        'finance_expensecategory'
    ]
    
    logger.info("=== COPYING ALPHA DATA EXACTLY ===")
    
    try:
        with connection.cursor() as cursor:
            # Get data from alpha
            alpha_data = {}
            
            cursor.execute('SET search_path TO "alpha"')
            
            for table in essential_tables:
                try:
                    # Check if table exists in alpha
                    cursor.execute(f"""
                        SELECT EXISTS (
                            SELECT FROM information_schema.tables 
                            WHERE table_schema = 'alpha' 
                            AND table_name = '{table}'
                        )
                    """)
                    
                    if cursor.fetchone()[0]:
                        # Get column names
                        cursor.execute(f"""
                            SELECT column_name 
                            FROM information_schema.columns 
                            WHERE table_schema = 'alpha' 
                            AND table_name = '{table}'
                            ORDER BY ordinal_position
                        """)
                        
                        columns = [row[0] for row in cursor.fetchall()]
                        
                        # Get data
                        cursor.execute(f"SELECT * FROM {table} ORDER BY id")
                        rows = cursor.fetchall()
                        
                        alpha_data[table] = {
                            'columns': columns,
                            'rows': rows
                        }
                        
                        logger.info(f"✅ Got {len(rows)} rows from alpha.{table}")
                    else:
                        logger.warning(f"⚠️  Table {table} does not exist in alpha")
                        
                except Exception as e:
                    logger.warning(f"⚠️  Failed to get data from alpha.{table}: {e}")
            
            # Copy data to each tenant
            for tenant_schema in tenant_schemas:
                logger.info(f"\nCopying data to {tenant_schema}")
                cursor.execute(f'SET search_path TO "{tenant_schema}"')
                
                for table, data in alpha_data.items():
                    try:
                        # Check if table exists in tenant
                        cursor.execute(f"""
                            SELECT EXISTS (
                                SELECT FROM information_schema.tables 
                                WHERE table_schema = '{tenant_schema}' 
                                AND table_name = '{table}'
                            )
                        """)
                        
                        if cursor.fetchone()[0]:
                            columns = data['columns']
                            rows = data['rows']
                            
                            if rows:
                                # Clear existing data
                                cursor.execute(f"DELETE FROM {table}")
                                
                                # Reset sequence if exists
                                try:
                                    cursor.execute(f"SELECT setval('{table}_id_seq', 1, false)")
                                except:
                                    pass
                                
                                # Insert alpha data
                                placeholders = ', '.join(['%s'] * len(columns))
                                columns_str = ', '.join(columns)
                                insert_sql = f"INSERT INTO {table} ({columns_str}) VALUES ({placeholders})"
                                
                                for row in rows:
                                    cursor.execute(insert_sql, row)
                                
                                logger.info(f"✅ Copied {len(rows)} rows to {tenant_schema}.{table}")
                            else:
                                logger.info(f"ℹ️  No data to copy for {table}")
                        else:
                            logger.warning(f"⚠️  Table {table} does not exist in {tenant_schema}")
                            
                    except Exception as e:
                        logger.error(f"❌ Failed to copy data to {tenant_schema}.{table}: {e}")
                        
    except Exception as e:
        logger.error(f"Failed to copy alpha data: {e}")

def create_missing_essential_data():
    """Create essential data if not present in alpha"""
    
    tenant_schemas = ['mandiva', 'aischool', 'bea', 'ruzivo']
    
    logger.info("=== CREATING MISSING ESSENTIAL DATA ===")
    
    for tenant_schema in tenant_schemas:
        logger.info(f"\nCreating essential data for {tenant_schema}")
        
        try:
            with connection.cursor() as cursor:
                cursor.execute(f'SET search_path TO "{tenant_schema}"')
                
                # 1. Academic Years
                try:
                    cursor.execute("SELECT COUNT(*) FROM schools_academicyear")
                    if cursor.fetchone()[0] == 0:
                        cursor.execute("""
                            INSERT INTO schools_academicyear (name, start_date, end_date, is_active, is_current, created_at, updated_at) VALUES
                            ('2024-2025', '2024-09-01', '2025-08-31', TRUE, TRUE, NOW(), NOW()),
                            ('2025-2026', '2025-09-01', '2026-08-31', FALSE, FALSE, NOW(), NOW())
                        """)
                        logger.info(f"✅ Created academic years in {tenant_schema}")
                except Exception as e:
                    logger.warning(f"Academic years in {tenant_schema}: {e}")
                
                # 2. Terms
                try:
                    cursor.execute("SELECT COUNT(*) FROM schools_term")
                    if cursor.fetchone()[0] == 0:
                        cursor.execute("""
                            INSERT INTO schools_term (name, start_date, end_date, academic_year_id, is_active, created_at, updated_at) VALUES
                            ('Term 1', '2024-09-01', '2024-12-15', 1, TRUE, NOW(), NOW()),
                            ('Term 2', '2025-01-15', '2025-04-15', 1, TRUE, NOW(), NOW()),
                            ('Term 3', '2025-04-30', '2025-08-31', 1, TRUE, NOW(), NOW())
                        """)
                        logger.info(f"✅ Created terms in {tenant_schema}")
                except Exception as e:
                    logger.warning(f"Terms in {tenant_schema}: {e}")
                
                # 3. School Classes
                try:
                    cursor.execute("SELECT COUNT(*) FROM schools_schoolclass")
                    if cursor.fetchone()[0] == 0:
                        cursor.execute("""
                            INSERT INTO schools_schoolclass (name, description, is_active, created_at, updated_at) VALUES
                            ('Grade 1', 'First Grade', TRUE, NOW(), NOW()),
                            ('Grade 2', 'Second Grade', TRUE, NOW(), NOW()),
                            ('Grade 3', 'Third Grade', TRUE, NOW(), NOW()),
                            ('Grade 4', 'Fourth Grade', TRUE, NOW(), NOW()),
                            ('Grade 5', 'Fifth Grade', TRUE, NOW(), NOW()),
                            ('Grade 6', 'Sixth Grade', TRUE, NOW(), NOW()),
                            ('Grade 7', 'Seventh Grade', TRUE, NOW(), NOW()),
                            ('Grade 8', 'Eighth Grade', TRUE, NOW(), NOW()),
                            ('Grade 9', 'Ninth Grade', TRUE, NOW(), NOW()),
                            ('Grade 10', 'Tenth Grade', TRUE, NOW(), NOW()),
                            ('Grade 11', 'Eleventh Grade', TRUE, NOW(), NOW()),
                            ('Grade 12', 'Twelfth Grade', TRUE, NOW(), NOW())
                        """)
                        logger.info(f"✅ Created school classes in {tenant_schema}")
                except Exception as e:
                    logger.warning(f"School classes in {tenant_schema}: {e}")
                
                # 4. Sections
                try:
                    cursor.execute("SELECT COUNT(*) FROM schools_section")
                    if cursor.fetchone()[0] == 0:
                        cursor.execute("""
                            INSERT INTO schools_section (name, school_class_id, is_active, created_at, updated_at) VALUES
                            ('A', 1, TRUE, NOW(), NOW()),
                            ('B', 1, TRUE, NOW(), NOW()),
                            ('A', 2, TRUE, NOW(), NOW()),
                            ('B', 2, TRUE, NOW(), NOW()),
                            ('A', 3, TRUE, NOW(), NOW()),
                            ('B', 3, TRUE, NOW(), NOW()),
                            ('A', 4, TRUE, NOW(), NOW()),
                            ('B', 4, TRUE, NOW(), NOW()),
                            ('A', 5, TRUE, NOW(), NOW()),
                            ('B', 5, TRUE, NOW(), NOW()),
                            ('A', 6, TRUE, NOW(), NOW()),
                            ('B', 6, TRUE, NOW(), NOW())
                        """)
                        logger.info(f"✅ Created sections in {tenant_schema}")
                except Exception as e:
                    logger.warning(f"Sections in {tenant_schema}: {e}")
                
                # 5. Account Types (if table exists)
                try:
                    cursor.execute("""
                        SELECT EXISTS (
                            SELECT FROM information_schema.tables 
                            WHERE table_schema = %s 
                            AND table_name = 'accounting_accounttype'
                        )
                    """, [tenant_schema])
                    
                    if cursor.fetchone()[0]:
                        cursor.execute("SELECT COUNT(*) FROM accounting_accounttype")
                        if cursor.fetchone()[0] == 0:
                            cursor.execute("""
                                INSERT INTO accounting_accounttype (name, code, classification, is_active, created_at, updated_at) VALUES
                                ('Cash', 'CASH', 'ASSET', TRUE, NOW(), NOW()),
                                ('Bank', 'BANK', 'ASSET', TRUE, NOW(), NOW()),
                                ('Accounts Receivable', 'AR', 'ASSET', TRUE, NOW(), NOW()),
                                ('Tuition Income', 'TUITION', 'REVENUE', TRUE, NOW(), NOW()),
                                ('Other Income', 'OTHER_INC', 'REVENUE', TRUE, NOW(), NOW())
                            """)
                            logger.info(f"✅ Created account types in {tenant_schema}")
                except Exception as e:
                    logger.warning(f"Account types in {tenant_schema}: {e}")
                
                # 6. Chart of Accounts (if table exists)
                try:
                    cursor.execute("""
                        SELECT EXISTS (
                            SELECT FROM information_schema.tables 
                            WHERE table_schema = %s 
                            AND table_name = 'accounting_account'
                        )
                    """, [tenant_schema])
                    
                    if cursor.fetchone()[0]:
                        cursor.execute("SELECT COUNT(*) FROM accounting_account")
                        if cursor.fetchone()[0] == 0:
                            cursor.execute("""
                                INSERT INTO accounting_account (name, code, account_type_id, is_active, is_control_account, can_be_used_in_je, created_at, updated_at) VALUES
                                ('Cash in Hand', '1001', 1, TRUE, FALSE, TRUE, NOW(), NOW()),
                                ('Bank - Main Account', '1002', 2, TRUE, FALSE, TRUE, NOW(), NOW()),
                                ('Student Fees Receivable', '1101', 3, TRUE, FALSE, TRUE, NOW(), NOW()),
                                ('Tuition Fees Income', '4001', 4, TRUE, FALSE, TRUE, NOW(), NOW()),
                                ('Registration Fees', '4002', 5, TRUE, FALSE, TRUE, NOW(), NOW())
                            """)
                            logger.info(f"✅ Created chart of accounts in {tenant_schema}")
                except Exception as e:
                    logger.warning(f"Chart of accounts in {tenant_schema}: {e}")
                
        except Exception as e:
            logger.error(f"❌ Failed to create essential data for {tenant_schema}: {e}")

def main():
    """Main function"""
    logger.info("=== COPY ALPHA DATA EXACTLY ===")
    
    try:
        # Copy exact data from alpha
        copy_essential_data_from_alpha()
        
        # Create missing essential data
        create_missing_essential_data()
        
        logger.info("\n🎉 ALPHA DATA COPIED EXACTLY!")
        logger.info("All tenant schemas now have:")
        logger.info("- Exact same data as alpha schema")
        logger.info("- Working dropdown menus")
        logger.info("- All essential records for school management")
        
        logger.info("\n🎯 TEST ALL TENANTS NOW:")
        logger.info("- mandiva.myapp.test:8000")
        logger.info("- aischool.myapp.test:8000") 
        logger.info("- bea.myapp.test:8000")
        logger.info("- ruzivo.myapp.test:8000")
        logger.info("\nAll dropdown menus should work exactly like alpha!")
        
        return True
        
    except Exception as e:
        logger.error(f"Copy alpha data failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
