#!/usr/bin/env python
"""
Fix HR Migration Issues
Fixes the LeaveBalance field name issue

Usage: python fix_hr_migration.py
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from django.db import connection
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def fix_hr_migration_issues():
    """Fix HR migration issues across all tenant schemas"""
    
    # List of tenant schemas to fix
    tenant_schemas = ['alpha', 'aischool', 'bea', 'mandiva', 'ruzivo']
    
    logger.info("=== FIXING HR MIGRATION ISSUES ===")
    
    for schema_name in tenant_schemas:
        logger.info(f"Fixing schema: {schema_name}")
        
        try:
            with connection.cursor() as cursor:
                # Set search path to tenant schema
                cursor.execute(f'SET search_path TO "{schema_name}"')
                
                # Check if the table exists and what columns it has
                cursor.execute("""
                    SELECT column_name 
                    FROM information_schema.columns 
                    WHERE table_name = 'hr_leavebalance' 
                    AND table_schema = %s
                """, [schema_name])
                
                columns = [row[0] for row in cursor.fetchall()]
                logger.info(f"LeaveBalance columns in {schema_name}: {columns}")
                
                # If the old field exists, rename it
                if 'year_or_period_info' in columns and 'year' not in columns:
                    logger.info(f"Renaming year_or_period_info to year in {schema_name}")
                    cursor.execute('ALTER TABLE hr_leavebalance RENAME COLUMN year_or_period_info TO year')
                    logger.info(f"✅ Renamed column in {schema_name}")
                elif 'year' in columns:
                    logger.info(f"✅ Column 'year' already exists in {schema_name}")
                else:
                    logger.info(f"⚠️  Neither 'year' nor 'year_or_period_info' found in {schema_name}")
                
        except Exception as e:
            logger.error(f"❌ Failed to fix {schema_name}: {e}")

def main():
    """Main function"""
    logger.info("=== HR MIGRATION FIX ===")
    
    try:
        fix_hr_migration_issues()
        logger.info("✅ HR migration issues fixed!")
        return True
        
    except Exception as e:
        logger.error(f"Failed to fix HR migration issues: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
