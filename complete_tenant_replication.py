#!/usr/bin/env python
"""
Complete Tenant Replication
Properly replicate EVERYTHING from alpha to other tenants including:
1. All tables and structure
2. All data including financial records
3. All formatting and premium features

Usage: python complete_tenant_replication.py
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from django.db import connection
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def get_alpha_complete_dump():
    """Get complete dump of alpha schema - structure and data"""
    
    logger.info("Getting complete alpha dump...")
    
    alpha_dump = {
        'table_structures': {},
        'table_data': {},
        'sequences': {},
        'indexes': {}
    }
    
    try:
        with connection.cursor() as cursor:
            cursor.execute('SET search_path TO "alpha"')
            
            # Get all tables
            cursor.execute("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'alpha' 
                AND table_type = 'BASE TABLE'
                ORDER BY table_name
            """)
            
            tables = [row[0] for row in cursor.fetchall()]
            logger.info(f"Found {len(tables)} tables in alpha")
            
            for table in tables:
                # Get table structure
                cursor.execute(f"""
                    SELECT column_name, data_type, character_maximum_length, 
                           numeric_precision, numeric_scale, is_nullable, column_default
                    FROM information_schema.columns 
                    WHERE table_schema = 'alpha' 
                    AND table_name = '{table}'
                    ORDER BY ordinal_position
                """)
                
                columns = cursor.fetchall()
                alpha_dump['table_structures'][table] = columns
                
                # Get all data from table
                try:
                    cursor.execute(f"SELECT * FROM {table}")
                    rows = cursor.fetchall()
                    
                    # Get column names
                    cursor.execute(f"""
                        SELECT column_name 
                        FROM information_schema.columns 
                        WHERE table_schema = 'alpha' 
                        AND table_name = '{table}'
                        ORDER BY ordinal_position
                    """)
                    col_names = [row[0] for row in cursor.fetchall()]
                    
                    alpha_dump['table_data'][table] = {
                        'columns': col_names,
                        'rows': rows
                    }
                    
                    logger.info(f"✅ {table}: {len(rows)} rows")
                    
                except Exception as e:
                    logger.warning(f"⚠️  Could not get data from {table}: {e}")
                    alpha_dump['table_data'][table] = {'columns': [], 'rows': []}
            
            logger.info(f"✅ Complete alpha dump obtained")
            
    except Exception as e:
        logger.error(f"❌ Failed to get alpha dump: {e}")
    
    return alpha_dump

def replicate_to_tenant(tenant_schema, alpha_dump):
    """Completely replicate alpha to tenant schema"""
    
    logger.info(f"Replicating alpha to {tenant_schema}...")
    
    try:
        with connection.cursor() as cursor:
            cursor.execute(f'SET search_path TO "{tenant_schema}"')
            
            # Drop and recreate all tables to ensure clean state
            logger.info(f"Cleaning {tenant_schema} schema...")
            
            # Get existing tables
            cursor.execute(f"""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = '{tenant_schema}' 
                AND table_type = 'BASE TABLE'
            """)
            
            existing_tables = [row[0] for row in cursor.fetchall()]
            
            # Drop all existing tables
            for table in existing_tables:
                try:
                    cursor.execute(f"DROP TABLE IF EXISTS {table} CASCADE")
                except Exception as e:
                    logger.warning(f"Could not drop {table}: {e}")
            
            logger.info(f"Dropped {len(existing_tables)} existing tables")
            
            # Create all tables from alpha
            created_count = 0
            
            for table_name, structure in alpha_dump['table_structures'].items():
                try:
                    # Build CREATE TABLE statement
                    columns_def = []
                    
                    for col_name, data_type, max_len, num_prec, num_scale, nullable, default in structure:
                        col_def = f'"{col_name}" '
                        
                        # Handle data types
                        if data_type == 'character varying':
                            if max_len:
                                col_def += f'VARCHAR({max_len})'
                            else:
                                col_def += 'VARCHAR(255)'
                        elif data_type == 'numeric' and num_prec and num_scale:
                            col_def += f'NUMERIC({num_prec},{num_scale})'
                        elif data_type == 'timestamp with time zone':
                            col_def += 'TIMESTAMP WITH TIME ZONE'
                        elif data_type == 'timestamp without time zone':
                            col_def += 'TIMESTAMP'
                        else:
                            col_def += data_type.upper()
                        
                        # Handle nullable
                        if nullable == 'NO':
                            col_def += ' NOT NULL'
                        
                        # Handle defaults (skip sequences for now)
                        if default and not default.startswith('nextval'):
                            col_def += f' DEFAULT {default}'
                        
                        columns_def.append(col_def)
                    
                    create_sql = f'CREATE TABLE "{table_name}" ({", ".join(columns_def)})'
                    
                    cursor.execute(create_sql)
                    created_count += 1
                    
                    # Create sequence if needed
                    if any('id' in col[0] for col in structure):
                        try:
                            cursor.execute(f'CREATE SEQUENCE IF NOT EXISTS {table_name}_id_seq')
                            cursor.execute(f'ALTER TABLE {table_name} ALTER COLUMN id SET DEFAULT nextval(\'{table_name}_id_seq\')')
                        except:
                            pass
                    
                    logger.info(f"✅ Created {table_name}")
                    
                except Exception as e:
                    logger.error(f"❌ Failed to create {table_name}: {e}")
            
            logger.info(f"Created {created_count} tables")
            
            # Insert all data
            inserted_count = 0
            
            for table_name, data in alpha_dump['table_data'].items():
                if data['rows']:
                    try:
                        columns = data['columns']
                        rows = data['rows']
                        
                        # Clear any existing data
                        cursor.execute(f'DELETE FROM "{table_name}"')
                        
                        # Insert data
                        placeholders = ', '.join(['%s'] * len(columns))
                        columns_str = ', '.join([f'"{col}"' for col in columns])
                        insert_sql = f'INSERT INTO "{table_name}" ({columns_str}) VALUES ({placeholders})'
                        
                        for row in rows:
                            cursor.execute(insert_sql, row)
                        
                        # Update sequence
                        try:
                            cursor.execute(f"SELECT setval('{table_name}_id_seq', (SELECT MAX(id) FROM {table_name}))")
                        except:
                            pass
                        
                        inserted_count += 1
                        logger.info(f"✅ Inserted {len(rows)} rows into {table_name}")
                        
                    except Exception as e:
                        logger.error(f"❌ Failed to insert data into {table_name}: {e}")
            
            logger.info(f"✅ Replicated {tenant_schema}: {created_count} tables, {inserted_count} with data")
            
    except Exception as e:
        logger.error(f"❌ Failed to replicate to {tenant_schema}: {e}")

def verify_replication(tenant_schema, alpha_dump):
    """Verify that replication was successful"""
    
    logger.info(f"Verifying {tenant_schema} replication...")
    
    try:
        with connection.cursor() as cursor:
            cursor.execute(f'SET search_path TO "{tenant_schema}"')
            
            # Check table count
            cursor.execute(f"""
                SELECT COUNT(*) 
                FROM information_schema.tables 
                WHERE table_schema = '{tenant_schema}' 
                AND table_type = 'BASE TABLE'
            """)
            
            table_count = cursor.fetchone()[0]
            expected_count = len(alpha_dump['table_structures'])
            
            logger.info(f"Tables: {table_count}/{expected_count}")
            
            # Check data in key tables
            key_tables = ['fees_invoice', 'payments_payment', 'students_student', 'schools_staffuser']
            
            for table in key_tables:
                if table in alpha_dump['table_data']:
                    try:
                        cursor.execute(f'SELECT COUNT(*) FROM "{table}"')
                        count = cursor.fetchone()[0]
                        expected = len(alpha_dump['table_data'][table]['rows'])
                        
                        if count == expected:
                            logger.info(f"✅ {table}: {count}/{expected} records")
                        else:
                            logger.warning(f"⚠️  {table}: {count}/{expected} records")
                            
                    except Exception as e:
                        logger.error(f"❌ {table}: {e}")
            
            if table_count == expected_count:
                logger.info(f"✅ {tenant_schema} replication SUCCESSFUL")
                return True
            else:
                logger.error(f"❌ {tenant_schema} replication INCOMPLETE")
                return False
                
    except Exception as e:
        logger.error(f"❌ Verification failed for {tenant_schema}: {e}")
        return False

def main():
    """Main function"""
    logger.info("=== COMPLETE TENANT REPLICATION ===")
    
    try:
        # Get complete alpha dump
        alpha_dump = get_alpha_complete_dump()
        
        if not alpha_dump['table_structures']:
            logger.error("❌ Failed to get alpha dump")
            return False
        
        # Replicate to all tenants
        tenant_schemas = ['mandiva', 'aischool', 'bea', 'ruzivo']
        
        success_count = 0
        
        for tenant_schema in tenant_schemas:
            logger.info(f"\n{'='*50}")
            logger.info(f"REPLICATING TO {tenant_schema.upper()}")
            logger.info(f"{'='*50}")
            
            # Replicate
            replicate_to_tenant(tenant_schema, alpha_dump)
            
            # Verify
            if verify_replication(tenant_schema, alpha_dump):
                success_count += 1
        
        logger.info(f"\n{'='*80}")
        logger.info("REPLICATION SUMMARY")
        logger.info(f"{'='*80}")
        logger.info(f"✅ Successful replications: {success_count}/{len(tenant_schemas)}")
        
        if success_count == len(tenant_schemas):
            logger.info("🎉 ALL TENANTS SUCCESSFULLY REPLICATED!")
            logger.info("All tenants now have:")
            logger.info("- Exact same structure as alpha")
            logger.info("- All financial data and records")
            logger.info("- All premium formatting")
            logger.info("- Working financial reports")
            logger.info("- Proper form formatting")
        else:
            logger.error(f"⚠️  {len(tenant_schemas) - success_count} tenant(s) failed replication")
        
        return success_count == len(tenant_schemas)
        
    except Exception as e:
        logger.error(f"Complete replication failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
