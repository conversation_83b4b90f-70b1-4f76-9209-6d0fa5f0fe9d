# D:\school_fees_saas_v2\apps\tenants\views.py

import logging
import uuid # For fallback schema name generation
from django.conf import settings
from django.contrib import messages
from django.contrib.auth import get_user_model, login as django_auth_login
from django.db import transaction, IntegrityError
from django.shortcuts import render, redirect
from django.urls import reverse # NoReverseMatch is an exception, not usually imported directly
from django.utils.text import slugify
from django.utils.translation import gettext_lazy as _ # For translatable strings
from django.views.generic import TemplateView

from .forms import RegistrationForm
from .models import School, Domain # Assuming School is your tenant model
from apps.communication.tasks import send_welcome_email_task # Ensure this path is correct

from django.contrib.auth import get_user_model, login as auth_login
from django_tenants.utils import schema_context # For operating in tenant schema

User = get_user_model() # Public User model

PublicUser = get_user_model() # This will get your 'users.User' model


import logging
from decimal import Decimal
from datetime import timedelta

from django.utils import timezone

# Models
from apps.users.models import User as PublicUser # Your public site admin/owner user
from apps.tenants.models import School, Domain # Your tenant and domain models
from apps.schools.models import StaffUser      # Your tenant-specific staff user
from django.contrib.auth.models import Group   # For assigning staff to groups
from apps.subscriptions.models import SubscriptionPlan, Subscription # Subscription models

# Tasks
from apps.communication.tasks import send_welcome_email_task # Adjust path if different

# Utils
from .utils import generate_safe_schema_name # Assuming you have this utility

logger = logging.getLogger(__name__)



def generate_safe_schema_name(base_name, max_length=63):
    """
    Generates a database-safe schema name.
    Lowercase, hyphens for spaces, removes invalid chars, respects max length,
    doesn't start/end with hyphen, and provides a UUID-based fallback.
    """
    schema_name = slugify(base_name) 
    if not schema_name: # Fallback if base_name was all non-alphanumeric
        # Using a more distinct prefix for generated schemas
        schema_name = f"sch-{str(uuid.uuid4().hex)[:12]}" # Shorter, more common UUID part
        logger.warning(f"Generated fallback schema name '{schema_name}' due to empty slug for base '{base_name}'")
    
    # Ensure it doesn't start or end with a hyphen after initial slugify might create one
    schema_name = schema_name.strip('-')
    if not schema_name: # If stripping hyphens made it empty (e.g. base_name was just "-")
        schema_name = f"sch-{str(uuid.uuid4().hex)[:12]}" # Re-apply fallback
        logger.warning(f"Re-applied fallback schema name '{schema_name}' after stripping made it empty.")

    return schema_name[:max_length]



# No need for @transaction.atomic here, as we use 'with transaction.atomic():' inside
def register_school_view(request): # Renamed from register_school for clarity
    if request.method == 'POST':
        # Pass settings.APP_HOSTNAME for help text in form if your form uses it
        form = RegistrationForm(request.POST, base_domain_for_help_text=settings.APP_HOSTNAME)
        if form.is_valid():
            school_name = form.cleaned_data['school_name']
            subdomain_slug = form.cleaned_data['subdomain'].lower()
            admin_first_name = form.cleaned_data['admin_first_name']
            admin_last_name = form.cleaned_data['admin_last_name']
            admin_email = form.cleaned_data['admin_email'].lower()
            admin_password = form.cleaned_data['admin_password1']
            
            # Subscription details
            selected_plan = form.cleaned_data['plan']
            selected_billing_cycle = form.cleaned_data['billing_cycle']

            schema_name_to_use = generate_safe_schema_name(subdomain_slug)
            new_tenant_domain_hostname = f"{subdomain_slug}.{settings.APP_HOSTNAME}"

            # --- Pre-transaction Uniqueness Checks (Good for early UI feedback) ---
            user_exists = PublicUser.objects.filter(email__iexact=admin_email).exists()
            # Domain check should consider the full domain name
            domain_exists = Domain.objects.filter(domain__iexact=new_tenant_domain_hostname).exists()
            schema_name_exists = School.objects.filter(schema_name__iexact=schema_name_to_use).exists()

            error_found = False
            if user_exists:
                form.add_error('admin_email', _("This email address is already registered. Please use a different email or log in."))
                error_found = True
            if domain_exists:
                form.add_error('subdomain', _('The portal address "%(domain)s" is already in use. Please choose another prefix.') % {'domain': new_tenant_domain_hostname})
                error_found = True
            # Schema name check might be redundant if subdomain slug directly becomes schema name after sanitization
            # and domain_exists check is robust. But doesn't hurt if generate_safe_schema_name can differ.
            elif schema_name_exists: # Use elif to avoid double error on subdomain if domain already taken
                form.add_error('subdomain', _('This subdomain (or a similar derived internal name) is already taken. Please choose another.'))
                error_found = True
            
            if error_found:
                messages.error(request, _("Please correct the errors highlighted below."))
            else:
                # All pre-checks passed, proceed with atomic transaction
                try:
                    # Force connection to public schema for tenant creation
                    from django.db import connection
                    from django_tenants.utils import schema_context

                    with schema_context('public'):
                        with transaction.atomic():
                            # 1. Create Public User (School Owner)
                            logger.info(f"Creating PublicUser: {admin_email} for school {school_name}")
                            admin_user = PublicUser.objects.create_user(
                                email=admin_email, # Assuming your PublicUser uses email as USERNAME_FIELD
                                password=admin_password,
                                first_name=admin_first_name,
                                last_name=admin_last_name,
                                is_active=True # Or False if email verification is used later
                            )
                            logger.info(f"Created PublicUser: {admin_user.email} (PK: {admin_user.pk})")

                            # 2. Create School (Tenant)
                            # auto_create_schema=True on School model handles schema creation.
                            school = School.objects.create(
                                name=school_name,
                                owner=admin_user,
                                schema_name=schema_name_to_use,
                                is_active=False # Start as inactive until subscription/payment is confirmed
                            )
                            logger.info(f"School '{school.name}' (PK: {school.pk}, Schema: {school.schema_name}) created. Schema creation triggered.")

                            # 3. Create Domain for the Tenant
                            # This must happen AFTER the tenant (School) is created AND its schema is prepared.
                            # The on_commit hook from auto_create_schema usually handles schema creation before this.
                            domain_obj = Domain.objects.create(
                                domain=new_tenant_domain_hostname,
                                tenant=school,
                                is_primary=True
                            )
                            logger.info(f"Domain '{domain_obj.domain}' created for tenant '{school.name}'.")

                            # 4. Create Subscription Record
                            now = timezone.now()
                            trial_start = None
                            trial_end = None
                            initial_subscription_status = 'PENDING' # Default if payment is required immediately

                            price = selected_plan.price_monthly if selected_billing_cycle == 'MONTHLY' else selected_plan.price_annually

                            if selected_plan.trial_period_days > 0:
                                trial_start = now
                                trial_end = now + timedelta(days=selected_plan.trial_period_days)
                                initial_subscription_status = 'TRIALING'
                                school.is_active = True # Activate school if on trial
                                school.save(update_fields=['is_active'])

                            # If it's a free plan (price is 0), also activate
                            elif price <= Decimal('0.00'):
                                initial_subscription_status = 'ACTIVE'
                                school.is_active = True
                                school.save(update_fields=['is_active'])

                            subscription = Subscription.objects.create(
                                school=school,
                                plan=selected_plan,
                                status=initial_subscription_status,
                                billing_cycle=selected_billing_cycle,
                                trial_start_date=trial_start,
                                trial_end_date=trial_end,
                                current_period_start=now if initial_subscription_status != 'PENDING' else None,
                                # current_period_end needs to be set based on cycle if activated
                                price_at_subscription=price
                            )
                            logger.info(f"Subscription record (PK: {subscription.pk}) created for school '{school.name}' with plan '{selected_plan.name}', status '{subscription.status}'.")
                        
                        # 5. Create StaffUser for the owner in the new tenant's schema
                        # This must happen AFTER the tenant (School) is created and its schema is active.
                        with schema_context(school.schema_name):
                            logger.info(f"Attempting to create StaffUser for owner '{admin_user.email}' in schema '{school.schema_name}'")
                            # Use get_or_create to handle potential re-runs or unique constraints
                            staff_user, staff_created = StaffUser.objects.get_or_create(
                                email__iexact=admin_user.email, # Case-insensitive match
                                defaults={
                                    'first_name': admin_user.first_name,
                                    'last_name': admin_user.last_name,
                                    'is_staff': True, # Django admin access
                                    'is_active': True,
                                    'is_superuser': True, # Make owner a superuser in tenant
                                    # Note: employee_id and designation are now in HR profile
                                }
                            )
                            if staff_created:
                                staff_user.set_password(admin_password) # Set password for new staff user
                                staff_user.save()
                                logger.info(f"StaffUser '{staff_user.email}' CREATED in tenant '{school.schema_name}'.")

                                # Create HR profile for the owner
                                try:
                                    from apps.hr.models import EmployeeProfile
                                    hr_profile, hr_created = EmployeeProfile.objects.get_or_create(
                                        user=staff_user,
                                        defaults={
                                            'employee_id': 'OWNER001',
                                            'designation': 'School Administrator (Owner)',
                                            'department': 'Administration',
                                            'employment_type': 'FULL_TIME',
                                        }
                                    )
                                    if hr_created:
                                        logger.info(f"HR Profile created for owner '{staff_user.email}' in tenant '{school.schema_name}'.")
                                except Exception as e:
                                    logger.warning(f"Could not create HR profile for owner: {e}")

                                # Assign to a default "School Administrators" group
                                admin_group_name = "School Administrators" # Define this consistently
                                admin_group, group_created = Group.objects.get_or_create(name=admin_group_name)
                                staff_user.groups.add(admin_group)
                                if group_created:
                                    logger.info(f"Group '{admin_group_name}' created in tenant '{school.schema_name}'. Assign permissions manually or via data migration.")
                                else:
                                    logger.info(f"StaffUser '{staff_user.email}' added to existing group '{admin_group_name}' in tenant '{school.schema_name}'.")
                            else:
                                logger.info(f"StaffUser '{staff_user.email}' already existed in tenant '{school.schema_name}'.")
                        
                    # --- Actions After Successful Transaction ---
                    logger.info(f"Transaction committed successfully for school registration: {school.name}.")

                    # Auto-login the public admin user to the *public site's session*
                    public_user_backend = 'django.contrib.auth.backends.ModelBackend' # Default
                    if admin_user.is_active:
                        django_auth_login(request, admin_user, backend=public_user_backend)
                        logger.info(f"Auto-logged in PublicUser '{admin_user.email}' to public site session.")
                    
                    # Queue the welcome email task
                    try:
                        send_welcome_email_task.delay(admin_user.pk, school.pk, school.schema_name) 
                        logger.info(f"Welcome email task queued for PublicUser PK {admin_user.pk}, School PK {school.pk}.")
                    except Exception as e_celery:
                        logger.error(f"Failed to queue welcome email task for {admin_user.email}: {e_celery}", exc_info=True)
                        messages.warning(request, _("School registered! We had an issue queuing your welcome email. Please contact support if it doesn't arrive soon."))
                    
                    # Determine next step based on subscription status
                    if subscription.status == 'TRIALING' or subscription.status == 'ACTIVE':
                        messages.success(request, _("School registration successful! Your portal is active. You have been logged in."))
                        # Redirect to a "setup complete, go to your dashboard" page on the public site,
                        # which then links to their new tenant domain.
                        request.session['new_tenant_domain'] = domain_obj.domain # Store for success page
                        request.session['new_tenant_name'] = school.name
                        return redirect(reverse('tenants:registration_success'))
                    else: # PENDING (e.g., payment required)
                        messages.info(request, _("School registration initiated! Please complete the payment to activate your portal."))
                        # TODO: Redirect to a payment initiation page
                        # return redirect(reverse('subscriptions:initiate_payment', kwargs={'subscription_pk': subscription.pk}))
                        # For now, redirecting to success, but ideally to payment.
                        request.session['new_tenant_domain'] = domain_obj.domain 
                        request.session['new_tenant_name'] = school.name
                        return redirect(reverse('tenants:registration_success')) # Placeholder

                except IntegrityError as e:
                    logger.error(f"IntegrityError during registration commit for {admin_email}/{subdomain_slug}: {e}", exc_info=True)
                    messages.error(request, _("This email or subdomain may already be in use, or another data conflict occurred. Please try different details."))
                except Exception as e:
                    logger.error(f"General error during registration commit for {admin_email}/{subdomain_slug}: {e}", exc_info=True)
                    messages.error(request, _("An unexpected error occurred during registration. Our team has been notified. Please try again later or contact support."))
                # If an exception occurred, the form with errors (if any added by pre-checks) or original data will be re-rendered.
        
        else: # form is not valid (from Django's initial form.is_valid())
            logger.warning(f"Registration form invalid on POST: {form.errors.as_json(escape_html=True)}")
            messages.error(request, _("Please correct the errors highlighted in the form below."))
    
    else: # GET request
        form = RegistrationForm(base_domain_for_help_text=settings.APP_HOSTNAME)

    context = {
        'form': form,
        'view_title': _("Register Your School"),
        'tenant_base_hostname_for_display': settings.APP_HOSTNAME 
    }
    return render(request, 'tenants/registration.html', context)



from django.shortcuts import render, redirect, get_object_or_404
from django.contrib import messages
from django.conf import settings
# ... other imports ...
from apps.subscriptions.models import Subscription

# --- ADD THIS NEW VIEW ---
def confirm_and_pay_view(request, subscription_id):
    """
    Displays a confirmation page before the user is sent to the payment gateway.
    This view is part of the public registration flow.
    """
    # Security: Ensure the subscription is still in an incomplete state
    # and maybe check if the user trying to access it is the one who just registered,
    # though using the unguessable subscription_id is a decent check.
    subscription = get_object_or_404(
        Subscription.objects.select_related('plan', 'school'), 
        pk=subscription_id, 
        status='INCOMPLETE'
    )
    
    context = {
        'subscription': subscription,
        'plan': subscription.plan,
        'school': subscription.school,
        # Pass the correct publishable key depending on your gateway
        'stripe_publishable_key': getattr(settings, 'STRIPE_PUBLISHABLE_KEY', None),
        'paystack_public_key': getattr(settings, 'PAYSTACK_PUBLIC_KEY', None),
    }
    
    return render(request, 'tenants/confirm_and_pay.html', context)


class RegistrationSuccessView(TemplateView):
    template_name = "tenants/registration_success.html"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["view_title"] = _("Registration Successful!")
        # Message is now more directly in the template you provided.
        # You could add dynamic school name here if passed via session or context.
        # context['school_name'] = self.request.GET.get('school_name', None) # If passed via query param
        return context
    
    
    
    
