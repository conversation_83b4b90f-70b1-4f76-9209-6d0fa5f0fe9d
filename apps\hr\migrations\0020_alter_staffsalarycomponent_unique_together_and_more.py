# Generated by Django 5.1.9 on 2025-07-09 18:58

import django.db.models.deletion
from decimal import Decimal
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('hr', '0019_alter_leavebalance_unique_together'),
    ]

    operations = [
        migrations.AlterUniqueTogether(
            name='staffsalarycomponent',
            unique_together=None,
        ),
        migrations.RemoveField(
            model_name='staffsalarycomponent',
            name='component',
        ),
        migrations.RemoveField(
            model_name='staffsalarycomponent',
            name='staff_salary',
        ),
        migrations.RemoveField(
            model_name='salarygrade',
            name='is_active',
        ),
        migrations.AddField(
            model_name='staffsalary',
            name='grade',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='staff_salaries', to='hr.salarygrade'),
        ),
        migrations.AlterField(
            model_name='salarygrade',
            name='name',
            field=models.CharField(max_length=100, unique=True, verbose_name='Grade Name'),
        ),
        migrations.AlterField(
            model_name='staffsalary',
            name='basic_salary',
            field=models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text="The gross basic salary per month. Overrides grade's basic if set.", max_digits=10, verbose_name='Basic Salary'),
        ),
        # Skip AlterUniqueTogether for leavebalance - constraint already handled in previous migrations
        migrations.CreateModel(
            name='GradeRule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('value', models.DecimalField(decimal_places=2, help_text='Enter a fixed amount or a percentage (e.g., 7.5 for 7.5%).', max_digits=10, verbose_name='Value/Percentage')),
                ('component', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='hr.salarycomponent')),
                ('grade', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='rules', to='hr.salarygrade')),
            ],
            options={
                'verbose_name': 'Grade Salary Rule',
                'verbose_name_plural': 'Grade Salary Rules',
                'unique_together': {('grade', 'component')},
            },
        ),
        migrations.DeleteModel(
            name='PayslipLineItem',
        ),
        migrations.DeleteModel(
            name='StaffSalaryComponent',
        ),
    ]
