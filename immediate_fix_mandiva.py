#!/usr/bin/env python
"""
Immediate Fix for Mandiva Schema
Adds missing tables directly to the mandiva schema

Usage: python immediate_fix_mandiva.py
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from django.db import connection
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def fix_mandiva_schema():
    """Fix the mandiva schema by adding missing tables"""
    schema_name = 'mandiva'
    
    logger.info(f"=== FIXING SCHEMA: {schema_name} ===")
    
    try:
        with connection.cursor() as cursor:
            # Set search path to mandiva schema
            cursor.execute(f'SET search_path TO "{schema_name}"')
            
            # Check what tables exist
            cursor.execute("""
                SELECT table_name FROM information_schema.tables 
                WHERE table_schema = %s
                ORDER BY table_name
            """, [schema_name])
            existing_tables = [row[0] for row in cursor.fetchall()]
            
            logger.info(f"Existing tables in {schema_name}: {existing_tables}")
            
            # Create missing tables
            tables_to_create = [
                # Schools Academic Year
                """
                CREATE TABLE IF NOT EXISTS schools_academicyear (
                    id BIGSERIAL PRIMARY KEY,
                    name VARCHAR(100) NOT NULL,
                    start_date DATE NOT NULL,
                    end_date DATE NOT NULL,
                    is_current BOOLEAN NOT NULL DEFAULT FALSE,
                    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
                    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
                );
                """,
                
                # Schools Class
                """
                CREATE TABLE IF NOT EXISTS schools_class (
                    id BIGSERIAL PRIMARY KEY,
                    name VARCHAR(100) NOT NULL,
                    description TEXT,
                    academic_year_id BIGINT,
                    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
                    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
                );
                """,
                
                # Schools Subject
                """
                CREATE TABLE IF NOT EXISTS schools_subject (
                    id BIGSERIAL PRIMARY KEY,
                    name VARCHAR(100) NOT NULL,
                    code VARCHAR(20),
                    description TEXT,
                    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
                    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
                );
                """,
                
                # Announcements
                """
                CREATE TABLE IF NOT EXISTS announcements_announcement (
                    id BIGSERIAL PRIMARY KEY,
                    title VARCHAR(255) NOT NULL,
                    content TEXT NOT NULL,
                    target_all_tenant_staff BOOLEAN NOT NULL DEFAULT FALSE,
                    target_all_tenant_parents BOOLEAN NOT NULL DEFAULT FALSE,
                    is_global BOOLEAN NOT NULL DEFAULT FALSE,
                    target_global_audience_type VARCHAR(50),
                    publish_date TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
                    expiry_date TIMESTAMP WITH TIME ZONE,
                    is_published BOOLEAN NOT NULL DEFAULT TRUE,
                    is_sticky BOOLEAN NOT NULL DEFAULT FALSE,
                    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
                    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
                    author_id BIGINT,
                    tenant_id BIGINT
                );
                """,
                
                # Students
                """
                CREATE TABLE IF NOT EXISTS students_student (
                    id BIGSERIAL PRIMARY KEY,
                    student_id VARCHAR(50) UNIQUE,
                    first_name VARCHAR(150) NOT NULL,
                    last_name VARCHAR(150) NOT NULL,
                    date_of_birth DATE,
                    gender VARCHAR(10),
                    class_id BIGINT,
                    is_active BOOLEAN NOT NULL DEFAULT TRUE,
                    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
                    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
                );
                """,
                
                # Parent User
                """
                CREATE TABLE IF NOT EXISTS students_parentuser (
                    id BIGSERIAL PRIMARY KEY,
                    password VARCHAR(128),
                    last_login TIMESTAMP WITH TIME ZONE,
                    is_superuser BOOLEAN NOT NULL DEFAULT FALSE,
                    email VARCHAR(254) UNIQUE NOT NULL,
                    first_name VARCHAR(150) NOT NULL,
                    last_name VARCHAR(150) NOT NULL,
                    phone VARCHAR(20),
                    is_active BOOLEAN NOT NULL DEFAULT TRUE,
                    is_staff BOOLEAN NOT NULL DEFAULT FALSE,
                    date_joined TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
                );
                """,
                
                # Parent User Groups
                """
                CREATE TABLE IF NOT EXISTS students_parentuser_groups (
                    id BIGSERIAL PRIMARY KEY,
                    parentuser_id BIGINT NOT NULL,
                    group_id INTEGER NOT NULL,
                    UNIQUE(parentuser_id, group_id)
                );
                """,
                
                # Content Types
                """
                CREATE TABLE IF NOT EXISTS django_content_type (
                    id SERIAL PRIMARY KEY,
                    app_label VARCHAR(100) NOT NULL,
                    model VARCHAR(100) NOT NULL,
                    UNIQUE(app_label, model)
                );
                """,
                
                # Auth Permissions
                """
                CREATE TABLE IF NOT EXISTS auth_permission (
                    id SERIAL PRIMARY KEY,
                    name VARCHAR(255) NOT NULL,
                    content_type_id INTEGER NOT NULL,
                    codename VARCHAR(100) NOT NULL,
                    UNIQUE(content_type_id, codename)
                );
                """,
                
                # Sessions
                """
                CREATE TABLE IF NOT EXISTS django_session (
                    session_key VARCHAR(40) PRIMARY KEY,
                    session_data TEXT NOT NULL,
                    expire_date TIMESTAMP WITH TIME ZONE NOT NULL
                );
                """,
            ]
            
            # Create each table
            for i, table_sql in enumerate(tables_to_create, 1):
                try:
                    cursor.execute(table_sql)
                    logger.info(f"✅ Created table {i}/{len(tables_to_create)}")
                except Exception as e:
                    logger.warning(f"⚠️ Table {i} creation issue: {e}")
            
            # Mark essential migrations as applied
            migrations_to_mark = [
                ('contenttypes', '0001_initial'),
                ('auth', '0001_initial'),
                ('sessions', '0001_initial'),
                ('schools', '0001_initial'),
                ('students', '0001_initial'),
                ('announcements', '0001_initial'),
                ('announcements', '0002_initial'),
            ]
            
            for app, migration in migrations_to_mark:
                cursor.execute("""
                    INSERT INTO django_migrations (app, name, applied) 
                    VALUES (%s, %s, NOW())
                    ON CONFLICT (app, name) DO NOTHING
                """, [app, migration])
            
            logger.info("✅ Migrations marked as applied")
            
            # Test the academic year table
            cursor.execute("SELECT COUNT(*) FROM schools_academicyear;")
            count = cursor.fetchone()[0]
            logger.info(f"✅ schools_academicyear table accessible (count: {count})")
            
            # Create a default academic year if none exists
            if count == 0:
                cursor.execute("""
                    INSERT INTO schools_academicyear (name, start_date, end_date, is_current)
                    VALUES ('2024-2025', '2024-09-01', '2025-07-31', TRUE)
                """)
                logger.info("✅ Created default academic year")
            
            logger.info(f"🎉 SCHEMA {schema_name} FIXED SUCCESSFULLY!")
            
    except Exception as e:
        logger.error(f"Failed to fix {schema_name}: {e}")
        raise

def test_access():
    """Test that we can access the key tables"""
    schema_name = 'mandiva'
    
    logger.info("=== TESTING TABLE ACCESS ===")
    
    test_tables = [
        'schools_academicyear',
        'announcements_announcement', 
        'schools_staffuser',
        'django_content_type'
    ]
    
    try:
        with connection.cursor() as cursor:
            cursor.execute(f'SET search_path TO "{schema_name}"')
            
            for table in test_tables:
                try:
                    cursor.execute(f"SELECT COUNT(*) FROM {table};")
                    count = cursor.fetchone()[0]
                    logger.info(f"✅ {table}: accessible (count: {count})")
                except Exception as e:
                    logger.error(f"❌ {table}: not accessible: {e}")
                    
    except Exception as e:
        logger.error(f"Test failed: {e}")

def main():
    """Main function"""
    logger.info("=== IMMEDIATE FIX FOR MANDIVA SCHEMA ===")
    
    try:
        # Fix the schema
        fix_mandiva_schema()
        
        # Test access
        test_access()
        
        logger.info("\n🎉 MANDIVA SCHEMA FIXED!")
        logger.info("Try accessing the dashboard again:")
        logger.info("http://mandiva.myapp.test:8000/portal/dashboard/")
        
        return True
        
    except Exception as e:
        logger.error(f"Fix failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
