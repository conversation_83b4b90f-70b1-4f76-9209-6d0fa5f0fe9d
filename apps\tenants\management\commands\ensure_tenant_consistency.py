"""
Ensure Tenant Consistency
Ensures all tenants have consistent structure and automatically fixes new tenants

Usage: 
python manage.py ensure_tenant_consistency --all
python manage.py ensure_tenant_consistency --tenant=schema_name
python manage.py ensure_tenant_consistency --monitor
"""

from django.core.management.base import BaseCommand
from django.db import connection
from django.core.management import call_command
from apps.tenants.models import School
from django_tenants.utils import schema_context
import logging
import time

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Ensure all tenants have consistent structure and fix any issues'

    def add_arguments(self, parser):
        parser.add_argument(
            '--all',
            action='store_true',
            help='Check and fix all tenants',
        )
        parser.add_argument(
            '--tenant',
            type=str,
            help='Check and fix specific tenant',
        )
        parser.add_argument(
            '--monitor',
            action='store_true',
            help='Monitor for new tenants and fix them automatically',
        )
        parser.add_argument(
            '--check-only',
            action='store_true',
            help='Only check, do not fix',
        )

    def handle(self, *args, **options):
        if options['monitor']:
            self.monitor_tenants()
        elif options['all']:
            self.ensure_all_tenants_consistent(options['check_only'])
        elif options['tenant']:
            self.ensure_tenant_consistent(options['tenant'], options['check_only'])
        else:
            self.stdout.write("Use --all, --tenant=<name>, or --monitor")

    def monitor_tenants(self):
        """Monitor for new tenants and fix them automatically"""
        
        self.stdout.write("=== MONITORING TENANTS FOR CONSISTENCY ===")
        
        known_tenants = set()
        
        while True:
            try:
                current_tenants = set(School.objects.exclude(schema_name='public').values_list('schema_name', flat=True))
                
                # Check for new tenants
                new_tenants = current_tenants - known_tenants
                
                if new_tenants:
                    for tenant_name in new_tenants:
                        self.stdout.write(f"\n🔍 NEW TENANT DETECTED: {tenant_name}")
                        self.ensure_tenant_consistent(tenant_name, check_only=False)
                        known_tenants.add(tenant_name)
                
                # Update known tenants
                known_tenants = current_tenants
                
                # Wait before next check
                time.sleep(30)  # Check every 30 seconds
                
            except KeyboardInterrupt:
                self.stdout.write("\n👋 Monitoring stopped by user")
                break
            except Exception as e:
                self.stdout.write(f"❌ Monitoring error: {e}")
                time.sleep(60)  # Wait longer on error

    def ensure_all_tenants_consistent(self, check_only=False):
        """Ensure all tenants have consistent structure"""
        
        self.stdout.write("=== ENSURING ALL TENANTS CONSISTENT ===")
        
        try:
            tenants = School.objects.exclude(schema_name='public')
            
            results = {}
            
            for tenant in tenants:
                self.stdout.write(f"\n--- Checking {tenant.schema_name} ---")
                results[tenant.schema_name] = self.ensure_tenant_consistent(tenant.schema_name, check_only)
            
            # Summary
            self.stdout.write("\n=== CONSISTENCY SUMMARY ===")
            
            consistent = 0
            inconsistent = 0
            
            for tenant_name, result in results.items():
                if result['consistent']:
                    self.stdout.write(f"✅ {tenant_name}: CONSISTENT")
                    consistent += 1
                else:
                    self.stdout.write(f"❌ {tenant_name}: INCONSISTENT")
                    inconsistent += 1
                    for issue in result['issues']:
                        self.stdout.write(f"    - {issue}")
            
            self.stdout.write(f"\nTotal: {len(results)} tenants")
            self.stdout.write(f"Consistent: {consistent}")
            self.stdout.write(f"Inconsistent: {inconsistent}")
            
            if inconsistent == 0:
                self.stdout.write("\n🎉 ALL TENANTS ARE CONSISTENT!")
            else:
                self.stdout.write(f"\n⚠️  {inconsistent} tenants need attention")
                if not check_only:
                    self.stdout.write("Run without --check-only to fix issues")
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"Failed to ensure all tenants consistent: {e}")
            )

    def ensure_tenant_consistent(self, tenant_name, check_only=False):
        """Ensure specific tenant has consistent structure"""
        
        result = {
            'consistent': True,
            'issues': [],
            'fixes_applied': []
        }
        
        try:
            # Check if tenant exists
            try:
                tenant = School.objects.get(schema_name=tenant_name)
            except School.DoesNotExist:
                result['consistent'] = False
                result['issues'].append(f"Tenant '{tenant_name}' not found")
                return result
            
            with connection.cursor() as cursor:
                cursor.execute(f'SET search_path TO "{tenant_name}"')
                
                # 1. Check essential tables
                essential_tables = [
                    'schools_academicyear', 'schools_staffuser', 'students_student',
                    'schools_schoolprofile', 'auth_group', 'auth_permission',
                    'django_content_type', 'hr_leavetype', 'payments_paymentmethod',
                    'fees_concessiontype', 'school_calendar_eventcategory'
                ]
                
                missing_tables = []
                for table in essential_tables:
                    cursor.execute(f"""
                        SELECT EXISTS (
                            SELECT FROM information_schema.tables 
                            WHERE table_schema = '{tenant_name}' 
                            AND table_name = '{table}'
                        )
                    """)
                    
                    if not cursor.fetchone()[0]:
                        missing_tables.append(table)
                
                if missing_tables:
                    result['consistent'] = False
                    result['issues'].append(f"Missing tables: {missing_tables}")
                    
                    if not check_only:
                        self.stdout.write(f"  🔧 Applying universal template to fix missing tables...")
                        call_command('apply_universal_template', tenant=tenant_name, verbosity=0)
                        result['fixes_applied'].append("Applied universal template")
                
                # 2. Check essential columns
                essential_columns = [
                    ('schools_academicyear', 'is_active'),
                    ('students_student', 'roll_number'),
                    ('schools_staffuser', 'employee_id')
                ]
                
                missing_columns = []
                for table, column in essential_columns:
                    try:
                        cursor.execute(f"""
                            SELECT EXISTS (
                                SELECT FROM information_schema.columns 
                                WHERE table_schema = '{tenant_name}' 
                                AND table_name = '{table}'
                                AND column_name = '{column}'
                            )
                        """)
                        
                        if not cursor.fetchone()[0]:
                            missing_columns.append(f"{table}.{column}")
                    except:
                        pass  # Table might not exist
                
                if missing_columns:
                    result['consistent'] = False
                    result['issues'].append(f"Missing columns: {missing_columns}")
                    
                    if not check_only:
                        self.stdout.write(f"  🔧 Applying universal template to fix missing columns...")
                        call_command('apply_universal_template', tenant=tenant_name, verbosity=0)
                        result['fixes_applied'].append("Applied universal template for columns")
                
                # 3. Check essential data
                data_checks = [
                    ('schools_academicyear', 1),
                    ('hr_leavetype', 3),
                    ('payments_paymentmethod', 3)
                ]
                
                missing_data = []
                for table, min_expected in data_checks:
                    try:
                        cursor.execute(f"SELECT COUNT(*) FROM {table}")
                        count = cursor.fetchone()[0]
                        
                        if count < min_expected:
                            missing_data.append(f"{table} ({count} < {min_expected})")
                    except:
                        missing_data.append(f"{table} (table error)")
                
                if missing_data:
                    result['consistent'] = False
                    result['issues'].append(f"Insufficient data: {missing_data}")
                    
                    if not check_only:
                        self.stdout.write(f"  🔧 Applying universal template to fix missing data...")
                        call_command('apply_universal_template', tenant=tenant_name, verbosity=0)
                        result['fixes_applied'].append("Applied universal template for data")
                
                # 4. Test basic functionality
                try:
                    cursor.execute("SELECT COUNT(*) FROM schools_academicyear WHERE is_active = TRUE")
                    active_years = cursor.fetchone()[0]
                    
                    if active_years == 0:
                        result['consistent'] = False
                        result['issues'].append("No active academic year")
                        
                        if not check_only:
                            cursor.execute("UPDATE schools_academicyear SET is_active = TRUE WHERE id = 1")
                            result['fixes_applied'].append("Set academic year as active")
                            
                except Exception as e:
                    result['consistent'] = False
                    result['issues'].append(f"Basic functionality test failed: {e}")
                
                # Log results
                if result['consistent']:
                    self.stdout.write(f"  ✅ {tenant_name}: All checks passed")
                else:
                    self.stdout.write(f"  ❌ {tenant_name}: {len(result['issues'])} issues found")
                    if result['fixes_applied']:
                        self.stdout.write(f"  🔧 {tenant_name}: Applied {len(result['fixes_applied'])} fixes")
                
                return result
                
        except Exception as e:
            result['consistent'] = False
            result['issues'].append(f"Check error: {e}")
            self.stdout.write(f"  ❌ {tenant_name}: Check failed - {e}")
            return result

    def fix_tenant_immediately(self, tenant_name):
        """Immediately fix a tenant that has issues"""
        
        self.stdout.write(f"🚨 IMMEDIATE FIX REQUIRED FOR: {tenant_name}")
        
        try:
            # Apply universal template
            self.stdout.write("  🔧 Applying universal template...")
            call_command('apply_universal_template', tenant=tenant_name, verbosity=0)
            
            # Verify fix
            result = self.ensure_tenant_consistent(tenant_name, check_only=True)
            
            if result['consistent']:
                self.stdout.write(f"  ✅ {tenant_name}: Successfully fixed!")
            else:
                self.stdout.write(f"  ⚠️  {tenant_name}: Still has issues after fix")
                for issue in result['issues']:
                    self.stdout.write(f"    - {issue}")
            
            return result['consistent']
            
        except Exception as e:
            self.stdout.write(f"  ❌ {tenant_name}: Fix failed - {e}")
            return False
