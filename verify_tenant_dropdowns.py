#!/usr/bin/env python
"""
Verify Tenant Dropdowns
Verifies that all dropdown data is working correctly across all tenants

Usage: python verify_tenant_dropdowns.py
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from django.db import connection
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def verify_dropdown_data():
    """Verify dropdown data across all tenant schemas"""
    
    tenant_schemas = ['alpha', 'mandiva', 'aischool', 'bea', 'ruzivo']
    
    # Essential dropdown tables
    dropdown_tables = {
        'schools_academicyear': 'Academic Years',
        'schools_term': 'Terms',
        'schools_schoolclass': 'School Classes',
        'schools_section': 'Sections',
        'fees_feehead': 'Fee Heads',
        'payments_paymentmethod': 'Payment Methods',
        'fees_concessiontype': 'Concession Types',
        'hr_leavetype': 'Leave Types',
        'school_calendar_eventcategory': 'Event Categories'
    }
    
    logger.info("=== VERIFYING DROPDOWN DATA ===")
    
    verification_results = {}
    
    for tenant_schema in tenant_schemas:
        logger.info(f"\nVerifying {tenant_schema}:")
        verification_results[tenant_schema] = {}
        
        try:
            with connection.cursor() as cursor:
                cursor.execute(f'SET search_path TO "{tenant_schema}"')
                
                for table, description in dropdown_tables.items():
                    try:
                        # Check if table exists
                        cursor.execute(f"""
                            SELECT EXISTS (
                                SELECT FROM information_schema.tables 
                                WHERE table_schema = '{tenant_schema}' 
                                AND table_name = '{table}'
                            )
                        """)
                        
                        table_exists = cursor.fetchone()[0]
                        
                        if table_exists:
                            # Count records
                            cursor.execute(f"SELECT COUNT(*) FROM {table}")
                            count = cursor.fetchone()[0]
                            
                            # Get sample data
                            cursor.execute(f"SELECT * FROM {table} LIMIT 3")
                            sample_data = cursor.fetchall()
                            
                            verification_results[tenant_schema][table] = {
                                'exists': True,
                                'count': count,
                                'sample': sample_data
                            }
                            
                            status = "✅" if count > 0 else "⚠️ "
                            logger.info(f"  {status} {description}: {count} records")
                            
                        else:
                            verification_results[tenant_schema][table] = {
                                'exists': False,
                                'count': 0,
                                'sample': []
                            }
                            logger.info(f"  ❌ {description}: Table missing")
                            
                    except Exception as e:
                        verification_results[tenant_schema][table] = {
                            'exists': False,
                            'count': 0,
                            'sample': [],
                            'error': str(e)
                        }
                        logger.error(f"  ❌ {description}: Error - {e}")
                        
        except Exception as e:
            logger.error(f"❌ Failed to verify {tenant_schema}: {e}")
    
    return verification_results

def generate_verification_report(results):
    """Generate a comprehensive verification report"""
    
    logger.info("\n" + "="*80)
    logger.info("COMPREHENSIVE VERIFICATION REPORT")
    logger.info("="*80)
    
    # Summary by table
    logger.info("\n📊 SUMMARY BY TABLE:")
    
    tables = ['schools_academicyear', 'schools_term', 'schools_schoolclass', 'schools_section',
              'fees_feehead', 'payments_paymentmethod', 'fees_concessiontype', 'hr_leavetype',
              'school_calendar_eventcategory']
    
    for table in tables:
        logger.info(f"\n{table.upper()}:")
        for tenant in ['alpha', 'mandiva', 'aischool', 'bea', 'ruzivo']:
            if tenant in results and table in results[tenant]:
                data = results[tenant][table]
                if data['exists']:
                    status = "✅" if data['count'] > 0 else "⚠️ "
                    logger.info(f"  {tenant:10} {status} {data['count']} records")
                else:
                    logger.info(f"  {tenant:10} ❌ Missing")
            else:
                logger.info(f"  {tenant:10} ❌ No data")
    
    # Summary by tenant
    logger.info("\n📊 SUMMARY BY TENANT:")
    
    for tenant in ['alpha', 'mandiva', 'aischool', 'bea', 'ruzivo']:
        logger.info(f"\n{tenant.upper()}:")
        if tenant in results:
            total_tables = len(tables)
            working_tables = 0
            missing_tables = 0
            empty_tables = 0
            
            for table in tables:
                if table in results[tenant]:
                    data = results[tenant][table]
                    if data['exists']:
                        if data['count'] > 0:
                            working_tables += 1
                        else:
                            empty_tables += 1
                    else:
                        missing_tables += 1
                else:
                    missing_tables += 1
            
            logger.info(f"  ✅ Working tables: {working_tables}/{total_tables}")
            logger.info(f"  ⚠️  Empty tables: {empty_tables}")
            logger.info(f"  ❌ Missing tables: {missing_tables}")
            
            if working_tables == total_tables:
                logger.info(f"  🎉 {tenant} is FULLY FUNCTIONAL!")
            elif working_tables >= total_tables * 0.8:
                logger.info(f"  👍 {tenant} is MOSTLY FUNCTIONAL")
            else:
                logger.info(f"  ⚠️  {tenant} needs attention")
    
    # Overall status
    logger.info("\n" + "="*80)
    logger.info("OVERALL STATUS")
    logger.info("="*80)
    
    fully_functional = 0
    mostly_functional = 0
    needs_attention = 0
    
    for tenant in ['mandiva', 'aischool', 'bea', 'ruzivo']:  # Exclude alpha from count
        if tenant in results:
            working_count = sum(1 for table in tables 
                              if table in results[tenant] 
                              and results[tenant][table]['exists'] 
                              and results[tenant][table]['count'] > 0)
            
            if working_count == len(tables):
                fully_functional += 1
            elif working_count >= len(tables) * 0.8:
                mostly_functional += 1
            else:
                needs_attention += 1
    
    total_tenants = 4  # mandiva, aischool, bea, ruzivo
    
    logger.info(f"✅ Fully functional tenants: {fully_functional}/{total_tenants}")
    logger.info(f"👍 Mostly functional tenants: {mostly_functional}/{total_tenants}")
    logger.info(f"⚠️  Tenants needing attention: {needs_attention}/{total_tenants}")
    
    if fully_functional == total_tenants:
        logger.info("\n🎉 ALL TENANTS ARE FULLY FUNCTIONAL!")
        logger.info("All dropdown menus should work perfectly across all tenants!")
    elif fully_functional + mostly_functional == total_tenants:
        logger.info("\n👍 ALL TENANTS ARE FUNCTIONAL!")
        logger.info("Dropdown menus should work well across all tenants!")
    else:
        logger.info(f"\n⚠️  {needs_attention} TENANT(S) NEED ATTENTION")
        logger.info("Some dropdown menus may not work properly")

def main():
    """Main function"""
    logger.info("=== VERIFY TENANT DROPDOWNS ===")
    
    try:
        # Verify dropdown data
        results = verify_dropdown_data()
        
        # Generate comprehensive report
        generate_verification_report(results)
        
        logger.info("\n🎯 NEXT STEPS:")
        logger.info("1. Test dropdown menus in the web interface")
        logger.info("2. Check these key pages:")
        logger.info("   - Student management (class/section dropdowns)")
        logger.info("   - Fee management (academic year/term dropdowns)")
        logger.info("   - Staff management (leave type dropdowns)")
        logger.info("   - Calendar management (event category dropdowns)")
        logger.info("\n🌐 TEST URLS:")
        logger.info("- http://mandiva.myapp.test:8000/portal/")
        logger.info("- http://aischool.myapp.test:8000/portal/")
        logger.info("- http://bea.myapp.test:8000/portal/")
        logger.info("- http://ruzivo.myapp.test:8000/portal/")
        
        return True
        
    except Exception as e:
        logger.error(f"Verification failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
