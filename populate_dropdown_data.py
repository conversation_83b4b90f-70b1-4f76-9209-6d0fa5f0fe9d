#!/usr/bin/env python
"""
Populate Dropdown Data
Creates essential sample data for all dropdowns across all schemas

Usage: python populate_dropdown_data.py
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from django.db import connection
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def populate_essential_data():
    """Populate essential data for dropdowns across all schemas"""
    
    schemas = ['mandiva', 'aischool', 'bea', 'ruzivo']
    
    logger.info("=== POPULATING DROPDOWN DATA ===")
    
    for schema_name in schemas:
        logger.info(f"\nPopulating data for: {schema_name}")
        
        try:
            with connection.cursor() as cursor:
                cursor.execute(f'SET search_path TO "{schema_name}"')
                
                # 1. Academic Years
                logger.info("Creating academic years...")
                cursor.execute("""
                    INSERT INTO schools_academicyear (name, start_date, end_date, is_active, created_at, updated_at) VALUES
                    ('2024-2025', '2024-09-01', '2025-08-31', TRUE, NOW(), NOW()),
                    ('2025-2026', '2025-09-01', '2026-08-31', FALSE, NOW(), NOW())
                    ON CONFLICT DO NOTHING;
                """)
                
                # 2. Terms
                logger.info("Creating terms...")
                cursor.execute("""
                    INSERT INTO schools_term (name, start_date, end_date, academic_year_id, is_active, created_at, updated_at) VALUES
                    ('Term 1', '2024-09-01', '2024-12-15', 1, TRUE, NOW(), NOW()),
                    ('Term 2', '2025-01-15', '2025-04-15', 1, TRUE, NOW(), NOW()),
                    ('Term 3', '2025-04-30', '2025-08-31', 1, TRUE, NOW(), NOW())
                    ON CONFLICT DO NOTHING;
                """)
                
                # 3. School Classes
                logger.info("Creating school classes...")
                cursor.execute("""
                    INSERT INTO schools_schoolclass (name, description, is_active, created_at, updated_at) VALUES
                    ('Grade 1', 'First Grade', TRUE, NOW(), NOW()),
                    ('Grade 2', 'Second Grade', TRUE, NOW(), NOW()),
                    ('Grade 3', 'Third Grade', TRUE, NOW(), NOW()),
                    ('Grade 4', 'Fourth Grade', TRUE, NOW(), NOW()),
                    ('Grade 5', 'Fifth Grade', TRUE, NOW(), NOW()),
                    ('Grade 6', 'Sixth Grade', TRUE, NOW(), NOW()),
                    ('Grade 7', 'Seventh Grade', TRUE, NOW(), NOW()),
                    ('Grade 8', 'Eighth Grade', TRUE, NOW(), NOW()),
                    ('Grade 9', 'Ninth Grade', TRUE, NOW(), NOW()),
                    ('Grade 10', 'Tenth Grade', TRUE, NOW(), NOW()),
                    ('Grade 11', 'Eleventh Grade', TRUE, NOW(), NOW()),
                    ('Grade 12', 'Twelfth Grade', TRUE, NOW(), NOW())
                    ON CONFLICT DO NOTHING;
                """)
                
                # 4. Sections
                logger.info("Creating sections...")
                cursor.execute("""
                    INSERT INTO schools_section (name, school_class_id, is_active, created_at, updated_at) VALUES
                    ('A', 1, TRUE, NOW(), NOW()),
                    ('B', 1, TRUE, NOW(), NOW()),
                    ('A', 2, TRUE, NOW(), NOW()),
                    ('B', 2, TRUE, NOW(), NOW()),
                    ('A', 3, TRUE, NOW(), NOW()),
                    ('B', 3, TRUE, NOW(), NOW()),
                    ('A', 4, TRUE, NOW(), NOW()),
                    ('B', 4, TRUE, NOW(), NOW()),
                    ('A', 5, TRUE, NOW(), NOW()),
                    ('B', 5, TRUE, NOW(), NOW()),
                    ('A', 6, TRUE, NOW(), NOW()),
                    ('B', 6, TRUE, NOW(), NOW())
                    ON CONFLICT DO NOTHING;
                """)
                
                # 5. Account Types
                logger.info("Creating account types...")
                cursor.execute("""
                    INSERT INTO accounting_accounttype (name, code, classification, normal_balance, statement_section, is_active, created_at, updated_at) VALUES
                    ('Cash', 'CASH', 'ASSET', 'DEBIT', 'BS', TRUE, NOW(), NOW()),
                    ('Bank', 'BANK', 'ASSET', 'DEBIT', 'BS', TRUE, NOW(), NOW()),
                    ('Accounts Receivable', 'AR', 'ASSET', 'DEBIT', 'BS', TRUE, NOW(), NOW()),
                    ('Tuition Income', 'TUITION', 'REVENUE', 'CREDIT', 'IS', TRUE, NOW(), NOW()),
                    ('Other Income', 'OTHER_INC', 'REVENUE', 'CREDIT', 'IS', TRUE, NOW(), NOW()),
                    ('Salaries Expense', 'SALARIES', 'EXPENSE', 'DEBIT', 'IS', TRUE, NOW(), NOW()),
                    ('Utilities Expense', 'UTILITIES', 'EXPENSE', 'DEBIT', 'IS', TRUE, NOW(), NOW()),
                    ('Office Supplies', 'SUPPLIES', 'EXPENSE', 'DEBIT', 'IS', TRUE, NOW(), NOW())
                    ON CONFLICT DO NOTHING;
                """)
                
                # 6. Chart of Accounts
                logger.info("Creating chart of accounts...")
                cursor.execute("""
                    INSERT INTO accounting_account (name, code, account_type_id, is_active, is_control_account, can_be_used_in_je, created_at, updated_at) VALUES
                    ('Cash in Hand', '1001', 1, TRUE, FALSE, TRUE, NOW(), NOW()),
                    ('Bank - Main Account', '1002', 2, TRUE, FALSE, TRUE, NOW(), NOW()),
                    ('Student Fees Receivable', '1101', 3, TRUE, FALSE, TRUE, NOW(), NOW()),
                    ('Tuition Fees Income', '4001', 4, TRUE, FALSE, TRUE, NOW(), NOW()),
                    ('Registration Fees', '4002', 5, TRUE, FALSE, TRUE, NOW(), NOW()),
                    ('Staff Salaries', '5001', 6, TRUE, FALSE, TRUE, NOW(), NOW()),
                    ('Electricity', '5101', 7, TRUE, FALSE, TRUE, NOW(), NOW()),
                    ('Office Stationery', '5201', 8, TRUE, FALSE, TRUE, NOW(), NOW())
                    ON CONFLICT DO NOTHING;
                """)
                
                # 7. Fee Heads
                logger.info("Creating fee heads...")
                cursor.execute("""
                    INSERT INTO fees_feehead (name, description, income_account_id, is_active, created_at, updated_at) VALUES
                    ('Tuition Fee', 'Monthly tuition fee', 4, TRUE, NOW(), NOW()),
                    ('Registration Fee', 'One-time registration fee', 5, TRUE, NOW(), NOW()),
                    ('Library Fee', 'Library usage fee', 5, TRUE, NOW(), NOW()),
                    ('Sports Fee', 'Sports activities fee', 5, TRUE, NOW(), NOW()),
                    ('Transport Fee', 'School transport fee', 5, TRUE, NOW(), NOW()),
                    ('Examination Fee', 'Examination fee', 5, TRUE, NOW(), NOW())
                    ON CONFLICT DO NOTHING;
                """)
                
                # 8. Payment Methods
                logger.info("Creating payment methods...")
                cursor.execute("""
                    INSERT INTO payments_paymentmethod (name, description, account_id, is_active, created_at, updated_at) VALUES
                    ('Cash', 'Cash payment', 1, TRUE, NOW(), NOW()),
                    ('Bank Transfer', 'Bank transfer payment', 2, TRUE, NOW(), NOW()),
                    ('Mobile Money', 'Mobile money payment', 2, TRUE, NOW(), NOW()),
                    ('Cheque', 'Cheque payment', 2, TRUE, NOW(), NOW())
                    ON CONFLICT DO NOTHING;
                """)
                
                # 9. Concession Types
                logger.info("Creating concession types...")
                cursor.execute("""
                    INSERT INTO fees_concessiontype (name, description, is_percentage, default_value, is_active, created_at, updated_at) VALUES
                    ('Sibling Discount', 'Discount for siblings', TRUE, 10.00, TRUE, NOW(), NOW()),
                    ('Staff Child Discount', 'Discount for staff children', TRUE, 50.00, TRUE, NOW(), NOW()),
                    ('Merit Scholarship', 'Merit-based scholarship', TRUE, 25.00, TRUE, NOW(), NOW()),
                    ('Financial Hardship', 'Financial hardship assistance', TRUE, 30.00, TRUE, NOW(), NOW())
                    ON CONFLICT DO NOTHING;
                """)
                
                # 10. Leave Types
                logger.info("Creating leave types...")
                cursor.execute("""
                    INSERT INTO hr_leavetype (name, description, max_days_per_year, is_paid, requires_approval, is_active, created_at, updated_at) VALUES
                    ('Annual Leave', 'Annual vacation leave', 21, TRUE, TRUE, TRUE, NOW(), NOW()),
                    ('Sick Leave', 'Medical leave', 10, TRUE, FALSE, TRUE, NOW(), NOW()),
                    ('Maternity Leave', 'Maternity leave', 90, TRUE, TRUE, TRUE, NOW(), NOW()),
                    ('Paternity Leave', 'Paternity leave', 7, TRUE, TRUE, TRUE, NOW(), NOW()),
                    ('Emergency Leave', 'Emergency leave', 3, FALSE, TRUE, TRUE, NOW(), NOW())
                    ON CONFLICT DO NOTHING;
                """)
                
                # 11. Event Categories
                logger.info("Creating event categories...")
                cursor.execute("""
                    INSERT INTO school_calendar_eventcategory (name, color, icon, description, is_active, created_at, updated_at) VALUES
                    ('Academic', '#007bff', 'bi-book', 'Academic events', TRUE, NOW(), NOW()),
                    ('Sports', '#28a745', 'bi-trophy', 'Sports events', TRUE, NOW(), NOW()),
                    ('Cultural', '#ffc107', 'bi-music-note', 'Cultural events', TRUE, NOW(), NOW()),
                    ('Meeting', '#6c757d', 'bi-people', 'Meetings', TRUE, NOW(), NOW()),
                    ('Holiday', '#dc3545', 'bi-calendar-x', 'Holidays', TRUE, NOW(), NOW()),
                    ('Examination', '#fd7e14', 'bi-pencil-square', 'Examinations', TRUE, NOW(), NOW())
                    ON CONFLICT DO NOTHING;
                """)
                
                # 12. Sample Events
                logger.info("Creating sample events...")
                cursor.execute("""
                    INSERT INTO school_calendar_schoolevent (title, description, event_date, event_type, priority, is_active, created_at, updated_at) VALUES
                    ('School Opening Day', 'First day of the new academic year', CURRENT_DATE + INTERVAL '30 days', 'ACADEMIC', 'HIGH', TRUE, NOW(), NOW()),
                    ('Parent-Teacher Meeting', 'Monthly parent-teacher conference', CURRENT_DATE + INTERVAL '45 days', 'MEETING', 'MEDIUM', TRUE, NOW(), NOW()),
                    ('Sports Day', 'Annual school sports competition', CURRENT_DATE + INTERVAL '60 days', 'SPORTS', 'HIGH', TRUE, NOW(), NOW()),
                    ('Science Fair', 'Student science project exhibition', CURRENT_DATE + INTERVAL '75 days', 'ACADEMIC', 'MEDIUM', TRUE, NOW(), NOW()),
                    ('Cultural Festival', 'Annual cultural celebration', CURRENT_DATE + INTERVAL '90 days', 'CULTURAL', 'HIGH', TRUE, NOW(), NOW())
                    ON CONFLICT DO NOTHING;
                """)
                
                logger.info(f"✅ Populated essential data for {schema_name}")
                
        except Exception as e:
            logger.error(f"❌ Failed to populate data for {schema_name}: {e}")

def main():
    """Main function"""
    logger.info("=== POPULATING ALL DROPDOWN DATA ===")
    
    try:
        populate_essential_data()
        
        logger.info("\n🎉 ALL DROPDOWN DATA POPULATED!")
        logger.info("All schemas now have essential data for dropdowns")
        logger.info("Test the dropdown menus - they should all work now!")
        
        return True
        
    except Exception as e:
        logger.error(f"Failed to populate dropdown data: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
