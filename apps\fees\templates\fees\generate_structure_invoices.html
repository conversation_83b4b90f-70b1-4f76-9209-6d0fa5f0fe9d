{# D:\school_fees_saas_v2\apps\fees\templates\fees\generate_structure_invoices.html #}
{% extends "tenant_base.html" %}
{% load static humanize %} {# Load tags if needed #}

{% block title %}{{ view_title|default:"Generate Invoices by Structure" }}{% endblock %}

{% block extra_tenant_css %}
<style>
    .form-select[multiple] {
        min-height: 120px;
        max-height: 200px;
        overflow-y: auto;
    }
    .card-header h6 {
        color: #495057;
    }
    .form-label.fw-bold {
        color: #212529;
        margin-bottom: 0.5rem;
    }
    .optional-filters-card {
        border: 1px solid #e9ecef;
        border-radius: 0.375rem;
    }
    .form-check {
        margin-bottom: 0.5rem;
    }
    .form-check-label {
        font-size: 0.9rem;
        cursor: pointer;
    }
    .student-checkbox {
        transition: background-color 0.2s ease;
        padding: 0.25rem;
        border-radius: 0.25rem;
    }
    .student-checkbox:hover {
        background-color: #f8f9fa;
    }
    .student-checkbox.hidden {
        display: none !important;
    }
    #studentsContainer {
        background-color: #fafafa;
    }
    .btn-sm {
        font-size: 0.8rem;
    }
    .selection-summary {
        background-color: #e3f2fd;
        border: 1px solid #bbdefb;
        border-radius: 0.25rem;
        padding: 0.5rem;
        margin-top: 0.5rem;
        font-size: 0.85rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-10 col-lg-8">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">{{ view_title|default:"Generate Invoices by Structure" }}</h4>
                </div>
                <div class="card-body">
                    {% include "partials/_messages.html" %}

                    <p class="text-muted">
                        Select a fee structure and specify the issue/due dates to generate invoices
                        for all students allocated to that structure.
                    </p>
                    <hr>

                    <form method="post" novalidate>
                        {% csrf_token %}

                        {# Fee Structure Selection #}
                        <div class="mb-4">
                            <label for="{{ form.fee_structure.id_for_label }}" class="form-label fw-bold">{{ form.fee_structure.label }}</label>
                            {{ form.fee_structure }}
                            {% if form.fee_structure.help_text %}
                                <div class="form-text">{{ form.fee_structure.help_text }}</div>
                            {% endif %}
                            {% for error in form.fee_structure.errors %}
                                <div class="invalid-feedback d-block">{{ error }}</div>
                            {% endfor %}
                        </div>

                        {# Date Fields Row #}
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label for="{{ form.issue_date.id_for_label }}" class="form-label fw-bold">{{ form.issue_date.label }}</label>
                                {{ form.issue_date }}
                                {% if form.issue_date.help_text %}
                                    <div class="form-text">{{ form.issue_date.help_text }}</div>
                                {% endif %}
                                {% for error in form.issue_date.errors %}
                                    <div class="invalid-feedback d-block">{{ error }}</div>
                                {% endfor %}
                            </div>
                            <div class="col-md-6">
                                <label for="{{ form.due_date.id_for_label }}" class="form-label fw-bold">{{ form.due_date.label }}</label>
                                {{ form.due_date }}
                                {% if form.due_date.help_text %}
                                    <div class="form-text">{{ form.due_date.help_text }}</div>
                                {% endif %}
                                {% for error in form.due_date.errors %}
                                    <div class="invalid-feedback d-block">{{ error }}</div>
                                {% endfor %}
                            </div>
                        </div>

                        {# Optional Filters Section #}
                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <h6 class="mb-0"><i class="bi bi-funnel me-2"></i>Student Selection</h6>
                                <small class="text-muted">Select classes and students for invoice generation</small>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    {# Classes Selection #}
                                    <div class="col-md-5">
                                        <label class="form-label fw-bold">{{ form.classes.label }}</label>
                                        <div class="border rounded p-3" style="max-height: 200px; overflow-y: auto;">
                                            {% for choice in form.classes %}
                                                <div class="form-check">
                                                    {{ choice.tag }}
                                                    <label class="form-check-label" for="{{ choice.id_for_label }}">
                                                        {{ choice.choice_label }}
                                                    </label>
                                                </div>
                                            {% endfor %}
                                        </div>
                                        {% if form.classes.help_text %}
                                            <div class="form-text">{{ form.classes.help_text }}</div>
                                        {% endif %}
                                        {% for error in form.classes.errors %}
                                            <div class="invalid-feedback d-block">{{ error }}</div>
                                        {% endfor %}
                                    </div>

                                    {# Students Selection #}
                                    <div class="col-md-7">
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <label class="form-label fw-bold mb-0">{{ form.students.label }}</label>
                                            <div>
                                                <button type="button" id="selectAllStudents" class="btn btn-sm btn-outline-primary me-2" disabled>
                                                    <i class="bi bi-check-all me-1"></i>Select All
                                                </button>
                                                <button type="button" id="clearAllStudents" class="btn btn-sm btn-outline-secondary" disabled>
                                                    <i class="bi bi-x-circle me-1"></i>Clear All
                                                </button>
                                            </div>
                                        </div>
                                        <div id="studentsContainer" class="border rounded p-3" style="max-height: 250px; overflow-y: auto;">
                                            <div class="text-muted text-center py-4" id="noStudentsMessage">
                                                <i class="bi bi-info-circle me-2"></i>
                                                Select one or more classes to see students
                                            </div>
                                            <div id="studentsCheckboxes" style="display: none;">
                                                {% for choice in form.students %}
                                                    <div class="form-check student-checkbox" data-class-id="{{ choice.choice_value|add:0 }}">
                                                        {{ choice.tag }}
                                                        <label class="form-check-label" for="{{ choice.id_for_label }}">
                                                            {{ choice.choice_label }}
                                                        </label>
                                                    </div>
                                                {% endfor %}
                                            </div>
                                        </div>
                                        {% if form.students.help_text %}
                                            <div class="form-text">{{ form.students.help_text }}</div>
                                        {% endif %}
                                        {% for error in form.students.errors %}
                                            <div class="invalid-feedback d-block">{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                </div>

                                {# Selection Summary #}
                                <div id="selectionSummary" class="selection-summary mt-3" style="display: none;">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <strong>Classes Selected:</strong> <span id="classCount">0</span>
                                        </div>
                                        <div class="col-md-6">
                                            <strong>Students Selected:</strong> <span id="studentCount">0</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {# Email Notification Option #}
                        <div class="mb-4">
                            <div class="form-check">
                                {{ form.send_email_notification }}
                                <label class="form-check-label" for="{{ form.send_email_notification.id_for_label }}">
                                    {{ form.send_email_notification.label }}
                                </label>
                            </div>
                            {% if form.send_email_notification.help_text %}
                                <div class="form-text">{{ form.send_email_notification.help_text }}</div>
                            {% endif %}
                            {% for error in form.send_email_notification.errors %}
                                <div class="invalid-feedback d-block">{{ error }}</div>
                            {% endfor %}
                        </div>

                        {# Form-wide errors #}
                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                <strong>Please correct the following errors:</strong>
                                <ul class="mb-0">
                                    {% for error in form.non_field_errors %}
                                        <li>{{ error }}</li>
                                    {% endfor %}
                                </ul>
                            </div>
                        {% endif %}

                        <hr>
                        <div class="d-flex justify-content-end gap-2">
                            <a href="{% url 'fees:invoice_list' %}" class="btn btn-outline-secondary">
                                <i class="bi bi-x-circle me-1"></i> Cancel
                            </a>
                            <button type="submit" class="btn btn-success">
                                <i class="bi bi-gear-fill me-1"></i> Generate Invoices
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block page_specific_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Get form elements
        const classCheckboxes = document.querySelectorAll('input[name="classes"]');
        const studentCheckboxes = document.querySelectorAll('.student-checkbox');
        const studentsContainer = document.getElementById('studentsContainer');
        const noStudentsMessage = document.getElementById('noStudentsMessage');
        const studentsCheckboxesDiv = document.getElementById('studentsCheckboxes');
        const selectAllBtn = document.getElementById('selectAllStudents');
        const clearAllBtn = document.getElementById('clearAllStudents');

        // Create a mapping of student checkboxes to their class IDs
        const studentClassMap = new Map();

        // Populate the student-class mapping from the backend data
        {% for student in form.students.field.queryset %}
            studentClassMap.set('{{ student.pk }}', '{{ student.current_class.pk|default:"" }}');
        {% endfor %}

        // Function to filter students based on selected classes
        function filterStudents() {
            const selectedClasses = Array.from(classCheckboxes)
                .filter(cb => cb.checked)
                .map(cb => cb.value);

            let visibleStudents = 0;

            studentCheckboxes.forEach(studentDiv => {
                const studentCheckbox = studentDiv.querySelector('input[type="checkbox"]');
                const studentId = studentCheckbox.value;
                const studentClassId = studentClassMap.get(studentId);

                if (selectedClasses.length === 0 || selectedClasses.includes(studentClassId)) {
                    studentDiv.classList.remove('hidden');
                    visibleStudents++;
                } else {
                    studentDiv.classList.add('hidden');
                    studentCheckbox.checked = false; // Uncheck hidden students
                }
            });

            // Show/hide appropriate sections
            if (selectedClasses.length === 0) {
                noStudentsMessage.style.display = 'block';
                studentsCheckboxesDiv.style.display = 'none';
                selectAllBtn.disabled = true;
                clearAllBtn.disabled = true;
            } else {
                noStudentsMessage.style.display = 'none';
                studentsCheckboxesDiv.style.display = 'block';
                selectAllBtn.disabled = false;
                clearAllBtn.disabled = false;
            }

            updateSelectionSummary();
        }

        // Function to update selection summary
        function updateSelectionSummary() {
            const selectedClasses = Array.from(classCheckboxes).filter(cb => cb.checked);
            const selectedStudents = Array.from(studentCheckboxes)
                .filter(div => !div.classList.contains('hidden'))
                .map(div => div.querySelector('input[type="checkbox"]'))
                .filter(cb => cb.checked);

            // Update button text
            if (selectedStudents.length > 0) {
                selectAllBtn.innerHTML = `<i class="bi bi-check-all me-1"></i>Select All (${getVisibleStudentCount()})`;
                clearAllBtn.innerHTML = `<i class="bi bi-x-circle me-1"></i>Clear (${selectedStudents.length})`;
            } else {
                selectAllBtn.innerHTML = `<i class="bi bi-check-all me-1"></i>Select All`;
                clearAllBtn.innerHTML = `<i class="bi bi-x-circle me-1"></i>Clear All`;
            }

            // Update selection summary
            const summaryDiv = document.getElementById('selectionSummary');
            const classCountSpan = document.getElementById('classCount');
            const studentCountSpan = document.getElementById('studentCount');

            if (selectedClasses.length > 0 || selectedStudents.length > 0) {
                summaryDiv.style.display = 'block';
                classCountSpan.textContent = selectedClasses.length;
                studentCountSpan.textContent = selectedStudents.length;
            } else {
                summaryDiv.style.display = 'none';
            }
        }

        // Function to get count of visible students
        function getVisibleStudentCount() {
            return Array.from(studentCheckboxes).filter(div => !div.classList.contains('hidden')).length;
        }

        // Function to select all visible students
        function selectAllVisibleStudents() {
            studentCheckboxes.forEach(studentDiv => {
                if (!studentDiv.classList.contains('hidden')) {
                    const checkbox = studentDiv.querySelector('input[type="checkbox"]');
                    checkbox.checked = true;
                }
            });
            updateSelectionSummary();
        }

        // Function to clear all student selections
        function clearAllStudentSelections() {
            studentCheckboxes.forEach(studentDiv => {
                const checkbox = studentDiv.querySelector('input[type="checkbox"]');
                checkbox.checked = false;
            });
            updateSelectionSummary();
        }

        // Add event listeners
        classCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', filterStudents);
        });

        studentCheckboxes.forEach(studentDiv => {
            const checkbox = studentDiv.querySelector('input[type="checkbox"]');
            checkbox.addEventListener('change', updateSelectionSummary);
        });

        selectAllBtn.addEventListener('click', selectAllVisibleStudents);
        clearAllBtn.addEventListener('click', clearAllStudentSelections);

        // Initial setup
        filterStudents();
    });
</script>
{% endblock %}


