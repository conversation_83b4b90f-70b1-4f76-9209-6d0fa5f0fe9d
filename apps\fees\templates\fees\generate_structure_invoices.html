{# D:\school_fees_saas_v2\apps\fees\templates\fees\generate_structure_invoices.html #}
{% extends "tenant_base.html" %}
{% load static humanize %} {# Load tags if needed #}

{% block title %}{{ view_title|default:"Generate Invoices by Structure" }}{% endblock %}

{% block extra_tenant_css %}
<style>
    .form-select[multiple] {
        min-height: 120px;
        max-height: 200px;
        overflow-y: auto;
    }
    .card-header h6 {
        color: #495057;
    }
    .form-label.fw-bold {
        color: #212529;
        margin-bottom: 0.5rem;
    }
    .optional-filters-card {
        border: 1px solid #e9ecef;
        border-radius: 0.375rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-10 col-lg-8">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">{{ view_title|default:"Generate Invoices by Structure" }}</h4>
                </div>
                <div class="card-body">
                    {% include "partials/_messages.html" %}

                    <p class="text-muted">
                        Select a fee structure and specify the issue/due dates to generate invoices
                        for all students allocated to that structure.
                    </p>
                    <hr>

                    <form method="post" novalidate>
                        {% csrf_token %}

                        {# Fee Structure Selection #}
                        <div class="mb-4">
                            <label for="{{ form.fee_structure.id_for_label }}" class="form-label fw-bold">{{ form.fee_structure.label }}</label>
                            {{ form.fee_structure }}
                            {% if form.fee_structure.help_text %}
                                <div class="form-text">{{ form.fee_structure.help_text }}</div>
                            {% endif %}
                            {% for error in form.fee_structure.errors %}
                                <div class="invalid-feedback d-block">{{ error }}</div>
                            {% endfor %}
                        </div>

                        {# Date Fields Row #}
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label for="{{ form.issue_date.id_for_label }}" class="form-label fw-bold">{{ form.issue_date.label }}</label>
                                {{ form.issue_date }}
                                {% if form.issue_date.help_text %}
                                    <div class="form-text">{{ form.issue_date.help_text }}</div>
                                {% endif %}
                                {% for error in form.issue_date.errors %}
                                    <div class="invalid-feedback d-block">{{ error }}</div>
                                {% endfor %}
                            </div>
                            <div class="col-md-6">
                                <label for="{{ form.due_date.id_for_label }}" class="form-label fw-bold">{{ form.due_date.label }}</label>
                                {{ form.due_date }}
                                {% if form.due_date.help_text %}
                                    <div class="form-text">{{ form.due_date.help_text }}</div>
                                {% endif %}
                                {% for error in form.due_date.errors %}
                                    <div class="invalid-feedback d-block">{{ error }}</div>
                                {% endfor %}
                            </div>
                        </div>

                        {# Optional Filters Section #}
                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <h6 class="mb-0"><i class="bi bi-funnel me-2"></i>Optional Filters</h6>
                                <small class="text-muted">Use these to generate invoices for specific classes or students only</small>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <label for="{{ form.classes.id_for_label }}" class="form-label">{{ form.classes.label }}</label>
                                        {{ form.classes }}
                                        {% if form.classes.help_text %}
                                            <div class="form-text">{{ form.classes.help_text }}</div>
                                        {% endif %}
                                        {% for error in form.classes.errors %}
                                            <div class="invalid-feedback d-block">{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                    <div class="col-md-6">
                                        <label for="{{ form.students.id_for_label }}" class="form-label">{{ form.students.label }}</label>
                                        {{ form.students }}
                                        {% if form.students.help_text %}
                                            <div class="form-text">{{ form.students.help_text }}</div>
                                        {% endif %}
                                        {% for error in form.students.errors %}
                                            <div class="invalid-feedback d-block">{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                </div>
                            </div>
                        </div>

                        {# Email Notification Option #}
                        <div class="mb-4">
                            <div class="form-check">
                                {{ form.send_email_notification }}
                                <label class="form-check-label" for="{{ form.send_email_notification.id_for_label }}">
                                    {{ form.send_email_notification.label }}
                                </label>
                            </div>
                            {% if form.send_email_notification.help_text %}
                                <div class="form-text">{{ form.send_email_notification.help_text }}</div>
                            {% endif %}
                            {% for error in form.send_email_notification.errors %}
                                <div class="invalid-feedback d-block">{{ error }}</div>
                            {% endfor %}
                        </div>

                        {# Form-wide errors #}
                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                <strong>Please correct the following errors:</strong>
                                <ul class="mb-0">
                                    {% for error in form.non_field_errors %}
                                        <li>{{ error }}</li>
                                    {% endfor %}
                                </ul>
                            </div>
                        {% endif %}

                        <hr>
                        <div class="d-flex justify-content-end gap-2">
                            <a href="{% url 'fees:invoice_list' %}" class="btn btn-outline-secondary">
                                <i class="bi bi-x-circle me-1"></i> Cancel
                            </a>
                            <button type="submit" class="btn btn-success">
                                <i class="bi bi-gear-fill me-1"></i> Generate Invoices
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block page_specific_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Get form elements
        const feeStructureSelect = document.getElementById('{{ form.fee_structure.id_for_label }}');
        const classesSelect = document.getElementById('{{ form.classes.id_for_label }}');
        const studentsSelect = document.getElementById('{{ form.students.id_for_label }}');

        // Store original student options for filtering
        let originalStudentOptions = [];
        if (studentsSelect) {
            originalStudentOptions = Array.from(studentsSelect.options).map(opt => ({
                value: opt.value,
                text: opt.text,
                classId: opt.dataset.classId // Assuming this data attribute exists
            }));
        }

        // Function to filter students based on selected classes
        function filterStudentsByClasses() {
            if (!studentsSelect || !classesSelect) return;

            const selectedClasses = Array.from(classesSelect.selectedOptions).map(opt => opt.value);

            // Clear current options
            studentsSelect.innerHTML = '';

            // Add filtered options
            originalStudentOptions.forEach(student => {
                if (selectedClasses.length === 0 || selectedClasses.includes(student.classId)) {
                    const option = new Option(student.text, student.value);
                    studentsSelect.appendChild(option);
                }
            });
        }

        // Add event listener for class selection changes
        if (classesSelect) {
            classesSelect.addEventListener('change', filterStudentsByClasses);
        }

        // Add helpful tooltips and instructions
        if (feeStructureSelect) {
            feeStructureSelect.addEventListener('change', function() {
                // You could add logic here to update available classes based on fee structure
                console.log('Fee structure changed:', this.value);
            });
        }

        // Additional functionality can be added here as needed
        }
    });
</script>
{% endblock %}


