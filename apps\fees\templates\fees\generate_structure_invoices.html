{# D:\school_fees_saas_v2\apps\fees\templates\fees\generate_structure_invoices.html #}
{% extends "tenant_base.html" %}
{% load static humanize %} {# Load tags if needed #}

{% block title %}{{ view_title|default:"Generate Invoices by Structure" }}{% endblock %}

{% block extra_tenant_css %}
<style>
    /* Premium Page Layout */
    .premium-container {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 2rem 0;
    }

    .premium-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 24px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .premium-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
    }

    /* Premium Header */
    .premium-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem;
        text-align: center;
        position: relative;
        overflow: hidden;
    }

    .premium-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        opacity: 0.3;
    }

    .premium-header h1 {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        position: relative;
        z-index: 1;
    }

    .premium-header p {
        font-size: 1.1rem;
        opacity: 0.9;
        margin-bottom: 0;
        position: relative;
        z-index: 1;
    }

    /* Premium Form Styling */
    .premium-form-container {
        padding: 3rem;
    }

    .premium-form-group {
        margin-bottom: 2rem;
    }

    .premium-label {
        font-size: 1rem;
        font-weight: 600;
        color: #2d3748;
        margin-bottom: 0.75rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .premium-label i {
        color: #667eea;
        font-size: 1.1rem;
    }

    .premium-input {
        border: 2px solid #e2e8f0;
        border-radius: 12px;
        padding: 0.875rem 1rem;
        font-size: 1rem;
        transition: all 0.3s ease;
        background: #ffffff;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
    }

    .premium-input:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        outline: none;
        transform: translateY(-1px);
    }

    .premium-select {
        border: 2px solid #e2e8f0;
        border-radius: 12px;
        padding: 0.875rem 1rem;
        font-size: 1rem;
        transition: all 0.3s ease;
        background: #ffffff;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
    }

    .premium-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        outline: none;
    }

    /* Premium Cards for Sections */
    .premium-section-card {
        background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
        border: 1px solid #e2e8f0;
        border-radius: 16px;
        padding: 0;
        margin-bottom: 2rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .premium-section-card:hover {
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        transform: translateY(-1px);
    }

    .premium-section-header {
        background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
        padding: 1.5rem 2rem;
        border-bottom: 1px solid #e2e8f0;
    }

    .premium-section-header h6 {
        font-size: 1.1rem;
        font-weight: 600;
        color: #2d3748;
        margin-bottom: 0.25rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .premium-section-header h6 i {
        color: #667eea;
        font-size: 1.2rem;
    }

    .premium-section-header small {
        color: #718096;
        font-size: 0.9rem;
    }

    .premium-section-body {
        padding: 2rem;
    }

    /* Premium Checkbox Styling */
    .premium-checkbox-container {
        background: #ffffff;
        border: 1px solid #e2e8f0;
        border-radius: 12px;
        padding: 1.5rem;
        max-height: 280px;
        overflow-y: auto;
        box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.02);
    }

    .premium-checkbox-container::-webkit-scrollbar {
        width: 8px;
    }

    .premium-checkbox-container::-webkit-scrollbar-track {
        background: #f1f5f9;
        border-radius: 4px;
    }

    .premium-checkbox-container::-webkit-scrollbar-thumb {
        background: #cbd5e0;
        border-radius: 4px;
    }

    .premium-checkbox-container::-webkit-scrollbar-thumb:hover {
        background: #a0aec0;
    }

    .premium-form-check {
        margin-bottom: 0.75rem;
        padding: 0.5rem 0.75rem;
        border-radius: 8px;
        transition: all 0.2s ease;
        border: 1px solid transparent;
    }

    .premium-form-check:hover {
        background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
        border-color: #e2e8f0;
        transform: translateX(2px);
    }

    .premium-form-check input[type="checkbox"] {
        width: 18px;
        height: 18px;
        margin-right: 0.75rem;
        accent-color: #667eea;
        cursor: pointer;
    }

    .premium-form-check label {
        font-size: 0.95rem;
        color: #4a5568;
        cursor: pointer;
        font-weight: 500;
        line-height: 1.4;
    }

    .premium-form-check.student-checkbox.hidden {
        display: none !important;
    }

    /* Premium Buttons */
    .premium-btn {
        padding: 0.75rem 1.5rem;
        border-radius: 10px;
        font-weight: 600;
        font-size: 0.9rem;
        transition: all 0.3s ease;
        border: none;
        cursor: pointer;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        text-decoration: none;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .premium-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .premium-btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }

    .premium-btn-primary:hover {
        background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
        color: white;
    }

    .premium-btn-outline {
        background: rgba(102, 126, 234, 0.1);
        color: #667eea;
        border: 1px solid rgba(102, 126, 234, 0.3);
    }

    .premium-btn-outline:hover {
        background: rgba(102, 126, 234, 0.2);
        color: #5a67d8;
        border-color: rgba(102, 126, 234, 0.5);
    }

    .premium-btn-secondary {
        background: linear-gradient(135deg, #718096 0%, #4a5568 100%);
        color: white;
    }

    .premium-btn-secondary:hover {
        background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%);
        color: white;
    }

    .premium-btn-success {
        background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
        color: white;
        font-size: 1rem;
        padding: 1rem 2rem;
    }

    .premium-btn-success:hover {
        background: linear-gradient(135deg, #38a169 0%, #2f855a 100%);
        color: white;
    }

    .premium-btn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none;
    }

    .premium-btn:disabled:hover {
        transform: none;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    /* Premium Selection Summary */
    .premium-summary {
        background: linear-gradient(135deg, #e6fffa 0%, #b2f5ea 100%);
        border: 1px solid #81e6d9;
        border-radius: 12px;
        padding: 1.25rem;
        margin-top: 1.5rem;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    .premium-summary-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .premium-summary-item {
        text-align: center;
    }

    .premium-summary-number {
        font-size: 1.5rem;
        font-weight: 700;
        color: #2d3748;
        display: block;
    }

    .premium-summary-label {
        font-size: 0.85rem;
        color: #4a5568;
        font-weight: 500;
    }

    /* Premium Help Text */
    .premium-help-text {
        font-size: 0.85rem;
        color: #718096;
        margin-top: 0.5rem;
        font-style: italic;
    }

    /* Premium Empty State */
    .premium-empty-state {
        text-align: center;
        padding: 3rem 2rem;
        color: #718096;
    }

    .premium-empty-state i {
        font-size: 3rem;
        color: #cbd5e0;
        margin-bottom: 1rem;
    }

    .premium-empty-state h6 {
        color: #4a5568;
        margin-bottom: 0.5rem;
    }

    /* Premium Animations */
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .premium-fade-in {
        animation: fadeInUp 0.6s ease-out;
    }

    /* Premium Responsive */
    @media (max-width: 768px) {
        .premium-container {
            padding: 1rem 0;
        }

        .premium-form-container {
            padding: 2rem 1.5rem;
        }

        .premium-header h1 {
            font-size: 2rem;
        }

        .premium-section-body {
            padding: 1.5rem;
        }

        .premium-summary-content {
            flex-direction: column;
            gap: 1rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="premium-container">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-11 col-xl-10">
                <div class="premium-card premium-fade-in">
                    <div class="premium-header">
                        <h1><i class="bi bi-magic me-3"></i>Invoice Generation Studio</h1>
                        <p>Create professional invoices with precision and elegance</p>
                    </div>
                    <div class="premium-form-container">
                        {% include "partials/_messages.html" %}

                        <form method="post" novalidate>
                        {% csrf_token %}

                        {# Fee Structure Selection #}
                        <div class="premium-form-group">
                            <label for="{{ form.fee_structure.id_for_label }}" class="premium-label">
                                <i class="bi bi-diagram-3"></i>{{ form.fee_structure.label }}
                            </label>
                            <select name="{{ form.fee_structure.name }}" id="{{ form.fee_structure.id_for_label }}" class="premium-select form-control" required>
                                {% for choice in form.fee_structure %}
                                    {{ choice.tag }}
                                {% endfor %}
                            </select>
                            {% if form.fee_structure.help_text %}
                                <div class="premium-help-text">{{ form.fee_structure.help_text }}</div>
                            {% endif %}
                            {% for error in form.fee_structure.errors %}
                                <div class="invalid-feedback d-block">{{ error }}</div>
                            {% endfor %}
                        </div>

                        {# Date Fields Row #}
                        <div class="row">
                            <div class="col-md-6">
                                <div class="premium-form-group">
                                    <label for="{{ form.issue_date.id_for_label }}" class="premium-label">
                                        <i class="bi bi-calendar-event"></i>{{ form.issue_date.label }}
                                    </label>
                                    <input type="date" name="{{ form.issue_date.name }}" id="{{ form.issue_date.id_for_label }}"
                                           class="premium-input form-control" value="{{ form.issue_date.value|default:'' }}" required>
                                    {% if form.issue_date.help_text %}
                                        <div class="premium-help-text">{{ form.issue_date.help_text }}</div>
                                    {% endif %}
                                    {% for error in form.issue_date.errors %}
                                        <div class="invalid-feedback d-block">{{ error }}</div>
                                    {% endfor %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="premium-form-group">
                                    <label for="{{ form.due_date.id_for_label }}" class="premium-label">
                                        <i class="bi bi-calendar-check"></i>{{ form.due_date.label }}
                                    </label>
                                    <input type="date" name="{{ form.due_date.name }}" id="{{ form.due_date.id_for_label }}"
                                           class="premium-input form-control" value="{{ form.due_date.value|default:'' }}" required>
                                    {% if form.due_date.help_text %}
                                        <div class="premium-help-text">{{ form.due_date.help_text }}</div>
                                    {% endif %}
                                    {% for error in form.due_date.errors %}
                                        <div class="invalid-feedback d-block">{{ error }}</div>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>

                        {# Student Selection Section #}
                        <div class="premium-section-card">
                            <div class="premium-section-header">
                                <h6><i class="bi bi-people-fill"></i>Student Selection</h6>
                                <small>Choose classes and students for targeted invoice generation</small>
                            </div>
                            <div class="premium-section-body">
                                <div class="row">
                                    {# Classes Selection #}
                                    <div class="col-md-5">
                                        <label class="premium-label">
                                            <i class="bi bi-collection"></i>{{ form.classes.label }}
                                        </label>
                                        <div class="premium-checkbox-container">
                                            {% for choice in form.classes %}
                                                <div class="premium-form-check">
                                                    {{ choice.tag }}
                                                    <label class="form-check-label" for="{{ choice.id_for_label }}">
                                                        {{ choice.choice_label }}
                                                    </label>
                                                </div>
                                            {% endfor %}
                                        </div>
                                        {% if form.classes.help_text %}
                                            <div class="premium-help-text">{{ form.classes.help_text }}</div>
                                        {% endif %}
                                        {% for error in form.classes.errors %}
                                            <div class="invalid-feedback d-block">{{ error }}</div>
                                        {% endfor %}
                                    </div>

                                    {# Students Selection #}
                                    <div class="col-md-7">
                                        <div class="d-flex justify-content-between align-items-center mb-3">
                                            <label class="premium-label mb-0">
                                                <i class="bi bi-person-check"></i>{{ form.students.label }}
                                            </label>
                                            <div class="d-flex gap-2">
                                                <button type="button" id="selectAllStudents" class="premium-btn premium-btn-outline" disabled>
                                                    <i class="bi bi-check-all"></i>Select All
                                                </button>
                                                <button type="button" id="clearAllStudents" class="premium-btn premium-btn-secondary" disabled>
                                                    <i class="bi bi-x-circle"></i>Clear All
                                                </button>
                                            </div>
                                        </div>
                                        <div id="studentsContainer" class="premium-checkbox-container" style="max-height: 320px;">
                                            <div class="premium-empty-state" id="noStudentsMessage">
                                                <i class="bi bi-mortarboard"></i>
                                                <h6>Select Classes First</h6>
                                                <p>Choose one or more classes to see available students</p>
                                            </div>
                                            <div id="studentsCheckboxes" style="display: none;">
                                                {% for choice in form.students %}
                                                    <div class="premium-form-check student-checkbox" data-class-id="{{ choice.choice_value|add:0 }}">
                                                        {{ choice.tag }}
                                                        <label class="form-check-label" for="{{ choice.id_for_label }}">
                                                            {{ choice.choice_label }}
                                                        </label>
                                                    </div>
                                                {% endfor %}
                                            </div>
                                        </div>
                                        {% if form.students.help_text %}
                                            <div class="premium-help-text">{{ form.students.help_text }}</div>
                                        {% endif %}
                                        {% for error in form.students.errors %}
                                            <div class="invalid-feedback d-block">{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                </div>

                                {# Selection Summary #}
                                <div id="selectionSummary" class="premium-summary" style="display: none;">
                                    <div class="premium-summary-content">
                                        <div class="premium-summary-item">
                                            <span class="premium-summary-number" id="classCount">0</span>
                                            <span class="premium-summary-label">Classes Selected</span>
                                        </div>
                                        <div class="premium-summary-item">
                                            <span class="premium-summary-number" id="studentCount">0</span>
                                            <span class="premium-summary-label">Students Selected</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {# Email Notification Option #}
                        <div class="premium-section-card">
                            <div class="premium-section-header">
                                <h6><i class="bi bi-envelope-at"></i>Notification Settings</h6>
                                <small>Configure email notifications for parents</small>
                            </div>
                            <div class="premium-section-body">
                                <div class="premium-form-check">
                                    {{ form.send_email_notification }}
                                    <label class="form-check-label" for="{{ form.send_email_notification.id_for_label }}">
                                        <strong>{{ form.send_email_notification.label }}</strong>
                                    </label>
                                </div>
                                {% if form.send_email_notification.help_text %}
                                    <div class="premium-help-text">{{ form.send_email_notification.help_text }}</div>
                                {% endif %}
                                {% for error in form.send_email_notification.errors %}
                                    <div class="invalid-feedback d-block">{{ error }}</div>
                                {% endfor %}
                            </div>
                        </div>

                        {# Form-wide errors #}
                        {% if form.non_field_errors %}
                            <div class="alert alert-danger rounded-3 border-0 shadow-sm">
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-exclamation-triangle-fill me-2 fs-4"></i>
                                    <div>
                                        <strong>Please correct the following errors:</strong>
                                        <ul class="mb-0 mt-1">
                                            {% for error in form.non_field_errors %}
                                                <li>{{ error }}</li>
                                            {% endfor %}
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        {% endif %}

                        <div class="d-flex justify-content-between align-items-center pt-4 mt-4" style="border-top: 2px solid #e2e8f0;">
                            <a href="{% url 'fees:invoice_list' %}" class="premium-btn premium-btn-secondary">
                                <i class="bi bi-arrow-left"></i> Back to Invoices
                            </a>
                            <button type="submit" class="premium-btn premium-btn-success">
                                <i class="bi bi-magic"></i> Generate Invoices
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block page_specific_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Get form elements
        const classCheckboxes = document.querySelectorAll('input[name="classes"]');
        const studentCheckboxes = document.querySelectorAll('.student-checkbox');
        const studentsContainer = document.getElementById('studentsContainer');
        const noStudentsMessage = document.getElementById('noStudentsMessage');
        const studentsCheckboxesDiv = document.getElementById('studentsCheckboxes');
        const selectAllBtn = document.getElementById('selectAllStudents');
        const clearAllBtn = document.getElementById('clearAllStudents');

        // Create a mapping of student checkboxes to their class IDs
        const studentClassMap = new Map();

        // Populate the student-class mapping from the backend data
        {% for student in form.students.field.queryset %}
            studentClassMap.set('{{ student.pk }}', '{{ student.current_class.pk|default:"" }}');
        {% endfor %}

        // Function to filter students based on selected classes
        function filterStudents() {
            const selectedClasses = Array.from(classCheckboxes)
                .filter(cb => cb.checked)
                .map(cb => cb.value);

            let visibleStudents = 0;

            studentCheckboxes.forEach(studentDiv => {
                const studentCheckbox = studentDiv.querySelector('input[type="checkbox"]');
                const studentId = studentCheckbox.value;
                const studentClassId = studentClassMap.get(studentId);

                if (selectedClasses.length === 0 || selectedClasses.includes(studentClassId)) {
                    studentDiv.classList.remove('hidden');
                    visibleStudents++;
                } else {
                    studentDiv.classList.add('hidden');
                    studentCheckbox.checked = false; // Uncheck hidden students
                }
            });

            // Show/hide appropriate sections
            if (selectedClasses.length === 0) {
                noStudentsMessage.style.display = 'block';
                studentsCheckboxesDiv.style.display = 'none';
                selectAllBtn.disabled = true;
                clearAllBtn.disabled = true;
            } else {
                noStudentsMessage.style.display = 'none';
                studentsCheckboxesDiv.style.display = 'block';
                selectAllBtn.disabled = false;
                clearAllBtn.disabled = false;
            }

            updateSelectionSummary();
        }

        // Function to update selection summary
        function updateSelectionSummary() {
            const selectedClasses = Array.from(classCheckboxes).filter(cb => cb.checked);
            const selectedStudents = Array.from(studentCheckboxes)
                .filter(div => !div.classList.contains('hidden'))
                .map(div => div.querySelector('input[type="checkbox"]'))
                .filter(cb => cb.checked);

            // Update button text
            if (selectedStudents.length > 0) {
                selectAllBtn.innerHTML = `<i class="bi bi-check-all me-1"></i>Select All (${getVisibleStudentCount()})`;
                clearAllBtn.innerHTML = `<i class="bi bi-x-circle me-1"></i>Clear (${selectedStudents.length})`;
            } else {
                selectAllBtn.innerHTML = `<i class="bi bi-check-all me-1"></i>Select All`;
                clearAllBtn.innerHTML = `<i class="bi bi-x-circle me-1"></i>Clear All`;
            }

            // Update selection summary
            const summaryDiv = document.getElementById('selectionSummary');
            const classCountSpan = document.getElementById('classCount');
            const studentCountSpan = document.getElementById('studentCount');

            if (selectedClasses.length > 0 || selectedStudents.length > 0) {
                summaryDiv.style.display = 'block';
                classCountSpan.textContent = selectedClasses.length;
                studentCountSpan.textContent = selectedStudents.length;
            } else {
                summaryDiv.style.display = 'none';
            }
        }

        // Function to get count of visible students
        function getVisibleStudentCount() {
            return Array.from(studentCheckboxes).filter(div => !div.classList.contains('hidden')).length;
        }

        // Function to select all visible students
        function selectAllVisibleStudents() {
            studentCheckboxes.forEach(studentDiv => {
                if (!studentDiv.classList.contains('hidden')) {
                    const checkbox = studentDiv.querySelector('input[type="checkbox"]');
                    checkbox.checked = true;
                }
            });
            updateSelectionSummary();
        }

        // Function to clear all student selections
        function clearAllStudentSelections() {
            studentCheckboxes.forEach(studentDiv => {
                const checkbox = studentDiv.querySelector('input[type="checkbox"]');
                checkbox.checked = false;
            });
            updateSelectionSummary();
        }

        // Add event listeners
        classCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', filterStudents);
        });

        studentCheckboxes.forEach(studentDiv => {
            const checkbox = studentDiv.querySelector('input[type="checkbox"]');
            checkbox.addEventListener('change', updateSelectionSummary);
        });

        selectAllBtn.addEventListener('click', selectAllVisibleStudents);
        clearAllBtn.addEventListener('click', clearAllStudentSelections);

        // Initial setup
        filterStudents();
    });
</script>
{% endblock %}


