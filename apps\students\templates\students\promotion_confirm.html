{% extends "base.html" %}
{% load i18n %}

{% block title %}{{ page_title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <h1 class="h3 mb-4 text-gray-800">{{ page_title }}</h1>
    
    {% if error %}
        <div class="alert alert-danger">{{ error }}</div>
        <a href="{% url 'students:promotion_setup' %}" class="btn btn-primary">{% trans "Start Over" %}</a>
    {% else %}
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">{% trans "Promotion Summary" %}</h6>
            </div>
            <div class="card-body">
                <p class="lead">
                    {% blocktrans with from_year=from_year.name from_class=from_class.name to_year=to_year.name %}
                    You are about to process promotions for students in <strong>{{ from_class }}</strong> from the <strong>{{ from_year }}</strong> academic year to the <strong>{{ to_year }}</strong> academic year.
                    {% endblocktrans %}
                </p>
                <p class="text-danger">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>{% trans "Please review the following actions carefully. This step is final and cannot be undone." %}</strong>
                </p>
                <hr>

                <!-- Promoted Students -->
                {% if promoted_list %}
                    <div class="mb-4">
                        <h5>{% blocktrans count counter=promoted_list|length %}The following {{ counter }} student will be <span class="text-success">PROMOTED</span>:{% plural %}The following {{ counter }} students will be <span class="text-success">PROMOTED</span>:{% endblocktrans %}</h5>
                        <ul>
                            {% for item in promoted_list %}
                                <li>{{ item.student.full_name }} (to {{ item.to_class.name }})</li>
                            {% endfor %}
                        </ul>
                    </div>
                {% endif %}

                <!-- Retained Students -->
                {% if retained_list %}
                    <div class="mb-4">
                        <h5>{% blocktrans count counter=retained_list|length %}The following {{ counter }} student will be <span class="text-warning">RETAINED</span>:{% plural %}The following {{ counter }} students will be <span class="text-warning">RETAINED</span>:{% endblocktrans %}</h5>
                        <ul>
                            {% for item in retained_list %}
                                <li>{{ item.student.full_name }} (in {{ item.to_class.name }})</li>
                            {% endfor %}
                        </ul>
                    </div>
                {% endif %}

                <!-- Graduated Students -->
                {% if graduated_list %}
                    <div class="mb-4">
                        <h5>{% blocktrans count counter=graduated_list|length %}The following {{ counter }} student will be marked as <span class="text-info">GRADUATED</span>:{% plural %}The following {{ counter }} students will be marked as <span class="text-info">GRADUATED</span>:{% endblocktrans %}</h5>
                        <ul>
                            {% for item in graduated_list %}
                                <li>{{ item.student.full_name }}</li>
                            {% endfor %}
                        </ul>
                    </div>
                {% endif %}

                <!-- Action Form -->
                <form action="{% url 'students:promotion_execute' %}" method="post" class="mt-5">
                    {% csrf_token %}
                    <div class="d-flex justify-content-between">
                        <a href="{{ review_url }}" class="btn btn-secondary btn-lg">
                            ← {% trans "Go Back & Make Changes" %}
                        </a>
                        <button type="submit" class="btn btn-danger btn-lg">
                            <i class="fas fa-check-circle"></i> {% trans "Confirm & Execute Promotions" %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    {% endif %}
</div>
{% endblock %}


