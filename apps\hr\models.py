# D:\school_fees_saas_v2\apps\hr\models.py
from django.db import models
from django.conf import settings # For settings.AUTH_USER_MODEL or settings.STAFF_USER_MODEL
from django.utils import timezone

from django.urls import reverse
from django.utils.translation import gettext_lazy as _
from decimal import Decimal
from datetime import timedelta

from apps.accounting.models import Account

from django.db.models import Sum, Q
from django.db.models.functions import Coalesce # <<< ADD THIS IMPORT

import logging
logger = logging.getLogger(__name__)

# Import StaffUser from the schools app
# This creates a dependency: hr app depends on schools app.
# Ensure 'apps.schools' appears before 'apps.hr' in INSTALLED_APPS if this becomes an issue,
# or use string references like 'schools.StaffUser'.
from apps.schools.models import StaffUser

# --- Constants for Choices ---
GENDER_CHOICES = [
    ('MALE', 'Male'),
    ('FEMALE', 'Female'),
    ('OTHER', 'Other'),
    ('UNSPECIFIED', 'Prefer not to say'),
]

EMPLOYMENT_TYPE_CHOICES = [
    ('FULL_TIME', 'Full-Time'),
    ('PART_TIME', 'Part-Time'),
    ('CONTRACT', 'Contract'),
    ('INTERN', 'Intern'),
    ('TEMPORARY', 'Temporary'),
]

MARITAL_STATUS_CHOICES = [
    ('SINGLE', 'Single'),
    ('MARRIED', 'Married'),
    ('DIVORCED', 'Divorced'),
    ('WIDOWED', 'Widowed'),
    ('OTHER', 'Other'),
]
# # --- Reusable Choices ---
# GENDER_CHOICES = [('MALE', 'Male'), ('FEMALE', 'Female'), ('OTHER', 'Other')]
# EMPLOYMENT_TYPE_CHOICES = [('FULL_TIME', 'Full-Time'), ('PART_TIME', 'Part-Time'), ('CONTRACT', 'Contract')]
# MARITAL_STATUS_CHOICES = [('SINGLE', 'Single'), ('MARRIED', 'Married'), ('DIVORCED', 'Divorced'), ('WIDOWED', 'Widowed')]

class EmployeeProfile(models.Model):
    """
    Stores all HR-specific details for a StaffUser.
    This model acts as an extension of the StaffUser model.
    """
    # This is the crucial link. Using primary_key=True makes this a true
    # 1-to-1 extension of the StaffUser model.
    user = models.OneToOneField(
        StaffUser,
        on_delete=models.CASCADE,
        primary_key=True,
        related_name='hr_profile' # Allows easy access: staff_user.hr_profile
    )
    
    # --- Employment Details ---
    employee_id = models.CharField(_("employee ID"), max_length=50, unique=True, blank=True, null=True, help_text=_("Unique school-assigned employee ID."))
    designation = models.CharField(_("designation"), max_length=100, blank=True)
    department = models.CharField(_("department"), max_length=100, blank=True)
    date_hired = models.DateField(_("date hired"), null=True, blank=True)
    date_left = models.DateField(_("date left employment"), null=True, blank=True)
    employment_type = models.CharField(_("employment type"), max_length=20, choices=EMPLOYMENT_TYPE_CHOICES, blank=True, null=True)

    # --- Personal Details ---
    middle_name = models.CharField(_("middle name(s)"), max_length=100, blank=True)
    gender = models.CharField(_("gender"), max_length=15, choices=GENDER_CHOICES, blank=True, null=True)
    date_of_birth = models.DateField(_("date of birth"), null=True, blank=True)
    marital_status = models.CharField(_("marital status"), max_length=15, choices=MARITAL_STATUS_CHOICES, blank=True, null=True)
    photo = models.ImageField(
        _("profile photo"),
        upload_to='staff_photos/', # Will be under MEDIA_ROOT/tenants/<schema>/staff_photos/
        null=True, blank=True
    )
    
    # --- Contact & Address ---
    phone_number_primary = models.CharField(_("primary phone"), max_length=30, blank=True)
    phone_number_alternate = models.CharField(_("alternate phone"), max_length=30, blank=True)
    address_line1 = models.CharField(_("address line 1"), max_length=255, blank=True)
    address_line2 = models.CharField(_("address line 2"), max_length=255, blank=True)
    city = models.CharField(_("city"), max_length=100, blank=True)
    state_province = models.CharField(_("state/province"), max_length=100, blank=True)
    postal_code = models.CharField(_("postal/zip code"), max_length=20, blank=True)
    country = models.CharField(_("country"), max_length=100, blank=True)

    # --- Other ---
    notes = models.TextField(_("internal HR notes"), blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _("employee HR profile")
        verbose_name_plural = _("employee HR profiles")
        ordering = ['user__last_name', 'user__first_name']
        
    def __str__(self):
        # Access the related user's name for a better string representation
        if hasattr(self, 'user'):
            return f"HR Profile for {self.user.get_full_name()}"
        return f"HR Profile (Unlinked)"

    def get_full_name(self):
        # Convenience method to proxy the user's name
        return self.user.get_full_name() if hasattr(self, 'user') else "N/A"



# # --- Employee Profile Model ---
# class EmployeeProfile(models.Model):
#     """
#     Stores HR-specific details for a StaffUser.
#     Linked one-to-one with the StaffUser model from the 'schools' app.
#     """
#     user = models.OneToOneField(
#         StaffUser, # Link to the StaffUser in apps.schools
#         on_delete=models.CASCADE, # If StaffUser is deleted, delete profile
#         primary_key=True, # Use StaffUser's PK as this model's PK
#         related_name='hr_profile' # Access via staff_user.hr_profile
#     )
#     # employee_id was moved to StaffUser model in the consolidated approach
#     # designation was moved to StaffUser model
#     # department was moved to StaffUser model
#     # date_hired (renamed to date_joined on StaffUser) was moved

#     # Additional HR-specific fields:
#     middle_name = models.CharField(_("middle name(s)"), max_length=100, blank=True)
#     gender = models.CharField(
#         _("gender"), max_length=15, choices=GENDER_CHOICES, blank=True, null=True
#     )
#     date_of_birth = models.DateField(_("date of birth"), null=True, blank=True)
#     marital_status = models.CharField(
#         _("marital status"), max_length=15, choices=MARITAL_STATUS_CHOICES, blank=True, null=True
#     )
#     # phone_number (primary) was moved to StaffUser
#     phone_number_alternate = models.CharField(
#         _("alternate phone"), max_length=30, blank=True,
#         help_text=_("Optional secondary contact number.")
#     )
#     address_line1 = models.CharField(_("address line 1"), max_length=255, blank=True)
#     address_line2 = models.CharField(_("address line 2"), max_length=255, blank=True)
#     city = models.CharField(_("city"), max_length=100, blank=True)
#     state_province = models.CharField(_("state/province"), max_length=100, blank=True)
#     postal_code = models.CharField(_("postal/zip code"), max_length=20, blank=True)
#     country = models.CharField(_("country"), max_length=100, blank=True) # Consider django-countries

#     employment_type = models.CharField(
#         _("employment type"), max_length=20, choices=EMPLOYMENT_TYPE_CHOICES, blank=True, null=True
#     )
#     date_left = models.DateField(_("date left employment"), null=True, blank=True)
#     photo = models.ImageField(
#         _("profile photo"),
#         upload_to='staff_photos/', # Will be under MEDIA_ROOT/tenants/<schema>/staff_photos/
#         null=True, blank=True
#     )
#     # Add other HR-specific fields: emergency contact, bank details (encrypt!), qualifications etc.

#     notes = models.TextField(_("internal notes"), blank=True)

#     created_at = models.DateTimeField(auto_now_add=True)
#     updated_at = models.DateTimeField(auto_now=True)

#     class Meta:
#         verbose_name = _("employee HR profile")
#         verbose_name_plural = _("employee HR profiles")
#         ordering = ['user__last_name', 'user__first_name']
        
#     def __str__(self):
#         if self.user:
#             # Call the method get_full_name()
#             user_display_name = self.user.get_full_name() 
#             # It's also good to provide a fallback if get_full_name() returns empty
#             # or if first_name/last_name are blank.
#             if not user_display_name: # If get_full_name() returned an empty string
#                 user_display_name = self.user.email # Fallback to email
#             return f"HR Profile for {user_display_name}"
#         return "HR Profile for Unknown User"



# D:\school_fees_saas_v2\apps\hr\models.py
from django.db import models
from django.utils.translation import gettext_lazy as _
from decimal import Decimal

class LeaveType(models.Model):
    """
    Defines different types of leave available in the school (e.g., Annual, Sick),
    including rules for how leave days are accrued over time.
    """
    # --- Existing Fields (They are good) ---
    name = models.CharField(
        max_length=100,
        unique=True, # Unique within the tenant
        help_text=_("Name of the leave type (e.g., Annual Leave, Sick Leave).")
    )
    description = models.TextField(blank=True, null=True)
    is_paid = models.BooleanField(default=True, help_text=_("Is this leave type paid?"))
    is_active = models.BooleanField(
        default=True, 
        help_text=_("Is this leave type available for new requests?")
    )
    
    # --- CONSOLIDATED & REFINED ACCRUAL FIELDS ---

    # We can rename max_annual_days for clarity if it's confusing, but it's fine.
    # Let's keep it but also add our new, more specific accrual fields.
    max_days_per_year_grant = models.DecimalField(
        _("Max Days Granted Annually"), 
        max_digits=5, decimal_places=2, null=True, blank=True,
        help_text=_("For non-accruing leave types, how many days are granted per year (e.g., 15 sick days).")
    )

    class AccrualFrequency(models.TextChoices):
        NONE = 'NONE', _('No Accrual (Manually Granted)')
        MONTHLY = 'MONTHLY', _('Monthly')
        ANNUALLY = 'ANNUALLY', _('Annually')

    accrual_frequency = models.CharField(
        _("Accrual Frequency"),
        max_length=20, choices=AccrualFrequency.choices, default=AccrualFrequency.NONE,
        help_text=_("Choose 'No Accrual' for leave that is granted manually or all at once per year.")
    )
    
    accrual_rate = models.DecimalField(
        _("Accrual Rate"),
        max_digits=5, decimal_places=2, default=Decimal('0.00'),
        help_text=_("If accruing, number of days added per period (e.g., 1.75 for monthly).")
    )
    
    max_accrual_balance = models.PositiveIntegerField(
        _("Maximum Balance"),
        null=True, blank=True,
        help_text=_("The maximum number of leave days a staff member can accumulate. Leave blank for no limit.")
    )
    
    prorate_accrual = models.BooleanField(
        _("Prorate for New Staff"),
        default=True,
        help_text=_("If accruing, new staff hired mid-period will receive a prorated amount for their first accrual.")
    )
    
    requires_approval = models.BooleanField(
        default=True, 
        help_text=_("Does this leave type require manager/admin approval?")
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['name']
        verbose_name = _("leave type")
        verbose_name_plural = _("leave types")
        permissions = [
            ("view_hr_module", "Can view the main HR module and navbar link"),
            ("approve_leave_requests", "Can approve or reject leave requests for other staff"),
        ]

    def __str__(self):
        return self.name



# D:\school_fees_saas_v2\apps\hr\models.py
from django.db import models
from django.db.models import Sum, Q
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from decimal import Decimal

# Import your other models (ensure paths are correct)
# from .models import EmployeeProfile, LeaveType, LeaveRequestStatusChoices

class LeaveBalance(models.Model):
    """
    Tracks the leave balance for an employee for a specific leave type and year.
    'days_taken' is calculated dynamically and not stored in the database.
    """
    employee = models.ForeignKey(
        'hr.EmployeeProfile',
        on_delete=models.CASCADE,
        related_name='leave_balances'
    )
    leave_type = models.ForeignKey(
        'hr.LeaveType',
        on_delete=models.CASCADE,
        related_name='leave_balances'
    )
    # Renamed for clarity and to fix the FieldError
    year = models.PositiveIntegerField(
        _("Balance Year"),
        default=timezone.now().year,
        help_text=_("The calendar year this balance applies to.")
    )
    # This is the "Entitled" column. It should be set by an admin or an automated process.
    days_accrued = models.DecimalField(
        _("Days Accrued/Entitled"),
        max_digits=5, decimal_places=1, default=Decimal('0.0')
    )
    
    # We REMOVE the 'days_taken' field from the database. It will be a calculated @property.

    # Optional field for tracking when accrual was last run
    last_accrual_date = models.DateField(null=True, blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _("Leave Balance")
        verbose_name_plural = _("Leave Balances")
        # Ensure only one balance record per employee, per leave_type, per year
        unique_together = ('employee', 'leave_type', 'year')
        ordering = ['employee__user__last_name', 'leave_type__name']

    def __str__(self):
        employee_name = "Unknown Employee"
        if self.employee and hasattr(self.employee, 'user') and self.employee.user:
            employee_name = self.employee.user.get_full_name()
        
        return f"{employee_name} - {self.leave_type.name} ({self.year})"

    @property
    def days_taken(self):
        """
        Calculates the total number of days taken.
        This is now robust and always returns a Decimal.
        """
        from .models import LeaveRequest, LeaveRequestStatusChoices # Local import to avoid circular dependency issues

        # The Coalesce function ensures that if the Sum is None (no records), it defaults to Decimal('0.0')
        total_taken = self.employee.leave_requests.filter(
            leave_type=self.leave_type,
            status=LeaveRequestStatusChoices.APPROVED,
            start_date__year=self.year 
        ).aggregate(
            total=Coalesce(Sum('duration'), Decimal('0.0'), output_field=models.DecimalField())
        )['total']
        
        return total_taken

    @property
    def days_available(self):
        """
        Calculates the remaining available leave days.
        This is now safe because days_accrued and days_taken are guaranteed to be Decimals.
        """
        try:
            # Ensure days_accrued is treated as a Decimal
            accrued = self.days_accrued or Decimal('0.0')
            return accrued - self.days_taken
        except (TypeError, ValueError):
            # Fallback in case of unexpected data types
            return Decimal('0.0')
    


class LeaveBalanceLog(models.Model):
    """
    An audit trail for every change to a LeaveBalance.
    """
    class Action(models.TextChoices):
        ACCRUAL = 'ACCRUAL', _('Automatic Accrual')
        ADJUSTMENT = 'ADJUSTMENT', _('Manual Adjustment')
        APPLICATION = 'APPLICATION', _('Leave Application')
        RESET = 'RESET', _('Yearly Reset')
        INITIAL = 'INITIAL', _('Initial Balance')

    leave_balance = models.ForeignKey(LeaveBalance, on_delete=models.CASCADE, related_name='logs')
    action = models.CharField(_("Action"), max_length=20, choices=Action.choices)
    change_amount = models.DecimalField(
        _("Change Amount"),
        max_digits=5, decimal_places=2,
        help_text=_("The number of days added (positive) or removed (negative).")
    )
    notes = models.CharField(_("Notes"), max_length=255, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    created_by = models.ForeignKey(
        'schools.StaffUser', 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True, # You had this
        related_name='leave_log_actions', # <<< CORRECTION: Add a related_name
        help_text=_("The admin who made a manual adjustment, or null for system actions.")
    )

    class Meta:
        ordering = ['-created_at']
        verbose_name = "Leave Balance Log" # <<< CORRECTION: Add verbose names
        verbose_name_plural = "Leave Balance Logs"

    def __str__(self):
        # This __str__ method is excellent.
        return f"{self.get_action_display()} of {self.change_amount} for {self.leave_balance}"


from django.db import models


class LeaveRequestStatusChoices(models.TextChoices):
    PENDING = 'PENDING', _('Pending')
    APPROVED = 'APPROVED', _('Approved')
    REJECTED = 'REJECTED', _('Rejected')
    CANCELLED_BY_STAFF = 'CANCELLED_BY_STAFF', _('Cancelled by Staff')
    # Add more as needed

LEAVE_REQUEST_STATUS_CHOICES = LeaveRequestStatusChoices.choices



# In D:\school_fees_saas_v2\apps\hr\models.py

# D:\school_fees_saas_v2\apps\hr\models.py

from django.db import models
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from decimal import Decimal
from datetime import timedelta


class LeaveRequest(models.Model):
    """
    Represents a leave request submitted by an employee.
    """
    employee = models.ForeignKey(
        'hr.EmployeeProfile', # Use 'app_label.ModelName' for robustness
        on_delete=models.CASCADE,
        related_name='leave_requests',
        verbose_name=_("Employee")
    )
    leave_type = models.ForeignKey(
        'hr.LeaveType',
        on_delete=models.PROTECT,
        related_name='leave_requests',
        verbose_name=_("Leave Type")
    )
    start_date = models.DateField(_("Start Date"))
    end_date = models.DateField(_("End Date"))
    half_day_start = models.BooleanField(_("Request Half-day on Start Date"), default=False)
    half_day_end = models.BooleanField(_("Request Half-day on End Date"), default=False)
    reason = models.TextField(_("Reason for Leave"))
    attachment = models.FileField(
        _("Attachment"),
        upload_to='leave_attachments/%Y/%m/',
        blank=True,
        null=True,
        help_text=_("Optional: Attach any supporting document.")
    )
    status = models.CharField(
        _("Status"),
        max_length=20,
        choices=LeaveRequestStatusChoices.choices,
        default=LeaveRequestStatusChoices.PENDING
    )
    status_reason = models.TextField(_("Admin/Manager Notes"), blank=True, null=True)
    status_changed_at = models.DateTimeField(_("Actioned On"), null=True, blank=True)
    approved_by = models.ForeignKey(
        'hr.EmployeeProfile', 
        on_delete=models.SET_NULL,
        null=True, blank=True,
        related_name='approved_leave_requests',
        verbose_name=_("Actioned By")
    )
    
    # This is the single, authoritative field for the leave duration.
    duration = models.DecimalField(
        _("Calculated Duration (Days)"),
        max_digits=5, decimal_places=1, default=Decimal('0.0'),
        help_text=_("The effective number of leave days, calculated by the system."),
        editable=False # This field should not be directly edited.
    )

    created_at = models.DateTimeField(_("Submitted On"), auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']
        verbose_name = _("Leave Request")
        verbose_name_plural = _("Leave Requests")

    def __str__(self):
        employee_name = "N/A"
        if self.employee and hasattr(self.employee, 'user') and self.employee.user:
            employee_name = self.employee.user.get_full_name()
        return f"Leave for {employee_name} ({self.leave_type.name})"

    def _calculate_effective_duration(self, exclude_weekends=True, holidays=None):
        """
        Internal method to calculate the effective number of leave days.
        """
        if not self.start_date or not self.end_date or self.end_date < self.start_date:
            return Decimal('0.0')

        holiday_set = set(holidays or [])
        day_count = Decimal('0.0')
        current_date = self.start_date
        is_single_day_request = (self.start_date == self.end_date)

        while current_date <= self.end_date:
            is_working_day = not (exclude_weekends and current_date.weekday() >= 5) and (current_date not in holiday_set)
            
            if is_working_day:
                day_value = Decimal('1.0')
                if is_single_day_request and (self.half_day_start or self.half_day_end):
                    day_value = Decimal('0.5')
                elif not is_single_day_request:
                    if current_date == self.start_date and self.half_day_start:
                        day_value -= Decimal('0.5')
                    if current_date == self.end_date and self.half_day_end:
                        day_value -= Decimal('0.5')
                day_count += max(Decimal('0.0'), day_value)

            current_date += timedelta(days=1)
            
        return day_count

    def save(self, *args, **kwargs):
        """
        Overrides save to automatically update the calculated duration field.
        """
        # This calculation runs every time the model is saved.
        self.duration = self._calculate_effective_duration(holidays=None)
        super().save(*args, **kwargs)

    @property
    def date_range_display(self):
        """
        A property to create a user-friendly string for the date range.
        """
        if self.start_date and self.end_date:
            if self.start_date == self.end_date:
                return self.start_date.strftime('%d %b, %Y')
            return f"{self.start_date.strftime('%d %b, %Y')} - {self.end_date.strftime('%d %b, %Y')}"
        return "N/A"
    
    def get_status_badge_class(self):
        """
        Returns a Bootstrap background color class based on the status.
        """
        status_map = {
            LeaveRequestStatusChoices.PENDING: "warning text-dark",
            LeaveRequestStatusChoices.APPROVED: "success",
            LeaveRequestStatusChoices.REJECTED: "danger",
            LeaveRequestStatusChoices.CANCELLED_BY_STAFF: "secondary",
        }
        return status_map.get(self.status, "light text-dark")
    
    
    
class HRPermissions(models.Model):
    class Meta:
        managed = False  # No database table created
        default_permissions = ()
        permissions = [
            ('view_hr_module', _('Can view the main HR module and navbar link')),
            ('manage_staff_users', _('Can create, edit, and manage staff accounts')),
            ('approve_leave_requests', _('Can approve or reject leave requests')),
            ('manage_leave_types', _('Can configure leave types')),
        ]
        


# ==============================================================================
# 1. DEFINE BASE PAYROLL MODELS FIRST
# These models are referenced by other models below them.
# ==============================================================================
# D:\school_fees_saas_v2\apps\hr\models.py
from django.db import models
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from decimal import Decimal
from apps.schools.models import StaffUser # Ensure StaffUser is imported

# ==============================================================================
# PAYROLL MODELS - USING THE SALARY GRADE STRUCTURE
# ==============================================================================

class SalaryComponent(models.Model):
    """
    A template for a type of earning or deduction.
    e.g., Name: "Housing Allowance", Type: Earning, Is Percentage: False
    e.g., Name: "Pension (Employee)", Type: Deduction, Is Percentage: True
    """
    class ComponentType(models.TextChoices):
        EARNING = 'EARNING', _('Earning')
        DEDUCTION = 'DEDUCTION', _('Deduction')

    name = models.CharField(_("Component Name"), max_length=100, unique=True)
    type = models.CharField(_("Component Type"), max_length=10, choices=ComponentType.choices)
    is_percentage = models.BooleanField(
        default=False, 
        help_text=_("Is this component calculated as a percentage of the basic salary?")
    )
    description = models.TextField(blank=True, null=True)

    class Meta:
        ordering = ['type', 'name']
        verbose_name = _("Salary Component")
        verbose_name_plural = _("Salary Components")

    def __str__(self):
        return f"{self.name} ({self.get_type_display()})"

# # --- THIS IS THE ONLY SALARY STRUCTURE MODEL YOU NEED ---
# class StaffSalary(models.Model):
#     """
#     The main salary record for a staff member, defining their basic pay
#     and linking to all their recurring allowances and deductions.
#     """
#     staff_member = models.OneToOneField(
#         StaffUser, 
#         on_delete=models.CASCADE, 
#         related_name='salary_details' # Use a clear, unique related_name
#     )
#     basic_salary = models.DecimalField(
#         _("Basic Salary"), 
#         max_digits=10, decimal_places=2, default=Decimal('0.00'),
#         help_text=_("The gross basic salary per month.")
#     )
#     effective_from = models.DateField(default=timezone.now)
    
#     updated_at = models.DateTimeField(auto_now=True)
    
#     class Meta:
#         verbose_name = _("Staff Salary")
#         verbose_name_plural = _("Staff Salaries")

#     def __str__(self):
#         return f"Salary for {self.staff_member.get_full_name()}"
    

# --- NEW MODEL: SalaryGrade ---
# SalaryGrade model is defined below at line 963


# --- NEW MODEL: GradeRule ---
class GradeRule(models.Model):
    """
    Links a SalaryComponent to a SalaryGrade with a specific value.
    This defines the standard structure for a grade.
    e.g., For "Senior" grade, the "Housing Allowance" component is a fixed amount of 1000.
    """
    grade = models.ForeignKey('SalaryGrade', on_delete=models.CASCADE, related_name='rules')
    component = models.ForeignKey(SalaryComponent, on_delete=models.PROTECT)
    value = models.DecimalField(
        _("Value/Percentage"),
        max_digits=10, decimal_places=2,
        help_text=_("Enter a fixed amount or a percentage (e.g., 7.5 for 7.5%).")
    )
    
    class Meta:
        unique_together = ('grade', 'component')
        verbose_name = _("Grade Salary Rule")
        verbose_name_plural = _("Grade Salary Rules")

    def __str__(self):
        if self.component.is_percentage:
            return f"{self.grade.name}: {self.component.name} ({self.value}%)"
        return f"{self.grade.name}: {self.component.name} ({self.value})"


# --- MODIFIED StaffSalary MODEL ---
class StaffSalary(models.Model):
    """
    The main salary record for a staff member, defining their basic pay
    AND linking them to a salary grade.
    """
    staff_member = models.OneToOneField(
        'schools.StaffUser', 
        on_delete=models.CASCADE, 
        related_name='salary_details'
    )
    # The grade determines the allowances and deductions
    grade = models.ForeignKey(
        'SalaryGrade',
        on_delete=models.PROTECT,
        null=True, # Allow null in case some staff don't have a grade
        blank=True,
        related_name='staff_salaries'
    )
    basic_salary = models.DecimalField(
        _("Basic Salary"), 
        max_digits=10, decimal_places=2, default=Decimal('0.00'),
        help_text=_("The gross basic salary per month. Overrides grade's basic if set.")
    )
    effective_from = models.DateField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = _("Staff Salary")
        verbose_name_plural = _("Staff Salaries")

    def __str__(self):
        return f"Salary for {self.staff_member.get_full_name()}"

# --- REMOVE StaffSalaryComponent MODEL ---
# We no longer assign components directly to staff. They are assigned to grades.
# So, you can now DELETE the entire 'StaffSalaryComponent' model class.

# class StaffSalaryComponent(models.Model):
#     """
#     Links a recurring SalaryComponent (like an allowance or deduction)
#     to a staff member's salary record.
#     """
#     staff_salary = models.ForeignKey(
#         StaffSalary,
#         on_delete=models.CASCADE,
#         related_name='components'
#     )
#     component = models.ForeignKey(SalaryComponent, on_delete=models.PROTECT)
    
#     # Store either a fixed amount or a percentage, based on the component's `is_percentage` flag
#     value = models.DecimalField(
#         _("Value/Percentage"),
#         max_digits=10, decimal_places=2,
#         help_text=_("Enter a fixed amount, or a percentage (e.g., 7.5 for 7.5%).")
#     )

#     class Meta:
#         unique_together = ('staff_salary', 'component')
#         verbose_name = _("Staff Salary Component")
#         verbose_name_plural = _("Staff Salary Components")

#     def __str__(self):
#         if self.component.is_percentage:
#             return f"{self.component.name} ({self.value}%)"
#         return f"{self.component.name} ({self.value})"

#     def calculate_amount(self, base_salary):
#         """Calculates the actual monetary value of this component."""
#         if self.component.is_percentage:
#             return (base_salary * self.value) / Decimal(100)
#         return self.value
    
    



# apps/hr/models.py
from django.db import models
from django.utils.translation import gettext_lazy as _
from decimal import Decimal
from apps.schools.models import StaffUser # Ensure this import is correct for your project
# from .payroll import PayrollRun # Ensure this import is correct

# ... any other existing models you have ...

# ==========================================================
# MODEL 1: YOUR TaxBracket MODEL (KEEP AS-IS)
# ==========================================================
class TaxBracket(models.Model):
    name = models.CharField(_("Bracket Name/Description"), max_length=100)
    from_amount = models.DecimalField(_("From Annual Amount"), max_digits=12, decimal_places=2)
    to_amount = models.DecimalField(_("To Annual Amount"), max_digits=12, decimal_places=2, null=True, blank=True, help_text="Leave blank for the highest bracket")
    rate_percent = models.DecimalField(_("Tax Rate (%)"), max_digits=5, decimal_places=2)
    deduction_amount = models.DecimalField(_("Flat Deduction Amount"), max_digits=12, decimal_places=2, default=Decimal('0.00'))
    is_active = models.BooleanField(default=True)
    
    class Meta:
        ordering = ['from_amount']
        verbose_name = _("Tax Bracket")
        verbose_name_plural = _("Tax Brackets")
    
    def __str__(self):
        return self.name

# ==========================================================
# MODEL 2: ADD THIS StatutoryDeduction MODEL
# ==========================================================
class StatutoryDeduction(models.Model):
    name = models.CharField(_("Deduction Name"), max_length=100, unique=True, help_text=_("e.g., Pension Fund, National Housing Fund"))
    
    payslip_label = models.CharField(
        _("Payslip Label"), 
        max_length=20, 
        blank=True,
        help_text=_("Optional: A shorter name for display on the payslip (e.g., 'Pension'). If blank, the full name is used.")
    )
    
    employee_contribution_rate = models.DecimalField(
        _("Employee Contribution (%)"), 
        max_digits=5, 
        decimal_places=2,
        help_text=_("Percentage of a relevant salary component (e.g., basic) deducted from the employee's pay.")
    )
    employer_contribution_rate = models.DecimalField(
        _("Employer Contribution (%)"), 
        max_digits=5, 
        decimal_places=2,
        default=0.00,
        help_text=_("Percentage contributed by the employer (a company cost).")
    )
    is_active = models.BooleanField(default=True)

    class Meta:
        verbose_name = _("Statutory Deduction")
        verbose_name_plural = _("Statutory Deductions")

    def __str__(self):
        return f"{self.name} ({self.employee_contribution_rate}% Employee)"



# ==========================================================
# MODEL 3: YOUR PayrollRun MODEL (KEEP AS-IS)
# ==========================================================

class PayrollRun(models.Model):
    """
    Represents a specific payroll run for a given period, e.g., "June 2025 Payroll".
    """
    class Status(models.TextChoices):
        DRAFT = 'DRAFT', _('Draft')
        PROCESSED = 'PROCESSED', _('Processed')
        PAID = 'PAID', _('Paid')
        CANCELLED = 'CANCELLED', _('Cancelled')

    pay_period_start = models.DateField()
    pay_period_end = models.DateField()
    payment_date = models.DateField()
    status = models.CharField(max_length=20, choices=Status.choices, default=Status.DRAFT)
    notes = models.TextField(blank=True, null=True)

    processed_by = models.ForeignKey(
        StaffUser, 
        on_delete=models.SET_NULL, 
        null=True, blank=True,
        related_name='processed_payroll_runs'
    )
    processed_at = models.DateTimeField(null=True, blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-payment_date']
        verbose_name = _("Payroll Run")
        verbose_name_plural = _("Payroll Runs")
        permissions = [
            ("manage_payroll", "Can create and process payroll runs"),
        ]

    def __str__(self):
        return f"Payroll for {self.pay_period_start.strftime('%B %Y')}"

    def get_absolute_url(self):
        return reverse('hr:payroll_run_detail', kwargs={'pk': self.pk})


# ==========================================================
# MODEL 4: YOUR Payslip MODEL (KEEP AS-IS)
# ==========================================================

# D:\school_fees_saas_v2\apps\hr\models.py

from django.db import models
from django.utils import timezone
from django.urls import reverse
from django.utils.translation import gettext_lazy as _
from decimal import Decimal
from .models import PayrollRun # Assuming PayrollRun is in the same file
from apps.schools.models import StaffUser

class Payslip(models.Model):
    payroll_run = models.ForeignKey(PayrollRun, on_delete=models.CASCADE, related_name='payslips', null=True, blank=True)
    staff_member = models.ForeignKey(StaffUser, on_delete=models.PROTECT, related_name='payslips')
    
    # --- Earnings ---
    basic_salary = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
    allowances = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'), help_text=_("Sum of all allowances"))
    bonuses = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
    
    # --- Deductions ---
    tax_deductions = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'), verbose_name=_("Tax (e.g., PAYE)"))
    pension_deductions = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
    loan_repayments = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
    other_deductions = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
    
    # --- Adjustments ---
    adjustments = models.DecimalField(
        _("Adjustments (Bonus/Deduction)"), 
        max_digits=10, 
        decimal_places=2, 
        default=Decimal('0.00'), 
        help_text="Positive value for a bonus/earning, negative value for a deduction."
    )
    
    # --- Calculated Fields ---
    gross_earnings = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
    total_deductions = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
    net_pay = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
    
    notes = models.TextField(blank=True, null=True, help_text=_("Notes specific to this payslip"))
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-payroll_run__payment_date', 'staff_member__last_name']
        unique_together = ('payroll_run', 'staff_member')
        verbose_name = _("Payslip")
        verbose_name_plural = _("Payslips")

    def __str__(self):
        # Add a null check for payroll_run for safety
        period = self.payroll_run.pay_period_start.strftime('%B %Y') if self.payroll_run else "Unassigned"
        return f"Payslip for {self.staff_member.get_full_name()} - {period}"

    def calculate_totals(self):
        """
        Calculates gross, total deductions, and net pay, correctly
        incorporating the adjustments field.
        """
        # Start with base earnings
        self.gross_earnings = self.basic_salary + self.allowances + self.bonuses
        
        # Start with base deductions
        self.total_deductions = self.tax_deductions + self.pension_deductions + self.loan_repayments + self.other_deductions

        # --- THIS IS THE FIX ---
        # Apply the adjustment
        if self.adjustments > 0:
            # Positive adjustment is an additional earning
            self.gross_earnings += self.adjustments
        elif self.adjustments < 0:
            # Negative adjustment is an additional deduction
            self.total_deductions += abs(self.adjustments) # Add the absolute (positive) value
            
        # Final net pay calculation
        self.net_pay = self.gross_earnings - self.total_deductions
    
    def save(self, *args, **kwargs):
        """Overrides save to ensure totals are calculated before saving."""
        self.calculate_totals()
        super().save(*args, **kwargs)
        


# 2. Define the SalaryGrade model. It doesn't depend on other models in this file.
class SalaryGrade(models.Model):
    """
    A template for a salary structure (e.g., "Senior Teacher", "Admin Grade 1").
    A tenant defines their own salary grades.
    """
    name = models.CharField(_("Grade Name"), max_length=100)
    description = models.TextField(blank=True, default="")
    is_active = models.BooleanField(default=True)
    
    class Meta:
        unique_together = ('name',) # Name should be unique per tenant
        ordering = ['name']
        verbose_name = _("Salary Grade")
        verbose_name_plural = _("Salary Grades")
        
    def __str__(self):
        return self.name



# Imports already defined at the top of the file

class StaffSalaryStructure(models.Model):
    """
    The single, authoritative salary structure for an individual staff member.
    """
    staff_user = models.OneToOneField(
        'schools.StaffUser', 
        on_delete=models.CASCADE, 
        related_name='salary_structure' # This name is now unique.
    )
    effective_date = models.DateField(default=timezone.now, help_text="The date this salary structure is effective from.")

    def __str__(self):
        return f"Salary Structure for {self.staff_user.get_full_name()}"

    def get_total_earnings(self):
        total = self.components.filter(component__type=SalaryComponent.ComponentType.EARNING).aggregate(
            total=Sum('amount')
        )['total']
        return total or Decimal('0.00')

    def get_total_deductions(self):
        total = self.components.filter(component__type=SalaryComponent.ComponentType.DEDUCTION).aggregate(
            total=Sum('amount')
        )['total']
        return total or Decimal('0.00')

    def get_net_salary(self):
        return self.get_total_earnings() - self.get_total_deductions()



class SalaryStructureComponent(models.Model):
    """
    Links a SalaryComponent to a StaffSalaryStructure with a specific amount.
    """
    salary_structure = models.ForeignKey(
        StaffSalaryStructure, 
        on_delete=models.CASCADE, 
        related_name='components'
    )
    # This ForeignKey to SalaryComponent will now work because the class is defined above.
    component = models.ForeignKey(SalaryComponent, on_delete=models.PROTECT, related_name='structure_lines')
    amount = models.DecimalField(max_digits=10, decimal_places=2)

    def __str__(self):
        return f"{self.component.name}: {self.amount}"

    class Meta:
        unique_together = ('salary_structure', 'component')
        
        
        
# # Model for the Salary Grade itself
# class SalaryGrade(models.Model):
#     name = models.CharField(max_length=100, unique=True, help_text=_("e.g., Grade A, Level 3, etc."))
#     description = models.TextField(blank=True, null=True)
#     is_active = models.BooleanField(default=True, help_text=_("Only active grades can be assigned to staff."))

#     class Meta:
#         ordering = ['name']
#         verbose_name = _("Salary Grade")
#         verbose_name_plural = _("Salary Grades")

#     def __str__(self):
#         return self.name


# The "through" model for the formset, linking a Grade to its Components
class SalaryGradeComponent(models.Model):
    grade = models.ForeignKey('SalaryGrade', on_delete=models.CASCADE, related_name='components')
    component = models.ForeignKey(SalaryComponent, on_delete=models.PROTECT) # Don't delete a component if it's in use
    amount = models.DecimalField(max_digits=10, decimal_places=2)

    class Meta:
        unique_together = ('grade', 'component') # A grade can only have one of each component type
        ordering = ['component__name']

    def __str__(self):
        return f"{self.grade.name} - {self.component.name}: {self.amount}"



# # 5. Define Payslip models. These must come AFTER SalaryComponent.

# D:\school_fees_saas_v2\apps\hr\models.py

from django.db import models
from django.conf import settings
from django.utils import timezone
from django.urls import reverse
from django.utils.translation import gettext_lazy as _
from decimal import Decimal

# Assuming your StaffUser model is in apps.schools.models
from apps.schools.models import StaffUser 



class PayslipLineItem(models.Model):
    # ... (Your PayslipLineItem model code is correct and can stay here) ...
    class ComponentType(models.TextChoices):
        EARNING = 'EARNING', _('Earning')
        DEDUCTION = 'DEDUCTION', _('Deduction')
    payslip = models.ForeignKey(Payslip, on_delete=models.CASCADE, related_name='line_items')
    name = models.CharField(max_length=100)
    type = models.CharField(max_length=10, choices=ComponentType.choices)
    amount = models.DecimalField(max_digits=12, decimal_places=2)
    source_component = models.ForeignKey(SalaryComponent, on_delete=models.SET_NULL, null=True, blank=True)
    class Meta:
        ordering = ['type', 'name']
        
    