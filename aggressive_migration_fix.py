#!/usr/bin/env python
"""
Aggressive Migration Fix
Completely resolves the hr.0013 dependency issue

Usage: python aggressive_migration_fix.py
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from django.db import connection, transaction
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def show_current_state():
    """Show current migration state"""
    logger.info("=== CURRENT MIGRATION STATE ===")
    
    with connection.cursor() as cursor:
        cursor.execute("""
            SELECT name, applied FROM django_migrations 
            WHERE app = 'hr' 
            ORDER BY applied;
        """)
        migrations = cursor.fetchall()
        
        logger.info("HR migrations in database:")
        for name, applied in migrations:
            logger.info(f"  {name} - {applied}")

def fix_migration_state_aggressively():
    """Aggressively fix the migration state"""
    logger.info("=== AGGRESSIVE MIGRATION FIX ===")
    
    try:
        with transaction.atomic():
            with connection.cursor() as cursor:
                # Step 1: Remove hr.0013 migration record temporarily
                logger.info("Step 1: Removing hr.0013 migration record...")
                cursor.execute("""
                    DELETE FROM django_migrations 
                    WHERE app = 'hr' AND name = '0013_add_missing_employeeprofile_fields';
                """)
                
                # Step 2: Remove any old hr.0012 records
                logger.info("Step 2: Removing old hr.0012 records...")
                cursor.execute("""
                    DELETE FROM django_migrations 
                    WHERE app = 'hr' AND name = '0012_add_missing_leavetype_fields';
                """)
                
                # Step 3: Add hr.0012_production migration record
                logger.info("Step 3: Adding hr.0012_production migration record...")
                cursor.execute("""
                    INSERT INTO django_migrations (app, name, applied) 
                    VALUES ('hr', '0012_add_missing_leavetype_fields_production', NOW())
                    ON CONFLICT (app, name) DO NOTHING;
                """)
                
                # Step 4: Re-add hr.0013 migration record
                logger.info("Step 4: Re-adding hr.0013 migration record...")
                cursor.execute("""
                    INSERT INTO django_migrations (app, name, applied) 
                    VALUES ('hr', '0013_add_missing_employeeprofile_fields', NOW())
                    ON CONFLICT (app, name) DO NOTHING;
                """)
                
                logger.info("✅ Migration state fixed aggressively")
                
    except Exception as e:
        logger.error(f"Aggressive fix failed: {e}")
        raise

def ensure_hr_table_columns():
    """Ensure HR table has all required columns"""
    logger.info("=== ENSURING HR TABLE COLUMNS ===")
    
    try:
        with connection.cursor() as cursor:
            # Add columns if they don't exist
            columns_to_add = [
                ("max_days_per_year_grant", "DECIMAL(5,2) NULL"),
                ("accrual_frequency", "VARCHAR(20) DEFAULT 'NONE' NOT NULL"),
                ("accrual_rate", "DECIMAL(5,2) DEFAULT 0.00 NOT NULL"),
                ("max_accrual_balance", "INTEGER NULL"),
                ("prorate_accrual", "BOOLEAN DEFAULT TRUE NOT NULL"),
            ]
            
            for column_name, column_def in columns_to_add:
                try:
                    cursor.execute(f"""
                        ALTER TABLE hr_leavetype 
                        ADD COLUMN IF NOT EXISTS {column_name} {column_def}
                    """)
                    logger.info(f"✅ Ensured column exists: {column_name}")
                except Exception as e:
                    logger.warning(f"Column {column_name} issue: {e}")
                    
    except Exception as e:
        logger.error(f"Failed to ensure HR table columns: {e}")
        raise

def test_migration_consistency():
    """Test migration consistency"""
    logger.info("=== TESTING MIGRATION CONSISTENCY ===")
    
    try:
        from django.db.migrations.loader import MigrationLoader
        
        # Test migration loader
        loader = MigrationLoader(connection)
        loader.check_consistent_history(connection)
        
        logger.info("✅ Migration history is now consistent")
        return True
        
    except Exception as e:
        logger.error(f"Migration consistency test failed: {e}")
        return False

def create_test_schema():
    """Test creating a schema to verify the fix works"""
    logger.info("=== TESTING SCHEMA CREATION ===")
    
    test_schema = 'test_aggressive_fix'
    
    try:
        # Clean up any existing test schema
        with connection.cursor() as cursor:
            cursor.execute(f'DROP SCHEMA IF EXISTS "{test_schema}" CASCADE')
        
        # Test schema creation
        from django.core.management import call_command
        call_command('migrate_tenant_safe', test_schema, verbosity=1)
        
        # Verify schema was created
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.schemata 
                    WHERE schema_name = %s
                );
            """, [test_schema])
            
            if cursor.fetchone()[0]:
                logger.info("✅ Test schema created successfully")
                
                # Clean up
                cursor.execute(f'DROP SCHEMA "{test_schema}" CASCADE')
                return True
            else:
                logger.error("❌ Test schema creation failed")
                return False
                
    except Exception as e:
        logger.error(f"Schema creation test failed: {e}")
        return False

def main():
    """Run the aggressive migration fix"""
    logger.info("=== AGGRESSIVE MIGRATION FIX STARTING ===")
    
    try:
        # Show current state
        show_current_state()
        
        # Fix migration state
        fix_migration_state_aggressively()
        
        # Ensure table columns
        ensure_hr_table_columns()
        
        # Test consistency
        if not test_migration_consistency():
            logger.error("Migration consistency test failed after fix")
            return False
        
        # Test schema creation
        if not create_test_schema():
            logger.error("Schema creation test failed after fix")
            return False
        
        # Show final state
        show_current_state()
        
        logger.info("🎉 AGGRESSIVE MIGRATION FIX COMPLETED SUCCESSFULLY!")
        logger.info("Registration should now work completely.")
        return True
        
    except Exception as e:
        logger.error(f"Aggressive migration fix failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
