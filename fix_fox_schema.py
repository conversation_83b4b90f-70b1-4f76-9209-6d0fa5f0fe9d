#!/usr/bin/env python
"""
Fix Fox Schema
Fix the fox schema specifically to add missing columns and tables

Usage: python fix_fox_schema.py
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from django.db import connection
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def fix_fox_schema():
    """Fix fox schema specifically"""
    
    logger.info("=== FIXING FOX SCHEMA ===")
    
    try:
        with connection.cursor() as cursor:
            cursor.execute('SET search_path TO "fox"')
            
            # 1. Add missing columns to existing tables
            missing_columns = [
                {
                    'table': 'schools_academicyear',
                    'column': 'is_active',
                    'definition': 'BOOLEAN DEFAULT TRUE'
                },
                {
                    'table': 'schools_academicyear',
                    'column': 'is_current',
                    'definition': 'BOOLEAN DEFAULT FALSE'
                },
                {
                    'table': 'schools_staffuser',
                    'column': 'employee_id',
                    'definition': 'VARCHAR(50) DEFAULT \'\''
                },
                {
                    'table': 'schools_staffuser',
                    'column': 'designation',
                    'definition': 'VARCHAR(100) DEFAULT \'\''
                }
            ]
            
            for col_info in missing_columns:
                table = col_info['table']
                column = col_info['column']
                definition = col_info['definition']
                
                try:
                    # Check if table exists
                    cursor.execute(f"""
                        SELECT EXISTS (
                            SELECT FROM information_schema.tables 
                            WHERE table_schema = 'fox' 
                            AND table_name = '{table}'
                        )
                    """)
                    
                    if not cursor.fetchone()[0]:
                        logger.warning(f"  ⚠️  Table {table} does not exist in fox")
                        continue
                    
                    # Check if column exists
                    cursor.execute(f"""
                        SELECT EXISTS (
                            SELECT FROM information_schema.columns 
                            WHERE table_schema = 'fox' 
                            AND table_name = '{table}'
                            AND column_name = '{column}'
                        )
                    """)
                    
                    if cursor.fetchone()[0]:
                        logger.info(f"  ✅ {table}.{column} already exists")
                        continue
                    
                    # Add missing column
                    alter_sql = f"ALTER TABLE {table} ADD COLUMN {column} {definition}"
                    cursor.execute(alter_sql)
                    
                    logger.info(f"  ✅ Added {table}.{column} to fox")
                    
                except Exception as e:
                    logger.error(f"  ❌ Failed to add {table}.{column} to fox: {e}")
            
            # 2. Create missing tables by copying from alpha
            missing_tables = [
                'schools_term', 'schools_schoolclass', 'schools_section',
                'fees_feehead', 'fees_feestructure', 'fees_feestructure_applicable_classes',
                'fees_invoice', 'fees_invoicedetail', 'payments_payment', 'payments_paymentallocation',
                'hr_leavebalance', 'hr_leaverequest', 'hr_leavetype',
                'payments_paymentmethod', 'fees_concessiontype', 'school_calendar_eventcategory'
            ]
            
            # Get table structures from alpha
            cursor.execute('SET search_path TO "alpha"')
            
            table_structures = {}
            for table in missing_tables:
                try:
                    cursor.execute(f"""
                        SELECT column_name, data_type, character_maximum_length, 
                               numeric_precision, numeric_scale, is_nullable, column_default
                        FROM information_schema.columns 
                        WHERE table_schema = 'alpha' 
                        AND table_name = '{table}'
                        ORDER BY ordinal_position
                    """)
                    
                    columns = cursor.fetchall()
                    if columns:
                        table_structures[table] = columns
                        logger.info(f"  📋 Got structure for {table} from alpha")
                    
                except Exception as e:
                    logger.warning(f"  ⚠️  Could not get structure for {table}: {e}")
            
            # Create tables in fox
            cursor.execute('SET search_path TO "fox"')
            
            for table, columns in table_structures.items():
                try:
                    # Check if table already exists
                    cursor.execute(f"""
                        SELECT EXISTS (
                            SELECT FROM information_schema.tables 
                            WHERE table_schema = 'fox' 
                            AND table_name = '{table}'
                        )
                    """)
                    
                    if cursor.fetchone()[0]:
                        logger.info(f"  ✅ Table {table} already exists in fox")
                        continue
                    
                    # Build CREATE TABLE statement
                    columns_def = []
                    
                    for col_name, data_type, max_len, num_prec, num_scale, nullable, default in columns:
                        col_def = f'"{col_name}" '
                        
                        # Handle data types
                        if data_type == 'character varying':
                            if max_len:
                                col_def += f'VARCHAR({max_len})'
                            else:
                                col_def += 'VARCHAR(255)'
                        elif data_type == 'numeric' and num_prec and num_scale:
                            col_def += f'NUMERIC({num_prec},{num_scale})'
                        elif data_type == 'timestamp with time zone':
                            col_def += 'TIMESTAMP WITH TIME ZONE'
                        elif data_type == 'timestamp without time zone':
                            col_def += 'TIMESTAMP'
                        else:
                            col_def += data_type.upper()
                        
                        # Handle nullable
                        if nullable == 'NO':
                            col_def += ' NOT NULL'
                        
                        # Handle defaults (skip sequences for now)
                        if default and not default.startswith('nextval'):
                            col_def += f' DEFAULT {default}'
                        
                        columns_def.append(col_def)
                    
                    create_sql = f'CREATE TABLE "{table}" ({", ".join(columns_def)})'
                    
                    cursor.execute(create_sql)
                    
                    # Create sequence if needed
                    if any('id' == col[0] for col in columns):
                        try:
                            cursor.execute(f'CREATE SEQUENCE IF NOT EXISTS {table}_id_seq')
                            cursor.execute(f'ALTER TABLE {table} ALTER COLUMN id SET DEFAULT nextval(\'{table}_id_seq\')')
                        except:
                            pass
                    
                    logger.info(f"  ✅ Created table {table} in fox")
                    
                except Exception as e:
                    logger.error(f"  ❌ Failed to create {table} in fox: {e}")
            
            # 3. Create essential data
            logger.info("Creating essential data in fox...")
            
            # Update academic year to have proper values
            try:
                cursor.execute("UPDATE schools_academicyear SET is_active = TRUE, is_current = TRUE WHERE id = 1")
                logger.info("  ✅ Updated academic year in fox")
            except Exception as e:
                logger.warning(f"  ⚠️  Could not update academic year: {e}")
            
            # Create essential reference data
            essential_data = [
                ("hr_leavetype", """
                    INSERT INTO hr_leavetype (name, description, is_paid, requires_approval, is_active, created_at, updated_at) VALUES
                    ('Annual Leave', 'Annual vacation leave', TRUE, TRUE, TRUE, NOW(), NOW()),
                    ('Sick Leave', 'Medical leave', TRUE, FALSE, TRUE, NOW(), NOW()),
                    ('Maternity Leave', 'Maternity leave', TRUE, TRUE, TRUE, NOW(), NOW()),
                    ('Emergency Leave', 'Emergency leave', FALSE, TRUE, TRUE, NOW(), NOW())
                    ON CONFLICT DO NOTHING
                """),
                ("payments_paymentmethod", """
                    INSERT INTO payments_paymentmethod (name, description, is_active, created_at, updated_at) VALUES
                    ('Cash', 'Cash payment', TRUE, NOW(), NOW()),
                    ('Bank Transfer', 'Bank transfer payment', TRUE, NOW(), NOW()),
                    ('Mobile Money', 'Mobile money payment', TRUE, NOW(), NOW()),
                    ('Cheque', 'Cheque payment', TRUE, NOW(), NOW())
                    ON CONFLICT DO NOTHING
                """),
                ("fees_concessiontype", """
                    INSERT INTO fees_concessiontype (name, description, is_active, created_at, updated_at) VALUES
                    ('Sibling Discount', 'Discount for siblings', TRUE, NOW(), NOW()),
                    ('Staff Child Discount', 'Discount for staff children', TRUE, NOW(), NOW()),
                    ('Merit Scholarship', 'Merit-based scholarship', TRUE, NOW(), NOW())
                    ON CONFLICT DO NOTHING
                """),
                ("school_calendar_eventcategory", """
                    INSERT INTO school_calendar_eventcategory (name, color, icon, description, is_active, created_at, updated_at) VALUES
                    ('Academic', '#007bff', 'bi-book', 'Academic events', TRUE, NOW(), NOW()),
                    ('Sports', '#28a745', 'bi-trophy', 'Sports events', TRUE, NOW(), NOW()),
                    ('Cultural', '#ffc107', 'bi-music-note', 'Cultural events', TRUE, NOW(), NOW()),
                    ('Meeting', '#6c757d', 'bi-people', 'Meetings', TRUE, NOW(), NOW()),
                    ('Holiday', '#dc3545', 'bi-calendar-x', 'Holidays', TRUE, NOW(), NOW())
                    ON CONFLICT DO NOTHING
                """)
            ]
            
            for table_name, sql in essential_data:
                try:
                    # Check if table exists
                    cursor.execute(f"""
                        SELECT EXISTS (
                            SELECT FROM information_schema.tables 
                            WHERE table_schema = 'fox' 
                            AND table_name = '{table_name}'
                        )
                    """)
                    
                    if cursor.fetchone()[0]:
                        cursor.execute(sql)
                        logger.info(f"  ✅ Created {table_name} data in fox")
                    else:
                        logger.warning(f"  ⚠️  Table {table_name} does not exist in fox")
                        
                except Exception as e:
                    logger.warning(f"  ⚠️  Failed to create {table_name} data in fox: {e}")
            
            logger.info("✅ Fox schema fix completed!")
            
    except Exception as e:
        logger.error(f"❌ Failed to fix fox schema: {e}")

def main():
    """Main function"""
    logger.info("=== FOX SCHEMA COMPREHENSIVE FIX ===")
    
    try:
        fix_fox_schema()
        
        logger.info("\n🎉 FOX SCHEMA FIX COMPLETE!")
        logger.info("Fox schema should now have:")
        logger.info("- All required columns")
        logger.info("- All essential tables")
        logger.info("- Essential reference data")
        logger.info("- Working dashboard and forms")
        
        return True
        
    except Exception as e:
        logger.error(f"Fox schema fix failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
