# Generated by Django 5.1.9 on 2025-07-08 20:56

from decimal import Decimal
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('hr', '0014_alter_leaverequest_options_and_more'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='leavebalance',
            options={'ordering': ['employee__user__last_name', 'leave_type__name'], 'verbose_name': 'Leave Balance', 'verbose_name_plural': 'Leave Balances'},
        ),
        migrations.AddField(
            model_name='leavebalance',
            name='year',
            field=models.PositiveIntegerField(default=2025, help_text='The calendar year this balance applies to.', verbose_name='Balance Year'),
        ),
        migrations.AlterField(
            model_name='leavebalance',
            name='days_accrued',
            field=models.DecimalField(decimal_places=1, default=Decimal('0.0'), max_digits=5, verbose_name='Days Accrued/Entitled'),
        ),
        migrations.RemoveField(
            model_name='leavebalance',
            name='days_taken',
        ),
        migrations.RemoveField(
            model_name='leavebalance',
            name='year_or_period_info',
        ),
    ]
