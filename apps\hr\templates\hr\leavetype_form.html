{% extends "tenant_base.html" %}
{% load static %}

{% block title %}
    {% if object %}Edit Leave Type{% else %}Add Leave Type{% endif %}
{% endblock %}

{% block extra_css %}
<style>
    .premium-form-container {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        min-height: 100vh;
        padding: 2rem 0;
    }

    .premium-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
        border-radius: 2rem;
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .premium-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 12px 40px rgba(31, 38, 135, 0.5);
    }

    .premium-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem;
        text-align: center;
        position: relative;
        overflow: hidden;
    }

    .premium-header::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
        transform: rotate(45deg);
        animation: shimmer 3s infinite;
    }

    @keyframes shimmer {
        0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
        100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
    }

    .premium-header h3 {
        position: relative;
        z-index: 2;
        margin: 0;
        font-weight: 600;
        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    }

    .premium-body {
        padding: 3rem;
        background: white;
    }

    .form-floating {
        position: relative;
        margin-bottom: 2rem;
    }

    .form-floating > .form-control,
    .form-floating > .form-select,
    .form-floating > textarea {
        height: calc(3.5rem + 2px);
        line-height: 1.25;
        border: 2px solid #e9ecef;
        border-radius: 1rem;
        padding: 1rem 0.75rem;
        background-color: #fff;
        transition: all 0.3s ease;
        font-size: 1rem;
    }

    .form-floating > textarea {
        height: auto;
        min-height: calc(3.5rem + 2px);
    }

    .form-floating > .form-control:focus,
    .form-floating > .form-select:focus,
    .form-floating > textarea:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.25rem rgba(102, 126, 234, 0.25);
        transform: translateY(-2px);
    }

    .form-floating > label {
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
        padding: 1rem 0.75rem;
        pointer-events: none;
        border: 2px solid transparent;
        transform-origin: 0 0;
        transition: all 0.3s ease;
        color: #6c757d;
        font-weight: 500;
    }

    .form-floating > .form-control:focus ~ label,
    .form-floating > .form-control:not(:placeholder-shown) ~ label,
    .form-floating > .form-select ~ label,
    .form-floating > textarea:focus ~ label,
    .form-floating > textarea:not(:placeholder-shown) ~ label {
        opacity: 0.65;
        transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
        color: #667eea;
    }

    .form-check {
        padding: 1.5rem;
        background: linear-gradient(135deg, #f8f9ff 0%, #e3f2fd 100%);
        border-radius: 1rem;
        border: 2px solid #e9ecef;
        transition: all 0.3s ease;
        margin-bottom: 1rem;
    }

    .form-check:hover {
        border-color: #667eea;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
    }

    .form-check-input {
        width: 2rem;
        height: 1rem;
        margin-top: 0.25rem;
        margin-right: 1rem;
        border: 2px solid #667eea;
        border-radius: 2rem;
        transition: all 0.3s ease;
    }

    .form-check-input:checked {
        background-color: #667eea;
        border-color: #667eea;
        box-shadow: 0 0 0 0.25rem rgba(102, 126, 234, 0.25);
    }

    .form-check-label {
        font-weight: 500;
        color: #495057;
        cursor: pointer;
        transition: color 0.3s ease;
    }

    .form-check:hover .form-check-label {
        color: #667eea;
    }

    .btn-premium {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 1rem;
        padding: 1rem 2.5rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        position: relative;
        overflow: hidden;
        color: white;
        text-decoration: none;
        display: inline-block;
        margin-right: 1rem;
    }

    .btn-premium:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
        color: white;
        text-decoration: none;
    }

    .btn-premium::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: left 0.5s;
    }

    .btn-premium:hover::before {
        left: 100%;
    }

    .btn-secondary-premium {
        background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
        border: none;
        border-radius: 1rem;
        padding: 1rem 2.5rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(108, 117, 125, 0.4);
        color: white;
        text-decoration: none;
        display: inline-block;
    }

    .btn-secondary-premium:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(108, 117, 125, 0.6);
        color: white;
        text-decoration: none;
    }

    .form-text {
        color: #6c757d;
        font-size: 0.875rem;
        margin-top: 0.5rem;
        padding-left: 0.75rem;
    }

    .invalid-feedback {
        color: #dc3545;
        font-size: 0.875rem;
        margin-top: 0.5rem;
        padding-left: 0.75rem;
        font-weight: 500;
    }

    .icon-input {
        color: #667eea;
        margin-right: 0.5rem;
    }

    .fieldset-header {
        background: linear-gradient(135deg, #f8f9ff 0%, #e3f2fd 100%);
        border: 2px solid #e9ecef;
        border-radius: 1rem;
        padding: 1.5rem;
        margin-bottom: 2rem;
        text-align: center;
    }

    .fieldset-header h4 {
        color: #667eea;
        font-weight: 600;
        margin: 0;
    }

    .alert {
        border-radius: 1rem;
        border: none;
        padding: 1rem 1.5rem;
        margin-bottom: 2rem;
    }

    .alert-danger {
        background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
        color: #721c24;
    }
</style>
{% endblock %}


{% block tenant_specific_content %}
<div class="premium-form-container">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="premium-card">
                    <div class="premium-header">
                        <h3>
                            <i class="bi bi-calendar-plus-fill me-2"></i>
                            {{ view_title }}
                        </h3>
                    </div>
                    <div class="premium-body">
                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {{ form.non_field_errors|join:", " }}
                            </div>
                        {% endif %}

                        <form method="post" novalidate>
                            {% csrf_token %}

                            <!-- Basic Information Section -->
                            <div class="fieldset-header">
                                <h4><i class="bi bi-info-circle me-2"></i>Basic Information</h4>
                            </div>
                            <div class="mb-3">
                                <label for="{{ form.name.id_for_label }}" class="form-label required">{{ form.name.label }}</label>
                                {% render_field form.name class="form-control" %}
                                {% for error in form.name.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                            </div>
                            <div class="mb-3">
                                <label for="{{ form.description.id_for_label }}" class="form-label">{{ form.description.label }}</label>
                                {% render_field form.description class="form-control" %}
                            </div>

                            <!-- Granting Method Section -->
                            <div class="fieldset-header">
                                <h4><i class="bi bi-gift-fill me-2"></i>Granting Method</h4>
                                <p class="small text-muted mb-0">Choose one method: either a simple annual grant OR a periodic accrual.</p>
                            </div>
                            <div class="mb-3">
                                <label for="{{ form.max_days_per_year_grant.id_for_label }}" class="form-label">{{ form.max_days_per_year_grant.label }}</label>
                                {% render_field form.max_days_per_year_grant class="form-control" placeholder="e.g., 21" %}
                                <div class="form-text">For simple leave types granted all at once per year.</div>
                                {% for error in form.max_days_per_year_grant.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                            </div>

                            <h6 class="text-muted">OR Periodic Accrual</h6>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.accrual_frequency.id_for_label }}" class="form-label">{{ form.accrual_frequency.label }}</label>
                                    {% render_field form.accrual_frequency class="form-select" %}
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.accrual_rate.id_for_label }}" class="form-label">{{ form.accrual_rate.label }}</label>
                                    {% render_field form.accrual_rate class="form-control" placeholder="e.g., 1.75" %}
                                    <div class="form-text">Days added per period (e.g., per month).</div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="{{ form.max_accrual_balance.id_for_label }}" class="form-label">{{ form.max_accrual_balance.label }}</label>
                                {% render_field form.max_accrual_balance class="form-control" placeholder="e.g., 30" %}
                                <div class="form-text">Max number of days an employee can accumulate. Leave blank for no limit.</div>
                            </div>


                            <!-- Rules & Settings Section -->
                            <div class="fieldset-header">
                                <h4><i class="bi bi-toggles2 me-2"></i>Rules & Settings</h4>
                            </div>
                            <div class="row">
                                <div class="col-sm-6 col-md-3"><div class="form-check form-switch mb-3">{% render_field form.is_paid class="form-check-input" %}<label class="form-check-label" for="{{ form.is_paid.id_for_label }}">{{ form.is_paid.label }}</label></div></div>
                                <div class="col-sm-6 col-md-3"><div class="form-check form-switch mb-3">{% render_field form.requires_approval class="form-check-input" %}<label class="form-check-label" for="{{ form.requires_approval.id_for_label }}">{{ form.requires_approval.label }}</label></div></div>
                                <div class="col-sm-6 col-md-3"><div class="form-check form-switch mb-3">{% render_field form.prorate_accrual class="form-check-input" %}<label class="form-check-label" for="{{ form.prorate_accrual.id_for_label }}">{{ form.prorate_accrual.label }}</label></div></div>
                                <div class="col-sm-6 col-md-3"><div class="form-check form-switch mb-3">{% render_field form.is_active class="form-check-input" %}<label class="form-check-label" for="{{ form.is_active.id_for_label }}">{{ form.is_active.label }}</label></div></div>
                            </div>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <div class="form-check form-switch">
                                        {% render_field form.is_gender_specific class="form-check-input" %}
                                        <label class="form-check-label" for="{{ form.is_gender_specific.id_for_label }}">{{ form.is_gender_specific.label }}</label>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.applicable_gender.id_for_label }}" class="form-label">{{ form.applicable_gender.label }}</label>
                                    {% render_field form.applicable_gender class="form-select" %}
                                </div>
                            </div>


                            <!-- Action Buttons -->
                            <div class="text-center mt-4">
                                <button type="submit" class="btn-premium">
                                    <i class="bi bi-save-fill me-2"></i>
                                    {% if object %}Update Leave Type{% else %}Create Leave Type{% endif %}
                                </button>
                                <a href="{% url 'hr:leavetype_list' %}" class="btn-secondary-premium">
                                    <i class="bi bi-x-lg me-2"></i>Cancel
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock tenant_specific_content %}

{% comment %} {% block content %}
<div class="premium-form-container">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="premium-card">
                    <div class="premium-header">
                        <h3>
                            <i class="fas fa-calendar-alt me-3"></i>
                            {% if object %}Edit Leave Type{% else %}Add New Leave Type{% endif %}
                        </h3>
                    </div>
                    <div class="premium-body">
                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {{ form.non_field_errors|join:", " }}
                            </div>
                        {% endif %}

                        <form method="post" novalidate>
                            {% csrf_token %}

                            <!-- Basic Information Section -->
                            <div class="fieldset-header">
                                <h4><i class="fas fa-info-circle me-2"></i>Basic Information</h4>
                            </div>

                            <!-- Name Field -->
                            <div class="form-floating">
                                <input type="text"
                                    class="form-control{% if form.name.errors %} is-invalid{% endif %}"
                                    id="{{ form.name.id_for_label }}"
                                    name="{{ form.name.name }}"
                                    value="{{ form.name.value|default:'' }}"
                                    placeholder="Enter leave type name"
                                    {% if form.name.field.required %}required{% endif %}>
                                <label for="{{ form.name.id_for_label }}">
                                    <i class="fas fa-tag icon-input"></i>Leave Type Name
                                </label>
                                {% if form.name.help_text %}
                                    <div class="form-text">{{ form.name.help_text }}</div>
                                {% endif %}
                                {% if form.name.errors %}
                                    <div class="invalid-feedback">{{ form.name.errors.0 }}</div>
                                {% endif %}
                            </div>

                            <!-- Description Field -->
                            <div class="form-floating">
                                <textarea class="form-control{% if form.description.errors %} is-invalid{% endif %}"
                                        id="{{ form.description.id_for_label }}"
                                        name="{{ form.description.name }}"
                                        placeholder="Enter description"
                                        style="height: 120px;"
                                        {% if form.description.field.required %}required{% endif %}>{{ form.description.value|default:'' }}</textarea>
                                <label for="{{ form.description.id_for_label }}">
                                    <i class="fas fa-align-left icon-input"></i>Description
                                </label>
                                {% if form.description.help_text %}
                                    <div class="form-text">{{ form.description.help_text }}</div>
                                {% endif %}
                                {% if form.description.errors %}
                                    <div class="invalid-feedback">{{ form.description.errors.0 }}</div>
                                {% endif %}
                            </div>

                            <!-- Max Annual Days Field -->
                            <div class="form-floating">
                                <input type="number"
                                    class="form-control{% if form.max_annual_days.errors %} is-invalid{% endif %}"
                                    id="{{ form.max_annual_days.id_for_label }}"
                                    name="{{ form.max_annual_days.name }}"
                                    value="{{ form.max_annual_days.value|default:'' }}"
                                    placeholder="Enter maximum annual days"
                                    {% if form.max_annual_days.field.required %}required{% endif %}>
                                <label for="{{ form.max_annual_days.id_for_label }}">
                                    <i class="fas fa-calendar-day icon-input"></i>Maximum Annual Days
                                </label>
                                {% if form.max_annual_days.help_text %}
                                    <div class="form-text">{{ form.max_annual_days.help_text }}</div>
                                {% endif %}
                                {% if form.max_annual_days.errors %}
                                    <div class="invalid-feedback">{{ form.max_annual_days.errors.0 }}</div>
                                {% endif %}
                            </div>

                            <!-- Settings Section -->
                            <div class="fieldset-header">
                                <h4><i class="fas fa-cogs me-2"></i>Leave Type Settings</h4>
                            </div>

                            <!-- Checkboxes Row -->
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input"
                                            type="checkbox"
                                            id="{{ form.is_paid.id_for_label }}"
                                            name="{{ form.is_paid.name }}"
                                            {% if form.is_paid.value %}checked{% endif %}>
                                        <label class="form-check-label" for="{{ form.is_paid.id_for_label }}">
                                            <i class="fas fa-money-bill-wave text-success me-2"></i>Is Paid Leave
                                        </label>
                                        {% if form.is_paid.help_text %}
                                            <div class="form-text">{{ form.is_paid.help_text }}</div>
                                        {% endif %}
                                        {% if form.is_paid.errors %}
                                            <div class="invalid-feedback d-block">{{ form.is_paid.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input"
                                            type="checkbox"
                                            id="{{ form.requires_approval.id_for_label }}"
                                            name="{{ form.requires_approval.name }}"
                                            {% if form.requires_approval.value %}checked{% endif %}>
                                        <label class="form-check-label" for="{{ form.requires_approval.id_for_label }}">
                                            <i class="fas fa-check-circle text-warning me-2"></i>Requires Approval
                                        </label>
                                        {% if form.requires_approval.help_text %}
                                            <div class="form-text">{{ form.requires_approval.help_text }}</div>
                                        {% endif %}
                                        {% if form.requires_approval.errors %}
                                            <div class="invalid-feedback d-block">{{ form.requires_approval.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>

                            <!-- Action Buttons -->
                            <div class="text-center mt-4">
                                <button type="submit" class="btn-premium">
                                    <i class="fas fa-save me-2"></i>
                                    {% if object %}Update Leave Type{% else %}Create Leave Type{% endif %}
                                </button>
                                <a href="{% url 'hr:leavetype_list' %}" class="btn-secondary-premium">
                                    <i class="fas fa-times me-2"></i>Cancel
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} {% endcomment %}

{% block page_specific_js %}
{{ block.super }}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Bootstrap tooltips if any
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Add loading state to form submission
    const form = document.querySelector('form');
    const submitBtn = document.querySelector('.btn-premium[type="submit"]');

    if (form && submitBtn) {
        form.addEventListener('submit', function() {
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing...';
            submitBtn.disabled = true;
        });
    }

    // Enhanced form validation feedback
    const inputs = document.querySelectorAll('.form-control, .form-select');
    inputs.forEach(input => {
        input.addEventListener('blur', function() {
            if (this.checkValidity()) {
                this.classList.remove('is-invalid');
                this.classList.add('is-valid');
            }
        });

        input.addEventListener('input', function() {
            if (this.classList.contains('is-invalid')) {
                if (this.checkValidity()) {
                    this.classList.remove('is-invalid');
                    this.classList.add('is-valid');
                }
            }
        });
    });
});
</script>
{% endblock %}



