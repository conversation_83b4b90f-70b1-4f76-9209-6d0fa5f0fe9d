#!/usr/bin/env python
"""
Fix AcademicYear Table Columns
Adds missing is_active column to schools_academicyear table

Usage: python fix_academicyear_columns.py
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from django.db import connection
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def fix_academicyear_table(schema_name):
    """Fix the schools_academicyear table structure"""
    logger.info(f"=== FIXING ACADEMICYEAR TABLE IN SCHEMA: {schema_name} ===")
    
    try:
        with connection.cursor() as cursor:
            # Set search path to schema
            cursor.execute(f'SET search_path TO "{schema_name}"')
            
            # Check current table structure
            cursor.execute("""
                SELECT column_name, data_type, is_nullable, column_default
                FROM information_schema.columns 
                WHERE table_name = 'schools_academicyear'
                ORDER BY ordinal_position
            """)
            
            existing_columns = cursor.fetchall()
            logger.info(f"Current columns in schools_academicyear:")
            for col in existing_columns:
                logger.info(f"  {col[0]} ({col[1]}) - nullable: {col[2]}, default: {col[3]}")
            
            # Check if is_active column exists
            column_names = [col[0] for col in existing_columns]
            
            if 'is_active' not in column_names:
                logger.info("Adding missing is_active column...")
                cursor.execute("""
                    ALTER TABLE schools_academicyear 
                    ADD COLUMN is_active BOOLEAN NOT NULL DEFAULT FALSE
                """)
                logger.info("✅ Added is_active column")
            else:
                logger.info("✅ is_active column already exists")
            
            # Update the existing record to have is_active = True
            cursor.execute("""
                UPDATE schools_academicyear 
                SET is_active = TRUE 
                WHERE is_current = TRUE
            """)
            
            # If no record has is_current = TRUE, set the first one as active
            cursor.execute("SELECT COUNT(*) FROM schools_academicyear WHERE is_active = TRUE")
            active_count = cursor.fetchone()[0]
            
            if active_count == 0:
                cursor.execute("""
                    UPDATE schools_academicyear 
                    SET is_active = TRUE, is_current = TRUE 
                    WHERE id = (SELECT id FROM schools_academicyear ORDER BY id LIMIT 1)
                """)
                logger.info("✅ Set first academic year as active")
            
            # Verify the fix
            cursor.execute("SELECT id, name, is_active, is_current FROM schools_academicyear")
            records = cursor.fetchall()
            logger.info("Academic year records:")
            for record in records:
                logger.info(f"  ID: {record[0]}, Name: {record[1]}, Active: {record[2]}, Current: {record[3]}")
            
            logger.info(f"🎉 ACADEMICYEAR TABLE FIXED IN {schema_name}!")
            
    except Exception as e:
        logger.error(f"Failed to fix academicyear table in {schema_name}: {e}")
        raise

def test_academicyear_access(schema_name):
    """Test that we can query the academic year table properly"""
    logger.info("=== TESTING ACADEMICYEAR ACCESS ===")
    
    try:
        with connection.cursor() as cursor:
            cursor.execute(f'SET search_path TO "{schema_name}"')
            
            # Test the query that was failing
            cursor.execute("""
                SELECT id, name, start_date, end_date, is_active, is_current, created_at, updated_at 
                FROM schools_academicyear
            """)
            
            records = cursor.fetchall()
            logger.info(f"✅ Successfully queried schools_academicyear (found {len(records)} records)")
            
            for record in records:
                logger.info(f"  {record[1]} - Active: {record[4]}, Current: {record[5]}")
                
    except Exception as e:
        logger.error(f"Failed to test academicyear access: {e}")
        raise

def fix_all_tenant_schemas():
    """Fix academicyear table in all tenant schemas"""
    logger.info("=== FIXING ACADEMICYEAR IN ALL TENANT SCHEMAS ===")
    
    # Get all tenant schemas
    with connection.cursor() as cursor:
        cursor.execute("""
            SELECT schema_name FROM information_schema.schemata 
            WHERE schema_name NOT IN ('public', 'information_schema') 
            AND schema_name NOT LIKE 'pg_%'
            ORDER BY schema_name
        """)
        schemas = [row[0] for row in cursor.fetchall()]
    
    logger.info(f"Found tenant schemas: {schemas}")
    
    for schema in schemas:
        try:
            # Check if schools_academicyear table exists
            with connection.cursor() as cursor:
                cursor.execute(f'SET search_path TO "{schema}"')
                cursor.execute("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_name = 'schools_academicyear'
                    );
                """)
                
                if cursor.fetchone()[0]:
                    logger.info(f"Fixing {schema}...")
                    fix_academicyear_table(schema)
                    test_academicyear_access(schema)
                else:
                    logger.warning(f"schools_academicyear table not found in {schema}")
                    
        except Exception as e:
            logger.error(f"Failed to fix {schema}: {e}")

def main():
    """Main function"""
    logger.info("=== FIXING ACADEMICYEAR COLUMNS ===")
    
    try:
        fix_all_tenant_schemas()
        
        logger.info("\n🎉 ACADEMICYEAR TABLES FIXED!")
        logger.info("Try accessing the dashboard again:")
        logger.info("http://mandiva.myapp.test:8000/portal/dashboard/")
        
        return True
        
    except Exception as e:
        logger.error(f"Fix failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
