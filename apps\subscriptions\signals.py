# apps/subscriptions/signals.py

import logging
from django.db.models.signals import post_save
from django.dispatch import receiver
from django.utils import timezone
from datetime import timedelta

from apps.tenants.models import School
from apps.subscriptions.models import SubscriptionPlan, Subscription

logger = logging.getLogger(__name__)

# your_project/apps/tenants/signals.py

import logging
from django.db.models.signals import post_save
from django.dispatch import receiver
from django.utils import timezone
from datetime import timedelta

from .models import School
from apps.subscriptions.models import Subscription, SubscriptionPlan

logger = logging.getLogger(__name__)

@receiver(post_save, sender=School)
def create_trial_subscription_for_new_tenant(sender, instance, created, **kwargs):
    """
    Automatically create a trial subscription when a new tenant (School) is created.
    """
    if not created or instance.schema_name == 'public':
        return

    if hasattr(instance, 'subscription'):
        logger.info(f"Tenant {instance.name} already has a subscription, skipping trial creation.")
        return

    try:
        trial_plan = SubscriptionPlan.objects.get(name='Trial Plan')
        trial_end = timezone.now() + timedelta(days=trial_plan.trial_period_days)

        # --- THIS IS THE CORRECTED AND FINAL FIX ---
        # Using 'school=instance' to match the field name in your Subscription model.
        Subscription.objects.create(
            school=instance,
            plan=trial_plan,
            status=Subscription.Status.TRIALING,
            trial_start_date=timezone.now(),
            trial_end_date=trial_end,
            billing_cycle=Subscription.BillingCycle.MONTHLY,
            current_period_start=timezone.now(),
            current_period_end=trial_end,
            price_at_subscription=trial_plan.price_monthly,
        )
        
        logger.info(f"SIGNAL: Trial subscription creation triggered for tenant: {instance.name}")

    except SubscriptionPlan.DoesNotExist:
        logger.error(f"CRITICAL: 'Trial Plan' not found in SubscriptionPlan model. Cannot create subscription for tenant: {instance.name}.")
    except Exception as e:
        logger.error(
            f"SIGNAL FAILED: Could not create trial subscription for tenant {instance.name}. Error: {e}",
            exc_info=True
        )

# NOTE: The other two signal receivers ('log_subscription_changes' and 
# 'store_original_subscription_status') should be DELETED from this file.


# @receiver(post_save, sender=School)
# def create_trial_subscription_for_new_tenant(sender, instance, created, **kwargs):
#     """
#     Automatically create a trial subscription when a new tenant is created.
#     """
#     if not created:
#         return
    
#     # Skip if this is the public schema
#     if instance.schema_name == 'public':
#         return
    
#     # Check if subscription already exists
#     if hasattr(instance, 'subscription'):
#         logger.info(f"Tenant {instance.name} already has a subscription, skipping trial creation")
#         return
    
#     try:
#         # Get the trial plan
#         trial_plan = SubscriptionPlan.objects.get(name='Trial Plan')
        
#         # Create trial subscription
#         trial_end = timezone.now() + timedelta(days=trial_plan.trial_period_days)
#         subscription = Subscription.objects.create(
#             school=instance,
#             plan=trial_plan,
#             status=Subscription.Status.TRIALING,
#             trial_start_date=timezone.now(),
#             trial_end_date=trial_end,
#             billing_cycle=Subscription.BillingCycle.MONTHLY,
#             current_period_start=timezone.now(),
#             current_period_end=trial_end,
#             price_at_subscription=trial_plan.price_monthly,
#         )
        
#         logger.info(
#             f"Created trial subscription for new tenant: {instance.name} "
#             f"(Trial ends: {subscription.trial_end_date})"
#         )
        
#     except SubscriptionPlan.DoesNotExist:
#         logger.error(
#             f"Trial Plan not found when creating subscription for tenant: {instance.name}. "
#             f"Please ensure the trial plan exists."
#         )
#     except Exception as e:
#         logger.error(
#             f"Failed to create trial subscription for tenant {instance.name}: {str(e)}"
#         )


@receiver(post_save, sender=Subscription)
def log_subscription_changes(sender, instance, created, **kwargs):
    """
    Log subscription changes for monitoring and debugging.
    """
    if created:
        logger.info(
            f"New subscription created for school {instance.school.name}: "
            f"Plan={instance.plan.name}, Status={instance.status}, "
            f"Trial End={instance.trial_end_date}"
        )
    else:
        # Log status changes
        if hasattr(instance, '_original_status') and instance._original_status != instance.status:
            logger.info(
                f"Subscription status changed for tenant {instance.tenant.name}: "
                f"{instance._original_status} -> {instance.status}"
            )


# Store original status for comparison
@receiver(post_save, sender=Subscription)
def store_original_subscription_status(sender, instance, **kwargs):
    """Store the original status to detect changes."""
    if instance.pk:
        try:
            original = Subscription.objects.get(pk=instance.pk)
            instance._original_status = original.status
        except Subscription.DoesNotExist:
            instance._original_status = None
