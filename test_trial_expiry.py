#!/usr/bin/env python
"""
Test Trial Expiry Flow
Tests the trial expiry and upgrade flow

Usage: python test_trial_expiry.py
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from django.utils import timezone
from datetime import timedelta
from apps.subscriptions.models import Subscription
from apps.tenants.models import School
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_trial_expiry_flow():
    """Test the trial expiry flow"""
    logger.info("=== TESTING TRIAL EXPIRY FLOW ===")
    
    try:
        # Find the mandiva school
        school = School.objects.filter(schema_name='mandiva').first()
        if not school:
            logger.error("Mandiva school not found")
            return False
        
        logger.info(f"Found school: {school.name}")
        
        # Get the subscription
        subscription = Subscription.objects.filter(school=school).first()
        if not subscription:
            logger.error("No subscription found for mandiva")
            return False
        
        logger.info(f"Current subscription: {subscription.plan.name if subscription.plan else 'No Plan'}")
        logger.info(f"Status: {subscription.status}")
        logger.info(f"Trial end: {subscription.trial_end_date}")
        
        # Check current trial status
        now = timezone.now()
        if subscription.trial_end_date:
            time_remaining = subscription.trial_end_date - now
            days_remaining = time_remaining.days
            hours_remaining = time_remaining.seconds // 3600
            
            logger.info(f"Time remaining: {days_remaining} days, {hours_remaining} hours")
            
            if subscription.trial_end_date > now:
                logger.info("✅ Trial is still active")
                
                # Test different urgency levels
                if days_remaining <= 1:
                    urgency = 'critical'
                elif days_remaining <= 3:
                    urgency = 'warning'
                elif days_remaining <= 7:
                    urgency = 'caution'
                else:
                    urgency = 'normal'
                
                logger.info(f"Trial urgency level: {urgency}")
                
            else:
                logger.info("❌ Trial has expired")
                urgency = 'expired'
        
        # Test setting trial to expire soon (for testing purposes)
        logger.info("\n--- Testing Trial Expiry Scenarios ---")
        
        # Save original trial end date
        original_trial_end = subscription.trial_end_date
        
        # Test 1: Set trial to expire in 1 hour (critical)
        subscription.trial_end_date = now + timedelta(hours=1)
        subscription.save()
        logger.info("✅ Test 1: Set trial to expire in 1 hour (should show critical urgency)")
        
        # Test 2: Set trial to expire in 2 days (warning)
        subscription.trial_end_date = now + timedelta(days=2)
        subscription.save()
        logger.info("✅ Test 2: Set trial to expire in 2 days (should show warning urgency)")
        
        # Test 3: Set trial to expire in 5 days (caution)
        subscription.trial_end_date = now + timedelta(days=5)
        subscription.save()
        logger.info("✅ Test 3: Set trial to expire in 5 days (should show caution urgency)")
        
        # Test 4: Set trial to expired (expired)
        subscription.trial_end_date = now - timedelta(hours=1)
        subscription.save()
        logger.info("✅ Test 4: Set trial to expired (should redirect to upgrade)")
        
        # Restore original trial end date
        subscription.trial_end_date = original_trial_end
        subscription.save()
        logger.info("✅ Restored original trial end date")
        
        return True
        
    except Exception as e:
        logger.error(f"Trial expiry test failed: {e}")
        return False

def show_available_plans():
    """Show available upgrade plans"""
    logger.info("\n=== AVAILABLE UPGRADE PLANS ===")
    
    from apps.subscriptions.models import SubscriptionPlan
    
    plans = SubscriptionPlan.objects.filter(is_active=True, is_public=True).order_by('display_order')
    
    for plan in plans:
        logger.info(f"\n📋 {plan.name}")
        logger.info(f"   💰 Monthly: ${plan.price_monthly} | Annual: ${plan.price_annually}")
        logger.info(f"   👥 Students: {plan.max_students} | Staff: {plan.max_staff or 'Unlimited'}")
        logger.info(f"   🎯 Trial: {plan.trial_period_days} days")
        logger.info(f"   📝 {plan.description}")
        
        features = plan.features.all()
        if features:
            logger.info(f"   🔧 Features: {', '.join([f.name for f in features[:5]])}{'...' if len(features) > 5 else ''}")

def test_upgrade_urls():
    """Test that upgrade URLs are accessible"""
    logger.info("\n=== TESTING UPGRADE URLS ===")
    
    urls_to_test = [
        ('Plan Selection', '/subscriptions/select-plan/'),
        ('Pricing Page', '/subscriptions/pricing/'),
        ('Subscription Details', '/subscriptions/details/'),
    ]
    
    for name, url in urls_to_test:
        logger.info(f"✅ {name}: {url}")
    
    logger.info("\nThese URLs should be accessible when trial expires:")
    logger.info("- Users will be redirected to plan selection")
    logger.info("- Modal will appear for expired trials")
    logger.info("- Upgrade buttons will be prominent")

def main():
    """Main function"""
    logger.info("=== TESTING TRIAL EXPIRY AND UPGRADE FLOW ===")
    
    try:
        # Test trial expiry flow
        if not test_trial_expiry_flow():
            return False
        
        # Show available plans
        show_available_plans()
        
        # Test upgrade URLs
        test_upgrade_urls()
        
        logger.info("\n🎉 TRIAL EXPIRY FLOW TEST COMPLETE!")
        logger.info("\nWhat happens when trial expires:")
        logger.info("1. ⏰ Trial counter shows urgency levels (normal → caution → warning → critical)")
        logger.info("2. 🚨 Expired trials redirect to plan selection page")
        logger.info("3. 📱 Modal appears with upgrade options")
        logger.info("4. 🎯 Clear upgrade buttons with plan choices")
        logger.info("5. 📞 Support contact options available")
        logger.info("\nUpgrade Options Available:")
        logger.info("- Starter Plan: $29.99/month (100 students)")
        logger.info("- Professional Plan: $79.99/month (500 students)")
        logger.info("- Enterprise Plan: $149.99/month (2000 students)")
        logger.info("\nTest the flow at: http://mandiva.myapp.test:8000/portal/dashboard/")
        
        return True
        
    except Exception as e:
        logger.error(f"Test failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
