{# templates/public_site/testimonials_list.html #}
{% extends "public_base.html" %}
{% load static %}

{% block title %}{{ view_title }} - School Fees Management Platform{% endblock %}

{% block extra_public_css %}
<style>
    .testimonials-hero {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 4rem 0;
        text-align: center;
        position: relative;
        overflow: hidden;
    }

    .testimonials-hero::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        opacity: 0.3;
    }

    .testimonials-hero h1 {
        font-size: 3rem;
        font-weight: 700;
        margin-bottom: 1rem;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        position: relative;
        z-index: 1;
    }

    .testimonials-hero p {
        font-size: 1.2rem;
        opacity: 0.9;
        margin-bottom: 2rem;
        position: relative;
        z-index: 1;
        max-width: 600px;
        margin-left: auto;
        margin-right: auto;
    }

    .testimonials-container {
        padding: 4rem 0;
        background: #f8fafc;
    }

    .stats-section {
        background: white;
        border-radius: 20px;
        padding: 3rem;
        margin-bottom: 3rem;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        text-align: center;
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 2rem;
        margin-top: 2rem;
    }

    .stat-item h3 {
        font-size: 2.5rem;
        color: #667eea;
        margin-bottom: 0.5rem;
        font-weight: 700;
    }

    .stat-item p {
        color: #718096;
        margin: 0;
        font-weight: 500;
    }

    .rating-overview {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 2rem;
        margin-top: 2rem;
    }

    .overall-rating {
        text-align: center;
    }

    .overall-rating .rating-number {
        font-size: 3rem;
        font-weight: 700;
        color: #667eea;
        margin-bottom: 0.5rem;
    }

    .overall-rating .stars {
        font-size: 1.5rem;
        color: #fbbf24;
        margin-bottom: 0.5rem;
    }

    .rating-bars {
        flex: 1;
        max-width: 300px;
    }

    .rating-bar {
        display: flex;
        align-items: center;
        margin-bottom: 0.5rem;
    }

    .rating-bar .stars {
        width: 80px;
        color: #fbbf24;
        font-size: 0.9rem;
    }

    .rating-bar .bar {
        flex: 1;
        height: 8px;
        background: #e2e8f0;
        border-radius: 4px;
        margin: 0 1rem;
        overflow: hidden;
    }

    .rating-bar .fill {
        height: 100%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 4px;
        transition: width 0.3s ease;
    }

    .rating-bar .count {
        width: 30px;
        text-align: right;
        color: #718096;
        font-size: 0.9rem;
    }

    .filters-section {
        background: white;
        border-radius: 16px;
        padding: 2rem;
        margin-bottom: 3rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    }

    .filters-title {
        color: #2d3748;
        margin-bottom: 1.5rem;
        text-align: center;
    }

    .filters-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
    }

    .filter-group label {
        display: block;
        color: #4a5568;
        font-weight: 500;
        margin-bottom: 0.5rem;
    }

    .filter-group select {
        width: 100%;
        padding: 0.75rem;
        border: 2px solid #e2e8f0;
        border-radius: 8px;
        background: white;
        color: #2d3748;
        transition: all 0.3s ease;
    }

    .filter-group select:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }

    .featured-section {
        margin-bottom: 3rem;
    }

    .section-title {
        text-align: center;
        margin-bottom: 2rem;
    }

    .section-title h2 {
        color: #2d3748;
        font-size: 2rem;
        margin-bottom: 0.5rem;
    }

    .section-title p {
        color: #718096;
        margin: 0;
    }

    .featured-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
        gap: 2rem;
        margin-bottom: 3rem;
    }

    .featured-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 20px;
        padding: 2.5rem;
        position: relative;
        overflow: hidden;
        box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
    }

    .featured-card::before {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 100px;
        height: 100px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 50%;
        transform: translate(30px, -30px);
    }

    .testimonial-card {
        background: white;
        border-radius: 16px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        border: 1px solid #e2e8f0;
        transition: all 0.3s ease;
        position: relative;
    }

    .testimonial-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }

    .testimonial-header {
        display: flex;
        align-items: center;
        margin-bottom: 1.5rem;
    }

    .testimonial-avatar {
        width: 60px;
        height: 60px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: 600;
        font-size: 1.5rem;
        margin-right: 1rem;
    }

    .testimonial-info h4 {
        margin: 0 0 0.25rem 0;
        color: #2d3748;
        font-size: 1.2rem;
    }

    .testimonial-meta {
        color: #718096;
        font-size: 0.9rem;
        margin-bottom: 0.5rem;
    }

    .testimonial-badges {
        display: flex;
        gap: 0.5rem;
        flex-wrap: wrap;
    }

    .category-badge {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 500;
    }

    .featured-badge {
        background: linear-gradient(135deg, #f6ad55 0%, #ed8936 100%);
        color: white;
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 500;
    }

    .school-type-badge {
        background: #e2e8f0;
        color: #4a5568;
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 500;
    }

    .testimonial-content {
        margin: 1.5rem 0;
    }

    .testimonial-title {
        color: #2d3748;
        font-size: 1.3rem;
        font-weight: 600;
        margin-bottom: 1rem;
    }

    .testimonial-text {
        color: #4a5568;
        line-height: 1.7;
        font-size: 1rem;
        margin-bottom: 1.5rem;
    }

    .testimonial-ratings {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 1rem;
        margin-bottom: 1.5rem;
    }

    .rating-item {
        text-align: center;
        padding: 0.75rem;
        background: #f7fafc;
        border-radius: 8px;
    }

    .rating-item .label {
        font-size: 0.8rem;
        color: #718096;
        margin-bottom: 0.25rem;
    }

    .rating-item .stars {
        color: #fbbf24;
        font-size: 1rem;
    }

    .testimonial-footer {
        display: flex;
        justify-content: between;
        align-items: center;
        padding-top: 1rem;
        border-top: 1px solid #e2e8f0;
        font-size: 0.9rem;
        color: #718096;
    }

    .usage-info {
        display: flex;
        gap: 1rem;
        flex-wrap: wrap;
    }

    .btn-primary-gradient {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        color: white;
        padding: 0.875rem 2rem;
        border-radius: 10px;
        font-weight: 600;
        text-decoration: none;
        display: inline-block;
        transition: all 0.3s ease;
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        position: relative;
        z-index: 1;
    }

    .btn-primary-gradient:hover {
        background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        color: white;
        text-decoration: none;
    }

    .pagination-container {
        display: flex;
        justify-content: center;
        margin-top: 3rem;
    }

    .pagination {
        display: flex;
        gap: 0.5rem;
    }

    .pagination a,
    .pagination span {
        padding: 0.75rem 1rem;
        border-radius: 8px;
        text-decoration: none;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .pagination a {
        background: white;
        color: #667eea;
        border: 1px solid #e2e8f0;
    }

    .pagination a:hover {
        background: #667eea;
        color: white;
        transform: translateY(-1px);
    }

    .pagination .current {
        background: #667eea;
        color: white;
        border: 1px solid #667eea;
    }

    .cta-section {
        background: white;
        border-radius: 20px;
        padding: 3rem;
        text-align: center;
        margin-top: 3rem;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }

    .cta-section h3 {
        color: #2d3748;
        margin-bottom: 1rem;
    }

    .cta-section p {
        color: #718096;
        margin-bottom: 2rem;
    }

    @media (max-width: 768px) {
        .testimonials-hero h1 {
            font-size: 2.5rem;
        }

        .testimonials-hero {
            padding: 3rem 0;
        }

        .testimonials-container {
            padding: 3rem 0;
        }

        .stats-section {
            padding: 2rem;
        }

        .testimonial-card {
            padding: 1.5rem;
        }

        .featured-grid {
            grid-template-columns: 1fr;
        }

        .filters-grid {
            grid-template-columns: 1fr;
        }

        .rating-overview {
            flex-direction: column;
            gap: 1rem;
        }

        .testimonial-ratings {
            grid-template-columns: repeat(2, 1fr);
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="testimonials-hero">
    <div class="container">
        <h1>Customer Reviews</h1>
        <p>See what schools around the world are saying about our fee management platform</p>
        <a href="{% url 'public_site:testimonial_add' %}" class="btn-primary-gradient">
            <i class="bi bi-chat-quote me-2"></i>
            Share Your Experience
        </a>
    </div>
</section>

<!-- Testimonials Container -->
<section class="testimonials-container">
    <div class="container">
        <!-- Statistics Section -->
        <div class="stats-section">
            <h2>What Our Numbers Say</h2>
            <div class="stats-grid">
                <div class="stat-item">
                    <h3>{{ total_reviews }}</h3>
                    <p>Total Reviews</p>
                </div>
                <div class="stat-item">
                    <h3>{{ average_rating|floatformat:1 }}</h3>
                    <p>Average Rating</p>
                </div>
                <div class="stat-item">
                    <h3>{{ featured_testimonials|length }}</h3>
                    <p>Featured Stories</p>
                </div>
                <div class="stat-item">
                    <h3>95%</h3>
                    <p>Would Recommend</p>
                </div>
            </div>

            <div class="rating-overview">
                <div class="overall-rating">
                    <div class="rating-number">{{ average_rating|floatformat:1 }}</div>
                    <div class="stars">
                        {% for i in "12345"|make_list %}
                            {% if average_rating >= i|add:0 %}★{% else %}☆{% endif %}
                        {% endfor %}
                    </div>
                    <p>Based on {{ total_reviews }} reviews</p>
                </div>

                <div class="rating-bars">
                    {% for rating, count in rating_distribution.items %}
                    <div class="rating-bar">
                        <div class="stars">{{ rating }} ★</div>
                        <div class="bar">
                            <div class="fill" style="width: {% if total_reviews > 0 %}{{ count|mul:100|div:total_reviews }}{% else %}0{% endif %}%"></div>
                        </div>
                        <div class="count">{{ count }}</div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>

        <!-- Filters Section -->
        <div class="filters-section">
            <h3 class="filters-title">Filter Reviews</h3>
            <form method="get" class="filters-grid">
                <div class="filter-group">
                    <label for="category">Category</label>
                    <select name="category" id="category" onchange="this.form.submit()">
                        <option value="all" {% if current_filters.category == 'all' %}selected{% endif %}>All Categories</option>
                        {% for value, label in categories %}
                            <option value="{{ value }}" {% if current_filters.category == value %}selected{% endif %}>{{ label }}</option>
                        {% endfor %}
                    </select>
                </div>

                <div class="filter-group">
                    <label for="school_type">School Type</label>
                    <select name="school_type" id="school_type" onchange="this.form.submit()">
                        <option value="all" {% if current_filters.school_type == 'all' %}selected{% endif %}>All School Types</option>
                        {% for value, label in school_types %}
                            <option value="{{ value }}" {% if current_filters.school_type == value %}selected{% endif %}>{{ label }}</option>
                        {% endfor %}
                    </select>
                </div>

                <div class="filter-group">
                    <label for="rating">Minimum Rating</label>
                    <select name="rating" id="rating" onchange="this.form.submit()">
                        <option value="all" {% if current_filters.rating == 'all' %}selected{% endif %}>All Ratings</option>
                        <option value="5" {% if current_filters.rating == '5' %}selected{% endif %}>5 Stars</option>
                        <option value="4" {% if current_filters.rating == '4' %}selected{% endif %}>4+ Stars</option>
                        <option value="3" {% if current_filters.rating == '3' %}selected{% endif %}>3+ Stars</option>
                    </select>
                </div>
            </form>
        </div>

        <!-- Featured Reviews -->
        {% if featured_testimonials %}
        <div class="featured-section">
            <div class="section-title">
                <h2>Featured Reviews</h2>
                <p>Highlighted experiences from our community</p>
            </div>

            <div class="featured-grid">
                {% for testimonial in featured_testimonials %}
                <div class="featured-card">
                    <div class="testimonial-header">
                        <div class="testimonial-avatar">
                            {{ testimonial.author_name|first|upper }}
                        </div>
                        <div class="testimonial-info">
                            <h4>{{ testimonial.author_name }}</h4>
                            <div class="testimonial-meta">{{ testimonial.author_title }}, {{ testimonial.school_name }}</div>
                            <div class="testimonial-badges">
                                <span class="category-badge">{{ testimonial.get_review_category_display }}</span>
                                <span class="featured-badge">Featured</span>
                            </div>
                        </div>
                    </div>

                    <div class="testimonial-content">
                        <div class="testimonial-title">{{ testimonial.title }}</div>
                        <div class="testimonial-text">{{ testimonial.quote|truncatewords:40 }}</div>

                        <div class="testimonial-ratings">
                            <div class="rating-item">
                                <div class="label">Overall</div>
                                <div class="stars">
                                    {% for i in "12345"|make_list %}
                                        {% if testimonial.overall_rating >= i|add:0 %}★{% else %}☆{% endif %}
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}

        <!-- All Reviews -->
        <div class="section-title">
            <h2>All Reviews</h2>
            <p>Authentic feedback from schools using our platform</p>
        </div>

        {% for testimonial in testimonials %}
        <div class="testimonial-card">
            <div class="testimonial-header">
                <div class="testimonial-avatar">
                    {{ testimonial.author_name|first|upper }}
                </div>
                <div class="testimonial-info">
                    <h4>{{ testimonial.author_name }}</h4>
                    <div class="testimonial-meta">
                        {{ testimonial.author_title }}, {{ testimonial.school_name }}
                        {% if testimonial.location %} • {{ testimonial.location }}{% endif %}
                    </div>
                    <div class="testimonial-badges">
                        <span class="category-badge">{{ testimonial.get_review_category_display }}</span>
                        {% if testimonial.is_featured %}
                            <span class="featured-badge">Featured</span>
                        {% endif %}
                        <span class="school-type-badge">{{ testimonial.get_school_type_display }}</span>
                    </div>
                </div>
            </div>

            <div class="testimonial-content">
                <div class="testimonial-title">{{ testimonial.title }}</div>
                <div class="testimonial-text">{{ testimonial.quote|linebreaksbr }}</div>

                <div class="testimonial-ratings">
                    <div class="rating-item">
                        <div class="label">Overall</div>
                        <div class="stars">
                            {% for i in "12345"|make_list %}
                                {% if testimonial.overall_rating >= i|add:0 %}★{% else %}☆{% endif %}
                            {% endfor %}
                        </div>
                    </div>
                    {% if testimonial.ease_of_use_rating %}
                    <div class="rating-item">
                        <div class="label">Ease of Use</div>
                        <div class="stars">
                            {% for i in "12345"|make_list %}
                                {% if testimonial.ease_of_use_rating >= i|add:0 %}★{% else %}☆{% endif %}
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}
                    {% if testimonial.customer_support_rating %}
                    <div class="rating-item">
                        <div class="label">Support</div>
                        <div class="stars">
                            {% for i in "12345"|make_list %}
                                {% if testimonial.customer_support_rating >= i|add:0 %}★{% else %}☆{% endif %}
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}
                    {% if testimonial.value_rating %}
                    <div class="rating-item">
                        <div class="label">Value</div>
                        <div class="stars">
                            {% for i in "12345"|make_list %}
                                {% if testimonial.value_rating >= i|add:0 %}★{% else %}☆{% endif %}
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>

            <div class="testimonial-footer">
                <div class="usage-info">
                    {% if testimonial.usage_duration %}
                        <span><i class="bi bi-clock me-1"></i>Using for {{ testimonial.usage_duration }}</span>
                    {% endif %}
                    {% if testimonial.would_recommend %}
                        <span><i class="bi bi-hand-thumbs-up me-1"></i>Recommends</span>
                    {% endif %}
                </div>
                <div>{{ testimonial.submitted_on|date:"M d, Y" }}</div>
            </div>
        </div>
        {% empty %}
        <div class="testimonial-card">
            <div class="text-center">
                <h4>No reviews yet</h4>
                <p>Be the first to share your experience with our platform!</p>
                <a href="{% url 'public_site:testimonial_add' %}" class="btn-primary-gradient">
                    <i class="bi bi-chat-quote me-2"></i>
                    Write a Review
                </a>
            </div>
        </div>
        {% endfor %}

        <!-- Pagination -->
        {% if is_paginated %}
        <div class="pagination-container">
            <div class="pagination">
                {% if page_obj.has_previous %}
                    <a href="?page=1{% if current_filters.category != 'all' %}&category={{ current_filters.category }}{% endif %}{% if current_filters.school_type != 'all' %}&school_type={{ current_filters.school_type }}{% endif %}{% if current_filters.rating != 'all' %}&rating={{ current_filters.rating }}{% endif %}">&laquo; First</a>
                    <a href="?page={{ page_obj.previous_page_number }}{% if current_filters.category != 'all' %}&category={{ current_filters.category }}{% endif %}{% if current_filters.school_type != 'all' %}&school_type={{ current_filters.school_type }}{% endif %}{% if current_filters.rating != 'all' %}&rating={{ current_filters.rating }}{% endif %}">Previous</a>
                {% endif %}

                {% for num in page_obj.paginator.page_range %}
                    {% if page_obj.number == num %}
                        <span class="current">{{ num }}</span>
                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                        <a href="?page={{ num }}{% if current_filters.category != 'all' %}&category={{ current_filters.category }}{% endif %}{% if current_filters.school_type != 'all' %}&school_type={{ current_filters.school_type }}{% endif %}{% if current_filters.rating != 'all' %}&rating={{ current_filters.rating }}{% endif %}">{{ num }}</a>
                    {% endif %}
                {% endfor %}

                {% if page_obj.has_next %}
                    <a href="?page={{ page_obj.next_page_number }}{% if current_filters.category != 'all' %}&category={{ current_filters.category }}{% endif %}{% if current_filters.school_type != 'all' %}&school_type={{ current_filters.school_type }}{% endif %}{% if current_filters.rating != 'all' %}&rating={{ current_filters.rating }}{% endif %}">Next</a>
                    <a href="?page={{ page_obj.paginator.num_pages }}{% if current_filters.category != 'all' %}&category={{ current_filters.category }}{% endif %}{% if current_filters.school_type != 'all' %}&school_type={{ current_filters.school_type }}{% endif %}{% if current_filters.rating != 'all' %}&rating={{ current_filters.rating }}{% endif %}">Last &raquo;</a>
                {% endif %}
            </div>
        </div>
        {% endif %}

        <!-- Call to Action -->
        <div class="cta-section">
            <h3>Share Your Experience</h3>
            <p>Help other schools by sharing your experience with our platform. Your feedback helps us improve and helps others make informed decisions.</p>
            <a href="{% url 'public_site:testimonial_add' %}" class="btn-primary-gradient">
                <i class="bi bi-chat-quote me-2"></i>
                Write a Review
            </a>
        </div>
    </div>
</section>
{% endblock %}


