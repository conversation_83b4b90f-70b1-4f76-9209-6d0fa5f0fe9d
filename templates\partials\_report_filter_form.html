{# templates/partials/_report_filter_form.html - Premium Design Version #}
{% load i18n widget_tweaks %}

{% if filter_form %}
<style>
    /* Premium Report Filter Form Design */
    .premium-filter-card {
        border: none;
        border-radius: 1.5rem;
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        transition: all 0.3s ease;
        margin-bottom: 2rem;
    }

    .premium-filter-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    }

    .premium-filter-header {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        color: white;
        padding: 1.5rem;
        position: relative;
        overflow: hidden;
    }

    .premium-filter-header::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
        transform: rotate(45deg);
        animation: shimmer 3s infinite;
    }

    @keyframes shimmer {
        0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
        100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
    }

    .premium-filter-title {
        margin: 0;
        font-size: 1.25rem;
        font-weight: 600;
        position: relative;
        z-index: 1;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .premium-filter-toggle {
        color: rgba(255, 255, 255, 0.9);
        text-decoration: none;
        font-size: 0.9rem;
        font-weight: 500;
        transition: all 0.3s ease;
        position: relative;
        z-index: 1;
    }

    .premium-filter-toggle:hover {
        color: white;
        text-decoration: underline;
    }

    .premium-filter-body {
        padding: 2rem;
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
    }

    .form-floating-premium {
        position: relative;
        margin-bottom: 1.5rem;
    }

    .form-floating-premium .form-control,
    .form-floating-premium .form-select {
        height: calc(3.5rem + 2px);
        line-height: 1.25;
        border: 2px solid #e9ecef;
        border-radius: 1rem;
        padding: 1rem 0.75rem 0.25rem 0.75rem;
        background: rgba(255, 255, 255, 0.9);
        transition: all 0.3s ease;
        font-size: 1rem;
    }

    .form-floating-premium .form-control:focus,
    .form-floating-premium .form-select:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 0.25rem rgba(0, 123, 255, 0.25);
        background: white;
        transform: translateY(-2px);
    }

    .form-floating-premium > label {
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
        padding: 1rem 0.75rem;
        pointer-events: none;
        border: 2px solid transparent;
        transform-origin: 0 0;
        transition: opacity 0.1s ease-in-out, transform 0.1s ease-in-out;
        color: #6c757d;
        font-weight: 500;
    }

    .form-floating-premium > .form-control:focus ~ label,
    .form-floating-premium > .form-control:not(:placeholder-shown) ~ label,
    .form-floating-premium > .form-select ~ label {
        opacity: 0.65;
        transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
        color: #007bff;
    }

    .form-floating-premium .form-control:focus ~ label {
        color: #007bff;
    }

    .premium-field-icon {
        position: absolute;
        right: 1rem;
        top: 50%;
        transform: translateY(-50%);
        color: #6c757d;
        font-size: 1.1rem;
        z-index: 5;
        pointer-events: none;
        transition: color 0.3s ease;
    }

    .form-floating-premium .form-control:focus ~ .premium-field-icon,
    .form-floating-premium .form-select:focus ~ .premium-field-icon {
        color: #007bff;
    }

    .premium-checkbox-container {
        background: rgba(255, 255, 255, 0.9);
        border: 2px solid #e9ecef;
        border-radius: 1rem;
        padding: 1rem;
        transition: all 0.3s ease;
        margin-bottom: 1.5rem;
    }

    .premium-checkbox-container:hover {
        border-color: #007bff;
        background: rgba(0, 123, 255, 0.05);
    }

    .premium-checkbox {
        transform: scale(1.2);
        accent-color: #007bff;
        margin-right: 0.75rem;
    }

    .premium-checkbox-label {
        font-weight: 500;
        color: #495057;
        cursor: pointer;
        transition: color 0.3s ease;
    }

    .premium-checkbox:checked + .premium-checkbox-label {
        color: #007bff;
        font-weight: 600;
    }

    .btn-premium {
        border-radius: 0.75rem;
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        transition: all 0.3s ease;
        border: none;
        position: relative;
        overflow: hidden;
        min-width: 140px;
    }

    .btn-premium::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }

    .btn-premium:hover::before {
        left: 100%;
    }

    .btn-premium-primary {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        color: white;
        box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
    }

    .btn-premium-primary:hover {
        background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
        color: white;
    }

    .btn-premium-secondary {
        background: transparent;
        border: 2px solid #6c757d;
        color: #6c757d;
    }

    .btn-premium-secondary:hover {
        background: #6c757d;
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
    }

    .alert-premium {
        border-radius: 1rem;
        border: none;
        padding: 1rem 1.5rem;
        margin-top: 1rem;
    }

    .alert-premium.alert-danger {
        background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
        color: #721c24;
        border-left: 4px solid #dc3545;
    }

    .filter-actions-container {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 1rem;
        padding: 1.5rem;
        margin-top: 1.5rem;
        border: 1px solid #dee2e6;
    }

    @media (max-width: 768px) {
        .premium-filter-header {
            padding: 1rem;
        }

        .premium-filter-title {
            font-size: 1.1rem;
        }

        .premium-filter-body {
            padding: 1.5rem;
        }

        .btn-premium {
            padding: 0.6rem 1.2rem;
            font-size: 0.9rem;
            min-width: 120px;
        }
    }

    @media (max-width: 576px) {
        .form-floating-premium .form-control,
        .form-floating-premium .form-select {
            height: calc(3rem + 2px);
            padding: 0.75rem 0.5rem 0.25rem 0.5rem;
        }

        .premium-field-icon {
            right: 0.75rem;
        }
    }
</style>

<div class="card premium-filter-card">
    <div class="premium-filter-header">
        <div class="premium-filter-title">
            <span>
                <i class="bi bi-funnel-fill me-2"></i>{% trans "Filter Report Data" %}
                {% if specific_filter_title %}<span class="ms-2 opacity-75">({{ specific_filter_title }})</span>{% endif %}
            </span>
            <div class="d-flex align-items-center gap-3">
                {# Export buttons removed - use _report_filter_export_card.html instead for export functionality #}
                {# Toggle Button #}
                <a class="premium-filter-toggle" data-bs-toggle="collapse" href="#filterCollapse{{ report_code|default:'_default' }}" role="button" aria-expanded="{% if request.GET and not request.GET.export %}true{% else %}false{% endif %}" aria-controls="filterCollapse{{ report_code|default:'_default' }}">
                    <i class="bi bi-chevron-down me-1"></i>{% trans "Toggle Filters" %}
                </a>
            </div>
        </div>
    </div>
    <div class="collapse {% if request.GET and not request.GET.export %}show{% endif %}" id="filterCollapse{{ report_code|default:'_default' }}">
        <div class="premium-filter-body">
            <form method="get" novalidate id="reportFilterForm">
                <div class="row g-4">
                    {# Loop through visible fields of the passed filter_form #}
                    {% for field in filter_form.visible_fields %}
                        <div class="col-md-{% if field.name == 'student_query' or field.name == 'description_contains' %}6{% else %}4{% endif %}">
                            {% if field.field.widget_type == 'checkbox' %}
                                <div class="premium-checkbox-container">
                                    <div class="form-check">
                                        {% render_field field class+="premium-checkbox form-check-input" %}
                                        <label class="premium-checkbox-label form-check-label" for="{{ field.id_for_label }}">
                                            <i class="bi bi-check-square me-2"></i>{{ field.label }}
                                        </label>
                                    </div>
                                    {% if field.help_text %}
                                        <small class="form-text text-muted mt-2 d-block">{{ field.help_text }}</small>
                                    {% endif %}
                                </div>
                            {% else %}
                                <div class="form-floating-premium">
                                    {% if field.field.widget_type == 'select' %}
                                        {% render_field field class+="form-select" placeholder=field.label %}
                                        <i class="premium-field-icon bi bi-chevron-down"></i>
                                    {% elif field.field.widget.input_type == 'date' %}
                                        {% render_field field class+="form-control" placeholder=field.label %}
                                        <i class="premium-field-icon bi bi-calendar3"></i>
                                    {% elif field.field.widget.input_type == 'number' %}
                                        {% render_field field class+="form-control" placeholder=field.label %}
                                        <i class="premium-field-icon bi bi-123"></i>
                                    {% elif field.field.widget.input_type == 'text' %}
                                        {% render_field field class+="form-control" placeholder=field.label %}
                                        <i class="premium-field-icon bi bi-search"></i>
                                    {% else %}
                                        {% render_field field class+="form-control" placeholder=field.label %}
                                        <i class="premium-field-icon bi bi-pencil"></i>
                                    {% endif %}
                                    <label for="{{ field.id_for_label }}">{{ field.label }}</label>
                                    {% if field.help_text %}
                                        <small class="form-text text-muted mt-1">{{ field.help_text }}</small>
                                    {% endif %}
                                </div>
                            {% endif %}
                            {% for error in field.errors %}
                                <div class="text-danger small mt-1">
                                    <i class="bi bi-exclamation-triangle me-1"></i>{{ error }}
                                </div>
                            {% endfor %}
                        </div>
                    {% endfor %}
                </div>

                {# Action Buttons #}
                <div class="filter-actions-container">
                    <div class="row g-3 justify-content-end">
                        <div class="col-auto">
                            <a href="{{ request.path }}" class="btn btn-premium btn-premium-secondary">
                                <i class="bi bi-arrow-clockwise me-2"></i>{% trans "Reset Filters" %}
                            </a>
                        </div>
                        <div class="col-auto">
                            <button type="submit" class="btn btn-premium btn-premium-primary" id="applyFiltersBtn">
                                <i class="bi bi-search me-2"></i>{% trans "Apply Filters" %}
                            </button>
                        </div>
                    </div>
                </div>
            </form>

            {% if filter_form.non_field_errors %}
                <div class="alert alert-premium alert-danger">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    {% for error in filter_form.non_field_errors %}{{ error }}{% if not forloop.last %}<br>{% endif %}{% endfor %}
                </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Form submission enhancement
    const form = document.getElementById('reportFilterForm');
    const submitBtn = document.getElementById('applyFiltersBtn');

    if (form && submitBtn) {
        form.addEventListener('submit', function(e) {
            // Add loading state
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>{% trans "Applying..." %}';

            // Re-enable after 5 seconds in case of issues
            setTimeout(function() {
                submitBtn.disabled = false;
                submitBtn.innerHTML = '<i class="bi bi-search me-2"></i>{% trans "Apply Filters" %}';
            }, 5000);
        });
    }

    // Enhanced form field interactions
    const formControls = document.querySelectorAll('.form-floating-premium .form-control, .form-floating-premium .form-select');
    formControls.forEach(function(control) {
        // Focus enhancement
        control.addEventListener('focus', function() {
            this.closest('.form-floating-premium').style.transform = 'translateY(-2px)';
            this.closest('.form-floating-premium').style.boxShadow = '0 5px 15px rgba(0, 123, 255, 0.1)';
        });

        // Blur enhancement
        control.addEventListener('blur', function() {
            this.closest('.form-floating-premium').style.transform = 'translateY(0)';
            this.closest('.form-floating-premium').style.boxShadow = 'none';
        });
    });

    // Checkbox enhancement
    const checkboxes = document.querySelectorAll('.premium-checkbox');
    checkboxes.forEach(function(checkbox) {
        checkbox.addEventListener('change', function() {
            const container = this.closest('.premium-checkbox-container');
            if (this.checked) {
                container.style.borderColor = '#007bff';
                container.style.background = 'rgba(0, 123, 255, 0.1)';
            } else {
                container.style.borderColor = '#e9ecef';
                container.style.background = 'rgba(255, 255, 255, 0.9)';
            }
        });
    });

    // Keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Enter to submit form
        if (e.key === 'Enter' && e.ctrlKey) {
            e.preventDefault();
            if (submitBtn) {
                submitBtn.click();
            }
        }

        // Escape to reset form
        if (e.key === 'Escape') {
            const resetBtn = document.querySelector('a[href="{{ request.path }}"]');
            if (resetBtn) {
                resetBtn.click();
            }
        }
    });

    console.log('Premium report filter form initialized');
});
</script>

{% else %}
{# <p class="text-muted"><em>{% trans "No filters available for this report." %}</em></p> #}
{% endif %}