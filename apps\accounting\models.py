# D:\school_fees_saas_v2\apps\accounting\models.py

from django.db import models
from django.utils.translation import gettext_lazy as _
from django.conf import settings
# from apps.common.models import BaseModel # Assuming JournalEntry might not use it directly
from django.utils import timezone
from django.db.models import Sum, Q, DecimalField, ExpressionWrapper
from django.core.exceptions import ValidationError
from django.db.models.functions import Coalesce
from decimal import Decimal
from mptt.models import MPTTModel, TreeForeignKey # Assuming you use MPTT for Account


import logging
logger = logging.getLogger(__name__)



# apps/accounting/models.py
from django.db import models
from django.utils.translation import gettext_lazy as _
from django.utils import timezone # If created_at/updated_at use it

# apps/accounting/models.py
from django.db import models
from django.utils.translation import gettext_lazy as _
from django.utils import timezone # For created_at/updated_at

class AccountType(models.Model):
    # --- Choices for Classification (as you have them) ---
    class ClassificationChoices(models.TextChoices):
        ASSET = 'ASSET', _('Asset')
        LIABILITY = 'LIABILITY', _('Liability')
        EQUITY = 'EQUITY', _('Equity')
        REVENUE = 'REVENUE', _('Revenue') # Or INCOME
        EXPENSE = 'EXPENSE', _('Expense')
        COGS = 'COGS', _('Cost of Goods Sold')

    # --- Choices for Normal Balance (This is the one we will use) ---
    class NormalBalanceChoices(models.TextChoices): 
        DEBIT = 'DEBIT', _('Debit')   # For Assets, Expenses, COGS
        CREDIT = 'CREDIT', _('Credit') # For Liabilities, Equity, Revenue
    
    # --- Choices for Statement Section (Optional but good for reporting) ---
    class StatementSectionChoices(models.TextChoices):
        BALANCE_SHEET = 'BS', _('Balance Sheet')
        INCOME_STATEMENT = 'IS', _('Income Statement') # Also P&L

    name = models.CharField(
        _("name"),
        max_length=100, 
        unique=True, 
        help_text=_("User-friendly name (e.g., Cash, Accounts Payable, Sales Income)")
    )
    
    code = models.CharField( # This is likely your account_code for sorting purposes
        _("code"), 
        max_length=20, 
        unique=True, 
        null=True, 
        blank=True,
        db_index=True,
        help_text=_("Short unique code for system reference, e.g., 1001, 4100. Can be based on standard COA numbering.")
    )
    
    classification = models.CharField(
        _("classification"),
        max_length=20,
        choices=ClassificationChoices.choices,
        help_text=_("The financial statement classification of this account type.")
    )
    
    # --- THIS IS YOUR CORRECT FIELD FOR NORMAL BALANCE ---
    normal_balance = models.CharField(
        _("Normal Balance"),
        max_length=6, 
        choices=NormalBalanceChoices.choices, # Uses the nested TextChoices
        help_text=_("The normal balance (Debit or Credit) for accounts of this type. Determines how transactions affect the balance."),
        # This field needs to exist in your DB. If adding now, makemigrations will ask for a default.
        # Making it editable=False if always set by save() is an option, but admin might need to override.
        # blank=True, null=True, # Could be added if you want to allow it to be empty initially and rely on save()
    )

    statement_section = models.CharField(
        _("Statement Section"),
        max_length=2, 
        choices=StatementSectionChoices.choices,
        blank=True, 
        null=True,
        help_text=_("Which financial statement this account type primarily appears on.")
    )
    
    description = models.TextField(_("description"), blank=True, null=True)
    
    created_at = models.DateTimeField(auto_now_add=True, null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True, null=True, blank=True)

    class Meta:
        ordering = ['classification', 'name'] # Or ['code'] if you prefer sorting by code
        verbose_name = _("Account Type")
        verbose_name_plural = _("Account Types")

    def __str__(self):
        # Display normal_balance if it's populated
        nb_display = self.get_normal_balance_display() if self.normal_balance else "N/A"
        return f"{self.name} ({self.get_classification_display()}) - NB: {nb_display}"

    def save(self, *args, **kwargs):
        # Automatically set normal_balance based on classification IF IT'S NOT ALREADY SET
        # This allows manual override if needed, but provides a sensible default.
        if not self.normal_balance: 
            if self.classification in [self.ClassificationChoices.ASSET, 
                                        self.ClassificationChoices.EXPENSE, 
                                        self.ClassificationChoices.COGS]:
                self.normal_balance = self.NormalBalanceChoices.DEBIT
            elif self.classification in [self.ClassificationChoices.LIABILITY, 
                                        self.ClassificationChoices.EQUITY, 
                                        self.ClassificationChoices.REVENUE]:
                self.normal_balance = self.NormalBalanceChoices.CREDIT
        
        # Automatically set statement_section if not already set
        if not self.statement_section:
            if self.classification in [self.ClassificationChoices.ASSET, 
                                        self.ClassificationChoices.LIABILITY, 
                                        self.ClassificationChoices.EQUITY]:
                self.statement_section = self.StatementSectionChoices.BALANCE_SHEET
            elif self.classification in [self.ClassificationChoices.REVENUE, 
                                        self.ClassificationChoices.EXPENSE, 
                                        self.ClassificationChoices.COGS]:
                self.statement_section = self.StatementSectionChoices.INCOME_STATEMENT
        
        super().save(*args, **kwargs)


# D:\school_fees_saas_v2\apps\accounting\models.py

from django.db import models
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
from mptt.models import MPTTModel, TreeForeignKey
from decimal import Decimal

# Import other models from this app if needed
# from .models import AccountType

class Account(MPTTModel):
    """
    Represents a single account in the Chart of Accounts for a tenant.
    This model uses MPTT for hierarchical structure.
    """
    # CRITICAL FIX: The 'tenant' ForeignKey is removed.
    # Being in a TENANT_APP, this model is automatically scoped to the tenant's schema.
    
    name = models.CharField(
        _("Account Name"),
        max_length=150,
        help_text=_("e.g., Cash in Bank, Tuition Fees, Salaries Expense.")
    )
    code = models.CharField(
        _("Account Code"),
        max_length=20,
        blank=True, 
        null=True,
        help_text=_("Unique account code/number (e.g., 1010, 4000, 5010).")
    )
    account_type = models.ForeignKey(
        'AccountType', # Use string reference to avoid import order issues
        on_delete=models.PROTECT,
        related_name='accounts',
        verbose_name=_("Account Type")
    )
    description = models.TextField(
        _("Description"),
        blank=True,
        null=True,
        help_text=_("Optional description for the account.")
    )
    is_active = models.BooleanField(
        _("Is Active?"),
        default=True,
        db_index=True,
        help_text=_("Is this account currently active and usable for posting?")
    )
    parent = TreeForeignKey( # MPTT convention is to name this 'parent'
        'self',
        on_delete=models.CASCADE, # Use CASCADE for MPTT to correctly handle tree rebuilding
        null=True,
        blank=True,
        related_name='children',
        verbose_name=_("Parent Account"),
        help_text=_("Select if this is a sub-account of another account."),
        db_index=True
    )
    is_control_account = models.BooleanField(
        _("Is Control Account?"),
        default=False,
        help_text=_("Control accounts summarize sub-accounts. Direct posting to them should be restricted.")
    )
    can_be_used_in_je = models.BooleanField(
        _("Usable in Manual Journal Entry?"),
        default=True,
        help_text=_("Can this account be directly selected in a manual journal entry line?")
    )  
    
    # Bank detail fields are good as they are
    bank_name = models.CharField(max_length=100, blank=True, null=True)
    account_holder_name = models.CharField(max_length=150, blank=True, null=True)
    account_number = models.CharField(max_length=50, blank=True, null=True)
    branch_code = models.CharField(max_length=50, blank=True, null=True, verbose_name="Branch/Sort Code")
    swift_code = models.CharField(max_length=20, blank=True, null=True, verbose_name="SWIFT/BIC")
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class MPTTMeta:
        order_insertion_by = ['code', 'name']
        # The 'parent' attribute is automatically detected by MPTT if named 'parent'

    class Meta:
        verbose_name = _("Account")
        verbose_name_plural = _("Chart of Accounts")
        # CRITICAL FIX: The uniqueness is now within the tenant's schema automatically.
        # No need for unique_together with a 'tenant' field.
        unique_together = [['code']] # The code must be unique within this schema.
        ordering = ['tree_id', 'lft'] # Standard MPTT ordering

    def __str__(self):
        code_prefix = f"{self.code} - " if self.code else ""
        return f"{code_prefix}{self.name}"

    def clean(self):
        """
        Custom model validation.
        """
        super().clean()
        if self.code == '': self.code = None # Ensure empty strings are saved as NULL
        
        # MPTT handles most circular dependency checks automatically,
        # but this explicit check is still good.
        if self.parent and self.pk and self.parent_id == self.pk:
            raise ValidationError({'parent': _("An account cannot be its own parent.")})

        # Check that child and parent classifications match
        if self.parent and self.account_type:
            if self.parent.account_type.classification != self.account_type.classification:
                raise ValidationError({
                    'account_type': _(
                        f"Child account classification ('{self.account_type.get_classification_display()}') "
                        f"must align with parent's ('{self.parent.account_type.get_classification_display()}')."
                    )
                })

    def get_balance(self, as_of_date=None, start_date=None, include_children=False):
        """
        Calculates the balance of this account based on its normal balance type.
        """
        # --- FIX for Circular Import ---
        # Import models inside the method where they are needed.
        from django.db.models import Sum
        from django.db.models.functions import Coalesce
        from .models import JournalLine, JournalEntry, AccountType

        accounts_to_sum = [self]
        if include_children:
            accounts_to_sum.extend(list(self.get_descendants(include_self=False)))

        # Base queryset for posted journal entries
        line_queryset = JournalLine.objects.filter(
            account__in=accounts_to_sum,
            journal_entry__status=JournalEntry.StatusChoices.POSTED
        )
        
        # Apply date filters if provided
        if start_date:
            line_queryset = line_queryset.filter(journal_entry__date__gte=start_date)
        if as_of_date:
            line_queryset = line_queryset.filter(journal_entry__date__lte=as_of_date)

        # Aggregate debits and credits in a single database query
        totals = line_queryset.aggregate(
            total_debits=Coalesce(Sum('debit_amount'), Decimal('0.00')),
            total_credits=Coalesce(Sum('credit_amount'), Decimal('0.00'))
        )
        
        debits = totals['total_debits']
        credits = totals['total_credits']

        if self.account_type.normal_balance == AccountType.NormalBalanceChoices.DEBIT:
            return debits - credits
        elif self.account_type.normal_balance == AccountType.NormalBalanceChoices.CREDIT:
            return credits - debits
        return Decimal('0.00')

    @property
    def has_transactions(self):
        from .models import JournalLine, JournalEntry
        return JournalLine.objects.filter(account=self, journal_entry__status=JournalEntry.StatusChoices.POSTED).exists()

    def can_be_deleted(self):
        """
        An account can be deleted only if it has no transactions and no children.
        """
        return not self.has_transactions and not self.get_children().exists()
        
        
# class Account(MPTTModel):
#     tenant = models.ForeignKey(
#         settings.TENANT_MODEL, # This will resolve to 'tenants.School'
#         on_delete=models.CASCADE,
#         related_name='chart_of_accounts_entries',
#         default=1 
#         # db_index=True # Good for performance if you filter by tenant often
#     )
    
#     name = models.CharField(
#         max_length=150,
#         help_text=_("Name of the account (e.g., Cash in Bank, Tuition Fees, Salaries Expense).")
#     )
#     code = models.CharField(
#         max_length=20,
#         blank=True, 
#         null=True,
#         help_text=_("Unique account code/number (e.g., 1010, 4000, 5010). Can be blank.")
#     )
#     account_type = models.ForeignKey(
#         AccountType,
#         on_delete=models.PROTECT,
#         related_name='accounts',
#         verbose_name=_("Account Type")
#     )
    
#     # # --- THIS FIELD IS LIKELY MISSING OR MISNAMED ---
#     # account_code = models.CharField(
#     #     _("Account Code"), 
#     #     max_length=20, 
#     #     unique=True, # Account codes are usually unique per tenant
#     #     blank=True, null=True, # Or not blank/null if always required
#     #     help_text=_("Unique code for this account (e.g., 1010, 4000-A).")
#     # )
#     # # --- END OF FIELD ---
    
#     description = models.TextField(
#         blank=True,
#         null=True,
#         help_text=_("Optional description for the account.")
#     )
#     is_active = models.BooleanField(
#         default=True,
#         verbose_name=_("Is Active?"),
#         db_index=True,
#         help_text=_("Is this account currently active and usable for posting?")
#     )
    
#     parent_account = TreeForeignKey(
#         'self',
#         on_delete=models.SET_NULL, 
#         null=True,
#         blank=True,
#         related_name='child_accounts',
#         verbose_name=_("Parent Account"),
#         help_text=_("Select if this is a sub-account of another account (for hierarchical CoA)."),
#         db_index=True
#     )
    
#     is_control_account = models.BooleanField(
#         default=False,
#         verbose_name=_("Is Control Account?"),
#         help_text=_("Control accounts summarize sub-accounts. Direct posting to them might be restricted.")
#     )
    
#     can_be_used_in_je = models.BooleanField(
#         _("Usable in Manual Journal Entry?"),
#         default=True,
#         help_text=_("Can this account be directly selected in a manual journal entry line?")
#     )  
    
#     # --- NEW BANK DETAIL FIELDS ---
#     # These fields will only be used/shown if the account_type is 'Cash Equivalents' (i.e., a bank account)
#     bank_name = models.CharField(max_length=100, blank=True, null=True)
#     account_holder_name = models.CharField(max_length=150, blank=True, null=True)
#     account_number = models.CharField(max_length=50, blank=True, null=True)
#     branch_code = models.CharField(max_length=50, blank=True, null=True, verbose_name="Branch/Sort Code")
#     swift_code = models.CharField(max_length=20, blank=True, null=True, verbose_name="SWIFT/BIC")
    
    
#     created_at = models.DateTimeField(auto_now_add=True)
#     updated_at = models.DateTimeField(auto_now=True)

#     class MPTTMeta:
#         order_insertion_by = ['code', 'name']
#         parent_attr = 'parent_account'

#     class Meta:
#         verbose_name = _("Chart of Account Entry")
#         verbose_name_plural = _("Chart of Accounts")
#         constraints = [
#             models.UniqueConstraint(
#                 fields=['code'],
#                 name='unique_account_code_mptt_accounting', # Make constraint name more specific
#                 condition=models.Q(code__isnull=False) & ~models.Q(code__exact='')
#             )
#         ]
#         ordering = ['tenant', 'tree_id', 'lft']
        
#         unique_together = [['tenant', 'code']]

#     def __str__(self):
#         code_prefix = f"{self.code} - " if self.code else ""
#         type_name = self.account_type.name if self.account_type else _("N/A Type")
#         return f"{code_prefix}{self.name} ({type_name})"

#     def clean(self):
#         super().clean()
#         if self.code == '':
#             self.code = None
#         if self.parent_account_id and self.pk and self.parent_account_id == self.pk:
#             raise ValidationError({'parent_account': _("An account cannot be its own parent.")})
#         if self.parent_account and self.pk:
#             if self.parent_account.is_descendant_of(self, include_self=True):
#                 raise ValidationError({'parent_account': _("Circular dependency: cannot make an account a child of its own descendant.")})
#             if self.account_type and self.parent_account.account_type:
#                 if self.parent_account.account_type.classification != self.account_type.classification:
#                     raise ValidationError(_(
#                         f"Child account classification ('{self.account_type.get_classification_display()}') "
#                         f"must align with parent's ('{self.parent_account.account_type.get_classification_display()}')."
#                     ))

#     # --- Corrected Balance Methods for amount + entry_type on JournalLine ---
#     def get_total_debits(self, start_date=None, end_date=None, include_children=False):
#         accounts_to_sum = [self]
#         if include_children:
#             accounts_to_sum.extend(list(self.get_descendants(include_self=False)))

#         queryset = JournalLine.objects.filter(
#             account__in=accounts_to_sum,
#             journal_entry__status=JournalEntry.StatusChoices.POSTED # Only posted entries
#         )
#         if start_date:
#             queryset = queryset.filter(journal_entry__date__gte=start_date)
#         if end_date:
#             queryset = queryset.filter(journal_entry__date__lte=end_date)
#         return queryset.aggregate(total=Coalesce(Sum('debit_amount'), Decimal('0.00')))['total']

#     def get_total_credits(self, start_date=None, end_date=None, include_children=False):
#         accounts_to_sum = [self]
#         if include_children:
#             accounts_to_sum.extend(list(self.get_descendants(include_self=False)))

#         queryset = JournalLine.objects.filter(
#             account__in=accounts_to_sum,
#             journal_entry__status=JournalEntry.StatusChoices.POSTED # Only posted entries
#         )
#         if start_date:
#             queryset = queryset.filter(journal_entry__date__gte=start_date)
#         if end_date:
#             queryset = queryset.filter(journal_entry__date__lte=end_date)
#         return queryset.aggregate(total=Coalesce(Sum('credit_amount'), Decimal('0.00')))['total']

#     def get_balance(self, as_of_date=None, start_date=None, include_children=False):
#         debits = self.get_total_debits(start_date=start_date, end_date=as_of_date, include_children=include_children)
#         credits = self.get_total_credits(start_date=start_date, end_date=as_of_date, include_children=include_children)
#         if not self.account_type: return Decimal('0.00')
#         if self.account_type.normal_balance == AccountType.NormalBalanceChoices.DEBIT:
#             return debits - credits
#         elif self.account_type.normal_balance == AccountType.NormalBalanceChoices.CREDIT:
#             return credits - debits
#         return Decimal('0.00')

#     @property
#     def has_transactions(self):
#         return JournalLine.objects.filter(account=self, journal_entry__status=JournalEntry.StatusChoices.POSTED).exists()

#     def can_be_deleted(self):
#         return not self.has_transactions and not self.get_children().exists()



# apps/accounting/models.py

# ... (imports: models, timezone, Decimal, _, Sum, Coalesce, F, ExpressionWrapper, DecimalField, ValidationError, transaction, settings) ...
# Ensure all necessary imports are at the top of your file.
from django.db import models, transaction # Added transaction
from django.utils import timezone
from decimal import Decimal
from django.utils.translation import gettext_lazy as _
from django.conf import settings
from django.core.exceptions import ValidationError
from django.db.models import Sum, F, ExpressionWrapper, DecimalField
from django.db.models.functions import Coalesce

# Assuming ChartOfAccount is defined in this file or correctly imported
# from .models import ChartOfAccount # Or wherever it is, e.g., if you named it Account
# For consistency, let's assume your Chart of Accounts model is named ChartOfAccount
# If it's named 'Account' as used in your JournalLine FK, we'll use that.
# Let's assume it's 'ChartOfAccount' for now as per my previous model suggestions.
# If it's 'Account', replace 'ChartOfAccount' with 'Account' below.

import logging
logger = logging.getLogger(__name__)


class JournalEntry(models.Model):
    class StatusChoices(models.TextChoices):
        DRAFT = 'DRAFT', _('Draft')
        POSTED = 'POSTED', _('Posted')
        CANCELLED = 'CANCELLED', _('Cancelled') # Good to have
    
    class EntryTypeChoices(models.TextChoices):
        MANUAL = 'MANUAL', _('Manual Entry')
        INVOICE = 'INVOICE', _('Invoice Posting')
        PAYMENT = 'PAYMENT', _('Payment Received')
        EXPENSE = 'EXPENSE', _('Expense Recorded')
        ADJUSTMENT = 'ADJUSTMENT', _('Adjustment Entry')
        OPENING_BALANCE = 'OPENING_BALANCE', _('Opening Balance')
        YEAR_END = 'YEAR_END', _('Year End Closing')
        
    class EntryStatus(models.TextChoices): # <<< THIS IS WHAT'S MISSING OR MISNAMED
        DRAFT = 'DRAFT', _('Draft')
        POSTED = 'POSTED', _('Posted')

    entry_number = models.CharField(
        _("entry number"), 
        max_length=50, 
        unique=True, # If accounting is a tenant app (tables per schema), this is fine.
                    # If shared table with tenant FK, use unique_together=('tenant', 'entry_number')
        blank=True, # Will be auto-generated
        help_text=_("System-generated unique journal entry number.")
    )
    date = models.DateField(_("Entry Date"), default=timezone.now, db_index=True)
    narration = models.TextField(_("Narration/Description"), help_text=_("Overall purpose of this journal entry.")) # Added help_text
    entry_type = models.CharField(
        _("Entry Type"), max_length=20, choices=EntryTypeChoices.choices,
        default=EntryTypeChoices.MANUAL # Correct for manual JE system
    )
    status = models.CharField(
        _("Status"), max_length=10, default=StatusChoices.DRAFT, # Correct for manual JE system
        choices=StatusChoices.choices, db_index=True
    )
    posted_at = models.DateTimeField(_("posted at"), null=True, blank=True, help_text=_("Timestamp when the entry was posted."))
    
    created_by = models.ForeignKey(
        settings.STAFF_USER_MODEL, # Use the new setting
        on_delete=models.SET_NULL,
        null=True, blank=True,
        related_name='created_journal_entries'
    )
    
    last_modified_by = models.ForeignKey( # This can be the same as created_by initially
        settings.AUTH_USER_MODEL, 
        on_delete=models.SET_NULL, 
        null=True, blank=True, related_name='journal_entries_modified'
    )
    # If accounting is a tenant app, tenant context is implicit.
    # If JournalEntry is a shared table, you need:
    # tenant = models.ForeignKey(settings.TENANT_MODEL, on_delete=models.CASCADE, related_name="journal_entries")


    is_reversing_entry = models.BooleanField(default=False, help_text=_("Indicates if this JE is a reversing entry."))
    reverses_entry = models.OneToOneField(
        'self', 
        on_delete=models.SET_NULL, # Or PROTECT if you never want to delete an original if it's been reversed
        null=True, blank=True, 
        related_name='reversed_by_entry',
        help_text=_("The journal entry that this entry reverses, if applicable.")
    )
    
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-date', '-id']
        verbose_name = _("Journal Entry")
        verbose_name_plural = _("Journal Entries")
        # Add permissions if you need more granular control like 'post_journalentry'
        # permissions = [("can_post_journalentry", "Can post journal entries")]

    def __str__(self):
        return f"{self.entry_number or f'JE-{self.pk}'} ({self.date}): {self.narration[:50]}"
    
    @property
    def total_debits(self):
        # Ensure related_name on JournalLine.journal_entry is 'lines'
        return self.lines.aggregate(total=Coalesce(Sum('debit_amount'), Decimal('0.00')))['total']
    
    
    @property
    def total_credits(self):
        return self.lines.aggregate(total=Coalesce(Sum('credit_amount'), Decimal('0.00')))['total']

    @property # Changed from method to property for consistency
    def is_balanced(self):
        return self.total_debits == self.total_credits
    
    @property
    def balance_difference(self):
        return self.total_debits - self.total_credits

    def clean(self):
        super().clean()
        if self.pk and self.status == self.StatusChoices.POSTED and not self.is_balanced:
            raise ValidationError(
                _("Posted journal entry must be balanced. Difference (Debit-Credit): %(diff)s") % 
                {'diff': self.balance_difference}
            )

    @classmethod
    def generate_new_entry_number(cls, tenant_instance=None): # Pass tenant if needed for tenant-specific sequence
        # Your existing logic is fine, but ensure it works correctly in a multi-tenant context
        # if entry numbers need to be unique per tenant but might overlap globally.
        # If 'accounting' app tables are per-schema, then cls.objects.all() is already tenant-scoped.
        today = timezone.now().date()
        date_prefix = today.strftime("%Y%m%d")
        
        # If you have tenant field directly on JE model:
        # queryset = cls.objects.filter(tenant=tenant_instance) if tenant_instance else cls.objects.all()
        # Else, if tenant-scoped by schema, cls.objects.all() is fine.
        queryset = cls.objects.all() # Assumes tenant-scoped by schema

        base_number_str = f"JE-{date_prefix}-"
        # More robust sequence generation:
        last_entry_with_prefix = queryset.filter(entry_number__startswith=base_number_str).order_by('entry_number').last()
        sequence = 1
        if last_entry_with_prefix and last_entry_with_prefix.entry_number:
            try:
                last_sequence_str = last_entry_with_prefix.entry_number.split('-')[-1]
                sequence = int(last_sequence_str) + 1
            except (IndexError, ValueError, TypeError):
                logger.warning(f"Could not parse sequence from {last_entry_with_prefix.entry_number} for prefix {base_number_str}. Re-evaluating based on count.")
                # Fallback if parsing fails, count entries with the same prefix
                sequence = queryset.filter(entry_number__startswith=base_number_str).count() + 1
        
        while True:
            new_entry_number = f"{base_number_str}{sequence:04d}"
            if not queryset.filter(entry_number=new_entry_number).exists():
                break
            sequence += 1
        return new_entry_number

    def save(self, *args, **kwargs):
        is_new = self._state.adding
        if is_new and not self.entry_number:
            # If tenant context is needed for generate_new_entry_number and JE is tenant-scoped by schema
            # request.tenant might not be available here directly.
            # If JE has a 'tenant' FK, pass self.tenant to generate_new_entry_number.
            self.entry_number = self.generate_new_entry_number()
        
        if not self.created_by_id and hasattr(kwargs.get('user_request'), 'user'): # Pass user via kwargs if possible
            self.created_by = kwargs.get('user_request').user
        if hasattr(kwargs.get('user_request'), 'user'):
            self.last_modified_by = kwargs.get('user_request').user

        super().save(*args, **kwargs)

    def post(self, user_request=None): # Pass the full request or just user
        if self.status == self.StatusChoices.POSTED:
            raise ValidationError(_("Journal entry is already posted."))
        if self.status == self.StatusChoices.CANCELLED:
            raise ValidationError(_("Cannot post a cancelled journal entry."))
        if not self.pk:
            raise ValidationError(_("Journal entry must be saved (be a draft) before posting."))

        if not self.is_balanced:
            raise ValidationError(
                _(f"Cannot post unbalanced journal entry. Difference (Debit-Credit): {self.balance_difference}")
            )
        
        user = getattr(user_request, 'user', None) if user_request else None

        with transaction.atomic():
            # Assuming your GeneralLedger model is for actual ledger postings
            from .models import GeneralLedger # Local import, ensure it's correctly named and located

            for line in self.lines.all(): # Assumes related_name is 'lines'
                if line.debit_amount > 0:
                    transaction_type = GeneralLedger.TransactionTypeChoices.DEBIT
                    amount = line.debit_amount
                elif line.credit_amount > 0:
                    transaction_type = GeneralLedger.TransactionTypeChoices.CREDIT
                    amount = line.credit_amount
                else:
                    # Skip lines with zero debit and credit, though formset should prevent these if not just empty.
                    continue 
                
                GeneralLedger.objects.create(
                    transaction_date=self.date, 
                    account=line.account,
                    description=line.description or self.narration, # Line desc takes precedence
                    transaction_type=transaction_type,
                    amount=amount,
                    journal_entry=self,
                    # created_by=user, # If GeneralLedger also tracks creator
                    # tenant=self.tenant, # If GeneralLedger needs explicit tenant and JE has it
                )
            self.status = self.StatusChoices.POSTED
            self.posted_at = timezone.now()
            if user:
                self.last_modified_by = user
            self.save(update_fields=['status', 'posted_at', 'last_modified_by'])
            logger.info(f"Journal Entry {self.entry_number or self.pk} posted by {getattr(user, 'email', 'System')}.")

    def unpost(self, user_request=None): # Pass the full request or just user
        if self.status != self.StatusChoices.POSTED:
            raise ValidationError(_("Only posted journal entries can be unposted."))
        
        user = getattr(user_request, 'user', None) if user_request else None

        with transaction.atomic():
            from .models import GeneralLedger # Local import
            
            # Delete related GeneralLedger entries
            # Assuming related_name from GeneralLedger.journal_entry to JournalEntry is 'gl_entries'
            # If not, query GeneralLedger.objects.filter(journal_entry=self).delete()
            if hasattr(self, 'gl_entries_for_journal_entry'): # Use the actual related_name
                self.gl_entries_for_journal_entry.all().delete()
            else: # Fallback, less efficient if many GL entries per JE
                GeneralLedger.objects.filter(journal_entry=self).delete()


            self.status = self.StatusChoices.DRAFT
            self.posted_at = None
            if user:
                self.last_modified_by = user
            self.save(update_fields=['status', 'posted_at', 'last_modified_by'])
            logger.info(f"Journal Entry {self.entry_number or self.pk} unposted by {getattr(user, 'email', 'System')}.")


    def can_be_reversed(self):
        """
        Checks if this journal entry can be reversed.
        Only POSTED entries that haven't already been reversed.
        """
        return self.status == self.EntryStatus.POSTED and not hasattr(self, 'reversed_by_entry') # Check if reversed_by_entry is None
    
    

class JournalLine(models.Model): # Renamed from JournalEntryLine for consistency with my previous full suggestion
    journal_entry = models.ForeignKey(
        JournalEntry, 
        on_delete=models.CASCADE, 
        related_name='lines' # This is important for JE.total_debits/credits
    )
    account = models.ForeignKey(
        'Account', # Ensure this is your Chart of Accounts model name
        on_delete=models.PROTECT, 
        related_name='journal_lines',
        verbose_name=_("Account")
    )
    # REMOVE old 'amount' and 'entry_type' if they existed here
    
    debit_amount = models.DecimalField(
        _("Debit Amount"), 
        max_digits=12, 
        decimal_places=2, 
        default=Decimal('0.00')
    )
    credit_amount = models.DecimalField(
        _("Credit Amount"), 
        max_digits=12, 
        decimal_places=2, 
        default=Decimal('0.00')
    )
    
    description = models.CharField(
        _("Line Description"),
        max_length=255,
        blank=True, # Keep blank=True if truly optional
        help_text=_("Optional description for this specific line item.")
    )
    # created_at and updated_at are not strictly necessary here if JE has them,
    # but can be useful for auditing line changes if you allow line edits later.
    # created_at = models.DateTimeField(auto_now_add=True, null=True, blank=True)
    # updated_at = models.DateTimeField(auto_now=True, null=True, blank=True)

    class Meta:
        ordering = ['pk'] # Or ['journal_entry', 'pk']
        verbose_name = _("Journal Line")
        verbose_name_plural = _("Journal Lines")

    def __str__(self):
        je_identifier = self.journal_entry.entry_number or f"JE-{self.journal_entry_id}"
        acc_name = self.account.name if self.account else _("N/A Account")
        if self.debit_amount > 0:
            return f"{_('Line for')} {je_identifier}: {_('Debit')} {acc_name} - {self.debit_amount}"
        elif self.credit_amount > 0:
            return f"{_('Line for')} {je_identifier}: {_('Credit')} {acc_name} - {self.credit_amount}"
        return f"{_('Line for')} {je_identifier}: {acc_name} - {_('Zero Amounts')}"

    def clean(self):
        super().clean()
        # Ensure Decimals, especially if data might come from places other than a DecimalField form widget
        if self.debit_amount is None: self.debit_amount = Decimal('0.00')
        if self.credit_amount is None: self.credit_amount = Decimal('0.00')

        if self.debit_amount < Decimal('0.00') or self.credit_amount < Decimal('0.00'):
            raise ValidationError(_("Debit and Credit amounts must not be negative."))
        if self.debit_amount > Decimal('0.00') and self.credit_amount > Decimal('0.00'):
            raise ValidationError(_("A journal line cannot have both a debit and a credit amount."))
        
        # A line MUST have an account if it's not being deleted.
        if not self.account_id: # Check account_id to avoid RelatedObjectDoesNotExist if account not set yet
            if (self.debit_amount > Decimal('0.00') or self.credit_amount > Decimal('0.00') or self.description):
                raise ValidationError({'account': _("An account is required if amounts or description are provided.")})
        elif self.account and not self.account.is_active: # Assuming your Account model has is_active
            raise ValidationError({'account': _(f"Account '{self.account.name}' is inactive and cannot be used.")})
        
        # Auto-fill line description from JE narration if this line's description is empty
        # This should happen on save or in the form, not really in clean() for model integrity.
        # if not self.description and self.journal_entry and self.journal_entry.narration:
        #     self.description = self.journal_entry.narration[:255]

    def save(self, *args, **kwargs):
        # Auto-fill line description from JE narration if this line's description is empty
        # This is a good place for such logic before saving.
        if not self.description and self.journal_entry and self.journal_entry.narration:
            self.description = self.journal_entry.narration[:255] # Ensure it fits CharField
        super().save(*args, **kwargs)


# class JournalEntry(models.Model):
#     # ... (Your existing JournalEntry model definition - NO CHANGES NEEDED HERE YET) ...
#     # The total_debits and total_credits properties will change based on JournalLine
#     class StatusChoices(models.TextChoices):
#         DRAFT = 'DRAFT', _('Draft')
#         POSTED = 'POSTED', _('Posted')
#         CANCELLED = 'CANCELLED', _('Cancelled')
    
#     class EntryTypeChoices(models.TextChoices):
#         MANUAL = 'MANUAL', _('Manual Entry')
#         INVOICE = 'INVOICE', _('Invoice Posting')
#         PAYMENT = 'PAYMENT', _('Payment Received')
#         EXPENSE = 'EXPENSE', _('Expense Recorded')
#         ADJUSTMENT = 'ADJUSTMENT', _('Adjustment Entry')
#         OPENING_BALANCE = 'OPENING_BALANCE', _('Opening Balance')
#         YEAR_END = 'YEAR_END', _('Year End Closing')

#     entry_number = models.CharField(
#         _("entry number"), 
#         max_length=50, 
#         unique=False, # Should eventually be unique per tenant if not globally
#         blank=True,
#         help_text=_("System-generated unique journal entry number.")
#     )
#     date = models.DateField(_("Entry Date"), default=timezone.now, db_index=True)
#     narration = models.TextField(_("Narration/Description"))
#     entry_type = models.CharField(
#         _("Entry Type"), max_length=20, choices=EntryTypeChoices.choices,
#         default=EntryTypeChoices.MANUAL
#     )
#     status = models.CharField(
#         _("Status"), max_length=10, default=StatusChoices.DRAFT,
#         choices=StatusChoices.choices, db_index=True
#     )
#     posted_at = models.DateTimeField(_("posted at"), null=True, blank=True, help_text=_("Timestamp when the entry was posted."))
#     created_by = models.ForeignKey(
#         settings.AUTH_USER_MODEL, on_delete=models.SET_NULL,
#         null=True, blank=True, related_name='created_journal_entries'
#     )
#     last_modified_by = models.ForeignKey(
#         settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, 
#         null=True, blank=True, related_name='journal_entries_modified'
#     )
#     created_at = models.DateTimeField(auto_now_add=True)
#     updated_at = models.DateTimeField(auto_now=True)
    
#     class Meta:
#         ordering = ['-date', '-id']
#         verbose_name = _("Journal Entry")
#         verbose_name_plural = _("Journal Entries")

#     def __str__(self):
#         return f"JE {self.entry_number or self.pk} - {self.narration[:50]} ({self.date})"

#     @property
#     def total_debits(self):
#         # This will now sum the 'debit_amount' field from JournalLine
#         return self.lines.aggregate(total=Coalesce(Sum('debit_amount'), Decimal('0.00')))['total']

#     @property
#     def total_credits(self):
#         # This will now sum the 'credit_amount' field from JournalLine
#         return self.lines.aggregate(total=Coalesce(Sum('credit_amount'), Decimal('0.00')))['total']

#     def is_entry_balanced(self):
#         debits = self.total_debits
#         credits = self.total_credits
#         difference = debits - credits
#         return difference == Decimal('0.00'), difference

#     def clean(self):
#         super().clean()
#         if self.pk and self.status == self.StatusChoices.POSTED:
#             is_balanced, diff = self.is_entry_balanced()
#             if not is_balanced:
#                 raise ValidationError(_(f"Posted journal entry must be balanced. Difference (Debit-Credit): {diff}"))

#     @classmethod
#     def generate_new_entry_number(cls, tenant_instance=None):
#         # ... (your existing generate_new_entry_number logic - no changes needed here) ...
#         today = timezone.now().date()
#         date_prefix = today.strftime("%Y%m%d")
#         base_number_str = f"JE-{date_prefix}-"
#         queryset = cls.objects.all()
#         last_entry = queryset.filter(entry_number__startswith=base_number_str).order_by('entry_number').last()
#         sequence = 1
#         if last_entry and last_entry.entry_number:
#             try:
#                 last_sequence_str = last_entry.entry_number.split('-')[-1]
#                 sequence = int(last_sequence_str) + 1
#             except (IndexError, ValueError, TypeError):
#                 # logger.warning(f"Could not parse sequence from {last_entry.entry_number}. Checking count.")
#                 existing_similar_count = queryset.filter(entry_number__startswith=base_number_str).count()
#                 sequence = existing_similar_count + 1
#         new_entry_number = f"{base_number_str}{sequence:04d}"
#         while cls.objects.filter(entry_number=new_entry_number).exists():
#             sequence += 1
#             new_entry_number = f"{base_number_str}{sequence:04d}"
#         return new_entry_number


#     def save(self, *args, **kwargs):
#         if not self.entry_number:
#             self.entry_number = self.generate_new_entry_number()
#         super().save(*args, **kwargs)

#     def post(self, user=None):
#         if self.status == self.StatusChoices.POSTED: raise ValidationError(_("Journal entry is already posted."))
#         if self.status == self.StatusChoices.CANCELLED: raise ValidationError(_("Cannot post a cancelled journal entry."))
#         if not self.pk: raise ValidationError(_("Journal entry must be saved before posting."))

#         is_balanced, diff = self.is_entry_balanced()
#         if not is_balanced: raise ValidationError(_(f"Cannot post unbalanced journal entry. Difference (Debit-Credit): {diff}"))
        
#         with transaction.atomic():
#             from .models import GeneralLedger # Local import for your GL posting model
#             for line in self.lines.all():
#                 # Adapt GL posting based on new debit_amount/credit_amount fields
#                 if line.debit_amount > 0:
#                     transaction_type = GeneralLedger.TransactionTypeChoices.DEBIT
#                     amount = line.debit_amount
#                 elif line.credit_amount > 0:
#                     transaction_type = GeneralLedger.TransactionTypeChoices.CREDIT
#                     amount = line.credit_amount
#                 else: # Should not happen if line validation is good
#                     continue 
                
#                 GeneralLedger.objects.create(
#                     transaction_date=self.date, 
#                     account=line.account,
#                     description=line.description or self.narration,
#                     transaction_type=transaction_type,
#                     amount=amount,
#                     journal_entry=self
#                 )
#             self.status = self.StatusChoices.POSTED
#             self.posted_at = timezone.now()
#             if user: self.last_modified_by = user
#             self.save(update_fields=['status', 'posted_at', 'last_modified_by'])
#             # logger.info(f"Journal Entry {self.entry_number or self.pk} posted by {user}.")

#     def unpost(self, user=None):
#         if self.status != self.StatusChoices.POSTED: raise ValidationError(_("Only posted journal entries can be unposted."))
#         with transaction.atomic():
#             # Assuming self.gl_entries is the related_name from GeneralLedger to JournalEntry
#             if hasattr(self, 'gl_entries'):
#                 self.gl_entries.all().delete()
#             else:
#                 # Fallback if related_name is different or you need to query GeneralLedger directly
#                 from .models import GeneralLedger
#                 GeneralLedger.objects.filter(journal_entry=self).delete()

#             self.status = self.StatusChoices.DRAFT
#             self.posted_at = None
#             if user: self.last_modified_by = user
#             self.save(update_fields=['status', 'posted_at', 'last_modified_by'])
#             # logger.info(f"Journal Entry {self.entry_number or self.pk} unposted by {user}.")




class JournalEntryItem(models.Model): # Your provided model name
    journal_entry = models.ForeignKey(JournalEntry, on_delete=models.CASCADE, related_name='items') # Changed related_name
    account = models.ForeignKey(
        'Account', # This should be your ChartOfAccount model
        on_delete=models.PROTECT, 
        related_name='journal_items' # Changed related_name
    )
    debit = models.DecimalField( # Field name is 'debit'
        _("debit"), 
        max_digits=12, 
        decimal_places=2, 
        null=True, blank=True, default=Decimal('0.00') # Added default
    )
    credit = models.DecimalField( # Field name is 'credit'
        _("credit"), 
        max_digits=12, 
        decimal_places=2, 
        null=True, blank=True, default=Decimal('0.00') # Added default
    )
    description = models.CharField(
        _("description"), 
        max_length=255, 
        blank=True, 
        # null=True is good if it's truly optional and you want DB nulls
    )
    # created_at = models.DateTimeField(auto_now_add=True, null=True, blank=True) # Consider adding
    # updated_at = models.DateTimeField(auto_now=True, null=True, blank=True)   # Consider adding


    class Meta:
        ordering = ['pk']
        verbose_name = _("Journal Entry Item")
        verbose_name_plural = _("Journal Entry Items")

    def __str__(self):
        # Ensure self.account and self.account.name exist before accessing
        acc_name = self.account.name if self.account and hasattr(self.account, 'name') else "N/A Account"
        if self.debit is not None and self.debit > 0: # Check against 0 explicitly
            return f"DR: {acc_name} - {self.debit}"
        elif self.credit is not None and self.credit > 0:
            return f"CR: {acc_name} - {self.credit}"
        return f"Item for {acc_name} (Zero/No Amount)" # More descriptive for zero amounts

    def clean(self):
        super().clean()
        # Ensure defaults are applied if None, before comparison
        debit_val = self.debit if self.debit is not None else Decimal('0.00')
        credit_val = self.credit if self.credit is not None else Decimal('0.00')

        if debit_val < Decimal('0.00'):
            raise ValidationError({'debit': _("Debit amount cannot be negative.")})
        if credit_val < Decimal('0.00'):
            raise ValidationError({'credit': _("Credit amount cannot be negative.")})
        
        if debit_val > Decimal('0.00') and credit_val > Decimal('0.00'):
            raise ValidationError(_("A journal entry item cannot have both a debit and a credit amount greater than zero."))
        
        # This validation might be too strict for an empty formset row.
        # It's better to ensure that if an account is selected, an amount must be present.
        # Or that if the form is not empty, then amounts must be valid.
        if self.account and debit_val == Decimal('0.00') and credit_val == Decimal('0.00'):
            # This is valid if description is provided and we want a zero-value line with description
            # However, for financial balancing, it's effectively a no-op.
            # Let's allow it if description is present, otherwise require an amount.
            if not self.description:
                raise ValidationError(_("A journal entry item with an account must have either a debit or a credit amount, or a description."))

        if self.account and not hasattr(self.account, 'is_active'): # Basic check
            pass # logger.warning("Account model does not have 'is_active' attribute.")
        elif self.account and hasattr(self.account, 'is_active') and not self.account.is_active:
            raise ValidationError({'account': _(f"Account '{self.account.name}' is inactive and cannot be used.")})

        # Auto-fill line description from JE narration if line description is empty
        if not self.description and self.journal_entry and self.journal_entry.narration:
            self.description = self.journal_entry.narration[:255]



# class JournalLine(models.Model):
#     journal_entry = models.ForeignKey(
#         JournalEntry, 
#         on_delete=models.CASCADE, 
#         related_name='lines'
#     )
#     account = models.ForeignKey(
#         'Account', # Ensure 'Account' is your Chart of Accounts model
#         on_delete=models.PROTECT, 
#         related_name='journal_lines'
#     )
#     # REMOVE 'amount' and 'entry_type'
#     # amount = models.DecimalField(...)
#     # entry_type = models.CharField(...)

#     # ADD 'debit_amount' and 'credit_amount'
#     debit_amount = models.DecimalField(
#         _("Debit Amount"), 
#         max_digits=12, 
#         decimal_places=2, 
#         default=Decimal('0.00')
#     )
#     credit_amount = models.DecimalField(
#         _("Credit Amount"), 
#         max_digits=12, 
#         decimal_places=2, 
#         default=Decimal('0.00')
#     )
    
#     description = models.CharField(
#         _("Line Description"),
#         max_length=255,
#         blank=True,
#         null=True, # Allow null for easier form handling if description is truly optional
#         help_text=_("Optional description for this specific line item.")
#     )
#     created_at = models.DateTimeField(auto_now_add=True, null=True, blank=True) # Often useful
#     updated_at = models.DateTimeField(auto_now=True, null=True, blank=True)   # Often useful

#     class Meta:
#         ordering = ['journal_entry', 'id']
#         verbose_name = _("Journal Line")
#         verbose_name_plural = _("Journal Lines")

#     def __str__(self):
#         entry_id_str = self.journal_entry.entry_number if self.journal_entry and self.journal_entry.entry_number else \
#                        (str(self.journal_entry_id) if self.journal_entry_id else "N/A")
#         acc_name = self.account.name if self.account else "N/A Account"
#         if self.debit_amount > 0:
#             return f"JE Line {self.pk} for JE {entry_id_str}: {acc_name} - Debit {self.debit_amount}"
#         elif self.credit_amount > 0:
#             return f"JE Line {self.pk} for JE {entry_id_str}: {acc_name} - Credit {self.credit_amount}"
#         return f"JE Line {self.pk} for JE {entry_id_str}: {acc_name} - Zero Amounts"


#     def clean(self):
#         super().clean()
#         if self.debit_amount is None: self.debit_amount = Decimal('0.00')
#         if self.credit_amount is None: self.credit_amount = Decimal('0.00')

#         if self.debit_amount < Decimal('0.00') or self.credit_amount < Decimal('0.00'):
#             raise ValidationError(_("Debit and Credit amounts must not be negative."))
#         if self.debit_amount > Decimal('0.00') and self.credit_amount > Decimal('0.00'):
#             raise ValidationError(_("A journal line cannot have both a debit and a credit amount."))
#         # A line can have zero debit and credit if it's an empty formset row, but if it's being saved
#         # with an account, it should have an amount. This is better handled in formset validation.
#         # if self.account and self.debit_amount == Decimal('0.00') and self.credit_amount == Decimal('0.00'):
#         #     raise ValidationError(_("A journal line with an account must have a debit or credit amount."))
        
#         if self.account and not self.account.is_active: # Assuming your Account model has is_active
#             raise ValidationError({'account': _(f"Account '{self.account.name}' is inactive.")})
        
#         if not self.description and self.journal_entry: # Auto-fill line description from JE narration if empty
#             self.description = self.journal_entry.narration[:255]



class GeneralLedger(models.Model):
    class TransactionTypeChoices(models.TextChoices):
        DEBIT = 'DEBIT', _('Debit')
        CREDIT = 'CREDIT', _('Credit')
    
    transaction_date = models.DateField(db_index=True)
    account = models.ForeignKey(Account, on_delete=models.PROTECT, related_name='gl_entries')
    description = models.CharField(max_length=255)
    transaction_type = models.CharField(max_length=6, choices=TransactionTypeChoices.choices) # Max length 6 for 'CREDIT'
    amount = models.DecimalField(max_digits=12, decimal_places=2, help_text=_("Always positive. Type indicates effect."))
    journal_entry = models.ForeignKey(
        JournalEntry, on_delete=models.CASCADE, related_name='gl_entries', 
        null=True, blank=True
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['transaction_date', 'id']
        verbose_name = _("General Ledger Transaction")
        verbose_name_plural = _("General Ledger Transactions")
        indexes = [
            models.Index(fields=['transaction_date', 'account']),
            models.Index(fields=['account', 'transaction_date']),
        ]

    def __str__(self):
        return f"{self.transaction_date} - {self.account.name} - {self.get_transaction_type_display()} - {self.amount}"

    def clean(self):
        super().clean()
        if self.amount is not None and self.amount <= Decimal('0.00'):
            raise ValidationError({'amount': _("Amount for a GL transaction must be positive.")})
        














