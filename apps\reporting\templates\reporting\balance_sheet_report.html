{% extends "tenant_base.html" %}
{% load humanize static core_tags reporting_filters %}

{% block tenant_page_title %}{{ view_title }}{% endblock %}

{% block tenant_specific_content %}
<div class="container-fluid mt-3">
    {% with report_icon_class="bi bi-collection-fill" %} {# Define icon for this specific report #}
        {% include "partials/_report_header.html" %}
    {% endwith %}

{% comment %} <div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h1 class="h3 mb-0 text-gray-800">{{ view_title }}</h1>
    </div> {% endcomment %}

    {% include "partials/_messages.html" %}
    {% include "partials/_report_filter_form.html" with filter_form=filter_form report_code=report_code specific_filter_title="Expense Filters" %}

    {# --- Export Links --- #}
    <div class="mb-3">
        <a href="?{{ request.GET.urlencode }}&export=csv" class="btn btn-success btn-sm">
            <i class="bi bi-file-earmark-spreadsheet"></i> Export CSV
        </a>
        <a href="?{{ request.GET.urlencode }}&export=excel" class="btn btn-success btn-sm ms-1">
            <i class="bi bi-file-earmark-excel"></i> Export Excel
        </a>
        <a href="?{{ request.GET.urlencode }}&export=pdf" target="_blank" class="btn btn-danger btn-sm ms-1">
            <i class="bi bi-file-earmark-pdf"></i> Export PDF
        </a>
    </div>

    <div class="card shadow-sm">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                Balance Sheet as at {{ report_data.report_date|date:"d F Y" }}
            </h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h4 class="text-center mb-3">Assets</h4>

                    <h5 class="mt-3">Current Assets</h5>
                    <table class="table table-sm table-hover">
                        <tbody>
                            {% for item in report_data.assets.current %}
                            <tr>
                                <td>    {{ item.account_name }}</td>
                                <td class="text-end">{{ item.amount|floatformat:2|intcomma }}</td>
                            </tr>
                            {% empty %}
                            <tr><td colspan="2" class="text-muted fst-italic">    No current assets.</td></tr>
                            {% endfor %}
                        </tbody>
                        <tfoot class="table-light fw-bold">
                            <tr>
                                <td>Total Current Assets</td>
                                <td class="text-end">{{ report_data.total_current_assets|floatformat:2|intcomma }}</td>
                            </tr>
                        </tfoot>
                    </table>

                    <h5 class="mt-4">Non-Current Assets</h5>
                    <table class="table table-sm table-hover">
                        <tbody>
                            {% for item in report_data.assets.non_current %}
                            <tr>
                                <td>    {{ item.account_name }}</td>
                                <td class="text-end">{{ item.amount|floatformat:2|intcomma }}</td>
                            </tr>
                            {% empty %}
                            <tr><td colspan="2" class="text-muted fst-italic">    No non-current assets.</td></tr>
                            {% endfor %}
                        </tbody>
                        <tfoot class="table-light fw-bold">
                            <tr>
                                <td>Total Non-Current Assets</td>
                                <td class="text-end">{{ report_data.total_non_current_assets|floatformat:2|intcomma }}</td>
                            </tr>
                        </tfoot>
                    </table>

                    <table class="table table-sm mt-3">
                        <tr class="fw-bolder fs-5 table-secondary">
                            <td>TOTAL ASSETS</td>
                            <td class="text-end">{{ report_data.total_assets|floatformat:2|intcomma }}</td>
                        </tr>
                    </table>
                </div>

                <div class="col-md-6">
                    <h4 class="text-center mb-3">Liabilities & Equity</h4>

                    <h5 class="mt-3">Current Liabilities</h5>
                    <table class="table table-sm table-hover">
                        <tbody>
                            {% for item in report_data.liabilities.current %}
                            <tr>
                                <td>    {{ item.account_name }}</td>
                                <td class="text-end">{{ item.amount|floatformat:2|intcomma }}</td>
                            </tr>
                            {% empty %}
                            <tr><td colspan="2" class="text-muted fst-italic">    No current liabilities.</td></tr>
                            {% endfor %}
                        </tbody>
                        <tfoot class="table-light fw-bold">
                            <tr>
                                <td>Total Current Liabilities</td>
                                <td class="text-end">{{ report_data.total_current_liabilities|floatformat:2|intcomma }}</td>
                            </tr>
                        </tfoot>
                    </table>

                    <h5 class="mt-4">Non-Current Liabilities</h5>
                    <table class="table table-sm table-hover">
                        <tbody>
                            {% for item in report_data.liabilities.non_current %}
                            <tr>
                                <td>    {{ item.account_name }}</td>
                                <td class="text-end">{{ item.amount|floatformat:2|intcomma }}</td>
                            </tr>
                            {% empty %}
                            <tr><td colspan="2" class="text-muted fst-italic">    No non-current liabilities.</td></tr>
                            {% endfor %}
                        </tbody>
                        <tfoot class="table-light fw-bold">
                            <tr>
                                <td>Total Non-Current Liabilities</td>
                                <td class="text-end">{{ report_data.total_non_current_liabilities|floatformat:2|intcomma }}</td>
                            </tr>
                        </tfoot>
                    </table>
                    <table class="table table-sm mt-1">
                        <tr class="fw-bold table-light">
                            <td>TOTAL LIABILITIES</td>
                            <td class="text-end">{{ report_data.total_liabilities|floatformat:2|intcomma }}</td>
                        </tr>
                    </table>


                    <h5 class="mt-4">Equity</h5>
                    <table class="table table-sm table-hover">
                        <tbody>
                            {% for item in report_data.equity %}
                            <tr>
                                <td>    {{ item.account_name }}</td>
                                <td class="text-end">{{ item.amount|floatformat:2|intcomma }}</td>
                            </tr>
                            {% empty %}
                            <tr><td colspan="2" class="text-muted fst-italic">    No equity items.</td></tr>
                            {% endfor %}
                            {# Add Retained Earnings if calculated and available in report_data #}
                            {% if report_data.retained_earnings %}
                            <tr>
                                <td>    Retained Earnings</td>
                                <td class="text-end">{{ report_data.retained_earnings|floatformat:2|intcomma }}</td>
                            </tr>
                            {% endif %}
                        </tbody>
                        <tfoot class="table-light fw-bold">
                            <tr>
                                <td>Total Equity</td>
                                <td class="text-end">{{ report_data.total_equity|floatformat:2|intcomma }}</td>
                            </tr>
                        </tfoot>
                    </table>

                    <table class="table table-sm mt-3">
                        <tr class="fw-bolder fs-5 table-secondary">
                            <td>TOTAL LIABILITIES & EQUITY</td>
                            <td class="text-end">{{ report_data.total_liabilities_and_equity|floatformat:2|intcomma }}</td>
                        </tr>
                    </table>
                </div>
            </div>

            {% if not report_data.balances_check %}
            <div class="alert alert-danger mt-4 text-center" role="alert">
                <strong>Warning!</strong> The Balance Sheet does not balance. Total Assets ({{ report_data.total_assets|floatformat:2|intcomma }}) do not equal Total Liabilities & Equity ({{ report_data.total_liabilities_and_equity|floatformat:2|intcomma }}).
                Difference: {{ report_data.total_assets|sub:report_data.total_liabilities_and_equity|floatformat:2|intcomma }}
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}




{% comment %} {% extends "tenant_base.html" %}
{% load static core_tags humanize %}

{% block title %}{{ view_title|default:"Balance Sheet" }}{% endblock %}

{% block content %}
<div class="container mt-4">
    <h1>{{ view_title|default:"Balance Sheet" }}</h1>
    <p class="lead">Report content will be displayed here.</p>
    {# Add filter form and table later #}
    <hr>
    <a href="{% url 'schools:dashboard' %}" class="btn btn-secondary">Back to Dashboard</a>
</div>
{% endblock %} {% endcomment %}