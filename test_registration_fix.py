#!/usr/bin/env python
"""
Quick Registration Test Script
Tests that registration will work after fixes

Usage: python test_registration_fix.py
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from django.db import connection
from django_tenants.utils import schema_context
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_models_import():
    """Test that all required models can be imported"""
    logger.info("=== TESTING MODEL IMPORTS ===")
    
    try:
        from apps.subscriptions.models import Subscription, SubscriptionPlan
        from apps.tenants.models import School
        from apps.announcements.models import Announcement
        from apps.schools.models import StaffUser, AcademicYear
        logger.info("✅ All models imported successfully")
        return True
    except Exception as e:
        logger.error(f"❌ Model import failed: {e}")
        return False

def test_subscription_fields():
    """Test subscription model has correct fields"""
    logger.info("=== TESTING SUBSCRIPTION MODEL ===")
    
    try:
        from apps.subscriptions.models import Subscription
        
        # Test creating a subscription dict (without saving)
        test_data = {
            'school': None,  # Would be a School instance
            'plan': None,    # Would be a SubscriptionPlan instance
            'status': Subscription.Status.TRIALING,
            'billing_cycle': Subscription.BillingCycle.MONTHLY,
            'price_at_subscription': 0.00,
        }
        
        # Check that all fields exist
        for field_name in test_data.keys():
            if not hasattr(Subscription, field_name):
                logger.error(f"❌ Subscription missing field: {field_name}")
                return False
        
        logger.info("✅ Subscription model has all required fields")
        return True
        
    except Exception as e:
        logger.error(f"❌ Subscription model test failed: {e}")
        return False

def test_announcements_table():
    """Test announcements table exists"""
    logger.info("=== TESTING ANNOUNCEMENTS TABLE ===")
    
    try:
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_name = 'announcements_announcement'
                );
            """)
            exists = cursor.fetchone()[0]
        
        if exists:
            logger.info("✅ Announcements table exists")
            return True
        else:
            logger.error("❌ Announcements table does not exist")
            return False
            
    except Exception as e:
        logger.error(f"❌ Announcements table test failed: {e}")
        return False

def test_schema_context():
    """Test schema context switching works"""
    logger.info("=== TESTING SCHEMA CONTEXT ===")
    
    try:
        # Test switching to public schema
        with schema_context('public'):
            logger.info("✅ Can switch to public schema")
        
        # Test accessing existing tenants
        from apps.tenants.models import School
        tenants = School.objects.all()
        
        for tenant in tenants[:1]:  # Test first tenant only
            with schema_context(tenant.schema_name):
                logger.info(f"✅ Can switch to tenant schema: {tenant.schema_name}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Schema context test failed: {e}")
        return False

def test_trial_plan_exists():
    """Test that trial plan exists"""
    logger.info("=== TESTING TRIAL PLAN ===")
    
    try:
        from apps.subscriptions.models import SubscriptionPlan
        
        trial_plans = SubscriptionPlan.objects.filter(
            name__icontains='trial'
        )
        
        if trial_plans.exists():
            plan = trial_plans.first()
            logger.info(f"✅ Trial plan exists: {plan.name}")
            return True
        else:
            logger.warning("⚠️ No trial plan found - registration may fail")
            return False
            
    except Exception as e:
        logger.error(f"❌ Trial plan test failed: {e}")
        return False

def run_all_tests():
    """Run all registration tests"""
    logger.info("=== RUNNING REGISTRATION READINESS TESTS ===")
    
    tests = [
        ("Model Imports", test_models_import),
        ("Subscription Fields", test_subscription_fields),
        ("Announcements Table", test_announcements_table),
        ("Schema Context", test_schema_context),
        ("Trial Plan", test_trial_plan_exists),
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        logger.info(f"\n--- {test_name} ---")
        try:
            if test_func():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            logger.error(f"❌ {test_name} ERROR: {e}")
            failed += 1
    
    logger.info(f"\n=== TEST SUMMARY ===")
    logger.info(f"✅ Passed: {passed}")
    logger.info(f"❌ Failed: {failed}")
    
    if failed == 0:
        logger.info("🎉 ALL TESTS PASSED! Registration should work.")
        return True
    else:
        logger.error("💥 SOME TESTS FAILED. Fix issues before testing registration.")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
