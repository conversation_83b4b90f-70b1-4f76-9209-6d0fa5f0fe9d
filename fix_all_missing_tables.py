#!/usr/bin/env python
"""
Fix All Missing Tables in Existing Tenants
Creates all missing tables that the application expects

Usage: python fix_all_missing_tables.py
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from django.db import connection
from django.core.management import call_command
from django_tenants.utils import schema_context
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def get_all_tenant_schemas():
    """Get list of all tenant schemas"""
    with connection.cursor() as cursor:
        cursor.execute("""
            SELECT schema_name FROM information_schema.schemata 
            WHERE schema_name NOT IN ('public', 'information_schema') 
            AND schema_name NOT LIKE 'pg_%'
            ORDER BY schema_name
        """)
        return [row[0] for row in cursor.fetchall()]

def get_missing_tables(schema_name):
    """Get list of missing tables in a schema"""
    expected_tables = [
        'announcements_announcement',
        'schools_academicyear',
        'schools_class',
        'schools_subject',
        'schools_staffuser',
        'schools_staffuser_groups',
        'schools_staffuser_user_permissions',
        'students_student',
        'students_parentuser',
        'students_parentuser_groups',
        'students_parentuser_user_permissions',
        'hr_employeeprofile',
        'hr_leavetype',
        'hr_leavebalance',
        'hr_leaveapplication',
        'hr_payslip',
        'fees_feehead',
        'fees_feestructure',
        'fees_studentfee',
        'finance_account',
        'finance_transaction',
        'payments_payment',
        'payments_paymentmethod',
        'auth_group',
        'auth_permission',
        'django_content_type',
        'django_migrations',
        'django_session',
    ]
    
    with connection.cursor() as cursor:
        cursor.execute(f'SET search_path TO "{schema_name}"')
        
        # Get existing tables
        cursor.execute("""
            SELECT table_name FROM information_schema.tables 
            WHERE table_schema = %s
        """, [schema_name])
        existing_tables = [row[0] for row in cursor.fetchall()]
        
        # Find missing tables
        missing_tables = [table for table in expected_tables if table not in existing_tables]
        
        return missing_tables, existing_tables

def create_all_missing_tables(schema_name):
    """Create all missing tables in a schema using Django migrations"""
    logger.info(f"Creating missing tables for schema: {schema_name}")
    
    try:
        # Use tenant_command to run migrations for this specific schema
        apps_to_migrate = [
            'contenttypes',
            'auth', 
            'sessions',
            'schools',
            'students', 
            'announcements',
            'hr',
            'fees',
            'finance', 
            'payments',
            'portal_admin',
            'parent_portal',
            'school_calendar',
            'reporting'
        ]
        
        for app in apps_to_migrate:
            try:
                logger.info(f"  Migrating {app} for {schema_name}...")
                call_command(
                    'tenant_command',
                    'migrate',
                    app,
                    schema=schema_name,
                    verbosity=0
                )
                logger.info(f"  ✅ {app} migrated successfully")
            except Exception as e:
                logger.warning(f"  ⚠️ {app} migration failed: {e}")
                # Continue with other apps
        
        logger.info(f"✅ Migration completed for {schema_name}")
        
    except Exception as e:
        logger.error(f"Failed to migrate {schema_name}: {e}")
        raise

def create_essential_tables_directly(schema_name):
    """Create essential tables directly with SQL as fallback"""
    logger.info(f"Creating essential tables directly for: {schema_name}")
    
    with connection.cursor() as cursor:
        cursor.execute(f'SET search_path TO "{schema_name}"')
        
        # Create essential tables that are commonly missing
        tables_sql = [
            # Django content types
            """
            CREATE TABLE IF NOT EXISTS django_content_type (
                id SERIAL PRIMARY KEY,
                app_label VARCHAR(100) NOT NULL,
                model VARCHAR(100) NOT NULL,
                UNIQUE(app_label, model)
            );
            """,
            
            # Auth permissions
            """
            CREATE TABLE IF NOT EXISTS auth_permission (
                id SERIAL PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                content_type_id INTEGER NOT NULL,
                codename VARCHAR(100) NOT NULL,
                UNIQUE(content_type_id, codename)
            );
            """,
            
            # Schools Academic Year
            """
            CREATE TABLE IF NOT EXISTS schools_academicyear (
                id BIGSERIAL PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                start_date DATE NOT NULL,
                end_date DATE NOT NULL,
                is_current BOOLEAN NOT NULL DEFAULT FALSE,
                created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
            );
            """,
            
            # Schools Class
            """
            CREATE TABLE IF NOT EXISTS schools_class (
                id BIGSERIAL PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                description TEXT,
                academic_year_id BIGINT,
                created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
            );
            """,
            
            # Announcements
            """
            CREATE TABLE IF NOT EXISTS announcements_announcement (
                id BIGSERIAL PRIMARY KEY,
                title VARCHAR(255) NOT NULL,
                content TEXT NOT NULL,
                target_all_tenant_staff BOOLEAN NOT NULL DEFAULT FALSE,
                target_all_tenant_parents BOOLEAN NOT NULL DEFAULT FALSE,
                is_global BOOLEAN NOT NULL DEFAULT FALSE,
                target_global_audience_type VARCHAR(50),
                publish_date TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
                expiry_date TIMESTAMP WITH TIME ZONE,
                is_published BOOLEAN NOT NULL DEFAULT TRUE,
                is_sticky BOOLEAN NOT NULL DEFAULT FALSE,
                created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
                author_id BIGINT,
                tenant_id BIGINT
            );
            """,
            
            # Students
            """
            CREATE TABLE IF NOT EXISTS students_student (
                id BIGSERIAL PRIMARY KEY,
                student_id VARCHAR(50) UNIQUE,
                first_name VARCHAR(150) NOT NULL,
                last_name VARCHAR(150) NOT NULL,
                date_of_birth DATE,
                gender VARCHAR(10),
                class_id BIGINT,
                is_active BOOLEAN NOT NULL DEFAULT TRUE,
                created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
            );
            """,
            
            # Parent User
            """
            CREATE TABLE IF NOT EXISTS students_parentuser (
                id BIGSERIAL PRIMARY KEY,
                password VARCHAR(128),
                last_login TIMESTAMP WITH TIME ZONE,
                is_superuser BOOLEAN NOT NULL DEFAULT FALSE,
                email VARCHAR(254) UNIQUE NOT NULL,
                first_name VARCHAR(150) NOT NULL,
                last_name VARCHAR(150) NOT NULL,
                phone VARCHAR(20),
                is_active BOOLEAN NOT NULL DEFAULT TRUE,
                is_staff BOOLEAN NOT NULL DEFAULT FALSE,
                date_joined TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
            );
            """,
            
            # Parent User Groups
            """
            CREATE TABLE IF NOT EXISTS students_parentuser_groups (
                id BIGSERIAL PRIMARY KEY,
                parentuser_id BIGINT NOT NULL,
                group_id INTEGER NOT NULL,
                UNIQUE(parentuser_id, group_id)
            );
            """,
        ]
        
        for sql in tables_sql:
            try:
                cursor.execute(sql)
            except Exception as e:
                logger.warning(f"Failed to create table: {e}")
        
        logger.info(f"✅ Essential tables created for {schema_name}")

def fix_all_tenant_schemas():
    """Fix all tenant schemas"""
    logger.info("=== FIXING ALL TENANT SCHEMAS ===")
    
    schemas = get_all_tenant_schemas()
    logger.info(f"Found {len(schemas)} tenant schemas: {schemas}")
    
    for schema in schemas:
        logger.info(f"\n--- Fixing schema: {schema} ---")
        
        try:
            # Get missing tables
            missing_tables, existing_tables = get_missing_tables(schema)
            logger.info(f"Existing tables: {len(existing_tables)}")
            logger.info(f"Missing tables: {len(missing_tables)}")
            
            if missing_tables:
                logger.info(f"Missing: {missing_tables}")
                
                # Try Django migrations first
                try:
                    create_all_missing_tables(schema)
                except Exception as e:
                    logger.warning(f"Django migrations failed for {schema}: {e}")
                    logger.info("Falling back to direct SQL creation...")
                    create_essential_tables_directly(schema)
            else:
                logger.info(f"✅ All tables exist in {schema}")
                
        except Exception as e:
            logger.error(f"❌ Failed to fix {schema}: {e}")

def test_table_access():
    """Test that key tables can be accessed"""
    logger.info("=== TESTING TABLE ACCESS ===")
    
    schemas = get_all_tenant_schemas()
    key_tables = ['schools_academicyear', 'announcements_announcement', 'schools_staffuser']
    
    for schema in schemas:
        logger.info(f"Testing {schema}...")
        
        with connection.cursor() as cursor:
            cursor.execute(f'SET search_path TO "{schema}"')
            
            for table in key_tables:
                try:
                    cursor.execute(f"SELECT COUNT(*) FROM {table};")
                    count = cursor.fetchone()[0]
                    logger.info(f"  ✅ {table}: accessible (count: {count})")
                except Exception as e:
                    logger.error(f"  ❌ {table}: not accessible: {e}")

def main():
    """Main function"""
    logger.info("=== FIXING ALL MISSING TABLES ===")
    
    try:
        # Fix all schemas
        fix_all_tenant_schemas()
        
        # Test access
        test_table_access()
        
        logger.info("\n🎉 ALL TENANT SCHEMAS FIXED!")
        logger.info("Missing table errors should now be resolved.")
        
        return True
        
    except Exception as e:
        logger.error(f"Fix failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
