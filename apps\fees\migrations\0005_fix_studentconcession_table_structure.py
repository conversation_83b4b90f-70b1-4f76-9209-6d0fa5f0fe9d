# Generated manually to fix StudentConcession table structure with duplicate columns

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('fees', '0004_fix_studentconcession_term_field'),
        ('schools', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        # Simply drop the corrupted table - data will be lost but table was corrupted anyway
        migrations.RunSQL(
            sql=["DROP TABLE IF EXISTS fees_studentconcession CASCADE;"],
            reverse_sql=["-- Cannot reverse table drop"]
        ),

        
        # Recreate the table with proper structure
        migrations.CreateModel(
            name='StudentConcession',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('notes', models.TextField(blank=True, null=True)),
                ('granted_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('academic_year', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='schools.academicyear')),
                ('concession_type', models.ForeignKey(limit_choices_to={'is_active': True}, on_delete=django.db.models.deletion.PROTECT, to='fees.concessiontype')),
                ('granted_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='granted_concessions', to=settings.AUTH_USER_MODEL)),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='applied_concessions', to='students.student')),
                ('term', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='schools.term')),
            ],
            options={
                'verbose_name': 'Student Specific Concession',
                'verbose_name_plural': 'Student Specific Concessions',
                'ordering': ['student__last_name', '-academic_year__start_date', 'concession_type__name'],
            },
        ),
        
        # Add unique constraint
        migrations.AddConstraint(
            model_name='studentconcession',
            constraint=models.UniqueConstraint(fields=['student', 'concession_type', 'academic_year', 'term'], name='unique_student_concession'),
        ),
        

    ]
