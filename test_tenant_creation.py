#!/usr/bin/env python
"""
Test Tenant Creation Script
Tests that tenant creation works after fixes

Usage: python test_tenant_creation.py
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from django.db import connection, transaction
from django_tenants.utils import schema_context
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_schema_creation():
    """Test creating a test schema"""
    test_schema = 'test_tenant_creation'
    
    logger.info(f"Testing schema creation: {test_schema}")
    
    try:
        with connection.cursor() as cursor:
            # Drop test schema if exists
            cursor.execute(f'DROP SCHEMA IF EXISTS "{test_schema}" CASCADE;')
            
            # Create test schema
            cursor.execute(f'CREATE SCHEMA "{test_schema}";')
            
            # Set search path
            cursor.execute(f'SET search_path TO "{test_schema}";')
            
            # Create announcements table
            cursor.execute("""
                CREATE TABLE announcements_announcement (
                    id BIGSERIAL PRIMARY KEY,
                    title VARCHAR(255) NOT NULL,
                    content TEXT NOT NULL,
                    target_all_tenant_staff BOOLEAN NOT NULL DEFAULT FALSE,
                    target_all_tenant_parents BOOLEAN NOT NULL DEFAULT FALSE,
                    is_global BOOLEAN NOT NULL DEFAULT FALSE,
                    target_global_audience_type VARCHAR(50),
                    publish_date TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
                    expiry_date TIMESTAMP WITH TIME ZONE,
                    is_published BOOLEAN NOT NULL DEFAULT TRUE,
                    is_sticky BOOLEAN NOT NULL DEFAULT FALSE,
                    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
                    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
                    author_id BIGINT,
                    tenant_id BIGINT
                );
            """)
            
            # Test inserting data
            cursor.execute("""
                INSERT INTO announcements_announcement 
                (title, content) VALUES ('Test', 'Test content');
            """)
            
            # Test querying data
            cursor.execute("SELECT COUNT(*) FROM announcements_announcement;")
            count = cursor.fetchone()[0]
            
            if count == 1:
                logger.info("✅ Schema creation and table operations work")
                
                # Clean up
                cursor.execute(f'DROP SCHEMA "{test_schema}" CASCADE;')
                return True
            else:
                logger.error("❌ Data insertion failed")
                return False
                
    except Exception as e:
        logger.error(f"Schema creation test failed: {e}")
        return False

def test_tenant_model_creation():
    """Test creating a tenant through the model"""
    logger.info("Testing tenant model creation...")
    
    try:
        from apps.tenants.models import School
        from django.contrib.auth import get_user_model
        
        User = get_user_model()
        
        # Create a test user if needed
        test_user, created = User.objects.get_or_create(
            email='<EMAIL>',
            defaults={
                'first_name': 'Test',
                'last_name': 'User',
                'is_active': True
            }
        )
        
        # Test creating a school (without actually saving)
        test_school = School(
            name='Test School Creation',
            owner=test_user,
            schema_name='test_creation_schema'
        )
        
        # Validate the model
        test_school.full_clean()
        
        logger.info("✅ Tenant model validation passed")
        return True
        
    except Exception as e:
        logger.error(f"Tenant model test failed: {e}")
        return False

def test_migration_state():
    """Test migration state"""
    logger.info("Testing migration state...")
    
    try:
        with connection.cursor() as cursor:
            # Check announcements migrations in public schema
            cursor.execute("""
                SELECT COUNT(*) FROM django_migrations 
                WHERE app = 'announcements'
            """)
            
            ann_migrations = cursor.fetchone()[0]
            
            if ann_migrations > 0:
                logger.info(f"✅ Found {ann_migrations} announcements migrations")
                return True
            else:
                logger.error("❌ No announcements migrations found")
                return False
                
    except Exception as e:
        logger.error(f"Migration state test failed: {e}")
        return False

def run_all_tests():
    """Run all tenant creation tests"""
    logger.info("=== RUNNING TENANT CREATION TESTS ===")
    
    tests = [
        ("Schema Creation", test_schema_creation),
        ("Tenant Model Creation", test_tenant_model_creation),
        ("Migration State", test_migration_state),
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        logger.info(f"\n--- {test_name} ---")
        try:
            if test_func():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            logger.error(f"❌ {test_name} ERROR: {e}")
            failed += 1
    
    logger.info(f"\n=== TEST SUMMARY ===")
    logger.info(f"✅ Passed: {passed}")
    logger.info(f"❌ Failed: {failed}")
    
    if failed == 0:
        logger.info("🎉 ALL TESTS PASSED! Tenant creation should work.")
        return True
    else:
        logger.error("💥 SOME TESTS FAILED. Fix issues before testing registration.")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
