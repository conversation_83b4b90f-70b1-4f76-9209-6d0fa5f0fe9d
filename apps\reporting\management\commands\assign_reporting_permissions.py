#!/usr/bin/env python3
"""
Management command to assign missing reporting permissions to School Administrators group.
This ensures that all reporting permissions are properly assigned to the admin group.
"""

from django.core.management.base import BaseCommand, CommandError
from django.contrib.auth.models import Group, Permission
from django.contrib.contenttypes.models import ContentType
from django.db import transaction
from django_tenants.utils import schema_context, get_tenant_model
import logging

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Assign missing reporting permissions to School Administrators group across all tenants'

    def add_arguments(self, parser):
        parser.add_argument(
            '--tenant',
            type=str,
            help='Specific tenant schema name to update (optional, updates all if not specified)',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be done without making changes',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        specific_tenant = options['tenant']
        
        if dry_run:
            self.stdout.write(self.style.WARNING("DRY RUN MODE - No changes will be made"))
        
        # Get all tenants or specific tenant
        Tenant = get_tenant_model()
        if specific_tenant:
            tenants = Tenant.objects.filter(schema_name=specific_tenant)
            if not tenants.exists():
                raise CommandError(f"Tenant '{specific_tenant}' not found")
        else:
            tenants = Tenant.objects.exclude(schema_name='public')
        
        # Define the reporting permissions that should be assigned
        reporting_permissions = [
            'view_collection_report',
            'view_outstanding_fees_report',
            'view_student_ledger_report',
            'view_payment_summary_report',
            'view_general_ledger_report',  # New General Ledger Report
            'view_accounts_receivable_aging_report',  # New AR Aging Report
            'view_accounts_payable_report',  # New AP Report
            'view_bank_reconciliation_report',  # New Bank Reconciliation Report
            'view_budget_vs_actual_report',  # New Budget vs Actual Report
            'view_fee_collection_analysis_report',  # New Fee Collection Analysis Report
            'view_student_account_statement_report',  # New Student Account Statement Report
            'view_classwise_fee_collection_report',  # New Class-wise Fee Collection Report
            'view_fee_defaulters_report',  # New Fee Defaulters Report
            'view_cash_flow_forecasting_report',  # New Cash Flow Forecasting Report
            'view_profitability_analysis_report',  # New Profitability Analysis Report
            'view_financial_ratio_analysis_report',  # New Financial Ratio Analysis Report
            'view_trial_balance_report',
            'view_income_statement_report',
            'view_income_expense_report',  # For IncomeExpenseReportView
            'view_balance_sheet_report',
            'view_cash_flow_statement_report',
            'view_cash_flow_statement',  # For CashFlowStatementView
            'view_budget_variance_report',
            'view_expense_report',
            'view_fee_projection_report',
            'view_report_dashboard',  # For dashboard access
        ]
        
        total_updated = 0
        
        for tenant in tenants:
            self.stdout.write(f"\nProcessing tenant: {tenant.schema_name}")
            
            with schema_context(tenant.schema_name):
                try:
                    # Get or create School Administrators group
                    admin_group, group_created = Group.objects.get_or_create(
                        name="School Administrators"
                    )
                    
                    if group_created:
                        self.stdout.write(
                            self.style.SUCCESS(f"  Created 'School Administrators' group")
                        )
                    
                    # Get reporting content type
                    try:
                        reporting_ct = ContentType.objects.get(app_label='reporting', model='reportingpermissions')
                    except ContentType.DoesNotExist:
                        self.stdout.write(
                            self.style.ERROR(f"  Reporting content type not found - skipping tenant")
                        )
                        continue
                    
                    # Get current permissions for the group
                    current_perms = set(admin_group.permissions.filter(
                        content_type=reporting_ct
                    ).values_list('codename', flat=True))
                    
                    # Find missing permissions
                    missing_perms = []
                    for perm_codename in reporting_permissions:
                        if perm_codename not in current_perms:
                            try:
                                perm = Permission.objects.get(
                                    content_type=reporting_ct,
                                    codename=perm_codename
                                )
                                missing_perms.append(perm)
                            except Permission.DoesNotExist:
                                self.stdout.write(
                                    self.style.WARNING(f"  Permission '{perm_codename}' not found")
                                )
                    
                    if missing_perms:
                        if not dry_run:
                            with transaction.atomic():
                                admin_group.permissions.add(*missing_perms)
                        
                        self.stdout.write(
                            self.style.SUCCESS(f"  {'Would assign' if dry_run else 'Assigned'} {len(missing_perms)} missing permissions:")
                        )
                        for perm in missing_perms:
                            self.stdout.write(f"    - {perm.codename}")
                        
                        total_updated += len(missing_perms)
                    else:
                        self.stdout.write(
                            self.style.SUCCESS(f"  All reporting permissions already assigned")
                        )
                
                except Exception as e:
                    self.stdout.write(
                        self.style.ERROR(f"  Error processing tenant {tenant.schema_name}: {e}")
                    )
                    logger.exception(f"Error in assign_reporting_permissions for {tenant.schema_name}")
        
        if total_updated > 0:
            self.stdout.write(
                self.style.SUCCESS(f"\n{'Would assign' if dry_run else 'Assigned'} {total_updated} permissions across {tenants.count()} tenants")
            )
        else:
            self.stdout.write(
                self.style.SUCCESS(f"\nAll tenants already have proper reporting permissions")
            )
