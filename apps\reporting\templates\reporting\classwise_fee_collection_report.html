{% extends "tenant_base.html" %}
{% load humanize static core_tags reporting_filters %}

{% block title %}{{ view_title|default:"Class-wise Fee Collection Report" }}{% endblock %}

{% block content %}
<div class="container-fluid mt-3">
    {% with report_icon_class="bi bi-people-fill" %}
        {% include "partials/_report_header.html" %}
    {% endwith %}

    {% include "partials/_messages.html" %}
    {% include "partials/_report_filter_form.html" with filter_form=filter_form report_code=report_code specific_filter_title="Class Collection Filters" %}

    <!-- Collection Summary Card -->
    <div class="card mb-4">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="bi bi-bar-chart me-2"></i>Overall Collection Summary</h5>
        </div>
        <div class="card-body">
            {% if academic_year or term or as_of_date %}
            <div class="row mb-3">
                <div class="col-md-12">
                    <p>
                        {% if academic_year %}<strong>Academic Year:</strong> {{ academic_year }} | {% endif %}
                        {% if term %}<strong>Term:</strong> {{ term }} | {% endif %}
                        <strong>As of Date:</strong> {{ as_of_date|date:"M d, Y" }}
                    </p>
                </div>
            </div>
            {% endif %}
            
            <div class="row">
                <div class="col-md-2">
                    <div class="text-center">
                        <h6 class="text-muted">Total Classes</h6>
                        <p class="mb-0 fw-bold text-primary fs-4">{{ collection_summary.total_classes|default:0 }}</p>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="text-center">
                        <h6 class="text-muted">Total Students</h6>
                        <p class="mb-0 fw-bold text-info">{{ collection_summary.total_students|default:0 }}</p>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="text-center">
                        <h6 class="text-muted">Total Invoiced</h6>
                        <p class="mb-0 fw-bold text-warning">{{ collection_summary.total_invoiced|currency }}</p>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="text-center">
                        <h6 class="text-muted">Total Collected</h6>
                        <p class="mb-0 fw-bold text-success">{{ collection_summary.total_collected|currency }}</p>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="text-center">
                        <h6 class="text-muted">Outstanding</h6>
                        <p class="mb-0 fw-bold text-danger">{{ collection_summary.total_outstanding|currency }}</p>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="text-center">
                        <h6 class="text-muted">Collection Rate</h6>
                        <p class="mb-0 fw-bold {% if collection_summary.overall_collection_rate >= 80 %}text-success{% elif collection_summary.overall_collection_rate >= 60 %}text-warning{% else %}text-danger{% endif %}">
                            {{ collection_summary.overall_collection_rate|floatformat:1 }}%
                        </p>
                    </div>
                </div>
            </div>
            
            <!-- Overall Collection Rate Progress Bar -->
            <div class="mt-3">
                <h6>Overall Collection Performance</h6>
                <div class="progress" style="height: 25px;">
                    <div class="progress-bar {% if collection_summary.overall_collection_rate >= 80 %}bg-success{% elif collection_summary.overall_collection_rate >= 60 %}bg-warning{% else %}bg-danger{% endif %}" 
                         role="progressbar" style="width: {{ collection_summary.overall_collection_rate|floatformat:0 }}%">
                        {{ collection_summary.overall_collection_rate|floatformat:1 }}%
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Class-wise Analysis -->
    {% if class_analysis %}
    <div class="card mb-4">
        <div class="card-header bg-light d-flex justify-content-between align-items-center">
            <h5 class="mb-0"><i class="bi bi-grid me-2"></i>Class-wise Collection Analysis</h5>
            <span class="badge bg-primary">{{ class_analysis|length }} classes</span>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-striped table-hover mb-0">
                    <thead class="table-dark">
                        <tr>
                            <th>Class</th>
                            <th class="text-center">Students</th>
                            <th class="text-center">Invoices</th>
                            <th class="text-end">Total Invoiced</th>
                            <th class="text-end">Total Collected</th>
                            <th class="text-end">Outstanding</th>
                            <th class="text-center">Collection Rate</th>
                            <th class="text-end">Avg per Student</th>
                            <th class="text-center">Performance</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for class_item in class_analysis %}
                        <tr class="{% if class_item.collection_rate < 50 %}table-danger{% elif class_item.collection_rate < 75 %}table-warning{% endif %}">
                            <td>
                                <strong>{{ class_item.class_name }}</strong>
                                <br><small class="text-muted">{{ class_item.percentage_of_total|floatformat:1 }}% of total</small>
                            </td>
                            <td class="text-center">
                                <span class="badge bg-info">{{ class_item.student_count }}</span>
                            </td>
                            <td class="text-center">
                                <span class="badge bg-secondary">{{ class_item.invoice_count }}</span>
                                <br><small class="text-muted">
                                    P:{{ class_item.paid_invoices }} | 
                                    U:{{ class_item.unpaid_invoices }}
                                    {% if class_item.overdue_invoices > 0 %} | O:{{ class_item.overdue_invoices }}{% endif %}
                                </small>
                            </td>
                            <td class="text-end">
                                <span class="text-warning fw-bold">{{ class_item.total_invoiced|currency }}</span>
                            </td>
                            <td class="text-end">
                                <span class="text-success fw-bold">{{ class_item.total_collected|currency }}</span>
                            </td>
                            <td class="text-end">
                                <span class="text-danger fw-bold">{{ class_item.total_outstanding|currency }}</span>
                                {% if class_item.overdue_invoices > 0 %}
                                <br><small class="text-danger">{{ class_item.overdue_invoices }} overdue</small>
                                {% endif %}
                            </td>
                            <td class="text-center">
                                <span class="fw-bold {% if class_item.collection_rate >= 80 %}text-success{% elif class_item.collection_rate >= 60 %}text-warning{% else %}text-danger{% endif %}">
                                    {{ class_item.collection_rate|floatformat:1 }}%
                                </span>
                            </td>
                            <td class="text-end">
                                {{ class_item.avg_per_student|currency }}
                            </td>
                            <td class="text-center">
                                <div class="progress" style="height: 20px; width: 80px;">
                                    <div class="progress-bar {% if class_item.collection_rate >= 80 %}bg-success{% elif class_item.collection_rate >= 60 %}bg-warning{% else %}bg-danger{% endif %}" 
                                         role="progressbar" style="width: {{ class_item.collection_rate|floatformat:0 }}%">
                                    </div>
                                </div>
                                {% if class_item.collection_rate >= 80 %}
                                <small class="text-success">Excellent</small>
                                {% elif class_item.collection_rate >= 60 %}
                                <small class="text-warning">Good</small>
                                {% else %}
                                <small class="text-danger">Needs Attention</small>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                    <tfoot class="table-light">
                        <tr>
                            <th>Totals:</th>
                            <th class="text-center">{{ collection_summary.total_students }}</th>
                            <th class="text-center">{{ collection_summary.total_invoices }}</th>
                            <th class="text-end text-warning">{{ collection_summary.total_invoiced|currency }}</th>
                            <th class="text-end text-success">{{ collection_summary.total_collected|currency }}</th>
                            <th class="text-end text-danger">{{ collection_summary.total_outstanding|currency }}</th>
                            <th class="text-center fw-bold">{{ collection_summary.overall_collection_rate|floatformat:1 }}%</th>
                            <th class="text-end">{{ collection_summary.total_invoiced|divide:collection_summary.total_students|currency }}</th>
                            <th></th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Fee Type Analysis -->
    {% if fee_type_analysis %}
    <div class="card mb-4">
        <div class="card-header bg-light d-flex justify-content-between align-items-center">
            <h5 class="mb-0"><i class="bi bi-tags me-2"></i>Fee Type Analysis</h5>
            <span class="badge bg-success">{{ fee_type_analysis|length }} fee types</span>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-striped table-hover mb-0">
                    <thead class="table-dark">
                        <tr>
                            <th>Fee Type</th>
                            <th class="text-center">Invoices</th>
                            <th class="text-end">Total Invoiced</th>
                            <th class="text-end">Total Collected</th>
                            <th class="text-center">Collection Rate</th>
                            <th class="text-center">Performance</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for fee_item in fee_type_analysis %}
                        <tr>
                            <td><strong>{{ fee_item.fee_type }}</strong></td>
                            <td class="text-center">
                                <span class="badge bg-info">{{ fee_item.invoice_count }}</span>
                            </td>
                            <td class="text-end">
                                <span class="text-warning fw-bold">{{ fee_item.total_invoiced|currency }}</span>
                            </td>
                            <td class="text-end">
                                <span class="text-success fw-bold">{{ fee_item.total_collected|currency }}</span>
                            </td>
                            <td class="text-center">
                                <span class="fw-bold {% if fee_item.collection_rate >= 80 %}text-success{% elif fee_item.collection_rate >= 60 %}text-warning{% else %}text-danger{% endif %}">
                                    {{ fee_item.collection_rate|floatformat:1 }}%
                                </span>
                            </td>
                            <td class="text-center">
                                <div class="progress" style="height: 20px; width: 80px;">
                                    <div class="progress-bar {% if fee_item.collection_rate >= 80 %}bg-success{% elif fee_item.collection_rate >= 60 %}bg-warning{% else %}bg-danger{% endif %}" 
                                         role="progressbar" style="width: {{ fee_item.collection_rate|floatformat:0 }}%">
                                    </div>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Performance Insights -->
    {% if class_analysis %}
    <div class="card">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="bi bi-lightbulb me-2"></i>Performance Insights & Recommendations</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h6>Top Performing Classes</h6>
                    <ul class="list-unstyled">
                        {% for class_item in class_analysis|slice:":3" %}
                        {% if class_item.collection_rate >= 75 %}
                        <li><i class="bi bi-trophy text-success me-2"></i>
                            <strong>{{ class_item.class_name }}</strong> - {{ class_item.collection_rate|floatformat:1 }}% collection rate
                        </li>
                        {% endif %}
                        {% endfor %}
                    </ul>
                    
                    <h6 class="mt-3">Key Metrics</h6>
                    <ul class="list-unstyled">
                        <li><i class="bi bi-check-circle text-success me-2"></i>
                            Average collection rate: {{ collection_summary.overall_collection_rate|floatformat:1 }}%
                        </li>
                        <li><i class="bi bi-cash me-2"></i>
                            Total outstanding: {{ collection_summary.total_outstanding|currency }}
                        </li>
                        <li><i class="bi bi-people me-2"></i>
                            {{ collection_summary.total_students }} students across {{ collection_summary.total_classes }} classes
                        </li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6>Classes Needing Attention</h6>
                    <ul class="list-unstyled">
                        {% for class_item in class_analysis %}
                        {% if class_item.collection_rate < 60 %}
                        <li><i class="bi bi-exclamation-triangle text-warning me-2"></i>
                            <strong>{{ class_item.class_name }}</strong> - {{ class_item.collection_rate|floatformat:1 }}% collection rate
                            {% if class_item.overdue_invoices > 0 %}({{ class_item.overdue_invoices }} overdue){% endif %}
                        </li>
                        {% endif %}
                        {% endfor %}
                    </ul>
                    
                    <h6 class="mt-3">Recommendations</h6>
                    <ul class="list-unstyled">
                        {% if collection_summary.overall_collection_rate < 75 %}
                        <li><i class="bi bi-arrow-right text-primary me-2"></i>Focus on classes with collection rates below 60%</li>
                        <li><i class="bi bi-arrow-right text-primary me-2"></i>Implement targeted follow-up for overdue invoices</li>
                        {% endif %}
                        <li><i class="bi bi-arrow-right text-primary me-2"></i>Consider payment plans for struggling classes</li>
                        <li><i class="bi bi-arrow-right text-primary me-2"></i>Share best practices from top-performing classes</li>
                        <li><i class="bi bi-arrow-right text-primary me-2"></i>Set up automated reminders for outstanding payments</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- No Data Message -->
    {% if not class_analysis %}
    <div class="card">
        <div class="card-body text-center py-5">
            <i class="bi bi-people-fill display-1 text-muted mb-3"></i>
            <h4 class="text-muted">No Collection Data Found</h4>
            <p class="text-muted">No fee collection data found for the selected criteria.</p>
            <div class="mt-3">
                <a href="{% url 'reporting:classwise_fee_collection_report' %}" class="btn btn-outline-primary me-2">
                    <i class="bi bi-arrow-clockwise me-2"></i>Reset Filters
                </a>
            </div>
        </div>
    </div>
    {% endif %}

</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Animate progress bars
    $('.progress-bar').each(function() {
        var $this = $(this);
        var width = $this.css('width');
        $this.css('width', '0%').animate({width: width}, 1000);
    });
    
    // Highlight poor performing classes
    $('.table-danger, .table-warning').hover(
        function() {
            $(this).addClass('table-active');
        },
        function() {
            $(this).removeClass('table-active');
        }
    );
    
    // Add click handlers for class rows
    $('tbody tr').on('click', function() {
        $(this).toggleClass('table-info');
    });
});
</script>
{% endblock %}

