"""
Create Tenant <PERSON><PERSON><PERSON><PERSON> creates a new tenant with complete structure and data

Usage: 
python manage.py create_tenant_properly --name="School Name" --schema="schema_name" --domain="domain.com"
"""

from django.core.management.base import BaseCommand
from django.db import connection
from django.core.management import call_command
from apps.tenants.models import School
from django_tenants.utils import schema_context
import json
import os
import logging

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Create a new tenant properly with complete structure and data'

    def add_arguments(self, parser):
        parser.add_argument(
            '--name',
            type=str,
            required=True,
            help='School name',
        )
        parser.add_argument(
            '--schema',
            type=str,
            required=True,
            help='Schema name (must be unique)',
        )
        parser.add_argument(
            '--domain',
            type=str,
            required=True,
            help='Domain name',
        )
        parser.add_argument(
            '--owner-email',
            type=str,
            help='Owner email address',
        )

    def handle(self, *args, **options):
        school_name = options['name']
        schema_name = options['schema']
        domain_name = options['domain']
        owner_email = options.get('owner_email')
        
        self.stdout.write(f"=== CREATING TENANT: {school_name} ===")
        self.stdout.write(f"Schema: {schema_name}")
        self.stdout.write(f"Domain: {domain_name}")
        
        try:
            # 1. Create the tenant record
            self.stdout.write("1. Creating tenant record...")
            
            # Check if tenant already exists
            if School.objects.filter(schema_name=schema_name).exists():
                self.stdout.write(
                    self.style.ERROR(f"Tenant with schema '{schema_name}' already exists")
                )
                return

            # Get or create owner
            from django.contrib.auth import get_user_model
            User = get_user_model()

            if owner_email:
                owner, created = User.objects.get_or_create(
                    email=owner_email,
                    defaults={
                        'first_name': 'School',
                        'last_name': 'Administrator',
                        'is_active': True
                    }
                )
                if created:
                    self.stdout.write(f"Created owner user: {owner_email}")
            else:
                # Get first superuser as default owner
                owner = User.objects.filter(is_superuser=True).first()
                if not owner:
                    self.stdout.write(
                        self.style.ERROR("No owner email provided and no superuser found")
                    )
                    return

            # Create tenant
            tenant = School.objects.create(
                schema_name=schema_name,
                name=school_name,
                owner=owner,
                is_active=True
            )
            
            # Create domain
            from apps.tenants.models import Domain
            Domain.objects.create(
                domain=domain_name,
                tenant=tenant,
                is_primary=True
            )
            
            self.stdout.write(
                self.style.SUCCESS(f"✅ Created tenant: {tenant.name}")
            )
            
            # 2. Run migrations to create all tables
            self.stdout.write("2. Running migrations...")
            
            with schema_context(schema_name):
                call_command('migrate', verbosity=1, interactive=False)
            
            self.stdout.write(
                self.style.SUCCESS("✅ Migrations completed")
            )
            
            # 3. Apply essential data
            self.stdout.write("3. Applying essential data...")
            
            self.apply_essential_data(schema_name)
            
            self.stdout.write(
                self.style.SUCCESS("✅ Essential data applied")
            )
            
            # 4. Create owner staff user if email provided
            if owner_email:
                self.stdout.write("4. Creating owner staff user...")
                self.create_owner_staff_user(schema_name, owner_email, school_name)
                self.stdout.write(
                    self.style.SUCCESS("✅ Owner staff user created")
                )
            
            # 5. Verify tenant is working
            self.stdout.write("5. Verifying tenant...")
            
            if self.verify_tenant(schema_name):
                self.stdout.write(
                    self.style.SUCCESS("✅ Tenant verification passed")
                )
            else:
                self.stdout.write(
                    self.style.WARNING("⚠️  Tenant verification had issues")
                )
            
            # Success message
            self.stdout.write(
                self.style.SUCCESS(f"\n🎉 TENANT CREATED SUCCESSFULLY!")
            )
            self.stdout.write(f"School: {school_name}")
            self.stdout.write(f"Schema: {schema_name}")
            self.stdout.write(f"Domain: {domain_name}")
            self.stdout.write(f"URL: http://{domain_name}:8000/")
            
            if owner_email:
                self.stdout.write(f"Owner: {owner_email}")
                self.stdout.write("The owner can now log in to the tenant portal.")
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"❌ Failed to create tenant: {e}")
            )
            
            # Cleanup on failure
            try:
                if School.objects.filter(schema_name=schema_name).exists():
                    School.objects.filter(schema_name=schema_name).delete()
                    self.stdout.write("Cleaned up failed tenant creation")
            except:
                pass

    def apply_essential_data(self, schema_name):
        """Apply essential data to the tenant"""
        
        try:
            with connection.cursor() as cursor:
                cursor.execute(f'SET search_path TO "{schema_name}"')
                
                # 1. Create academic year
                cursor.execute("SELECT COUNT(*) FROM schools_academicyear")
                if cursor.fetchone()[0] == 0:
                    cursor.execute("""
                        INSERT INTO schools_academicyear (name, start_date, end_date, is_active, is_current, created_at, updated_at) 
                        VALUES ('2024-2025', '2024-09-01', '2025-08-31', TRUE, TRUE, NOW(), NOW())
                    """)
                    self.stdout.write("    ✅ Created academic year")
                
                # 2. Create academic settings
                cursor.execute("SELECT COUNT(*) FROM schools_academicsetting")
                if cursor.fetchone()[0] == 0:
                    cursor.execute("""
                        INSERT INTO schools_academicsetting (academic_year_id, created_at, updated_at) 
                        VALUES (1, NOW(), NOW())
                    """)
                    self.stdout.write("    ✅ Created academic settings")
                
                # 3. Create sequences
                for seq_table, prefix in [('schools_invoicesequence', 'INV'), ('schools_receiptsequence', 'RCP')]:
                    cursor.execute(f"SELECT COUNT(*) FROM {seq_table}")
                    if cursor.fetchone()[0] == 0:
                        cursor.execute(f"""
                            INSERT INTO {seq_table} (prefix, current_number, created_at, updated_at) 
                            VALUES ('{prefix}', 1, NOW(), NOW())
                        """)
                        self.stdout.write(f"    ✅ Created {seq_table}")
                
                # 4. Create essential reference data
                self.create_reference_data(cursor)
                
        except Exception as e:
            self.stdout.write(f"    ⚠️  Error applying essential data: {e}")

    def create_reference_data(self, cursor):
        """Create essential reference data"""
        
        # Leave types
        cursor.execute("SELECT COUNT(*) FROM hr_leavetype")
        if cursor.fetchone()[0] == 0:
            cursor.execute("""
                INSERT INTO hr_leavetype (name, description, max_annual_days, is_paid, requires_approval, is_active, accrual_frequency, accrual_rate, prorate_accrual, created_at, updated_at) VALUES
                ('Annual Leave', 'Annual vacation leave', 21, TRUE, TRUE, TRUE, 'YEARLY', 1.75, FALSE, NOW(), NOW()),
                ('Sick Leave', 'Medical leave', 10, TRUE, FALSE, TRUE, 'YEARLY', 0.83, FALSE, NOW(), NOW()),
                ('Maternity Leave', 'Maternity leave', 90, TRUE, TRUE, TRUE, 'ONCE', 90.0, FALSE, NOW(), NOW()),
                ('Emergency Leave', 'Emergency leave', 3, FALSE, TRUE, TRUE, 'YEARLY', 0.25, FALSE, NOW(), NOW())
            """)
            self.stdout.write("    ✅ Created leave types")
        
        # Payment methods
        cursor.execute("SELECT COUNT(*) FROM payments_paymentmethod")
        if cursor.fetchone()[0] == 0:
            cursor.execute("""
                INSERT INTO payments_paymentmethod (name, description, type, is_active, created_at, updated_at) VALUES
                ('Cash', 'Cash payment', 'CASH', TRUE, NOW(), NOW()),
                ('Bank Transfer', 'Bank transfer payment', 'BANK_TRANSFER', TRUE, NOW(), NOW()),
                ('Mobile Money', 'Mobile money payment', 'MOBILE_MONEY', TRUE, NOW(), NOW()),
                ('Cheque', 'Cheque payment', 'CHEQUE', TRUE, NOW(), NOW())
            """)
            self.stdout.write("    ✅ Created payment methods")
        
        # Concession types
        cursor.execute("SELECT COUNT(*) FROM fees_concessiontype")
        if cursor.fetchone()[0] == 0:
            cursor.execute("""
                INSERT INTO fees_concessiontype (name, description, type, value, is_active, created_at, updated_at) VALUES
                ('Sibling Discount', 'Discount for siblings', 'PERCENTAGE', 10.00, TRUE, NOW(), NOW()),
                ('Staff Child Discount', 'Discount for staff children', 'PERCENTAGE', 50.00, TRUE, NOW(), NOW()),
                ('Merit Scholarship', 'Merit-based scholarship', 'PERCENTAGE', 25.00, TRUE, NOW(), NOW())
            """)
            self.stdout.write("    ✅ Created concession types")
        
        # Event categories
        cursor.execute("SELECT COUNT(*) FROM school_calendar_eventcategory")
        if cursor.fetchone()[0] == 0:
            cursor.execute("""
                INSERT INTO school_calendar_eventcategory (name, color, icon, description, is_active, created_at, updated_at) VALUES
                ('Academic', '#007bff', 'bi-book', 'Academic events', TRUE, NOW(), NOW()),
                ('Sports', '#28a745', 'bi-trophy', 'Sports events', TRUE, NOW(), NOW()),
                ('Cultural', '#ffc107', 'bi-music-note', 'Cultural events', TRUE, NOW(), NOW()),
                ('Meeting', '#6c757d', 'bi-people', 'Meetings', TRUE, NOW(), NOW()),
                ('Holiday', '#dc3545', 'bi-calendar-x', 'Holidays', TRUE, NOW(), NOW())
            """)
            self.stdout.write("    ✅ Created event categories")

    def create_owner_staff_user(self, schema_name, owner_email, school_name):
        """Create owner staff user"""
        
        try:
            with schema_context(schema_name):
                from apps.schools.models import StaffUser
                from django.contrib.auth.models import Group
                
                # Create or get admin group
                admin_group, created = Group.objects.get_or_create(
                    name='School Administrator',
                    defaults={'name': 'School Administrator'}
                )
                
                # Create staff user
                staff_user = StaffUser.objects.create_user(
                    email=owner_email,
                    first_name='School',
                    last_name='Administrator',
                    is_staff=True,
                    is_superuser=True,
                    is_active=True
                )
                
                # Add to admin group
                staff_user.groups.add(admin_group)
                
                self.stdout.write(f"    ✅ Created staff user: {owner_email}")
                
        except Exception as e:
            self.stdout.write(f"    ⚠️  Error creating owner staff user: {e}")

    def verify_tenant(self, schema_name):
        """Verify tenant is working correctly"""
        
        try:
            with connection.cursor() as cursor:
                cursor.execute(f'SET search_path TO "{schema_name}"')
                
                # Check essential tables exist
                essential_tables = [
                    'schools_academicyear', 'schools_staffuser', 'students_student',
                    'fees_invoice', 'payments_payment', 'hr_leavetype'
                ]
                
                for table in essential_tables:
                    cursor.execute(f"""
                        SELECT EXISTS (
                            SELECT FROM information_schema.tables 
                            WHERE table_schema = '{schema_name}' 
                            AND table_name = '{table}'
                        )
                    """)
                    
                    if not cursor.fetchone()[0]:
                        self.stdout.write(f"    ❌ Missing table: {table}")
                        return False
                
                # Check essential data exists
                cursor.execute("SELECT COUNT(*) FROM schools_academicyear")
                if cursor.fetchone()[0] == 0:
                    self.stdout.write("    ❌ No academic year data")
                    return False
                
                cursor.execute("SELECT COUNT(*) FROM hr_leavetype")
                if cursor.fetchone()[0] == 0:
                    self.stdout.write("    ❌ No leave type data")
                    return False
                
                self.stdout.write("    ✅ All essential components verified")
                return True
                
        except Exception as e:
            self.stdout.write(f"    ❌ Verification error: {e}")
            return False
