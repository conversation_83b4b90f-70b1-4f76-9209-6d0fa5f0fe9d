# apps/public_site/admin.py
from django.contrib import admin
from .models import Testimonial, ContactInquiry # Import if you created them

@admin.register(Testimonial)
class TestimonialAdmin(admin.ModelAdmin):
    list_display = (
        'author_name', 'school_name', 'school_type', 'review_category',
        'overall_rating', 'is_approved', 'is_featured', 'submitted_on'
    )
    list_filter = (
        'review_category', 'school_type', 'school_size', 'overall_rating',
        'is_approved', 'is_featured', 'would_recommend', 'submitted_on'
    )
    list_editable = ('is_approved', 'is_featured')
    search_fields = (
        'author_name', 'author_title', 'school_name', 'title', 'quote',
        'location', 'email'
    )
    readonly_fields = (
        'submitted_on', 'approved_on', 'ip_address', 'user_agent',
        'author_title_school', 'rating', 'average_rating', 'star_display'
    )

    fieldsets = (
        ('Personal Information', {
            'fields': ('author_name', 'author_title', 'email', 'phone')
        }),
        ('School Information', {
            'fields': ('school_name', 'school_type', 'school_size', 'location', 'website')
        }),
        ('Review Content', {
            'fields': ('review_category', 'title', 'quote')
        }),
        ('Ratings', {
            'fields': (
                'overall_rating', 'ease_of_use_rating',
                'customer_support_rating', 'value_rating'
            )
        }),
        ('Usage Information', {
            'fields': ('usage_duration', 'previous_solution', 'would_recommend')
        }),
        ('Admin Controls', {
            'fields': ('is_approved', 'is_featured', 'admin_notes', 'approved_on')
        }),
        ('Metadata', {
            'fields': ('submitted_on', 'ip_address', 'user_agent'),
            'classes': ('collapse',)
        }),
        ('Computed Fields', {
            'fields': ('author_title_school', 'rating', 'average_rating', 'star_display'),
            'classes': ('collapse',),
            'description': 'Read-only computed fields for backward compatibility and display'
        }),
    )

    actions = ['approve_testimonials', 'feature_testimonials', 'send_thank_you_emails']

    def approve_testimonials(self, request, queryset):
        from django.utils import timezone
        updated = queryset.update(
            is_approved=True,
            approved_on=timezone.now()
        )
        self.message_user(request, f'{updated} testimonials approved.')
    approve_testimonials.short_description = "Approve selected testimonials"

    def feature_testimonials(self, request, queryset):
        from django.utils import timezone
        updated = queryset.update(
            is_featured=True,
            is_approved=True,
            approved_on=timezone.now()
        )
        self.message_user(request, f'{updated} testimonials featured.')
    feature_testimonials.short_description = "Feature selected testimonials"

    def send_thank_you_emails(self, request, queryset):
        # This would integrate with email system to send thank you emails
        count = queryset.filter(email__isnull=False).count()
        self.message_user(request, f'Thank you emails queued for {count} testimonials.')
    send_thank_you_emails.short_description = "Send thank you emails"

    def get_queryset(self, request):
        return super().get_queryset(request).select_related()

@admin.register(ContactInquiry)
class ContactInquiryAdmin(admin.ModelAdmin):
    list_display = (
        'name', 'email', 'inquiry_type', 'priority', 'subject',
        'submitted_at', 'is_addressed', 'is_approved_for_broadcast', 'is_featured'
    )
    list_filter = (
        'inquiry_type', 'priority', 'is_addressed', 'is_approved_for_broadcast',
        'is_featured', 'submitted_at'
    )
    list_editable = ('is_addressed', 'is_approved_for_broadcast', 'is_featured')
    search_fields = ('name', 'email', 'subject', 'message', 'organization')
    readonly_fields = (
        'name', 'email', 'phone', 'organization', 'inquiry_type', 'subject',
        'message', 'submitted_at', 'ip_address', 'user_agent'
    )

    fieldsets = (
        ('Contact Information', {
            'fields': ('name', 'email', 'phone', 'organization')
        }),
        ('Inquiry Details', {
            'fields': ('inquiry_type', 'subject', 'message', 'priority')
        }),
        ('Status & Response', {
            'fields': ('is_addressed', 'addressed_at', 'addressed_by', 'admin_response', 'response_sent_at')
        }),
        ('Broadcasting', {
            'fields': ('is_approved_for_broadcast', 'broadcast_title', 'broadcast_excerpt', 'is_featured'),
            'description': 'Control whether this inquiry appears on the public platform'
        }),
        ('Metadata', {
            'fields': ('submitted_at', 'ip_address', 'user_agent'),
            'classes': ('collapse',)
        }),
    )

    actions = ['mark_as_addressed', 'approve_for_broadcast', 'feature_inquiry', 'send_response']

    def mark_as_addressed(self, request, queryset):
        from django.utils import timezone
        updated = queryset.update(
            is_addressed=True,
            addressed_at=timezone.now(),
            addressed_by=request.user.get_full_name() or request.user.username
        )
        self.message_user(request, f'{updated} inquiries marked as addressed.')
    mark_as_addressed.short_description = "Mark selected inquiries as addressed"

    def approve_for_broadcast(self, request, queryset):
        updated = queryset.update(is_approved_for_broadcast=True)
        self.message_user(request, f'{updated} inquiries approved for broadcast.')
    approve_for_broadcast.short_description = "Approve selected inquiries for broadcast"

    def feature_inquiry(self, request, queryset):
        updated = queryset.update(is_featured=True, is_approved_for_broadcast=True)
        self.message_user(request, f'{updated} inquiries featured on platform.')
    feature_inquiry.short_description = "Feature selected inquiries prominently"

    def send_response(self, request, queryset):
        # This would integrate with email system to send responses
        count = queryset.count()
        self.message_user(request, f'Response action initiated for {count} inquiries.')
    send_response.short_description = "Send response to selected inquiries"
    
    
    
