# apps/public_site/admin.py
from django.contrib import admin
from .models import Testimonial, ContactInquiry # Import if you created them

@admin.register(Testimonial)
class TestimonialAdmin(admin.ModelAdmin):
    list_display = ('author_name', 'author_title_school', 'rating', 'is_approved', 'submitted_on')
    list_filter = ('is_approved', 'rating', 'submitted_on')
    list_editable = ('is_approved',) # Allow approving directly from list
    search_fields = ('author_name', 'author_title_school', 'quote')

@admin.register(ContactInquiry)
class ContactInquiryAdmin(admin.ModelAdmin):
    list_display = (
        'name', 'email', 'inquiry_type', 'priority', 'subject',
        'submitted_at', 'is_addressed', 'is_approved_for_broadcast', 'is_featured'
    )
    list_filter = (
        'inquiry_type', 'priority', 'is_addressed', 'is_approved_for_broadcast',
        'is_featured', 'submitted_at'
    )
    list_editable = ('is_addressed', 'is_approved_for_broadcast', 'is_featured')
    search_fields = ('name', 'email', 'subject', 'message', 'organization')
    readonly_fields = (
        'name', 'email', 'phone', 'organization', 'inquiry_type', 'subject',
        'message', 'submitted_at', 'ip_address', 'user_agent'
    )

    fieldsets = (
        ('Contact Information', {
            'fields': ('name', 'email', 'phone', 'organization')
        }),
        ('Inquiry Details', {
            'fields': ('inquiry_type', 'subject', 'message', 'priority')
        }),
        ('Status & Response', {
            'fields': ('is_addressed', 'addressed_at', 'addressed_by', 'admin_response', 'response_sent_at')
        }),
        ('Broadcasting', {
            'fields': ('is_approved_for_broadcast', 'broadcast_title', 'broadcast_excerpt', 'is_featured'),
            'description': 'Control whether this inquiry appears on the public platform'
        }),
        ('Metadata', {
            'fields': ('submitted_at', 'ip_address', 'user_agent'),
            'classes': ('collapse',)
        }),
    )

    actions = ['mark_as_addressed', 'approve_for_broadcast', 'feature_inquiry', 'send_response']

    def mark_as_addressed(self, request, queryset):
        from django.utils import timezone
        updated = queryset.update(
            is_addressed=True,
            addressed_at=timezone.now(),
            addressed_by=request.user.get_full_name() or request.user.username
        )
        self.message_user(request, f'{updated} inquiries marked as addressed.')
    mark_as_addressed.short_description = "Mark selected inquiries as addressed"

    def approve_for_broadcast(self, request, queryset):
        updated = queryset.update(is_approved_for_broadcast=True)
        self.message_user(request, f'{updated} inquiries approved for broadcast.')
    approve_for_broadcast.short_description = "Approve selected inquiries for broadcast"

    def feature_inquiry(self, request, queryset):
        updated = queryset.update(is_featured=True, is_approved_for_broadcast=True)
        self.message_user(request, f'{updated} inquiries featured on platform.')
    feature_inquiry.short_description = "Feature selected inquiries prominently"

    def send_response(self, request, queryset):
        # This would integrate with email system to send responses
        count = queryset.count()
        self.message_user(request, f'Response action initiated for {count} inquiries.')
    send_response.short_description = "Send response to selected inquiries"
    
    
    
