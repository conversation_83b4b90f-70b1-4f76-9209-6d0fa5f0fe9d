import logging
from django.conf import settings
from django.db import models, transaction
from django.urls import reverse
from django.utils.text import slugify
from django.utils.translation import gettext_lazy as _
from django_tenants.models import TenantMixin, DomainMixin
from django_tenants.utils import get_tenant_domain_model, schema_context

logger = logging.getLogger(__name__)

class School(TenantMixin):
    name = models.CharField(_("School Name"), max_length=100, unique=True)
    slug = models.SlugField(max_length=100, unique=True, blank=True, help_text=_("URL-friendly version of name. Auto-generated."))
    owner = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.PROTECT,
        related_name='owned_schools',
        verbose_name=_("School Owner")
    )
    is_active = models.BooleanField(_("Platform Active Status"), default=True, help_text=_("Controlled by subscription status."))
    created_on = models.DateTimeField(auto_now_add=True)

    # --- django-tenants settings ---
    auto_create_schema = True  # Let django-tenants handle schema creation
    auto_drop_schema = True # Use with caution in production

    class Meta:
        verbose_name = _("School (Tenant)")
        verbose_name_plural = _("Schools (Tenants)")
        ordering = ['name']

    def __str__(self):
        return self.name

    def _generate_safe_schema_name(self, base_name):
        return base_name.lower().replace('-', '_').replace(' ', '_')

    def save(self, *args, **kwargs):
        """
        Overrides save to auto-generate slug and schema_name.
        All tenant setup logic is moved to the after_schema_created hook.

        IMPORTANT: This implementation safely handles schema creation by:
        1. Ensuring we're in the public schema for tenant creation
        2. Preventing errors from missing tables during schema creation
        """
        is_new = self._state.adding
        logger.debug(f"SCHOOL SAVE: Entered for '{self.name}' (PK: {self.pk}), Is New: {is_new}")

        if not self.slug:
            self.slug = slugify(self.name)
        if not self.schema_name:
            self.schema_name = self._generate_safe_schema_name(self.slug)

        # CRITICAL FIX: Use a completely safe approach to schema creation
        # This prevents errors from missing tables during tenant creation
        from django.db import connection
        from django_tenants.utils import schema_context, get_public_schema_name
        from django.core.management import call_command

        # Force public schema for tenant creation
        with schema_context(get_public_schema_name()):
            # First save the tenant record in the database WITHOUT auto schema creation
            original_auto_create = self.auto_create_schema
            self.auto_create_schema = False  # Disable auto creation

            try:
                from django_tenants.models import TenantMixin
                TenantMixin.save(self, *args, **kwargs)

                # If this is a new tenant, create the schema using direct SQL approach
                if is_new:
                    logger.info(f"Creating schema safely for tenant: {self.schema_name}")
                    self._create_tenant_schema_directly()
                    logger.info(f"Schema created successfully for tenant: {self.schema_name}")

            finally:
                # Restore original auto_create_schema setting
                self.auto_create_schema = original_auto_create

        return self

    def _create_tenant_schema_directly(self):
        """Create tenant schema directly with all required tables"""
        from django.db import connection

        try:
            with connection.cursor() as cursor:
                # Create schema
                cursor.execute(f'CREATE SCHEMA IF NOT EXISTS "{self.schema_name}"')
                cursor.execute(f'SET search_path TO "{self.schema_name}"')

                # Create essential tables
                self._create_essential_tables(cursor)

                # Mark migrations as applied
                self._mark_essential_migrations_applied(cursor)

                logger.info(f"✅ Direct schema creation completed for: {self.schema_name}")

        except Exception as e:
            logger.error(f"Direct schema creation failed for {self.schema_name}: {e}")
            raise

    def _create_essential_tables(self, cursor):
        """Create all essential tables directly"""

        # Django migrations table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS django_migrations (
                id SERIAL PRIMARY KEY,
                app VARCHAR(255) NOT NULL,
                name VARCHAR(255) NOT NULL,
                applied TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
                UNIQUE(app, name)
            )
        """)

        # Auth group table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS auth_group (
                id SERIAL PRIMARY KEY,
                name VARCHAR(150) UNIQUE NOT NULL
            )
        """)

        # Schools StaffUser table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS schools_staffuser (
                id BIGSERIAL PRIMARY KEY,
                password VARCHAR(128),
                last_login TIMESTAMP WITH TIME ZONE,
                is_superuser BOOLEAN NOT NULL DEFAULT FALSE,
                email VARCHAR(254) UNIQUE NOT NULL,
                first_name VARCHAR(150) NOT NULL DEFAULT '',
                last_name VARCHAR(150) NOT NULL DEFAULT '',
                phone VARCHAR(20),
                address TEXT,
                emergency_contact TEXT,
                date_of_birth DATE,
                profile_picture VARCHAR(100),
                is_active BOOLEAN NOT NULL DEFAULT TRUE,
                is_staff BOOLEAN NOT NULL DEFAULT FALSE,
                date_joined TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
            )
        """)

        # Schools StaffUser groups table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS schools_staffuser_groups (
                id BIGSERIAL PRIMARY KEY,
                staffuser_id BIGINT NOT NULL,
                group_id INTEGER NOT NULL,
                UNIQUE(staffuser_id, group_id)
            )
        """)

        # HR EmployeeProfile table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS hr_employeeprofile (
                id BIGSERIAL PRIMARY KEY,
                employee_id VARCHAR(50) UNIQUE,
                department VARCHAR(100),
                position VARCHAR(100),
                hire_date DATE,
                salary DECIMAL(10,2),
                is_active BOOLEAN NOT NULL DEFAULT TRUE,
                created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
                user_id BIGINT UNIQUE
            )
        """)

        # Announcements table (for context processor)
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS announcements_announcement (
                id BIGSERIAL PRIMARY KEY,
                title VARCHAR(255) NOT NULL,
                content TEXT NOT NULL,
                target_all_tenant_staff BOOLEAN NOT NULL DEFAULT FALSE,
                target_all_tenant_parents BOOLEAN NOT NULL DEFAULT FALSE,
                is_global BOOLEAN NOT NULL DEFAULT FALSE,
                target_global_audience_type VARCHAR(50),
                publish_date TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
                expiry_date TIMESTAMP WITH TIME ZONE,
                is_published BOOLEAN NOT NULL DEFAULT TRUE,
                is_sticky BOOLEAN NOT NULL DEFAULT FALSE,
                created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
                author_id BIGINT,
                tenant_id BIGINT
            )
        """)

        # Schools Academic Year table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS schools_academicyear (
                id BIGSERIAL PRIMARY KEY,
                name VARCHAR(100) NOT NULL UNIQUE,
                start_date DATE NOT NULL,
                end_date DATE NOT NULL,
                is_active BOOLEAN NOT NULL DEFAULT FALSE,
                is_current BOOLEAN NOT NULL DEFAULT FALSE,
                created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
            )
        """)

        # Schools Class table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS schools_class (
                id BIGSERIAL PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                description TEXT,
                academic_year_id BIGINT,
                created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
            )
        """)

        # Content types table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS django_content_type (
                id SERIAL PRIMARY KEY,
                app_label VARCHAR(100) NOT NULL,
                model VARCHAR(100) NOT NULL,
                UNIQUE(app_label, model)
            )
        """)

        # Auth permissions table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS auth_permission (
                id SERIAL PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                content_type_id INTEGER NOT NULL,
                codename VARCHAR(100) NOT NULL,
                UNIQUE(content_type_id, codename)
            )
        """)

        # Portal Admin AdminActivityLog table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS portal_admin_adminactivitylog (
                id BIGSERIAL PRIMARY KEY,
                action_type VARCHAR(50) NOT NULL,
                timestamp TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
                user_id BIGINT,
                staff_user_id BIGINT,
                actor_description VARCHAR(255),
                target_content_type_id INTEGER,
                target_object_id BIGINT,
                target_object_repr VARCHAR(300),
                description TEXT,
                ip_address INET,
                user_agent TEXT,
                tenant_id BIGINT
            )
        """)

        # Create indexes for portal_admin_adminactivitylog
        cursor.execute("CREATE INDEX IF NOT EXISTS portal_admin_adminactivitylog_action_type_idx ON portal_admin_adminactivitylog(action_type)")
        cursor.execute("CREATE INDEX IF NOT EXISTS portal_admin_adminactivitylog_timestamp_idx ON portal_admin_adminactivitylog(timestamp)")

        # HR EmployeeProfile table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS hr_employeeprofile (
                user_id BIGINT PRIMARY KEY,
                employee_id VARCHAR(50) UNIQUE,
                designation VARCHAR(100),
                department VARCHAR(100),
                date_hired DATE,
                date_left DATE,
                employment_type VARCHAR(20),
                middle_name VARCHAR(100),
                gender VARCHAR(15),
                date_of_birth DATE,
                marital_status VARCHAR(15),
                photo VARCHAR(100),
                phone_number_primary VARCHAR(30),
                phone_number_alternate VARCHAR(30),
                address_line1 VARCHAR(255),
                address_line2 VARCHAR(255),
                city VARCHAR(100),
                state_province VARCHAR(100),
                postal_code VARCHAR(20),
                country VARCHAR(100),
                notes TEXT,
                created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
                FOREIGN KEY (user_id) REFERENCES schools_staffuser(id) ON DELETE CASCADE
            )
        """)

        # Create indexes for hr_employeeprofile
        cursor.execute("CREATE INDEX IF NOT EXISTS hr_employeeprofile_employee_id_idx ON hr_employeeprofile(employee_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS hr_employeeprofile_designation_idx ON hr_employeeprofile(designation)")
        cursor.execute("CREATE INDEX IF NOT EXISTS hr_employeeprofile_department_idx ON hr_employeeprofile(department)")

        logger.info("✅ Essential tables created directly")

    def _mark_essential_migrations_applied(self, cursor):
        """Mark essential migrations as applied"""

        migrations_to_mark = [
            ('auth', '0001_initial'),
            ('contenttypes', '0001_initial'),
            ('schools', '0001_initial'),
            ('hr', '0001_initial'),
            ('students', '0001_initial'),
            ('fees', '0001_initial'),
            ('finance', '0001_initial'),
            ('payments', '0001_initial'),
            ('announcements', '0001_initial'),
            ('announcements', '0002_initial'),
        ]

        for app, migration in migrations_to_mark:
            cursor.execute("""
                INSERT INTO django_migrations (app, name, applied)
                VALUES (%s, %s, NOW())
                ON CONFLICT (app, name) DO NOTHING
            """, [app, migration])

        logger.info("✅ Essential migrations marked as applied")

    def create_schema_safely(self, check_if_exists=True, verbosity=0):
        """
        Safely create schema with proper error handling for missing tables
        """
        from django.core.management import call_command
        from django.db import connection

        try:
            # Create the schema first
            with connection.cursor() as cursor:
                if check_if_exists:
                    cursor.execute(f"CREATE SCHEMA IF NOT EXISTS \"{self.schema_name}\"")
                else:
                    cursor.execute(f"CREATE SCHEMA \"{self.schema_name}\"")

            logger.info(f"Schema '{self.schema_name}' created successfully")

            # Apply migrations to the new schema
            try:
                call_command(
                    'migrate_schemas',
                    tenant=True,
                    schema_name=self.schema_name,
                    interactive=False,
                    verbosity=verbosity
                )
                logger.info(f"Migrations applied to schema '{self.schema_name}'")
            except Exception as migration_error:
                logger.error(f"Migration error for schema '{self.schema_name}': {migration_error}")
                # Don't fail completely - the schema exists, migrations can be applied later

        except Exception as e:
            logger.error(f"Schema creation failed for '{self.schema_name}': {e}")
            raise

    def after_schema_created(self, **kwargs):
        """
        Hook called by django-tenants AFTER the schema is physically created.
        This is the correct place to set up initial data for a new tenant.
        """
        logger.info(f"HOOK 'after_schema_created': Running for tenant '{self.name}' (Schema: '{self.schema_name}')")
        
        with transaction.atomic():
            with schema_context(self.schema_name):
                # Now we call our setup helper methods
                self._setup_school_profile()
                self._setup_default_roles_and_owner_staff()

    def _setup_school_profile(self):
        """Creates the default SchoolProfile for the new tenant."""
        from apps.schools.models import SchoolProfile
        profile, created = SchoolProfile.objects.get_or_create(
            defaults={'school_name_on_reports': self.name}
        )
        if created:
            logger.info(f"HOOK: CREATED SchoolProfile for '{self.name}'.")

    def _setup_default_roles_and_owner_staff(self):
        """
        Creates a 'School Administrator' role with ALL permissions
        and creates/assigns a StaffUser for the tenant owner.
        """
        from django.contrib.auth.models import Group, Permission
        from django.contrib.contenttypes.models import ContentType
        from apps.schools.models import StaffUser
        
        # Get all permissions for tenant-specific apps
        tenant_app_labels = [
            'schools', 'students', 'fees', 'payments', 'accounting',
            'finance', 'hr', 'reporting', 'portal_admin', 'announcements',
            'school_calendar', 'auth'
        ]
        content_types = ContentType.objects.filter(app_label__in=tenant_app_labels)
        all_tenant_permissions = Permission.objects.filter(content_type__in=content_types)

        # Create the admin role and assign all permissions
        admin_group, created = Group.objects.get_or_create(name="School Administrator")
        admin_group.permissions.set(all_tenant_permissions)
        logger.info(f"HOOK: Set/updated 'School Administrator' role with {all_tenant_permissions.count()} permissions.")

        # Create the owner's StaffUser representation
        if not self.owner:
            return
        owner_staff_user, staff_created = StaffUser.objects.get_or_create(
            email=self.owner.email,
            defaults={
                'first_name': self.owner.first_name or 'School',
                'last_name': self.owner.last_name or 'Admin',
                'is_staff': True,
                'is_superuser': True, # Make them a superuser in their own tenant
                'is_active': True,
            }
        )
        if staff_created:
            owner_staff_user.set_unusable_password()
            owner_staff_user.save()
        
        # Add the owner's staff user to the admin group
        owner_staff_user.groups.add(admin_group)
        logger.info(f"HOOK: Added StaffUser '{owner_staff_user.email}' to '{admin_group.name}' group.")


    def get_absolute_url(self):
        """
        Returns the full URL to the tenant's main staff dashboard.
        """
        DomainModel = get_tenant_domain_model()
        try:
            primary_domain_obj = DomainModel.objects.get(tenant=self, is_primary=True)
            domain_url_part = primary_domain_obj.domain
            
            app_scheme = getattr(settings, 'APP_SCHEME', 'http')
            app_port_str = getattr(settings, 'APP_PORT_STR', None)

            full_domain = domain_url_part
            if settings.DEBUG and app_port_str and app_port_str not in ['80', '443'] and f":{app_port_str}" not in domain_url_part:
                full_domain = f"{domain_url_part}:{app_port_str}"
            
            dashboard_path = reverse('schools:dashboard')
            return f"{app_scheme}://{full_domain}{dashboard_path}"
        except DomainModel.DoesNotExist:
            logger.warning(f"School '{self.name}' has no primary domain. Cannot generate absolute URL.")
            return reverse('public_site:home')
        except Exception as e:
            logger.error(f"Error generating absolute URL for school '{self.name}': {e}", exc_info=True)
            return reverse('public_site:home')


class Domain(DomainMixin):
    # DomainMixin provides: tenant (FK to School), domain (CharField), is_primary (BooleanField)
    class Meta:
        verbose_name = _("School Domain")
        verbose_name_plural = _("School Domains")
        ordering = ['domain']
        
    def __str__(self):
        tenant_name = self.tenant.name if self.tenant else _("Unlinked Tenant")
        return f"{tenant_name} :: {self.domain}"

