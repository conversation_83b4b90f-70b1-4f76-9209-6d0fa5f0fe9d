# D:\school_fees_saas_v2\apps\tenants\urls.py
from django.urls import path
from . import views

app_name = 'tenants' # This defines the namespace

urlpatterns = [
    path('register/', views.register_school_view, name='register_school'),
    path('register/success/', views.RegistrationSuccessView.as_view(), name='registration_success'),
    # Add other tenant account-related public URLs here if needed
    
    # The page shown after registration, before payment.
    path('confirm-and-pay/<int:subscription_id>/', views.confirm_and_pay_view, name='confirm_and_pay'),
    
    # You might also have a "registration successful" page to show after the webhook confirms payment.
    path('registration-complete/', views.RegistrationSuccessView.as_view(), name='registration_complete'),
]

