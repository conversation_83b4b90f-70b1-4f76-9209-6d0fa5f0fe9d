{% extends "reporting/pdf/_pdf_base.html" %}
{% load static humanize %}

{% block pdf_title %}Payment Summary Report{% endblock %}

{% block pdf_content %}
    <table>
        <thead>
            <tr>
                <th>Date Paid</th>
                <th>Student Name</th>
                <th>Class</th>
                <th>Invoice #</th>
                <th>Payment Ref #</th>
                <th>Payment Method</th>
                <th class="text-end">Amount Paid</th>
            </tr>
        </thead>
        <tbody>
            {% for payment in report_items %}
            <tr>
                <td>{{ payment.payment_date|date:"d M Y" }}</td>
                <td>{{ payment.student.get_full_name|default:"N/A" }}</td>
                <td>{{ payment.student.current_class_and_section|default:"N/A" }}</td>
                <td>
                    {% if payment.allocations.exists %}
                        {% for allocation in payment.allocations.all %}
                            {{ allocation.invoice.invoice_number }}{% if not forloop.last %}, {% endif %}
                        {% endfor %}
                    {% else %}
                        Unallocated
                    {% endif %}
                </td>
                <td>{{ payment.transaction_reference|default:"N/A" }}</td>
                <td>{{ payment.payment_method.name|default:"N/A" }}</td>
                <td class="text-end">{{ payment.amount|floatformat:2|intcomma }}</td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="7" class="text-center">No payments found.</td>
            </tr>
            {% endfor %}
        </tbody>
        {% if report_items %}
        <tfoot>
            <tr class="total-row">
                <td colspan="6" class="text-end"><strong>Total Amount Collected:</strong></td>
                <td class="text-end"><strong>{{ total_amount_collected|floatformat:2|intcomma }}</strong></td>
            </tr>
        </tfoot>
        {% endif %}
    </table>
{% endblock %}
