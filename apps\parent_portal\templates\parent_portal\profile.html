{% extends "parent_portal/parent_portal_base.html" %}

{% load static %}

{% block parent_portal_page_title %}{{ view_title|default:"My Profile" }}{% endblock %}

{% block extra_parent_portal_css %}
    {{ block.super }} {# Includes CSS from tenant_base.html #}
    {# Add any parent_portal specific CSS here if needed #}
    <style>
        .profile-details dt {
            font-weight: 600;
            color: #008080; /* Teal, consistent with tenant_form_styles */
        }
        .profile-details dd {
            margin-bottom: 0.75rem;
        }
        .profile-card {
            max-width: 700px;
            margin: 2rem auto;
        }
    </style>
{% endblock page_specific_css %}

{% block parent_portal_main_content %}
<div class="container mt-4">
    <div class="pagetitle mb-3">
        <h1>{{ view_title|default:"My Profile" }}</h1>
        <nav>
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'parent_portal:dashboard' %}">Dashboard</a></li>
                <li class="breadcrumb-item active">My Profile</li>
            </ol>
        </nav>
    </div><!-- End Page Title -->

    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{% if message.tags %}{{ message.tags }}{% else %}info{% endif %} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        {% endfor %}
    {% endif %}

    <div class="card shadow-sm profile-card">
        <div class="card-header bg-info text-white">
            <h5 class="mb-0">Your Account Details</h5>
        </div>
        <div class="card-body p-4">
            {% if parent_user %}
                <dl class="row profile-details">
                    <dt class="col-sm-4">Full Name:</dt>
                    <dd class="col-sm-8">{{ parent_user.get_full_name|default:"Not Set" }}</dd>

                    <dt class="col-sm-4">Email Address (Login ID):</dt>
                    <dd class="col-sm-8">{{ parent_user.email }}</dd>

                    <dt class="col-sm-4">Phone Number:</dt>
                    <dd class="col-sm-8">{{ parent_user.phone_number|default:"Not Set" }}</dd>
                    
                    <dt class="col-sm-4">Address:</dt>
                    <dd class="col-sm-8">{{ parent_user.address|default:"Not Set"|linebreaksbr }}</dd>

                    <dt class="col-sm-4">Account Status:</dt>
                    <dd class="col-sm-8">
                        {% if parent_user.is_active %}
                            <span class="badge bg-success">Active</span>
                        {% else %}
                            <span class="badge bg-danger">Inactive</span>
                        {% endif %}
                    </dd>
                    
                    {# You can add more fields from your ParentUser model as needed #}
                    {# For example, if you have a 'date_joined' or 'last_login' field #}
                    {# 
                    <dt class="col-sm-4">Date Joined:</dt>
                    <dd class="col-sm-8">{{ parent_user.date_joined|date:"F d, Y" }}</dd>

                    <dt class="col-sm-4">Last Login:</dt>
                    <dd class="col-sm-8">{{ parent_user.last_login|date:"F d, Y, P"|default:"Never" }}</dd> 
                    #}
                </dl>

                <hr>
                <div class="text-end">
                    <a href="{% url 'parent_portal:profile_update' %}" class="btn btn-primary">
                        <i class="bi bi-pencil-square"></i> Edit Profile
                    </a>
                    {# We'll add password change link later if needed #}
                    {# <a href="{% url 'parent_portal:password_change' %}" class="btn btn-secondary ms-2">
                        <i class="bi bi-key-fill"></i> Change Password
                    </a> #}
                </div>
            {% else %}
                <div class="alert alert-warning" role="alert">
                    Could not load your profile information at this time.
                </div>
            {% endif %}
        </div>
    </div>

    {# Section for Linked Students (if applicable and data is passed) #}
    {% if linked_students %}
    <div class="card shadow-sm profile-card mt-4">
        <div class="card-header bg-secondary text-white">
            <h5 class="mb-0">Linked Students</h5>
        </div>
        <div class="card-body p-4">
            {% if linked_students %}
                <ul class="list-group">
                    {% for student in linked_students %}
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            {{ student.get_full_name }} (Class: {{ student.current_class.name }} - {{ student.current_section.name }})
                            <a href="{% url 'parent_portal:student_detail' student_id=student.pk %}" class="btn btn-sm btn-outline-info">View Details</a>
                        </li>
                    {% empty %}
                        <li class="list-group-item">No students are currently linked to your account.</li>
                    {% endfor %}
                </ul>
            {% else %}
                <p>Information about linked students is not available.</p>
            {% endif %}
        </div>
    </div>
    {% endif %}

</div>
{% endblock parent_portal_main_content %}