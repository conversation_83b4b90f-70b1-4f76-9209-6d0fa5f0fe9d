"""
Filters for Schools App
Filter forms for staff and other school-related models
"""

import django_filters
from django import forms
from django.db.models import Q
from django.utils.translation import gettext_lazy as _

from .models import StaffUser


class StaffUserFilterForm(django_filters.FilterSet):
    """Filter form for StaffUser list view."""

    name = django_filters.CharFilter(
        method='filter_by_name_or_email',
        label=_("Name or Email"),
        widget=forms.TextInput(attrs={
            'class': 'form-control form-control-sm',
            'placeholder': _('Name or Email')
        })
    )

    employee_id = django_filters.CharFilter(
        method='filter_by_employee_id',
        label=_("Employee ID"),
        widget=forms.TextInput(attrs={
            'class': 'form-control form-control-sm',
            'placeholder': _('Employee ID')
        })
    )

    designation = django_filters.CharFilter(
        method='filter_by_designation',
        label=_("Designation"),
        widget=forms.TextInput(attrs={
            'class': 'form-control form-control-sm',
            'placeholder': _('Designation')
        })
    )

    department = django_filters.CharFilter(
        method='filter_by_department',
        label=_("Department"),
        widget=forms.TextInput(attrs={
            'class': 'form-control form-control-sm',
            'placeholder': _('Department')
        })
    )

    is_active = django_filters.BooleanFilter(
        label=_("Status"),
        widget=forms.Select(
            choices=[
                ('', _('-- All Status --')),
                (True, _('Active')),
                (False, _('Inactive'))
            ],
            attrs={'class': 'form-select form-select-sm'}
        )
    )

    date_hired = django_filters.DateFromToRangeFilter(
        method='filter_by_date_hired',
        label=_("Date Hired"),
        widget=django_filters.widgets.RangeWidget(attrs={
            'class': 'form-control form-control-sm',
            'type': 'date'
        })
    )

    class Meta:
        model = StaffUser
        fields = ['name', 'employee_id', 'designation', 'department', 'is_active', 'date_hired']

    def __init__(self, *args, **kwargs):
        self.request = kwargs.pop('request', None)
        super().__init__(*args, **kwargs)

    def filter_by_name_or_email(self, queryset, name, value):
        """Filter by first name, last name, or email."""
        if value:
            return queryset.filter(
                Q(first_name__icontains=value) |
                Q(last_name__icontains=value) |
                Q(email__icontains=value)
            ).distinct()
        return queryset

    def filter_by_employee_id(self, queryset, name, value):
        """Filter by employee ID from HR profile."""
        if value:
            return queryset.filter(
                Q(hr_profile__employee_id__icontains=value) |
                Q(employee_id__icontains=value)  # Direct field if exists
            ).distinct()
        return queryset

    def filter_by_designation(self, queryset, name, value):
        """Filter by designation from HR profile."""
        if value:
            return queryset.filter(
                Q(hr_profile__designation__icontains=value) |
                Q(designation__icontains=value)  # Direct field if exists
            ).distinct()
        return queryset

    def filter_by_department(self, queryset, name, value):
        """Filter by department from HR profile."""
        if value:
            return queryset.filter(
                hr_profile__department__icontains=value
            ).distinct()
        return queryset

    def filter_by_date_hired(self, queryset, name, value):
        """Filter by date hired from HR profile."""
        if value:
            if value.start:
                queryset = queryset.filter(hr_profile__date_hired__gte=value.start)
            if value.stop:
                queryset = queryset.filter(hr_profile__date_hired__lte=value.stop)
        return queryset
