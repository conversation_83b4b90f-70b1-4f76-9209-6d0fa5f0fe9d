{% extends "tenant_base.html" %}
{% load humanize static core_tags %}

{% block title %}{{ view_title|default:"Student Enrollment Impact Report" }}{% endblock %}

{% block content %}
<div class="container-fluid mt-3">
    {% with report_icon_class="bi bi-people" %}
        {% include "partials/_report_header.html" %}
    {% endwith %}

    {% include "partials/_messages.html" %}
    {% include "partials/_report_filter_form.html" with filter_form=filter_form report_code=report_code specific_filter_title="Student Enrollment Impact Filters" %}

    <!-- Enrollment Impact Overview -->
    <div class="card mb-4">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="bi bi-graph-up me-2"></i>Enrollment Impact Overview</h5>
        </div>
        <div class="card-body">
            <div class="row mb-3">
                <div class="col-md-12">
                    <p>
                        <strong>Date Range:</strong> {{ start_date }} to {{ end_date }} | 
                        <strong>Impact Analysis:</strong> {{ impact_analysis|title }} | 
                        <strong>Efficiency Grade:</strong> 
                        <span class="badge {% if financial_impact.enrollment_efficiency_grade == 'A' %}bg-success{% elif financial_impact.enrollment_efficiency_grade == 'B' %}bg-info{% elif financial_impact.enrollment_efficiency_grade == 'C' %}bg-warning{% else %}bg-danger{% endif %} fs-6">
                            {{ financial_impact.enrollment_efficiency_grade }}
                        </span>
                    </p>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-3">
                    <div class="text-center">
                        <h6 class="text-muted">New Enrollments</h6>
                        <p class="mb-0 fw-bold text-primary fs-4">{{ financial_impact.total_new_enrollments }}</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h6 class="text-muted">Revenue Generated</h6>
                        <p class="mb-0 fw-bold text-success fs-4">{{ financial_impact.total_revenue_generated|currency }}</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h6 class="text-muted">Collection Rate</h6>
                        <p class="mb-0 fw-bold text-warning fs-4">{{ financial_impact.overall_collection_rate|floatformat:1 }}%</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h6 class="text-muted">Revenue/Student</h6>
                        <p class="mb-0 fw-bold text-info fs-4">{{ financial_impact.avg_revenue_per_enrollment|currency }}</p>
                    </div>
                </div>
            </div>
            
            <!-- Enrollment Efficiency Indicator -->
            <div class="mt-3">
                <h6>Enrollment Efficiency Score</h6>
                <div class="progress" style="height: 25px;">
                    <div class="progress-bar {% if financial_impact.enrollment_efficiency_score >= 80 %}bg-success{% elif financial_impact.enrollment_efficiency_score >= 60 %}bg-info{% elif financial_impact.enrollment_efficiency_score >= 40 %}bg-warning{% else %}bg-danger{% endif %}" 
                         role="progressbar" style="width: {{ financial_impact.enrollment_efficiency_score }}%">
                        {{ financial_impact.enrollment_efficiency_score|floatformat:0 }}/100
                    </div>
                </div>
                <small class="text-muted">
                    Grade {{ financial_impact.enrollment_efficiency_grade }} - 
                    {% if financial_impact.enrollment_efficiency_score >= 80 %}
                    Excellent enrollment-to-revenue conversion
                    {% elif financial_impact.enrollment_efficiency_score >= 60 %}
                    Good performance with optimization opportunities
                    {% else %}
                    Needs improvement in enrollment monetization
                    {% endif %}
                </small>
            </div>
        </div>
    </div>

    <!-- Financial Impact Metrics -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="bi bi-cash-stack me-2"></i>Financial Metrics</h5>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled">
                        <li><i class="bi bi-people text-primary me-2"></i>
                            <strong>Total New Enrollments:</strong> {{ financial_impact.total_new_enrollments }}
                        </li>
                        <li><i class="bi bi-currency-dollar text-success me-2"></i>
                            <strong>Revenue Generated:</strong> {{ financial_impact.total_revenue_generated|currency }}
                        </li>
                        <li><i class="bi bi-arrow-down-circle text-info me-2"></i>
                            <strong>Payments Received:</strong> {{ financial_impact.total_payments_received|currency }}
                        </li>
                        <li><i class="bi bi-exclamation-circle text-warning me-2"></i>
                            <strong>Outstanding Amount:</strong> {{ financial_impact.total_outstanding|currency }}
                        </li>
                        <li><i class="bi bi-calculator text-secondary me-2"></i>
                            <strong>Avg Revenue/Student:</strong> {{ financial_impact.avg_revenue_per_enrollment|currency }}
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="bi bi-speedometer me-2"></i>Performance Indicators</h5>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled">
                        <li><i class="bi bi-percent text-primary me-2"></i>
                            <strong>Collection Rate:</strong> {{ financial_impact.overall_collection_rate|floatformat:1 }}%
                        </li>
                        <li><i class="bi bi-award text-warning me-2"></i>
                            <strong>Efficiency Score:</strong> {{ financial_impact.enrollment_efficiency_score|floatformat:0 }}/100
                        </li>
                        <li><i class="bi bi-trophy text-success me-2"></i>
                            <strong>Efficiency Grade:</strong> {{ financial_impact.enrollment_efficiency_grade }}
                        </li>
                        <li><i class="bi bi-graph-up text-info me-2"></i>
                            <strong>Revenue Correlation:</strong> {{ correlation_insights.enrollment_revenue_correlation }}
                        </li>
                        <li><i class="bi bi-calendar text-secondary me-2"></i>
                            <strong>Analysis Period:</strong> {{ group_by_period|title }}
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Enrollment Analysis by Period -->
    {% if enrollment_analysis %}
    <div class="card mb-4">
        <div class="card-header bg-light d-flex justify-content-between align-items-center">
            <h5 class="mb-0"><i class="bi bi-table me-2"></i>Enrollment Analysis by Period</h5>
            <span class="badge bg-primary">{{ enrollment_analysis|length }} periods</span>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-striped table-hover mb-0">
                    <thead class="table-dark">
                        <tr>
                            <th>Period</th>
                            <th class="text-center">New Enrollments</th>
                            <th class="text-end">Revenue Generated</th>
                            <th class="text-end">Payments Received</th>
                            <th class="text-center">Collection Rate</th>
                            <th class="text-end">Avg Revenue/Student</th>
                            <th class="text-center">Growth Rate</th>
                            <th class="text-center">Trend</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for analysis in enrollment_analysis %}
                        <tr class="{% if analysis.trend == 'DECLINING' %}table-warning{% elif analysis.trend == 'STRONG_GROWTH' %}table-success{% endif %}">
                            <td>
                                <strong>{{ analysis.period }}</strong>
                            </td>
                            <td class="text-center">
                                <span class="fw-bold">{{ analysis.new_enrollments }}</span>
                            </td>
                            <td class="text-end">
                                <span class="fw-bold text-success">{{ analysis.revenue_generated|currency }}</span>
                            </td>
                            <td class="text-end">
                                <span class="text-info">{{ analysis.payments_received|currency }}</span>
                                {% if analysis.outstanding_amount > 0 %}
                                <br><small class="text-warning">Outstanding: {{ analysis.outstanding_amount|currency }}</small>
                                {% endif %}
                            </td>
                            <td class="text-center">
                                <div class="progress" style="height: 20px; width: 80px;">
                                    <div class="progress-bar {% if analysis.collection_rate >= 80 %}bg-success{% elif analysis.collection_rate >= 60 %}bg-warning{% else %}bg-danger{% endif %}" 
                                         role="progressbar" style="width: {{ analysis.collection_rate }}%">
                                    </div>
                                </div>
                                <small class="text-muted">{{ analysis.collection_rate|floatformat:1 }}%</small>
                            </td>
                            <td class="text-end">
                                <span class="text-primary fw-bold">{{ analysis.avg_revenue_per_student|currency }}</span>
                            </td>
                            <td class="text-center">
                                <span class="{% if analysis.growth_rate > 0 %}text-success{% elif analysis.growth_rate < -5 %}text-danger{% else %}text-warning{% endif %}">
                                    {% if analysis.growth_rate > 0 %}+{% endif %}{{ analysis.growth_rate|floatformat:1 }}%
                                </span>
                            </td>
                            <td class="text-center">
                                <span class="badge {% if analysis.trend == 'STRONG_GROWTH' %}bg-success{% elif analysis.trend == 'MODERATE_GROWTH' %}bg-info{% elif analysis.trend == 'STABLE' %}bg-secondary{% else %}bg-danger{% endif %}">
                                    {% if analysis.trend == 'STRONG_GROWTH' %}Strong Growth
                                    {% elif analysis.trend == 'MODERATE_GROWTH' %}Moderate Growth
                                    {% elif analysis.trend == 'STABLE' %}Stable
                                    {% else %}Declining{% endif %}
                                </span>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                    <tfoot class="table-light">
                        <tr>
                            <th>Totals:</th>
                            <th class="text-center">{{ financial_impact.total_new_enrollments }}</th>
                            <th class="text-end">{{ financial_impact.total_revenue_generated|currency }}</th>
                            <th class="text-end">{{ financial_impact.total_payments_received|currency }}</th>
                            <th class="text-center">{{ financial_impact.overall_collection_rate|floatformat:1 }}%</th>
                            <th class="text-end">{{ financial_impact.avg_revenue_per_enrollment|currency }}</th>
                            <th colspan="2"></th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Correlation Insights -->
    {% if correlation_insights %}
    <div class="row mb-4">
        <div class="col-md-6">
            <!-- Seasonal Patterns -->
            {% if correlation_insights.seasonal_patterns %}
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="bi bi-calendar-event me-2"></i>Seasonal Patterns</h5>
                </div>
                <div class="card-body">
                    {% for pattern in correlation_insights.seasonal_patterns %}
                    <div class="mb-3">
                        <h6 class="text-primary">{{ pattern.pattern }}</h6>
                        <p class="mb-1"><strong>Period:</strong> {{ pattern.period }}</p>
                        <p class="mb-0"><small class="text-muted">{{ pattern.impact }}</small></p>
                    </div>
                    {% if not forloop.last %}<hr>{% endif %}
                    {% endfor %}
                </div>
            </div>
            {% endif %}
        </div>
        
        <div class="col-md-6">
            <!-- Risk Factors -->
            {% if correlation_insights.risk_factors %}
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="bi bi-exclamation-triangle me-2"></i>Risk Factors</h5>
                </div>
                <div class="card-body">
                    {% for risk in correlation_insights.risk_factors %}
                    <div class="mb-3">
                        <h6 class="text-warning">{{ risk.risk }}</h6>
                        <p class="mb-1"><small>{{ risk.impact }}</small></p>
                        <p class="mb-0"><strong>Mitigation:</strong> <small class="text-success">{{ risk.mitigation }}</small></p>
                    </div>
                    {% if not forloop.last %}<hr>{% endif %}
                    {% endfor %}
                </div>
            </div>
            {% endif %}
        </div>
    </div>
    {% endif %}

    <!-- Optimization Opportunities -->
    {% if correlation_insights.optimization_opportunities %}
    <div class="card mb-4">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="bi bi-lightbulb me-2"></i>Optimization Opportunities</h5>
        </div>
        <div class="card-body">
            {% for opportunity in correlation_insights.optimization_opportunities %}
            <div class="mb-3">
                <div class="d-flex justify-content-between align-items-start">
                    <div class="flex-grow-1">
                        <h6 class="mb-1">{{ opportunity.opportunity }}</h6>
                        <p class="mb-1">{{ opportunity.recommendation }}</p>
                        <small class="text-info">
                            <i class="bi bi-calendar me-1"></i>Periods Affected: {{ opportunity.periods_affected }}
                        </small>
                    </div>
                </div>
            </div>
            {% if not forloop.last %}<hr>{% endif %}
            {% endfor %}
        </div>
    </div>
    {% endif %}

    <!-- Enrollment Forecasting -->
    {% if forecasting_data %}
    <div class="card mb-4">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="bi bi-graph-up me-2"></i>Enrollment Forecasting</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Forecast Period</th>
                            <th class="text-center">Projected Enrollments</th>
                            <th class="text-end">Projected Revenue</th>
                            <th class="text-center">Confidence Level</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for forecast in forecasting_data %}
                        <tr>
                            <td><strong>{{ forecast.period }}</strong></td>
                            <td class="text-center">{{ forecast.projected_enrollments }}</td>
                            <td class="text-end">{{ forecast.projected_revenue|currency }}</td>
                            <td class="text-center">
                                <span class="badge {% if forecast.confidence_level >= 80 %}bg-success{% elif forecast.confidence_level >= 60 %}bg-warning{% else %}bg-danger{% endif %}">
                                    {{ forecast.confidence_level }}%
                                </span>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            <div class="alert alert-info mt-3">
                <small>
                    <i class="bi bi-info-circle me-2"></i>
                    Forecasts are based on historical enrollment trends and should be used as guidance only. 
                    Actual results may vary based on market conditions and institutional factors.
                </small>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Strategic Insights -->
    <div class="card">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="bi bi-info-circle me-2"></i>Strategic Insights</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h6>Enrollment Performance Assessment</h6>
                    <ul class="list-unstyled">
                        {% if financial_impact.enrollment_efficiency_score >= 80 %}
                        <li><i class="bi bi-check-circle text-success me-2"></i>
                            Excellent enrollment-to-revenue conversion efficiency
                        </li>
                        {% elif financial_impact.enrollment_efficiency_score >= 60 %}
                        <li><i class="bi bi-info-circle text-info me-2"></i>
                            Good enrollment performance with optimization opportunities
                        </li>
                        {% else %}
                        <li><i class="bi bi-exclamation-triangle text-warning me-2"></i>
                            Enrollment monetization needs significant improvement
                        </li>
                        {% endif %}
                        
                        {% if financial_impact.overall_collection_rate >= 80 %}
                        <li><i class="bi bi-check-circle text-success me-2"></i>
                            Strong collection rate from new enrollments
                        </li>
                        {% else %}
                        <li><i class="bi bi-exclamation-triangle text-warning me-2"></i>
                            Collection rate from new enrollments needs improvement
                        </li>
                        {% endif %}
                        
                        <li><i class="bi bi-people text-info me-2"></i>
                            {{ financial_impact.total_new_enrollments }} new students enrolled
                        </li>
                        
                        <li><i class="bi bi-currency-dollar text-success me-2"></i>
                            Average revenue per enrollment: {{ financial_impact.avg_revenue_per_enrollment|currency }}
                        </li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6>Strategic Recommendations</h6>
                    <ul class="list-unstyled">
                        {% if financial_impact.enrollment_efficiency_score < 60 %}
                        <li><i class="bi bi-arrow-right text-primary me-2"></i>
                            Optimize enrollment-to-revenue conversion processes
                        </li>
                        {% endif %}
                        
                        {% if financial_impact.overall_collection_rate < 80 %}
                        <li><i class="bi bi-arrow-right text-warning me-2"></i>
                            Implement proactive collection strategies for new enrollments
                        </li>
                        {% endif %}
                        
                        <li><i class="bi bi-arrow-right text-success me-2"></i>
                            Monitor enrollment trends for capacity planning
                        </li>
                        
                        <li><i class="bi bi-arrow-right text-info me-2"></i>
                            Analyze seasonal patterns for marketing optimization
                        </li>
                        
                        <li><i class="bi bi-arrow-right text-primary me-2"></i>
                            Develop enrollment forecasting models for budget planning
                        </li>
                        
                        <li><i class="bi bi-arrow-right text-secondary me-2"></i>
                            Regular enrollment impact reviews and adjustments
                        </li>
                    </ul>
                </div>
            </div>
            
            <hr>
            
            <div class="alert {% if financial_impact.enrollment_efficiency_score >= 80 %}alert-success{% elif financial_impact.enrollment_efficiency_score >= 60 %}alert-info{% else %}alert-warning{% endif %}">
                <h6 class="alert-heading">
                    <i class="bi {% if financial_impact.enrollment_efficiency_score >= 80 %}bi-check-circle{% elif financial_impact.enrollment_efficiency_score >= 60 %}bi-info-circle{% else %}bi-exclamation-triangle{% endif %} me-2"></i>
                    Enrollment Impact Summary
                </h6>
                <p class="mb-0">
                    {% if financial_impact.enrollment_efficiency_score >= 80 %}
                    Your enrollment impact is excellent with strong revenue conversion and collection rates. Continue current strategies and monitor for consistency.
                    {% elif financial_impact.enrollment_efficiency_score >= 60 %}
                    Your enrollment impact shows good performance with clear opportunities for optimization. Focus on improving collection rates and revenue per student.
                    {% else %}
                    Your enrollment impact requires significant attention. Implement comprehensive strategies to improve enrollment monetization and collection efficiency.
                    {% endif %}
                    Current efficiency score: {{ financial_impact.enrollment_efficiency_score|floatformat:0 }}/100 (Grade {{ financial_impact.enrollment_efficiency_grade }}).
                </p>
            </div>
        </div>
    </div>

    <!-- No Data Message -->
    {% if not enrollment_analysis %}
    <div class="card">
        <div class="card-body text-center py-5">
            <i class="bi bi-people display-1 text-muted mb-3"></i>
            <h4 class="text-muted">No Enrollment Data Available</h4>
            <p class="text-muted">No student enrollment data found for the selected criteria.</p>
            <div class="mt-3">
                <a href="{% url 'reporting:student_enrollment_impact_report' %}" class="btn btn-outline-primary me-2">
                    <i class="bi bi-arrow-clockwise me-2"></i>Reset Filters
                </a>
            </div>
        </div>
    </div>
    {% endif %}

</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Animate progress bars
    $('.progress-bar').each(function() {
        var $this = $(this);
        var width = $this.css('width');
        $this.css('width', '0%').animate({width: width}, 1500);
    });
    
    // Highlight declining trends
    $('.table-warning').hover(
        function() {
            $(this).addClass('table-active');
        },
        function() {
            $(this).removeClass('table-active');
        }
    );
    
    // Add click handlers for enrollment analysis rows
    $('tbody tr').on('click', function() {
        $(this).toggleClass('table-info');
    });
});
</script>
{% endblock %}
