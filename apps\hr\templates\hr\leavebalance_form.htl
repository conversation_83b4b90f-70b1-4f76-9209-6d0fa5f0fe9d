{# D:\school_fees_saas_v2\apps\hr\templates\hr\leavebalance_form.html #}
{% extends "tenant_base.html" %}
{% load static i18n widget_tweaks %}

{% block tenant_page_title %}{{ view_title }}{% endblock %}

{% block tenant_specific_content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-lg-8 col-md-10">

            {# Page Header and Breadcrumbs #}
            <div class="pagetitle mb-4">
                <h1 class="h3">{{ view_title }}</h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{% url 'schools:dashboard' %}">{% trans "Dashboard" %}</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'hr:hr_dashboard' %}">{% trans "HR" %}</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'hr:leave_balance_list' %}">{% trans "Manage Leave Balances" %}</a></li>
                        <li class="breadcrumb-item active" aria-current="page">{% trans "Edit" %}</li>
                    </ol>
                </nav>
            </div>

            {% include "partials/_messages.html" %}

            <div class="card shadow-sm">
                <div class="card-header">
                    <h5 class="mb-0">{% trans "Update Leave Entitlement" %}</h5>
                </div>
                <div class="card-body p-4">
                    <form method="post" novalidate>
                        {% csrf_token %}
                        
                        {# Display any non-field errors from the form's clean() method #}
                        {% for error in form.non_field_errors %}
                            <div class="alert alert-danger">{{ error }}</div>
                        {% endfor %}

                        {# --- Read-only Information --- #}
                        <div class="mb-3">
                            <label class="form-label">{% trans "Employee" %}</label>
                            <input type="text" class="form-control" value="{{ object.employee.user.get_full_name }}" disabled readonly>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">{% trans "Leave Type" %}</label>
                            <input type="text" class="form-control" value="{{ object.leave_type.name }}" disabled readonly>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">{% trans "Balance Year" %}</label>
                            <input type="text" class="form-control" value="{{ object.year }}" disabled readonly>
                        </div>
                        
                        {# --- Hidden fields that need to be submitted but not edited --- #}
                        {# This is important so the form can validate correctly #}
                        {{ form.employee.as_hidden }}
                        {{ form.leave_type.as_hidden }}
                        {{ form.year.as_hidden }}
                        
                        <hr>

                        {# --- Editable Field --- #}
                        <div class="mb-3">
                            <label for="{{ form.days_accrued.id_for_label }}" class="form-label required">
                                {{ form.days_accrued.label|default:"Days Accrued/Entitled" }}
                            </label>
                            {% render_field form.days_accrued class="form-control" %}
                            <div class="form-text">{{ form.days_accrued.help_text }}</div>
                            {% for error in form.days_accrued.errors %}
                                <div class="invalid-feedback d-block">{{ error }}</div>
                            {% endfor %}
                        </div>

                        {# --- Display Calculated Values for Context --- #}
                        <div class="alert alert-info">
                            <div class="row">
                                <div class="col-6">
                                    <strong>{% trans "Current Days Taken" %}:</strong> {{ object.days_taken }}
                                </div>
                                <div class="col-6">
                                    <strong>{% trans "Current Days Available" %}:</strong> {{ object.days_available }}
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-end gap-2 mt-4">
                            <a href="{% url 'hr:leavebalance_list' %}" class="btn btn-secondary">
                                <i class="bi bi-x-circle me-1"></i> {% trans "Cancel" %}
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-save-fill me-2"></i>{% trans "Update Balance" %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>

        </div>
    </div>
</div>
{% endblock tenant_specific_content %}
















{% comment %} {# D:\school_fees_saas_v2\apps\hr\templates\hr\leavebalance_form.html #}
{% extends "tenant_base.html" %}
{% load static i18n widget_tweaks %}

{% block tenant_page_title %}{{ view_title }}{% endblock %}

{% block tenant_specific_content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-lg-8 col-md-10">

="text-muted mb-0">
                        {% blocktrans with employee_name=object.employee.user.get_full_name leave_type=object.leave_type.name year=object.year %}
                        Adjusting balance for <strong>{{ employee_name }}</strong> - <strong>{{ leave_type }}</strong> for the year <strong>{{ year }}</strong>.
                        {% endblocktrans %}
                    </p>
                </div>
            </div>

            {% include "partials/_messages.html" %}

            <div class="card shadow-sm">
                <div class="card-body p-4">
                    <form method="post" novalidate>
                        {% csrf_token %}
                        
                        {# Display any non-field errors at the top of the form #}
                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {% for error in form.non_field_errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {%            {# Page Header and Breadcrumbs #}
            <div class="pagetitle mb-3">
                <h1>{{ view_title }}</h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{% url 'schools:dashboard' %}">{% trans "Dashboard" %}</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'hr:hr_dashboard' %}">{% trans "HR" %}</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'hr:leave_balance_list' %}">{% trans "Manage Balances" %}</a></li>
                        <li class="breadcrumb-item active" aria-current="page">{% trans "Edit" %}</li>
                    </ol>
                </nav>
            </div>

            {% include "partials/_messages.html" %}

            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0">{% trans "Editing Leave Balance" %}</h5>
                </div>
                <div class="card-body p-4">
                    
                    {# Display read-only information about the record being edited #}
                    <div class="alert alert-info" role="alert">
                        <div class="row">
                            <div class="col-md-4">
                                <strong>{% trans "Employee" %}:</strong><br>
                                {{ object.employee.user.get_full_name }} endif %}

                        {# --- READ-ONLY FIELDS --- #}
                        {# We display these as text, not as disabled form fields, for a cleaner look. #}
                        {# The actual form fields can be hidden if needed, but since we define #}
                        {# which fields are editable in the form class, this is safe. #}
                        <div class="mb-3">
                            <label class="form-label">{% trans "Employee" %}</label>
                            <input type="text" class="form-control" value="{{ object.employee.user.get_full_name }}" disabled readonly>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">{% trans "Leave Type" %}</label>
                                <input type="text" class="form-control" value="{{ object.leave_type.name }}" disabled readonly>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">{% trans "Year" %}</label>
                                <input type="text" class="form-control" value="{{ object.year }}" disabled readonly>
                            </div>
                        </div>

                        <hr>

                        {# --- EDITABLE FIELD --- #}
                        <div class="mb-3">
                            <label for="{{ form.days_accrued.id_for
                            </div>
                            <div class="col-md-4">
                                <strong>{% trans "Leave Type" %}:</strong><br>
                                {{ object.leave_type.name }}
                            </div>
                            <div class="col-md-4">
                                <strong>{% trans "For Year" %}:</strong><br>
                                {{ object.year }}
                            </div>
                        </div>
                    </div>

                    <form method="post" novalidate>
                        {% csrf_token %}
                        
                        {# Hide the fields that should not be edited on this form #}
                        <div style="display: none;">
                            {{ form.employee }}
                            {{ form.leave_type }}
                            {{ form.year }}
                        </div>

                        {# The only field the user should edit #}
                        <div class="mb-3">
                            <label for="{{ form.days_accrued.id_for_label }}" class="form-label required">
                                {{ form.days_accrued.label }}
                            </label>
                            {% render_field form.days_accrued class="form-control" %}
                            {% if form.days_accrued.help_text %}
                                <div class="form-text">{{ form.days_accrued.help_text }}</div>
                            {% endif %}
                            {% for error in form.days_accrued.errors %}
                                <div class="invalid-feedback d-block">{{ error }}</div>
                            {% endfor %}
                        </div>

                        {# Display any non-field errors from the form's clean() method #}
                        {% for error in form.non_field_errors %}
                            <div class="alert alert-danger mt-3">{{ error }}</div>
                        {% endfor %}
                        
                        <hr class="my-4">

                        <div class="d-flex justify-content-end gap-2">
                            <a href="{% url 'hr:leave_balance_list' %}" class="btn btn-secondary">
                                <i class="bi bi-x-circle me-1"></i> {% trans "Cancel" %}
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-save-fill me-2"></i>{% trans "Update Balance" %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock tenant_specific_content %}
 {% endcomment %}

