{% extends "tenant_base.html" %}
{% load humanize static core_tags reporting_filters %}

{% block title %}{{ view_title|default:"Financial Ratio Analysis Report" }}{% endblock %}

{% block content %}
<div class="container-fluid mt-3">
    {% with report_icon_class="bi bi-speedometer2" %}
        {% include "partials/_report_header.html" %}
    {% endwith %}

    {% include "partials/_messages.html" %}
    {% include "partials/_report_filter_form.html" with filter_form=filter_form report_code=report_code specific_filter_title="Financial Ratio Analysis Filters" %}

    <!-- Analysis Overview -->
    <div class="card mb-4">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="bi bi-graph-up me-2"></i>Financial Health Overview</h5>
        </div>
        <div class="card-body">
            <div class="row mb-3">
                <div class="col-md-12">
                    <p>
                        <strong>Analysis Period:</strong> {{ analysis_period|title_replace }} |
                        <strong>Categories:</strong> {{ ratio_categories|join:", "|title }} | 
                        <strong>Benchmarks:</strong> {% if benchmark_comparison %}Included{% else %}Not Included{% endif %}
                    </p>
                </div>
            </div>
            
            <!-- Risk Assessment Summary -->
            {% if risk_assessment %}
            <div class="row">
                <div class="col-md-4">
                    <div class="text-center">
                        <h6 class="text-muted">Overall Risk Level</h6>
                        <p class="mb-0 fw-bold {% if risk_assessment.risk_level == 'Low' %}text-success{% elif risk_assessment.risk_level == 'Medium' %}text-warning{% else %}text-danger{% endif %} fs-4">
                            {{ risk_assessment.risk_level }}
                        </p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="text-center">
                        <h6 class="text-muted">Risk Score</h6>
                        <p class="mb-0 fw-bold text-info">{{ risk_assessment.risk_score }}/{{ ratio_analysis|length|multiply:3 }}</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="text-center">
                        <h6 class="text-muted">Risk Factors</h6>
                        <p class="mb-0 fw-bold text-danger">{{ risk_assessment.risk_factors|length }}</p>
                    </div>
                </div>
            </div>
            
            <!-- Risk Level Progress Bar -->
            <div class="mt-3">
                <h6>Financial Health Score</h6>
                {% with max_score=ratio_analysis|length|multiply:3 health_percentage=risk_assessment.risk_score|percentage:max_score|subtract:100|make_positive %}
                <div class="progress" style="height: 25px;">
                    <div class="progress-bar {% if risk_assessment.risk_level == 'Low' %}bg-success{% elif risk_assessment.risk_level == 'Medium' %}bg-warning{% else %}bg-danger{% endif %}" 
                         role="progressbar" style="width: {{ health_percentage }}%">
                        {{ risk_assessment.risk_level }} Risk
                    </div>
                </div>
                {% endwith %}
                <small class="text-muted">
                    {% if risk_assessment.risk_level == 'Low' %}
                    Excellent financial health with strong ratios
                    {% elif risk_assessment.risk_level == 'Medium' %}
                    Moderate financial health - some areas need attention
                    {% else %}
                    Poor financial health - immediate action required
                    {% endif %}
                </small>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- Ratio Analysis by Category -->
    {% for category_key, category_data in ratio_analysis.items %}
    <div class="card mb-4">
        <div class="card-header bg-light d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                <i class="bi {% if category_key == 'liquidity' %}bi-droplet{% elif category_key == 'profitability' %}bi-graph-up{% elif category_key == 'efficiency' %}bi-speedometer{% elif category_key == 'leverage' %}bi-bar-chart{% else %}bi-trending-up{% endif %} me-2"></i>
                {{ category_data.category_name }}
            </h5>
            <span class="badge bg-primary">{{ category_data.ratios|length }} ratios</span>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-striped table-hover mb-0">
                    <thead class="table-dark">
                        <tr>
                            <th>Ratio</th>
                            <th class="text-end">Current Value</th>
                            <th class="text-end">Benchmark</th>
                            <th class="text-center">Performance</th>
                            <th class="text-center">Status</th>
                            <th>Interpretation</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for ratio in category_data.ratios %}
                        <tr class="{% if ratio.status == 'Poor' %}table-danger{% elif ratio.status == 'Fair' %}table-warning{% endif %}">
                            <td>
                                <strong>{{ ratio.name }}</strong>
                                <br><small class="text-muted">{{ ratio.description }}</small>
                            </td>
                            <td class="text-end">
                                <span class="fw-bold {% if ratio.status == 'Good' %}text-success{% elif ratio.status == 'Fair' %}text-warning{% else %}text-danger{% endif %}">
                                    {% if '%' in ratio.name %}
                                        {{ ratio.value|floatformat:1 }}%
                                    {% else %}
                                        {{ ratio.value|floatformat:2 }}
                                    {% endif %}
                                </span>
                            </td>
                            <td class="text-end">
                                <span class="text-muted">
                                    {% if '%' in ratio.name %}
                                        {{ ratio.benchmark|floatformat:1 }}%
                                    {% else %}
                                        {{ ratio.benchmark|floatformat:2 }}
                                    {% endif %}
                                </span>
                            </td>
                            <td class="text-center">
                                {% with performance_ratio=ratio.value|divide:ratio.benchmark|multiply:100 %}
                                <div class="progress" style="height: 20px; width: 100px;">
                                    <div class="progress-bar {% if ratio.status == 'Good' %}bg-success{% elif ratio.status == 'Fair' %}bg-warning{% else %}bg-danger{% endif %}" 
                                         role="progressbar" style="width: {% if performance_ratio > 100 %}100{% else %}{{ performance_ratio|floatformat:0 }}{% endif %}%">
                                    </div>
                                </div>
                                <small class="text-muted">{{ performance_ratio|floatformat:0 }}% of benchmark</small>
                                {% endwith %}
                            </td>
                            <td class="text-center">
                                {% if ratio.status == 'Good' %}
                                <span class="badge bg-success">{{ ratio.status }}</span>
                                {% elif ratio.status == 'Fair' %}
                                <span class="badge bg-warning">{{ ratio.status }}</span>
                                {% else %}
                                <span class="badge bg-danger">{{ ratio.status }}</span>
                                {% endif %}
                            </td>
                            <td>
                                <small class="text-muted">{{ ratio.interpretation }}</small>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    {% endfor %}

    <!-- Risk Assessment Details -->
    {% if risk_assessment and risk_assessment.risk_factors %}
    <div class="card mb-4">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="bi bi-shield-exclamation me-2"></i>Risk Assessment & Recommendations</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h6>Identified Risk Factors</h6>
                    {% if risk_assessment.risk_factors %}
                    <ul class="list-unstyled">
                        {% for factor in risk_assessment.risk_factors %}
                        <li><i class="bi bi-exclamation-triangle text-warning me-2"></i>{{ factor }}</li>
                        {% endfor %}
                    </ul>
                    {% else %}
                    <p class="text-success"><i class="bi bi-check-circle me-2"></i>No significant risk factors identified</p>
                    {% endif %}
                </div>
                <div class="col-md-6">
                    <h6>Strategic Recommendations</h6>
                    {% if risk_assessment.recommendations %}
                    <ul class="list-unstyled">
                        {% for recommendation in risk_assessment.recommendations %}
                        <li><i class="bi bi-arrow-right text-primary me-2"></i>{{ recommendation }}</li>
                        {% endfor %}
                    </ul>
                    {% else %}
                    <ul class="list-unstyled">
                        <li><i class="bi bi-arrow-right text-success me-2"></i>Continue monitoring financial ratios regularly</li>
                        <li><i class="bi bi-arrow-right text-info me-2"></i>Maintain current financial management practices</li>
                        <li><i class="bi bi-arrow-right text-primary me-2"></i>Consider opportunities for further optimization</li>
                    </ul>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Financial Health Summary -->
    {% if ratio_analysis %}
    <div class="card">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="bi bi-lightbulb me-2"></i>Financial Health Summary & Insights</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h6>Strengths</h6>
                    <ul class="list-unstyled">
                        {% for category_key, category_data in ratio_analysis.items %}
                        {% for ratio in category_data.ratios %}
                        {% if ratio.status == 'Good' %}
                        <li><i class="bi bi-check-circle text-success me-2"></i>
                            <strong>{{ ratio.name }}:</strong> {{ ratio.value|floatformat:1 }}{% if '%' in ratio.name %}%{% endif %} ({{ ratio.status }})
                        </li>
                        {% endif %}
                        {% endfor %}
                        {% endfor %}
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6>Areas for Improvement</h6>
                    <ul class="list-unstyled">
                        {% for category_key, category_data in ratio_analysis.items %}
                        {% for ratio in category_data.ratios %}
                        {% if ratio.status != 'Good' %}
                        <li><i class="bi bi-exclamation-triangle {% if ratio.status == 'Fair' %}text-warning{% else %}text-danger{% endif %} me-2"></i>
                            <strong>{{ ratio.name }}:</strong> {{ ratio.value|floatformat:1 }}{% if '%' in ratio.name %}%{% endif %} ({{ ratio.status }})
                        </li>
                        {% endif %}
                        {% endfor %}
                        {% endfor %}
                    </ul>
                </div>
            </div>
            
            <hr>
            
            <div class="row">
                <div class="col-md-12">
                    <h6>Key Insights</h6>
                    <div class="alert alert-info">
                        <p class="mb-2">
                            <strong>Overall Assessment:</strong> 
                            {% if risk_assessment.risk_level == 'Low' %}
                            Your institution demonstrates strong financial health with most ratios meeting or exceeding benchmarks.
                            {% elif risk_assessment.risk_level == 'Medium' %}
                            Your institution shows moderate financial health with some areas requiring attention to optimize performance.
                            {% else %}
                            Your institution faces financial challenges that require immediate strategic intervention.
                            {% endif %}
                        </p>
                        <p class="mb-0">
                            <strong>Next Steps:</strong> 
                            {% if risk_assessment.risk_level == 'Low' %}
                            Focus on maintaining current performance levels and exploring growth opportunities.
                            {% elif risk_assessment.risk_level == 'Medium' %}
                            Address identified weaknesses while building on existing strengths.
                            {% else %}
                            Prioritize immediate corrective actions for poor-performing ratios and develop a comprehensive improvement plan.
                            {% endif %}
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- No Data Message -->
    {% if not ratio_analysis %}
    <div class="card">
        <div class="card-body text-center py-5">
            <i class="bi bi-speedometer2 display-1 text-muted mb-3"></i>
            <h4 class="text-muted">No Financial Data Available</h4>
            <p class="text-muted">Insufficient financial data to calculate ratios for the selected period.</p>
            <div class="mt-3">
                <a href="{% url 'reporting:financial_ratio_analysis_report' %}" class="btn btn-outline-primary me-2">
                    <i class="bi bi-arrow-clockwise me-2"></i>Reset Filters
                </a>
            </div>
        </div>
    </div>
    {% endif %}

</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Animate progress bars
    $('.progress-bar').each(function() {
        var $this = $(this);
        var width = $this.css('width');
        $this.css('width', '0%').animate({width: width}, 1500);
    });
    
    // Highlight poor performing ratios
    $('.table-danger, .table-warning').hover(
        function() {
            $(this).addClass('table-active');
        },
        function() {
            $(this).removeClass('table-active');
        }
    );
    
    // Add click handlers for ratio rows
    $('tbody tr').on('click', function() {
        $(this).toggleClass('table-info');
    });
    
    // Add ratio category icons animation
    $('.card-header i').hover(
        function() {
            $(this).addClass('text-primary');
        },
        function() {
            $(this).removeClass('text-primary');
        }
    );
});
</script>
{% endblock %}

