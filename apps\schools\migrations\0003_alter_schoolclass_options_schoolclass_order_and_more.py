# Generated by Django 5.1.9 on 2025-07-17 20:43

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('schools', '0002_alter_staffuser_options_alter_staffuser_managers_and_more'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='schoolclass',
            options={'ordering': ['order', 'name'], 'verbose_name': 'School Class', 'verbose_name_plural': 'School Classes'},
        ),
        migrations.AddField(
            model_name='schoolclass',
            name='order',
            field=models.PositiveIntegerField(db_index=True, default=0, help_text='The numerical order for promotion (e.g., Nursery=0, Grade 1=1, Grade 2=2).'),
        ),
        migrations.AlterField(
            model_name='schoolclass',
            name='is_active',
            field=models.BooleanField(db_index=True, default=True, help_text='Is this class currently active and can have students enrolled?', verbose_name='is active'),
        ),
    ]
