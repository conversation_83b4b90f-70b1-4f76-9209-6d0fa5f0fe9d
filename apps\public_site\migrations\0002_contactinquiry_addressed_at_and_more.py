# Generated by Django 5.1.7 on 2025-07-11 18:57

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('public_site', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='contactinquiry',
            name='addressed_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='contactinquiry',
            name='addressed_by',
            field=models.CharField(blank=True, help_text='Staff member who addressed this', max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='contactinquiry',
            name='admin_response',
            field=models.TextField(blank=True, help_text='Admin response to the inquiry', null=True),
        ),
        migrations.AddField(
            model_name='contactinquiry',
            name='broadcast_excerpt',
            field=models.TextField(blank=True, help_text='Short excerpt for public display', null=True),
        ),
        migrations.AddField(
            model_name='contactinquiry',
            name='broadcast_title',
            field=models.Char<PERSON><PERSON>(blank=True, help_text='Custom title for public display', max_length=200, null=True),
        ),
        migrations.AddField(
            model_name='contactinquiry',
            name='inquiry_type',
            field=models.CharField(choices=[('general', 'General Inquiry'), ('support', 'Technical Support'), ('sales', 'Sales Question'), ('partnership', 'Partnership Opportunity'), ('feedback', 'Feedback & Suggestions'), ('demo', 'Request Demo'), ('other', 'Other')], default='general', max_length=20),
        ),
        migrations.AddField(
            model_name='contactinquiry',
            name='ip_address',
            field=models.GenericIPAddressField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='contactinquiry',
            name='is_approved_for_broadcast',
            field=models.BooleanField(default=False, help_text='Approve to display on public platform'),
        ),
        migrations.AddField(
            model_name='contactinquiry',
            name='is_featured',
            field=models.BooleanField(default=False, help_text='Feature this inquiry prominently'),
        ),
        migrations.AddField(
            model_name='contactinquiry',
            name='organization',
            field=models.CharField(blank=True, help_text='School or organization name', max_length=150, null=True),
        ),
        migrations.AddField(
            model_name='contactinquiry',
            name='phone',
            field=models.CharField(blank=True, help_text='Optional phone number', max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='contactinquiry',
            name='priority',
            field=models.CharField(choices=[('low', 'Low'), ('medium', 'Medium'), ('high', 'High'), ('urgent', 'Urgent')], default='medium', max_length=10),
        ),
        migrations.AddField(
            model_name='contactinquiry',
            name='response_sent_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='contactinquiry',
            name='user_agent',
            field=models.TextField(blank=True, null=True),
        ),
    ]
