# D:\school_fees_saas_v2\apps\parent_portal\urls.py

from django.urls import path
from . import views

app_name = 'parent_portal'

urlpatterns = [
    # --- Auth & Dashboard ---
    path('login/', views.parent_login_view, name='login'),
    path('logout/', views.parent_logout_view, name='logout'),
    path('dashboard/', views.ParentDashboardView.as_view(), name='dashboard'),

    # --- Children & Fees ---
    path('my-children/', views.MyChildrenListView.as_view(), name='my_children_list'),
    path('my-children/fees/', views.ChildrenFeesSummaryView.as_view(), name='children_fees_summary'),
    
    # --- Student Specific ---
    path('my-children/<int:student_pk>/detail/', views.ParentStudentDetailView.as_view(), name='student_detail'),
    path('my-children/<int:student_pk>/fees/', views.ParentStudentFeesView.as_view(), name='student_fees'),
    
    # This URL is for the payment history of a SINGLE student
    path('my-children/<int:student_pk>/payment-history/', views.ParentStudentPaymentHistoryView.as_view(), name='student_payment_history'),

    # --- Payment History for ALL Children (Combined) ---
    # The URL name 'payment_history_all' is used by the button on the dashboard.
    # We will point it to the SAME view 'ParentPaymentHistoryView', but without a student_pk.
    # The view will be updated to handle both cases.
    path('payment-history/', views.ParentPaymentHistoryView.as_view(), name='payment_history_all'),

    # --- Profile ---
    path('profile/', views.ParentProfileDisplayView.as_view(), name='profile_display'),
    path('profile/edit/', views.ParentProfileUpdateView.as_view(), name='profile_update'),

    # --- Invoice Views for Parents ---
    path('invoices/<int:pk>/', views.ParentInvoiceDetailView.as_view(), name='invoice_detail'),
    path('invoices/<int:pk>/pdf/', views.ParentInvoicePDFView.as_view(), name='invoice_pdf'),
    
    # --- Payment Processing Views ---
    path('pay/', views.SelectInvoicesForPaymentView.as_view(), name='select_invoices_for_payment'),
    path('pay/gateway/', views.MockPaymentGatewayView.as_view(), name='mock_payment_gateway'),
    path('pay/process/', views.ProcessMockPaymentView.as_view(), name='process_mock_payment'),
    path('pay/success/<int:payment_pk>/', views.MockPaymentSuccessView.as_view(), name='mock_payment_success'),
    path('pay/failure/', views.MockPaymentFailureView.as_view(), name='mock_payment_failure'),
    
    path('pay/initiate/', views.InitiatePaymentView.as_view(), name='initiate_payment'),
    path('pay/callback/', views.PaymentCallbackView.as_view(), name='payment_callback'),
    
]






# # apps/parent_portal/urls.py
# from django.urls import path
# from . import views

# app_name = 'parent_portal'

# urlpatterns = [
    
#     path('login/', views.parent_login_view, name='login'),
#     path('logout/', views.parent_logout_view, name='logout'),
#     path('dashboard/', views.ParentDashboardView.as_view(), name='dashboard'),
    
#     path('profile/', views.ParentProfileDisplayView.as_view(), name='profile_display'),
#     path('profile/edit/', views.ParentProfileUpdateView.as_view(), name='profile_update'),
    
#     path('my-children/', views.MyChildrenListView.as_view(), name='my_children_list'),
    
#     path('my-children/<int:student_pk>/fees/', views.ParentStudentFeesView.as_view(), name='student_fees'),


#     path('my-children/', views.MyChildrenListView.as_view(), name='my_children_list'),
#     path('my-children/fees/', views.ChildrenFeesSummaryView.as_view(), name='children_fees_summary'),
    
#     path('my-children/<int:student_pk>/detail/', views.ParentStudentDetailView.as_view(), name='student_detail'),
#     path('my-children/<int:student_pk>/fees/', views.ParentStudentFeesView.as_view(), name='student_fees'),

#     # --- THIS IS THE CORRECTED LINE ---
#     # Use the correct view name 'ParentPaymentHistoryView'
#     # And use the URL name 'student_payment_history' which is what your template likely expects
#     path('my-children/<int:student_pk>/payment-history/', views.ParentStudentPaymentHistoryView.as_view(), name='student_payment_history'),

#     path('payment-history/all/', views.ParentAllChildrenPaymentHistoryView.as_view(), name='payment_history_all'),
    
    
#     # # New URL for Children's Fees Summary
#     # path('children-fees-summary/', views.ChildrenFeesSummaryView.as_view(), name='children_fees_summary'),

#     # path('my-children/<int:student_pk>/fee-details/', views.ParentStudentDetailView.as_view(), name='student_fee_details'),
#     # path('my-children/<int:student_pk>/payment-history/', views.ParentStudentPaymentHistoryView.as_view(), name='student_payment_history_specific'), # For specific student detail page

#     # # path('payment-history/all/', views.ParentStudentPaymentHistoryView.as_view(), name='payment_history_list'), # General payment history for all children
    
#     # path('payment-history/all/', views.ParentAllChildrenPaymentHistoryView.as_view(), name='payment_history_list'),
    
#     # # Example for a direct student detail view if needed, distinct from fee details
#     # path('my-children/<int:student_pk>/details/', views.ParentStudentDetailView.as_view(), name='student_detail'),
    
    
#     # path('payment-history/all/', views.ParentAllChildrenPaymentHistoryView.as_view(), name='payment_history_all'),
    
#     # # Add this new URL pattern for making a summary payment
#     # path('payments/make-summary/', views.MakePaymentSummaryView.as_view(), name='make_payment_summary'),

#     path('profile/', views.ParentProfileDisplayView.as_view(), name='profile_display'), # From dashboard quick links

#     path('pay-fees/', views.SelectInvoicesForPaymentView.as_view(), name='select_invoices_for_payment'),
#     path('mock-payment-gateway/', views.MockPaymentGatewayView.as_view(), name='mock_payment_gateway'),
#     path('process-mock-payment/', views.ProcessMockPaymentView.as_view(), name='process_mock_payment'),
#     path('mock-payment-success/<int:payment_pk>/', views.TemplateView.as_view(template_name='parent_portal/mock_payment_success.html'), name='mock_payment_success'), # Simple TemplateView for now
#     path('mock-payment-failure/', views.TemplateView.as_view(template_name='parent_portal/mock_payment_failure.html'), name='mock_payment_failure'),

#     # Parent invoice viewing
#     path('invoices/<int:pk>/', views.ParentInvoiceDetailView.as_view(), name='invoice_detail'),
#     path('invoices/<int:pk>/pdf/', views.ParentInvoicePDFView.as_view(), name='invoice_pdf'),

# ]