<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>{{ report_title }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            margin: 0;
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
        }
        .school-name {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .report-title {
            font-size: 16px;
            font-weight: bold;
            margin: 10px 0;
        }
        .report-date {
            font-size: 12px;
            color: #666;
        }
        .summary-section {
            margin: 20px 0;
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
        }
        .summary-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
        }
        .summary-table th,
        .summary-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
        }
        .summary-table th {
            background-color: #e9ecef;
            font-weight: bold;
        }
        .current-amount {
            color: #28a745;
            font-weight: bold;
        }
        .overdue-amount {
            color: #dc3545;
            font-weight: bold;
        }
        .total-amount {
            color: #007bff;
            font-weight: bold;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 6px;
            text-align: left;
            font-size: 10px;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .text-right {
            text-align: right;
        }
        .text-center {
            text-align: center;
        }
        .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 10px;
            color: #666;
            border-top: 1px solid #ddd;
            padding-top: 10px;
        }
        .page-break {
            page-break-before: always;
        }
        .totals-row {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .vendor-name {
            font-weight: bold;
        }
        .contact-info {
            font-size: 9px;
            color: #666;
        }
        .overdue-row {
            background-color: #fff3cd;
        }
        .reference-badge {
            background-color: #6c757d;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 9px;
        }
        .category-badge {
            background-color: #f8f9fa;
            color: #333;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 9px;
            border: 1px solid #ddd;
        }
        .overdue-badge {
            background-color: #dc3545;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 9px;
        }
    </style>
</head>
<body>
    <!-- Header Section -->
    <div class="header">
        {% if school_profile %}
        <div class="school-name">{{ school_profile.school_name }}</div>
        {% if school_profile.address %}
        <div>{{ school_profile.address }}</div>
        {% endif %}
        {% if school_profile.phone %}
        <div>Phone: {{ school_profile.phone }}</div>
        {% endif %}
        {% if school_profile.email %}
        <div>Email: {{ school_profile.email }}</div>
        {% endif %}
        {% else %}
        <div class="school-name">{{ tenant.name }}</div>
        {% endif %}
        
        <div class="report-title">{{ report_title }}</div>
        <div class="report-date">
            As of Date: {{ as_of_date|date:"M d, Y" }}
        </div>
    </div>

    <!-- Summary Section -->
    <div class="summary-section">
        <h3>Payable Summary</h3>
        <table class="summary-table">
            <thead>
                <tr>
                    <th>Total Payable</th>
                    <th>Current Amount</th>
                    <th>Overdue Amount</th>
                    <th>Total Items</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td class="text-right total-amount">{{ total_payable|floatformat:2 }}</td>
                    <td class="text-right current-amount">{{ total_payable|subtract:overdue_total|floatformat:2 }}</td>
                    <td class="text-right overdue-amount">{{ overdue_total|floatformat:2 }}</td>
                    <td class="text-center">{{ payable_data|length }}</td>
                </tr>
            </tbody>
        </table>
    </div>

    <!-- Vendor Summary -->
    {% if vendor_summary %}
    <h3>Vendor Summary</h3>
    <table>
        <thead>
            <tr>
                <th style="width: 30%;">Vendor</th>
                <th style="width: 10%;" class="text-center">Invoices</th>
                <th style="width: 20%;" class="text-right">Total Amount</th>
                <th style="width: 20%;" class="text-right">Current</th>
                <th style="width: 20%;" class="text-right">Overdue</th>
            </tr>
        </thead>
        <tbody>
            {% for vendor in vendor_summary %}
            <tr>
                <td>
                    <div class="vendor-name">{{ vendor.vendor.name|default:"Unknown Vendor" }}</div>
                    {% if vendor.vendor.contact_person %}
                    <div class="contact-info">Contact: {{ vendor.vendor.contact_person }}</div>
                    {% endif %}
                </td>
                <td class="text-center">{{ vendor.invoice_count }}</td>
                <td class="text-right total-amount">{{ vendor.total_amount|floatformat:2 }}</td>
                <td class="text-right current-amount">{{ vendor.current_amount|floatformat:2 }}</td>
                <td class="text-right overdue-amount">{{ vendor.overdue_amount|floatformat:2 }}</td>
            </tr>
            {% endfor %}
        </tbody>
        <tfoot>
            <tr class="totals-row">
                <td><strong>Totals:</strong></td>
                <td class="text-center"><strong>{{ payable_data|length }}</strong></td>
                <td class="text-right total-amount"><strong>{{ total_payable|floatformat:2 }}</strong></td>
                <td class="text-right current-amount"><strong>{{ total_payable|subtract:overdue_total|floatformat:2 }}</strong></td>
                <td class="text-right overdue-amount"><strong>{{ overdue_total|floatformat:2 }}</strong></td>
            </tr>
        </tfoot>
    </table>
    {% endif %}

    <!-- Payable Details -->
    {% if payable_data %}
    <div class="page-break"></div>
    <h3>Payable Details</h3>
    <table>
        <thead>
            <tr>
                <th style="width: 15%;">Vendor</th>
                <th style="width: 10%;">Expense Date</th>
                <th style="width: 10%;">Due Date</th>
                <th style="width: 8%;" class="text-center">Days Overdue</th>
                <th style="width: 12%;" class="text-right">Amount</th>
                <th style="width: 12%;">Category</th>
                <th style="width: 10%;">Reference</th>
                <th style="width: 23%;">Description</th>
            </tr>
        </thead>
        <tbody>
            {% for item in payable_data %}
            <tr class="{% if item.is_overdue %}overdue-row{% endif %}">
                <td>
                    <div class="vendor-name">{{ item.vendor_name }}</div>
                    {% if item.expense.vendor.email %}
                    <div class="contact-info">{{ item.expense.vendor.email }}</div>
                    {% endif %}
                </td>
                <td>{{ item.expense.expense_date|date:"M d, Y" }}</td>
                <td>
                    {{ item.due_date|date:"M d, Y" }}
                    {% if item.is_overdue %}
                    <br><span class="overdue-badge">Overdue</span>
                    {% endif %}
                </td>
                <td class="text-center">
                    {% if item.is_overdue %}
                    <span class="overdue-badge">{{ item.days_overdue }}</span>
                    {% else %}
                    -
                    {% endif %}
                </td>
                <td class="text-right">
                    <span class="{% if item.is_overdue %}overdue-amount{% else %}total-amount{% endif %}">
                        {{ item.amount|floatformat:2 }}
                    </span>
                </td>
                <td>
                    <span class="category-badge">{{ item.expense.category.name }}</span>
                </td>
                <td>
                    {% if item.expense.reference_number %}
                    <span class="reference-badge">{{ item.expense.reference_number }}</span>
                    {% else %}
                    -
                    {% endif %}
                </td>
                <td>
                    {{ item.expense.description|truncatechars:40 }}
                    {% if item.expense.recorded_by %}
                    <br><span class="contact-info">By: {{ item.expense.recorded_by.get_full_name }}</span>
                    {% endif %}
                </td>
            </tr>
            {% endfor %}
        </tbody>
        <tfoot>
            <tr class="totals-row">
                <td colspan="4" class="text-right"><strong>Totals:</strong></td>
                <td class="text-right total-amount"><strong>{{ total_payable|floatformat:2 }}</strong></td>
                <td colspan="3"></td>
            </tr>
        </tfoot>
    </table>
    {% endif %}

    <!-- Analysis Section -->
    {% if payable_data %}
    <div style="margin-top: 30px; background-color: #f8f9fa; padding: 15px; border-radius: 5px;">
        <h3>Analysis</h3>
        <ul>
            <li><strong>Total Outstanding Payables:</strong> {{ total_payable|floatformat:2 }}</li>
            <li><strong>Number of Vendors:</strong> {{ vendor_summary|length }}</li>
            <li><strong>Number of Outstanding Items:</strong> {{ payable_data|length }}</li>
            <li><strong>Average Amount per Item:</strong> 
                {% if payable_data %}
                {{ total_payable|divide:payable_data|length|floatformat:2 }}
                {% else %}
                0.00
                {% endif %}
            </li>
            {% if overdue_total > 0 %}
            <li><strong>Overdue Percentage:</strong> {{ overdue_total|percentage:total_payable|floatformat:1 }}% of total payable</li>
            {% endif %}
        </ul>
        
        <div style="margin-top: 15px; padding: 10px; background-color: #e3f2fd; border-left: 4px solid #2196f3;">
            <strong>Note:</strong> This report shows expenses recorded without payment methods (indicating unpaid status). 
            Due dates are calculated as expense date + 30 days. Consider implementing a dedicated vendor invoice system for more accurate payable tracking.
        </div>
    </div>
    {% endif %}

    <!-- Footer -->
    <div class="footer">
        <p>Generated on {{ "now"|date:"F d, Y \a\\t g:i A" }} | {{ tenant.name }} | Accounts Payable Report</p>
    </div>
</body>
</html>
