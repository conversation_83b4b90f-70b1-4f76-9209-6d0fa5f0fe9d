#!/usr/bin/env python
"""
Clear Tenant Data
Removes all copied data from tenants while keeping the proper structure
Each tenant should have EMPTY tables ready for their own unique data

Usage: python clear_tenant_data.py
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from django.db import connection
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def clear_tenant_data(tenant_schema):
    """Clear all data from tenant while keeping structure"""
    
    logger.info(f"Clearing data from {tenant_schema}...")
    
    # Tables that should be completely cleared (user data)
    user_data_tables = [
        'students_student',
        'students_student_parents',
        'students_parentuser',
        'students_parentuser_groups',
        'students_parentuser_user_permissions',
        'schools_staffuser',
        'schools_staffuser_groups', 
        'schools_staffuser_user_permissions',
        'hr_employeeprofile',
        'hr_leavebalance',
        'hr_leaverequest',
        'hr_payrollrun',
        'hr_payslip',
        'hr_paysliplineitem',
        'hr_staffsalary',
        'hr_staffsalarystructure',
        'fees_invoice',
        'fees_invoicedetail',
        'fees_studentconcession',
        'fees_studentfeeallocation',
        'payments_payment',
        'payments_paymentallocation',
        'finance_budget',
        'finance_budgetamount',
        'finance_expense',
        'school_calendar_schoolevent',
        'school_calendar_eventattendee',
        'announcements_announcement',
        'announcements_announcement_target_tenant_staff_groups',
        'communication_communicationlog',
        'portal_admin_adminactivitylog',
        'django_admin_log',
        'schools_schoolprofile'
    ]
    
    # Tables that should keep essential system data but clear custom data
    system_tables_to_partial_clear = [
        'fees_feehead',
        'fees_feestructure', 
        'fees_feestructure_applicable_classes',
        'fees_feestructureitem',
        'finance_budgetitem',
        'finance_vendor',
        'schools_academicyear',
        'schools_term',
        'schools_schoolclass',
        'schools_section'
    ]
    
    try:
        with connection.cursor() as cursor:
            cursor.execute(f'SET search_path TO "{tenant_schema}"')
            
            # Clear all user data tables completely
            cleared_count = 0
            for table in user_data_tables:
                try:
                    # Check if table exists
                    cursor.execute(f"""
                        SELECT EXISTS (
                            SELECT FROM information_schema.tables 
                            WHERE table_schema = '{tenant_schema}' 
                            AND table_name = '{table}'
                        )
                    """)
                    
                    if cursor.fetchone()[0]:
                        # Clear all data
                        cursor.execute(f'DELETE FROM "{table}"')
                        
                        # Reset sequence if exists
                        try:
                            cursor.execute(f"SELECT setval('{table}_id_seq', 1, false)")
                        except:
                            pass
                        
                        cleared_count += 1
                        logger.info(f"✅ Cleared {table}")
                    
                except Exception as e:
                    logger.warning(f"⚠️  Could not clear {table}: {e}")
            
            # Clear custom data from system tables
            for table in system_tables_to_partial_clear:
                try:
                    cursor.execute(f"""
                        SELECT EXISTS (
                            SELECT FROM information_schema.tables 
                            WHERE table_schema = '{tenant_schema}' 
                            AND table_name = '{table}'
                        )
                    """)
                    
                    if cursor.fetchone()[0]:
                        cursor.execute(f'DELETE FROM "{table}"')
                        
                        # Reset sequence
                        try:
                            cursor.execute(f"SELECT setval('{table}_id_seq', 1, false)")
                        except:
                            pass
                        
                        logger.info(f"✅ Cleared {table}")
                    
                except Exception as e:
                    logger.warning(f"⚠️  Could not clear {table}: {e}")
            
            # Keep essential system tables but clear any custom entries
            # Keep: auth_group, auth_permission, django_content_type, django_migrations
            # Keep: fees_concessiontype, hr_leavetype, hr_salarycomponent, hr_statutorydeduction, hr_taxbracket
            # Keep: payments_paymentmethod, school_calendar_eventcategory
            # Keep: schools_academicsetting, schools_invoicesequence, schools_receiptsequence
            
            logger.info(f"✅ Cleared {cleared_count} user data tables from {tenant_schema}")
            logger.info(f"✅ {tenant_schema} now has clean structure ready for unique data")
            
    except Exception as e:
        logger.error(f"❌ Failed to clear data from {tenant_schema}: {e}")

def verify_data_cleared(tenant_schema):
    """Verify that user data has been cleared"""
    
    logger.info(f"Verifying {tenant_schema} data cleared...")
    
    # Key tables that should be empty
    should_be_empty = [
        'students_student',
        'schools_staffuser', 
        'fees_invoice',
        'payments_payment',
        'hr_employeeprofile',
        'schools_schoolprofile'
    ]
    
    try:
        with connection.cursor() as cursor:
            cursor.execute(f'SET search_path TO "{tenant_schema}"')
            
            all_empty = True
            
            for table in should_be_empty:
                try:
                    cursor.execute(f'SELECT COUNT(*) FROM "{table}"')
                    count = cursor.fetchone()[0]
                    
                    if count == 0:
                        logger.info(f"✅ {table}: Empty (correct)")
                    else:
                        logger.error(f"❌ {table}: {count} records (should be empty)")
                        all_empty = False
                        
                except Exception as e:
                    logger.warning(f"⚠️  Could not check {table}: {e}")
            
            if all_empty:
                logger.info(f"✅ {tenant_schema} verification PASSED - All user data cleared")
                return True
            else:
                logger.error(f"❌ {tenant_schema} verification FAILED - Some data remains")
                return False
                
    except Exception as e:
        logger.error(f"❌ Verification failed for {tenant_schema}: {e}")
        return False

def create_minimal_system_data(tenant_schema):
    """Create only the minimal system data needed for the tenant to function"""
    
    logger.info(f"Creating minimal system data for {tenant_schema}...")
    
    try:
        with connection.cursor() as cursor:
            cursor.execute(f'SET search_path TO "{tenant_schema}"')
            
            # Create minimal academic structure (empty, ready for customization)
            try:
                # Check if academic year exists, if not create a default one
                cursor.execute("SELECT COUNT(*) FROM schools_academicyear")
                if cursor.fetchone()[0] == 0:
                    cursor.execute("""
                        INSERT INTO schools_academicyear (name, start_date, end_date, is_active, is_current, created_at, updated_at) 
                        VALUES ('Academic Year', CURRENT_DATE, CURRENT_DATE + INTERVAL '1 year', TRUE, TRUE, NOW(), NOW())
                    """)
                    logger.info(f"✅ Created default academic year for {tenant_schema}")
            except Exception as e:
                logger.warning(f"Academic year creation: {e}")
            
            # Create minimal school settings
            try:
                cursor.execute("SELECT COUNT(*) FROM schools_academicsetting")
                if cursor.fetchone()[0] == 0:
                    cursor.execute("""
                        INSERT INTO schools_academicsetting (academic_year_id, created_at, updated_at) 
                        VALUES (1, NOW(), NOW())
                    """)
                    logger.info(f"✅ Created academic settings for {tenant_schema}")
            except Exception as e:
                logger.warning(f"Academic settings creation: {e}")
            
            # Create invoice and receipt sequences
            try:
                cursor.execute("SELECT COUNT(*) FROM schools_invoicesequence")
                if cursor.fetchone()[0] == 0:
                    cursor.execute("""
                        INSERT INTO schools_invoicesequence (prefix, current_number, created_at, updated_at) 
                        VALUES ('INV', 1, NOW(), NOW())
                    """)
                    logger.info(f"✅ Created invoice sequence for {tenant_schema}")
            except Exception as e:
                logger.warning(f"Invoice sequence creation: {e}")
            
            try:
                cursor.execute("SELECT COUNT(*) FROM schools_receiptsequence")
                if cursor.fetchone()[0] == 0:
                    cursor.execute("""
                        INSERT INTO schools_receiptsequence (prefix, current_number, created_at, updated_at) 
                        VALUES ('RCP', 1, NOW(), NOW())
                    """)
                    logger.info(f"✅ Created receipt sequence for {tenant_schema}")
            except Exception as e:
                logger.warning(f"Receipt sequence creation: {e}")
            
            logger.info(f"✅ Minimal system data created for {tenant_schema}")
            
    except Exception as e:
        logger.error(f"❌ Failed to create minimal system data for {tenant_schema}: {e}")

def main():
    """Main function"""
    logger.info("=== CLEARING TENANT DATA ===")
    logger.info("Removing all copied data while keeping proper structure")
    logger.info("Each tenant will have empty tables ready for unique data")
    
    try:
        # Clear data from all tenants (NOT alpha)
        tenant_schemas = ['mandiva', 'aischool', 'bea', 'ruzivo']
        
        success_count = 0
        
        for tenant_schema in tenant_schemas:
            logger.info(f"\n{'='*50}")
            logger.info(f"CLEARING {tenant_schema.upper()}")
            logger.info(f"{'='*50}")
            
            # Clear data
            clear_tenant_data(tenant_schema)
            
            # Create minimal system data
            create_minimal_system_data(tenant_schema)
            
            # Verify
            if verify_data_cleared(tenant_schema):
                success_count += 1
        
        logger.info(f"\n{'='*80}")
        logger.info("DATA CLEARING SUMMARY")
        logger.info(f"{'='*80}")
        logger.info(f"✅ Successfully cleared: {success_count}/{len(tenant_schemas)}")
        
        if success_count == len(tenant_schemas):
            logger.info("🎉 ALL TENANT DATA SUCCESSFULLY CLEARED!")
            logger.info("Each tenant now has:")
            logger.info("- Complete database structure (same as alpha)")
            logger.info("- Empty tables ready for unique data")
            logger.info("- No shared data between tenants")
            logger.info("- Minimal system data for functionality")
            logger.info("- Ready for independent data population")
        else:
            logger.error(f"⚠️  {len(tenant_schemas) - success_count} tenant(s) failed clearing")
        
        logger.info("\n🎯 NEXT STEPS:")
        logger.info("1. Each tenant can now be populated with unique data")
        logger.info("2. No data overlap between tenants")
        logger.info("3. Each tenant operates independently")
        logger.info("4. Structure is consistent across all tenants")
        
        return success_count == len(tenant_schemas)
        
    except Exception as e:
        logger.error(f"Data clearing failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
