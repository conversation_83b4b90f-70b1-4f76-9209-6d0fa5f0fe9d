{# D:\school_fees_saas_v2\templates\parent_portal\profile_form.html #}
{% extends "parent_portal/parent_portal_base.html" %} {# Or "tenant_base.html" #}

{% load static widget_tweaks %}

{% block parent_portal_page_title %}{{ view_title|default:"Edit My Profile" }}{% endblock %}

{% block parent_portal_main_content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-7">
            <div class="card shadow-sm">
                <div class="card-header bg-info text-white">
                    <h4 class="mb-0">{{ view_title|default:"Edit My Profile" }}</h4>
                </div>
                <div class="card-body p-4">
                    {% include "partials/_messages.html" %}

                    <form method="post" enctype="multipart/form-data"> {# enctype for profile_picture #}
                        {% csrf_token %}

                        {# Render using as_p for simplicity, or manually for more control #}
                        {{ form.as_p }}

                        <div class="mt-4">
                            <button type="submit" class="btn btn-info text-white">Save Profile Changes</button>
                            <a href="{% url 'parent_portal:dashboard' %}" class="btn btn-outline-secondary ms-2">Cancel</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock parent_portal_main_content %}