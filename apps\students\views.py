# D:\school_fees_saas_v2\apps\students\views.py
from django.shortcuts import render, redirect, get_object_or_404
from django.views.generic import ListView, DetailView, CreateView, UpdateView, DeleteView, View
from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin # For login and permission protection
from django.contrib.messages.views import SuccessMessageMixin
from django.urls import reverse_lazy, reverse
from django.contrib import messages
from django.db.models import Q, Prefetch, ExpressionWrapper, F, DecimalField # For complex lookups and annotations
from django.db import transaction, connection
from django.http import JsonResponse # For AJAX

from apps.schools.models import StaffUser

from .models import Student, ParentUser # Import both Student and ParentUser models
from .forms import StudentForm #, StudentFilterForm # StudentFilterForm for ListView

from apps.fees.models import AcademicYear

from apps.schools.models import SchoolClass, Section # For form querysets and filtering
# Import models for fee allocation and concession display if needed on detail page
from apps.fees.models import StudentFeeAllocation, StudentConcession, Invoice, FeeStructure, ConcessionType
from apps.fees.forms import StudentFeeAllocationForm, StudentConcessionForm

from .forms import StudentForm, ParentUserChangeForm, ParentUserCreationForm # StudentFilterForm,

from datetime import date
import logging

from django.utils.translation import gettext_lazy as _
from apps.common.mixins import TenantLoginRequiredMixin, TenantPermissionRequiredMixin

logger = logging.getLogger(__name__)

# from django.db.models import Prefetch # Only if still needed elsewhere
from datetime import date # For age calculation
import logging # For logging
from apps.fees.models import Invoice
from .models import Student
from .filters import StudentFilterForm


from django.shortcuts import render, get_object_or_404, redirect
from django.views.generic import ListView, DetailView, UpdateView, CreateView
from django.contrib.auth.mixins import LoginRequiredMixin # Use your StaffLoginRequiredMixin
from apps.common.mixins import StaffLoginRequiredMixin, TenantPermissionRequiredMixin # Your actual mixins
from .models import Student, ParentUser # Assuming ParentUser is here for now
from .forms import StudentForm, StudentParentLinkForm # We'll define StudentParentLinkForm soon


from django.db import models

# Helper function for age calculation
def calculate_student_age(birth_date):
    if not birth_date:
        return None # Or "N/A"
    today = date.today()
    try:
        age = today.year - birth_date.year - ((today.month, today.day) < (birth_date.month, birth_date.day))
        return age
    except AttributeError: # In case birth_date is not a proper date object
        logger.warning(f"Could not calculate age for birth_date: {birth_date} (type: {type(birth_date)})")
        return None



# D:\school_fees_saas_v2\apps\students\views.py

import logging
from decimal import Decimal
from datetime import date # For calculate_student_age

from django import forms
from django.db import models # For isinstance(filtered_qs, models.QuerySet)
from django.contrib import messages
from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin
# from apps.common.mixins import StaffLoginRequiredMixin, TenantPermissionRequiredMixin # Use these if they are your standard
from django.contrib.messages.views import SuccessMessageMixin
from django.db.models import Prefetch
from django.http import HttpResponseForbidden # Or your custom permission denied handling
from django.shortcuts import redirect, get_object_or_404
from django.urls import reverse_lazy, reverse
from django.utils.translation import gettext_lazy as _
from django.views.generic import ListView, DetailView, CreateView, UpdateView, DeleteView

# Model & Form Imports
from .models import Student, ParentUser # Assuming ParentUser is defined here or in users app
from .filters import StudentFilterForm, ParentUserFilterForm
from .forms import StudentForm, StudentParentLinkForm # StudentParentLinkForm to be created
# from apps.schools.models import AcademicYear, SchoolClass, Section # For type hints and choices if needed
# from apps.fees.models import Invoice, StudentFeeAllocation, StudentConcession, FeeStructure, ConcessionType
# from apps.common.utils import calculate_student_age # If in common utils



# --- Utility Functions (if not imported) ---
def calculate_student_age(dob):
    if not dob:
        return None
    today = date.today()
    try:
        age = today.year - dob.year - ((today.month, today.day) < (dob.month, dob.day))
        return age
    except AttributeError: # dob might not be a date object
        return None

# --- Student CRUD Views ---

class StudentListView(LoginRequiredMixin, PermissionRequiredMixin, ListView): # Replace with StaffLoginRequiredMixin, TenantPermissionRequiredMixin
    login_url = reverse_lazy('schools:staff_login')
    permission_required = 'students.view_student'
    model = Student
    template_name = 'students/student_list.html'
    context_object_name = 'students'
    paginate_by = 20
    filterset_class = StudentFilterForm

    def get(self, request, *args, **kwargs):
        """Handle GET requests including export functionality."""
        export_type = request.GET.get('export')
        if export_type in ['csv', 'excel', 'pdf']:
            return self.handle_export(request, export_type)
        return super().get(request, *args, **kwargs)

    def get_queryset(self):
        logger.debug(f"StudentListView ({self.__class__.__name__}): get_queryset called. Request GET: {self.request.GET}")

        base_queryset = super().get_queryset().filter(is_active=True).select_related( # Default to active students
            'current_class', 'current_section'
        ).prefetch_related(
            Prefetch('parents', queryset=ParentUser.objects.filter(is_active=True)) # Prefetch active parents
        )

        self.filterset = self.filterset_class(
            self.request.GET,
            queryset=base_queryset,
            request=self.request
        )

        logger.debug(f"StudentListView: Filterset instance: {self.filterset}, Form type: {type(self.filterset.form)}")
        logger.debug(f"StudentListView: Is self.filterset.form a Form instance? {isinstance(self.filterset.form, forms.Form)}")

        filtered_qs = self.filterset.qs.order_by(
            'current_class__name', 'current_section__name', 'last_name', 'first_name'
        )

        logger.debug(f"StudentListView: Filtered queryset count: {filtered_qs.count() if isinstance(filtered_qs, models.QuerySet) else 'N/A'}")
        return filtered_qs

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = _("Manage Students")

        if hasattr(self, 'filterset'):
            context['filter_form'] = self.filterset.form
            if not isinstance(context['filter_form'], forms.Form):
                logger.error(f"StudentListView: CRITICAL - context['filter_form'] IS NOT A DJANGO FORM! Type: {type(context['filter_form'])}")
            else:
                logger.info(f"StudentListView: context['filter_form'] IS a Django Form instance.")
        else:
            logger.error("StudentListView: self.filterset NOT available in get_context_data. Recreating (this is a fallback).")
            base_qs_for_context = super().get_queryset().filter(is_active=True).select_related('current_class', 'current_section').prefetch_related('parents')
            self.filterset = self.filterset_class(self.request.GET, queryset=base_qs_for_context, request=self.request)
            context['filter_form'] = self.filterset.form

        students_on_page = context.get(self.context_object_name)
        if students_on_page:
            for student_obj in students_on_page: # Renamed to avoid conflict with model name
                if student_obj.date_of_birth:
                    student_obj.calculated_age = calculate_student_age(student_obj.date_of_birth)
                else:
                    student_obj.calculated_age = None

        # Add filter parameters for export links
        if self.request.GET:
            query_params = self.request.GET.copy()
            if 'page' in query_params:
                del query_params['page']
            context['filter_params'] = query_params.urlencode()

        logger.debug(f"StudentListView: Final context keys: {list(context.keys())}")
        return context

    def handle_export(self, request, export_type):
        """
        Handle export requests for students.
        """
        # Get the filtered queryset for export
        queryset = super().get_queryset().filter(is_active=True).select_related(
            'current_class', 'current_section'
        ).prefetch_related(
            Prefetch('parents', queryset=ParentUser.objects.filter(is_active=True))
        )

        # Apply filtering
        filter_obj = StudentFilterForm(request.GET, queryset=queryset, request=request)
        filtered_queryset = filter_obj.qs.order_by(
            'current_class__name', 'current_section__name', 'last_name', 'first_name'
        )

        if export_type == 'csv':
            return self.export_to_csv(filtered_queryset, request)
        elif export_type == 'excel':
            return self.export_to_excel(filtered_queryset, request)
        elif export_type == 'pdf':
            return self.export_to_pdf(filtered_queryset, request)

        # Fallback to normal view
        return super().get(request, *args, **kwargs)

    def export_to_csv(self, queryset, request):
        """Export students to CSV format."""
        import csv
        from django.http import HttpResponse
        from django.utils import timezone

        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = f'attachment; filename="students_{timezone.now().strftime("%Y%m%d_%H%M%S")}.csv"'

        writer = csv.writer(response)

        # Write headers
        headers = [
            'Admission Number', 'First Name', 'Middle Name', 'Last Name', 'Full Name',
            'Date of Birth', 'Age', 'Gender', 'Class', 'Section', 'Status',
            'Date of Admission', 'Primary Guardian', 'Guardian Relationship',
            'Guardian Phone', 'Guardian Email', 'Address', 'Emergency Contact'
        ]
        writer.writerow(headers)

        # Write data
        for student in queryset:
            # Calculate age
            age = calculate_student_age(student.date_of_birth) if student.date_of_birth else ''

            # Get primary guardian info
            primary_guardian = student.parents.filter(is_active=True).first()
            guardian_name = primary_guardian.get_full_name() if primary_guardian else ''
            guardian_relationship = primary_guardian.get_relationship_display() if primary_guardian else ''
            guardian_phone = primary_guardian.phone_number if primary_guardian else ''
            guardian_email = primary_guardian.email if primary_guardian else ''

            writer.writerow([
                student.admission_number,
                student.first_name,
                student.middle_name,
                student.last_name,
                student.full_name,
                student.date_of_birth.strftime('%Y-%m-%d') if student.date_of_birth else '',
                age,
                student.get_gender_display(),
                student.current_class.name if student.current_class else '',
                student.current_section.name if student.current_section else '',
                student.get_status_display(),
                student.date_of_admission.strftime('%Y-%m-%d') if student.date_of_admission else '',
                guardian_name,
                guardian_relationship,
                guardian_phone,
                guardian_email,
                student.address or '',
                student.emergency_contact_name or ''
            ])

        return response

    def export_to_excel(self, queryset, request):
        """Export students to Excel format."""
        import openpyxl
        from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
        from django.http import HttpResponse
        from django.utils import timezone

        response = HttpResponse(content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        response['Content-Disposition'] = f'attachment; filename="students_{timezone.now().strftime("%Y%m%d_%H%M%S")}.xlsx"'

        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "Students"

        # Define styles
        header_font = Font(bold=True, color="FFFFFF")
        header_fill = PatternFill(start_color="007bff", end_color="007bff", fill_type="solid")
        header_alignment = Alignment(horizontal="center", vertical="center")
        border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )

        # Headers
        headers = [
            'Admission Number', 'First Name', 'Middle Name', 'Last Name', 'Full Name',
            'Date of Birth', 'Age', 'Gender', 'Class', 'Section', 'Status',
            'Date of Admission', 'Primary Guardian', 'Guardian Relationship',
            'Guardian Phone', 'Guardian Email', 'Address', 'Emergency Contact'
        ]

        # Write headers
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = header_alignment
            cell.border = border

        # Write data
        for row, student in enumerate(queryset, 2):
            age = calculate_student_age(student.date_of_birth) if student.date_of_birth else ''
            primary_guardian = student.parents.filter(is_active=True).first()
            guardian_name = primary_guardian.get_full_name() if primary_guardian else ''
            guardian_relationship = primary_guardian.get_relationship_display() if primary_guardian else ''
            guardian_phone = primary_guardian.phone_number if primary_guardian else ''
            guardian_email = primary_guardian.email if primary_guardian else ''

            data = [
                student.admission_number,
                student.first_name,
                student.middle_name,
                student.last_name,
                student.full_name,
                student.date_of_birth.strftime('%Y-%m-%d') if student.date_of_birth else '',
                age,
                student.get_gender_display(),
                student.current_class.name if student.current_class else '',
                student.current_section.name if student.current_section else '',
                student.get_status_display(),
                student.date_of_admission.strftime('%Y-%m-%d') if student.date_of_admission else '',
                guardian_name,
                guardian_relationship,
                guardian_phone,
                guardian_email,
                student.address or '',
                student.emergency_contact_name or ''
            ]

            for col, value in enumerate(data, 1):
                cell = ws.cell(row=row, column=col, value=value)
                cell.border = border

        # Auto-adjust column widths
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            ws.column_dimensions[column_letter].width = adjusted_width

        wb.save(response)
        return response

    def export_to_pdf(self, queryset, request):
        """Export students to PDF format."""
        from django.template.loader import render_to_string
        from django.http import HttpResponse
        from django.utils import timezone
        from xhtml2pdf import pisa
        from apps.schools.models import SchoolProfile

        # Get school profile
        try:
            school_profile = SchoolProfile.objects.first()
        except:
            school_profile = None

        # Calculate statistics
        total_students = queryset.count()
        active_students = queryset.filter(status='ACTIVE').count()
        male_students = queryset.filter(gender='MALE').count()
        female_students = queryset.filter(gender='FEMALE').count()

        # Get unique classes
        classes = queryset.values_list('current_class__name', flat=True).distinct()
        total_classes = len([c for c in classes if c])

        # Build filters applied text
        filters_applied = []
        if request.GET.get('name'):
            filters_applied.append(f"Name/Admission: {request.GET.get('name')}")
        if request.GET.get('current_class'):
            try:
                from apps.academic.models import SchoolClass
                class_obj = SchoolClass.objects.get(pk=request.GET.get('current_class'))
                filters_applied.append(f"Class: {class_obj.name}")
            except:
                pass
        if request.GET.get('current_section'):
            try:
                from apps.academic.models import Section
                section_obj = Section.objects.get(pk=request.GET.get('current_section'))
                filters_applied.append(f"Section: {section_obj.name}")
            except:
                pass
        if request.GET.get('status'):
            filters_applied.append(f"Status: {request.GET.get('status')}")

        filters_text = ", ".join(filters_applied) if filters_applied else None

        # Add calculated age to students
        students_with_age = []
        for student in queryset:
            student.calculated_age = calculate_student_age(student.date_of_birth) if student.date_of_birth else None
            students_with_age.append(student)

        context = {
            'students': students_with_age,
            'school_profile': school_profile,
            'current_date': timezone.now(),
            'total_students': total_students,
            'active_students': active_students,
            'male_students': male_students,
            'female_students': female_students,
            'total_classes': total_classes,
            'filters_applied': filters_text,
        }

        html_string = render_to_string('students/student_list_pdf.html', context)

        response = HttpResponse(content_type='application/pdf')
        response['Content-Disposition'] = f'attachment; filename="students_{timezone.now().strftime("%Y%m%d_%H%M%S")}.pdf"'

        pisa_status = pisa.CreatePDF(html_string, dest=response)

        if pisa_status.err:
            return HttpResponse('We had some errors <pre>' + html_string + '</pre>')

        return response


class StudentImportView(LoginRequiredMixin, PermissionRequiredMixin, View):
    """Handle student import from Excel files."""
    login_url = reverse_lazy('schools:staff_login')
    permission_required = 'students.add_student'

    def post(self, request, *args, **kwargs):
        """Handle Excel file upload and import."""
        import openpyxl
        from django.http import JsonResponse
        from django.core.files.storage import default_storage
        from django.core.files.base import ContentFile
        import tempfile
        import os

        try:
            # Get uploaded file
            uploaded_file = request.FILES.get('import_file')
            if not uploaded_file:
                return JsonResponse({
                    'success': False,
                    'message': 'No file uploaded.'
                })

            # Validate file type
            if not uploaded_file.name.endswith(('.xlsx', '.xls')):
                return JsonResponse({
                    'success': False,
                    'message': 'Invalid file type. Please upload an Excel file (.xlsx or .xls).'
                })

            # Validate file size (10MB limit)
            if uploaded_file.size > 10 * 1024 * 1024:
                return JsonResponse({
                    'success': False,
                    'message': 'File size too large. Maximum size is 10MB.'
                })

            # Save file temporarily
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.xlsx')
            for chunk in uploaded_file.chunks():
                temp_file.write(chunk)
            temp_file.close()

            # Process Excel file
            try:
                workbook = openpyxl.load_workbook(temp_file.name)
                worksheet = workbook.active

                # Get options
                update_existing = request.POST.get('update_existing') == '1'
                validate_only = request.POST.get('validate_only') == '1'

                # Process import
                result = self.process_excel_import(worksheet, update_existing, validate_only)

                return JsonResponse(result)

            except Exception as e:
                return JsonResponse({
                    'success': False,
                    'message': f'Error processing Excel file: {str(e)}'
                })
            finally:
                # Clean up temporary file
                if os.path.exists(temp_file.name):
                    os.unlink(temp_file.name)

        except Exception as e:
            return JsonResponse({
                'success': False,
                'message': f'Import failed: {str(e)}'
            })

    def process_excel_import(self, worksheet, update_existing=False, validate_only=False):
        """Process the Excel worksheet and import students."""
        from apps.academic.models import SchoolClass, Section
        from django.db import transaction

        imported_count = 0
        updated_count = 0
        skipped_count = 0
        errors = []

        # Expected headers (must match template)
        expected_headers = [
            'admission_number', 'first_name', 'middle_name', 'last_name',
            'date_of_birth', 'gender', 'class_name', 'section_name',
            'status', 'address', 'emergency_contact_name', 'emergency_contact_phone'
        ]

        # Get headers from first row
        headers = []
        for col in range(1, worksheet.max_column + 1):
            cell_value = worksheet.cell(row=1, column=col).value
            if cell_value:
                headers.append(str(cell_value).lower().strip().replace(' ', '_'))

        # Validate headers
        missing_headers = [h for h in expected_headers[:4] if h not in headers]  # Required fields
        if missing_headers:
            return {
                'success': False,
                'message': f'Missing required columns: {", ".join(missing_headers)}'
            }

        # Process each row
        for row_num in range(2, worksheet.max_row + 1):
            try:
                # Extract data from row
                row_data = {}
                for col, header in enumerate(headers, 1):
                    if col <= worksheet.max_column:
                        cell_value = worksheet.cell(row=row_num, column=col).value
                        row_data[header] = str(cell_value).strip() if cell_value else ''

                # Skip empty rows
                if not any(row_data.values()):
                    continue

                # Validate required fields
                if not row_data.get('admission_number') or not row_data.get('first_name') or not row_data.get('last_name'):
                    errors.append(f'Row {row_num}: Missing required fields (admission_number, first_name, last_name)')
                    skipped_count += 1
                    continue

                # Process student data
                if not validate_only:
                    with transaction.atomic():
                        student, created = self.create_or_update_student(row_data, update_existing)
                        if created:
                            imported_count += 1
                        elif student:
                            updated_count += 1
                        else:
                            skipped_count += 1
                else:
                    # Just validate
                    imported_count += 1

            except Exception as e:
                errors.append(f'Row {row_num}: {str(e)}')
                skipped_count += 1

        return {
            'success': True,
            'imported_count': imported_count,
            'updated_count': updated_count,
            'skipped_count': skipped_count,
            'errors': errors[:10],  # Limit errors shown
            'message': f'Import completed. {imported_count} imported, {updated_count} updated, {skipped_count} skipped.'
        }

    def create_or_update_student(self, row_data, update_existing):
        """Create or update a student from row data."""
        from apps.academic.models import SchoolClass, Section
        from datetime import datetime

        admission_number = row_data.get('admission_number')

        # Check if student exists
        try:
            student = Student.objects.get(admission_number=admission_number)
            if not update_existing:
                return None, False  # Skip existing
        except Student.DoesNotExist:
            student = Student(admission_number=admission_number)

        # Set basic fields
        student.first_name = row_data.get('first_name', '')
        student.middle_name = row_data.get('middle_name', '')
        student.last_name = row_data.get('last_name', '')

        # Parse date of birth
        dob_str = row_data.get('date_of_birth', '')
        if dob_str:
            try:
                student.date_of_birth = datetime.strptime(dob_str, '%Y-%m-%d').date()
            except:
                try:
                    student.date_of_birth = datetime.strptime(dob_str, '%m/%d/%Y').date()
                except:
                    pass  # Skip invalid dates

        # Set gender
        gender = row_data.get('gender', '').upper()
        if gender in ['MALE', 'FEMALE', 'OTHER']:
            student.gender = gender

        # Set class and section
        class_name = row_data.get('class_name', '')
        if class_name:
            try:
                school_class = SchoolClass.objects.get(name__iexact=class_name)
                student.current_class = school_class
            except SchoolClass.DoesNotExist:
                pass

        section_name = row_data.get('section_name', '')
        if section_name and student.current_class:
            try:
                section = Section.objects.get(name__iexact=section_name, school_class=student.current_class)
                student.current_section = section
            except Section.DoesNotExist:
                pass

        # Set status
        status = row_data.get('status', '').upper()
        if status in ['ACTIVE', 'INACTIVE', 'SUSPENDED', 'GRADUATED', 'LEFT_SCHOOL']:
            student.status = status
        else:
            student.status = 'ACTIVE'  # Default

        # Set other fields
        student.address = row_data.get('address', '')
        student.emergency_contact_name = row_data.get('emergency_contact_name', '')
        student.emergency_contact_phone = row_data.get('emergency_contact_phone', '')

        # Save student
        created = student.pk is None
        student.save()

        return student, created


class DownloadImportTemplateView(LoginRequiredMixin, PermissionRequiredMixin, View):
    """Download Excel template for student import."""
    login_url = reverse_lazy('schools:staff_login')
    permission_required = 'students.view_student'

    def get(self, request, *args, **kwargs):
        """Generate and return Excel template."""
        import openpyxl
        from openpyxl.styles import Font, PatternFill, Alignment
        from django.http import HttpResponse

        # Create workbook
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "Student Import Template"

        # Define styles
        header_font = Font(bold=True, color="FFFFFF")
        header_fill = PatternFill(start_color="007bff", end_color="007bff", fill_type="solid")
        header_alignment = Alignment(horizontal="center", vertical="center")

        # Headers
        headers = [
            'admission_number', 'first_name', 'middle_name', 'last_name',
            'date_of_birth', 'gender', 'class_name', 'section_name',
            'status', 'address', 'emergency_contact_name', 'emergency_contact_phone'
        ]

        # Write headers
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = header_alignment

        # Add sample data
        sample_data = [
            ['STU001', 'John', 'Michael', 'Doe', '2010-05-15', 'MALE', 'Grade 1', 'A', 'ACTIVE', '123 Main St', 'Jane Doe', '+1234567890'],
            ['STU002', 'Jane', '', 'Smith', '2009-08-22', 'FEMALE', 'Grade 2', 'B', 'ACTIVE', '456 Oak Ave', 'Bob Smith', '+0987654321']
        ]

        for row, data in enumerate(sample_data, 2):
            for col, value in enumerate(data, 1):
                ws.cell(row=row, column=col, value=value)

        # Auto-adjust column widths
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 30)
            ws.column_dimensions[column_letter].width = adjusted_width

        # Create response
        response = HttpResponse(content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        response['Content-Disposition'] = 'attachment; filename="student_import_template.xlsx"'

        wb.save(response)
        return response


class StudentDetailView(LoginRequiredMixin, PermissionRequiredMixin, DetailView): # Replace with StaffLoginRequiredMixin, TenantPermissionRequiredMixin
    login_url = reverse_lazy('schools:staff_login')
    permission_required = 'students.view_student'
    model = Student
    template_name = 'students/student_detail.html'
    context_object_name = 'student'

    def get_queryset(self):
        # Your existing comprehensive queryset with select_related and prefetch_related
        # is good. Ensure all related models (StudentFeeAllocation, Invoice, ConcessionType etc.)
        # are imported at the top of the file if you uncomment these prefetches.
        return super().get_queryset().select_related(
            'current_class', 'current_section', 'created_by'
        ).prefetch_related(
            Prefetch('parents', queryset=ParentUser.objects.filter(is_active=True).only('id', 'email', 'first_name', 'last_name', 'phone_number')),
            # Prefetch('assigned_fees', queryset=StudentFeeAllocation.objects.select_related(
            #     'fee_structure__academic_year', 'fee_structure__term', 
            #     'academic_year', 'term'
            # ).order_by('-academic_year__start_date', '-term__start_date')),
            # Prefetch('concessions', queryset=StudentConcession.objects.select_related(
            #     'concession_type', 'academic_year', 'specific_fee_head'
            # ).order_by('-academic_year__start_date', 'concession_type__name')),
            # Prefetch('invoices', queryset=Invoice.objects.filter(
            #     status__in=[Invoice.InvoiceStatus.SENT, Invoice.InvoiceStatus.PARTIALLY_PAID, Invoice.InvoiceStatus.OVERDUE]
            # ).select_related(
            #     'academic_year', 'term', 'fee_structure'
            # ).order_by('-issue_date')) 
        )

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        student = self.object
        context['view_title'] = f"Student Details: {student.get_full_name()}"
        context['page_title'] = student.get_full_name()
        context['linked_parents'] = student.parents.all() # For display

        logger.debug(f"StudentDetailView: Context prepared for student {student.pk}.")
        return context

class StudentCreateView(LoginRequiredMixin, PermissionRequiredMixin, SuccessMessageMixin, CreateView): # Replace with Staff mixins
    model = Student
    form_class = StudentForm
    template_name = 'students/student_form.html'
    permission_required = 'students.add_student'
    success_url = reverse_lazy('students:student_list')
    success_message = _("Student '%(first_name)s %(last_name)s' (%(admission_number)s) created successfully.")

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['tenant'] = self.request.tenant # Pass tenant to form if needed
        return kwargs

    def form_valid(self, form):
        # --- USAGE LIMIT ENFORCEMENT ---
        # from apps.subscriptions.models import Subscription # Ensure import
        subscription = getattr(self.request.tenant, 'subscription', None)
        if subscription and subscription.is_usable and hasattr(subscription.plan, 'max_students') and subscription.plan.max_students is not None:
            # Count active students for the current tenant
            current_student_count = Student.objects.filter(is_active=True).count() 
            if current_student_count >= subscription.plan.max_students:
                error_message = _(
                    "Cannot add new student. You have reached the maximum of %(max_students)s active students allowed for your current plan (%(plan_name)s). "
                    "Please archive inactive students or upgrade your plan."
                ) % {'max_students': subscription.plan.max_students, 'plan_name': subscription.plan.name}
                messages.error(self.request, error_message)
                logger.warning(f"Tenant '{self.request.tenant.schema_name}': Student creation blocked. Max students ({subscription.plan.max_students}) reached for plan '{subscription.plan.name}'. Current: {current_student_count}.")
                return self.form_invalid(form)
        # --- END USAGE LIMIT ENFORCEMENT ---

        # Set created_by
        # from apps.schools.models import StaffUser # Ensure import
        if StaffUser and isinstance(self.request.user, StaffUser):
            form.instance.created_by = self.request.user
            logger.info(f"Student '{form.cleaned_data.get('first_name')} {form.cleaned_data.get('last_name')}' being created by STAFF: '{self.request.user.email}' in tenant '{self.request.tenant.schema_name}'.")
        else:
            logger.warning(f"StudentCreateView: created_by not set. request.user is not StaffUser or StaffUser model not loaded. User: {self.request.user}")
        
        self.object = form.save() # Save the object
        # SuccessMessageMixin will use self.object for formatting the message
        return super().form_valid(form) # This calls form.save() again, ensure form_class.save() is idempotent or handle it.
                                        # A common pattern is:
                                        # self.object = form.save()
                                        # return HttpResponseRedirect(self.get_success_url())
                                        # But SuccessMessageMixin hooks into form_valid.

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = _("Add New Student")
        context['form_mode'] = "create"
        subscription = getattr(self.request.tenant, 'subscription', None)
        if subscription and subscription.is_usable and hasattr(subscription.plan, 'max_students') and subscription.plan.max_students is not None:
            context['current_student_count'] = Student.objects.filter(is_active=True).count()
            context['max_students_limit'] = subscription.plan.max_students
        logger.debug("StudentCreateView: Context prepared.")
        return context

class StudentUpdateView(LoginRequiredMixin, PermissionRequiredMixin, SuccessMessageMixin, UpdateView): # Replace with Staff mixins
    model = Student
    form_class = StudentForm
    template_name = 'students/student_form.html'
    context_object_name = 'student'
    permission_required = 'students.change_student'
    success_message = _("Student '%(first_name)s %(last_name)s' (%(admission_number)s) updated successfully.")

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['tenant'] = self.request.tenant
        return kwargs

    def get_success_url(self):
        return reverse('students:student_detail', kwargs={'pk': self.object.pk})

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = f"Edit Student: {self.object.get_full_name()}"
        context['form_mode'] = "update"
        logger.debug(f"StudentUpdateView: Context for editing student {self.object.pk}.")
        return context

class StudentDeleteView(LoginRequiredMixin, PermissionRequiredMixin, DeleteView): # Replace with Staff mixins
    model = Student
    template_name = 'students/student_confirm_delete.html'
    success_url = reverse_lazy('students:student_list')
    context_object_name = 'student'
    permission_required = 'students.delete_student'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = f"Confirm Delete: {self.object.get_full_name()}"
        return context

    def form_valid(self, form): # Overriding form_valid to add message *before* deletion by super()
        student_name = self.object.get_full_name() # Get name before object is deleted
        # Add pre-delete checks here:
        # if self.object.invoices.exists(): # Ensure Invoice related_name is 'invoices'
        #     messages.error(self.request, _(f"Cannot delete student '{student_name}' as they have associated invoices. Please clear or reassign invoices first."))
        #     return redirect('students:student_detail', pk=self.object.pk)
        
        response = super().form_valid(form) # Performs the delete
        messages.success(self.request, _(f"Student '{student_name}' deleted successfully."))
        logger.info(f"Student '{student_name}' (PK: {self.object.pk if hasattr(self, 'object') else 'UnknownPK_deleted'}) DELETED by user '{self.request.user.email}' in tenant '{self.request.tenant.schema_name}'.")
        return response

# --- View for Managing Student-Parent Links ---
class StudentManageParentsView(StaffLoginRequiredMixin, TenantPermissionRequiredMixin, SuccessMessageMixin, UpdateView): # Replace with Staff mixins
    model = Student
    form_class = StudentParentLinkForm 
    template_name = 'students/student_manage_parents.html' 
    context_object_name = 'student'
    permission_required = 'students.change_student' # Or a more specific permission
    # For success_message to work with M2M changes, often need to ensure form.save() triggers it correctly
    # or set it manually in form_valid before super().
    success_message = _("Parent links for student '%(full_name)s' updated successfully.")
    # Let's try to make it work with the object's name.
    
    def get_success_message(self, cleaned_data):
        return _("Parent links for student '%(full_name)s' updated successfully.") % {'full_name': self.object.get_full_name()}


    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['request_get'] = self.request.GET # Pass GET params to form for search query
        return kwargs

    def get_success_url(self):
        return reverse('students:student_detail', kwargs={'pk': self.object.pk})

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = f"Manage Parents for: {self.object.get_full_name()}"
        # The form instance (self.get_form()) will have the search_query populated
        # if it was in request.GET, because get_form_kwargs passes request.GET to form.
        return context

    def form_valid(self, form):
        # When form_class is a ModelForm and Meta.fields includes the M2M field ('parents'),
        # super().form_valid(form) calls form.save(), which handles the M2M update.
        logger.info(f"Updating parent links for student '{self.object.get_full_name()}'.")
        return super().form_valid(form)
    
    
# TODO:
# - ParentUser CRUD views (ParentUserListView, ParentUserCreateView, etc.)
# - ParentUserManageChildrenView (mirror of StudentManageParentsView, from Parent's perspective)
# - Refine StudentParentLinkForm and its template for better UX (e.g., Select2, search for parents)


def load_sections_for_class(request):
    class_id = request.GET.get('class_id')
    sections_data = []
    if class_id:
        # This query will be tenant-scoped automatically
        sections = Section.objects.filter(school_class_id=class_id).order_by('name')
        sections_data = list(sections.values('id', 'name'))
    return JsonResponse(sections_data, safe=False)



from django.http import HttpResponse, HttpResponseRedirect
from django.contrib.auth.decorators import login_required, permission_required
from django.urls import reverse
from django.contrib import messages

@login_required
@permission_required('students.delete_student', raise_exception=True) # Example permission
def student_bulk_delete_view(request):
    if request.method == 'POST':
        student_ids = request.POST.getlist('student_ids_to_delete') # Assuming checkboxes have this name
        if student_ids:
            # students_to_delete = Student.objects.filter(pk__in=student_ids, school=request.tenant.school) # Example: ensure tenant scope
            # count = students_to_delete.count()
            # students_to_delete.delete()
            # messages.success(request, f"Successfully deleted {count} students.")
            messages.info(request, "Bulk delete functionality (placeholder): Would delete students.") # Placeholder
            return HttpResponseRedirect(reverse('students:student_list'))
        else:
            messages.warning(request, "No students selected for deletion.")
    # Redirect back to list if not POST or no IDs
    return HttpResponseRedirect(reverse('students:student_list'))




from .forms import StudentParentLinkForm # Your form
from .models import Student # Your Student model

class StudentManageParentsView(StaffLoginRequiredMixin, TenantPermissionRequiredMixin, SuccessMessageMixin, UpdateView):
    model = Student
    form_class = StudentParentLinkForm 
    template_name = 'students/student_manage_parents.html' 
    context_object_name = 'student'
    permission_required = 'students.change_student' 
    
    # This class attribute is now just a fallback or not used if get_success_message is overridden
    # success_message = _("Parent links for student '%(full_name)s' updated successfully.") 

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        return kwargs

    def get_success_url(self):
        return reverse('students:student_detail', kwargs={'pk': self.object.pk})

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = f"Manage Parents for: {self.object.get_full_name()}"
        return context

    def form_valid(self, form):
        logger.info(
            f"StudentManageParentsView: form_valid for student '{self.object.get_full_name()}' (PK: {self.object.pk}). "
            f"User '{self.request.user.email}' submitted new parent links: "
            f"{[p.email for p in form.cleaned_data.get('parents', [])]}"
        )
        # super().form_valid(form) calls form.save() and sets self.object
        # It then calls get_success_message() and adds the message.
        return super().form_valid(form)

    def get_success_message(self, cleaned_data):
        # self.object is the Student instance that was just saved by form.save()
        # which is called within super().form_valid()
        student_name = self.object.get_full_name() # Assuming Student model has get_full_name()
        return _("Parent links for student '%(student_name)s' updated successfully.") % {'student_name': student_name}
   
    
    
# apps/students/views.py
class ParentUserListView(StaffLoginRequiredMixin, TenantPermissionRequiredMixin, ListView):
    model = ParentUser
    template_name = 'students/parentuser_list.html'
    context_object_name = 'parents'
    paginate_by = 20
    permission_required = 'students.view_parentuser'
    filterset_class = ParentUserFilterForm

    def get(self, request, *args, **kwargs):
        """Handle GET requests including export functionality."""
        export_type = request.GET.get('export')
        if export_type in ['csv', 'excel', 'pdf']:
            return self.handle_export(request, export_type)
        return super().get(request, *args, **kwargs)

    def get_queryset(self):
        logger.debug(f"ParentUserListView: get_queryset called. Request GET: {self.request.GET}")

        base_queryset = super().get_queryset().filter(is_active=True).select_related().prefetch_related(
            'children'  # Prefetch linked children
        ).order_by('last_name', 'first_name')

        self.filterset = self.filterset_class(
            self.request.GET,
            queryset=base_queryset,
            request=self.request
        )

        return self.filterset.qs

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = "Manage Parent Accounts"
        context['filter_form'] = self.filterset.form if hasattr(self, 'filterset') else None

        # Add filter parameters for export links
        filter_params = self.request.GET.copy()
        if 'export' in filter_params:
            del filter_params['export']
        context['filter_params'] = filter_params.urlencode()

        return context

    def handle_export(self, request, export_type):
        """Handle different export types."""
        try:
            if export_type == 'csv':
                return self.export_to_csv(request)
            elif export_type == 'excel':
                return self.export_to_excel(request)
            elif export_type == 'pdf':
                return self.export_to_pdf(request)
        except Exception as e:
            logger.error(f"Export error ({export_type}): {str(e)}")
            messages.error(request, f"Export failed: {str(e)}")
            return redirect('students:parentuser_list')

    def export_to_csv(self, request):
        """Export parent list to CSV."""
        import csv
        from django.http import HttpResponse

        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename="parent_list.csv"'

        writer = csv.writer(response)

        # Write header
        writer.writerow([
            'Full Name', 'Email', 'Phone Number', 'Status', 'Date Joined',
            'Children Count', 'Children Names'
        ])

        # Get filtered queryset
        queryset = self.get_queryset()

        # Write data
        for parent in queryset:
            children_names = ', '.join([child.get_full_name() for child in parent.children.all()])
            writer.writerow([
                parent.get_full_name(),
                parent.email,
                parent.phone_number or 'N/A',
                'Active' if parent.is_active else 'Inactive',
                parent.date_joined.strftime('%Y-%m-%d'),
                parent.children.count(),
                children_names or 'None'
            ])

        return response

    def export_to_excel(self, request):
        """Export parent list to Excel."""
        from openpyxl import Workbook
        from openpyxl.styles import Font, PatternFill, Alignment
        from django.http import HttpResponse

        # Create workbook and worksheet
        wb = Workbook()
        ws = wb.active
        ws.title = "Parent List"

        # Define styles
        header_font = Font(bold=True, color="FFFFFF")
        header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        header_alignment = Alignment(horizontal="center", vertical="center")

        # Write headers
        headers = [
            'Full Name', 'Email', 'Phone Number', 'Status', 'Date Joined',
            'Children Count', 'Children Names'
        ]

        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = header_alignment

        # Get filtered queryset
        queryset = self.get_queryset()

        # Write data
        for row, parent in enumerate(queryset, 2):
            children_names = ', '.join([child.get_full_name() for child in parent.children.all()])

            ws.cell(row=row, column=1, value=parent.get_full_name())
            ws.cell(row=row, column=2, value=parent.email)
            ws.cell(row=row, column=3, value=parent.phone_number or 'N/A')
            ws.cell(row=row, column=4, value='Active' if parent.is_active else 'Inactive')
            ws.cell(row=row, column=5, value=parent.date_joined.strftime('%Y-%m-%d'))
            ws.cell(row=row, column=6, value=parent.children.count())
            ws.cell(row=row, column=7, value=children_names or 'None')

        # Auto-adjust column widths
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            ws.column_dimensions[column_letter].width = adjusted_width

        # Create response
        response = HttpResponse(
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = 'attachment; filename="parent_list.xlsx"'

        wb.save(response)
        return response

    def export_to_pdf(self, request):
        """Export parent list to PDF."""
        from django.template.loader import render_to_string
        from django.http import HttpResponse
        from xhtml2pdf import pisa
        from django.utils import timezone

        # Get filtered queryset
        queryset = self.get_queryset()

        # Calculate statistics
        total_parents = queryset.count()
        active_parents = queryset.filter(is_active=True).count()
        inactive_parents = total_parents - active_parents
        parents_with_children = queryset.filter(children__isnull=False).distinct().count()
        parents_without_children = total_parents - parents_with_children

        # Get applied filters
        applied_filters = {}
        for key, value in self.request.GET.items():
            if key != 'export' and value:
                applied_filters[key.replace('_', ' ').title()] = value

        # Prepare context
        context = {
            'parents': queryset,
            'current_datetime': timezone.now(),
            'total_parents': total_parents,
            'active_parents': active_parents,
            'inactive_parents': inactive_parents,
            'parents_with_children': parents_with_children,
            'parents_without_children': parents_without_children,
            'applied_filters': applied_filters,
            'request': request,
        }

        # Render template
        html_string = render_to_string('students/parent_list_pdf.html', context)

        # Create PDF
        response = HttpResponse(content_type='application/pdf')
        response['Content-Disposition'] = 'attachment; filename="parent_list.pdf"'

        pisa_status = pisa.CreatePDF(html_string, dest=response)

        if pisa_status.err:
            logger.error(f"PDF generation error: {pisa_status.err}")
            messages.error(request, "PDF generation failed.")
            return redirect('students:parentuser_list')

        return response


class ParentUserImportView(StaffLoginRequiredMixin, TenantPermissionRequiredMixin, View):
    """Handle parent import from Excel files."""
    permission_required = 'students.add_parentuser'

    def post(self, request, *args, **kwargs):
        """Process uploaded Excel file and import parents."""
        import openpyxl
        from django.db import transaction
        from django.http import JsonResponse

        try:
            # Get uploaded file
            uploaded_file = request.FILES.get('import_file')
            if not uploaded_file:
                return JsonResponse({'error': 'No file uploaded'}, status=400)

            # Validate file type
            if not uploaded_file.name.endswith(('.xlsx', '.xls')):
                return JsonResponse({'error': 'Invalid file type. Please upload an Excel file.'}, status=400)

            # Get import options
            import_mode = request.POST.get('import_mode', 'create_only')
            send_welcome_emails = request.POST.get('send_welcome_emails') == 'on'

            # Load workbook
            try:
                workbook = openpyxl.load_workbook(uploaded_file)
                worksheet = workbook.active
            except Exception as e:
                return JsonResponse({'error': f'Error reading Excel file: {str(e)}'}, status=400)

            # Initialize counters
            total_processed = 0
            created_count = 0
            updated_count = 0
            skipped_count = 0
            error_count = 0
            errors = []

            # Process rows (skip header)
            with transaction.atomic():
                for row_num, row in enumerate(worksheet.iter_rows(min_row=2, values_only=True), start=2):
                    if not any(row):  # Skip empty rows
                        continue

                    total_processed += 1

                    try:
                        # Extract data from row
                        email = row[0] if row[0] else None
                        first_name = row[1] if row[1] else ''
                        last_name = row[2] if row[2] else ''
                        phone_number = row[3] if row[3] else ''
                        status = row[4] if row[4] else 'Active'

                        # Validate required fields
                        if not email:
                            errors.append(f"Row {row_num}: Email is required")
                            error_count += 1
                            continue

                        if not first_name or not last_name:
                            errors.append(f"Row {row_num}: First name and last name are required")
                            error_count += 1
                            continue

                        # Validate email format
                        from django.core.validators import validate_email
                        from django.core.exceptions import ValidationError
                        try:
                            validate_email(email)
                        except ValidationError:
                            errors.append(f"Row {row_num}: Invalid email format")
                            error_count += 1
                            continue

                        # Check if parent exists
                        existing_parent = ParentUser.objects.filter(email=email).first()

                        if existing_parent:
                            if import_mode == 'create_only':
                                skipped_count += 1
                                continue
                            elif import_mode == 'update_existing':
                                # Update existing parent
                                existing_parent.first_name = first_name
                                existing_parent.last_name = last_name
                                existing_parent.phone_number = phone_number
                                existing_parent.is_active = status.lower() == 'active'

                                if import_mode != 'validate_only':
                                    existing_parent.save()
                                    updated_count += 1
                            elif import_mode == 'validate_only':
                                # Just validate, don't save
                                continue
                        else:
                            # Create new parent
                            if import_mode != 'validate_only':
                                new_parent = ParentUser.objects.create_user(
                                    email=email,
                                    first_name=first_name,
                                    last_name=last_name,
                                    phone_number=phone_number,
                                    is_active=status.lower() == 'active',
                                    password=ParentUser.objects.make_random_password()
                                )
                                created_count += 1

                                # TODO: Send welcome email if requested
                                # if send_welcome_emails:
                                #     send_parent_welcome_email(new_parent)

                    except Exception as e:
                        errors.append(f"Row {row_num}: {str(e)}")
                        error_count += 1
                        continue

            # Prepare response
            response_data = {
                'total_processed': total_processed,
                'created_count': created_count,
                'updated_count': updated_count,
                'skipped_count': skipped_count,
                'error_count': error_count,
                'errors': errors[:10],  # Limit to first 10 errors
            }

            if import_mode == 'validate_only':
                response_data['message'] = 'Validation completed successfully'
            else:
                response_data['message'] = 'Import completed successfully'

            return JsonResponse(response_data)

        except Exception as e:
            logger.error(f"Parent import error: {str(e)}")
            return JsonResponse({'error': f'Import failed: {str(e)}'}, status=500)


class DownloadParentImportTemplateView(StaffLoginRequiredMixin, TenantPermissionRequiredMixin, View):
    """Generate and download Excel template for parent import."""
    permission_required = 'students.add_parentuser'

    def get(self, request, *args, **kwargs):
        """Generate Excel template with sample data."""
        from openpyxl import Workbook
        from openpyxl.styles import Font, PatternFill, Alignment
        from django.http import HttpResponse

        # Create workbook
        wb = Workbook()
        ws = wb.active
        ws.title = "Parent Import Template"

        # Define styles
        header_font = Font(bold=True, color="FFFFFF")
        header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        header_alignment = Alignment(horizontal="center", vertical="center")

        # Headers
        headers = [
            'Email (Required)', 'First Name (Required)', 'Last Name (Required)',
            'Phone Number', 'Status (Active/Inactive)'
        ]

        # Write headers
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = header_alignment

        # Add sample data
        sample_data = [
            ['<EMAIL>', 'John', 'Doe', '+1234567890', 'Active'],
            ['<EMAIL>', 'Jane', 'Smith', '+0987654321', 'Active'],
            ['<EMAIL>', 'Bob', 'Johnson', '', 'Inactive'],
        ]

        for row_num, row_data in enumerate(sample_data, 2):
            for col, value in enumerate(row_data, 1):
                ws.cell(row=row_num, column=col, value=value)

        # Auto-adjust column widths
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 30)
            ws.column_dimensions[column_letter].width = adjusted_width

        # Create response
        response = HttpResponse(
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = 'attachment; filename="parent_import_template.xlsx"'

        wb.save(response)
        return response


class ParentUserCreateView(StaffLoginRequiredMixin, TenantPermissionRequiredMixin, SuccessMessageMixin, CreateView):
    model = ParentUser
    form_class = ParentUserCreationForm
    template_name = 'students/parentuser_form.html' # Ensure this template exists and is correctly structured
    permission_required = 'students.add_parentuser' # Make sure this permission exists and is assigned to relevant staff roles
    success_message = _("Parent account for '%(email)s' created successfully.")
    # login_url can be inherited from StaffLoginRequiredMixin if defined there, or set explicitly:
    # login_url = reverse_lazy('schools:staff_login')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = _("Create New Parent Account")
        context['form_mode'] = "create" # Useful for the template to adapt (e.g., button text)

        student_pk_to_link = self.request.GET.get('student_pk_to_link')
        if student_pk_to_link:
            try:
                # Ensure student exists and belongs to the current tenant if relevant
                # For now, just fetching by PK. Add tenant check if Student has FK to tenant.
                context['linking_to_student'] = Student.objects.get(pk=student_pk_to_link)
                logger.debug(f"ParentUserCreateView: Context - Will attempt to link to student PK {student_pk_to_link}")
            except Student.DoesNotExist:
                logger.warning(f"ParentUserCreateView: Context - Student PK {student_pk_to_link} provided in GET params not found.")
                messages.warning(self.request, _("The student specified for linking could not be found."))
            except ValueError: # If student_pk_to_link is not a valid integer
                logger.warning(f"ParentUserCreateView: Context - Invalid Student PK '{student_pk_to_link}' provided in GET params.")
                messages.warning(self.request, _("Invalid student identifier provided for linking."))

        return context

    def form_valid(self, form):
        logger.info(
            f"ParentUserCreateView: Form IS VALID. Parent account for '{form.cleaned_data.get('email')}' "
            f"being CREATED by staff '{self.request.user.email}' in tenant '{self.request.tenant.schema_name}'."
        )
        # The form's save() method (specifically the overridden one in ParentUserCreationForm)
        # should handle password hashing and setting any default flags like is_active.
        self.object = form.save() # This will call ParentUserCreationForm.save()

        # SuccessMessageMixin will display self.success_message after redirect.
        # The auto-linking logic is now moved to get_success_url, which is cleaner as
        # form_valid should primarily focus on saving the main object.
        return super().form_valid(form) # This calls get_success_url indirectly

    def form_invalid(self, form):
        logger.warning(f"ParentUserCreateView: Form IS INVALID. Errors: {form.errors.as_json()}")
        messages.error(self.request, _("Please correct the errors below to create the parent account."))
        return super().form_invalid(form)


    def get_success_url(self):
        # self.object is the newly created ParentUser instance, set by form_valid
        next_url_param = self.request.GET.get('next')
        student_pk_to_link = self.request.GET.get('student_pk_to_link')

        # Auto-link student if parameters are present and valid
        if student_pk_to_link and self.object and self.object.pk:
            try:
                student_pk_int = int(student_pk_to_link) # Ensure it's an integer
                student_to_link = Student.objects.get(pk=student_pk_int)
                
                # Add parent to student's parents M2M
                # Assuming Student.parents is the M2M field to ParentUser
                student_to_link.parents.add(self.object)
                logger.info(f"ParentUserCreateView: Successfully auto-linked Parent '{self.object.email}' to Student '{student_to_link.get_full_name()}'.")
                messages.success(self.request, _(f"Parent '{self.object.email}' has been successfully linked to student '{student_to_link.get_full_name()}'."))
                
                # If a 'next' URL was provided (e.g., back to student_manage_parents), prioritize it
                if next_url_param:
                    logger.debug(f"ParentUserCreateView: Redirecting to 'next' URL after linking: {next_url_param}")
                    return next_url_param
                
                # Otherwise, redirect to the student's detail page after linking
                logger.debug(f"ParentUserCreateView: Redirecting to student detail page after linking: Student PK {student_to_link.pk}")
                return reverse('students:student_detail', kwargs={'pk': student_to_link.pk})

            except Student.DoesNotExist:
                messages.warning(self.request, _(f"Could not auto-link parent: Student with ID {student_pk_to_link} not found."))
                logger.warning(f"ParentUserCreateView: Student.DoesNotExist for PK {student_pk_to_link} during auto-linking.")
            except ValueError:
                messages.warning(self.request, _(f"Could not auto-link parent: Invalid student ID '{student_pk_to_link}'."))
                logger.warning(f"ParentUserCreateView: ValueError for student PK '{student_pk_to_link}' during auto-linking.")
            except Exception as e:
                messages.error(self.request, _(f"An error occurred while auto-linking parent to student: {e}"))
                logger.error(f"ParentUserCreateView: Error auto-linking Parent '{self.object.email}' to Student PK {student_pk_to_link}: {e}", exc_info=True)
        
        # If no specific student linking or 'next' URL, fall back to general 'next' or parent list
        if next_url_param:
            logger.debug(f"ParentUserCreateView: Redirecting to general 'next' URL: {next_url_param}")
            return next_url_param
            
        logger.debug("ParentUserCreateView: Fallback redirect to parent user list.")
        return reverse_lazy('students:parentuser_list') # Default fallback
    


class ParentUserUpdateView(StaffLoginRequiredMixin, TenantPermissionRequiredMixin, SuccessMessageMixin, UpdateView):
    model = ParentUser
    form_class = ParentUserChangeForm
    template_name = 'students/parentuser_form.html' # Re-use the same form template as create, or make a specific one
    context_object_name = 'parent_user_obj' # Name for the object in template context
    permission_required = 'students.change_parentuser' # Ensure this perm exists and is assigned
    success_message = _("Parent account '%(email)s' updated successfully.")

    # login_url can be inherited from StaffLoginRequiredMixin or set explicitly if needed
    # login_url = reverse_lazy('schools:staff_login') 

    def get_queryset(self):
        # Ensure we are fetching ParentUser from the current tenant's schema.
        # ListView/DetailView/UpdateView/DeleteView automatically filter by PK from URL,
        # but if ParentUser model itself doesn't have a tenant FK (common if it's a user model
        # placed in a tenant app), queries are naturally scoped by schema.
        # If ParentUser *does* have an FK to School (tenant), you'd filter here:
        # return super().get_queryset().filter(tenant=self.request.tenant)
        return super().get_queryset() # Relies on schema context

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        # kwargs['tenant'] = self.request.tenant # Pass tenant to form if form's __init__ needs it
        return kwargs

    def get_success_url(self):
        # Redirect to a parent user list or detail page
        # For now, let's assume a parent user list view will exist
        # If you have a ParentUserDetailView:
        # return reverse('students:parentuser_detail', kwargs={'pk': self.object.pk})
        return reverse_lazy('students:parentuser_list') # Needs ParentUserListView

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # self.object is the ParentUser instance being edited
        context['view_title'] = _(f"Edit Parent Account: {self.object.get_full_name() or self.object.email}")
        context['form_mode'] = "update" # For template to adapt (e.g., button text)
        logger.debug(f"ParentUserUpdateView: Context prepared for editing ParentUser {self.object.pk}.")
        return context

    def form_valid(self, form):
        logger.info(f"ParentUser '{form.instance.email}' (PK: {form.instance.pk}) being UPDATED by user '{self.request.user.email}' in tenant '{self.request.tenant.schema_name}'.")
        # SuccessMessageMixin handles the message
        return super().form_valid(form)
    


from django.views.generic import ListView, CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy
from django.contrib.messages.views import SuccessMessageMixin

from .models import ParentUser # Or whatever your parent model is named
from .forms import ParentUserForm



class ParentUserDeleteView(SuccessMessageMixin, DeleteView):
    """
    Handles the deletion of a parent user record.
    """
    model = ParentUser
    # This is the template that asks "Are you sure?"
    template_name = 'students/parentuser_confirm_delete.html' 
    
    # Where to redirect after a successful deletion
    success_url = reverse_lazy('students:parent_list') 
    
    # The message to display upon successful deletion
    success_message = "Parent user was deleted successfully."



# your_project/apps/students/views.py (Simplified Logic)

from django.db import transaction
from django.contrib import messages
from django.shortcuts import render, redirect
from django.views.generic.edit import FormView
from django.urls import reverse

from .forms import PromotionSetupForm
# Add any necessary permission mixins you use, e.g., from apps.schools.mixins
# from apps.schools.mixins import StaffRequiredMixin 

class PromotionSetupView(FormView): # Using FormView is perfect for this step
    """
    Step 1 of the Student Promotion Wizard.
    Allows the admin to select the academic years and the class to promote.
    """
    template_name = 'students/promotion_setup.html'
    form_class = PromotionSetupForm

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['page_title'] = "Start Student Promotion"
        return context

    def form_valid(self, form):
        """
        If the form is valid, redirect to the next step (the review page),
        passing the selected IDs as URL query parameters.
        """
        from_year_id = form.cleaned_data['from_academic_year'].id
        to_year_id = form.cleaned_data['to_academic_year'].id
        class_id = form.cleaned_data['class_to_promote'].id

        # We will build 'promotion_review' in the next step.
        # This creates a URL like: /portal/students/promotions/review/?from_year=1&to_year=2&class=5
        review_url = reverse('students:promotion_review') # This URL doesn't exist yet
        
        # For now, let's just show a temporary success message until we build step 2
        # In the final version, we will redirect to the review_url with query params.
        from django.contrib import messages
        messages.success(self.request, "Setup complete! Redirecting to review page...") # Temporary
        
        # This is what the final redirect will look like:
        # return redirect(f'{review_url}?from_year={from_year_id}&to_year={to_year_id}&class={class_id}')
        
        # For now, just redirect back to the same page to show it works.
        return redirect('students:promotion_setup')
    

class PromotionExecuteView(View):
    @transaction.atomic
    def post(self, request, *args, **kwargs):
        # Retrieve the confirmed actions (e.g., from session or hidden form inputs)
        confirmed_actions = request.session.get('promotion_actions', [])
        from_year = AcademicYear.objects.get(pk=request.session.get('from_year_pk'))
        to_year = AcademicYear.objects.get(pk=request.session.get('to_year_pk'))

        for action_data in confirmed_actions:
            student = Student.objects.get(pk=action_data['student_id'])
            original_class = student.current_class
            action = action_data['action']
            
            if action == 'PROMOTE' or action == 'RETAIN':
                new_class = SchoolClass.objects.get(pk=action_data['to_class_id'])
                
                # 1. Update the Student record
                student.current_class = new_class
                student.academic_year = to_year
                student.save()

                # 2. Create the history record
                PromotionHistory.objects.create(
                    student=student,
                    action=PromotionHistory.Action.PROMOTED if action == 'PROMOTE' else PromotionHistory.Action.RETAINED,
                    from_class=original_class,
                    to_class=new_class,
                    from_academic_year=from_year,
                    to_academic_year=to_year,
                    performed_by=request.user
                )

            elif action == 'GRADUATE':
                # 1. Update the Student record
                student.is_active = False
                student.status = 'ALUMNI' # Or a similar status
                student.save()

                # 2. Create the history record
                PromotionHistory.objects.create(
                    student=student,
                    action=PromotionHistory.Action.GRADUATED,
                    from_class=original_class,
                    to_class=None, # No 'to_class' for graduation
                    from_academic_year=from_year,
                    to_academic_year=to_year,
                    performed_by=request.user
                )
        
        # Clean up session and show success message
        del request.session['promotion_actions']
        messages.success(request, "Student promotions have been processed successfully!")
        return redirect('students:promotion_dashboard') # Redirect to a results page