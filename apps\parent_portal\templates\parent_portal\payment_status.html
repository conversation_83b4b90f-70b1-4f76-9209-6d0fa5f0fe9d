{# D:\school_fees_saas_v2\apps\parent_portal\templates\parent_portal\payment_status.html #}
{% extends "parent_portal/parent_portal_base.html" %}
{% load static i18n humanize %}

{% block parent_portal_page_title %}{{ view_title }}{% endblock parent_portal_page_title %}

{% block parent_portal_main_content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-8 text-center">
            
            {% if status == 'success' %}
                {# --- SUCCESS MESSAGE --- #}
                <div class="card border-success shadow">
                    <div class="card-body p-5">
                        <i class="bi bi-check-circle-fill display-1 text-success mb-3"></i>
                        <h1 class="card-title">{{ message_title }}</h1>
                        <p class="lead">{{ message_body }}</p>

                        {% if payment %}
                        <div class="alert alert-light mt-4">
                            <h5 class="alert-heading">Payment Summary</h5>
                            <p><strong>Amount Paid:</strong> {{ school_profile.currency_symbol|default:'$' }}{{ payment.amount|floatformat:2|intcomma }}</p>
                            <p><strong>Payment Date:</strong> {{ payment.payment_date|date:"F d, Y" }}</p>
                            <p><strong>Receipt No:</strong> P-{{ payment.id }}</p>
                        </div>
                        <a href="{% url 'payments:payment_receipt_pdf_public' pk=payment.pk %}" target="_blank" class="btn btn-danger mt-3">
                            <i class="bi bi-file-earmark-pdf-fill me-1"></i> {% trans "View Receipt" %}
                        </a>
                        {% endif %}
                    </div>
                </div>

            {% elif status == 'failure' %}
                {# --- FAILURE MESSAGE --- #}
                <div class="card border-danger shadow">
                    <div class="card-body p-5">
                        <i class="bi bi-x-circle-fill display-1 text-danger mb-3"></i>
                        <h1 class="card-title">{{ message_title }}</h1>
                        <p class="lead">{{ message_body }}</p>
                    </div>
                </div>
                
            {% else %}
                 {# --- GENERIC ERROR MESSAGE --- #}
                 <div class="card border-warning shadow">
                    <div class="card-body p-5">
                        <i class="bi bi-exclamation-triangle-fill display-1 text-warning mb-3"></i>
                        <h1 class="card-title">{{ message_title|default:_("An Error Occurred") }}</h1>
                        <p class="lead">{{ message_body|default:_("We could not retrieve the status of your transaction.") }}</p>
                    </div>
                </div>
            {% endif %}

            <div class="mt-4">
                <a href="{% url 'parent_portal:dashboard' %}" class="btn btn-primary btn-lg">
                    <i class="bi bi-arrow-left-circle me-1"></i> {% trans "Return to Dashboard" %}
                </a>
                <a href="{% url 'parent_portal:payment_history_all' %}" class="btn btn-outline-secondary btn-lg ms-2">
                    <i class="bi bi-clock-history me-1"></i> {% trans "View Payment History" %}
                </a>
            </div>

        </div>
    </div>
</div>
{% endblock parent_portal_main_content %}


