{% extends "tenant_base.html" %}
{% load humanize static core_tags reporting_filters %}

{% block title %}{{ view_title|default:"Profitability Analysis Report" }}{% endblock %}

{% block content %}
<div class="container-fluid mt-3">
    {% with report_icon_class="bi bi-pie-chart" %}
        {% include "partials/_report_header.html" %}
    {% endwith %}

    {% include "partials/_messages.html" %}
    {% include "partials/_report_filter_form.html" with filter_form=filter_form report_code=report_code specific_filter_title="Profitability Analysis Filters" %}

    <!-- Summary Card -->
    <div class="card mb-4">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="bi bi-graph-up me-2"></i>Profitability Overview</h5>
        </div>
        <div class="card-body">
            <div class="row mb-3">
                <div class="col-md-12">
                    <p>
                        {% if academic_year %}<strong>Academic Year:</strong> {{ academic_year }} | {% endif %}
                        <strong>Analysis Type:</strong> {{ analysis_type|title|replace:"_":" " }} | 
                        <strong>Cost Allocation:</strong> {{ cost_allocation_method|title|replace:"_":" " }}
                    </p>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-3">
                    <div class="text-center">
                        <h6 class="text-muted">Total Revenue</h6>
                        <p class="mb-0 fw-bold text-success fs-4">{{ summary.total_revenue|currency }}</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h6 class="text-muted">Total Costs</h6>
                        <p class="mb-0 fw-bold text-danger">{{ summary.total_costs|currency }}</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h6 class="text-muted">Total Profit</h6>
                        <p class="mb-0 fw-bold {% if summary.total_profit >= 0 %}text-success{% else %}text-danger{% endif %} fs-4">
                            {{ summary.total_profit|currency }}
                        </p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h6 class="text-muted">Profit Margin</h6>
                        <p class="mb-0 fw-bold {% if summary.overall_profit_margin >= profitability_threshold %}text-success{% elif summary.overall_profit_margin >= 0 %}text-warning{% else %}text-danger{% endif %} fs-4">
                            {{ summary.overall_profit_margin|floatformat:1 }}%
                        </p>
                    </div>
                </div>
            </div>
            
            <!-- Profitability Progress Bar -->
            <div class="mt-3">
                <h6>Overall Profitability Performance</h6>
                <div class="progress" style="height: 25px;">
                    <div class="progress-bar {% if summary.overall_profit_margin >= profitability_threshold %}bg-success{% elif summary.overall_profit_margin >= 0 %}bg-warning{% else %}bg-danger{% endif %}" 
                         role="progressbar" style="width: {% if summary.overall_profit_margin > 0 %}{{ summary.overall_profit_margin|floatformat:0 }}{% else %}10{% endif %}%">
                        {{ summary.overall_profit_margin|floatformat:1 }}%
                    </div>
                </div>
                <small class="text-muted">
                    Target: {{ profitability_threshold }}% | 
                    {% if summary.overall_profit_margin >= profitability_threshold %}
                    Exceeds target profitability
                    {% elif summary.overall_profit_margin >= 0 %}
                    Below target but profitable
                    {% else %}
                    Operating at a loss
                    {% endif %}
                </small>
            </div>
        </div>
    </div>

    <!-- Performance Metrics -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="bi bi-speedometer2 me-2"></i>Performance Metrics</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6">
                            <div class="text-center">
                                <h6 class="text-muted">Profitable Groups</h6>
                                <p class="mb-0 fw-bold text-success">{{ summary.profitable_groups }}</p>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center">
                                <h6 class="text-muted">Loss-making Groups</h6>
                                <p class="mb-0 fw-bold text-danger">{{ summary.loss_making_groups }}</p>
                            </div>
                        </div>
                    </div>
                    <hr>
                    <div class="row">
                        <div class="col-6">
                            <div class="text-center">
                                <h6 class="text-muted">Highest Margin</h6>
                                <p class="mb-0 fw-bold text-success">{{ summary.highest_margin|floatformat:1 }}%</p>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center">
                                <h6 class="text-muted">Lowest Margin</h6>
                                <p class="mb-0 fw-bold {% if summary.lowest_margin >= 0 %}text-warning{% else %}text-danger{% endif %}">{{ summary.lowest_margin|floatformat:1 }}%</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="bi bi-pie-chart-fill me-2"></i>Cost Breakdown</h5>
                </div>
                <div class="card-body">
                    {% for cost_item in cost_breakdown %}
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>{{ cost_item.cost_type }}</span>
                            <span class="fw-bold">{{ cost_item.amount|currency }} ({{ cost_item.percentage|floatformat:1 }}%)</span>
                        </div>
                        <div class="progress" style="height: 20px;">
                            <div class="progress-bar {% if cost_item.cost_type == 'Direct Costs' %}bg-primary{% else %}bg-secondary{% endif %}" 
                                 role="progressbar" style="width: {{ cost_item.percentage }}%">
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>

    <!-- Profitability Analysis Details -->
    {% if profitability_analysis %}
    <div class="card">
        <div class="card-header bg-light d-flex justify-content-between align-items-center">
            <h5 class="mb-0"><i class="bi bi-table me-2"></i>{{ analysis_type|title|replace:"_":" " }} Profitability Analysis</h5>
            <span class="badge bg-primary">{{ profitability_analysis|length }} groups</span>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-striped table-hover mb-0">
                    <thead class="table-dark">
                        <tr>
                            <th>{{ analysis_type|title|replace:"_":" " }}</th>
                            <th class="text-end">Revenue</th>
                            <th class="text-end">Costs</th>
                            <th class="text-end">Profit</th>
                            <th class="text-center">Margin %</th>
                            <th class="text-center">Students</th>
                            <th class="text-end">Revenue/Student</th>
                            <th class="text-end">Profit/Student</th>
                            <th class="text-center">Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for item in profitability_analysis %}
                        <tr class="{% if item.profit_margin < 0 %}table-danger{% elif not item.meets_threshold %}table-warning{% endif %}">
                            <td>
                                <strong>{{ item.group_name }}</strong>
                                {% if item.collection_rate < 100 %}
                                <br><small class="text-muted">Collection: {{ item.collection_rate|floatformat:1 }}%</small>
                                {% endif %}
                            </td>
                            <td class="text-end">
                                <span class="text-success fw-bold">{{ item.total_revenue|currency }}</span>
                            </td>
                            <td class="text-end">
                                <span class="text-danger fw-bold">{{ item.allocated_costs|currency }}</span>
                            </td>
                            <td class="text-end">
                                <span class="{% if item.gross_profit >= 0 %}text-success{% else %}text-danger{% endif %} fw-bold">
                                    {{ item.gross_profit|currency }}
                                </span>
                            </td>
                            <td class="text-center">
                                <span class="fw-bold {% if item.profit_margin >= profitability_threshold %}text-success{% elif item.profit_margin >= 0 %}text-warning{% else %}text-danger{% endif %}">
                                    {{ item.profit_margin|floatformat:1 }}%
                                </span>
                            </td>
                            <td class="text-center">
                                <span class="badge bg-info">{{ item.student_count }}</span>
                            </td>
                            <td class="text-end">
                                {{ item.revenue_per_student|currency }}
                            </td>
                            <td class="text-end">
                                <span class="{% if item.profit_per_student >= 0 %}text-success{% else %}text-danger{% endif %}">
                                    {{ item.profit_per_student|currency }}
                                </span>
                            </td>
                            <td class="text-center">
                                {% if item.profitability_status == 'Highly Profitable' %}
                                <span class="badge bg-success">{{ item.profitability_status }}</span>
                                {% elif item.profitability_status == 'Profitable' %}
                                <span class="badge bg-info">{{ item.profitability_status }}</span>
                                {% elif item.profitability_status == 'Break-even' %}
                                <span class="badge bg-warning">{{ item.profitability_status }}</span>
                                {% else %}
                                <span class="badge bg-danger">{{ item.profitability_status }}</span>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                    <tfoot class="table-light">
                        <tr>
                            <th>Totals:</th>
                            <th class="text-end text-success">{{ summary.total_revenue|currency }}</th>
                            <th class="text-end text-danger">{{ summary.total_costs|currency }}</th>
                            <th class="text-end {% if summary.total_profit >= 0 %}text-success{% else %}text-danger{% endif %}">{{ summary.total_profit|currency }}</th>
                            <th class="text-center fw-bold">{{ summary.overall_profit_margin|floatformat:1 }}%</th>
                            <th colspan="4"></th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Strategic Insights -->
    {% if profitability_analysis %}
    <div class="card mt-4">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="bi bi-lightbulb me-2"></i>Strategic Insights & Recommendations</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h6>Top Performers</h6>
                    <ul class="list-unstyled">
                        {% for item in profitability_analysis|slice:":3" %}
                        {% if item.profit_margin > 0 %}
                        <li><i class="bi bi-trophy text-success me-2"></i>
                            <strong>{{ item.group_name }}</strong> - {{ item.profit_margin|floatformat:1 }}% margin ({{ item.gross_profit|currency }} profit)
                        </li>
                        {% endif %}
                        {% endfor %}
                    </ul>
                    
                    <h6 class="mt-3">Key Insights</h6>
                    <ul class="list-unstyled">
                        <li><i class="bi bi-check-circle text-success me-2"></i>
                            {{ summary.profitable_groups }} out of {{ summary.total_groups }} groups are profitable
                        </li>
                        <li><i class="bi bi-graph-up text-info me-2"></i>
                            Average profit margin: {{ summary.avg_profit_margin|floatformat:1 }}%
                        </li>
                        <li><i class="bi bi-cash text-primary me-2"></i>
                            Total profit: {{ summary.total_profit|currency }}
                        </li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6>Areas for Improvement</h6>
                    <ul class="list-unstyled">
                        {% for item in profitability_analysis %}
                        {% if item.profit_margin < 0 %}
                        <li><i class="bi bi-exclamation-triangle text-danger me-2"></i>
                            <strong>{{ item.group_name }}</strong> - {{ item.profit_margin|floatformat:1 }}% margin ({{ item.gross_profit|currency }} loss)
                        </li>
                        {% endif %}
                        {% endfor %}
                    </ul>
                    
                    <h6 class="mt-3">Strategic Recommendations</h6>
                    <ul class="list-unstyled">
                        {% if summary.loss_making_groups > 0 %}
                        <li><i class="bi bi-arrow-right text-primary me-2"></i>Review cost allocation for loss-making groups</li>
                        <li><i class="bi bi-arrow-right text-primary me-2"></i>Consider fee adjustments for underperforming segments</li>
                        {% endif %}
                        
                        {% if summary.overall_profit_margin < profitability_threshold %}
                        <li><i class="bi bi-arrow-right text-warning me-2"></i>Overall margin below {{ profitability_threshold }}% target - review pricing strategy</li>
                        {% endif %}
                        
                        <li><i class="bi bi-arrow-right text-success me-2"></i>Replicate success factors from top performers</li>
                        <li><i class="bi bi-arrow-right text-info me-2"></i>Monitor cost allocation accuracy and efficiency</li>
                        <li><i class="bi bi-arrow-right text-primary me-2"></i>Regular profitability reviews for strategic planning</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- No Data Message -->
    {% if not profitability_analysis %}
    <div class="card">
        <div class="card-body text-center py-5">
            <i class="bi bi-pie-chart display-1 text-muted mb-3"></i>
            <h4 class="text-muted">No Profitability Data Available</h4>
            <p class="text-muted">No revenue or cost data found for the selected criteria.</p>
            <div class="mt-3">
                <a href="{% url 'reporting:profitability_analysis_report' %}" class="btn btn-outline-primary me-2">
                    <i class="bi bi-arrow-clockwise me-2"></i>Reset Filters
                </a>
            </div>
        </div>
    </div>
    {% endif %}

</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Animate progress bars
    $('.progress-bar').each(function() {
        var $this = $(this);
        var width = $this.css('width');
        $this.css('width', '0%').animate({width: width}, 1500);
    });
    
    // Highlight loss-making and underperforming rows
    $('.table-danger, .table-warning').hover(
        function() {
            $(this).addClass('table-active');
        },
        function() {
            $(this).removeClass('table-active');
        }
    );
    
    // Add click handlers for profitability rows
    $('tbody tr').on('click', function() {
        $(this).toggleClass('table-info');
    });
});
</script>
{% endblock %}

