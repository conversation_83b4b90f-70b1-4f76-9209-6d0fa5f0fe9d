# D:\school_fees_saas_v2\apps\subscriptions\apps.py
from django.apps import AppConfig
from django.utils.translation import gettext_lazy as _ # If using _ in verbose_name

class SubscriptionsConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'apps.subscriptions'
    verbose_name = _("Subscriptions Management")

    def ready(self):
        import apps.subscriptions.signals  # Import signals to register them