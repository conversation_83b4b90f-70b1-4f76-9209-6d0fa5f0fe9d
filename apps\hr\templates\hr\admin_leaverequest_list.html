{% extends "tenant_base.html" %}
{% load static humanize i18n widget_tweaks %}

{% block tenant_page_title %}{{ view_title|default:_("Administer Leave Requests") }}{% endblock %}

{% block extra_tenant_css %}
    {{ block.super }}
    <style>
        .table th, .table td { vertical-align: middle; }
    </style>
{% endblock %}

{% block tenant_specific_content %}
<div class="container-fluid mt-4">
    <div class="pagetitle mb-3">
        <h1>{{ view_title|default:_("Administer Leave Requests") }}</h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'schools:dashboard' %}">{% trans "Dashboard" %}</a></li>
                <li class="breadcrumb-item"><a href="{% url 'hr:hr_dashboard' %}">{% trans "HR" %}</a></li>
                <li class="breadcrumb-item active" aria-current="page">{% trans "Administer Leave Requests" %}</li>
            </ol>
        </nav>
    </div>

    {% include "partials/_messages.html" %}

    {# Filter Form Card #}
    <div class="card shadow-sm mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0"><i class="bi bi-funnel-fill me-2"></i>{% trans "Filter Requests" %}</h5>
        </div>
        <div class="card-body py-3">
            <form method="get" class="row g-3 align-items-end">
                <div class="col-md-4">
                    <label for="employee" class="form-label">{% trans "Employee" %}</label>
                    <select name="employee" class="form-select form-select-sm" id="employee">
                        <option value="">{% trans "All Employees" %}</option>
                        {% for emp in employees %}
                            <option value="{{ emp.pk }}" {% if request.GET.employee == emp.pk|stringformat:"s" %}selected{% endif %}>
                                {{ emp.user.get_full_name }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="leave_type" class="form-label">{% trans "Leave Type" %}</label>
                    <select name="leave_type" class="form-select form-select-sm" id="leave_type">
                        <option value="">{% trans "All Leave Types" %}</option>
                        {% for lt in leave_types %}
                            <option value="{{ lt.pk }}" {% if request.GET.leave_type == lt.pk|stringformat:"s" %}selected{% endif %}>
                                {{ lt.name }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="status" class="form-label">{% trans "Status" %}</label>
                    <select name="status" class="form-select form-select-sm" id="status">
                        <option value="">{% trans "All Statuses" %}</option>
                        <option value="PENDING" {% if request.GET.status == "PENDING" %}selected{% endif %}>{% trans "Pending" %}</option>
                        <option value="APPROVED" {% if request.GET.status == "APPROVED" %}selected{% endif %}>{% trans "Approved" %}</option>
                        <option value="REJECTED" {% if request.GET.status == "REJECTED" %}selected{% endif %}>{% trans "Rejected" %}</option>
                    </select>
                </div>
                <div class="col-md-2 d-flex gap-2">
                    <button type="submit" class="btn btn-primary btn-sm w-100">{% trans "Apply" %}</button>
                    <a href="{% url 'hr:admin_leaverequest_list' %}" class="btn btn-secondary btn-sm w-100">{% trans "Clear" %}</a>
                </div>
            </form>
        </div>
    </div>

    {# Leave Requests List Card #}
    <div class="card shadow-sm">
        <div class="card-header">
            <h5 class="card-title mb-0"><i class="bi bi-card-list me-2"></i>{% trans "All Submitted Leave Requests" %}</h5>
        </div>
        <div class="card-body p-0">
            {% with request_list=object_list %}
            {% if request_list %}
            <div class="table-responsive">
                <table class="table table-hover table-striped mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>{% trans "Staff" %}</th>
                            <th>{% trans "Leave Type" %}</th>
                            <th>{% trans "Dates" %}</th>
                            <th class="text-center">{% trans "Days" %}</th>
                            <th class="text-center">{% trans "Status" %}</th>
                            <th>{% trans "Submitted" %}</th>
                            <th class="text-center">{% trans "Actions" %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for lr in request_list %}
                        <tr>
                            <td>
                                {{ lr.employee.user.get_full_name }}<br>
                                <small class="text-muted">{{ lr.employee.user.email }}</small>
                            </td>
                            <td>{{ lr.leave_type.name }}</td>
                            <td>{{ lr.start_date|date:"d M Y" }} - {{ lr.end_date|date:"d M Y" }}</td>
                            
                            {# --- CORRECTED DAYS COLUMN --- #}
                            <td class="text-center">{{ lr.duration }}</td>
                            
                            <td class="text-center">
                                <span class="badge bg-{{ lr.get_status_badge_class }}">{{ lr.get_status_display }}</span>
                            </td>
                            <td>{{ lr.created_at|naturaltime }}</td>
                            <td class="text-center">
                                <button type="button" class="btn btn-sm btn-outline-info" data-bs-toggle="modal" data-bs-target="#leaveRequestDetailModal-{{ lr.pk }}" title="{% trans 'View Details' %}"><i class="bi bi-eye"></i></button>
                                {% if perms.hr.change_leaverequest and lr.status == 'PENDING' %}
                                    <a href="{% url 'hr:admin_leaverequest_update' pk=lr.pk %}" class="btn btn-sm btn-primary" title="{% trans 'Approve / Reject' %}"><i class="bi bi-pencil-square"></i></a>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="p-4 text-center">
                <p class="text-muted mt-3">{% trans "No leave requests found matching your criteria." %}</p>
            </div>
            {% endif %}
            {% endwith %}
        </div>
        {% if is_paginated %}
        <div class="card-footer bg-light">
            {% include "partials/_pagination.html" with page_obj=page_obj %}
        </div>
        {% endif %}
    </div>
</div>

{# --- Modals Section --- #}
{% with request_list=object_list %}
{% for lr in request_list %}
<div class="modal fade" id="leaveRequestDetailModal-{{ lr.pk }}" tabindex="-1" aria-labelledby="leaveRequestDetailModalLabel-{{ lr.pk }}" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="leaveRequestDetailModalLabel-{{ lr.pk }}">{% trans "Leave Request Details" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <dl class="row">
                    <dt class="col-sm-3">{% trans "Staff" %}:</dt>
                    <dd class="col-sm-9">{{ lr.employee.user.get_full_name }} ({{ lr.employee.user.email }})</dd>
                    
                    <dt class="col-sm-3">{% trans "Leave Type" %}:</dt>
                    <dd class="col-sm-9">{{ lr.leave_type.name }}</dd>
                    
                    <dt class="col-sm-3">{% trans "Dates" %}:</dt>
                    <dd class="col-sm-9">{{ lr.start_date|date:"D, d M Y" }} {% trans "to" %} {{ lr.end_date|date:"D, d M Y" }}</dd>
                    
                    {# --- CORRECTED DURATION IN MODAL --- #}
                    <dt class="col-sm-3">{% trans "Duration" %}:</dt>
                    <dd class="col-sm-9">{{ lr.duration }} {% trans "day" %}{{ lr.duration|pluralize }}</dd>
                    
                    <dt class="col-sm-3">{% trans "Reason" %}:</dt>
                    <dd class="col-sm-9" style="white-space: pre-wrap;">{{ lr.reason|default:"N/A" }}</dd>
                    
                    {% if lr.attachment %}
                    <dt class="col-sm-3">{% trans "Attachment" %}:</dt>
                    <dd class="col-sm-9"><a href="{{ lr.attachment.url }}" target="_blank">{% trans "View Attachment" %} <i class="bi bi-box-arrow-up-right"></i></a></dd>
                    {% endif %}
                </dl>
                <hr>
                <dl class="row">
                    <dt class="col-sm-3">{% trans "Status" %}:</dt>
                    <dd class="col-sm-9"><span class="badge bg-{{ lr.get_status_badge_class }}">{{ lr.get_status_display }}</span></dd>
                    
                    <dt class="col-sm-3">{% trans "Submitted" %}:</dt>
                    <dd class="col-sm-9">{{ lr.created_at|date:"D, d M Y, H:i" }}</dd>

                    {% if lr.status != 'PENDING' %}
                    <dt class="col-sm-3">{% trans "Actioned At" %}:</dt>
                    <dd class="col-sm-9">{{ lr.status_changed_at|date:"D, d M Y, H:i"|default:"N/A" }}</dd>
                        
                    <dt class="col-sm-3">{% trans "Action By" %}:</dt>
                    <dd class="col-sm-9">{{ lr.approved_by.get_full_name|default:"N/A" }}</dd>
                    
                    <dt class="col-sm-3">{% trans "Comment" %}:</dt>
                    <dd class="col-sm-9" style="white-space: pre-wrap;">{{ lr.status_reason|default:"N/A" }}</dd>
                    {% endif %}
                </dl>
            </div>
            <div class="modal-footer">
                {% if perms.hr.change_leaverequest and lr.status == 'PENDING' %}
                    <a href="{% url 'hr:admin_leaverequest_update' pk=lr.pk %}" class="btn btn-primary">{% trans "Approve / Reject this Request" %}</a>
                {% endif %}
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Close" %}</button>
            </div>
        </div>
    </div>
</div>
{% endfor %}
{% endwith %}

{% endblock tenant_specific_content %}








{% comment %} {% extends "tenant_base.html" %}
{% load static humanize i18n widget_tweaks %}

{% block tenant_page_title %}{{ view_title|default:_("Administer Leave Requests") }}{% endblock %}

{% block extra_tenant_css %}
    {{ block.super }}
    <style>
        .table th, .table td { vertical-align: middle; }
    </style>
{% endblock %}

{# CORRECTED BLOCK NAME #}
{% block tenant_specific_content %}
<div class="container-fluid mt-4">
    <div class="pagetitle mb-3">
        <h1>{{ view_title|default:_("Administer Leave Requests") }}</h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'schools:dashboard' %}">{% trans "Dashboard" %}</a></li>
                <li class="breadcrumb-item active" aria-current="page">{% trans "Administer Leave Requests" %}</li>
            </ol>
        </nav>
    </div>

    {% include "partials/_messages.html" %}


    
    <div class="card shadow-sm">
        <div class="card-header">
            <h5 class="card-title mb-0"><i class="bi bi-card-list me-2"></i>{% trans "All Submitted Leave Requests" %}</h5>
        </div>
        <div class="card-body p-0">
            {% with request_list=leave_requests|default:object_list %}
            {% if request_list %}
            <div class="table-responsive">
                <table class="table table-hover table-striped mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>{% trans "Staff" %}</th>
                            <th>{% trans "Leave Type" %}</th>
                            <th>{% trans "Dates" %}</th>
                            <th class="text-center">{% trans "Days" %}</th>
                            <th class="text-center">{% trans "Status" %}</th>
                            <th>{% trans "Submitted" %}</th>
                            <th class="text-center">{% trans "Actions" %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for lr in request_list %}
                        <tr>
                            <td>
                                {{ lr.employee.user.get_full_name }}<br>
                                <small class="text-muted">{{ lr.employee.user.email }}</small>
                            </td>
                            <td>{{ lr.leave_type.name }}</td>
                            <td>{{ lr.start_date|date:"d M Y" }} - {{ lr.end_date|date:"d M Y" }}</td>
                            <td class="text-center">{{ lr.duration }}</td>
                            <td class="text-center">
                                <span class="badge bg-{{ lr.get_status_badge_class }}">{{ lr.get_status_display }}</span>
                            </td>
                            <td>{{ lr.created_at|naturaltime }}</td>
                            <td class="text-center">
                                <button type="button" class="btn btn-sm btn-outline-info" data-bs-toggle="modal" data-bs-target="#leaveRequestDetailModal-{{ lr.pk }}" title="{% trans 'View Details' %}"><i class="bi bi-eye"></i></button>
                                {% if perms.hr.change_leaverequest and lr.status == 'PENDING' %}
                                    <a href="{% url 'hr:admin_leaverequest_update' pk=lr.pk %}" class="btn btn-sm btn-primary" title="{% trans 'Approve / Reject' %}"><i class="bi bi-pencil-square"></i></a>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="p-4 text-center">
                <p class="text-muted mt-3">{% trans "No leave requests found matching your criteria." %}</p>
            </div>
            {% endif %}
            {% endwith %}
        </div>
        {% if is_paginated %}
        <div class="card-footer bg-light">
            {% include "partials/_pagination.html" with page_obj=page_obj %}
        </div>
        {% endif %}
    </div>
</div>

{# --- Modals Section --- #}
{% with request_list=leave_requests|default:object_list %}
{% for lr in request_list %}
<div class="modal fade" id="leaveRequestDetailModal-{{ lr.pk }}" tabindex="-1" aria-labelledby="leaveRequestDetailModalLabel-{{ lr.pk }}" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="leaveRequestDetailModalLabel-{{ lr.pk }}">{% trans "Leave Request Details" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <dl class="row">
                    <dt class="col-sm-3">{% trans "Staff" %}:</dt>
                    <dd class="col-sm-9">{{ lr.employee.user.get_full_name }} ({{ lr.employee.user.email }})</dd>
                    
                    <dt class="col-sm-3">{% trans "Leave Type" %}:</dt>
                    <dd class="col-sm-9">{{ lr.leave_type.name }}</dd>
                    
                    <dt class="col-sm-3">{% trans "Dates" %}:</dt>
                    <dd class="col-sm-9">{{ lr.start_date|date:"D, d M Y" }} {% trans "to" %} {{ lr.end_date|date:"D, d M Y" }}</dd>
                    
                    <dt class="col-sm-3">{% trans "Duration" %}:</dt>
                    <dd class="col-sm-9">{{ lr.duration }} {% trans "day" %}{{ lr.duration|pluralize }}</dd>
                    
                    <dt class="col-sm-3">{% trans "Reason" %}:</dt>
                    <dd class="col-sm-9" style="white-space: pre-wrap;">{{ lr.reason|default:"N/A" }}</dd>
                    
                    {% if lr.attachment %}
                    <dt class="col-sm-3">{% trans "Attachment" %}:</dt>
                    <dd class="col-sm-9"><a href="{{ lr.attachment.url }}" target="_blank">{% trans "View Attachment" %} <i class="bi bi-box-arrow-up-right"></i></a></dd>
                    {% endif %}
                </dl>
                <hr>
                <dl class="row">
                    <dt class="col-sm-3">{% trans "Status" %}:</dt>
                    <dd class="col-sm-9"><span class="badge bg-{{ lr.get_status_badge_class }}">{{ lr.get_status_display }}</span></dd>
                    
                    <dt class="col-sm-3">{% trans "Submitted" %}:</dt>
                    <dd class="col-sm-9">{{ lr.created_at|date:"D, d M Y, H:i" }}</dd>

                    {% if lr.status != 'PENDING' %}
                    <dt class="col-sm-3">{% trans "Actioned At" %}:</dt>
                    <dd class="col-sm-9">{{ lr.status_changed_at|date:"D, d M Y, H:i"|default:"N/A" }}</dd>
                        
                    <dt class="col-sm-3">{% trans "Action By" %}:</dt>
                    <dd class="col-sm-9">{{ lr.approved_by.get_full_name|default:"N/A" }}</dd>
                    
                    <dt class="col-sm-3">{% trans "Comment" %}:</dt>
                    <dd class="col-sm-9" style="white-space: pre-wrap;">{{ lr.status_reason|default:"N/A" }}</dd>
                    {% endif %}
                </dl>
            </div>
            <div class="modal-footer">
                {% if perms.hr.change_leaverequest and lr.status == 'PENDING' %}
                    <a href="{% url 'hr:admin_leaverequest_update' pk=lr.pk %}" class="btn btn-primary">{% trans "Approve / Reject this Request" %}</a>
                {% endif %}
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Close" %}</button>
            </div>
        </div>
    </div>
</div>
{% endfor %}
{% endwith %}

{% endblock tenant_specific_content %}

 {% endcomment %}
