# Generated migration for missing student fields
from django.db import migrations, models

class Migration(migrations.Migration):
    dependencies = [
        ('students', '0001_initial'),  # Adjust based on your last migration
    ]

    operations = [
        migrations.AddField(
            model_name='student',
            name='photo',
            field=models.ImageField(upload_to='student_photos/', blank=True, null=True),
        ),
        migrations.AddField(
            model_name='student',
            name='middle_name',
            field=models.CharField(max_length=150, blank=True),
        ),
        migrations.AddField(
            model_name='student',
            name='blood_group',
            field=models.CharField(max_length=10, blank=True),
        ),
        migrations.AddField(
            model_name='student',
            name='nationality',
            field=models.Char<PERSON>ield(max_length=100, default='Unknown'),
        ),
        migrations.AddField(
            model_name='student',
            name='religion',
            field=models.CharField(max_length=100, blank=True),
        ),
        migrations.AddField(
            model_name='student',
            name='emergency_contact',
            field=models.Char<PERSON>ield(max_length=200, blank=True),
        ),
        migrations.Add<PERSON>ield(
            model_name='student',
            name='emergency_phone',
            field=models.Char<PERSON>ield(max_length=20, blank=True),
        ),
        migrations.AddField(
            model_name='student',
            name='medical_conditions',
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name='student',
            name='transport_mode',
            field=models.CharField(max_length=50, blank=True),
        ),
        migrations.AddField(
            model_name='student',
            name='house',
            field=models.CharField(max_length=100, blank=True),
        ),
        migrations.AddField(
            model_name='student',
            name='previous_school',
            field=models.CharField(max_length=255, blank=True),
        ),
        migrations.AddField(
            model_name='student',
            name='current_section_id',
            field=models.BigIntegerField(null=True, blank=True),
        ),
        migrations.AddField(
            model_name='student',
            name='admission_number',
            field=models.CharField(max_length=50, unique=True, null=True, blank=True),
        ),
    ]
