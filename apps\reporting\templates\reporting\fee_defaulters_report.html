{% extends "tenant_base.html" %}
{% load humanize static core_tags reporting_filters %}

{% block title %}{{ view_title|default:"Fee Defaulters Report" }}{% endblock %}

{% block content %}
<div class="container-fluid mt-3">
    {% with report_icon_class="bi bi-exclamation-triangle" %}
        {% include "partials/_report_header.html" %}
    {% endwith %}

    {% include "partials/_messages.html" %}
    {% include "reporting/_report_filter_export_card.html" with filter_form=filter_form report_code=report_code specific_filter_title="Defaulters Report Filters" %}

    <!-- Summary Card -->
    <div class="card mb-4">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="bi bi-exclamation-circle me-2"></i>Defaulters Summary</h5>
        </div>
        <div class="card-body">
            {% if academic_year or as_of_date %}
            <div class="row mb-3">
                <div class="col-md-12">
                    <p>
                        {% if academic_year %}<strong>Academic Year:</strong> {{ academic_year }} | {% endif %}
                        <strong>As of Date:</strong> {{ as_of_date|date:"M d, Y" }}
                    </p>
                </div>
            </div>
            {% endif %}
            
            <div class="row">
                <div class="col-md-3">
                    <div class="text-center">
                        <h6 class="text-muted">Total Defaulters</h6>
                        <p class="mb-0 fw-bold text-danger fs-4">{{ summary.total_defaulters|default:0 }}</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h6 class="text-muted">Total Overdue Amount</h6>
                        <p class="mb-0 fw-bold text-danger">{{ summary.total_overdue_amount|currency }}</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h6 class="text-muted">Average Outstanding</h6>
                        <p class="mb-0 fw-bold text-warning">{{ summary.avg_outstanding|currency }}</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h6 class="text-muted">Avg Days Overdue</h6>
                        <p class="mb-0 fw-bold text-info">{{ summary.avg_days_overdue|floatformat:0 }} days</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Risk Level Breakdown -->
    {% if summary.risk_breakdown %}
    <div class="card mb-4">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="bi bi-shield-exclamation me-2"></i>Risk Level Breakdown</h5>
        </div>
        <div class="card-body">
            <div class="row">
                {% for risk_level, count in summary.risk_breakdown.items %}
                <div class="col-md-3">
                    <div class="text-center">
                        <h6 class="text-muted">{{ risk_level }} Risk</h6>
                        <p class="mb-0 fw-bold {% if risk_level == 'Critical' %}text-danger{% elif risk_level == 'High' %}text-warning{% elif risk_level == 'Medium' %}text-info{% else %}text-success{% endif %}">
                            {{ count }}
                        </p>
                        {% if summary.total_defaulters > 0 %}
                        <small class="text-muted">{{ count|percentage:summary.total_defaulters|floatformat:1 }}%</small>
                        {% endif %}
                    </div>
                </div>
                {% endfor %}
            </div>
            
            <!-- Risk Level Progress Bars -->
            <div class="mt-3">
                {% for risk_level, count in summary.risk_breakdown.items %}
                {% if count > 0 %}
                <div class="mb-2">
                    <div class="d-flex justify-content-between">
                        <span>{{ risk_level }} Risk</span>
                        <span>{{ count }} ({{ count|percentage:summary.total_defaulters|floatformat:1 }}%)</span>
                    </div>
                    <div class="progress" style="height: 20px;">
                        <div class="progress-bar {% if risk_level == 'Critical' %}bg-danger{% elif risk_level == 'High' %}bg-warning{% elif risk_level == 'Medium' %}bg-info{% else %}bg-success{% endif %}" 
                             role="progressbar" style="width: {{ count|percentage:summary.total_defaulters }}%">
                        </div>
                    </div>
                </div>
                {% endif %}
                {% endfor %}
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Defaulters List -->
    {% if defaulters %}
    <div class="card">
        <div class="card-header bg-light d-flex justify-content-between align-items-center">
            <h5 class="mb-0"><i class="bi bi-list-ul me-2"></i>Fee Defaulters Details</h5>
            <span class="badge bg-danger">{{ defaulters|length }} defaulters</span>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-striped table-hover mb-0">
                    <thead class="table-dark">
                        <tr>
                            <th>Student</th>
                            <th>Class</th>
                            <th class="text-end">Outstanding Amount</th>
                            <th class="text-center">Max Days Overdue</th>
                            <th class="text-center">Invoices</th>
                            <th class="text-center">Risk Level</th>
                            {% if include_contact_info %}
                            <th>Contact Information</th>
                            {% endif %}
                            <th class="text-center">Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for defaulter in defaulters %}
                        <tr class="{% if defaulter.risk_level == 'Critical' %}table-danger{% elif defaulter.risk_level == 'High' %}table-warning{% endif %}">
                            <td>
                                <strong>{{ defaulter.student.get_full_name }}</strong>
                                <br><small class="text-muted">ID: {{ defaulter.student.student_number|default:defaulter.student.id }}</small>
                                {% if not defaulter.student.is_active %}
                                <br><span class="badge bg-secondary">Inactive</span>
                                {% endif %}
                            </td>
                            <td>
                                <span class="badge bg-info">{{ defaulter.class_name }}</span>
                            </td>
                            <td class="text-end">
                                <span class="text-danger fw-bold fs-6">{{ defaulter.total_outstanding|currency }}</span>
                                <br><small class="text-muted">{{ defaulter.invoice_count }} invoice{{ defaulter.invoice_count|pluralize }}</small>
                            </td>
                            <td class="text-center">
                                <span class="badge {% if defaulter.max_days_overdue >= 120 %}bg-danger{% elif defaulter.max_days_overdue >= 60 %}bg-warning{% else %}bg-info{% endif %}">
                                    {{ defaulter.max_days_overdue }} days
                                </span>
                                {% if defaulter.earliest_due_date %}
                                <br><small class="text-muted">Since: {{ defaulter.earliest_due_date|date:"M d" }}</small>
                                {% endif %}
                            </td>
                            <td class="text-center">
                                <span class="badge bg-secondary">{{ defaulter.invoice_count }}</span>
                                <br><small class="text-muted">
                                    Range: {{ defaulter.min_days_overdue }}-{{ defaulter.max_days_overdue }} days
                                </small>
                            </td>
                            <td class="text-center">
                                <span class="badge {% if defaulter.risk_level == 'Critical' %}bg-danger{% elif defaulter.risk_level == 'High' %}bg-warning{% elif defaulter.risk_level == 'Medium' %}bg-info{% else %}bg-success{% endif %}">
                                    {{ defaulter.risk_level }}
                                </span>
                            </td>
                            {% if include_contact_info %}
                            <td>
                                {% if defaulter.contact_info.parent_name %}
                                <strong>{{ defaulter.contact_info.parent_name }}</strong>
                                {% if defaulter.contact_info.phone %}
                                <br><small class="text-muted"><i class="bi bi-telephone me-1"></i>{{ defaulter.contact_info.phone }}</small>
                                {% endif %}
                                {% if defaulter.contact_info.email %}
                                <br><small class="text-muted"><i class="bi bi-envelope me-1"></i>{{ defaulter.contact_info.email }}</small>
                                {% endif %}
                                {% else %}
                                <span class="text-muted">No contact info</span>
                                {% endif %}
                            </td>
                            {% endif %}
                            <td class="text-center">
                                <div class="btn-group-vertical btn-group-sm">
                                    <button class="btn btn-outline-primary btn-sm" onclick="viewDetails('{{ defaulter.student.id }}')">
                                        <i class="bi bi-eye"></i> View
                                    </button>
                                    {% if defaulter.contact_info.phone %}
                                    <button class="btn btn-outline-success btn-sm" onclick="contactParent('{{ defaulter.contact_info.phone }}')">
                                        <i class="bi bi-telephone"></i> Call
                                    </button>
                                    {% endif %}
                                    {% if defaulter.contact_info.email %}
                                    <button class="btn btn-outline-info btn-sm" onclick="emailParent('{{ defaulter.contact_info.email }}')">
                                        <i class="bi bi-envelope"></i> Email
                                    </button>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                    <tfoot class="table-light">
                        <tr>
                            <th colspan="2" class="text-end">Totals:</th>
                            <th class="text-end text-danger">{{ summary.total_overdue_amount|currency }}</th>
                            <th class="text-center">{{ summary.avg_days_overdue|floatformat:0 }} avg</th>
                            <th class="text-center">{{ summary.total_overdue_invoices }}</th>
                            <th colspan="{% if include_contact_info %}3{% else %}2{% endif %}"></th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Class Breakdown -->
    {% if summary.class_breakdown %}
    <div class="card mt-4">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="bi bi-grid me-2"></i>Class-wise Defaulter Breakdown</h5>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-striped mb-0">
                    <thead class="table-dark">
                        <tr>
                            <th>Class</th>
                            <th class="text-center">Defaulters</th>
                            <th class="text-end">Total Outstanding</th>
                            <th class="text-end">Average per Defaulter</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for class_name, class_data in summary.class_breakdown.items %}
                        <tr>
                            <td><strong>{{ class_name }}</strong></td>
                            <td class="text-center">
                                <span class="badge bg-warning">{{ class_data.count }}</span>
                            </td>
                            <td class="text-end">
                                <span class="text-danger fw-bold">{{ class_data.amount|currency }}</span>
                            </td>
                            <td class="text-end">
                                {{ class_data.amount|divide:class_data.count|currency }}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Action Recommendations -->
    {% if defaulters %}
    <div class="alert alert-warning mt-4">
        <h5><i class="bi bi-lightbulb me-2"></i>Recommended Actions</h5>
        <div class="row">
            <div class="col-md-6">
                <h6>Immediate Actions:</h6>
                <ul>
                    {% if summary.risk_breakdown.Critical > 0 %}
                    <li><strong>Critical Cases:</strong> {{ summary.risk_breakdown.Critical }} students need immediate attention</li>
                    {% endif %}
                    {% if summary.risk_breakdown.High > 0 %}
                    <li><strong>High Risk:</strong> {{ summary.risk_breakdown.High }} students require follow-up calls</li>
                    {% endif %}
                    <li>Send payment reminders to all defaulters</li>
                    <li>Contact parents/guardians of critical cases</li>
                </ul>
            </div>
            <div class="col-md-6">
                <h6>Long-term Strategies:</h6>
                <ul>
                    <li>Implement payment plans for struggling families</li>
                    <li>Set up automated reminder systems</li>
                    <li>Review fee structure and payment terms</li>
                    <li>Consider early payment discounts</li>
                </ul>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- No Data Message -->
    {% if not defaulters %}
    <div class="card">
        <div class="card-body text-center py-5">
            <i class="bi bi-check-circle display-1 text-success mb-3"></i>
            <h4 class="text-success">No Fee Defaulters Found</h4>
            <p class="text-muted">Great news! No students have overdue fees matching your criteria.</p>
            <div class="mt-3">
                <a href="{% url 'reporting:fee_defaulters_report' %}" class="btn btn-outline-primary me-2">
                    <i class="bi bi-arrow-clockwise me-2"></i>Reset Filters
                </a>
            </div>
        </div>
    </div>
    {% endif %}

</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Animate progress bars
    $('.progress-bar').each(function() {
        var $this = $(this);
        var width = $this.css('width');
        $this.css('width', '0%').animate({width: width}, 1000);
    });
    
    // Highlight critical and high risk rows
    $('.table-danger, .table-warning').hover(
        function() {
            $(this).addClass('table-active');
        },
        function() {
            $(this).removeClass('table-active');
        }
    );
});

function viewDetails(studentId) {
    // Implement view student details functionality
    alert('View details for student ID: ' + studentId);
    // You can redirect to student account statement or details page
}

function contactParent(phone) {
    // Implement contact functionality
    if (phone) {
        window.open('tel:' + phone);
    }
}

function emailParent(email) {
    // Implement email functionality
    if (email) {
        window.open('mailto:' + email + '?subject=Outstanding Fee Payment Reminder');
    }
}
</script>
{% endblock %}

