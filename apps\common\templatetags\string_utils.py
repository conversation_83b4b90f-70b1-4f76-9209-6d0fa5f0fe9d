from django import template

register = template.Library()

@register.filter
def contains(value, arg):
    """Check if a string contains another string"""
    return str(arg) in str(value)


@register.filter
def class_name(value):
    if hasattr(value, '__class__'):
        return value.__class__.__name__
    return type(value).__name__

# --- ADD THIS NEW FILTER ---
@register.filter
def split(value, key):
    """
    Splits a string by the given key.
    Useful for getting parts of a path or string.
    """
    try:
        return value.split(key)
    except (AttributeError, TypeError):
        # If value is not a string or doesn't have split, return it as is
        return value


