{# templates/public_site/testimonial_form.html #}
{% extends "public_base.html" %}
{% load static %}

{% block title %}{{ view_title }} - School Fees Management Platform{% endblock %}

{% block extra_public_css %}
<style>
    .review-hero {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 4rem 0;
        text-align: center;
        position: relative;
        overflow: hidden;
    }

    .review-hero::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        opacity: 0.3;
    }

    .review-hero h1 {
        font-size: 3rem;
        font-weight: 700;
        margin-bottom: 1rem;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        position: relative;
        z-index: 1;
    }

    .review-hero p {
        font-size: 1.2rem;
        opacity: 0.9;
        margin-bottom: 0;
        position: relative;
        z-index: 1;
        max-width: 600px;
        margin: 0 auto;
    }

    .review-container {
        padding: 4rem 0;
        background: #f8fafc;
    }

    .review-form-card {
        background: white;
        border-radius: 20px;
        padding: 3rem;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        border: 1px solid #e2e8f0;
        max-width: 900px;
        margin: 0 auto;
    }

    .form-section {
        margin-bottom: 3rem;
        padding-bottom: 2rem;
        border-bottom: 1px solid #e2e8f0;
    }

    .form-section:last-child {
        border-bottom: none;
        margin-bottom: 0;
    }

    .form-section-title {
        color: #2d3748;
        font-size: 1.5rem;
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
    }

    .form-section-title i {
        margin-right: 0.75rem;
        color: #667eea;
        font-size: 1.3rem;
    }

    .premium-form-group {
        position: relative;
        margin-bottom: 2rem;
    }

    .premium-input,
    .premium-select,
    .premium-textarea {
        width: 100%;
        padding: 1rem 1rem 1rem 3rem;
        border: 2px solid #e2e8f0;
        border-radius: 12px;
        font-size: 1rem;
        transition: all 0.3s ease;
        background: #ffffff;
        color: #2d3748;
    }

    .premium-input:focus,
    .premium-select:focus,
    .premium-textarea:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        transform: translateY(-2px);
    }

    .premium-textarea {
        resize: vertical;
        min-height: 120px;
    }

    .premium-label {
        position: absolute;
        left: 3rem;
        top: 1rem;
        color: #718096;
        font-size: 1rem;
        transition: all 0.3s ease;
        pointer-events: none;
        background: white;
        padding: 0 0.5rem;
    }

    .premium-input:focus + .premium-label,
    .premium-input:not(:placeholder-shown) + .premium-label,
    .premium-select:focus + .premium-label,
    .premium-select:not([value=""]) + .premium-label,
    .premium-textarea:focus + .premium-label,
    .premium-textarea:not(:placeholder-shown) + .premium-label {
        top: -0.5rem;
        left: 2.5rem;
        font-size: 0.875rem;
        color: #667eea;
        font-weight: 500;
    }

    .field-icon {
        position: absolute;
        left: 1rem;
        top: 1rem;
        color: #718096;
        font-size: 1.1rem;
        transition: all 0.3s ease;
        z-index: 1;
    }

    .premium-input:focus ~ .field-icon,
    .premium-select:focus ~ .field-icon,
    .premium-textarea:focus ~ .field-icon {
        color: #667eea;
    }

    .help-text {
        font-size: 0.875rem;
        color: #718096;
        margin-top: 0.5rem;
        margin-left: 3rem;
    }

    .error-text {
        font-size: 0.875rem;
        color: #e53e3e;
        margin-top: 0.5rem;
        margin-left: 3rem;
    }

    .rating-group {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
        margin-top: 1rem;
    }

    .rating-item {
        background: #f7fafc;
        border-radius: 12px;
        padding: 1.5rem;
        text-align: center;
    }

    .rating-item h4 {
        color: #2d3748;
        margin-bottom: 1rem;
        font-size: 1rem;
    }

    .star-rating {
        display: flex;
        justify-content: center;
        gap: 0.25rem;
        margin-bottom: 0.5rem;
    }

    .star-rating input[type="radio"] {
        display: none;
    }

    .star-rating label {
        font-size: 1.5rem;
        color: #e2e8f0;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .star-rating label:hover,
    .star-rating label:hover ~ label,
    .star-rating input[type="radio"]:checked ~ label {
        color: #fbbf24;
    }

    .premium-checkbox-container {
        background: #f7fafc;
        border-radius: 12px;
        padding: 1.5rem;
        display: flex;
        align-items: center;
        margin-top: 1rem;
    }

    .premium-checkbox {
        width: 20px;
        height: 20px;
        margin-right: 1rem;
        accent-color: #667eea;
    }

    .premium-checkbox-label {
        color: #2d3748;
        font-weight: 500;
        margin: 0;
        cursor: pointer;
    }

    .premium-btn {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        color: white;
        padding: 1rem 3rem;
        border-radius: 12px;
        font-size: 1.1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        margin-right: 1rem;
    }

    .premium-btn:hover {
        background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
    }

    .btn-outline-gradient {
        background: transparent;
        border: 2px solid #667eea;
        color: #667eea;
        padding: 1rem 2rem;
        border-radius: 12px;
        font-weight: 600;
        text-decoration: none;
        display: inline-block;
        transition: all 0.3s ease;
    }

    .btn-outline-gradient:hover {
        background: #667eea;
        color: white;
        text-decoration: none;
        transform: translateY(-2px);
    }

    .success-message {
        background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 12px;
        margin-bottom: 2rem;
        display: flex;
        align-items: center;
    }

    .success-message i {
        margin-right: 0.75rem;
        font-size: 1.2rem;
    }

    .error-message {
        background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 12px;
        margin-bottom: 2rem;
        display: flex;
        align-items: center;
    }

    .error-message i {
        margin-right: 0.75rem;
        font-size: 1.2rem;
    }

    @media (max-width: 768px) {
        .review-hero h1 {
            font-size: 2.5rem;
        }

        .review-hero {
            padding: 3rem 0;
        }

        .review-container {
            padding: 3rem 0;
        }

        .review-form-card {
            padding: 2rem;
        }

        .premium-input,
        .premium-select,
        .premium-textarea {
            padding: 0.875rem 0.875rem 0.875rem 2.5rem;
        }

        .premium-label {
            left: 2.5rem;
        }

        .field-icon {
            left: 0.75rem;
        }

        .help-text,
        .error-text {
            margin-left: 2.5rem;
        }

        .rating-group {
            grid-template-columns: 1fr;
        }

        .premium-btn,
        .btn-outline-gradient {
            display: block;
            width: 100%;
            margin: 0.5rem 0;
            text-align: center;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="review-hero">
    <div class="container">
        <h1>Share Your Experience</h1>
        <p>Help other schools by sharing your experience with our platform. Your feedback helps us improve and helps others make informed decisions.</p>
    </div>
</section>

<!-- Review Form Section -->
<section class="review-container">
    <div class="container">
        <div class="review-form-card">
            <!-- Messages -->
            {% if messages %}
                {% for message in messages %}
                    {% if message.tags == 'success' %}
                        <div class="success-message">
                            <i class="bi bi-check-circle"></i>
                            {{ message }}
                        </div>
                    {% elif message.tags == 'error' %}
                        <div class="error-message">
                            <i class="bi bi-exclamation-triangle"></i>
                            {{ message }}
                        </div>
                    {% endif %}
                {% endfor %}
            {% endif %}

            <form method="post" novalidate>
                {% csrf_token %}

                <!-- Personal Information Section -->
                <div class="form-section">
                    <h3 class="form-section-title">
                        <i class="bi bi-person"></i>
                        Personal Information
                    </h3>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="premium-form-group">
                                {{ form.author_name }}
                                <label for="{{ form.author_name.id_for_label }}" class="premium-label">{{ form.author_name.label }}</label>
                                <i class="bi bi-person field-icon"></i>
                                {% if form.author_name.errors %}
                                    <div class="error-text">{{ form.author_name.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="premium-form-group">
                                {{ form.author_title }}
                                <label for="{{ form.author_title.id_for_label }}" class="premium-label">{{ form.author_title.label }}</label>
                                <i class="bi bi-person-badge field-icon"></i>
                                {% if form.author_title.errors %}
                                    <div class="error-text">{{ form.author_title.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="premium-form-group">
                                {{ form.email }}
                                <label for="{{ form.email.id_for_label }}" class="premium-label">{{ form.email.label }}</label>
                                <i class="bi bi-envelope field-icon"></i>
                                {% if form.email.help_text %}
                                    <div class="help-text">{{ form.email.help_text }}</div>
                                {% endif %}
                                {% if form.email.errors %}
                                    <div class="error-text">{{ form.email.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="premium-form-group">
                                {{ form.phone }}
                                <label for="{{ form.phone.id_for_label }}" class="premium-label">{{ form.phone.label }}</label>
                                <i class="bi bi-telephone field-icon"></i>
                                {% if form.phone.help_text %}
                                    <div class="help-text">{{ form.phone.help_text }}</div>
                                {% endif %}
                                {% if form.phone.errors %}
                                    <div class="error-text">{{ form.phone.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- School Information Section -->
                <div class="form-section">
                    <h3 class="form-section-title">
                        <i class="bi bi-building"></i>
                        School Information
                    </h3>

                    <div class="row">
                        <div class="col-md-8">
                            <div class="premium-form-group">
                                {{ form.school_name }}
                                <label for="{{ form.school_name.id_for_label }}" class="premium-label">{{ form.school_name.label }}</label>
                                <i class="bi bi-building field-icon"></i>
                                {% if form.school_name.errors %}
                                    <div class="error-text">{{ form.school_name.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="premium-form-group">
                                {{ form.location }}
                                <label for="{{ form.location.id_for_label }}" class="premium-label">{{ form.location.label }}</label>
                                <i class="bi bi-geo-alt field-icon"></i>
                                {% if form.location.help_text %}
                                    <div class="help-text">{{ form.location.help_text }}</div>
                                {% endif %}
                                {% if form.location.errors %}
                                    <div class="error-text">{{ form.location.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="premium-form-group">
                                {{ form.school_type }}
                                <label for="{{ form.school_type.id_for_label }}" class="premium-label">{{ form.school_type.label }}</label>
                                <i class="bi bi-mortarboard field-icon"></i>
                                {% if form.school_type.errors %}
                                    <div class="error-text">{{ form.school_type.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="premium-form-group">
                                {{ form.school_size }}
                                <label for="{{ form.school_size.id_for_label }}" class="premium-label">{{ form.school_size.label }}</label>
                                <i class="bi bi-people field-icon"></i>
                                {% if form.school_size.errors %}
                                    <div class="error-text">{{ form.school_size.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="premium-form-group">
                                {{ form.website }}
                                <label for="{{ form.website.id_for_label }}" class="premium-label">{{ form.website.label }}</label>
                                <i class="bi bi-globe field-icon"></i>
                                {% if form.website.help_text %}
                                    <div class="help-text">{{ form.website.help_text }}</div>
                                {% endif %}
                                {% if form.website.errors %}
                                    <div class="error-text">{{ form.website.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Review Content Section -->
                <div class="form-section">
                    <h3 class="form-section-title">
                        <i class="bi bi-chat-quote"></i>
                        Your Review
                    </h3>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="premium-form-group">
                                {{ form.review_category }}
                                <label for="{{ form.review_category.id_for_label }}" class="premium-label">{{ form.review_category.label }}</label>
                                <i class="bi bi-tag field-icon"></i>
                                {% if form.review_category.errors %}
                                    <div class="error-text">{{ form.review_category.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="premium-form-group">
                                {{ form.title }}
                                <label for="{{ form.title.id_for_label }}" class="premium-label">{{ form.title.label }}</label>
                                <i class="bi bi-chat-dots field-icon"></i>
                                {% if form.title.help_text %}
                                    <div class="help-text">{{ form.title.help_text }}</div>
                                {% endif %}
                                {% if form.title.errors %}
                                    <div class="error-text">{{ form.title.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="premium-form-group">
                        {{ form.quote }}
                        <label for="{{ form.quote.id_for_label }}" class="premium-label">{{ form.quote.label }}</label>
                        <i class="bi bi-chat-text field-icon"></i>
                        {% if form.quote.help_text %}
                            <div class="help-text">{{ form.quote.help_text }}</div>
                        {% endif %}
                        {% if form.quote.errors %}
                            <div class="error-text">{{ form.quote.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>

                <!-- Ratings Section -->
                <div class="form-section">
                    <h3 class="form-section-title">
                        <i class="bi bi-star"></i>
                        Rate Your Experience
                    </h3>

                    <div class="rating-group">
                        <div class="rating-item">
                            <h4>{{ form.overall_rating.label }}</h4>
                            <div class="star-rating">
                                {% for i in "54321"|make_list %}
                                    <input type="radio" name="{{ form.overall_rating.name }}" value="{{ i }}" id="overall_{{ i }}" required>
                                    <label for="overall_{{ i }}">★</label>
                                {% endfor %}
                            </div>
                            {% if form.overall_rating.errors %}
                                <div class="error-text">{{ form.overall_rating.errors.0 }}</div>
                            {% endif %}
                        </div>

                        <div class="rating-item">
                            <h4>{{ form.ease_of_use_rating.label }}</h4>
                            <div class="star-rating">
                                {% for i in "54321"|make_list %}
                                    <input type="radio" name="{{ form.ease_of_use_rating.name }}" value="{{ i }}" id="ease_{{ i }}">
                                    <label for="ease_{{ i }}">★</label>
                                {% endfor %}
                            </div>
                            {% if form.ease_of_use_rating.errors %}
                                <div class="error-text">{{ form.ease_of_use_rating.errors.0 }}</div>
                            {% endif %}
                        </div>

                        <div class="rating-item">
                            <h4>{{ form.customer_support_rating.label }}</h4>
                            <div class="star-rating">
                                {% for i in "54321"|make_list %}
                                    <input type="radio" name="{{ form.customer_support_rating.name }}" value="{{ i }}" id="support_{{ i }}">
                                    <label for="support_{{ i }}">★</label>
                                {% endfor %}
                            </div>
                            {% if form.customer_support_rating.errors %}
                                <div class="error-text">{{ form.customer_support_rating.errors.0 }}</div>
                            {% endif %}
                        </div>

                        <div class="rating-item">
                            <h4>{{ form.value_rating.label }}</h4>
                            <div class="star-rating">
                                {% for i in "54321"|make_list %}
                                    <input type="radio" name="{{ form.value_rating.name }}" value="{{ i }}" id="value_{{ i }}">
                                    <label for="value_{{ i }}">★</label>
                                {% endfor %}
                            </div>
                            {% if form.value_rating.errors %}
                                <div class="error-text">{{ form.value_rating.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Additional Information Section -->
                <div class="form-section">
                    <h3 class="form-section-title">
                        <i class="bi bi-info-circle"></i>
                        Additional Information
                    </h3>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="premium-form-group">
                                {{ form.usage_duration }}
                                <label for="{{ form.usage_duration.id_for_label }}" class="premium-label">{{ form.usage_duration.label }}</label>
                                <i class="bi bi-clock field-icon"></i>
                                {% if form.usage_duration.help_text %}
                                    <div class="help-text">{{ form.usage_duration.help_text }}</div>
                                {% endif %}
                                {% if form.usage_duration.errors %}
                                    <div class="error-text">{{ form.usage_duration.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="premium-form-group">
                                {{ form.previous_solution }}
                                <label for="{{ form.previous_solution.id_for_label }}" class="premium-label">{{ form.previous_solution.label }}</label>
                                <i class="bi bi-arrow-left-right field-icon"></i>
                                {% if form.previous_solution.help_text %}
                                    <div class="help-text">{{ form.previous_solution.help_text }}</div>
                                {% endif %}
                                {% if form.previous_solution.errors %}
                                    <div class="error-text">{{ form.previous_solution.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="premium-checkbox-container">
                        {{ form.would_recommend }}
                        <label for="{{ form.would_recommend.id_for_label }}" class="premium-checkbox-label">
                            {{ form.would_recommend.label }}
                        </label>
                    </div>
                    {% if form.would_recommend.errors %}
                        <div class="error-text">{{ form.would_recommend.errors.0 }}</div>
                    {% endif %}
                </div>

                <!-- Submit Section -->
                <div class="text-center">
                    <button type="submit" class="premium-btn">
                        <i class="bi bi-send me-2"></i>
                        Submit Review
                    </button>
                    <a href="{% url 'public_site:testimonial_list' %}" class="btn-outline-gradient">
                        <i class="bi bi-arrow-left me-2"></i>
                        Cancel
                    </a>
                </div>
            </form>
        </div>
    </div>
</section>

<script>
// Star rating functionality
document.addEventListener('DOMContentLoaded', function() {
    const starRatings = document.querySelectorAll('.star-rating');

    starRatings.forEach(rating => {
        const inputs = rating.querySelectorAll('input[type="radio"]');
        const labels = rating.querySelectorAll('label');

        labels.forEach((label, index) => {
            label.addEventListener('mouseenter', () => {
                labels.forEach((l, i) => {
                    if (i >= index) {
                        l.style.color = '#fbbf24';
                    } else {
                        l.style.color = '#e2e8f0';
                    }
                });
            });

            label.addEventListener('mouseleave', () => {
                const checkedInput = rating.querySelector('input[type="radio"]:checked');
                if (checkedInput) {
                    const checkedIndex = Array.from(inputs).indexOf(checkedInput);
                    labels.forEach((l, i) => {
                        if (i >= checkedIndex) {
                            l.style.color = '#fbbf24';
                        } else {
                            l.style.color = '#e2e8f0';
                        }
                    });
                } else {
                    labels.forEach(l => l.style.color = '#e2e8f0');
                }
            });

            label.addEventListener('click', () => {
                const input = inputs[index];
                input.checked = true;

                labels.forEach((l, i) => {
                    if (i >= index) {
                        l.style.color = '#fbbf24';
                    } else {
                        l.style.color = '#e2e8f0';
                    }
                });
            });
        });
    });
});
</script>
{% endblock %}


