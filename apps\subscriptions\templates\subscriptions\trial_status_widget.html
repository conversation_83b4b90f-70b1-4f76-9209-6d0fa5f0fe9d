{% load static %}
{% load i18n %}

<!-- Trial Status Widget -->
{% if subscription %}
    {% if subscription.status == 'TRIALING' %}
        {% if trial_days_remaining is not None %}
            {% if trial_days_remaining > 0 %}
                <!-- Active Trial -->
                <div class="card border-info mb-4">
                    <div class="card-header bg-info text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-clock"></i> {% trans "Free Trial Active" %}
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <p class="mb-2">
                                    <strong>
                                        {% blocktrans count days=trial_days_remaining %}
                                            {{ days }} day remaining
                                        {% plural %}
                                            {{ days }} days remaining
                                        {% endblocktrans %}
                                    </strong>
                                </p>
                                <p class="text-muted mb-0">
                                    {% trans "You're currently on a free trial with up to" %} 
                                    <strong>{{ subscription.plan.max_students }} {% trans "students" %}</strong>.
                                </p>
                            </div>
                            <div class="col-md-4 text-end">
                                <a href="{% url 'subscriptions:plan_selection' %}" class="btn btn-primary">
                                    <i class="fas fa-arrow-up"></i> {% trans "Upgrade Now" %}
                                </a>
                            </div>
                        </div>
                        
                        <!-- Progress bar showing trial progress -->
                        {% if subscription.trial_start_date and subscription.trial_end_date %}
                            {% with total_days=subscription.plan.trial_period_days %}
                                {% with elapsed_days=total_days|sub:trial_days_remaining %}
                                    {% with progress_percent=elapsed_days|div:total_days|mul:100 %}
                                        <div class="mt-3">
                                            <div class="d-flex justify-content-between small text-muted mb-1">
                                                <span>{% trans "Trial Progress" %}</span>
                                                <span>{{ elapsed_days }}/{{ total_days }} {% trans "days" %}</span>
                                            </div>
                                            <div class="progress" style="height: 6px;">
                                                <div class="progress-bar bg-info" role="progressbar" 
                                                     style="width: {{ progress_percent }}%" 
                                                     aria-valuenow="{{ progress_percent }}" 
                                                     aria-valuemin="0" 
                                                     aria-valuemax="100">
                                                </div>
                                            </div>
                                        </div>
                                    {% endwith %}
                                {% endwith %}
                            {% endwith %}
                        {% endif %}
                    </div>
                </div>
            {% else %}
                <!-- Expired Trial -->
                <div class="card border-danger mb-4">
                    <div class="card-header bg-danger text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-exclamation-triangle"></i> {% trans "Trial Expired" %}
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <p class="mb-2">
                                    <strong>{% trans "Your 7-day trial has ended." %}</strong>
                                </p>
                                <p class="text-muted mb-0">
                                    {% trans "Upgrade to a paid plan to continue managing students and accessing all features." %}
                                </p>
                            </div>
                            <div class="col-md-4 text-end">
                                <a href="{% url 'subscriptions:plan_selection' %}" class="btn btn-danger">
                                    <i class="fas fa-credit-card"></i> {% trans "Upgrade Now" %}
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            {% endif %}
        {% endif %}
    {% elif subscription.status == 'ACTIVE' %}
        <!-- Active Subscription -->
        <div class="card border-success mb-4">
            <div class="card-header bg-success text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-check-circle"></i> {% trans "Subscription Active" %}
                </h5>
            </div>
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <p class="mb-2">
                            <strong>{{ subscription.plan.name }}</strong>
                        </p>
                        <p class="text-muted mb-0">
                            {% if subscription.plan.max_students >= 9999 %}
                                {% trans "Unlimited students" %}
                            {% else %}
                                {% blocktrans with limit=subscription.plan.max_students %}Up to {{ limit }} students{% endblocktrans %}
                            {% endif %}
                            {% if subscription.next_billing_date %}
                                • {% trans "Next billing:" %} {{ subscription.next_billing_date|date:"M d, Y" }}
                            {% endif %}
                        </p>
                    </div>
                    <div class="col-md-4 text-end">
                        <a href="{% url 'subscriptions:subscription_details' %}" class="btn btn-outline-success">
                            <i class="fas fa-cog"></i> {% trans "Manage" %}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    {% elif subscription.status == 'PAST_DUE' %}
        <!-- Past Due Subscription -->
        <div class="card border-warning mb-4">
            <div class="card-header bg-warning text-dark">
                <h5 class="card-title mb-0">
                    <i class="fas fa-exclamation-circle"></i> {% trans "Payment Past Due" %}
                </h5>
            </div>
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <p class="mb-2">
                            <strong>{% trans "Your payment is overdue." %}</strong>
                        </p>
                        <p class="text-muted mb-0">
                            {% trans "Please update your payment method to continue using all features." %}
                        </p>
                    </div>
                    <div class="col-md-4 text-end">
                        <a href="{% url 'subscriptions:update_payment_method' %}" class="btn btn-warning">
                            <i class="fas fa-credit-card"></i> {% trans "Update Payment" %}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    {% endif %}
{% else %}
    <!-- No Subscription -->
    <div class="card border-primary mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="card-title mb-0">
                <i class="fas fa-rocket"></i> {% trans "Get Started" %}
            </h5>
        </div>
        <div class="card-body">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <p class="mb-2">
                        <strong>{% trans "Start your free 7-day trial today!" %}</strong>
                    </p>
                    <p class="text-muted mb-0">
                        {% trans "Try our platform with up to 10 students completely free." %}
                    </p>
                </div>
                <div class="col-md-4 text-end">
                    <a href="{% url 'subscriptions:plan_selection' %}" class="btn btn-primary">
                        <i class="fas fa-play"></i> {% trans "Start Trial" %}
                    </a>
                </div>
            </div>
        </div>
    </div>
{% endif %}

<!-- Student Usage Widget (for trial plans) -->
{% if subscription and subscription.plan.max_students <= 50 %}
    <div class="card mb-4">
        <div class="card-header">
            <h6 class="card-title mb-0">
                <i class="fas fa-users"></i> {% trans "Student Usage" %}
            </h6>
        </div>
        <div class="card-body">
            {% with current_students=student_count|default:0 max_students=subscription.plan.max_students %}
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span>{{ current_students }} / {{ max_students }} {% trans "students" %}</span>
                    <span class="badge {% if current_students >= max_students %}bg-danger{% elif current_students >= max_students|mul:0.8 %}bg-warning{% else %}bg-success{% endif %}">
                        {% widthratio current_students max_students 100 %}%
                    </span>
                </div>
                <div class="progress" style="height: 8px;">
                    <div class="progress-bar {% if current_students >= max_students %}bg-danger{% elif current_students >= max_students|mul:0.8 %}bg-warning{% else %}bg-success{% endif %}" 
                         role="progressbar" 
                         style="width: {% widthratio current_students max_students 100 %}%" 
                         aria-valuenow="{{ current_students }}" 
                         aria-valuemin="0" 
                         aria-valuemax="{{ max_students }}">
                    </div>
                </div>
                {% if current_students >= max_students %}
                    <small class="text-danger mt-1 d-block">
                        <i class="fas fa-exclamation-triangle"></i> 
                        {% trans "Student limit reached. Upgrade to add more students." %}
                    </small>
                {% elif current_students >= max_students|mul:0.8 %}
                    <small class="text-warning mt-1 d-block">
                        <i class="fas fa-info-circle"></i> 
                        {% trans "Approaching student limit. Consider upgrading soon." %}
                    </small>
                {% endif %}
            {% endwith %}
        </div>
    </div>
{% endif %}
