# D:\school_fees_saas_v2\core\settings.py

import os
from pathlib import Path
from dotenv import load_dotenv
from django.urls import reverse_lazy


# --- Base Directory and .env Loading ---
BASE_DIR = Path(__file__).resolve().parent.parent
load_dotenv(BASE_DIR / '.env') # Make sure .env is in the same directory as manage.py


# --- Core Django Settings (ensure these are already in your file) ---
SECRET_KEY = os.getenv('SECRET_KEY')
DEBUG = os.getenv('DEBUG', 'False') == 'True'


# core/settings.py
# ...
PAYSTACK_SECRET_KEY = os.getenv('PAYSTACK_SECRET_KEY')
PAYSTACK_PUBLIC_KEY = os.getenv('PAYSTACK_PUBLIC_KEY')


# --- Environment Dependent Settings ---
DEBUG = os.getenv('DEBUG', 'False').lower() == 'true'
DJANGO_ENV = os.getenv('DJANGO_ENV', 'development') # 'development' or 'production'


# TENANT_BASE_DOMAIN from .env is crucial.
# For local development using runserver, it's typically 'yourdomain.test:8000' or 'localhost:8000'.
# For production, it would be 'yourdomain.com' (port 80/443 implied by scheme).
TENANT_BASE_DOMAIN_FROM_ENV = os.getenv('TENANT_BASE_DOMAIN', 'localhost:8000')


# --- Determine Scheme, Hostname, and Port ---
APP_SCHEME = "https" if not DEBUG and DJANGO_ENV == 'production' else "http"

_domain_parts = TENANT_BASE_DOMAIN_FROM_ENV.split(':')
APP_HOSTNAME = _domain_parts[0]  # e.g., myapp.test or localhost
APP_PORT_STR = _domain_parts[1] if len(_domain_parts) > 1 else None # e.g., "8000" or None


# --- Domain & Host Configuration ---
TENANT_BASE_DOMAIN = os.getenv('TENANT_BASE_DOMAIN', 'myapp.test:8000')
base_hostname = TENANT_BASE_DOMAIN.split(':')[0] # Should be 'myapp.test'

# --- ALLOWED_HOSTS ---
# This list defines which host/domain names Django will serve.
# The Host header from the browser (without port) must be in this list.
ALLOWED_HOSTS = [
    APP_HOSTNAME,    # e.g., myapp.test (if TENANT_BASE_DOMAIN_FROM_ENV="myapp.test:8000") or localhost
    'localhost',     # Always good for local dev
    '127.0.0.1',   # Always good for local dev
    'myapp.test',        # Your public domain
    '.myapp.test',       # Wildcard for all subdomains like zharatest.myapp.test
]
if APP_HOSTNAME not in ['localhost', '127.0.0.1']:
    ALLOWED_HOSTS.append(f".{APP_HOSTNAME}") # e.g., .myapp.test (to allow all subdomains)
ALLOWED_HOSTS = list(set(ALLOWED_HOSTS)) # Remove duplicates


# core/settings.py

# --- Define which of your apps are primarily shared vs. tenant-specific ---
# This helps in reasoning about their models.


SHARED_APPS = [
    'django_tenants',  # Must be first
    'mptt',
    'debug_toolbar', 
    
    'apps.platform_management.apps.PlatformManagementConfig', 
    # 'apps.announcements', # If removed

    # Core Django apps for public schema functionality
    'django.contrib.admin',
    'django.contrib.auth',      # For public User, Group, Permission
    'django.contrib.contenttypes',
    'django.contrib.sessions',  # <<< ENSURE THIS IS PRESENT AND CORRECTLY SPELLED
    'django.contrib.messages',  # You fixed this one
    'django.contrib.staticfiles',
    'django.contrib.humanize',
    
    'mathfilters',
    
    # Your core tenant lifecycle and user management apps
    'apps.tenants.apps.TenantsConfig',    
    'apps.users.apps.UsersConfig',        

    # Your other primarily shared custom apps
    'apps.public_site.apps.PublicSiteConfig',
    'apps.accounting', 
    'apps.subscriptions.apps.SubscriptionsConfig', 
    'apps.common.apps.CommonConfig',     
    
    'apps.common_filters.apps.CommonFiltersConfig',
    
    'apps.core.apps.CoreConfig', 
    'apps.scheduler.apps.SchedulerConfig',
    'management', 
    'apps.communication.apps.CommunicationConfig', # Assuming this also has tenant-specific models/functionality

    # Shared third-party apps
    'crispy_forms',
    'crispy_bootstrap5',
    'widget_tweaks',
    'django_filters',
    'django_celery_beat', 
    'auditlog',
]

TENANT_APPS = [
    # Django contrib apps needed for tenant context
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes', # Contenttypes is often needed in tenant for permissions on tenant models
    # 'django.contrib.sessions',  # Usually NOT here if sessions are shared (default behavior)
    # 'django.contrib.messages',  # Usually NOT here if messages are shared
    # 'django.contrib.staticfiles',
    
    # ... your tenant-specific apps ...
    'apps.portal_auth',
    'apps.schools',    
    'apps.announcements',
    'apps.students',     
    'apps.hr',                 
    'apps.fees',             
    'apps.finance',       
    'apps.payments',     
    'apps.portal_admin',
    'apps.parent_portal',
    'apps.school_calendar',

    'apps.reporting',
]

INSTALLED_APPS = list(SHARED_APPS) + [
    app for app in TENANT_APPS if app not in SHARED_APPS
]


INTERNAL_IPS = [
    '127.0.0.1',
]


# For django-tenants
TENANT_MODEL = "tenants.Client"  # Replace with your Tenant model (e.g., "tenants.SchoolTenant")
TENANT_DOMAIN_MODEL = "tenants.Domain"  # Replace with your Domain model

# Crispy forms settings
CRISPY_ALLOWED_TEMPLATE_PACKS = "bootstrap5"
CRISPY_TEMPLATE_PACK = "bootstrap5"

# --- Middleware (Keep it minimal for now, only essentials for login & tenants) ---

MIDDLEWARE = [
    # 1. Django-Tenants must be first for tenant identification.
    'django_tenants.middleware.main.TenantMainMiddleware',
    
    # <<< INSERT DJANGO DEBUG TOOLBAR MIDDLEWARE HERE >>>
    'debug_toolbar.middleware.DebugToolbarMiddleware', 

    # 2. Standard Django security and session middleware.
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware', # TenantMainMiddleware needs session
    'django.middleware.locale.LocaleMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',

    # 3. Standard Django authentication.
    'django.contrib.auth.middleware.AuthenticationMiddleware',

    # 4. Your custom middleware that depend on tenant and authenticated user.
    #    The order among these might matter based on their specific logic.
    'apps.common.middleware.TenantFeatureMiddleware',      # Needs request.tenant
    'apps.common.middleware.EffectiveTenantUserMiddleware',# Needs request.tenant and request.user
    
    # Your other custom middleware (ensure they don't unset request.tenant)
    # 'apps.common.middleware.TenantContextMiddleware', # What does this do? Where should it be?
                                                    # If it relies on request.tenant, it must be after TenantMainMiddleware.
    # 'apps.common.middleware.SessionSharingMiddleware',# If it modifies session based on tenant, also after.
    # 'apps.common.middleware.PreventParentAdminAccessMiddleware', # Needs request.user and path
    # 'apps.common.middleware.CSRFDebugMiddleware', # Usually for debug, order less critical unless it interferes.

    # 5. Standard Django messages and clickjacking.
    'django.contrib.messages.middleware.MessageMiddleware',
    
    'apps.subscriptions.middleware.SubscriptionAccessMiddleware',
    
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    
    # Add your custom middleware here
    'apps.subscriptions.middleware.SubscriptionLimitMiddleware', 
]



ROOT_URLCONF = 'core.urls'

# --- Templates (Ensure CSRF processor is present) ---
TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [BASE_DIR / 'templates'],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
                'django.template.context_processors.csrf', # <<< ESSENTIAL
                # Temporarily comment out custom context processors if unsure
                
                'apps.common.context_processors.global_context',
                
                # 'apps.common.context_processors.tenant_features_processor',
                # # 'core.context_processors.user_and_tenant_info', # <<< ESSENTIAL
                # # 'apps.common.context_processors.user_type_context', 
                # # # Add this line
                # 'apps.common.context_processors.platform_announcements_processor',
            ],
            'debug': DEBUG,
            
            'builtins': [
                # Standard Django builtins (often implicitly available but good to list)
                # 'django.contrib.staticfiles.templatetags.staticfiles',
                'django.templatetags.i18n',

                # Django-tenants (if you use its specific tags like get_tenant_domain without {% load %})
                # 'django_tenants.templatetags.tenant_extras', # We removed this as it was causing issues

                # Your custom templatetags
                # 'apps.core.templatetags.core_tags', 
                'apps.common.templatetags.string_utils', 
                # 'apps.common.templatetags.nav_utils', # If you created and use this

                # Third-party app templatetags
                'widget_tweaks.templatetags.widget_tweaks', # <<< ADD THIS EXACT LINE
                # Add other third-party builtins if needed, e.g., for crispy_forms
                # 'crispy_forms.templatetags.crispy_forms_tags',
            ],
        },
    },
]

WSGI_APPLICATION = 'core.wsgi.application'


# core/settings.py

DATABASES = {
    'default': {
        'ENGINE': 'django_tenants.postgresql_backend', # <<<< COMMENT THIS OUT
        # 'ENGINE': 'django.db.backends.postgresql',    # <<<< USE THIS TEMPORARILY
        'NAME': os.getenv('DATABASE_NAME'),
        'USER': os.getenv('DATABASE_USER'),
        'PASSWORD': os.getenv('DATABASE_PASSWORD'),
        'HOST': os.getenv('DATABASE_HOST'),
        'PORT': os.getenv('DATABASE_PORT'),
        'OPTIONS': {
            #'disable_server_side_cursors': True,
        },
    }
}


DATABASE_ROUTERS = ('django_tenants.routers.TenantSyncRouter',)

AUTH_PASSWORD_VALIDATORS = [
    {'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',},
    {'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',},
    {'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',},
    {'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',},
]

LANGUAGE_CODE = 'en-us'
TIME_ZONE = 'Africa/Harare'
USE_I18N = True
USE_TZ = True

STATIC_URL = 'static/'
STATICFILES_DIRS = [
    BASE_DIR / 'static', # For project-level static files like your custom CSS
]
# IMPORTANT FOR PRODUCTION & link_callback:
# STATIC_ROOT must be defined, even if it's just for xhtml2pdf to resolve static paths in dev
# If you don't run collectstatic in dev, link_callback will try STATICFILES_DIRS.
#STATIC_ROOT = BASE_DIR / 'staticfiles_collected_for_pdf_only' # Or your actual collectstatic dir

STATIC_ROOT = BASE_DIR / 'staticfiles_for_pdf_resolution' # Can be a dummy path for dev if not running collectstatic

DEFAULT_FILE_STORAGE = 'django_tenants.files.storage.TenantFileSystemStorage'
MEDIA_URL = '/media/'
MEDIA_ROOT = BASE_DIR / 'mediafiles_base' # Where SchoolProfile logos are uploaded


DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

TENANT_MODEL = "tenants.School"
TENANT_DOMAIN_MODEL = "tenants.Domain"

SHOW_PUBLIC_IF_NO_TENANT_FOUND = True
PUBLIC_SCHEMA_URLCONF = 'core.urls'


DATABASE_ROUTERS = ('django_tenants.routers.TenantSyncRouter',)


TENANT_BASE_DOMAIN_CONFIG = os.getenv('TENANT_BASE_DOMAIN', 'localhost:8000') 
# Example: 'myapp.test:8000' or 'localhost:8000'

# --- CSRF and Session Cookie Settings ---
# For development with tenants on subdomains like 'tenant1.myapp.test:8000',
# cookies need to be scoped to the parent domain to be shared.

# Temporarily disable custom cookie domains to fix session issues
SESSION_COOKIE_DOMAIN = None  # Let Django handle this automatically
CSRF_COOKIE_DOMAIN = None     # Let Django handle this automatically

# if APP_HOSTNAME not in ['localhost', '127.0.0.1']:
#     # For domains like 'myapp.test', scope cookies to '.myapp.test'
#     SESSION_COOKIE_DOMAIN = f".{APP_HOSTNAME}"
#     CSRF_COOKIE_DOMAIN = f".{APP_HOSTNAME}"
# else:
#     # For 'localhost' or '127.0.0.1', let Django use its default (None),
#     # or explicitly set to 'localhost' / '127.0.0.1'. None is usually fine.
#     SESSION_COOKIE_DOMAIN = None # Or APP_HOSTNAME
#     CSRF_COOKIE_DOMAIN = None    # Or APP_HOSTNAME

SESSION_COOKIE_SAMESITE = 'Lax'
CSRF_COOKIE_SAMESITE = 'Lax' # Recommended for most cases

# Secure cookies should only be used over HTTPS
SESSION_COOKIE_SECURE = APP_SCHEME == "https"
CSRF_COOKIE_SECURE = APP_SCHEME == "https"

# Session configuration - try cache backend to fix persistence issues
SESSION_ENGINE = 'django.contrib.sessions.backends.cache'  # Use cache sessions for testing
SESSION_CACHE_ALIAS = 'default'  # Use default cache
SESSION_SAVE_EVERY_REQUEST = True  # Save session on every request to prevent loss
SESSION_EXPIRE_AT_BROWSER_CLOSE = False  # Keep sessions across browser sessions
SESSION_COOKIE_AGE = 86400  # 24 hours
SESSION_COOKIE_HTTPONLY = True  # Prevent JavaScript access to session cookie
SESSION_COOKIE_NAME = 'sessionid'  # Default name, but explicit is better

CSRF_COOKIE_HTTPONLY = False  # Default is False. Set to True if your client-side JS doesn't need the CSRF cookie.
                            # For standard forms, False is not an issue.

# --- CSRF Trusted Origins ---
# This tells Django which origins are allowed to make POST requests.
# Format: scheme://hostname OR scheme://hostname:port (if port is non-standard)

CSRF_TRUSTED_ORIGINS = []

# 1. Primary application domain (and its subdomains if applicable)
_origin_base = f"{APP_SCHEME}://{APP_HOSTNAME}"
if APP_PORT_STR: # If port is specified in TENANT_BASE_DOMAIN_FROM_ENV
    _origin_base_with_port = f"{_origin_base}:{APP_PORT_STR}"
    CSRF_TRUSTED_ORIGINS.append(_origin_base_with_port)
    if APP_HOSTNAME not in ['localhost', '127.0.0.1']:
        CSRF_TRUSTED_ORIGINS.append(f"{APP_SCHEME}://*.{APP_HOSTNAME}:{APP_PORT_STR}")
else: # No port specified (implies standard port 80 for http, 443 for https)
    CSRF_TRUSTED_ORIGINS.append(_origin_base)
    if APP_HOSTNAME not in ['localhost', '127.0.0.1']:
        CSRF_TRUSTED_ORIGINS.append(f"{APP_SCHEME}://*.{APP_HOSTNAME}")

# 2. Add localhost and 127.0.0.1 explicitly for runserver, using the port from APP_PORT_STR
#    (if TENANT_BASE_DOMAIN_FROM_ENV was like 'myapp.test:8000', APP_PORT_STR will be '8000').
#    If TENANT_BASE_DOMAIN_FROM_ENV was 'localhost:8000', it's already covered by _origin_base_with_port.
#    This ensures direct access via localhost:xxxx or 127.0.0.1:xxxx works.

_runserver_port = APP_PORT_STR if APP_PORT_STR else "8000" # Fallback to 8000 if no port in env

_localhost_origin = f"{APP_SCHEME}://localhost:{_runserver_port}"
if _localhost_origin not in CSRF_TRUSTED_ORIGINS:
    CSRF_TRUSTED_ORIGINS.append(_localhost_origin)

_loopback_origin = f"{APP_SCHEME}://127.0.0.1:{_runserver_port}"
if _loopback_origin not in CSRF_TRUSTED_ORIGINS:
    CSRF_TRUSTED_ORIGINS.append(_loopback_origin)

CSRF_TRUSTED_ORIGINS = list(set(CSRF_TRUSTED_ORIGINS)) # Remove duplicates


# --- Optional: Logging Configuration (Basic example for seeing CSRF logs) ---
# Ensure you have a LOGGING setting block in your settings.py
# If not, this is a very basic one:

LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'simple_no_time': {
            'format': '{levelname} {module}: {message}', # REMOVED {asctime}
            'style': '{',
        },
        'original_simple': { # Keep your original for reference or other handlers
            'format': '{levelname} {asctime} {module}: {message}',
            'style': '{',
        },
    },
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
            'formatter': 'simple_no_time', # USE THE FORMATTER WITHOUT ASCTIME
        },
    },
    'root': { # Ensure root uses this handler, or specific loggers do
        'handlers': ['console'],
        'level': 'DEBUG', # Keep DEBUG for now
    },
    'loggers': {
        'django': {
            'handlers': ['console'],
            'level': os.getenv('DJANGO_LOG_LEVEL', 'INFO'),
            'propagate': False,
        },
        'project.context_processors': { # Matches logger name used above
            'handlers': ['console'],
            'level': 'DEBUG', # <<< SET TO DEBUG
            'propagate': False,
        },
        'apps': { # This will cover getLogger(__name__) in your apps
            'handlers': ['console'],
            'level': 'DEBUG',
            'propagate': False, # Set to False to ensure ONLY simple_no_time is used for these
        },
        # If you defined logger_backends = logging.getLogger('project.backends')
        # then add a specific logger for it:
        'project.backends': { # Or whatever name you used for logger_backends
            'handlers': ['console'],
            'level': 'DEBUG',
            'propagate': False,
        },
        # ADD THIS NEW SPECIFIC LOGGER
        'project.tenant_signals': { # Must match the name used in getLogger()
            'handlers': ['console'],
            'level': 'DEBUG',       # Explicitly set to DEBUG
            'propagate': False,     # Prevent passing to root if you want only this handler's format
        }
    }
}




# --- Verification Print (Very useful for debugging setup) ---
# This will print to console when Django starts (e.g., with runserver)
print(f"\n--- SETTINGS CHECK ---")
print(f"DEBUG: {DEBUG}")
print(f"DJANGO_ENV: {DJANGO_ENV}")
print(f"TENANT_BASE_DOMAIN_FROM_ENV: {TENANT_BASE_DOMAIN_FROM_ENV}")
print(f"APP_SCHEME: {APP_SCHEME}")
print(f"APP_HOSTNAME: {APP_HOSTNAME}")
print(f"APP_PORT_STR: {APP_PORT_STR if APP_PORT_STR else '(Standard http:80 or https:443)'}")
print(f"ALLOWED_HOSTS: {ALLOWED_HOSTS}")
print(f"SESSION_COOKIE_DOMAIN: {SESSION_COOKIE_DOMAIN if SESSION_COOKIE_DOMAIN else '(Default/Current Host)'}")
print(f"SESSION_COOKIE_SECURE: {SESSION_COOKIE_SECURE}")
print(f"CSRF_COOKIE_DOMAIN: {CSRF_COOKIE_DOMAIN if CSRF_COOKIE_DOMAIN else '(Default/Current Host)'}")
print(f"CSRF_COOKIE_SECURE: {CSRF_COOKIE_SECURE}")
print(f"CSRF_TRUSTED_ORIGINS: {CSRF_TRUSTED_ORIGINS}")
print(f"--- END SETTINGS CHECK ---\n")

# IMPORTANT: Ensure this settings block is placed correctly within your existing settings.py,
# usually after initial imports and basic path setups, but before app-specific settings
# if those app-specific settings depend on these values.
# Make sure required variables like DEBUG, SECRET_KEY, DATABASES, INSTALLED_APPS, MIDDLEWARE etc.
# are defined elsewhere in your settings.py as per a standard Django project structure.



# --- Your Custom Settings Check ---
# This block will now only execute for the main runserver process
if os.environ.get("DJANGO_MAIN_PROCESS") == "true":
    print("\n--- SETTINGS CHECK (Main Process Only) ---") # Added (Main Process Only) for clarity
    print(f"DEBUG: {DEBUG}")
    # Assuming DJANGO_ENV is set elsewhere or you derive it
    print(f"DJANGO_ENV: {os.environ.get('DJANGO_ENV', 'Not Set')}") 
    # Assuming TENANT_BASE_DOMAIN is loaded into settings from .env
    print(f"TENANT_BASE_DOMAIN_FROM_ENV: {os.environ.get('TENANT_BASE_DOMAIN', 'Not Set in Env')}") 
    print(f"APP_SCHEME: {APP_SCHEME if 'APP_SCHEME' in locals() else 'Not Defined in Settings'}") # Check if defined
    print(f"APP_HOSTNAME: {APP_HOSTNAME if 'APP_HOSTNAME' in locals() else 'Not Defined in Settings'}")
    print(f"APP_PORT_STR: {APP_PORT_STR if 'APP_PORT_STR' in locals() else 'Not Defined in Settings'}")
    print(f"ALLOWED_HOSTS: {ALLOWED_HOSTS}")
    print(f"SESSION_COOKIE_DOMAIN: {SESSION_COOKIE_DOMAIN}")
    print(f"SESSION_COOKIE_SECURE: {SESSION_COOKIE_SECURE}")
    print(f"CSRF_COOKIE_DOMAIN: {CSRF_COOKIE_DOMAIN}")
    print(f"CSRF_COOKIE_SECURE: {CSRF_COOKIE_SECURE}")
    print(f"CSRF_TRUSTED_ORIGINS: {CSRF_TRUSTED_ORIGINS}")
    print("--- END SETTINGS CHECK ---\n")
# --- End of Custom Settings Check ---


AUTH_USER_MODEL = 'users.User' # Your existing public User model
STAFF_USER_MODEL = 'schools.StaffUser' # Assuming StaffUser is in apps.schools.models
PARENT_USER_MODEL = 'students.ParentUser' # Assuming ParentUser is in apps.students.models

# --- Authentication Backends ---

# MIGRATION_MODULES = {
#     'finance': None, # Tells Django to ignore 'finance' migrations
# }

AUTHENTICATION_BACKENDS = [
    # 1. For authenticating PublicUser (owner) AS a StaffUser on tenant domain
    'apps.schools.backends.TenantOwnerAsStaffBackend', # Or wherever TenantOwnerAsStaffBackend is

    # 2. For authenticating StaffUser directly on tenant domain
    'apps.schools.backends.TenantStaffBackend',

    # 3. For authenticating ParentUser directly on tenant domain
    'apps.schools.backends.TenantParentBackend', # Or wherever TenantParentBackend is

    # 4. THIS IS NEW: For providing CORRECT permission checks for StaffUser
    'apps.schools.backends.StaffUserModelBackend',

    # 5. For authenticating PublicUser (settings.AUTH_USER_MODEL) on public domain (e.g. /admin/)
    # AND for providing permission checks for PublicUser.
    # This backend will be skipped for StaffUser permission checks if StaffUserModelBackend handles it.
    'django.contrib.auth.backends.ModelBackend',
]

CELERY_BEAT_SCHEDULER = 'django_celery_beat.schedulers:DatabaseScheduler'

# Django Crispy Forms settings
CRISPY_ALLOWED_TEMPLATE_PACKS = "bootstrap5"
CRISPY_TEMPLATE_PACK = "bootstrap5"


# # --- Login URLs ---
# LOGIN_URL = reverse_lazy('users:school_admin_login') # Public Admin Login
# LOGIN_REDIRECT_URL = '/' # Fallback if login view doesn't redirect
# LOGOUT_REDIRECT_URL = reverse_lazy('public_site:home')


LOGIN_URL = 'users:school_admin_login' # Your existing public admin login
LOGIN_REDIRECT_URL = 'public_site:home' # Or wherever public admins redirect
LOGOUT_REDIRECT_URL = 'public_site:home'

# --- Tenant Specific Settings ---
TENANT_LOGIN_URL = 'schools:staff_login' # For staff login on tenant sites
# You might also want a tenant-specific logout redirect or dashboard:
# TENANT_LOGIN_REDIRECT_URL = 'schools:dashboard' 
# TENANT_LOGOUT_REDIRECT_URL = 'schools:staff_login'


# --- Email Configuration ---
EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'

# <AUTHOR> <EMAIL>'

DEFAULT_FROM_EMAIL = os.getenv('DEFAULT_FROM_EMAIL', f'School Fees Platform <noreply@{APP_HOSTNAME}>') # APP_HOSTNAME from your settings
SERVER_EMAIL = DEFAULT_FROM_EMAIL # For error emails to admins
ADMINS = [('Your Name', '<EMAIL>')] # For error reports
MANAGERS = ADMINS




PLATFORM_DEFAULT_CURRENCY_SYMBOL = '$'
SUBSCRIPTION_MONTH_DAYS = 30 # For approximate end date calculation
SUBSCRIPTION_YEAR_DAYS = 365
REQUIRE_PAYMENT_GATEWAY_FOR_ACTIVATION = False # Set to True when gateway is live
# STRIPE_WEBHOOK_SIGNING_SECRET = os.getenv('STRIPE_WEBHOOK_SIGNING_SECRET') # For webhooks



SIMULATE_PAYMENTS = True # For development
REQUIRE_PAYMENT_FOR_ACTIVATION = False # For development if SIMULATE_PAYMENTS is True
SUBSCRIPTION_MONTH_DAYS = 30
SUBSCRIPTION_YEAR_DAYS = 365
PAYMENT_SUCCESS_URL_NAME = 'subscriptions:payment_success'
PAYMENT_CANCEL_URL_NAME = 'subscriptions:payment_cancel'
# PLATFORM_DEFAULT_CURRENCY_SYMBOL = '$' (already there)



# ==============================================================================
# CUSTOM APPLICATION SETTINGS
# ==============================================================================

# --- Accounting Configuration Codes ---
# These codes should match the 'code' field of your AccountType model instances.
# It's recommended to create these AccountType instances in each tenant's schema
# (e.g., via data migrations or initial setup script for new tenants).

# --- Chart of Account CODES for specific default accounts ---
# These point to specific ChartOfAccount instances by their 'code'.
ACCOUNTING_ACCOUNTS_RECEIVABLE_CODE = os.getenv('ACCOUNTING_AR_CODE', '1200')
ACCOUNTING_DEFAULT_CASH_ACCOUNT_CODE = os.getenv('ACCOUNTING_CASH_CODE', '1010')
ACCOUNTING_DEFAULT_BANK_ACCOUNT_CODE = os.getenv('ACCOUNTING_BANK_CODE', '1020')
ACCOUNTING_DISCOUNT_GIVEN_CODE = os.getenv('ACCOUNTING_DISCOUNT_CODE', '4210') # Often an Expense or Contra-Revenue
ACCOUNTING_RETAINED_EARNINGS_CODE = os.getenv('ACCOUNTING_RETAINED_EARNINGS_CODE', '3300') # Equity
ACCOUNTING_DEFAULT_INCOME_ACCOUNT_CODE = os.getenv('ACCOUNTING_DEFAULT_INCOME_CODE', '4000') # General Fee Income
ACCOUNTING_DEFAULT_EXPENSE_ACCOUNT_CODE = os.getenv('ACCOUNTING_DEFAULT_EXPENSE_CODE', '5000') # General Operating Expense

# --- AccountType CODES for Financial Statement Grouping ---
# These codes refer to the 'code' field of your AccountType model,
# used to categorize accounts for P&L, Balance Sheet, Cash Flow.

# For Balance Sheet Categories
ACCOUNTING_CURRENT_ASSET_TYPE_CODE = os.getenv('ACCOUNTING_CA_TYPE_CODE', 'CA')         # e.g., Current Asset
ACCOUNTING_NON_CURRENT_ASSET_TYPE_CODE = os.getenv('ACCOUNTING_NCA_TYPE_CODE', 'NCA')   # e.g., Non-Current Asset
ACCOUNTING_CURRENT_LIABILITY_TYPE_CODE = os.getenv('ACCOUNTING_CL_TYPE_CODE', 'CL')     # e.g., Current Liability
ACCOUNTING_NON_CURRENT_LIABILITY_TYPE_CODE = os.getenv('ACCOUNTING_NCL_TYPE_CODE', 'NCL') # e.g., Non-Current Liability
ACCOUNTING_EQUITY_TYPE_CODE = os.getenv('ACCOUNTING_EQ_TYPE_CODE', 'EQUITY')       # e.g., Equity

# For Income Statement (Profit & Loss) Categories
ACCOUNTING_REVENUE_TYPE_CODE = os.getenv('ACCOUNTING_REVENUE_TYPE_CODE', 'REVENUE')     # e.g., Revenue
ACCOUNTING_COGS_TYPE_CODE = os.getenv('ACCOUNTING_COGS_TYPE_CODE', 'COGS')           # e.g., Cost of Goods Sold (if applicable)
ACCOUNTING_EXPENSE_TYPE_CODE = os.getenv('ACCOUNTING_OPEXP_TYPE_CODE', 'OPEX')        # For general/operating expenses
# You might add more specific expense type codes if needed, e.g., ADMIN_EXPENSE_TYPE_CODE
# ACCOUNTING_OTHER_INCOME_TYPE_CODE = os.getenv('ACCOUNTING_OTH_INC_TYPE_CODE', 'OTHINC')
# ACCOUNTING_OTHER_EXPENSE_TYPE_CODE = os.getenv('ACCOUNTING_OTH_EXP_TYPE_CODE', 'OTHEXP')

# # For Cash Flow Statement (to identify Cash & Cash Equivalent Account Types)
# ACCOUNTING_CASH_ACCOUNT_TYPE_CODE = os.getenv('ACCOUNTING_CASH_ACC_TYPE_CODE', 'CASH_ACC') # Type for pure Cash accounts
# ACCOUNTING_BANK_ACCOUNT_TYPE_CODE = os.getenv('ACCOUNTING_BANK_ACC_TYPE_CODE', 'BANK_ACC') # Type for Bank accounts
# # ACCOUNTING_CASH_EQUIVALENT_TYPE_CODE = os.getenv('ACCOUNTING_CE_ACC_TYPE_CODE', 'CE') # If you have this category


# core/settings.py
ACCOUNTING_CASH_TYPE_CODE = 'CASH' # The 'code' field value for your Cash AccountType
ACCOUNTING_BANK_TYPE_CODE = 'BANK' # The 'code' field value for your Bank AccountType
# --- END Accounting Configuration Codes ---


COA_ONLINE_PAYMENTS_BANK = '1010' # Example code for your bank account receiving online payments
COA_ACCOUNTS_RECEIVABLE_CONTROL = '1200' # Example code for your main AR control account



# ==============================================================================
# CELERY & CELERY BEAT SETTINGS
# ==============================================================================

# Broker and Result Backend Configuration
# Ensure Redis is running. These URLs are read from environment variables for
# security and flexibility, falling back to a local default for development.
CELERY_BROKER_URL = os.getenv('CELERY_BROKER_URL', 'redis://localhost:6379/0')
CELERY_RESULT_BACKEND = os.getenv('CELERY_RESULT_BACKEND', 'redis://localhost:6379/0')

# Task serialization settings
CELERY_ACCEPT_CONTENT = ['json']
CELERY_TASK_SERIALIZER = 'json'
CELERY_RESULT_SERIALIZER = 'json'
CELERY_TIMEZONE = TIME_ZONE # Use Django's timezone for consistency

# Optional but useful task settings
CELERY_TASK_TRACK_STARTED = True
CELERY_TASK_TIME_LIMIT = 30 * 60 # Tasks will be terminated if they run longer than 30 mins

# Queue settings
CELERY_TASK_DEFAULT_QUEUE = 'default'
CELERY_TASK_CREATE_MISSING_QUEUES = True # Good for development

# --- Django-Celery-Beat Configuration ---
# Use the database to store the schedule.
# This allows you to add, edit, and disable scheduled tasks from the Django admin.
CELERY_BEAT_SCHEDULER = 'django_celery_beat.schedulers:DatabaseScheduler'

# Import the crontab scheduler for defining cron-like schedules
from celery.schedules import crontab

# --- DEFINE THE STATIC SCHEDULE DICTIONARY ---
# This dictionary defines all your scheduled (periodic) tasks that are
# configured in your code. You can also add schedules dynamically via the
# Django admin if you are using the DatabaseScheduler.
CELERY_BEAT_SCHEDULE = {
    # This is the new task for annual leave accrual.
    'accrue-leave-annually': {
        'task': 'hr.accrue_leave_for_all_tenants_annually',
        # This schedule uses crontab format to run at 1:00 AM on the 1st of January every year.
        'schedule': crontab(month_of_year=1, day_of_month=1, hour=1, minute=0),
        'args': (), # No arguments are needed for the top-level task
        'options': {'queue': 'periodic'}, # Optional: route to a specific queue
    },
    
    # EXAMPLE: A task that runs daily to clean up old sessions
    'cleanup-daily-sessions': {
        'task': 'django.contrib.sessions.tasks.cleanup_sessions',
        'schedule': crontab(hour=4, minute=30, day_of_week='*'), # Runs at 4:30 AM every day
    },

    # EXAMPLE: A task for sending fee reminders on the 15th of every month
    # 'send-fee-reminders-monthly': {
    #     'task': 'apps.fees.tasks.send_monthly_fee_reminders',
    #     'schedule': crontab(day_of_month=15, hour=9, minute=0),
    # },
}

# NOTE ON DJANGO-TENANTS & CELERY:
# Your current approach where the top-level task (`accrue_leave_for_all_tenants_annually`)
# fetches all tenants from the public schema and then triggers a sub-task for each one
# (`accrue_leave_for_single_tenant_annually.delay(tenant.schema_name)`) is an
# excellent and robust pattern. It correctly handles multi-tenancy without needing
# complex Celery base Task classes.

# ==============================================================================
# END OF CELERY SETTINGS
# ==============================================================================

