#!/usr/bin/env python
"""
Fix Fox Completely
Fix all remaining issues in fox schema

Usage: python fix_fox_completely.py
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from django.db import connection
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def fix_fox_completely():
    """Fix all remaining issues in fox schema"""
    
    logger.info("=== FIXING FOX COMPLETELY ===")
    
    try:
        with connection.cursor() as cursor:
            # 1. Fix is_active column in schools_academicyear
            cursor.execute('SET search_path TO "fox"')
            
            try:
                # Check if is_active column exists
                cursor.execute("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.columns 
                        WHERE table_schema = 'fox' 
                        AND table_name = 'schools_academicyear'
                        AND column_name = 'is_active'
                    )
                """)
                
                if not cursor.fetchone()[0]:
                    cursor.execute("ALTER TABLE schools_academicyear ADD COLUMN is_active BOOLEAN DEFAULT TRUE")
                    logger.info("✅ Added is_active column to schools_academicyear")
                
                # Update existing records
                cursor.execute("UPDATE schools_academicyear SET is_active = TRUE WHERE is_active IS NULL")
                logger.info("✅ Updated is_active values")
                
            except Exception as e:
                logger.error(f"❌ Failed to fix is_active column: {e}")
            
            # 2. Create portal_admin_adminactivitylog table
            try:
                cursor.execute("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_schema = 'fox' 
                        AND table_name = 'portal_admin_adminactivitylog'
                    )
                """)
                
                if not cursor.fetchone()[0]:
                    # Get structure from alpha
                    cursor.execute('SET search_path TO "alpha"')
                    
                    cursor.execute("""
                        SELECT column_name, data_type, character_maximum_length, 
                               numeric_precision, numeric_scale, is_nullable, column_default
                        FROM information_schema.columns 
                        WHERE table_schema = 'alpha' 
                        AND table_name = 'portal_admin_adminactivitylog'
                        ORDER BY ordinal_position
                    """)
                    
                    columns = cursor.fetchall()
                    
                    if columns:
                        # Build CREATE TABLE statement
                        columns_def = []
                        
                        for col_name, data_type, max_len, num_prec, num_scale, nullable, default in columns:
                            col_def = f'"{col_name}" '
                            
                            # Handle data types
                            if data_type == 'character varying':
                                if max_len:
                                    col_def += f'VARCHAR({max_len})'
                                else:
                                    col_def += 'VARCHAR(255)'
                            elif data_type == 'numeric' and num_prec and num_scale:
                                col_def += f'NUMERIC({num_prec},{num_scale})'
                            elif data_type == 'timestamp with time zone':
                                col_def += 'TIMESTAMP WITH TIME ZONE'
                            elif data_type == 'timestamp without time zone':
                                col_def += 'TIMESTAMP'
                            else:
                                col_def += data_type.upper()
                            
                            # Handle nullable
                            if nullable == 'NO':
                                col_def += ' NOT NULL'
                            
                            # Handle defaults (skip sequences for now)
                            if default and not default.startswith('nextval'):
                                col_def += f' DEFAULT {default}'
                            
                            columns_def.append(col_def)
                        
                        create_sql = f'CREATE TABLE "portal_admin_adminactivitylog" ({", ".join(columns_def)})'
                        
                        cursor.execute('SET search_path TO "fox"')
                        cursor.execute(create_sql)
                        
                        # Create sequence if needed
                        if any('id' == col[0] for col in columns):
                            try:
                                cursor.execute('CREATE SEQUENCE IF NOT EXISTS portal_admin_adminactivitylog_id_seq')
                                cursor.execute('ALTER TABLE portal_admin_adminactivitylog ALTER COLUMN id SET DEFAULT nextval(\'portal_admin_adminactivitylog_id_seq\')')
                            except:
                                pass
                        
                        logger.info("✅ Created portal_admin_adminactivitylog table")
                    else:
                        logger.warning("⚠️  Could not get structure for portal_admin_adminactivitylog from alpha")
                else:
                    logger.info("✅ portal_admin_adminactivitylog table already exists")
                    
            except Exception as e:
                logger.error(f"❌ Failed to create portal_admin_adminactivitylog: {e}")
            
            # 3. Create other missing tables that might be needed
            cursor.execute('SET search_path TO "fox"')
            
            missing_tables = [
                'students_student',
                'students_parentuser', 
                'announcements_announcement',
                'communication_communicationlog'
            ]
            
            for table in missing_tables:
                try:
                    cursor.execute(f"""
                        SELECT EXISTS (
                            SELECT FROM information_schema.tables 
                            WHERE table_schema = 'fox' 
                            AND table_name = '{table}'
                        )
                    """)
                    
                    if not cursor.fetchone()[0]:
                        # Get structure from alpha
                        cursor.execute('SET search_path TO "alpha"')
                        
                        cursor.execute(f"""
                            SELECT column_name, data_type, character_maximum_length, 
                                   numeric_precision, numeric_scale, is_nullable, column_default
                            FROM information_schema.columns 
                            WHERE table_schema = 'alpha' 
                            AND table_name = '{table}'
                            ORDER BY ordinal_position
                        """)
                        
                        columns = cursor.fetchall()
                        
                        if columns:
                            # Build CREATE TABLE statement
                            columns_def = []
                            
                            for col_name, data_type, max_len, num_prec, num_scale, nullable, default in columns:
                                col_def = f'"{col_name}" '
                                
                                # Handle data types
                                if data_type == 'character varying':
                                    if max_len:
                                        col_def += f'VARCHAR({max_len})'
                                    else:
                                        col_def += 'VARCHAR(255)'
                                elif data_type == 'numeric' and num_prec and num_scale:
                                    col_def += f'NUMERIC({num_prec},{num_scale})'
                                elif data_type == 'timestamp with time zone':
                                    col_def += 'TIMESTAMP WITH TIME ZONE'
                                elif data_type == 'timestamp without time zone':
                                    col_def += 'TIMESTAMP'
                                else:
                                    col_def += data_type.upper()
                                
                                # Handle nullable
                                if nullable == 'NO':
                                    col_def += ' NOT NULL'
                                
                                # Handle defaults (skip sequences for now)
                                if default and not default.startswith('nextval'):
                                    col_def += f' DEFAULT {default}'
                                
                                columns_def.append(col_def)
                            
                            create_sql = f'CREATE TABLE "{table}" ({", ".join(columns_def)})'
                            
                            cursor.execute('SET search_path TO "fox"')
                            cursor.execute(create_sql)
                            
                            # Create sequence if needed
                            if any('id' == col[0] for col in columns):
                                try:
                                    cursor.execute(f'CREATE SEQUENCE IF NOT EXISTS {table}_id_seq')
                                    cursor.execute(f'ALTER TABLE {table} ALTER COLUMN id SET DEFAULT nextval(\'{table}_id_seq\')')
                                except:
                                    pass
                            
                            logger.info(f"✅ Created {table} table")
                        else:
                            logger.warning(f"⚠️  Could not get structure for {table} from alpha")
                    else:
                        logger.info(f"✅ {table} table already exists")
                        
                except Exception as e:
                    logger.error(f"❌ Failed to create {table}: {e}")
            
            # 4. Verify fixes
            logger.info("Verifying fixes...")
            
            cursor.execute('SET search_path TO "fox"')
            
            # Check is_active column
            cursor.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.columns 
                    WHERE table_schema = 'fox' 
                    AND table_name = 'schools_academicyear'
                    AND column_name = 'is_active'
                )
            """)
            
            if cursor.fetchone()[0]:
                logger.info("✅ is_active column verified")
            else:
                logger.error("❌ is_active column still missing")
            
            # Check portal_admin_adminactivitylog table
            cursor.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_schema = 'fox' 
                    AND table_name = 'portal_admin_adminactivitylog'
                )
            """)
            
            if cursor.fetchone()[0]:
                logger.info("✅ portal_admin_adminactivitylog table verified")
            else:
                logger.error("❌ portal_admin_adminactivitylog table still missing")
            
            logger.info("✅ Fox complete fix finished!")
            
    except Exception as e:
        logger.error(f"❌ Failed to fix fox completely: {e}")

def main():
    """Main function"""
    logger.info("=== FOX COMPLETE FIX ===")
    
    try:
        fix_fox_completely()
        
        logger.info("\n🎉 FOX COMPLETE FIX FINISHED!")
        logger.info("Fox schema should now be fully functional!")
        
        return True
        
    except Exception as e:
        logger.error(f"Fox complete fix failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
