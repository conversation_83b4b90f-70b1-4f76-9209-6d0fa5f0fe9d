#!/usr/bin/env python
"""
Add Roll Number to Fox
Add the missing roll_number column to students_student in fox

Usage: python add_roll_number_fox.py
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from django.db import connection
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def add_roll_number_fox():
    """Add roll_number column to students_student in fox"""
    
    logger.info("=== ADDING ROLL_NUMBER TO FOX ===")
    
    try:
        with connection.cursor() as cursor:
            cursor.execute('SET search_path TO "fox"')
            
            # Check if roll_number column exists
            cursor.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.columns 
                    WHERE table_schema = 'fox' 
                    AND table_name = 'students_student'
                    AND column_name = 'roll_number'
                )
            """)
            
            if not cursor.fetchone()[0]:
                cursor.execute("ALTER TABLE students_student ADD COLUMN roll_number VARCHAR(50) DEFAULT ''")
                logger.info("✅ Added roll_number column to students_student in fox")
            else:
                logger.info("✅ roll_number column already exists in fox")
            
            # Verify
            cursor.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.columns 
                    WHERE table_schema = 'fox' 
                    AND table_name = 'students_student'
                    AND column_name = 'roll_number'
                )
            """)
            
            if cursor.fetchone()[0]:
                logger.info("✅ roll_number column verified in fox")
            else:
                logger.error("❌ roll_number column still missing in fox")
            
    except Exception as e:
        logger.error(f"❌ Failed to add roll_number to fox: {e}")

def main():
    """Main function"""
    logger.info("=== ADD ROLL_NUMBER FOX ===")
    
    try:
        add_roll_number_fox()
        
        logger.info("\n🎉 ROLL_NUMBER ADDED TO FOX!")
        
        return True
        
    except Exception as e:
        logger.error(f"Add roll_number fox failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
