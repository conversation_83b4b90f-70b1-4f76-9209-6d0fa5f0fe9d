{% extends "tenant_base.html" %}
{% load humanize static core_tags reporting_filters %}

{% block title %}{{ view_title|default:"Audit Trail Report" }}{% endblock %}

{% block content %}
<div class="container-fluid mt-3">
    {% with report_icon_class="bi bi-shield-check" %}
        {% include "partials/_report_header.html" %}
    {% endwith %}

    {% include "partials/_messages.html" %}
    {% include "partials/_report_filter_form.html" with filter_form=filter_form report_code=report_code specific_filter_title="Audit Trail Filters" %}

    <!-- Audit Summary -->
    <div class="card mb-4">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="bi bi-graph-up me-2"></i>Audit Trail Summary</h5>
        </div>
        <div class="card-body">
            <div class="row mb-3">
                <div class="col-md-12">
                    <p>
                        <strong>Date Range:</strong> {{ summary.date_range }} | 
                        <strong>Total Entries:</strong> {{ summary.total_entries }} | 
                        <strong>Details:</strong> {% if show_details %}Shown{% else %}Hidden{% endif %}
                    </p>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-3">
                    <div class="text-center">
                        <h6 class="text-muted">Total Entries</h6>
                        <p class="mb-0 fw-bold text-primary fs-4">{{ summary.total_entries }}</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h6 class="text-muted">User Actions</h6>
                        <p class="mb-0 fw-bold text-success fs-4">{{ summary.user_entries }}</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h6 class="text-muted">System Actions</h6>
                        <p class="mb-0 fw-bold text-info fs-4">{{ summary.system_entries }}</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h6 class="text-muted">Unique Users</h6>
                        <p class="mb-0 fw-bold text-warning">{{ summary.unique_users }}</p>
                    </div>
                </div>
            </div>
            
            <!-- Activity Distribution -->
            <div class="mt-3">
                <h6>Activity Distribution</h6>
                <div class="progress" style="height: 25px;">
                    {% with user_percentage=summary.user_entries|percentage:summary.total_entries system_percentage=summary.system_entries|percentage:summary.total_entries %}
                    <div class="progress-bar bg-success" role="progressbar" style="width: {{ user_percentage }}%">
                        User: {{ user_percentage|floatformat:0 }}%
                    </div>
                    <div class="progress-bar bg-info" role="progressbar" style="width: {{ system_percentage }}%">
                        System: {{ system_percentage|floatformat:0 }}%
                    </div>
                    {% endwith %}
                </div>
                <small class="text-muted">
                    Most active user: {{ summary.most_active_user }}
                </small>
            </div>
        </div>
    </div>

    <!-- Activity Breakdown -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="bi bi-pie-chart me-2"></i>Transaction Types</h5>
                </div>
                <div class="card-body">
                    {% for type, count in summary.type_counts.items %}
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>{{ type }}</span>
                            <span class="fw-bold">{{ count }}</span>
                        </div>
                        <div class="progress" style="height: 15px;">
                            {% with percentage=count|percentage:summary.total_entries %}
                            <div class="progress-bar {% if type == 'Invoice' %}bg-primary{% elif type == 'Payment' %}bg-success{% elif type == 'Journal Entry' %}bg-warning{% else %}bg-info{% endif %}" 
                                 role="progressbar" style="width: {{ percentage }}%">
                            </div>
                            {% endwith %}
                        </div>
                    </div>
                    {% empty %}
                    <p class="text-muted">No transaction data available</p>
                    {% endfor %}
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="bi bi-activity me-2"></i>Action Types</h5>
                </div>
                <div class="card-body">
                    {% for action, count in summary.action_counts.items %}
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>{{ action }}</span>
                            <span class="fw-bold">{{ count }}</span>
                        </div>
                        <div class="progress" style="height: 15px;">
                            {% with percentage=count|percentage:summary.total_entries %}
                            <div class="progress-bar {% if action == 'CREATE' %}bg-success{% elif action == 'UPDATE' %}bg-warning{% elif action == 'DELETE' %}bg-danger{% elif action == 'POST' %}bg-info{% else %}bg-secondary{% endif %}" 
                                 role="progressbar" style="width: {{ percentage }}%">
                            </div>
                            {% endwith %}
                        </div>
                    </div>
                    {% empty %}
                    <p class="text-muted">No action data available</p>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>

    <!-- Audit Trail Entries -->
    {% if audit_entries %}
    <div class="card">
        <div class="card-header bg-light d-flex justify-content-between align-items-center">
            <h5 class="mb-0"><i class="bi bi-table me-2"></i>Audit Trail Entries</h5>
            <span class="badge bg-primary">{{ audit_entries|length }} entries</span>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-striped table-hover mb-0">
                    <thead class="table-dark">
                        <tr>
                            <th>Timestamp</th>
                            <th>Type</th>
                            <th class="text-center">Action</th>
                            <th>User</th>
                            <th>Description</th>
                            <th class="text-end">Amount</th>
                            {% if show_details %}
                            <th>Details</th>
                            {% endif %}
                        </tr>
                    </thead>
                    <tbody>
                        {% for entry in audit_entries %}
                        <tr class="{% if entry.user_type == 'System' %}table-light{% endif %}">
                            <td>
                                <strong>{{ entry.timestamp|date:"M d, Y" }}</strong>
                                <br><small class="text-muted">{{ entry.timestamp|time:"H:i:s" }}</small>
                            </td>
                            <td>
                                <span class="badge {% if entry.transaction_type == 'Invoice' %}bg-primary{% elif entry.transaction_type == 'Payment' %}bg-success{% elif entry.transaction_type == 'Journal Entry' %}bg-warning{% else %}bg-info{% endif %}">
                                    {{ entry.transaction_type }}
                                </span>
                            </td>
                            <td class="text-center">
                                <span class="badge {% if entry.action == 'CREATE' %}bg-success{% elif entry.action == 'UPDATE' %}bg-warning{% elif entry.action == 'DELETE' %}bg-danger{% elif entry.action == 'POST' %}bg-info{% else %}bg-secondary{% endif %}">
                                    {{ entry.action }}
                                </span>
                            </td>
                            <td>
                                {{ entry.user }}
                                {% if entry.user_type == 'System' %}
                                <br><small class="text-muted">System</small>
                                {% endif %}
                            </td>
                            <td>
                                {{ entry.object_description }}
                            </td>
                            <td class="text-end">
                                {% if entry.amount %}
                                <span class="fw-bold text-success">{{ entry.amount|currency }}</span>
                                {% else %}
                                <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            {% if show_details %}
                            <td>
                                {% if entry.details %}
                                <button class="btn btn-sm btn-outline-info" type="button" data-bs-toggle="collapse" data-bs-target="#details-{{ forloop.counter }}" aria-expanded="false">
                                    <i class="bi bi-info-circle"></i> Details
                                </button>
                                <div class="collapse mt-2" id="details-{{ forloop.counter }}">
                                    <div class="card card-body">
                                        {% for key, value in entry.details.items %}
                                        <small><strong>{{ key|title }}:</strong> {{ value }}</small><br>
                                        {% endfor %}
                                    </div>
                                </div>
                                {% else %}
                                <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            {% endif %}
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Audit Compliance Information -->
    {% if audit_entries %}
    <div class="card mt-4">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="bi bi-shield-check me-2"></i>Audit Compliance Information</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h6>Audit Trail Completeness</h6>
                    <ul class="list-unstyled">
                        <li><i class="bi bi-check-circle text-success me-2"></i>Complete transaction history maintained</li>
                        <li><i class="bi bi-check-circle text-success me-2"></i>User actions properly attributed</li>
                        <li><i class="bi bi-check-circle text-success me-2"></i>Timestamps accurately recorded</li>
                        <li><i class="bi bi-check-circle text-success me-2"></i>System actions clearly identified</li>
                        <li><i class="bi bi-check-circle text-success me-2"></i>Change details preserved</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6>Audit Statistics</h6>
                    <ul class="list-unstyled">
                        <li><i class="bi bi-info-circle text-info me-2"></i>
                            {{ summary.total_entries }} total audit entries recorded
                        </li>
                        <li><i class="bi bi-person text-primary me-2"></i>
                            {{ summary.user_entries }} user-initiated actions
                        </li>
                        <li><i class="bi bi-cpu text-info me-2"></i>
                            {{ summary.system_entries }} system-generated actions
                        </li>
                        <li><i class="bi bi-people text-warning me-2"></i>
                            {{ summary.unique_users }} unique users active
                        </li>
                        <li><i class="bi bi-calendar text-muted me-2"></i>
                            Period: {{ summary.date_range }}
                        </li>
                    </ul>
                </div>
            </div>
            
            <hr>
            
            <div class="alert alert-info">
                <h6 class="alert-heading"><i class="bi bi-info-circle me-2"></i>Audit Trail Purpose</h6>
                <p class="mb-0">
                    This audit trail provides a complete record of all financial transactions and system changes. 
                    It ensures accountability, supports regulatory compliance, and enables forensic analysis when needed. 
                    All entries are immutable and provide a reliable history of system activity for audit purposes.
                </p>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- No Data Message -->
    {% if not audit_entries %}
    <div class="card">
        <div class="card-body text-center py-5">
            <i class="bi bi-shield-check display-1 text-muted mb-3"></i>
            <h4 class="text-muted">No Audit Trail Data Found</h4>
            <p class="text-muted">No audit trail entries found for the selected criteria.</p>
            <div class="mt-3">
                <a href="{% url 'reporting:audit_trail_report' %}" class="btn btn-outline-primary me-2">
                    <i class="bi bi-arrow-clockwise me-2"></i>Reset Filters
                </a>
            </div>
        </div>
    </div>
    {% endif %}

</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Animate progress bars
    $('.progress-bar').each(function() {
        var $this = $(this);
        var width = $this.css('width');
        $this.css('width', '0%').animate({width: width}, 1500);
    });
    
    // Highlight system entries
    $('.table-light').hover(
        function() {
            $(this).addClass('table-active');
        },
        function() {
            $(this).removeClass('table-active');
        }
    );
    
    // Add click handlers for audit rows
    $('tbody tr').on('click', function() {
        $(this).toggleClass('table-info');
    });
    
    // Auto-collapse details after 10 seconds
    $('.collapse').on('shown.bs.collapse', function() {
        var $this = $(this);
        setTimeout(function() {
            $this.collapse('hide');
        }, 10000);
    });
});
</script>
{% endblock %}

