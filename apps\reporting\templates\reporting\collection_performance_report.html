{% extends "tenant_base.html" %}
{% load humanize static core_tags reporting_filters %}

{% block title %}{{ view_title|default:"Collection Performance Report" }}{% endblock %}

{% block content %}
<div class="container-fluid mt-3">
    {% with report_icon_class="bi bi-collection" %}
        {% include "partials/_report_header.html" %}
    {% endwith %}

    {% include "partials/_messages.html" %}
    {% include "partials/_report_filter_form.html" with filter_form=filter_form report_code=report_code specific_filter_title="Collection Performance Filters" %}

    <!-- Collection Performance Overview -->
    <div class="card mb-4">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="bi bi-speedometer me-2"></i>Collection Performance Overview</h5>
        </div>
        <div class="card-body">
            <div class="row mb-3">
                <div class="col-md-12">
                    <p>
                        <strong>Date Range:</strong> {{ start_date }} to {{ end_date }} | 
                        <strong>Performance Metric:</strong> {{ performance_metric|title }} | 
                        <strong>Efficiency Grade:</strong> 
                        <span class="badge {% if collection_summary.efficiency_grade == 'A' %}bg-success{% elif collection_summary.efficiency_grade == 'B' %}bg-info{% elif collection_summary.efficiency_grade == 'C' %}bg-warning{% else %}bg-danger{% endif %} fs-6">
                            {{ collection_summary.efficiency_grade }}
                        </span>
                    </p>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-3">
                    <div class="text-center">
                        <h6 class="text-muted">Collection Rate</h6>
                        <p class="mb-0 fw-bold text-primary fs-4">{{ collection_summary.collection_rate|floatformat:1 }}%</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h6 class="text-muted">Overdue Rate</h6>
                        <p class="mb-0 fw-bold text-warning fs-4">{{ collection_summary.overdue_rate|floatformat:1 }}%</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h6 class="text-muted">Full Payment Rate</h6>
                        <p class="mb-0 fw-bold text-success fs-4">{{ collection_summary.full_payment_rate|floatformat:1 }}%</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h6 class="text-muted">Efficiency Score</h6>
                        <p class="mb-0 fw-bold text-info fs-4">{{ collection_summary.efficiency_score|floatformat:0 }}/100</p>
                    </div>
                </div>
            </div>
            
            <!-- Collection Status Distribution -->
            <div class="mt-3">
                <h6>Payment Status Distribution</h6>
                <div class="progress" style="height: 25px;">
                    {% with fully_paid_pct=collection_summary.fully_paid_count|percentage:collection_summary.total_invoices partially_paid_pct=collection_summary.partially_paid_count|percentage:collection_summary.total_invoices unpaid_pct=collection_summary.unpaid_count|percentage:collection_summary.total_invoices %}
                    <div class="progress-bar bg-success" role="progressbar" style="width: {{ fully_paid_pct }}%">
                        Fully Paid: {{ fully_paid_pct|floatformat:0 }}%
                    </div>
                    <div class="progress-bar bg-warning" role="progressbar" style="width: {{ partially_paid_pct }}%">
                        Partial: {{ partially_paid_pct|floatformat:0 }}%
                    </div>
                    <div class="progress-bar bg-danger" role="progressbar" style="width: {{ unpaid_pct }}%">
                        Unpaid: {{ unpaid_pct|floatformat:0 }}%
                    </div>
                    {% endwith %}
                </div>
                <small class="text-muted">
                    Fully Paid: {{ collection_summary.fully_paid_count }} | 
                    Partially Paid: {{ collection_summary.partially_paid_count }} | 
                    Unpaid: {{ collection_summary.unpaid_count }} | 
                    Overdue: {{ collection_summary.overdue_count }}
                </small>
            </div>
        </div>
    </div>

    <!-- Financial Metrics -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="bi bi-cash-stack me-2"></i>Financial Metrics</h5>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled">
                        <li><i class="bi bi-file-earmark-text text-primary me-2"></i>
                            <strong>Total Invoices:</strong> {{ collection_summary.total_invoices }}
                        </li>
                        <li><i class="bi bi-currency-dollar text-info me-2"></i>
                            <strong>Total Invoice Amount:</strong> {{ collection_summary.total_invoice_amount|currency }}
                        </li>
                        <li><i class="bi bi-arrow-down-circle text-success me-2"></i>
                            <strong>Collected Amount:</strong> {{ collection_summary.collected_amount|currency }}
                        </li>
                        <li><i class="bi bi-exclamation-circle text-warning me-2"></i>
                            <strong>Outstanding Amount:</strong> {{ collection_summary.outstanding_amount|currency }}
                        </li>
                        <li><i class="bi bi-clock text-danger me-2"></i>
                            <strong>Overdue Amount:</strong> {{ collection_summary.overdue_amount|currency }}
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="bi bi-graph-up me-2"></i>Performance Indicators</h5>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled">
                        <li><i class="bi bi-percent text-primary me-2"></i>
                            <strong>Collection Rate:</strong> {{ collection_summary.collection_rate|floatformat:1 }}%
                        </li>
                        <li><i class="bi bi-exclamation-triangle text-warning me-2"></i>
                            <strong>Overdue Rate:</strong> {{ collection_summary.overdue_rate|floatformat:1 }}%
                        </li>
                        <li><i class="bi bi-check-circle text-success me-2"></i>
                            <strong>Full Payment Rate:</strong> {{ collection_summary.full_payment_rate|floatformat:1 }}%
                        </li>
                        <li><i class="bi bi-calendar text-info me-2"></i>
                            <strong>Avg Collection Time:</strong> {{ collection_summary.avg_collection_days|floatformat:1 }} days
                        </li>
                        <li><i class="bi bi-award text-warning me-2"></i>
                            <strong>Efficiency Score:</strong> {{ collection_summary.efficiency_score|floatformat:0 }}/100 (Grade {{ collection_summary.efficiency_grade }})
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Aging Analysis -->
    {% if aging_analysis %}
    <div class="card mb-4">
        <div class="card-header bg-light d-flex justify-content-between align-items-center">
            <h5 class="mb-0"><i class="bi bi-hourglass me-2"></i>Aging Analysis</h5>
            <span class="badge bg-primary">{{ aging_analysis|length }} aging periods</span>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-striped table-hover mb-0">
                    <thead class="table-dark">
                        <tr>
                            <th>Aging Period</th>
                            <th class="text-center">Count</th>
                            <th class="text-end">Amount</th>
                            <th class="text-center">Percentage</th>
                            <th class="text-end">Average Amount</th>
                            <th class="text-center">Urgency Level</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for aging in aging_analysis %}
                        <tr class="{% if aging.urgency_level == 'CRITICAL' %}table-danger{% elif aging.urgency_level == 'HIGH' %}table-warning{% elif aging.urgency_level == 'MEDIUM' %}table-info{% endif %}">
                            <td>
                                <strong>{{ aging.period_display }}</strong>
                                <br><small class="text-muted">{{ aging.period }} days overdue</small>
                            </td>
                            <td class="text-center">
                                <span class="fw-bold">{{ aging.count }}</span>
                            </td>
                            <td class="text-end">
                                <span class="fw-bold {% if aging.urgency_level == 'CRITICAL' %}text-danger{% elif aging.urgency_level == 'HIGH' %}text-warning{% else %}text-primary{% endif %}">
                                    {{ aging.amount|currency }}
                                </span>
                            </td>
                            <td class="text-center">
                                <div class="progress" style="height: 20px; width: 80px;">
                                    <div class="progress-bar {% if aging.urgency_level == 'CRITICAL' %}bg-danger{% elif aging.urgency_level == 'HIGH' %}bg-warning{% elif aging.urgency_level == 'MEDIUM' %}bg-info{% else %}bg-success{% endif %}" 
                                         role="progressbar" style="width: {{ aging.percentage }}%">
                                    </div>
                                </div>
                                <small class="text-muted">{{ aging.percentage|floatformat:1 }}%</small>
                            </td>
                            <td class="text-end">
                                <span class="text-primary">{{ aging.avg_amount|currency }}</span>
                            </td>
                            <td class="text-center">
                                <span class="badge {% if aging.urgency_level == 'CRITICAL' %}bg-danger{% elif aging.urgency_level == 'HIGH' %}bg-warning{% elif aging.urgency_level == 'MEDIUM' %}bg-info{% else %}bg-success{% endif %}">
                                    {{ aging.urgency_level }}
                                </span>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                    <tfoot class="table-light">
                        <tr>
                            <th>Totals:</th>
                            <th class="text-center">{{ collection_summary.overdue_count }}</th>
                            <th class="text-end">{{ collection_summary.overdue_amount|currency }}</th>
                            <th class="text-center">100.0%</th>
                            <th class="text-end">
                                {% if collection_summary.overdue_count > 0 %}
                                {{ collection_summary.overdue_amount|div:collection_summary.overdue_count|currency }}
                                {% else %}
                                {{ 0|currency }}
                                {% endif %}
                            </th>
                            <th></th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Performance Recommendations -->
    {% if performance_metrics.recommendations %}
    <div class="card mb-4">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="bi bi-lightbulb me-2"></i>Performance Recommendations</h5>
        </div>
        <div class="card-body">
            {% for recommendation in performance_metrics.recommendations %}
            <div class="mb-3">
                <div class="d-flex justify-content-between align-items-start">
                    <div class="flex-grow-1">
                        <h6 class="mb-1">
                            <span class="badge {% if recommendation.priority == 'CRITICAL' %}bg-danger{% elif recommendation.priority == 'HIGH' %}bg-warning{% else %}bg-info{% endif %} me-2">
                                {{ recommendation.priority }}
                            </span>
                            {{ recommendation.category }}
                        </h6>
                        <p class="mb-1">{{ recommendation.recommendation }}</p>
                        <small class="text-success">
                            <i class="bi bi-arrow-up me-1"></i>Expected Impact: {{ recommendation.impact }}
                        </small>
                    </div>
                </div>
            </div>
            {% if not forloop.last %}<hr>{% endif %}
            {% endfor %}
        </div>
    </div>
    {% endif %}

    <!-- Performance Trends -->
    {% if performance_metrics.performance_trends %}
    <div class="card mb-4">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="bi bi-graph-up me-2"></i>Performance Trends</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Period</th>
                            <th class="text-center">Collection Rate (%)</th>
                            <th class="text-center">Overdue Rate (%)</th>
                            <th class="text-center">Efficiency Score</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for trend in performance_metrics.performance_trends %}
                        <tr>
                            <td><strong>{{ trend.period }}</strong></td>
                            <td class="text-center">
                                <span class="{% if trend.collection_rate >= 85 %}text-success{% elif trend.collection_rate >= 70 %}text-warning{% else %}text-danger{% endif %}">
                                    {{ trend.collection_rate|floatformat:1 }}%
                                </span>
                            </td>
                            <td class="text-center">
                                <span class="{% if trend.overdue_rate <= 15 %}text-success{% elif trend.overdue_rate <= 25 %}text-warning{% else %}text-danger{% endif %}">
                                    {{ trend.overdue_rate|floatformat:1 }}%
                                </span>
                            </td>
                            <td class="text-center">
                                <span class="{% if trend.efficiency_score >= 80 %}text-success{% elif trend.efficiency_score >= 60 %}text-warning{% else %}text-danger{% endif %}">
                                    {{ trend.efficiency_score|floatformat:0 }}
                                </span>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Collection Insights -->
    <div class="card">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="bi bi-info-circle me-2"></i>Collection Insights</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h6>Performance Assessment</h6>
                    <ul class="list-unstyled">
                        {% if collection_summary.efficiency_score >= 80 %}
                        <li><i class="bi bi-check-circle text-success me-2"></i>
                            Excellent collection performance (Grade {{ collection_summary.efficiency_grade }})
                        </li>
                        {% elif collection_summary.efficiency_score >= 60 %}
                        <li><i class="bi bi-info-circle text-info me-2"></i>
                            Good collection performance with improvement opportunities
                        </li>
                        {% else %}
                        <li><i class="bi bi-exclamation-triangle text-warning me-2"></i>
                            Collection performance needs significant improvement
                        </li>
                        {% endif %}
                        
                        {% if collection_summary.collection_rate >= 85 %}
                        <li><i class="bi bi-check-circle text-success me-2"></i>
                            Strong collection rate ({{ collection_summary.collection_rate|floatformat:1 }}%)
                        </li>
                        {% else %}
                        <li><i class="bi bi-exclamation-triangle text-warning me-2"></i>
                            Collection rate below target
                        </li>
                        {% endif %}
                        
                        {% if collection_summary.overdue_rate <= 15 %}
                        <li><i class="bi bi-check-circle text-success me-2"></i>
                            Low overdue rate ({{ collection_summary.overdue_rate|floatformat:1 }}%)
                        </li>
                        {% else %}
                        <li><i class="bi bi-exclamation-triangle text-warning me-2"></i>
                            High overdue rate requires attention
                        </li>
                        {% endif %}
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6>Action Items</h6>
                    <ul class="list-unstyled">
                        {% if collection_summary.overdue_count > 0 %}
                        <li><i class="bi bi-arrow-right text-primary me-2"></i>
                            Address {{ collection_summary.overdue_count }} overdue accounts
                        </li>
                        {% endif %}
                        
                        {% if collection_summary.collection_rate < 80 %}
                        <li><i class="bi bi-arrow-right text-warning me-2"></i>
                            Implement collection improvement strategies
                        </li>
                        {% endif %}
                        
                        {% if collection_summary.avg_collection_days > 30 %}
                        <li><i class="bi bi-arrow-right text-info me-2"></i>
                            Optimize collection processes for faster payments
                        </li>
                        {% endif %}
                        
                        <li><i class="bi bi-arrow-right text-success me-2"></i>
                            Monitor collection trends for early intervention
                        </li>
                        
                        <li><i class="bi bi-arrow-right text-primary me-2"></i>
                            Review and update collection policies as needed
                        </li>
                    </ul>
                </div>
            </div>
            
            <hr>
            
            <div class="alert {% if collection_summary.efficiency_score >= 80 %}alert-success{% elif collection_summary.efficiency_score >= 60 %}alert-info{% else %}alert-warning{% endif %}">
                <h6 class="alert-heading">
                    <i class="bi {% if collection_summary.efficiency_score >= 80 %}bi-check-circle{% elif collection_summary.efficiency_score >= 60 %}bi-info-circle{% else %}bi-exclamation-triangle{% endif %} me-2"></i>
                    Collection Performance Summary
                </h6>
                <p class="mb-0">
                    {% if collection_summary.efficiency_score >= 80 %}
                    Your collection performance is excellent. Continue current practices and monitor for consistency.
                    {% elif collection_summary.efficiency_score >= 60 %}
                    Your collection performance is good but has room for improvement. Focus on reducing overdue rates and improving collection efficiency.
                    {% else %}
                    Your collection performance needs immediate attention. Implement comprehensive collection improvement strategies.
                    {% endif %}
                    Current efficiency score: {{ collection_summary.efficiency_score|floatformat:0 }}/100 (Grade {{ collection_summary.efficiency_grade }}).
                </p>
            </div>
        </div>
    </div>

    <!-- No Data Message -->
    {% if not collection_summary %}
    <div class="card">
        <div class="card-body text-center py-5">
            <i class="bi bi-collection display-1 text-muted mb-3"></i>
            <h4 class="text-muted">No Collection Data Available</h4>
            <p class="text-muted">No collection performance data found for the selected criteria.</p>
            <div class="mt-3">
                <a href="{% url 'reporting:collection_performance_report' %}" class="btn btn-outline-primary me-2">
                    <i class="bi bi-arrow-clockwise me-2"></i>Reset Filters
                </a>
            </div>
        </div>
    </div>
    {% endif %}

</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Animate progress bars
    $('.progress-bar').each(function() {
        var $this = $(this);
        var width = $this.css('width');
        $this.css('width', '0%').animate({width: width}, 1500);
    });
    
    // Highlight critical aging periods
    $('.table-danger, .table-warning').hover(
        function() {
            $(this).addClass('table-active');
        },
        function() {
            $(this).removeClass('table-active');
        }
    );
    
    // Add click handlers for aging analysis rows
    $('tbody tr').on('click', function() {
        $(this).toggleClass('table-info');
    });
});
</script>
{% endblock %}

