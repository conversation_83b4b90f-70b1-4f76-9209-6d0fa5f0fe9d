#!/usr/bin/env python
"""
Fix AdminActivityLog Table
Creates the correct portal_admin_adminactivitylog table structure

Usage: python fix_adminactivitylog_table.py
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from django.db import connection
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def fix_adminactivitylog_table():
    """Fix the portal_admin_adminactivitylog table structure"""
    schema_name = 'mandiva'
    
    logger.info(f"=== FIXING ADMINACTIVITYLOG TABLE IN {schema_name} ===")
    
    try:
        with connection.cursor() as cursor:
            # Set search path to mandiva schema
            cursor.execute(f'SET search_path TO "{schema_name}"')
            
            # Drop the existing table if it exists
            logger.info("Dropping existing portal_admin_adminactivitylog table...")
            cursor.execute("DROP TABLE IF EXISTS portal_admin_adminactivitylog CASCADE")
            
            # Create the table with correct structure matching the Django model
            logger.info("Creating portal_admin_adminactivitylog table with correct structure...")
            cursor.execute("""
                CREATE TABLE portal_admin_adminactivitylog (
                    id BIGSERIAL PRIMARY KEY,
                    action_type VARCHAR(50) NOT NULL,
                    timestamp TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
                    user_id BIGINT,
                    staff_user_id BIGINT,
                    actor_description VARCHAR(255),
                    tenant_id BIGINT,
                    ip_address INET,
                    user_agent TEXT,
                    target_content_type_id INTEGER,
                    target_object_id VARCHAR(255),
                    target_object_repr VARCHAR(300),
                    description TEXT
                );
            """)
            
            # Create indexes for performance (matching Django model)
            cursor.execute("""
                CREATE INDEX portal_admin_adminactivitylog_action_type_idx ON portal_admin_adminactivitylog (action_type);
            """)
            
            cursor.execute("""
                CREATE INDEX portal_admin_adminactivitylog_timestamp_idx ON portal_admin_adminactivitylog (timestamp);
            """)
            
            cursor.execute("""
                CREATE INDEX portal_admin_adminactivitylog_tenant_id_idx ON portal_admin_adminactivitylog (tenant_id);
            """)
            
            cursor.execute("""
                CREATE INDEX portal_admin_adminactivitylog_target_object_id_idx ON portal_admin_adminactivitylog (target_object_id);
            """)
            
            logger.info("✅ Created portal_admin_adminactivitylog table with correct structure")
            
            # Insert some sample data
            cursor.execute("""
                INSERT INTO portal_admin_adminactivitylog 
                (action_type, description, staff_user_id, tenant_id)
                VALUES 
                ('SYSTEM_SETUP', 'Initial system setup completed', 1, 1),
                ('LOGIN_SUCCESS', 'Staff user logged in successfully', 1, 1),
                ('SCHOOL_PROFILE_UPDATE', 'School profile information updated', 1, 1)
            """)
            
            logger.info("✅ Added sample activity log data")
            
            # Verify the table structure
            cursor.execute("""
                SELECT column_name, data_type, is_nullable, column_default
                FROM information_schema.columns 
                WHERE table_name = 'portal_admin_adminactivitylog'
                ORDER BY ordinal_position
            """)
            
            columns = cursor.fetchall()
            logger.info("Table structure:")
            for col in columns:
                logger.info(f"  {col[0]} ({col[1]}) - nullable: {col[2]}")
            
            # Test the query that was failing
            cursor.execute("""
                SELECT id, action_type, timestamp, description 
                FROM portal_admin_adminactivitylog
                ORDER BY timestamp DESC
                LIMIT 5
            """)
            
            records = cursor.fetchall()
            logger.info(f"✅ Successfully queried portal_admin_adminactivitylog (found {len(records)} records)")
            
            for record in records:
                logger.info(f"  ID: {record[0]}, Action: {record[1]}, Description: {record[3]}")
            
            logger.info(f"🎉 ADMINACTIVITYLOG TABLE FIXED!")
            
    except Exception as e:
        logger.error(f"Failed to fix adminactivitylog table: {e}")
        raise

def test_dashboard_query():
    """Test the specific query that the dashboard is making"""
    schema_name = 'mandiva'
    
    logger.info("=== TESTING DASHBOARD ADMINACTIVITYLOG QUERY ===")
    
    try:
        with connection.cursor() as cursor:
            cursor.execute(f'SET search_path TO "{schema_name}"')
            
            # This is the query from the dashboard view
            cursor.execute("""
                SELECT "portal_admin_adminactivitylog"."id", 
                       "portal_admin_adminactivitylog"."action_type", 
                       "portal_admin_adminactivitylog"."timestamp", 
                       "portal_admin_adminactivitylog"."description"
                FROM "portal_admin_adminactivitylog" 
                ORDER BY "portal_admin_adminactivitylog"."timestamp" DESC 
                LIMIT 5
            """)
            
            records = cursor.fetchall()
            logger.info(f"✅ Dashboard AdminActivityLog query successful (found {len(records)} records)")
            
    except Exception as e:
        logger.error(f"Dashboard AdminActivityLog query test failed: {e}")
        raise

def main():
    """Main function"""
    logger.info("=== FIXING ADMINACTIVITYLOG TABLE ===")
    
    try:
        # Fix the table
        fix_adminactivitylog_table()
        
        # Test the queries
        test_dashboard_query()
        
        logger.info("\n🎉 ADMINACTIVITYLOG TABLE COMPLETELY FIXED!")
        logger.info("The dashboard should now work without any AdminActivityLog errors.")
        logger.info("Try accessing: http://mandiva.myapp.test:8000/portal/dashboard/")
        
        return True
        
    except Exception as e:
        logger.error(f"Fix failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
