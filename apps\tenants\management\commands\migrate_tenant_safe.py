"""
Custom migration command that handles problematic migrations for tenant schemas.
This command is designed to work around known migration conflicts.
"""
import psycopg
from django.core.management.base import BaseCommand
from django.core.management import call_command
from django.conf import settings
from django.db import connection
from django_tenants.utils import schema_context


class Command(BaseCommand):
    help = 'Safely migrate a tenant schema, handling known migration conflicts'

    def add_arguments(self, parser):
        parser.add_argument('schema_name', type=str, help='The schema name to migrate')

    def handle(self, *args, **options):
        schema_name = options['schema_name']
        verbosity = options.get('verbosity', 1)
        
        self.stdout.write(f"🔧 Starting safe migration for schema: {schema_name}")
        
        try:
            # First, fix any known migration conflicts
            self._fix_migration_conflicts(schema_name, verbosity)
            
            # Then run the actual migration
            self._run_migration(schema_name, verbosity)
            
            self.stdout.write(
                self.style.SUCCESS(f"✅ Successfully migrated schema: {schema_name}")
            )
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"❌ Failed to migrate schema {schema_name}: {e}")
            )
            raise

    def _fix_migration_conflicts(self, schema_name, verbosity):
        """Fix known migration conflicts before running migrations"""
        if verbosity >= 1:
            self.stdout.write(f"🔧 Fixing migration conflicts for {schema_name}...")
        
        db_settings = settings.DATABASES['default']
        conn = psycopg.connect(
            host=db_settings['HOST'],
            port=db_settings['PORT'],
            dbname=db_settings['NAME'],
            user=db_settings['USER'],
            password=db_settings['PASSWORD']
        )
        conn.autocommit = True
        cur = conn.cursor()
        
        try:
            # Create the schema first if it doesn't exist
            cur.execute(f'CREATE SCHEMA IF NOT EXISTS "{schema_name}";')

            # Set search path to the tenant schema
            cur.execute(f'SET search_path TO "{schema_name}";')

            # Create django_migrations table if it doesn't exist
            cur.execute("""
                CREATE TABLE IF NOT EXISTS django_migrations (
                    id SERIAL PRIMARY KEY,
                    app VARCHAR(255) NOT NULL,
                    name VARCHAR(255) NOT NULL,
                    applied TIMESTAMP WITH TIME ZONE NOT NULL,
                    UNIQUE(app, name)
                )
            """)
            
            # Fix schools.0003_remove_school_foreign_key migration
            self._fix_schools_migration(cur, schema_name, verbosity)
            
            # Fix fees.0004_feepermissions_feestructure_total_amount migration
            self._fix_fees_migration(cur, schema_name, verbosity)
            
        finally:
            conn.close()

    def _fix_schools_migration(self, cur, schema_name, verbosity):
        """Fix the schools.0003_remove_school_foreign_key migration"""
        # Check if migration is already applied
        cur.execute("""
            SELECT name FROM django_migrations 
            WHERE app = 'schools' AND name = '0003_remove_school_foreign_key'
        """)
        
        if cur.fetchone():
            if verbosity >= 2:
                self.stdout.write(f"  ✅ schools.0003_remove_school_foreign_key already applied")
            return
        
        # Check if schools_schoolprofile table exists
        cur.execute("""
            SELECT table_name FROM information_schema.tables 
            WHERE table_schema = %s AND table_name = 'schools_schoolprofile'
        """, (schema_name,))
        
        if cur.fetchone():
            # Table exists, check for multiple primary keys
            cur.execute("""
                SELECT constraint_name 
                FROM information_schema.table_constraints 
                WHERE table_schema = %s AND table_name = 'schools_schoolprofile' 
                AND constraint_type = 'PRIMARY KEY'
            """, (schema_name,))
            
            pk_constraints = cur.fetchall()
            
            if len(pk_constraints) > 1:
                if verbosity >= 1:
                    self.stdout.write(f"  🔧 Fixing multiple primary keys in schools_schoolprofile")
                
                # Drop extra primary key constraints
                for constraint_name, in pk_constraints[1:]:
                    try:
                        cur.execute(f'ALTER TABLE schools_schoolprofile DROP CONSTRAINT "{constraint_name}"')
                        if verbosity >= 2:
                            self.stdout.write(f"    ✅ Dropped constraint: {constraint_name}")
                    except Exception as e:
                        if verbosity >= 1:
                            self.stdout.write(f"    ⚠️  Could not drop constraint {constraint_name}: {e}")
        
        # Mark migration as applied
        cur.execute("""
            INSERT INTO django_migrations (app, name, applied)
            VALUES ('schools', '0003_remove_school_foreign_key', NOW())
            ON CONFLICT (app, name) DO NOTHING
        """)
        
        if verbosity >= 2:
            self.stdout.write(f"  ✅ Marked schools.0003_remove_school_foreign_key as applied")

    def _fix_fees_migration(self, cur, schema_name, verbosity):
        """Fix the fees.0004_feepermissions_feestructure_total_amount migration"""
        # Check if migration is already applied
        cur.execute("""
            SELECT name FROM django_migrations 
            WHERE app = 'fees' AND name = '0004_feepermissions_feestructure_total_amount'
        """)
        
        if cur.fetchone():
            if verbosity >= 2:
                self.stdout.write(f"  ✅ fees.0004_feepermissions_feestructure_total_amount already applied")
            return
        
        # Check if fees_feestructure table exists and has total_amount column
        cur.execute("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_schema = %s AND table_name = 'fees_feestructure' 
            AND column_name = 'total_amount'
        """, (schema_name,))
        
        if cur.fetchone():
            # Column exists, mark migration as applied
            cur.execute("""
                INSERT INTO django_migrations (app, name, applied)
                VALUES ('fees', '0004_feepermissions_feestructure_total_amount', NOW())
                ON CONFLICT (app, name) DO NOTHING
            """)
            
            if verbosity >= 2:
                self.stdout.write(f"  ✅ Marked fees.0004_feepermissions_feestructure_total_amount as applied")

    def _run_migration(self, schema_name, verbosity):
        """Run the actual migration for the schema"""
        if verbosity >= 1:
            self.stdout.write(f"🚀 Running migrations for {schema_name}...")

        # Use schema context to run migrations in the specific schema
        with schema_context(schema_name):
            call_command(
                'migrate',
                verbosity=verbosity,
                interactive=False
            )
