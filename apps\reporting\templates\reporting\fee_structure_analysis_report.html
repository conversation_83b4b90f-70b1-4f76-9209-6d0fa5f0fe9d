{% extends "tenant_base.html" %}
{% load humanize static core_tags reporting_filters %}

{% block title %}{{ view_title|default:"Fee Structure Analysis Report" }}{% endblock %}

{% block content %}
<div class="container-fluid mt-3">
    {% with report_icon_class="bi bi-diagram-3" %}
        {% include "partials/_report_header.html" %}
    {% endwith %}

    {% include "partials/_messages.html" %}
    {% include "partials/_report_filter_form.html" with filter_form=filter_form report_code=report_code specific_filter_title="Fee Structure Analysis Filters" %}

    <!-- Fee Structure Overview -->
    <div class="card mb-4">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="bi bi-pie-chart me-2"></i>Fee Structure Overview</h5>
        </div>
        <div class="card-body">
            <div class="row mb-3">
                <div class="col-md-12">
                    <p>
                        <strong>Academic Year:</strong> {{ academic_year }} | 
                        <strong>Analysis Focus:</strong> {{ analysis_focus|title }} | 
                        <strong>Overall Effectiveness:</strong> 
                        <span class="badge {% if optimization_insights.overall_effectiveness >= 70 %}bg-success{% elif optimization_insights.overall_effectiveness >= 50 %}bg-warning{% else %}bg-danger{% endif %} fs-6">
                            {{ optimization_insights.overall_effectiveness|floatformat:0 }}/100
                        </span>
                    </p>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-3">
                    <div class="text-center">
                        <h6 class="text-muted">Total Fee Types</h6>
                        <p class="mb-0 fw-bold text-primary fs-4">{{ optimization_insights.total_fee_types }}</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h6 class="text-muted">Total Revenue</h6>
                        <p class="mb-0 fw-bold text-success fs-4">{{ optimization_insights.total_revenue|currency }}</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h6 class="text-muted">Revenue Concentration</h6>
                        <p class="mb-0 fw-bold text-warning fs-4">{{ optimization_insights.revenue_concentration|floatformat:1 }}%</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h6 class="text-muted">Avg Revenue/Invoice</h6>
                        <p class="mb-0 fw-bold text-info fs-4">{{ optimization_insights.avg_revenue_per_invoice|currency }}</p>
                    </div>
                </div>
            </div>
            
            <!-- Revenue Concentration Indicator -->
            <div class="mt-3">
                <h6>Revenue Concentration Analysis</h6>
                <div class="progress" style="height: 25px;">
                    <div class="progress-bar {% if optimization_insights.revenue_concentration > 70 %}bg-warning{% elif optimization_insights.revenue_concentration > 50 %}bg-info{% else %}bg-success{% endif %}" 
                         role="progressbar" style="width: {{ optimization_insights.revenue_concentration }}%">
                        Top 3 Fees: {{ optimization_insights.revenue_concentration|floatformat:1 }}%
                    </div>
                </div>
                <small class="text-muted">
                    {% if optimization_insights.revenue_concentration > 70 %}
                    High concentration - consider diversifying revenue streams
                    {% elif optimization_insights.revenue_concentration > 50 %}
                    Moderate concentration - monitor for balance
                    {% else %}
                    Well-diversified revenue structure
                    {% endif %}
                </small>
            </div>
        </div>
    </div>

    <!-- Category Performance -->
    {% if optimization_insights.category_performance %}
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="bi bi-tags me-2"></i>Category Performance</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for category, performance in optimization_insights.category_performance.items %}
                        <div class="col-md-4 mb-3">
                            <div class="card border-0 bg-light">
                                <div class="card-body text-center">
                                    <h6 class="card-title">{{ category }}</h6>
                                    <p class="mb-1"><strong>Revenue:</strong> {{ performance.revenue|currency }}</p>
                                    <p class="mb-1"><strong>Fee Types:</strong> {{ performance.count }}</p>
                                    <p class="mb-0">
                                        <strong>Avg Collection:</strong> 
                                        <span class="{% if performance.avg_collection_rate >= 80 %}text-success{% elif performance.avg_collection_rate >= 60 %}text-warning{% else %}text-danger{% endif %}">
                                            {{ performance.avg_collection_rate|floatformat:1 }}%
                                        </span>
                                    </p>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Fee Structure Analysis -->
    {% if fee_analysis %}
    <div class="card mb-4">
        <div class="card-header bg-light d-flex justify-content-between align-items-center">
            <h5 class="mb-0"><i class="bi bi-table me-2"></i>Fee Structure Analysis</h5>
            <span class="badge bg-primary">{{ fee_analysis|length }} fee types</span>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-striped table-hover mb-0">
                    <thead class="table-dark">
                        <tr>
                            <th>Fee Type</th>
                            <th class="text-center">Category</th>
                            <th class="text-center">Volume</th>
                            <th class="text-end">Total Revenue</th>
                            <th class="text-center">Collection Rate</th>
                            <th class="text-end">Avg Amount</th>
                            <th class="text-center">Market Share</th>
                            <th class="text-center">Effectiveness</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for fee in fee_analysis %}
                        <tr class="{% if fee.effectiveness_score < 50 %}table-warning{% elif fee.effectiveness_score < 30 %}table-danger{% endif %}">
                            <td>
                                <strong>{{ fee.fee_type }}</strong>
                                {% if fee.outstanding_amount > 0 %}
                                <br><small class="text-warning">Outstanding: {{ fee.outstanding_amount|currency }}</small>
                                {% endif %}
                            </td>
                            <td class="text-center">
                                <span class="badge {% if fee.category == 'TUITION' %}bg-primary{% elif fee.category == 'LIBRARY' %}bg-info{% elif fee.category == 'LABORATORY' %}bg-success{% elif fee.category == 'SPORTS' %}bg-warning{% elif fee.category == 'TRANSPORT' %}bg-secondary{% elif fee.category == 'EXAMINATION' %}bg-danger{% else %}bg-dark{% endif %}">
                                    {{ fee.category }}
                                </span>
                            </td>
                            <td class="text-center">
                                <span class="fw-bold">{{ fee.invoice_count }}</span>
                                <br><small class="text-muted">invoices</small>
                            </td>
                            <td class="text-end">
                                <span class="fw-bold text-success">{{ fee.total_revenue|currency }}</span>
                                <br><small class="text-muted">Collected: {{ fee.payments_received|currency }}</small>
                            </td>
                            <td class="text-center">
                                <div class="progress" style="height: 20px; width: 80px;">
                                    <div class="progress-bar {% if fee.collection_rate >= 80 %}bg-success{% elif fee.collection_rate >= 60 %}bg-warning{% else %}bg-danger{% endif %}" 
                                         role="progressbar" style="width: {{ fee.collection_rate }}%">
                                    </div>
                                </div>
                                <small class="text-muted">{{ fee.collection_rate|floatformat:1 }}%</small>
                            </td>
                            <td class="text-end">
                                <span class="text-primary fw-bold">{{ fee.avg_fee_amount|currency }}</span>
                            </td>
                            <td class="text-center">
                                <div class="progress" style="height: 20px; width: 80px;">
                                    <div class="progress-bar bg-info" role="progressbar" style="width: {{ fee.market_share }}%">
                                    </div>
                                </div>
                                <small class="text-muted">{{ fee.market_share|floatformat:1 }}%</small>
                            </td>
                            <td class="text-center">
                                <span class="badge {% if fee.effectiveness_grade == 'A' %}bg-success{% elif fee.effectiveness_grade == 'B' %}bg-info{% elif fee.effectiveness_grade == 'C' %}bg-warning{% else %}bg-danger{% endif %} fs-6">
                                    {{ fee.effectiveness_score|floatformat:0 }}
                                </span>
                                <br><small class="text-muted">Grade {{ fee.effectiveness_grade }}</small>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                    <tfoot class="table-light">
                        <tr>
                            <th>Totals:</th>
                            <th></th>
                            <th class="text-center">{{ optimization_insights.total_invoices }}</th>
                            <th class="text-end">{{ optimization_insights.total_revenue|currency }}</th>
                            <th></th>
                            <th class="text-end">{{ optimization_insights.avg_revenue_per_invoice|currency }}</th>
                            <th class="text-center">100.0%</th>
                            <th class="text-center">{{ optimization_insights.overall_effectiveness|floatformat:0 }}</th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Optimization Opportunities -->
    {% if optimization_insights.optimization_opportunities %}
    <div class="card mb-4">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="bi bi-lightbulb me-2"></i>Optimization Opportunities</h5>
        </div>
        <div class="card-body">
            {% for opportunity in optimization_insights.optimization_opportunities %}
            <div class="mb-3">
                <div class="d-flex justify-content-between align-items-start">
                    <div class="flex-grow-1">
                        <h6 class="mb-1">
                            <span class="badge {% if opportunity.priority == 'HIGH' %}bg-danger{% elif opportunity.priority == 'MEDIUM' %}bg-warning{% else %}bg-info{% endif %} me-2">
                                {{ opportunity.priority }}
                            </span>
                            {{ opportunity.category }}
                        </h6>
                        <p class="mb-1">{{ opportunity.opportunity }}</p>
                        <small class="text-success">
                            <i class="bi bi-arrow-up me-1"></i>Expected Impact: {{ opportunity.impact }}
                        </small>
                    </div>
                </div>
            </div>
            {% if not forloop.last %}<hr>{% endif %}
            {% endfor %}
        </div>
    </div>
    {% endif %}

    <!-- Pricing Trends -->
    {% if pricing_trends and analysis_focus == 'trends' %}
    <div class="card mb-4">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="bi bi-graph-up me-2"></i>Pricing Trends</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Period</th>
                            {% for fee_data in pricing_trends.0.fee_data %}
                            <th class="text-end">{{ fee_data.fee_type }}</th>
                            {% endfor %}
                        </tr>
                    </thead>
                    <tbody>
                        {% for trend in pricing_trends %}
                        <tr>
                            <td><strong>{{ trend.period }}</strong></td>
                            {% for fee_data in trend.fee_data %}
                            <td class="text-end">
                                {{ fee_data.avg_amount|currency }}
                                <br><small class="text-muted">{{ fee_data.collection_rate|floatformat:1 }}%</small>
                            </td>
                            {% endfor %}
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Fee Recommendations -->
    {% if fee_analysis %}
    <div class="card">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="bi bi-gear me-2"></i>Fee-Specific Recommendations</h5>
        </div>
        <div class="card-body">
            {% for fee in fee_analysis %}
                {% if fee.recommendations %}
                <div class="mb-4">
                    <h6 class="text-primary">{{ fee.fee_type }}</h6>
                    {% for recommendation in fee.recommendations %}
                    <div class="mb-2">
                        <span class="badge {% if recommendation.priority == 'HIGH' %}bg-danger{% elif recommendation.priority == 'MEDIUM' %}bg-warning{% else %}bg-info{% endif %} me-2">
                            {{ recommendation.priority }}
                        </span>
                        <span class="badge bg-secondary me-2">{{ recommendation.type }}</span>
                        <span>{{ recommendation.recommendation }}</span>
                        <br><small class="text-muted ms-5">Issue: {{ recommendation.current_issue }}</small>
                    </div>
                    {% endfor %}
                </div>
                {% endif %}
            {% endfor %}
        </div>
    </div>
    {% endif %}

    <!-- Strategic Insights -->
    <div class="card mt-4">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="bi bi-info-circle me-2"></i>Strategic Insights</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h6>Fee Structure Assessment</h6>
                    <ul class="list-unstyled">
                        {% if optimization_insights.overall_effectiveness >= 70 %}
                        <li><i class="bi bi-check-circle text-success me-2"></i>
                            Strong overall fee structure effectiveness
                        </li>
                        {% elif optimization_insights.overall_effectiveness >= 50 %}
                        <li><i class="bi bi-info-circle text-info me-2"></i>
                            Moderate effectiveness with improvement opportunities
                        </li>
                        {% else %}
                        <li><i class="bi bi-exclamation-triangle text-warning me-2"></i>
                            Fee structure needs significant optimization
                        </li>
                        {% endif %}
                        
                        {% if optimization_insights.revenue_concentration > 70 %}
                        <li><i class="bi bi-exclamation-triangle text-warning me-2"></i>
                            High revenue concentration in top fees
                        </li>
                        {% else %}
                        <li><i class="bi bi-check-circle text-success me-2"></i>
                            Well-diversified revenue structure
                        </li>
                        {% endif %}
                        
                        <li><i class="bi bi-currency-dollar text-info me-2"></i>
                            Average revenue per invoice: {{ optimization_insights.avg_revenue_per_invoice|currency }}
                        </li>
                        
                        <li><i class="bi bi-diagram-3 text-primary me-2"></i>
                            {{ optimization_insights.total_fee_types }} different fee types in structure
                        </li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6>Strategic Recommendations</h6>
                    <ul class="list-unstyled">
                        {% if optimization_insights.revenue_concentration > 70 %}
                        <li><i class="bi bi-arrow-right text-primary me-2"></i>
                            Diversify revenue streams to reduce dependency risk
                        </li>
                        {% endif %}
                        
                        {% if optimization_insights.overall_effectiveness < 60 %}
                        <li><i class="bi bi-arrow-right text-warning me-2"></i>
                            Review and optimize underperforming fee structures
                        </li>
                        {% endif %}
                        
                        <li><i class="bi bi-arrow-right text-success me-2"></i>
                            Implement data-driven pricing strategies
                        </li>
                        
                        <li><i class="bi bi-arrow-right text-info me-2"></i>
                            Monitor collection rates for pricing optimization
                        </li>
                        
                        <li><i class="bi bi-arrow-right text-primary me-2"></i>
                            Consider installment options for high-value fees
                        </li>
                        
                        <li><i class="bi bi-arrow-right text-secondary me-2"></i>
                            Regular fee structure reviews and adjustments
                        </li>
                    </ul>
                </div>
            </div>
            
            <hr>
            
            <div class="alert {% if optimization_insights.overall_effectiveness >= 70 %}alert-success{% elif optimization_insights.overall_effectiveness >= 50 %}alert-info{% else %}alert-warning{% endif %}">
                <h6 class="alert-heading">
                    <i class="bi {% if optimization_insights.overall_effectiveness >= 70 %}bi-check-circle{% elif optimization_insights.overall_effectiveness >= 50 %}bi-info-circle{% else %}bi-exclamation-triangle{% endif %} me-2"></i>
                    Fee Structure Performance Summary
                </h6>
                <p class="mb-0">
                    {% if optimization_insights.overall_effectiveness >= 70 %}
                    Your fee structure is performing well with strong effectiveness across most fee types. Continue monitoring and fine-tuning for optimal performance.
                    {% elif optimization_insights.overall_effectiveness >= 50 %}
                    Your fee structure shows moderate performance with clear opportunities for improvement. Focus on optimizing underperforming fees and collection rates.
                    {% else %}
                    Your fee structure requires significant attention and optimization. Implement comprehensive pricing and collection strategy improvements.
                    {% endif %}
                    Overall effectiveness score: {{ optimization_insights.overall_effectiveness|floatformat:0 }}/100.
                </p>
            </div>
        </div>
    </div>

    <!-- No Data Message -->
    {% if not fee_analysis %}
    <div class="card">
        <div class="card-body text-center py-5">
            <i class="bi bi-diagram-3 display-1 text-muted mb-3"></i>
            <h4 class="text-muted">No Fee Structure Data Available</h4>
            <p class="text-muted">No fee structure data found for the selected criteria.</p>
            <div class="mt-3">
                <a href="{% url 'reporting:fee_structure_analysis_report' %}" class="btn btn-outline-primary me-2">
                    <i class="bi bi-arrow-clockwise me-2"></i>Reset Filters
                </a>
            </div>
        </div>
    </div>
    {% endif %}

</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Animate progress bars
    $('.progress-bar').each(function() {
        var $this = $(this);
        var width = $this.css('width');
        $this.css('width', '0%').animate({width: width}, 1500);
    });
    
    // Highlight low-performing fees
    $('.table-warning, .table-danger').hover(
        function() {
            $(this).addClass('table-active');
        },
        function() {
            $(this).removeClass('table-active');
        }
    );
    
    // Add click handlers for fee analysis rows
    $('tbody tr').on('click', function() {
        $(this).toggleClass('table-info');
    });
});
</script>
{% endblock %}

