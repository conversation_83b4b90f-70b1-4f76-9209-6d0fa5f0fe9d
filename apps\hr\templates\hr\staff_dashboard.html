{# D:\school_fees_saas_v2\apps\hr\templates\hr\staff_dashboard.html #}
{% extends "tenant_base.html" %}
{% load static i18n %}

{# This block provides the title for the <title> tag in the browser tab #}
{% block tenant_page_title %}{{ view_title|default:_("My HR Dashboard") }}{% endblock tenant_page_title %}

{# This is the main content block that MUST match the name in tenant_base.html #}
{% block tenant_specific_content %}
<div class="container mt-4">

    {# Page Header #}
    <div class="row mb-4">
        <div class="col">
            <h1 class="display-6">{{ view_title|default:_("My HR Dashboard") }}</h1>
            <p class="lead text-muted">
                {% blocktrans with full_name=request.user.get_full_name|default:request.user.email %}
                    Welcome, {{ full_name }}!
                {% endblocktrans %}
            </p>
        </div>
    </div>

    {# Display any messages passed from views #}
    {% include "partials/_messages.html" %}

    {# Dashboard Content Cards #}
    <div class="row">
        <div class="col-md-6 col-lg-4 mb-4">
            <div class="card h-100 shadow-sm">
                <div class="card-body text-center">
                    <i class="bi bi-calendar-plus-fill fs-1 text-primary mb-3"></i>
                    <h5 class="card-title">{% trans "Apply for Leave" %}</h5>
                    <p class="card-text">{% trans "Submit a new request for annual, sick, or other types of leave." %}</p>
                    <a href="{% url 'hr:staff_leaverequest_apply' %}" class="btn btn-primary mt-auto">{% trans "Go to Application Form" %}</a>
                </div>
            </div>
        </div>

        <div class="col-md-6 col-lg-4 mb-4">
            <div class="card h-100 shadow-sm">
                <div class="card-body text-center">
                    <i class="bi bi-card-list fs-1 text-success mb-3"></i>
                    <h5 class="card-title">{% trans "My Leave Requests" %}</h5>
                    <p class="card-text">{% trans "View the status of your past and pending leave applications." %}</p>
                    <a href="{% url 'hr:staff_leaverequest_list' %}" class="btn btn-success mt-auto">{% trans "View My Requests" %}</a>
                </div>
            </div>
        </div>

        <div class="col-md-6 col-lg-4 mb-4">
            <div class="card h-100 shadow-sm">
                <div class="card-body text-center">
                    <i class="bi bi-graph-up fs-1 text-info mb-3"></i>
                    <h5 class="card-title">{% trans "My Leave Summary" %}</h5>
                    <p class="card-text">{% trans "Check your available leave balances for the current year." %}</p>
                    <a href="{% url 'hr:my_leave_summary' %}" class="btn btn-info mt-auto">{% trans "View My Balances" %}</a>
                </div>
            </div>
        </div>
    </div>

</div>
{% endblock tenant_specific_content %}







{% comment %} {# D:\school_fees_saas_v2\apps\hr\templates\hr\staff_dashboard.html #}
{% extends "tenant_base.html" %}
{% load i18n %}

{% block tenant_page_title %}{{ view_title }}{% endblock %}

{% block tenant_specific_content %}
<div class="container mt-4">
    <h1>{{ view_title }}</h1>
    <p>Welcome, {{ request.user.get_full_name }}!</p>
    <p>This is your HR dashboard. From here you can apply for leave and view your history.</p>
    
    <a href="{% url 'hr:staff_leaverequest_apply' %}" class="btn btn-primary">Apply for Leave</a>
    <a href="{% url 'hr:staff_leaverequest_list' %}" class="btn btn-secondary">View My Leave Requests</a>
</div>
{% endblock %}
 {% endcomment %}

