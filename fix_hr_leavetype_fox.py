#!/usr/bin/env python
"""
Fix HR Leave Type in Fox
Check and fix the hr_leavetype table structure in fox

Usage: python fix_hr_leavetype_fox.py
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from django.db import connection
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def fix_hr_leavetype_fox():
    """Fix hr_leavetype table in fox schema"""
    
    logger.info("=== FIXING HR_LEAVETYPE IN FOX ===")
    
    try:
        with connection.cursor() as cursor:
            # Check current structure in fox
            cursor.execute('SET search_path TO "fox"')
            
            cursor.execute("""
                SELECT column_name, data_type, is_nullable, column_default
                FROM information_schema.columns 
                WHERE table_schema = 'fox' 
                AND table_name = 'hr_leavetype'
                ORDER BY ordinal_position
            """)
            
            fox_columns = cursor.fetchall()
            logger.info("Current hr_leavetype columns in fox:")
            for col in fox_columns:
                logger.info(f"  - {col[0]} ({col[1]}) {'NULL' if col[2] == 'YES' else 'NOT NULL'}")
            
            # Get structure from alpha for comparison
            cursor.execute('SET search_path TO "alpha"')
            
            cursor.execute("""
                SELECT column_name, data_type, is_nullable, column_default
                FROM information_schema.columns 
                WHERE table_schema = 'alpha' 
                AND table_name = 'hr_leavetype'
                ORDER BY ordinal_position
            """)
            
            alpha_columns = cursor.fetchall()
            logger.info("\nhr_leavetype columns in alpha:")
            for col in alpha_columns:
                logger.info(f"  - {col[0]} ({col[1]}) {'NULL' if col[2] == 'YES' else 'NOT NULL'}")
            
            # Find missing columns
            fox_col_names = {col[0] for col in fox_columns}
            alpha_col_names = {col[0] for col in alpha_columns}
            
            missing_columns = alpha_col_names - fox_col_names
            
            if missing_columns:
                logger.info(f"\nMissing columns in fox: {missing_columns}")
                
                # Add missing columns
                cursor.execute('SET search_path TO "fox"')
                
                for alpha_col in alpha_columns:
                    col_name, data_type, nullable, default = alpha_col
                    
                    if col_name in missing_columns:
                        # Build column definition
                        col_def = data_type.upper()
                        if data_type == 'character varying':
                            col_def = 'VARCHAR(255)'
                        elif data_type == 'timestamp with time zone':
                            col_def = 'TIMESTAMP WITH TIME ZONE'
                        elif data_type == 'timestamp without time zone':
                            col_def = 'TIMESTAMP'
                        
                        null_clause = '' if nullable == 'YES' else ' NOT NULL'
                        default_clause = ''
                        
                        # Add safe defaults for NOT NULL columns
                        if nullable == 'NO':
                            if 'boolean' in data_type.lower():
                                default_clause = ' DEFAULT FALSE'
                            elif 'integer' in data_type.lower() or 'numeric' in data_type.lower():
                                default_clause = ' DEFAULT 0'
                            elif 'varchar' in col_def.lower() or 'text' in col_def.lower():
                                default_clause = " DEFAULT ''"
                            elif 'timestamp' in col_def.lower():
                                default_clause = ' DEFAULT NOW()'
                        
                        alter_sql = f"ALTER TABLE hr_leavetype ADD COLUMN {col_name} {col_def}{null_clause}{default_clause}"
                        
                        try:
                            cursor.execute(alter_sql)
                            logger.info(f"  ✅ Added column {col_name}")
                        except Exception as e:
                            logger.error(f"  ❌ Failed to add {col_name}: {e}")
            
            # Now insert proper data
            cursor.execute('SET search_path TO "fox"')
            
            # Clear existing data
            cursor.execute("DELETE FROM hr_leavetype")
            
            # Get the actual column names now
            cursor.execute("""
                SELECT column_name
                FROM information_schema.columns 
                WHERE table_schema = 'fox' 
                AND table_name = 'hr_leavetype'
                AND column_name != 'id'
                ORDER BY ordinal_position
            """)
            
            available_columns = [col[0] for col in cursor.fetchall()]
            logger.info(f"\nAvailable columns for insert: {available_columns}")
            
            # Build insert statement with only available columns
            base_columns = ['name', 'description', 'is_paid', 'requires_approval', 'is_active', 'created_at', 'updated_at']
            insert_columns = [col for col in base_columns if col in available_columns]
            
            if 'max_days_per_year' in available_columns:
                insert_columns.insert(2, 'max_days_per_year')
            
            if 'accrual_frequency' in available_columns:
                insert_columns.append('accrual_frequency')
            
            columns_str = ', '.join(insert_columns)
            
            # Build values based on available columns
            values = []
            leave_types = [
                ('Annual Leave', 'Annual vacation leave', 21, True, True, True, 'YEARLY'),
                ('Sick Leave', 'Medical leave', 10, True, False, True, 'YEARLY'),
                ('Maternity Leave', 'Maternity leave', 90, True, True, True, 'ONCE'),
                ('Emergency Leave', 'Emergency leave', 3, False, True, True, 'YEARLY')
            ]
            
            for name, desc, max_days, is_paid, requires_approval, is_active, accrual in leave_types:
                value_parts = [f"'{name}'", f"'{desc}'"]
                
                if 'max_days_per_year' in insert_columns:
                    value_parts.append(str(max_days))
                
                value_parts.extend([str(is_paid), str(requires_approval), str(is_active), 'NOW()', 'NOW()'])
                
                if 'accrual_frequency' in insert_columns:
                    value_parts.append(f"'{accrual}'")
                
                values.append(f"({', '.join(value_parts)})")
            
            insert_sql = f"INSERT INTO hr_leavetype ({columns_str}) VALUES {', '.join(values)}"
            
            try:
                cursor.execute(insert_sql)
                logger.info("  ✅ Inserted leave type data")
                
                # Verify
                cursor.execute("SELECT COUNT(*) FROM hr_leavetype")
                count = cursor.fetchone()[0]
                logger.info(f"  ✅ hr_leavetype now has {count} records")
                
            except Exception as e:
                logger.error(f"  ❌ Failed to insert leave type data: {e}")
                logger.error(f"  SQL: {insert_sql}")
            
            logger.info("✅ hr_leavetype fix completed!")
            
    except Exception as e:
        logger.error(f"❌ Failed to fix hr_leavetype: {e}")

def main():
    """Main function"""
    logger.info("=== HR_LEAVETYPE FOX FIX ===")
    
    try:
        fix_hr_leavetype_fox()
        
        logger.info("\n🎉 HR_LEAVETYPE FOX FIX COMPLETE!")
        
        return True
        
    except Exception as e:
        logger.error(f"hr_leavetype fox fix failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
