"""
Management command to migrate all tenants
Usage: python manage.py migrate_all_tenants
"""

from django.core.management.base import BaseCommand
from django.core.management import call_command
from apps.tenants.models import School
from django_tenants.utils import schema_context
import logging

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Apply migrations to all tenant schemas'

    def add_arguments(self, parser):
        parser.add_argument(
            '--schema',
            type=str,
            help='Migrate only specific schema',
        )

    def handle(self, *args, **options):
        self.stdout.write("=== MIGRATING ALL TENANTS ===")

        # If specific schema is provided
        if options['schema']:
            schema_name = options['schema']
            self.stdout.write(f"Migrating specific schema: {schema_name}")
            try:
                if schema_name == 'public':
                    call_command('migrate_schemas', '--schema=public')
                else:
                    with schema_context(schema_name):
                        call_command('migrate')
                self.stdout.write(
                    self.style.SUCCESS(f"✅ Successfully migrated {schema_name}")
                )
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f"❌ Failed to migrate {schema_name}: {e}")
                )
            return

        # Migrate public schema first
        self.stdout.write("1. Migrating public schema...")
        try:
            call_command('migrate_schemas', '--schema=public')
            self.stdout.write(
                self.style.SUCCESS("✅ Public schema migrated successfully")
            )
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"❌ Failed to migrate public schema: {e}")
            )
            return

        # Get all tenants
        try:
            tenants = School.objects.all()
            self.stdout.write(f"Found {tenants.count()} tenant(s) to migrate")
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"❌ Failed to get tenants: {e}")
            )
            return

        # Migrate each tenant
        success_count = 0
        error_count = 0

        for tenant in tenants:
            self.stdout.write(f"2. Migrating tenant: {tenant.schema_name} ({tenant.name})")
            try:
                with schema_context(tenant.schema_name):
                    call_command('migrate')
                self.stdout.write(
                    self.style.SUCCESS(f"✅ Successfully migrated {tenant.schema_name}")
                )
                success_count += 1
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f"❌ Failed to migrate {tenant.schema_name}: {e}")
                )
                error_count += 1

        # Summary
        self.stdout.write("\n=== MIGRATION SUMMARY ===")
        self.stdout.write(f"✅ Successful migrations: {success_count}")
        self.stdout.write(f"❌ Failed migrations: {error_count}")

        if error_count == 0:
            self.stdout.write(
                self.style.SUCCESS("🎉 All tenants migrated successfully!")
            )
        else:
            self.stdout.write(
                self.style.WARNING(f"⚠️  {error_count} tenant(s) had migration errors")
            )