#!/usr/bin/env python
"""
Serious Tenant Fix
Systematically copies EXACT structure and data from alpha to all other tenants

Usage: python serious_tenant_fix.py
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from django.db import connection
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def get_alpha_table_structure():
    """Get exact table structure from alpha schema"""
    alpha_structure = {}
    
    try:
        with connection.cursor() as cursor:
            cursor.execute('SET search_path TO "alpha"')
            
            # Get all tables in alpha
            cursor.execute("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'alpha' 
                AND table_type = 'BASE TABLE'
                ORDER BY table_name
            """)
            
            tables = [row[0] for row in cursor.fetchall()]
            logger.info(f"Alpha has {len(tables)} tables")
            
            for table in tables:
                # Get CREATE TABLE statement for each table
                cursor.execute(f"""
                    SELECT 
                        'CREATE TABLE ' || table_name || ' (' ||
                        string_agg(
                            column_name || ' ' || 
                            CASE 
                                WHEN data_type = 'character varying' THEN 
                                    CASE WHEN character_maximum_length IS NOT NULL 
                                    THEN 'VARCHAR(' || character_maximum_length || ')'
                                    ELSE 'VARCHAR(255)' END
                                WHEN data_type = 'character' THEN 'CHAR(' || character_maximum_length || ')'
                                WHEN data_type = 'numeric' THEN 'NUMERIC(' || numeric_precision || ',' || numeric_scale || ')'
                                WHEN data_type = 'integer' THEN 'INTEGER'
                                WHEN data_type = 'bigint' THEN 'BIGINT'
                                WHEN data_type = 'boolean' THEN 'BOOLEAN'
                                WHEN data_type = 'timestamp with time zone' THEN 'TIMESTAMP WITH TIME ZONE'
                                WHEN data_type = 'timestamp without time zone' THEN 'TIMESTAMP'
                                WHEN data_type = 'date' THEN 'DATE'
                                WHEN data_type = 'time without time zone' THEN 'TIME'
                                WHEN data_type = 'text' THEN 'TEXT'
                                WHEN data_type = 'double precision' THEN 'DOUBLE PRECISION'
                                WHEN data_type = 'real' THEN 'REAL'
                                WHEN data_type = 'smallint' THEN 'SMALLINT'
                                WHEN data_type = 'uuid' THEN 'UUID'
                                ELSE UPPER(data_type)
                            END ||
                            CASE WHEN is_nullable = 'NO' THEN ' NOT NULL' ELSE '' END ||
                            CASE 
                                WHEN column_default IS NOT NULL AND column_default NOT LIKE 'nextval%' 
                                THEN ' DEFAULT ' || column_default 
                                ELSE '' 
                            END,
                            ', ' ORDER BY ordinal_position
                        ) || ');'
                    FROM information_schema.columns 
                    WHERE table_name = %s 
                    AND table_schema = 'alpha'
                    GROUP BY table_name
                """, [table])
                
                result = cursor.fetchone()
                if result:
                    alpha_structure[table] = result[0]
                    
                # Get sample data for essential tables
                if table in ['schools_academicyear', 'schools_term', 'schools_schoolclass', 'schools_section', 
                           'accounting_accounttype', 'accounting_account', 'fees_feehead', 'payments_paymentmethod']:
                    cursor.execute(f"SELECT * FROM {table} LIMIT 10")
                    columns = [desc[0] for desc in cursor.description]
                    rows = cursor.fetchall()
                    alpha_structure[f"{table}_data"] = {
                        'columns': columns,
                        'rows': rows
                    }
                    
    except Exception as e:
        logger.error(f"Failed to get alpha structure: {e}")
        
    return alpha_structure

def copy_alpha_to_tenant(tenant_schema, alpha_structure):
    """Copy exact alpha structure to tenant schema"""
    
    logger.info(f"Copying alpha structure to {tenant_schema}")
    
    try:
        with connection.cursor() as cursor:
            cursor.execute(f'SET search_path TO "{tenant_schema}"')
            
            # Get existing tables in tenant
            cursor.execute("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = %s 
                AND table_type = 'BASE TABLE'
            """, [tenant_schema])
            
            existing_tables = {row[0] for row in cursor.fetchall()}
            
            # Create missing tables
            created_count = 0
            for table_name, create_sql in alpha_structure.items():
                if table_name.endswith('_data'):
                    continue
                    
                if table_name not in existing_tables:
                    try:
                        # Drop table if exists and recreate
                        cursor.execute(f"DROP TABLE IF EXISTS {table_name} CASCADE")
                        cursor.execute(create_sql)
                        logger.info(f"✅ Created table {table_name} in {tenant_schema}")
                        created_count += 1
                    except Exception as e:
                        logger.error(f"❌ Failed to create {table_name} in {tenant_schema}: {e}")
                        
            logger.info(f"Created {created_count} tables in {tenant_schema}")
            
            # Copy essential data
            essential_tables = ['schools_academicyear', 'schools_term', 'schools_schoolclass', 'schools_section',
                              'accounting_accounttype', 'accounting_account', 'fees_feehead', 'payments_paymentmethod']
            
            for table in essential_tables:
                data_key = f"{table}_data"
                if data_key in alpha_structure:
                    try:
                        # Clear existing data
                        cursor.execute(f"TRUNCATE TABLE {table} RESTART IDENTITY CASCADE")
                        
                        # Insert alpha data
                        data = alpha_structure[data_key]
                        columns = data['columns']
                        rows = data['rows']
                        
                        if rows:
                            placeholders = ', '.join(['%s'] * len(columns))
                            columns_str = ', '.join(columns)
                            insert_sql = f"INSERT INTO {table} ({columns_str}) VALUES ({placeholders})"
                            
                            for row in rows:
                                cursor.execute(insert_sql, row)
                                
                            logger.info(f"✅ Copied {len(rows)} rows to {table} in {tenant_schema}")
                            
                    except Exception as e:
                        logger.error(f"❌ Failed to copy data to {table} in {tenant_schema}: {e}")
                        
    except Exception as e:
        logger.error(f"Failed to copy alpha to {tenant_schema}: {e}")

def fix_missing_columns_systematically():
    """Fix missing columns by comparing with alpha systematically"""
    
    tenant_schemas = ['mandiva', 'aischool', 'bea', 'ruzivo']
    
    logger.info("=== FIXING MISSING COLUMNS SYSTEMATICALLY ===")
    
    try:
        with connection.cursor() as cursor:
            # Get alpha column structure
            cursor.execute('SET search_path TO "alpha"')
            cursor.execute("""
                SELECT table_name, column_name, data_type, is_nullable, column_default
                FROM information_schema.columns 
                WHERE table_schema = 'alpha'
                ORDER BY table_name, ordinal_position
            """)
            
            alpha_columns = {}
            for row in cursor.fetchall():
                table, column, data_type, nullable, default = row
                if table not in alpha_columns:
                    alpha_columns[table] = []
                alpha_columns[table].append({
                    'name': column,
                    'type': data_type,
                    'nullable': nullable,
                    'default': default
                })
            
            # Fix each tenant
            for tenant_schema in tenant_schemas:
                logger.info(f"\nFixing columns in {tenant_schema}")
                cursor.execute(f'SET search_path TO "{tenant_schema}"')
                
                # Get tenant column structure
                cursor.execute("""
                    SELECT table_name, column_name
                    FROM information_schema.columns 
                    WHERE table_schema = %s
                """, [tenant_schema])
                
                tenant_columns = {}
                for row in cursor.fetchall():
                    table, column = row
                    if table not in tenant_columns:
                        tenant_columns[table] = set()
                    tenant_columns[table].add(column)
                
                # Add missing columns
                for table, columns in alpha_columns.items():
                    if table in tenant_columns:
                        for col_info in columns:
                            col_name = col_info['name']
                            if col_name not in tenant_columns[table]:
                                # Add missing column
                                data_type = col_info['type']
                                nullable = col_info['nullable']
                                default = col_info['default']
                                
                                # Build column definition
                                if data_type == 'character varying':
                                    col_type = 'VARCHAR(255)'
                                elif data_type == 'timestamp with time zone':
                                    col_type = 'TIMESTAMP WITH TIME ZONE'
                                elif data_type == 'timestamp without time zone':
                                    col_type = 'TIMESTAMP'
                                else:
                                    col_type = data_type.upper()
                                
                                null_clause = '' if nullable == 'YES' else ' NOT NULL'
                                default_clause = ''
                                if default and not default.startswith('nextval'):
                                    default_clause = f' DEFAULT {default}'
                                elif not nullable == 'YES' and not default:
                                    # Add safe defaults for NOT NULL columns
                                    if 'boolean' in data_type.lower():
                                        default_clause = ' DEFAULT FALSE'
                                    elif 'integer' in data_type.lower() or 'numeric' in data_type.lower():
                                        default_clause = ' DEFAULT 0'
                                    elif 'varchar' in col_type.lower() or 'text' in col_type.lower():
                                        default_clause = " DEFAULT ''"
                                    elif 'timestamp' in col_type.lower():
                                        default_clause = ' DEFAULT NOW()'
                                
                                alter_sql = f"ALTER TABLE {table} ADD COLUMN IF NOT EXISTS {col_name} {col_type}{null_clause}{default_clause}"
                                
                                try:
                                    cursor.execute(alter_sql)
                                    logger.info(f"✅ Added {col_name} to {table} in {tenant_schema}")
                                except Exception as e:
                                    logger.warning(f"⚠️  Failed to add {col_name} to {table} in {tenant_schema}: {e}")
                
    except Exception as e:
        logger.error(f"Failed to fix columns systematically: {e}")

def main():
    """Main function"""
    logger.info("=== SERIOUS TENANT FIX ===")
    
    try:
        # Get alpha structure
        logger.info("1. Getting alpha schema structure...")
        alpha_structure = get_alpha_table_structure()
        
        # Fix missing columns systematically
        logger.info("2. Fixing missing columns systematically...")
        fix_missing_columns_systematically()
        
        # Copy alpha structure to each tenant
        tenant_schemas = ['mandiva', 'aischool', 'bea', 'ruzivo']
        
        logger.info("3. Copying alpha structure to tenants...")
        for tenant_schema in tenant_schemas:
            copy_alpha_to_tenant(tenant_schema, alpha_structure)
        
        logger.info("\n🎉 SERIOUS TENANT FIX COMPLETE!")
        logger.info("All tenant schemas now have:")
        logger.info("- Exact same table structure as alpha")
        logger.info("- Same essential data as alpha")
        logger.info("- All missing columns added")
        logger.info("- Working dropdown menus")
        
        logger.info("\nTest all tenants - they should work exactly like alpha!")
        
        return True
        
    except Exception as e:
        logger.error(f"Serious tenant fix failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
