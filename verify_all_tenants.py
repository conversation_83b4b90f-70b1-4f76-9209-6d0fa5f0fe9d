#!/usr/bin/env python
"""
Verify All Tenants
Comprehensive verification that all tenants are working properly

Usage: python verify_all_tenants.py
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from django.db import connection
from apps.tenants.models import School
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def verify_all_tenants():
    """Verify all tenants are working properly"""
    
    logger.info("=== VERIFYING ALL TENANTS ===")
    
    try:
        tenants = School.objects.exclude(schema_name='public')
        
        results = {}
        
        for tenant in tenants:
            logger.info(f"\n--- Verifying {tenant.schema_name} ---")
            results[tenant.schema_name] = verify_tenant(tenant.schema_name)
        
        # Summary
        logger.info("\n=== VERIFICATION SUMMARY ===")
        
        passed = 0
        failed = 0
        
        for tenant_name, result in results.items():
            if result['overall_status']:
                logger.info(f"✅ {tenant_name}: PASSED")
                passed += 1
            else:
                logger.info(f"❌ {tenant_name}: FAILED")
                failed += 1
                for issue in result['issues']:
                    logger.info(f"    - {issue}")
        
        logger.info(f"\nTotal: {len(results)} tenants")
        logger.info(f"Passed: {passed}")
        logger.info(f"Failed: {failed}")
        
        if failed == 0:
            logger.info("\n🎉 ALL TENANTS VERIFIED SUCCESSFULLY!")
            logger.info("The universal template system is working perfectly!")
        else:
            logger.info(f"\n⚠️  {failed} tenants have issues that need attention")
        
        return failed == 0
        
    except Exception as e:
        logger.error(f"❌ Failed to verify tenants: {e}")
        return False

def verify_tenant(tenant_schema):
    """Verify specific tenant"""
    
    result = {
        'overall_status': True,
        'issues': [],
        'tables_checked': 0,
        'tables_missing': 0,
        'columns_checked': 0,
        'columns_missing': 0,
        'data_tables_checked': 0,
        'data_tables_empty': 0
    }
    
    try:
        with connection.cursor() as cursor:
            cursor.execute(f'SET search_path TO "{tenant_schema}"')
            
            # 1. Check essential tables
            essential_tables = [
                'schools_academicyear', 'schools_staffuser', 'students_student',
                'schools_schoolprofile', 'auth_group', 'auth_permission',
                'django_content_type', 'hr_leavetype', 'payments_paymentmethod',
                'fees_concessiontype', 'school_calendar_eventcategory',
                'fees_invoice', 'payments_payment', 'hr_employeeprofile'
            ]
            
            for table in essential_tables:
                result['tables_checked'] += 1
                cursor.execute(f"""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_schema = '{tenant_schema}' 
                        AND table_name = '{table}'
                    )
                """)
                
                if not cursor.fetchone()[0]:
                    result['issues'].append(f"Missing table: {table}")
                    result['tables_missing'] += 1
                    result['overall_status'] = False
            
            # 2. Check essential columns
            essential_columns = [
                ('schools_academicyear', 'is_active'),
                ('students_student', 'roll_number'),
                ('schools_staffuser', 'employee_id'),
                ('hr_employeeprofile', 'phone_number_primary')
            ]
            
            for table, column in essential_columns:
                result['columns_checked'] += 1
                try:
                    cursor.execute(f"""
                        SELECT EXISTS (
                            SELECT FROM information_schema.columns 
                            WHERE table_schema = '{tenant_schema}' 
                            AND table_name = '{table}'
                            AND column_name = '{column}'
                        )
                    """)
                    
                    if not cursor.fetchone()[0]:
                        result['issues'].append(f"Missing column: {table}.{column}")
                        result['columns_missing'] += 1
                        result['overall_status'] = False
                        
                except Exception as e:
                    result['issues'].append(f"Error checking column {table}.{column}: {e}")
                    result['overall_status'] = False
            
            # 3. Check essential data
            data_checks = [
                ('auth_group', 5),
                ('auth_permission', 100),
                ('django_content_type', 50),
                ('hr_leavetype', 3),
                ('payments_paymentmethod', 3),
                ('fees_concessiontype', 2)
            ]
            
            for table, min_expected in data_checks:
                result['data_tables_checked'] += 1
                try:
                    cursor.execute(f"SELECT COUNT(*) FROM {table}")
                    count = cursor.fetchone()[0]
                    
                    if count < min_expected:
                        result['issues'].append(f"Insufficient data in {table}: {count} (expected >= {min_expected})")
                        result['data_tables_empty'] += 1
                        result['overall_status'] = False
                        
                except Exception as e:
                    result['issues'].append(f"Error checking data in {table}: {e}")
                    result['overall_status'] = False
            
            # 4. Test basic functionality
            try:
                # Test academic year access
                cursor.execute("SELECT COUNT(*) FROM schools_academicyear")
                ay_count = cursor.fetchone()[0]
                
                # Test staff user access
                cursor.execute("SELECT COUNT(*) FROM schools_staffuser")
                staff_count = cursor.fetchone()[0]
                
                # Test student access
                cursor.execute("SELECT COUNT(*) FROM students_student")
                student_count = cursor.fetchone()[0]
                
                logger.info(f"  ✅ Basic functionality: AY={ay_count}, Staff={staff_count}, Students={student_count}")
                
            except Exception as e:
                result['issues'].append(f"Basic functionality test failed: {e}")
                result['overall_status'] = False
            
            # Log results
            if result['overall_status']:
                logger.info(f"  ✅ {tenant_schema}: All checks passed")
            else:
                logger.info(f"  ❌ {tenant_schema}: {len(result['issues'])} issues found")
                for issue in result['issues'][:3]:  # Show first 3 issues
                    logger.info(f"    - {issue}")
                if len(result['issues']) > 3:
                    logger.info(f"    ... and {len(result['issues']) - 3} more issues")
            
            return result
            
    except Exception as e:
        result['issues'].append(f"Verification error: {e}")
        result['overall_status'] = False
        logger.error(f"  ❌ {tenant_schema}: Verification failed - {e}")
        return result

def test_specific_functionality():
    """Test specific functionality that was previously failing"""
    
    logger.info("\n=== TESTING SPECIFIC FUNCTIONALITY ===")
    
    test_cases = [
        {
            'tenant': 'fox',
            'url_path': '/portal/reporting/fee-projection/',
            'description': 'Fee Projection Report (was failing due to missing schools_schoolprofile)'
        },
        {
            'tenant': 'mandiva',
            'url_path': '/portal/admin/roles/',
            'description': 'Roles Management (was failing due to SQL GROUP BY error)'
        },
        {
            'tenant': 'alpha',
            'url_path': '/portal/dashboard/',
            'description': 'Dashboard Access (baseline working tenant)'
        }
    ]
    
    for test_case in test_cases:
        logger.info(f"\nTesting: {test_case['description']}")
        logger.info(f"Tenant: {test_case['tenant']}")
        logger.info(f"URL: http://{test_case['tenant']}.myapp.test:8000{test_case['url_path']}")
        
        # Test database access for the functionality
        try:
            with connection.cursor() as cursor:
                cursor.execute(f'SET search_path TO "{test_case["tenant"]}"')
                
                if 'fee-projection' in test_case['url_path']:
                    # Test schools_schoolprofile access
                    cursor.execute("SELECT COUNT(*) FROM schools_schoolprofile")
                    count = cursor.fetchone()[0]
                    logger.info(f"  ✅ schools_schoolprofile accessible: {count} records")
                
                elif 'roles' in test_case['url_path']:
                    # Test auth_group access with proper GROUP BY
                    cursor.execute("SELECT COUNT(*) FROM auth_group")
                    count = cursor.fetchone()[0]
                    logger.info(f"  ✅ auth_group accessible: {count} records")
                
                elif 'dashboard' in test_case['url_path']:
                    # Test basic dashboard requirements
                    cursor.execute("SELECT COUNT(*) FROM schools_academicyear")
                    ay_count = cursor.fetchone()[0]
                    cursor.execute("SELECT COUNT(*) FROM announcements_announcement")
                    ann_count = cursor.fetchone()[0]
                    logger.info(f"  ✅ Dashboard data accessible: AY={ay_count}, Announcements={ann_count}")
                
        except Exception as e:
            logger.error(f"  ❌ Database test failed: {e}")

def main():
    """Main function"""
    logger.info("=== COMPREHENSIVE TENANT VERIFICATION ===")
    
    try:
        # Verify all tenants
        all_passed = verify_all_tenants()
        
        # Test specific functionality
        test_specific_functionality()
        
        if all_passed:
            logger.info("\n🎉 COMPREHENSIVE VERIFICATION PASSED!")
            logger.info("✅ All tenants are working correctly")
            logger.info("✅ Universal template system is successful")
            logger.info("✅ No manual fixes required for any tenant")
            logger.info("✅ New tenants will be created with complete structure")
        else:
            logger.info("\n⚠️  SOME ISSUES FOUND")
            logger.info("Run: python manage.py apply_universal_template --all")
            logger.info("To fix any remaining issues")
        
        return all_passed
        
    except Exception as e:
        logger.error(f"Comprehensive verification failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
