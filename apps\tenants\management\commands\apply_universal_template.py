"""
Apply Universal Template
Applies a curated, universal template to ALL tenants (existing and new)

Usage: 
python manage.py apply_universal_template --all
python manage.py apply_universal_template --tenant=schema_name
python manage.py apply_universal_template --create-template
"""

from django.core.management.base import BaseCommand
from django.db import connection
from django.core.management import call_command
from apps.tenants.models import School
from django_tenants.utils import schema_context
import json
import os
import logging

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Apply universal template to all tenants for consistency'

    def add_arguments(self, parser):
        parser.add_argument(
            '--all',
            action='store_true',
            help='Apply template to all tenants',
        )
        parser.add_argument(
            '--tenant',
            type=str,
            help='Apply template to specific tenant',
        )
        parser.add_argument(
            '--create-template',
            action='store_true',
            help='Create universal template from alpha tenant',
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force application even if tenant seems complete',
        )

    def handle(self, *args, **options):
        if options['create_template']:
            self.create_universal_template()
        elif options['all']:
            self.apply_to_all_tenants(options['force'])
        elif options['tenant']:
            self.apply_to_tenant(options['tenant'], options['force'])
        else:
            self.stdout.write("Use --all, --tenant=<name>, or --create-template")

    def create_universal_template(self):
        """Create universal template from alpha tenant"""
        
        self.stdout.write("=== CREATING UNIVERSAL TEMPLATE ===")
        
        try:
            with connection.cursor() as cursor:
                cursor.execute('SET search_path TO "alpha"')
                
                # Get all tables from alpha
                cursor.execute("""
                    SELECT table_name 
                    FROM information_schema.tables 
                    WHERE table_schema = 'alpha' 
                    AND table_type = 'BASE TABLE'
                    ORDER BY table_name
                """)
                
                tables = [row[0] for row in cursor.fetchall()]
                self.stdout.write(f"Found {len(tables)} tables in alpha")
                
                template = {
                    'version': '1.0',
                    'created_from': 'alpha',
                    'tables': {},
                    'essential_data': {},
                    'missing_table_fixes': {}
                }
                
                # Get structure for each table
                for table in tables:
                    self.stdout.write(f"Processing {table}...")
                    
                    # Get column definitions
                    cursor.execute(f"""
                        SELECT 
                            column_name, 
                            data_type, 
                            character_maximum_length,
                            numeric_precision,
                            numeric_scale,
                            is_nullable, 
                            column_default,
                            ordinal_position
                        FROM information_schema.columns 
                        WHERE table_schema = 'alpha' 
                        AND table_name = '{table}'
                        ORDER BY ordinal_position
                    """)
                    
                    columns = cursor.fetchall()
                    template['tables'][table] = {
                        'columns': columns,
                        'create_sql': self.generate_create_sql(table, columns)
                    }
                
                # Get essential data from key tables
                essential_tables = [
                    'auth_group', 'auth_permission', 'django_content_type',
                    'hr_leavetype', 'payments_paymentmethod', 'fees_concessiontype',
                    'school_calendar_eventcategory', 'hr_salarycomponent',
                    'hr_statutorydeduction', 'hr_taxbracket'
                ]
                
                for table in essential_tables:
                    if table in template['tables']:
                        try:
                            cursor.execute(f"SELECT * FROM {table}")
                            rows = cursor.fetchall()
                            
                            cursor.execute(f"""
                                SELECT column_name 
                                FROM information_schema.columns 
                                WHERE table_schema = 'alpha' 
                                AND table_name = '{table}'
                                ORDER BY ordinal_position
                            """)
                            
                            col_names = [row[0] for row in cursor.fetchall()]
                            
                            template['essential_data'][table] = {
                                'columns': col_names,
                                'rows': [list(row) for row in rows]
                            }
                            
                            self.stdout.write(f"  ✅ Captured {len(rows)} rows from {table}")
                            
                        except Exception as e:
                            self.stdout.write(f"  ⚠️  Could not capture data from {table}: {e}")
                
                # Save template
                template_file = "universal_tenant_template.json"
                with open(template_file, 'w') as f:
                    json.dump(template, f, indent=2, default=str)
                
                self.stdout.write(
                    self.style.SUCCESS(f"✅ Universal template saved to {template_file}")
                )
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"❌ Failed to create universal template: {e}")
            )

    def generate_create_sql(self, table_name, columns):
        """Generate CREATE TABLE SQL from column definitions"""
        
        column_defs = []
        
        for col_name, data_type, max_len, num_prec, num_scale, nullable, default, position in columns:
            col_def = f'"{col_name}" '
            
            # Handle data types
            if data_type == 'character varying':
                if max_len:
                    col_def += f'VARCHAR({max_len})'
                else:
                    col_def += 'VARCHAR(255)'
            elif data_type == 'numeric' and num_prec and num_scale:
                col_def += f'NUMERIC({num_prec},{num_scale})'
            elif data_type == 'timestamp with time zone':
                col_def += 'TIMESTAMP WITH TIME ZONE'
            elif data_type == 'timestamp without time zone':
                col_def += 'TIMESTAMP'
            else:
                col_def += data_type.upper()
            
            # Handle nullable
            if nullable == 'NO':
                col_def += ' NOT NULL'
            
            # Handle defaults (skip sequences)
            if default and not default.startswith('nextval'):
                col_def += f' DEFAULT {default}'
            
            column_defs.append(col_def)
        
        return f'CREATE TABLE IF NOT EXISTS "{table_name}" ({", ".join(column_defs)})'

    def apply_to_all_tenants(self, force=False):
        """Apply universal template to all tenants"""
        
        self.stdout.write("=== APPLYING UNIVERSAL TEMPLATE TO ALL TENANTS ===")
        
        try:
            tenants = School.objects.exclude(schema_name='public')
            
            for tenant in tenants:
                self.stdout.write(f"\n--- Processing {tenant.schema_name} ---")
                self.apply_universal_template_to_tenant(tenant.schema_name, force)
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"Failed to apply template to all tenants: {e}")
            )

    def apply_to_tenant(self, tenant_name, force=False):
        """Apply universal template to specific tenant"""
        
        self.stdout.write(f"=== APPLYING UNIVERSAL TEMPLATE TO {tenant_name.upper()} ===")
        
        try:
            tenant = School.objects.get(schema_name=tenant_name)
            self.apply_universal_template_to_tenant(tenant_name, force)
            
        except School.DoesNotExist:
            self.stdout.write(
                self.style.ERROR(f"Tenant '{tenant_name}' not found")
            )
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"Failed to apply template to {tenant_name}: {e}")
            )

    def apply_universal_template_to_tenant(self, tenant_schema, force=False):
        """Apply universal template to specific tenant"""
        
        try:
            # Load template
            template_file = "universal_tenant_template.json"
            if not os.path.exists(template_file):
                self.stdout.write(
                    self.style.ERROR(f"Template file {template_file} not found. Run --create-template first.")
                )
                return
            
            with open(template_file, 'r') as f:
                template = json.load(f)
            
            with connection.cursor() as cursor:
                cursor.execute(f'SET search_path TO "{tenant_schema}"')
                
                self.stdout.write(f"Applying universal template to {tenant_schema}...")
                
                # 1. Create missing tables
                missing_tables = self.find_missing_tables(cursor, tenant_schema, template)
                if missing_tables:
                    self.stdout.write(f"  Creating {len(missing_tables)} missing tables...")
                    for table in missing_tables:
                        self.create_missing_table(cursor, table, template)
                
                # 2. Add missing columns
                missing_columns = self.find_missing_columns(cursor, tenant_schema, template)
                if missing_columns:
                    self.stdout.write(f"  Adding {len(missing_columns)} missing columns...")
                    for table, column, col_def in missing_columns:
                        self.add_missing_column(cursor, table, column, col_def)
                
                # 3. Apply essential data
                self.stdout.write("  Applying essential data...")
                self.apply_essential_data(cursor, template)
                
                # 4. Create sequences
                self.create_sequences(cursor, template)
                
                self.stdout.write(
                    self.style.SUCCESS(f"✅ Universal template applied to {tenant_schema}")
                )
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"❌ Failed to apply template to {tenant_schema}: {e}")
            )

    def find_missing_tables(self, cursor, tenant_schema, template):
        """Find tables that exist in template but not in tenant"""
        
        missing_tables = []
        
        for table_name in template['tables'].keys():
            cursor.execute(f"""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_schema = '{tenant_schema}' 
                    AND table_name = '{table_name}'
                )
            """)
            
            if not cursor.fetchone()[0]:
                missing_tables.append(table_name)
        
        return missing_tables

    def find_missing_columns(self, cursor, tenant_schema, template):
        """Find columns that exist in template but not in tenant tables"""
        
        missing_columns = []
        
        for table_name, table_info in template['tables'].items():
            # Check if table exists in tenant
            cursor.execute(f"""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_schema = '{tenant_schema}' 
                    AND table_name = '{table_name}'
                )
            """)
            
            if cursor.fetchone()[0]:  # Table exists, check columns
                for col_name, data_type, max_len, num_prec, num_scale, nullable, default, position in table_info['columns']:
                    cursor.execute(f"""
                        SELECT EXISTS (
                            SELECT FROM information_schema.columns 
                            WHERE table_schema = '{tenant_schema}' 
                            AND table_name = '{table_name}'
                            AND column_name = '{col_name}'
                        )
                    """)
                    
                    if not cursor.fetchone()[0]:
                        # Generate column definition
                        col_def = self.generate_column_definition(col_name, data_type, max_len, num_prec, num_scale, nullable, default)
                        missing_columns.append((table_name, col_name, col_def))
        
        return missing_columns

    def generate_column_definition(self, col_name, data_type, max_len, num_prec, num_scale, nullable, default):
        """Generate column definition for ALTER TABLE"""
        
        col_def = ''
        
        # Handle data types
        if data_type == 'character varying':
            if max_len:
                col_def += f'VARCHAR({max_len})'
            else:
                col_def += 'VARCHAR(255)'
        elif data_type == 'numeric' and num_prec and num_scale:
            col_def += f'NUMERIC({num_prec},{num_scale})'
        elif data_type == 'timestamp with time zone':
            col_def += 'TIMESTAMP WITH TIME ZONE'
        elif data_type == 'timestamp without time zone':
            col_def += 'TIMESTAMP'
        else:
            col_def += data_type.upper()
        
        # Handle defaults (skip sequences)
        if default and not default.startswith('nextval'):
            col_def += f' DEFAULT {default}'
        
        return col_def

    def create_missing_table(self, cursor, table_name, template):
        """Create missing table using template"""
        
        try:
            create_sql = template['tables'][table_name]['create_sql']
            cursor.execute(create_sql)
            
            # Create sequence if needed
            columns = template['tables'][table_name]['columns']
            if any(col[0] == 'id' for col in columns):
                try:
                    cursor.execute(f'CREATE SEQUENCE IF NOT EXISTS {table_name}_id_seq')
                    cursor.execute(f'ALTER TABLE {table_name} ALTER COLUMN id SET DEFAULT nextval(\'{table_name}_id_seq\')')
                except:
                    pass
            
            self.stdout.write(f"    ✅ Created table: {table_name}")
            
        except Exception as e:
            self.stdout.write(f"    ❌ Failed to create table {table_name}: {e}")

    def add_missing_column(self, cursor, table_name, column_name, column_def):
        """Add missing column to table"""
        
        try:
            alter_sql = f'ALTER TABLE {table_name} ADD COLUMN {column_name} {column_def}'
            cursor.execute(alter_sql)
            self.stdout.write(f"    ✅ Added column: {table_name}.{column_name}")
            
        except Exception as e:
            self.stdout.write(f"    ❌ Failed to add column {table_name}.{column_name}: {e}")

    def apply_essential_data(self, cursor, template):
        """Apply essential data from template"""
        
        for table_name, data_info in template['essential_data'].items():
            try:
                # Check if table exists and is empty
                cursor.execute(f"""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_name = '{table_name}'
                    )
                """)
                
                if cursor.fetchone()[0]:
                    cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                    if cursor.fetchone()[0] == 0:  # Table is empty
                        # Insert data
                        columns_str = ', '.join([f'"{col}"' for col in data_info['columns']])
                        
                        for row in data_info['rows']:
                            values = []
                            for value in row:
                                if value is None:
                                    values.append('NULL')
                                elif isinstance(value, str):
                                    escaped_value = value.replace("'", "''")
                                    values.append(f"'{escaped_value}'")
                                elif isinstance(value, bool):
                                    values.append('TRUE' if value else 'FALSE')
                                else:
                                    values.append(str(value))
                            
                            insert_sql = f"INSERT INTO {table_name} ({columns_str}) VALUES ({', '.join(values)})"
                            cursor.execute(insert_sql)
                        
                        self.stdout.write(f"    ✅ Applied data to: {table_name}")
                
            except Exception as e:
                self.stdout.write(f"    ⚠️  Data application for {table_name}: {e}")

    def create_sequences(self, cursor, template):
        """Create missing sequences"""
        
        sequence_tables = ['schools_invoicesequence', 'schools_receiptsequence']
        
        for seq_table in sequence_tables:
            if seq_table in template['tables']:
                try:
                    cursor.execute(f"SELECT COUNT(*) FROM {seq_table}")
                    if cursor.fetchone()[0] == 0:
                        prefix = 'INV' if 'invoice' in seq_table else 'RCP'
                        cursor.execute(f"""
                            INSERT INTO {seq_table} (prefix, current_number, created_at, updated_at) 
                            VALUES ('{prefix}', 1, NOW(), NOW())
                        """)
                        self.stdout.write(f"    ✅ Created sequence: {seq_table}")
                        
                except Exception as e:
                    self.stdout.write(f"    ⚠️  Sequence {seq_table}: {e}")
