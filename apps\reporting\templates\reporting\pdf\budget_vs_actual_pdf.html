<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>{{ report_title }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            margin: 0;
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
        }
        .school-name {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .report-title {
            font-size: 16px;
            font-weight: bold;
            margin: 10px 0;
        }
        .report-period {
            font-size: 12px;
            color: #666;
        }
        .summary-section {
            margin: 20px 0;
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
        }
        .summary-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
        }
        .summary-table th,
        .summary-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
        }
        .summary-table th {
            background-color: #e9ecef;
            font-weight: bold;
        }
        .income-amount {
            color: #28a745;
            font-weight: bold;
        }
        .expense-amount {
            color: #dc3545;
            font-weight: bold;
        }
        .favorable-variance {
            color: #28a745;
            font-weight: bold;
        }
        .unfavorable-variance {
            color: #ffc107;
            font-weight: bold;
        }
        .net-amount {
            color: #007bff;
            font-weight: bold;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 6px;
            text-align: left;
            font-size: 10px;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .text-right {
            text-align: right;
        }
        .text-center {
            text-align: center;
        }
        .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 10px;
            color: #666;
            border-top: 1px solid #ddd;
            padding-top: 10px;
        }
        .page-break {
            page-break-before: always;
        }
        .totals-row {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .budget-item-name {
            font-weight: bold;
        }
        .budget-description {
            font-size: 9px;
            color: #666;
        }
        .account-code {
            font-size: 9px;
            color: #666;
        }
        .type-badge {
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 9px;
            font-weight: bold;
        }
        .type-income {
            background-color: #28a745;
            color: white;
        }
        .type-expense {
            background-color: #dc3545;
            color: white;
        }
        .status-favorable {
            background-color: #28a745;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 9px;
        }
        .status-unfavorable {
            background-color: #ffc107;
            color: black;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 9px;
        }
        .variance-analysis {
            background-color: #e3f2fd;
            padding: 15px;
            margin: 20px 0;
            border-left: 4px solid #2196f3;
        }
    </style>
</head>
<body>
    <!-- Header Section -->
    <div class="header">
        {% if school_profile %}
        <div class="school-name">{{ school_profile.school_name }}</div>
        {% if school_profile.address %}
        <div>{{ school_profile.address }}</div>
        {% endif %}
        {% if school_profile.phone %}
        <div>Phone: {{ school_profile.phone }}</div>
        {% endif %}
        {% if school_profile.email %}
        <div>Email: {{ school_profile.email }}</div>
        {% endif %}
        {% else %}
        <div class="school-name">{{ tenant.name }}</div>
        {% endif %}
        
        <div class="report-title">{{ report_title }}</div>
        <div class="report-period">
            {% if academic_year %}Academic Year: {{ academic_year }}{% endif %}
            {% if term %} | Term: {{ term }}{% endif %}
        </div>
    </div>

    <!-- Summary Section -->
    <div class="summary-section">
        <h3>Budget Performance Summary</h3>
        <table class="summary-table">
            <thead>
                <tr>
                    <th>Category</th>
                    <th>Budgeted Amount</th>
                    <th>Actual Amount</th>
                    <th>Variance Amount</th>
                    <th>Variance %</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><strong>Income</strong></td>
                    <td class="text-right income-amount">{{ total_budgeted_income|floatformat:2 }}</td>
                    <td class="text-right income-amount">{{ total_actual_income|floatformat:2 }}</td>
                    <td class="text-right {% if variance_summary.income_variance >= 0 %}favorable-variance{% else %}unfavorable-variance{% endif %}">
                        {% if variance_summary.income_variance >= 0 %}+{% endif %}{{ variance_summary.income_variance|floatformat:2 }}
                    </td>
                    <td class="text-right {% if variance_summary.income_variance >= 0 %}favorable-variance{% else %}unfavorable-variance{% endif %}">
                        {% if variance_summary.income_variance_pct >= 0 %}+{% endif %}{{ variance_summary.income_variance_pct|floatformat:1 }}%
                    </td>
                </tr>
                <tr>
                    <td><strong>Expense</strong></td>
                    <td class="text-right expense-amount">{{ total_budgeted_expense|floatformat:2 }}</td>
                    <td class="text-right expense-amount">{{ total_actual_expense|floatformat:2 }}</td>
                    <td class="text-right {% if variance_summary.expense_variance <= 0 %}favorable-variance{% else %}unfavorable-variance{% endif %}">
                        {% if variance_summary.expense_variance >= 0 %}+{% endif %}{{ variance_summary.expense_variance|floatformat:2 }}
                    </td>
                    <td class="text-right {% if variance_summary.expense_variance <= 0 %}favorable-variance{% else %}unfavorable-variance{% endif %}">
                        {% if variance_summary.expense_variance_pct >= 0 %}+{% endif %}{{ variance_summary.expense_variance_pct|floatformat:1 }}%
                    </td>
                </tr>
                <tr style="border-top: 2px solid #333;">
                    <td><strong>Net Position</strong></td>
                    <td class="text-right net-amount"><strong>{{ variance_summary.net_budget|floatformat:2 }}</strong></td>
                    <td class="text-right net-amount"><strong>{{ variance_summary.net_actual|floatformat:2 }}</strong></td>
                    <td class="text-right {% if variance_summary.net_variance >= 0 %}favorable-variance{% else %}unfavorable-variance{% endif %}">
                        <strong>{% if variance_summary.net_variance >= 0 %}+{% endif %}{{ variance_summary.net_variance|floatformat:2 }}</strong>
                    </td>
                    <td class="text-center">-</td>
                </tr>
            </tbody>
        </table>
    </div>

    <!-- Variance Analysis -->
    {% if variance_summary %}
    <div class="variance-analysis">
        <h3>Variance Analysis</h3>
        <ul>
            <li><strong>Income Performance:</strong> 
                {% if variance_summary.income_variance >= 0 %}
                Income exceeded budget by {{ variance_summary.income_variance|floatformat:2 }} ({{ variance_summary.income_variance_pct|floatformat:1 }}%)
                {% else %}
                Income fell short of budget by {{ variance_summary.income_variance|make_positive|floatformat:2 }} ({{ variance_summary.income_variance_pct|make_positive|floatformat:1 }}%)
                {% endif %}
            </li>
            <li><strong>Expense Control:</strong> 
                {% if variance_summary.expense_variance <= 0 %}
                Expenses were {{ variance_summary.expense_variance|make_positive|floatformat:2 }} under budget ({{ variance_summary.expense_variance_pct|make_positive|floatformat:1 }}%)
                {% else %}
                Expenses exceeded budget by {{ variance_summary.expense_variance|floatformat:2 }} ({{ variance_summary.expense_variance_pct|floatformat:1 }}%)
                {% endif %}
            </li>
            <li><strong>Overall Performance:</strong> 
                {% if variance_summary.net_variance >= 0 %}
                Net position improved by {{ variance_summary.net_variance|floatformat:2 }} compared to budget
                {% else %}
                Net position was {{ variance_summary.net_variance|make_positive|floatformat:2 }} below budget
                {% endif %}
            </li>
        </ul>
    </div>
    {% endif %}

    <!-- Budget Item Details -->
    {% if budget_analysis %}
    <h3>Budget Item Analysis</h3>
    <table>
        <thead>
            <tr>
                <th style="width: 20%;">Budget Item</th>
                <th style="width: 8%;">Type</th>
                <th style="width: 15%;">Account</th>
                <th style="width: 12%;" class="text-right">Budgeted</th>
                <th style="width: 12%;" class="text-right">Actual</th>
                <th style="width: 12%;" class="text-right">Variance</th>
                <th style="width: 10%;" class="text-right">Variance %</th>
                <th style="width: 11%;" class="text-center">Status</th>
            </tr>
        </thead>
        <tbody>
            {% for item in budget_analysis %}
            <tr>
                <td>
                    <div class="budget-item-name">{{ item.budget_item.name }}</div>
                    {% if item.budget_item.description %}
                    <div class="budget-description">{{ item.budget_item.description|truncatechars:40 }}</div>
                    {% endif %}
                </td>
                <td class="text-center">
                    {% if item.budget_item.budget_item_type == 'INCOME' %}
                    <span class="type-badge type-income">Income</span>
                    {% else %}
                    <span class="type-badge type-expense">Expense</span>
                    {% endif %}
                </td>
                <td>
                    {{ item.account_name }}
                    {% if item.account_code %}
                    <br><span class="account-code">{{ item.account_code }}</span>
                    {% endif %}
                </td>
                <td class="text-right">
                    <span class="{% if item.budget_item.budget_item_type == 'INCOME' %}income-amount{% else %}expense-amount{% endif %}">
                        {{ item.budgeted_amount|floatformat:2 }}
                    </span>
                </td>
                <td class="text-right">
                    <span class="{% if item.budget_item.budget_item_type == 'INCOME' %}income-amount{% else %}expense-amount{% endif %}">
                        {{ item.actual_amount|floatformat:2 }}
                    </span>
                </td>
                <td class="text-right">
                    <span class="{% if item.is_favorable %}favorable-variance{% else %}unfavorable-variance{% endif %}">
                        {% if item.variance_amount >= 0 %}+{% endif %}{{ item.variance_amount|floatformat:2 }}
                    </span>
                </td>
                <td class="text-right">
                    <span class="{% if item.is_favorable %}favorable-variance{% else %}unfavorable-variance{% endif %}">
                        {% if item.variance_percentage >= 0 %}+{% endif %}{{ item.variance_percentage|floatformat:1 }}%
                    </span>
                </td>
                <td class="text-center">
                    {% if item.is_favorable %}
                    <span class="status-favorable">Favorable</span>
                    {% else %}
                    <span class="status-unfavorable">Unfavorable</span>
                    {% endif %}
                </td>
            </tr>
            {% endfor %}
        </tbody>
        <tfoot>
            <tr class="totals-row">
                <td colspan="3" class="text-right"><strong>Totals:</strong></td>
                <td class="text-right">
                    <div>Income: {{ total_budgeted_income|floatformat:2 }}</div>
                    <div>Expense: {{ total_budgeted_expense|floatformat:2 }}</div>
                </td>
                <td class="text-right">
                    <div>Income: {{ total_actual_income|floatformat:2 }}</div>
                    <div>Expense: {{ total_actual_expense|floatformat:2 }}</div>
                </td>
                <td class="text-right">
                    <div>Income: {% if variance_summary.income_variance >= 0 %}+{% endif %}{{ variance_summary.income_variance|floatformat:2 }}</div>
                    <div>Expense: {% if variance_summary.expense_variance >= 0 %}+{% endif %}{{ variance_summary.expense_variance|floatformat:2 }}</div>
                </td>
                <td class="text-right">
                    <strong>Net: {% if variance_summary.net_variance >= 0 %}+{% endif %}{{ variance_summary.net_variance|floatformat:2 }}</strong>
                </td>
                <td></td>
            </tr>
        </tfoot>
    </table>
    {% endif %}

    <!-- Footer -->
    <div class="footer">
        <p>Generated on {{ "now"|date:"F d, Y \a\\t g:i A" }} | {{ tenant.name }} | Budget vs Actual Report</p>
        <p><em>Favorable variances: Income above budget, Expenses below budget. Unfavorable variances: Income below budget, Expenses above budget.</em></p>
    </div>
</body>
</html>
