# D:\school_fees_saas_v2\apps\public_site\forms.py
from django import forms

class SchoolFinderForm(forms.Form):
    query = forms.CharField(
        label="Enter School Name or Subdomain",
        max_length=100,
        required=True, # Make it required
        widget=forms.TextInput(attrs={'placeholder': 'e.g., Applewood or apple', 'class': 'form-control'})
    )
    
# apps/public_site/forms.py
from django import forms
from .models import ContactInquiry

class ContactForm(forms.ModelForm):
    class Meta:
        model = ContactInquiry
        fields = ['name', 'email', 'phone', 'organization', 'inquiry_type', 'subject', 'message']

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Add premium styling and attributes to all fields
        self.fields['name'].widget.attrs.update({
            'class': 'premium-input',
            'placeholder': ' ',
            'required': True
        })

        self.fields['email'].widget.attrs.update({
            'class': 'premium-input',
            'placeholder': ' ',
            'required': True
        })

        self.fields['phone'].widget.attrs.update({
            'class': 'premium-input',
            'placeholder': ' ',
        })

        self.fields['organization'].widget.attrs.update({
            'class': 'premium-input',
            'placeholder': ' ',
        })

        self.fields['inquiry_type'].widget.attrs.update({
            'class': 'premium-select',
        })

        self.fields['subject'].widget.attrs.update({
            'class': 'premium-input',
            'placeholder': ' ',
            'required': True
        })

        self.fields['message'].widget.attrs.update({
            'class': 'premium-textarea',
            'placeholder': ' ',
            'rows': 5,
            'required': True
        })

        # Update field labels
        self.fields['name'].label = 'Full Name'
        self.fields['email'].label = 'Email Address'
        self.fields['phone'].label = 'Phone Number'
        self.fields['organization'].label = 'School/Organization'
        self.fields['inquiry_type'].label = 'Inquiry Type'
        self.fields['subject'].label = 'Subject'
        self.fields['message'].label = 'Message'

        # Add help text
        self.fields['phone'].help_text = 'Optional - for urgent matters'
        self.fields['organization'].help_text = 'Optional - helps us provide better assistance'
        self.fields['message'].help_text = 'Please provide as much detail as possible'
    
    
# apps/public_site/forms.py (continued)
from .models import Testimonial # If you created the Testimonial model

class TestimonialSubmissionForm(forms.ModelForm):
    class Meta:
        model = Testimonial
        fields = ['author_name', 'author_title_school', 'quote', 'rating']
        widgets = {
            'author_name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Your Full Name'}),
            'author_title_school': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Your Title & School Name'}),
            'quote': forms.Textarea(attrs={'class': 'form-control', 'rows': 4, 'placeholder': 'Your testimonial...'}),
            'rating': forms.NumberInput(attrs={'class': 'form-control', 'min': 1, 'max': 5, 'placeholder': 'Rating (1-5)'}),
        }
