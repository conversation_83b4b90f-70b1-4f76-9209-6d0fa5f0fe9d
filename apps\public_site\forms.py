# D:\school_fees_saas_v2\apps\public_site\forms.py
from django import forms

class SchoolFinderForm(forms.Form):
    query = forms.CharField(
        label="Enter School Name or Subdomain",
        max_length=100,
        required=True, # Make it required
        widget=forms.TextInput(attrs={'placeholder': 'e.g., Applewood or apple', 'class': 'form-control'})
    )
    
# apps/public_site/forms.py
from django import forms
from .models import ContactInquiry

class ContactForm(forms.ModelForm):
    class Meta:
        model = ContactInquiry
        fields = ['name', 'email', 'phone', 'organization', 'inquiry_type', 'subject', 'message']

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Add premium styling and attributes to all fields
        self.fields['name'].widget.attrs.update({
            'class': 'premium-input',
            'placeholder': ' ',
            'required': True
        })

        self.fields['email'].widget.attrs.update({
            'class': 'premium-input',
            'placeholder': ' ',
            'required': True
        })

        self.fields['phone'].widget.attrs.update({
            'class': 'premium-input',
            'placeholder': ' ',
        })

        self.fields['organization'].widget.attrs.update({
            'class': 'premium-input',
            'placeholder': ' ',
        })

        self.fields['inquiry_type'].widget.attrs.update({
            'class': 'premium-select',
        })

        self.fields['subject'].widget.attrs.update({
            'class': 'premium-input',
            'placeholder': ' ',
            'required': True
        })

        self.fields['message'].widget.attrs.update({
            'class': 'premium-textarea',
            'placeholder': ' ',
            'rows': 5,
            'required': True
        })

        # Update field labels
        self.fields['name'].label = 'Full Name'
        self.fields['email'].label = 'Email Address'
        self.fields['phone'].label = 'Phone Number'
        self.fields['organization'].label = 'School/Organization'
        self.fields['inquiry_type'].label = 'Inquiry Type'
        self.fields['subject'].label = 'Subject'
        self.fields['message'].label = 'Message'

        # Add help text
        self.fields['phone'].help_text = 'Optional - for urgent matters'
        self.fields['organization'].help_text = 'Optional - helps us provide better assistance'
        self.fields['message'].help_text = 'Please provide as much detail as possible'
    
    
# apps/public_site/forms.py (continued)
from .models import Testimonial # If you created the Testimonial model

class TestimonialSubmissionForm(forms.ModelForm):
    class Meta:
        model = Testimonial
        fields = [
            'author_name', 'author_title', 'school_name', 'school_type', 'school_size', 'location',
            'email', 'phone', 'website', 'review_category', 'title', 'quote',
            'overall_rating', 'ease_of_use_rating', 'customer_support_rating', 'value_rating',
            'usage_duration', 'previous_solution', 'would_recommend'
        ]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Add premium styling and attributes to all fields
        self.fields['author_name'].widget.attrs.update({
            'class': 'premium-input',
            'placeholder': ' ',
            'required': True
        })

        self.fields['author_title'].widget.attrs.update({
            'class': 'premium-input',
            'placeholder': ' ',
            'required': True
        })

        self.fields['school_name'].widget.attrs.update({
            'class': 'premium-input',
            'placeholder': ' ',
            'required': True
        })

        self.fields['school_type'].widget.attrs.update({
            'class': 'premium-select',
        })

        self.fields['school_size'].widget.attrs.update({
            'class': 'premium-select',
        })

        self.fields['location'].widget.attrs.update({
            'class': 'premium-input',
            'placeholder': ' ',
        })

        self.fields['email'].widget.attrs.update({
            'class': 'premium-input',
            'placeholder': ' ',
        })

        self.fields['phone'].widget.attrs.update({
            'class': 'premium-input',
            'placeholder': ' ',
        })

        self.fields['website'].widget.attrs.update({
            'class': 'premium-input',
            'placeholder': ' ',
        })

        self.fields['review_category'].widget.attrs.update({
            'class': 'premium-select',
        })

        self.fields['title'].widget.attrs.update({
            'class': 'premium-input',
            'placeholder': ' ',
            'required': True
        })

        self.fields['quote'].widget.attrs.update({
            'class': 'premium-textarea',
            'placeholder': ' ',
            'rows': 5,
            'required': True
        })

        # Rating fields
        for field_name in ['overall_rating', 'ease_of_use_rating', 'customer_support_rating', 'value_rating']:
            self.fields[field_name].widget.attrs.update({
                'class': 'premium-rating',
                'min': 1,
                'max': 5,
                'step': 1
            })

        self.fields['usage_duration'].widget.attrs.update({
            'class': 'premium-input',
            'placeholder': ' ',
        })

        self.fields['previous_solution'].widget.attrs.update({
            'class': 'premium-input',
            'placeholder': ' ',
        })

        self.fields['would_recommend'].widget.attrs.update({
            'class': 'premium-checkbox',
        })

        # Update field labels and help text
        self.fields['author_name'].label = 'Full Name'
        self.fields['author_title'].label = 'Job Title'
        self.fields['school_name'].label = 'School/Organization Name'
        self.fields['school_type'].label = 'School Type'
        self.fields['school_size'].label = 'School Size'
        self.fields['location'].label = 'Location'
        self.fields['email'].label = 'Email Address'
        self.fields['phone'].label = 'Phone Number'
        self.fields['website'].label = 'School Website'
        self.fields['review_category'].label = 'Review Category'
        self.fields['title'].label = 'Review Title'
        self.fields['quote'].label = 'Your Review'
        self.fields['overall_rating'].label = 'Overall Rating'
        self.fields['ease_of_use_rating'].label = 'Ease of Use'
        self.fields['customer_support_rating'].label = 'Customer Support'
        self.fields['value_rating'].label = 'Value for Money'
        self.fields['usage_duration'].label = 'Usage Duration'
        self.fields['previous_solution'].label = 'Previous Solution'
        self.fields['would_recommend'].label = 'Would you recommend our platform?'

        # Add help text
        self.fields['location'].help_text = 'City, State/Country (optional)'
        self.fields['email'].help_text = 'Optional - for follow-up questions'
        self.fields['phone'].help_text = 'Optional phone number'
        self.fields['website'].help_text = 'School website (optional)'
        self.fields['title'].help_text = 'Brief title summarizing your experience'
        self.fields['quote'].help_text = 'Share your detailed experience with our platform'
        self.fields['usage_duration'].help_text = 'e.g., 6 months, 2 years'
        self.fields['previous_solution'].help_text = 'What did you use before? (optional)'
