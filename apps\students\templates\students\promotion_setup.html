{% extends "base.html" %}
{% load i18n crispy_forms_tags %}

{% block title %}{{ page_title }}{% endblock %}

{% block content %}
<div class="container-fluid">

    <!-- Page Heading -->
    <h1 class="h3 mb-4 text-gray-800">{{ page_title }}</h1>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">{% trans "Step 1: Select Promotion Context" %}</h6>
        </div>
        <div class="card-body">
            <p>
                {% trans "Please select the academic years and the class you wish to promote. This will load all students from the selected class for you to review in the next step." %}
            </p>
            <hr>
            
            <form method="post" novalidate>
                {% csrf_token %}
                
                <!-- Display non-field errors (e.g., the 'years cannot be the same' error) -->
                {% if form.non_field_errors %}
                    <div class="alert alert-danger">
                        {% for error in form.non_field_errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                {% endif %}

                <div class="row">
                    <div class="col-md-4 mb-3">
                        {{ form.from_academic_year|as_crispy_field }}
                    </div>
                    <div class="col-md-4 mb-3">
                        {{ form.to_academic_year|as_crispy_field }}
                    </div>
                    <div class="col-md-4 mb-3">
                        {{ form.class_to_promote|as_crispy_field }}
                    </div>
                </div>

                <div class="mt-4 d-flex justify-content-end">
                    <button type="submit" class="btn btn-primary btn-lg">
                        {% trans "Proceed to Review Students" %} →
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock content %}



