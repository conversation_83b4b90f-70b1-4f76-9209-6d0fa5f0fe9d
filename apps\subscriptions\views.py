# D:\school_fees_saas_v2\apps\subscriptions\views.py
from django.shortcuts import render, redirect, get_object_or_404
from django.http import HttpResponse, Http404, JsonResponse # Added JsonResponse for potential JS-based checkout
from django.views.generic import TemplateView, FormView, View, ListView
from django.urls import reverse, reverse_lazy
from django.contrib.auth.mixins import LoginRequiredMixin, UserPassesTestMixin
from django.contrib.auth.decorators import login_required, user_passes_test
from django.contrib import messages
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from django.conf import settings
from django_tenants.utils import get_public_schema_name, schema_context
from django.views.decorators.csrf import csrf_exempt
from decimal import Decimal
from datetime import timedelta
import uuid # For generating mock transaction IDs

from .models import SubscriptionPlan, Subscription, Feature
from .forms import PlanCycleSelectionForm
from apps.common.mixins import TenantLoginRequiredMixin, TenantPermissionRequiredMixin # ADJUST AS NEEDED
from apps.schools.models import SchoolProfile
# from apps.communication.tasks import send_subscription_activated_email_task # For actual notifications

import logging
logger = logging.getLogger(__name__)

# --- Helper for Platform Admin Decorator/Mixin ---
def is_platform_admin_check(user):
    return user.is_authenticated and user.is_staff and user.is_superuser

# --- Base View for Tenant-Context Subscription Management ---
class SubscriptionManagementBaseView(TenantLoginRequiredMixin, UserPassesTestMixin, View):
    permission_to_manage_subscription = 'subscriptions.change_subscription' # Example
    login_url = reverse_lazy('schools:staff_login')

    def test_func(self):
        # ... (your existing test_func logic) ...
        # For brevity, assuming it's correctly implemented
        if not self.request.user.is_authenticated: return False
        self.current_tenant = getattr(self.request, 'tenant', None)
        if not self.current_tenant or self.current_tenant.schema_name == get_public_schema_name(): return False
        # Add your actual permission check here, e.g., is owner or has specific staff role
        return hasattr(self.request.user, 'is_staff_member') and self.request.user.is_staff_member


    def handle_no_permission(self):
        messages.error(self.request, _("You do not have permission to manage this school's subscription."))
        return redirect(reverse_lazy('schools:dashboard'))

    def dispatch(self, request, *args, **kwargs):
        self.current_tenant = getattr(request, 'tenant', None)
        if not self.current_tenant or self.current_tenant.schema_name == get_public_schema_name():
            messages.error(request, _("Subscription management is only available within your school's portal."))
            return redirect('public_site:home')
        return super().dispatch(request, *args, **kwargs)

    def get_common_context_data(self, **kwargs):
        # ... (your existing get_common_context_data logic) ...
        context = {}
        context['tenant'] = self.current_tenant
        try:
            context['subscription'] = self.current_tenant.subscription
        except Subscription.DoesNotExist:
            context['subscription'] = None
        
        effective_currency_symbol = getattr(settings, 'PLATFORM_DEFAULT_CURRENCY_SYMBOL', '$')
        with schema_context(self.current_tenant.schema_name):
            school_profile = SchoolProfile.objects.first()
            if school_profile and school_profile.currency_symbol:
                effective_currency_symbol = school_profile.currency_symbol
        context['school_currency_symbol'] = effective_currency_symbol
        return context


# --- Public Pricing Page ---
def pricing_page_view(request):
    # ... (your existing pricing_page_view logic from previous response - it's good) ...
    view_title = _("Our Plans & Pricing")
    plans_data = []
    try:
        raw_plans = SubscriptionPlan.objects.filter(is_active=True, is_public=True)\
            .prefetch_related('features').order_by('display_order', 'price_monthly')
        if not raw_plans.exists(): logger.warning("Pricing page: No active/public plans.")
        
        for plan in raw_plans:
            monthly_price = plan.price_monthly if plan.price_monthly else Decimal('0.00')
            annual_price = plan.price_annually if plan.price_annually else Decimal('0.00')
            annual_equivalent_if_monthly = monthly_price * 12
            savings_if_annual = Decimal('0.00'); show_savings = False
            if annual_price > 0 and monthly_price > 0 and annual_price < annual_equivalent_if_monthly:
                savings_if_annual = annual_equivalent_if_monthly - annual_price
                show_savings = True
            plans_data.append({
                'instance': plan, 'annual_equivalent_if_monthly': annual_equivalent_if_monthly,
                'savings_if_annual': savings_if_annual, 'show_savings': show_savings
            })
    except Exception as e: logger.error(f"Error fetching plans for pricing page: {e}", exc_info=True)
    
    current_tenant_subscription_plan_pk = None; tenant_can_change_plan = False; current_tenant_name_for_cta = None
    current_tenant = getattr(request, 'tenant', None)
    if request.user.is_authenticated and current_tenant and current_tenant.schema_name != get_public_schema_name():
        current_tenant_name_for_cta = current_tenant.name
        try:
            tenant_subscription = current_tenant.subscription
            if tenant_subscription and tenant_subscription.plan:
                current_tenant_subscription_plan_pk = tenant_subscription.plan.pk
            tenant_can_change_plan = True
        except (Subscription.DoesNotExist, AttributeError): tenant_can_change_plan = True
    
    context = {
        'view_title': view_title, 'plans_data': plans_data,
        'current_tenant_subscription_plan_pk': current_tenant_subscription_plan_pk,
        'tenant_can_change_plan': tenant_can_change_plan,
        'platform_currency_symbol': getattr(settings, 'PLATFORM_DEFAULT_CURRENCY_SYMBOL', '$'),
        'current_tenant_name_for_cta': current_tenant_name_for_cta,
    }
    return render(request, 'subscriptions/pricing_page.html', context)

# --- Tenant-Specific Subscription Views ---
class SubscriptionDetailsView(SubscriptionManagementBaseView, TemplateView):
    # ... (your existing SubscriptionDetailsView logic - it's good) ...
    template_name = 'subscriptions/subscription_details.html'
    permission_to_manage_subscription = 'subscriptions.view_subscription'

    def get_context_data(self, **kwargs):
        context = super().get_common_context_data(**kwargs)
        context['view_title'] = _("Your School's Subscription")
        if not context.get('subscription'):
            messages.info(self.request, _("Your school does not have an active subscription. Please select a plan."))
        return context

class PlanSelectionView(SubscriptionManagementBaseView, ListView):
    # ... (your existing PlanSelectionView logic - it's good, ensure context_object_name matches template) ...
    model = SubscriptionPlan
    template_name = 'subscriptions/plan_selection.html'
    context_object_name = 'plans_data_list' 
    permission_to_manage_subscription = 'subscriptions.change_subscription'

    def get_queryset(self):
        return SubscriptionPlan.objects.filter(is_active=True, is_public=True)\
            .prefetch_related('features').order_by('display_order', 'price_monthly')

    def get_context_data(self, **kwargs):
        context = super().get_common_context_data(**kwargs)
        list_view_context = super(SubscriptionManagementBaseView, self).get_context_data(**kwargs) # Call ListView's get_context_data
        context.update(list_view_context)
        context['view_title'] = _("Choose or Change Your Subscription Plan")
        if context.get('subscription') and context['subscription'].plan:
             context['current_plan_pk'] = context['subscription'].plan.pk
        else:
            context['current_plan_pk'] = None
        processed_plans_data = []
        plans_from_queryset = context.get(self.context_object_name) or context.get('object_list')
        if plans_from_queryset:
            for plan_instance in plans_from_queryset:
                monthly_price = plan_instance.price_monthly or Decimal('0.00')
                annual_price = plan_instance.price_annually or Decimal('0.00')
                annual_equivalent_if_monthly = monthly_price * 12
                savings_if_annual = Decimal('0.00'); show_savings = False
                if annual_price > 0 and monthly_price > 0 and annual_price < annual_equivalent_if_monthly:
                    savings_if_annual = annual_equivalent_if_monthly - annual_price
                    show_savings = True
                processed_plans_data.append({
                    'instance': plan_instance, 'annual_equivalent_if_monthly': annual_equivalent_if_monthly,
                    'savings_if_annual': savings_if_annual, 'show_savings': show_savings
                })
        context[self.context_object_name] = processed_plans_data
        return context


class InitiateCheckoutView(SubscriptionManagementBaseView, FormView):
    template_name = 'subscriptions/initiate_checkout.html'
    form_class = PlanCycleSelectionForm
    permission_to_manage_subscription = 'subscriptions.change_subscription'

    def setup(self, request, *args, **kwargs):
        super().setup(request, *args, **kwargs)
        try:
            self.selected_plan = get_object_or_404(SubscriptionPlan, slug=self.kwargs['plan_slug'], is_active=True)
        except Http404:
            self.selected_plan = None # Will be handled in dispatch

    def dispatch(self, request, *args, **kwargs):
        response = super().dispatch(request, *args, **kwargs) # Base class dispatch (auth, tenant context)
        if hasattr(response, 'status_code') and response.status_code >= 300: return response
        
        if not self.selected_plan:
            messages.error(request, _("The selected plan is invalid or no longer available."))
            return redirect(reverse_lazy('subscriptions:select_plan'))
        return response

    def get_initial(self):
        initial = super().get_initial()
        initial['billing_cycle'] = self.kwargs.get('billing_cycle', self.request.GET.get('billing_cycle', 'MONTHLY')).upper()
        return initial

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['plan'] = self.selected_plan
        return kwargs

    def form_valid(self, form):
        selected_billing_cycle = form.cleaned_data['billing_cycle']
        tenant = self.current_tenant
        plan = self.selected_plan

        logger.info(f"Checkout initiated for tenant '{tenant.name}', plan '{plan.name}', cycle '{selected_billing_cycle}'.")

        # --- PAYMENT GATEWAY INTEGRATION POINT / SIMULATION ---
        
        # Prepare success and cancel URLs for the gateway (or our simulation)
        # These must be absolute URLs for external gateways
        success_url = self.request.build_absolute_uri(reverse(settings.PAYMENT_SUCCESS_URL_NAME))
        cancel_url = self.request.build_absolute_uri(reverse(settings.PAYMENT_CANCEL_URL_NAME))

        # 1. Create or Update Subscription Record to PENDING
        #    (or update if changing plan for an existing subscription)
        current_subscription = getattr(tenant, 'subscription', None)
        price_for_this_cycle = plan.price_monthly if selected_billing_cycle == 'MONTHLY' else plan.price_annually

        defaults_for_subscription = {
            'plan': plan,
            'billing_cycle': selected_billing_cycle,
            'price_at_subscription': price_for_this_cycle,
            'status': 'PENDING', # Always PENDING before payment/trial activation
            'trial_start_date': None, 'trial_end_date': None,
            'current_period_start': None, 'current_period_end': None,
        }
        if current_subscription:
            defaults_for_subscription['pg_customer_id'] = current_subscription.pg_customer_id # Preserve customer ID
            # pg_subscription_id might be new if plan changes, gateway dependent
            # defaults_for_subscription['pg_subscription_id'] = None # Clear old one for now
        
        subscription, created_sub_record = Subscription.objects.update_or_create(
            school=tenant,
            defaults=defaults_for_subscription
        )
        # Store subscription ID in session to retrieve it on success/cancel pages
        self.request.session['pending_subscription_id'] = subscription.id
        self.request.session['pending_plan_name'] = plan.name
        self.request.session['pending_trial_days'] = plan.trial_period_days


        if settings.SIMULATE_PAYMENTS:
            logger.info(f"SIMULATING payment gateway for Sub ID: {subscription.id}, Plan: {plan.name}")
            # Simulate immediate "payment" or trial start if SIMULATE_PAYMENTS is True
            if plan.trial_period_days > 0 and (created_sub_record or not current_subscription or current_subscription.plan != plan):
                subscription.status = 'TRIALING'
                subscription.trial_start_date = timezone.now()
                subscription.trial_end_date = timezone.now() + timedelta(days=plan.trial_period_days)
                subscription.current_period_start = subscription.trial_start_date
                subscription.current_period_end = subscription.trial_end_date
                subscription.save()
                logger.info(f"Simulated trial activation for Sub ID: {subscription.id}. Trial ends {subscription.trial_end_date}")
            elif not settings.REQUIRE_PAYMENT_FOR_ACTIVATION: # If no trial and not requiring real payment for activation
                subscription.status = 'ACTIVE'
                subscription.current_period_start = timezone.now()
                days_in_cycle = settings.SUBSCRIPTION_MONTH_DAYS if selected_billing_cycle == 'MONTHLY' else settings.SUBSCRIPTION_YEAR_DAYS
                subscription.current_period_end = timezone.now() + timedelta(days=days_in_cycle)
                subscription.save()
                logger.info(f"Simulated direct activation for Sub ID: {subscription.id}. Period ends {subscription.current_period_end}")
            else: # Requires "payment" but we are simulating - leave as PENDING, admin can approve
                logger.info(f"Simulated payment - Sub ID: {subscription.id} remains PENDING for plan '{plan.name}'. Manual approval or webhook needed.")
                subscription.save() # Ensure pending status is saved

            # In simulation, we redirect to our own success URL directly
            return redirect(success_url) 
        else:
            # --- REAL PAYMENT GATEWAY LOGIC ---
            logger.info(f"Attempting REAL payment gateway integration for Sub ID: {subscription.id}, Plan: {plan.name}")
            # Example with Stripe (conceptual, requires stripe library and setup)
            # try:
            #     import stripe
            #     stripe.api_key = settings.STRIPE_SECRET_KEY
            #     line_items = [{'price': plan.pg_price_id_monthly if selected_billing_cycle == 'MONTHLY' else plan.pg_price_id_annually, 'quantity': 1}]
                
            #     checkout_session_params = {
            #         'payment_method_types': ['card'],
            #         'line_items': line_items,
            #         'mode': 'subscription',
            #         'success_url': success_url + '?session_id={CHECKOUT_SESSION_ID}', # Stripe appends session_id
            #         'cancel_url': cancel_url,
            #         'metadata': {
            #             'django_subscription_id': subscription.id,
            #             'tenant_schema_name': tenant.schema_name,
            #             'plan_id': plan.id,
            #             'billing_cycle': selected_billing_cycle,
            #         }
            #     }
            #     # Add customer if exists, or allow Stripe to create one
            #     if subscription.pg_customer_id:
            #         checkout_session_params['customer'] = subscription.pg_customer_id
            #     else: # Create customer or pass email for Stripe to handle
            #         checkout_session_params['customer_email'] = tenant.owner.email # Assuming tenant has owner with email

            #     if plan.trial_period_days > 0 and (created_sub_record or not current_subscription or current_subscription.plan != plan):
            #         checkout_session_params['subscription_data'] = {
            #             'trial_period_days': plan.trial_period_days
            #         }
                
            #     checkout_session = stripe.checkout.Session.create(**checkout_session_params)
            #     subscription.pg_checkout_session_id = checkout_session.id # Optional: store session id
            #     subscription.save()
            #     return redirect(checkout_session.url, status=303)

            # except Exception as e:
            #     logger.error(f"Payment Gateway Error for tenant {tenant.name}, plan {plan.name}: {e}", exc_info=True)
            #     messages.error(self.request, _("There was an issue connecting to the payment gateway. Please try again."))
            #     return self.form_invalid(form) # Rerender the form page
            messages.error(self.request, _("Real payment gateway integration is not yet implemented."))
            return self.form_invalid(form)


    def get_context_data(self, **kwargs):
        context = super().get_common_context_data(**kwargs)
        context.update(super(SubscriptionManagementBaseView, self).get_context_data(**kwargs))
        
        context['view_title'] = _("Confirm Subscription: %(plan_name)s") % {'plan_name': self.selected_plan.name}
        context['plan'] = self.selected_plan
        
        initial_cycle = self.get_initial().get('billing_cycle','MONTHLY')
        if initial_cycle == 'ANNUALLY' and self.selected_plan.price_annually is not None and self.selected_plan.price_annually > 0 :
            context['display_price'] = self.selected_plan.price_annually
            context['display_cycle_name'] = dict(SubscriptionPlan.BILLING_CYCLE_CHOICES).get('ANNUALLY', 'Annual')
        else:
            context['display_price'] = self.selected_plan.price_monthly
            context['display_cycle_name'] = dict(SubscriptionPlan.BILLING_CYCLE_CHOICES).get('MONTHLY', 'Monthly')
        return context


class PaymentSuccessView(SubscriptionManagementBaseView, TemplateView):
    template_name = 'subscriptions/payment_success.html'
    permission_to_manage_subscription = None # Allow any authenticated tenant user who just "paid"

    def get_context_data(self, **kwargs):
        context = super().get_common_context_data(**kwargs) # Gets current subscription
        
        # For real gateway, you'd verify session_id from query params here
        # session_id = self.request.GET.get('session_id')
        # if session_id and not settings.SIMULATE_PAYMENTS:
        #    # Retrieve checkout session from Stripe, verify payment, update subscription
        #    # This logic is often better handled by a webhook for reliability.
        #    pass

        pending_subscription_id = self.request.session.pop('pending_subscription_id', None)
        pending_plan_name = self.request.session.pop('pending_plan_name', _("your selected plan"))
        pending_trial_days = self.request.session.pop('pending_trial_days', 0)

        if pending_subscription_id:
            try:
                # Re-fetch the subscription to get its latest status after form_valid logic
                updated_subscription = Subscription.objects.get(pk=pending_subscription_id, school=self.current_tenant)
                context['subscription'] = updated_subscription # Override common context one
                
                if updated_subscription.status == 'TRIALING':
                    context['view_title'] = _("Free Trial Activated!")
                    messages.success(self.request, _("Your %(days)s-day free trial for the %(plan)s plan has started successfully!") % {'days': updated_subscription.plan.trial_period_days, 'plan': updated_subscription.plan.name})
                elif updated_subscription.status == 'ACTIVE':
                    context['view_title'] = _("Subscription Activated!")
                    messages.success(self.request, _("Your subscription to the %(plan)s plan is now active!") % {'plan': updated_subscription.plan.name})
                elif updated_subscription.status == 'PENDING':
                    context['view_title'] = _("Subscription Pending")
                    messages.info(self.request, _("Your subscription to the %(plan)s plan is currently pending. It will be activated upon payment confirmation or administrative approval.") % {'plan': updated_subscription.plan.name})
                else: # Other statuses
                    context['view_title'] = _("Subscription Status Updated")
                    messages.info(self.request, _("Your subscription status is currently: %(status)s") % {'status': updated_subscription.get_status_display()})

            except Subscription.DoesNotExist:
                 logger.error(f"PaymentSuccessView: Subscription ID {pending_subscription_id} from session not found for tenant {self.current_tenant.name}")
                 messages.error(self.request, _("There was an issue confirming your subscription details. Please check 'My Subscription' or contact support."))
                 context['view_title'] = _("Subscription Confirmation")
        else: # Direct access or session lost
            logger.warning(f"PaymentSuccessView accessed without pending_subscription_id in session for tenant {self.current_tenant.name}")
            messages.info(self.request, _("Subscription process completed. You can view your subscription details below."))
            context['view_title'] = _("Subscription Status") # Generic title

        return context


class PaymentCancelView(SubscriptionManagementBaseView, TemplateView):
    template_name = 'subscriptions/payment_cancel.html'
    permission_to_manage_subscription = None # Allow any authenticated tenant user

    def get_context_data(self, **kwargs):
        context = super().get_common_context_data(**kwargs)
        context['view_title'] = _("Subscription Process Cancelled")
        
        # Clear pending session variables if any
        self.request.session.pop('pending_subscription_id', None)
        self.request.session.pop('pending_plan_name', None)
        self.request.session.pop('pending_trial_days', None)

        if not messages.get_messages(self.request): # Avoid duplicate if message already set
            messages.warning(self.request, _("Your subscription process was cancelled or not completed. No changes were made to your current subscription. You can select a plan and try again anytime."))
        return context


# --- Admin Manual Approval View ---
@user_passes_test(is_platform_admin_check, login_url=reverse_lazy('admin:login'))
def admin_manual_approve_subscription_view(request, subscription_pk):
    # ... (Your existing detailed logic for this view is good) ...
    subscription = get_object_or_404(Subscription, pk=subscription_pk)
    tenant_public_model_instance = subscription.school
    
    if request.method == 'POST':
        # ... (your POST logic for activation) ...
        # Ensure you call subscription.save() after making changes.
        # Example snippet from your code:
        if subscription.status in ['PENDING', 'TRIALING', 'PAST_DUE', 'UNPAID', 'INCOMPLETE']:
            original_status = subscription.status
            subscription.status = 'ACTIVE'
            now = timezone.now()
            subscription.current_period_start = now
            days_in_cycle = settings.SUBSCRIPTION_MONTH_DAYS if subscription.billing_cycle == 'MONTHLY' else settings.SUBSCRIPTION_YEAR_DAYS
            subscription.current_period_end = now + timedelta(days=days_in_cycle)
            if original_status == 'TRIALING': subscription.trial_start_date = None; subscription.trial_end_date = None
            subscription.save()
            # ... (activate tenant, log audit, send email) ...
            messages.success(request, _("Subscription for '%(school_name)s' manually activated.") % {'school_name': tenant_public_model_instance.name})
        else:
            messages.warning(request, _("Subscription (status: %(status)s) cannot be manually activated.") % {'status': subscription.get_status_display()})
        return redirect(reverse('admin:subscriptions_subscription_change', args=[subscription.pk]))

    context = {
        'view_title': _("Manually Approve Subscription: ") + tenant_public_model_instance.name,
        'subscription': subscription,
        'tenant_public_model_instance': tenant_public_model_instance, # For display
    }
    return render(request, 'subscriptions/admin_manual_approve_confirm.html', context)


# --- Webhook and other placeholders ---
@login_required
def update_payment_method_view(request):
    # ... (your existing placeholder, redirect to subscription_details) ...
    current_tenant = getattr(request, 'tenant', None)
    if not current_tenant or current_tenant.schema_name == get_public_schema_name():
        messages.error(request, _("Access denied."))
        return redirect('public_site:home')
    messages.info(request, _("Payment method update via Stripe Billing Portal (placeholder)."))
    return redirect('subscriptions:subscription_details')


@login_required
def manage_subscription_redirect_view(request):
    # ... (your existing placeholder, redirect to subscription_details) ...
    current_tenant = getattr(request, 'tenant', None)
    if not current_tenant or current_tenant.schema_name == get_public_schema_name():
        return redirect('public_site:home')
    return redirect('subscriptions:subscription_details')



# @csrf_exempt
# def payment_gateway_webhook_view(request):
#     # ... (your existing placeholder) ...
#     if request.method == 'POST':
#         logger.info(f"Webhook received. Body (first 200 chars): {request.body.decode('utf-8', errors='ignore')[:200]}")
#         return HttpResponse(status=200)
#     return HttpResponse("Webhook endpoint. POST requests only.", status=405)


@csrf_exempt
def payment_gateway_webhook_view(request):
    if request.method == 'POST':
        payload = request.body.decode('utf-8') # Decode body for logging
        sig_header = request.META.get('HTTP_STRIPE_SIGNATURE') # Example for Stripe
        endpoint_secret = getattr(settings, 'STRIPE_WEBHOOK_SIGNING_SECRET', None)
        
        logger.info(f"Webhook received. Signature: {sig_header}. Payload (first 500 chars): {payload[:500]}")

        # Real implementation would verify signature and process event types
        # For now, just acknowledge
        return HttpResponse(status=200)
        
    logger.warning("Webhook endpoint accessed with non-POST method.")
    return HttpResponse("Webhook endpoint. POST requests only.", status=405)




@login_required # Basic login check
def subscription_checkout_success_view(request): # <<< MATCHES URLS.PY
    # You'll need to get current_tenant and subscription here if needed by the template
    current_tenant = getattr(request, 'tenant', None)
    subscription_instance = None
    effective_currency_symbol = getattr(settings, 'PLATFORM_DEFAULT_CURRENCY_SYMBOL', '$')

    if current_tenant and current_tenant.schema_name != get_public_schema_name():
        try:
            subscription_instance = current_tenant.subscription
            with schema_context(current_tenant.schema_name):
                from apps.schools.models import SchoolProfile
                school_profile = SchoolProfile.objects.first()
                if school_profile and school_profile.currency_symbol:
                    effective_currency_symbol = school_profile.currency_symbol
        except Subscription.DoesNotExist:
            messages.error(request, _("Could not retrieve your updated subscription details."))
        except Exception as e:
            logger.error(f"Error in subscription_checkout_success_view for tenant {current_tenant.name}: {e}")
            messages.error(request, _("An error occurred."))

    context = {
        'view_title': _("Subscription Confirmed!"),
        'subscription': subscription_instance,
        'tenant': current_tenant,
        'school_currency_symbol': effective_currency_symbol,
    }
    
    if subscription_instance and subscription_instance.status not in ['ACTIVE', 'TRIALING']:
        messages.warning(request, _("Your subscription status is '%(status)s'. Activation may take a few moments or require further steps.") % {'status': subscription_instance.get_status_display()})
    elif not subscription_instance and current_tenant:
        messages.error(request, _("Could not retrieve your updated subscription details. Please check 'My Subscription' or contact support."))


    return render(request, 'subscriptions/payment_success.html', context)




###############################################################################
#
#
#
###############################################################################


class PaymentSuccessView(SubscriptionManagementBaseView, TemplateView):
    template_name = 'subscriptions/payment_success.html'
    permission_to_manage_subscription = 'subscriptions.view_subscription' # Or allow any authenticated tenant user

    def get_context_data(self, **kwargs):
        context = super().get_common_context_data(**kwargs)
        context['view_title'] = _("Subscription Confirmed!")
        if not context.get('subscription'):
            messages.error(self.request, _("Could not retrieve your updated subscription details. Please check 'My Subscription' or contact support."))
        elif context['subscription'].status not in ['ACTIVE', 'TRIALING']:
            messages.warning(self.request, _("Your subscription status is '%(status)s'. Activation may take a few moments or require further steps.") % {'status': context['subscription'].get_status_display()})
        return context




# D:\school_fees_saas_v2\apps\subscriptions\views.py

import requests
import json
import hmac
import hashlib
import logging
from django.conf import settings
from django.http import HttpResponse, HttpResponseForbidden
from django.shortcuts import get_object_or_404, redirect
from django.urls import reverse
from django.utils import timezone
from django.views import View
from django.views.decorators.csrf import csrf_exempt
from django.db import transaction
from django.contrib import messages

from .models import Subscription, SubscriptionPlan

logger = logging.getLogger(__name__)

# --- ADD THIS NEW VIEW CLASS ---

class InitiateSubscriptionPaymentView(View):
    """
    Handles the POST request from the "Confirm & Pay" page.
    Creates a pending transaction with the payment gateway and redirects the user.
    """
    def post(self, request, *args, **kwargs):
        subscription_pk = self.kwargs.get('subscription_pk')
        subscription = get_object_or_404(
            Subscription.objects.select_related('plan', 'school__owner'), 
            pk=subscription_pk, 
            status='INCOMPLETE'
        )
        plan = subscription.plan
        owner = subscription.school.owner

        # --- Initiate Transaction with Paystack (Example) ---
        url = "https://api.paystack.co/transaction/initialize"
        headers = {
            "Authorization": f"Bearer {settings.PAYSTACK_SECRET_KEY}",
            "Content-Type": "application/json",
        }
        
        # The callback URL Paystack will redirect to after payment.
        # We rely on webhooks for the actual activation, so this can be a simple "thank you" page.
        callback_url = request.build_absolute_uri(reverse('tenants:registration_pending')) # A new page to show "check your email"
        
        # A unique reference for this transaction attempt
        reference = f"SUB_{subscription.pk}_{int(timezone.now().timestamp())}"

        payload = {
            "email": owner.email,
            "amount": int(plan.price_monthly * 100),  # Paystack expects amount in kobo
            "reference": reference,
            "callback_url": callback_url,
            "metadata": {
                "subscription_id": subscription.pk,
                "school_schema": subscription.school.schema_name,
            }
        }

        try:
            response = requests.post(url, headers=headers, json=payload)
            response.raise_for_status() # Raise an exception for bad status codes (4xx or 5xx)
            response_data = response.json()

            if response_data.get("status"):
                # Redirect the user to Paystack's secure checkout page
                return redirect(response_data['data']['authorization_url'])
            else:
                logger.error(f"Paystack initialization failed: {response_data.get('message')}")
                messages.error(request, "We couldn't connect to the payment service. Please try again later.")
                return redirect('tenants:confirm_and_pay', subscription_id=subscription.pk)

        except requests.exceptions.RequestException as e:
            logger.error(f"Paystack API request failed: {e}")
            messages.error(request, "There was a network error connecting to the payment service. Please try again.")
            return redirect('tenants:confirm_and_pay', subscription_id=subscription.pk)


# D:\school_fees_saas_v2\apps\subscriptions\views.py

import requests
import json
import hmac
import hashlib
import logging
from django.conf import settings
from django.http import HttpResponse, HttpResponseForbidden
from django.shortcuts import get_object_or_404, redirect
from django.urls import reverse
from django.utils import timezone
from django.views import View
from django.views.decorators.csrf import csrf_exempt
from django.db import transaction
from django.contrib import messages

from .models import Subscription, SubscriptionPlan

logger = logging.getLogger(__name__)

# --- ADD THIS NEW VIEW CLASS ---

class InitiateSubscriptionPaymentView(View):
    """
    Handles the POST request from the "Confirm & Pay" page.
    Creates a pending transaction with the payment gateway and redirects the user.
    """
    def post(self, request, *args, **kwargs):
        subscription_pk = self.kwargs.get('subscription_pk')
        subscription = get_object_or_404(
            Subscription.objects.select_related('plan', 'school__owner'), 
            pk=subscription_pk, 
            status='INCOMPLETE'
        )
        plan = subscription.plan
        owner = subscription.school.owner

        # --- Initiate Transaction with Paystack (Example) ---
        url = "https://api.paystack.co/transaction/initialize"
        headers = {
            "Authorization": f"Bearer {settings.PAYSTACK_SECRET_KEY}",
            "Content-Type": "application/json",
        }
        
        # The callback URL Paystack will redirect to after payment.
        # We rely on webhooks for the actual activation, so this can be a simple "thank you" page.
        callback_url = request.build_absolute_uri(reverse('tenants:registration_pending')) # A new page to show "check your email"
        
        # A unique reference for this transaction attempt
        reference = f"SUB_{subscription.pk}_{int(timezone.now().timestamp())}"

        payload = {
            "email": owner.email,
            "amount": int(plan.price_monthly * 100),  # Paystack expects amount in kobo
            "reference": reference,
            "callback_url": callback_url,
            "metadata": {
                "subscription_id": subscription.pk,
                "school_schema": subscription.school.schema_name,
            }
        }

        try:
            response = requests.post(url, headers=headers, json=payload)
            response.raise_for_status() # Raise an exception for bad status codes (4xx or 5xx)
            response_data = response.json()

            if response_data.get("status"):
                # Redirect the user to Paystack's secure checkout page
                return redirect(response_data['data']['authorization_url'])
            else:
                logger.error(f"Paystack initialization failed: {response_data.get('message')}")
                messages.error(request, "We couldn't connect to the payment service. Please try again later.")
                return redirect('tenants:confirm_and_pay', subscription_id=subscription.pk)

        except requests.exceptions.RequestException as e:
            logger.error(f"Paystack API request failed: {e}")
            messages.error(request, "There was a network error connecting to the payment service. Please try again.")
            return redirect('tenants:confirm_and_pay', subscription_id=subscription.pk)


# --- Your existing webhook view should also be in this file ---

@csrf_exempt
def paystack_webhook_view(request):
    # ... your existing webhook logic ...
    pass


# ========================================
# TRIAL AND PLAN SELECTION VIEWS
# ========================================

class PlanSelectionView(TemplateView):
    """
    View for selecting or upgrading subscription plans.
    Shows available plans with trial status and upgrade options.
    """
    template_name = 'subscriptions/plan_selection.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Get current tenant and subscription
        tenant = getattr(self.request, 'tenant', None)
        current_subscription = None
        trial_days_remaining = None

        if tenant:
            try:
                current_subscription = tenant.subscription
                if (current_subscription.status == 'TRIALING' and
                    current_subscription.trial_end_date):
                    trial_days_remaining = (current_subscription.trial_end_date - timezone.now()).days
                    if trial_days_remaining < 0:
                        trial_days_remaining = 0
            except Subscription.DoesNotExist:
                pass

        # Get available plans
        available_plans = SubscriptionPlan.objects.filter(
            is_active=True,
            is_public=True
        ).order_by('display_order', 'price_monthly')

        context.update({
            'available_plans': available_plans,
            'current_subscription': current_subscription,
            'trial_days_remaining': trial_days_remaining,
            'is_trial_expired': (
                current_subscription and
                current_subscription.status == 'TRIALING' and
                current_subscription.trial_end_date and
                current_subscription.trial_end_date <= timezone.now()
            ),
            'current_plan': current_subscription.plan if current_subscription else None,
        })

        return context



