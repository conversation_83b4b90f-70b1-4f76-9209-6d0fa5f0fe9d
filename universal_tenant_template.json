{"version": "1.0", "created_from": "alpha", "tables": {"announcements_announcement": {"columns": [["id", "bigint", null, 64, 0, "NO", null, 1], ["title", "character varying", 255, null, null, "NO", null, 2], ["content", "text", null, null, null, "NO", null, 3], ["publish_date", "timestamp with time zone", null, null, null, "NO", null, 6], ["expiry_date", "timestamp with time zone", null, null, null, "YES", null, 7], ["is_published", "boolean", null, null, null, "NO", null, 8], ["is_sticky", "boolean", null, null, null, "NO", null, 9], ["created_at", "timestamp with time zone", null, null, null, "NO", null, 10], ["updated_at", "timestamp with time zone", null, null, null, "NO", null, 11], ["author_id", "bigint", null, 64, 0, "YES", null, 12], ["is_global", "boolean", null, null, null, "NO", null, 13], ["target_all_tenant_parents", "boolean", null, null, null, "NO", null, 14], ["target_all_tenant_staff", "boolean", null, null, null, "NO", null, 15], ["target_global_audience_type", "character varying", 50, null, null, "YES", null, 16], ["tenant_id", "bigint", null, 64, 0, "YES", null, 17]], "create_sql": "CREATE TABLE IF NOT EXISTS \"announcements_announcement\" (\"id\" BIGINT NOT NULL, \"title\" VARCHAR(255) NOT NULL, \"content\" TEXT NOT NULL, \"publish_date\" TIMESTAMP WITH TIME ZONE NOT NULL, \"expiry_date\" TIMESTAMP WITH TIME ZONE, \"is_published\" BOOLEAN NOT NULL, \"is_sticky\" BOOLEAN NOT NULL, \"created_at\" TIMESTAMP WITH TIME ZONE NOT NULL, \"updated_at\" TIMESTAMP WITH TIME ZONE NOT NULL, \"author_id\" BIGINT, \"is_global\" BOOLEAN NOT NULL, \"target_all_tenant_parents\" BOOLEAN NOT NULL, \"target_all_tenant_staff\" BOOLEAN NOT NULL, \"target_global_audience_type\" VARCHAR(50), \"tenant_id\" BIGINT)"}, "announcements_announcement_target_tenant_staff_groups": {"columns": [["id", "bigint", null, 64, 0, "NO", null, 1], ["announcement_id", "bigint", null, 64, 0, "NO", null, 2], ["group_id", "integer", null, 32, 0, "NO", null, 3]], "create_sql": "CREATE TABLE IF NOT EXISTS \"announcements_announcement_target_tenant_staff_groups\" (\"id\" BIGINT NOT NULL, \"announcement_id\" BIGINT NOT NULL, \"group_id\" INTEGER NOT NULL)"}, "auth_group": {"columns": [["id", "integer", null, 32, 0, "NO", null, 1], ["name", "character varying", 150, null, null, "NO", null, 2]], "create_sql": "CREATE TABLE IF NOT EXISTS \"auth_group\" (\"id\" INTEGER NOT NULL, \"name\" VARCHAR(150) NOT NULL)"}, "auth_group_permissions": {"columns": [["id", "bigint", null, 64, 0, "NO", null, 1], ["group_id", "integer", null, 32, 0, "NO", null, 2], ["permission_id", "integer", null, 32, 0, "NO", null, 3]], "create_sql": "CREATE TABLE IF NOT EXISTS \"auth_group_permissions\" (\"id\" BIGINT NOT NULL, \"group_id\" INTEGER NOT NULL, \"permission_id\" INTEGER NOT NULL)"}, "auth_permission": {"columns": [["id", "integer", null, 32, 0, "NO", null, 1], ["name", "character varying", 255, null, null, "NO", null, 2], ["content_type_id", "integer", null, 32, 0, "NO", null, 3], ["codename", "character varying", 100, null, null, "NO", null, 4]], "create_sql": "CREATE TABLE IF NOT EXISTS \"auth_permission\" (\"id\" INTEGER NOT NULL, \"name\" VARCHAR(255) NOT NULL, \"content_type_id\" INTEGER NOT NULL, \"codename\" VARCHAR(100) NOT NULL)"}, "communication_communicationlog": {"columns": [["id", "bigint", null, 64, 0, "NO", null, 1], ["message_type", "character varying", 30, null, null, "NO", null, 2], ["status", "character varying", 20, null, null, "NO", null, 3], ["recipient_email", "character varying", 254, null, null, "YES", null, 4], ["recipient_phone", "character varying", 30, null, null, "YES", null, 5], ["subject", "character varying", 255, null, null, "YES", null, 6], ["body_preview", "text", null, null, null, "YES", null, 7], ["object_id", "character varying", 100, null, null, "YES", null, 8], ["sent_at", "timestamp with time zone", null, null, null, "YES", null, 9], ["created_at", "timestamp with time zone", null, null, null, "NO", null, 10], ["error_message", "text", null, null, null, "YES", null, 11], ["content_type_id", "integer", null, 32, 0, "YES", null, 12], ["sent_by_id", "bigint", null, 64, 0, "YES", null, 13], ["task_id", "character varying", 255, null, null, "YES", null, 14], ["updated_at", "timestamp with time zone", null, null, null, "NO", null, 15]], "create_sql": "CREATE TABLE IF NOT EXISTS \"communication_communicationlog\" (\"id\" BIGINT NOT NULL, \"message_type\" VARCHAR(30) NOT NULL, \"status\" VARCHAR(20) NOT NULL, \"recipient_email\" VARCHAR(254), \"recipient_phone\" VARCHAR(30), \"subject\" VARCHAR(255), \"body_preview\" TEXT, \"object_id\" VARCHAR(100), \"sent_at\" TIMESTAMP WITH TIME ZONE, \"created_at\" TIMESTAMP WITH TIME ZONE NOT NULL, \"error_message\" TEXT, \"content_type_id\" INTEGER, \"sent_by_id\" BIGINT, \"task_id\" VARCHAR(255), \"updated_at\" TIMESTAMP WITH TIME ZONE NOT NULL)"}, "django_admin_log": {"columns": [["id", "integer", null, 32, 0, "NO", null, 1], ["action_time", "timestamp with time zone", null, null, null, "NO", null, 2], ["object_id", "text", null, null, null, "YES", null, 3], ["object_repr", "character varying", 200, null, null, "NO", null, 4], ["action_flag", "smallint", null, 16, 0, "NO", null, 5], ["change_message", "text", null, null, null, "NO", null, 6], ["content_type_id", "integer", null, 32, 0, "YES", null, 7], ["user_id", "bigint", null, 64, 0, "NO", null, 8]], "create_sql": "CREATE TABLE IF NOT EXISTS \"django_admin_log\" (\"id\" INTEGER NOT NULL, \"action_time\" TIMESTAMP WITH TIME ZONE NOT NULL, \"object_id\" TEXT, \"object_repr\" VARCHAR(200) NOT NULL, \"action_flag\" SMALLINT NOT NULL, \"change_message\" TEXT NOT NULL, \"content_type_id\" INTEGER, \"user_id\" BIGINT NOT NULL)"}, "django_content_type": {"columns": [["id", "integer", null, 32, 0, "NO", null, 1], ["app_label", "character varying", 100, null, null, "NO", null, 3], ["model", "character varying", 100, null, null, "NO", null, 4]], "create_sql": "CREATE TABLE IF NOT EXISTS \"django_content_type\" (\"id\" INTEGER NOT NULL, \"app_label\" VARCHAR(100) NOT NULL, \"model\" VARCHAR(100) NOT NULL)"}, "django_migrations": {"columns": [["id", "bigint", null, 64, 0, "NO", null, 1], ["app", "character varying", 255, null, null, "NO", null, 2], ["name", "character varying", 255, null, null, "NO", null, 3], ["applied", "timestamp with time zone", null, null, null, "NO", null, 4]], "create_sql": "CREATE TABLE IF NOT EXISTS \"django_migrations\" (\"id\" BIGINT NOT NULL, \"app\" VARCHAR(255) NOT NULL, \"name\" VARCHAR(255) NOT NULL, \"applied\" TIMESTAMP WITH TIME ZONE NOT NULL)"}, "fees_account": {"columns": [["id", "bigint", null, 64, 0, "NO", null, 1], ["created_at", "timestamp with time zone", null, null, null, "NO", null, 2], ["updated_at", "timestamp with time zone", null, null, null, "NO", null, 3], ["name", "character varying", 100, null, null, "NO", null, 4], ["account_number", "character varying", 50, null, null, "YES", null, 5], ["account_type", "character varying", 10, null, null, "NO", null, 6], ["description", "text", null, null, null, "YES", null, 7], ["is_active", "boolean", null, null, null, "NO", null, 8]], "create_sql": "CREATE TABLE IF NOT EXISTS \"fees_account\" (\"id\" BIGINT NOT NULL, \"created_at\" TIMESTAMP WITH TIME ZONE NOT NULL, \"updated_at\" TIMESTAMP WITH TIME ZONE NOT NULL, \"name\" VARCHAR(100) NOT NULL, \"account_number\" VARCHAR(50), \"account_type\" VARCHAR(10) NOT NULL, \"description\" TEXT, \"is_active\" BOOLEAN NOT NULL)"}, "fees_concessiontype": {"columns": [["id", "bigint", null, 64, 0, "NO", null, 1], ["name", "character varying", 100, null, null, "NO", null, 2], ["description", "text", null, null, null, "YES", null, 3], ["value", "numeric", null, 10, 2, "NO", null, 5], ["is_active", "boolean", null, null, null, "NO", null, 6], ["created_at", "timestamp with time zone", null, null, null, "NO", null, 7], ["updated_at", "timestamp with time zone", null, null, null, "NO", null, 8], ["type", "character varying", 20, null, null, "NO", "'PERCENTAGE'::character varying", 9]], "create_sql": "CREATE TABLE IF NOT EXISTS \"fees_concessiontype\" (\"id\" BIGINT NOT NULL, \"name\" VARCHAR(100) NOT NULL, \"description\" TEXT, \"value\" NUMERIC(10,2) NOT NULL, \"is_active\" BOOLEAN NOT NULL, \"created_at\" TIMESTAMP WITH TIME ZONE NOT NULL, \"updated_at\" TIMESTAMP WITH TIME ZONE NOT NULL, \"type\" VARCHAR(20) NOT NULL DEFAULT 'PERCENTAGE'::character varying)"}, "fees_feehead": {"columns": [["id", "bigint", null, 64, 0, "NO", "nextval('fees_feehead_id_seq'::regclass)", 1], ["name", "character varying", 150, null, null, "NO", null, 2], ["description", "text", null, null, null, "YES", null, 3], ["income_account_link_id", "bigint", null, 64, 0, "YES", null, 4], ["is_active", "boolean", null, null, null, "NO", "true", 5], ["created_at", "timestamp with time zone", null, null, null, "NO", "now()", 6], ["updated_at", "timestamp with time zone", null, null, null, "NO", "now()", 7]], "create_sql": "CREATE TABLE IF NOT EXISTS \"fees_feehead\" (\"id\" BIGINT NOT NULL, \"name\" VARCHAR(150) NOT NULL, \"description\" TEXT, \"income_account_link_id\" BIGINT, \"is_active\" BOOLEAN NOT NULL DEFAULT true, \"created_at\" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), \"updated_at\" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now())"}, "fees_feestructure": {"columns": [["id", "bigint", null, 64, 0, "NO", null, 1], ["name", "character varying", 200, null, null, "NO", null, 2], ["description", "text", null, null, null, "YES", null, 3], ["is_active", "boolean", null, null, null, "NO", null, 4], ["total_amount", "numeric", null, 12, 2, "NO", null, 5], ["created_at", "timestamp with time zone", null, null, null, "NO", null, 6], ["updated_at", "timestamp with time zone", null, null, null, "NO", null, 7], ["academic_year_id", "bigint", null, 64, 0, "NO", null, 8], ["term_id", "bigint", null, 64, 0, "YES", null, 10]], "create_sql": "CREATE TABLE IF NOT EXISTS \"fees_feestructure\" (\"id\" BIGINT NOT NULL, \"name\" VARCHAR(200) NOT NULL, \"description\" TEXT, \"is_active\" BOOLEAN NOT NULL, \"total_amount\" NUMERIC(12,2) NOT NULL, \"created_at\" TIMESTAMP WITH TIME ZONE NOT NULL, \"updated_at\" TIMESTAMP WITH TIME ZONE NOT NULL, \"academic_year_id\" BIGINT NOT NULL, \"term_id\" BIGINT)"}, "fees_feestructure_applicable_classes": {"columns": [["id", "bigint", null, 64, 0, "NO", "nextval('fees_feestructure_applicable_classes_id_seq'::regclass)", 1], ["feestructure_id", "bigint", null, 64, 0, "NO", null, 2], ["schoolclass_id", "bigint", null, 64, 0, "NO", null, 3]], "create_sql": "CREATE TABLE IF NOT EXISTS \"fees_feestructure_applicable_classes\" (\"id\" BIGINT NOT NULL, \"feestructure_id\" BIGINT NOT NULL, \"schoolclass_id\" BIGINT NOT NULL)"}, "fees_feestructureitem": {"columns": [["id", "bigint", null, 64, 0, "NO", null, 1], ["amount", "numeric", null, 10, 2, "NO", null, 2], ["is_optional", "boolean", null, null, null, "NO", null, 3], ["created_at", "timestamp with time zone", null, null, null, "YES", null, 4], ["updated_at", "timestamp with time zone", null, null, null, "YES", null, 5], ["concession_type_id", "bigint", null, 64, 0, "YES", null, 6], ["fee_head_id", "bigint", null, 64, 0, "NO", null, 7], ["fee_structure_id", "bigint", null, 64, 0, "NO", null, 8], ["description", "character varying", 255, null, null, "YES", null, 9]], "create_sql": "CREATE TABLE IF NOT EXISTS \"fees_feestructureitem\" (\"id\" BIGINT NOT NULL, \"amount\" NUMERIC(10,2) NOT NULL, \"is_optional\" BOOLEAN NOT NULL, \"created_at\" TIMESTAMP WITH TIME ZONE, \"updated_at\" TIMESTAMP WITH TIME ZONE, \"concession_type_id\" BIGINT, \"fee_head_id\" BIGINT NOT NULL, \"fee_structure_id\" BIGINT NOT NULL, \"description\" VARCHAR(255))"}, "fees_invoice": {"columns": [["id", "bigint", null, 64, 0, "NO", null, 1], ["invoice_number", "character varying", 50, null, null, "NO", null, 2], ["issue_date", "date", null, null, null, "NO", null, 3], ["due_date", "date", null, null, null, "YES", null, 4], ["subtotal_amount_calc", "numeric", null, 12, 2, "YES", null, 5], ["discount_amount_calc", "numeric", null, 12, 2, "YES", null, 6], ["total_amount", "numeric", null, 12, 2, "YES", null, 7], ["amount_paid", "numeric", null, 12, 2, "NO", null, 8], ["status", "character varying", 20, null, null, "NO", null, 9], ["period_description_override", "character varying", 255, null, null, "YES", null, 10], ["notes", "text", null, null, null, "YES", null, 11], ["internal_notes", "text", null, null, null, "YES", null, 12], ["created_at", "timestamp with time zone", null, null, null, "NO", null, 13], ["updated_at", "timestamp with time zone", null, null, null, "NO", null, 14], ["academic_year_id", "bigint", null, 64, 0, "NO", null, 15], ["created_by_id", "bigint", null, 64, 0, "YES", null, 16], ["fee_structure_id", "bigint", null, 64, 0, "YES", null, 17], ["related_journal_entry_id", "bigint", null, 64, 0, "YES", null, 18], ["student_id", "bigint", null, 64, 0, "NO", null, 19], ["term_id", "bigint", null, 64, 0, "YES", null, 20], ["subtotal_amount", "numeric", null, 12, 2, "NO", "0.00", 21], ["total_concession_amount", "numeric", null, 12, 2, "NO", "0.00", 22], ["notes_to_parent", "text", null, null, null, "YES", null, 23]], "create_sql": "CREATE TABLE IF NOT EXISTS \"fees_invoice\" (\"id\" BIGINT NOT NULL, \"invoice_number\" VARCHAR(50) NOT NULL, \"issue_date\" DATE NOT NULL, \"due_date\" DATE, \"subtotal_amount_calc\" NUMERIC(12,2), \"discount_amount_calc\" NUMERIC(12,2), \"total_amount\" NUMERIC(12,2), \"amount_paid\" NUMERIC(12,2) NOT NULL, \"status\" VARCHAR(20) NOT NULL, \"period_description_override\" VARCHAR(255), \"notes\" TEXT, \"internal_notes\" TEXT, \"created_at\" TIMESTAMP WITH TIME ZONE NOT NULL, \"updated_at\" TIMESTAMP WITH TIME ZONE NOT NULL, \"academic_year_id\" BIGINT NOT NULL, \"created_by_id\" BIGINT, \"fee_structure_id\" BIGINT, \"related_journal_entry_id\" BIGINT, \"student_id\" BIGINT NOT NULL, \"term_id\" BIGINT, \"subtotal_amount\" NUMERIC(12,2) NOT NULL DEFAULT 0.00, \"total_concession_amount\" NUMERIC(12,2) NOT NULL DEFAULT 0.00, \"notes_to_parent\" TEXT)"}, "fees_invoicedetail": {"columns": [["id", "bigint", null, 64, 0, "NO", null, 1], ["line_type", "character varying", 20, null, null, "NO", null, 2], ["description", "character varying", 255, null, null, "NO", null, 3], ["quantity", "numeric", null, 10, 2, "NO", null, 4], ["unit_price", "numeric", null, 12, 2, "NO", null, 5], ["created_at", "timestamp with time zone", null, null, null, "NO", null, 6], ["updated_at", "timestamp with time zone", null, null, null, "NO", null, 7], ["applies_to_line_id", "bigint", null, 64, 0, "YES", null, 8], ["concession_type_id", "bigint", null, 64, 0, "YES", null, 9], ["fee_head_id", "bigint", null, 64, 0, "YES", null, 10], ["invoice_id", "bigint", null, 64, 0, "NO", null, 11], ["amount", "numeric", null, 12, 2, "NO", "0.00", 12]], "create_sql": "CREATE TABLE IF NOT EXISTS \"fees_invoicedetail\" (\"id\" BIGINT NOT NULL, \"line_type\" VARCHAR(20) NOT NULL, \"description\" VARCHAR(255) NOT NULL, \"quantity\" NUMERIC(10,2) NOT NULL, \"unit_price\" NUMERIC(12,2) NOT NULL, \"created_at\" TIMESTAMP WITH TIME ZONE NOT NULL, \"updated_at\" TIMESTAMP WITH TIME ZONE NOT NULL, \"applies_to_line_id\" BIGINT, \"concession_type_id\" BIGINT, \"fee_head_id\" BIGINT, \"invoice_id\" BIGINT NOT NULL, \"amount\" NUMERIC(12,2) NOT NULL DEFAULT 0.00)"}, "fees_studentconcession": {"columns": [["id", "bigint", null, 64, 0, "NO", null, 1], ["notes", "text", null, null, null, "YES", null, 2], ["granted_at", "timestamp with time zone", null, null, null, "NO", null, 3], ["academic_year_id", "bigint", null, 64, 0, "NO", null, 4], ["concession_type_id", "bigint", null, 64, 0, "NO", null, 5], ["granted_by_id", "bigint", null, 64, 0, "YES", null, 6], ["student_id", "bigint", null, 64, 0, "NO", null, 7], ["term_id", "bigint", null, 64, 0, "YES", null, 8]], "create_sql": "CREATE TABLE IF NOT EXISTS \"fees_studentconcession\" (\"id\" BIGINT NOT NULL, \"notes\" TEXT, \"granted_at\" TIMESTAMP WITH TIME ZONE NOT NULL, \"academic_year_id\" BIGINT NOT NULL, \"concession_type_id\" BIGINT NOT NULL, \"granted_by_id\" BIGINT, \"student_id\" BIGINT NOT NULL, \"term_id\" BIGINT)"}, "fees_studentfeeallocation": {"columns": [["id", "bigint", null, 64, 0, "NO", null, 1], ["effective_from", "date", null, null, null, "NO", null, 3], ["effective_to", "date", null, null, null, "YES", null, 4], ["is_active", "boolean", null, null, null, "NO", null, 5], ["created_at", "timestamp with time zone", null, null, null, "YES", null, 6], ["updated_at", "timestamp with time zone", null, null, null, "YES", null, 7], ["academic_year_id", "bigint", null, 64, 0, "NO", null, 8], ["fee_structure_id", "bigint", null, 64, 0, "NO", null, 9], ["student_id", "bigint", null, 64, 0, "NO", null, 10], ["term_id", "bigint", null, 64, 0, "YES", null, 11], ["notes", "text", null, null, null, "YES", null, 12]], "create_sql": "CREATE TABLE IF NOT EXISTS \"fees_studentfeeallocation\" (\"id\" BIGINT NOT NULL, \"effective_from\" DATE NOT NULL, \"effective_to\" DATE, \"is_active\" BOOLEAN NOT NULL, \"created_at\" TIMESTAMP WITH TIME ZONE, \"updated_at\" TIMESTAMP WITH TIME ZONE, \"academic_year_id\" BIGINT NOT NULL, \"fee_structure_id\" BIGINT NOT NULL, \"student_id\" BIGINT NOT NULL, \"term_id\" BIGINT, \"notes\" TEXT)"}, "finance_budget": {"columns": [["id", "bigint", null, 64, 0, "NO", null, 1], ["name", "character varying", 255, null, null, "NO", null, 2], ["financial_year_start", "date", null, null, null, "NO", null, 3], ["financial_year_end", "date", null, null, null, "NO", null, 4], ["description", "text", null, null, null, "YES", null, 5], ["is_active", "boolean", null, null, null, "NO", null, 6]], "create_sql": "CREATE TABLE IF NOT EXISTS \"finance_budget\" (\"id\" BIGINT NOT NULL, \"name\" VARCHAR(255) NOT NULL, \"financial_year_start\" DATE NOT NULL, \"financial_year_end\" DATE NOT NULL, \"description\" TEXT, \"is_active\" BOOLEAN NOT NULL)"}, "finance_budgetamount": {"columns": [["id", "bigint", null, 64, 0, "NO", null, 1], ["created_at", "timestamp with time zone", null, null, null, "YES", null, 2], ["updated_at", "timestamp with time zone", null, null, null, "YES", null, 3], ["budgeted_amount", "numeric", null, 12, 2, "NO", null, 4], ["notes", "text", null, null, null, "YES", null, 5], ["academic_year_id", "bigint", null, 64, 0, "NO", null, 6], ["term_id", "bigint", null, 64, 0, "YES", null, 7], ["budget_item_id", "bigint", null, 64, 0, "NO", null, 8]], "create_sql": "CREATE TABLE IF NOT EXISTS \"finance_budgetamount\" (\"id\" BIGINT NOT NULL, \"created_at\" TIMESTAMP WITH TIME ZONE, \"updated_at\" TIMESTAMP WITH TIME ZONE, \"budgeted_amount\" NUMERIC(12,2) NOT NULL, \"notes\" TEXT, \"academic_year_id\" BIGINT NOT NULL, \"term_id\" BIGINT, \"budget_item_id\" BIGINT NOT NULL)"}, "finance_budgetitem": {"columns": [["id", "bigint", null, 64, 0, "NO", null, 1], ["created_at", "timestamp with time zone", null, null, null, "YES", null, 2], ["updated_at", "timestamp with time zone", null, null, null, "YES", null, 3], ["name", "character varying", 150, null, null, "NO", null, 4], ["description", "text", null, null, null, "YES", null, 5], ["budget_item_type", "character varying", 10, null, null, "NO", null, 6], ["linked_coa_account_id", "bigint", null, 64, 0, "NO", null, 7]], "create_sql": "CREATE TABLE IF NOT EXISTS \"finance_budgetitem\" (\"id\" BIGINT NOT NULL, \"created_at\" TIMESTAMP WITH TIME ZONE, \"updated_at\" TIMESTAMP WITH TIME ZONE, \"name\" VARCHAR(150) NOT NULL, \"description\" TEXT, \"budget_item_type\" VARCHAR(10) NOT NULL, \"linked_coa_account_id\" BIGINT NOT NULL)"}, "finance_expense": {"columns": [["id", "bigint", null, 64, 0, "NO", null, 1], ["created_at", "timestamp with time zone", null, null, null, "YES", null, 2], ["updated_at", "timestamp with time zone", null, null, null, "YES", null, 3], ["expense_date", "date", null, null, null, "NO", null, 4], ["amount", "numeric", null, 12, 2, "NO", null, 5], ["description", "text", null, null, null, "NO", null, 6], ["reference_number", "character varying", 100, null, null, "YES", null, 7], ["payment_method_id", "bigint", null, 64, 0, "YES", null, 8], ["recorded_by_id", "bigint", null, 64, 0, "YES", null, 9], ["category_id", "bigint", null, 64, 0, "NO", null, 10], ["vendor_id", "bigint", null, 64, 0, "YES", null, 11]], "create_sql": "CREATE TABLE IF NOT EXISTS \"finance_expense\" (\"id\" BIGINT NOT NULL, \"created_at\" TIMESTAMP WITH TIME ZONE, \"updated_at\" TIMESTAMP WITH TIME ZONE, \"expense_date\" DATE NOT NULL, \"amount\" NUMERIC(12,2) NOT NULL, \"description\" TEXT NOT NULL, \"reference_number\" VARCHAR(100), \"payment_method_id\" BIGINT, \"recorded_by_id\" BIGINT, \"category_id\" BIGINT NOT NULL, \"vendor_id\" BIGINT)"}, "finance_expensecategory": {"columns": [["id", "bigint", null, 64, 0, "NO", null, 1], ["created_at", "timestamp with time zone", null, null, null, "YES", null, 2], ["updated_at", "timestamp with time zone", null, null, null, "YES", null, 3], ["name", "character varying", 100, null, null, "NO", null, 4], ["description", "text", null, null, null, "YES", null, 5], ["is_active", "boolean", null, null, null, "NO", null, 6], ["expense_account_id", "bigint", null, 64, 0, "NO", null, 7]], "create_sql": "CREATE TABLE IF NOT EXISTS \"finance_expensecategory\" (\"id\" BIGINT NOT NULL, \"created_at\" TIMESTAMP WITH TIME ZONE, \"updated_at\" TIMESTAMP WITH TIME ZONE, \"name\" VARCHAR(100) NOT NULL, \"description\" TEXT, \"is_active\" BOOLEAN NOT NULL, \"expense_account_id\" BIGINT NOT NULL)"}, "finance_vendor": {"columns": [["id", "bigint", null, 64, 0, "NO", null, 1], ["created_at", "timestamp with time zone", null, null, null, "YES", null, 2], ["updated_at", "timestamp with time zone", null, null, null, "YES", null, 3], ["name", "character varying", 150, null, null, "NO", null, 4], ["contact_person", "character varying", 100, null, null, "YES", null, 5], ["email", "character varying", 254, null, null, "YES", null, 6], ["phone_number", "character varying", 30, null, null, "YES", null, 7], ["address_line1", "character varying", 255, null, null, "YES", null, 8], ["address_line2", "character varying", 255, null, null, "YES", null, 9], ["city", "character varying", 100, null, null, "YES", null, 10], ["state_province", "character varying", 100, null, null, "YES", null, 11], ["postal_code", "character varying", 20, null, null, "YES", null, 12], ["country", "character varying", 100, null, null, "YES", null, 13], ["notes", "text", null, null, null, "YES", null, 14], ["is_active", "boolean", null, null, null, "NO", null, 15]], "create_sql": "CREATE TABLE IF NOT EXISTS \"finance_vendor\" (\"id\" BIGINT NOT NULL, \"created_at\" TIMESTAMP WITH TIME ZONE, \"updated_at\" TIMESTAMP WITH TIME ZONE, \"name\" VARCHAR(150) NOT NULL, \"contact_person\" VARCHAR(100), \"email\" VARCHAR(254), \"phone_number\" VARCHAR(30), \"address_line1\" VARCHAR(255), \"address_line2\" VARCHAR(255), \"city\" VARCHAR(100), \"state_province\" VARCHAR(100), \"postal_code\" VARCHAR(20), \"country\" VARCHAR(100), \"notes\" TEXT, \"is_active\" BOOLEAN NOT NULL)"}, "hr_employeeprofile": {"columns": [["user_id", "bigint", null, 64, 0, "NO", null, 1], ["middle_name", "character varying", 100, null, null, "NO", null, 2], ["gender", "character varying", 15, null, null, "YES", null, 3], ["date_of_birth", "date", null, null, null, "YES", null, 4], ["marital_status", "character varying", 15, null, null, "YES", null, 5], ["phone_number_alternate", "character varying", 30, null, null, "NO", null, 6], ["address_line1", "character varying", 255, null, null, "NO", null, 7], ["address_line2", "character varying", 255, null, null, "NO", null, 8], ["city", "character varying", 100, null, null, "NO", null, 9], ["state_province", "character varying", 100, null, null, "NO", null, 10], ["postal_code", "character varying", 20, null, null, "NO", null, 11], ["country", "character varying", 100, null, null, "NO", null, 12], ["employment_type", "character varying", 20, null, null, "YES", null, 13], ["date_left", "date", null, null, null, "YES", null, 14], ["photo", "character varying", 100, null, null, "YES", null, 15], ["notes", "text", null, null, null, "NO", null, 16], ["created_at", "timestamp with time zone", null, null, null, "NO", null, 17], ["updated_at", "timestamp with time zone", null, null, null, "NO", null, 18], ["date_hired", "date", null, null, null, "YES", null, 19], ["department", "character varying", 100, null, null, "NO", null, 20], ["designation", "character varying", 100, null, null, "NO", null, 21], ["employee_id", "character varying", 50, null, null, "YES", null, 22], ["phone_number_primary", "character varying", 30, null, null, "NO", null, 23]], "create_sql": "CREATE TABLE IF NOT EXISTS \"hr_employeeprofile\" (\"user_id\" BIGINT NOT NULL, \"middle_name\" VARCHAR(100) NOT NULL, \"gender\" VARCHAR(15), \"date_of_birth\" DATE, \"marital_status\" VARCHAR(15), \"phone_number_alternate\" VARCHAR(30) NOT NULL, \"address_line1\" VARCHAR(255) NOT NULL, \"address_line2\" VARCHAR(255) NOT NULL, \"city\" VARCHAR(100) NOT NULL, \"state_province\" VARCHAR(100) NOT NULL, \"postal_code\" VARCHAR(20) NOT NULL, \"country\" VARCHAR(100) NOT NULL, \"employment_type\" VARCHAR(20), \"date_left\" DATE, \"photo\" VARCHAR(100), \"notes\" TEXT NOT NULL, \"created_at\" TIMESTAMP WITH TIME ZONE NOT NULL, \"updated_at\" TIMESTAMP WITH TIME ZONE NOT NULL, \"date_hired\" DATE, \"department\" VARCHAR(100) NOT NULL, \"designation\" VARCHAR(100) NOT NULL, \"employee_id\" VARCHAR(50), \"phone_number_primary\" VARCHAR(30) NOT NULL)"}, "hr_graderule": {"columns": [["id", "bigint", null, 64, 0, "NO", null, 1], ["value", "numeric", null, 10, 2, "NO", null, 2], ["component_id", "bigint", null, 64, 0, "NO", null, 3], ["grade_id", "bigint", null, 64, 0, "NO", null, 4]], "create_sql": "CREATE TABLE IF NOT EXISTS \"hr_graderule\" (\"id\" BIGINT NOT NULL, \"value\" NUMERIC(10,2) NOT NULL, \"component_id\" BIGINT NOT NULL, \"grade_id\" BIGINT NOT NULL)"}, "hr_leavebalance": {"columns": [["id", "bigint", null, 64, 0, "NO", null, 1], ["days_accrued", "numeric", null, 5, 1, "NO", null, 3], ["last_accrual_date", "date", null, null, null, "YES", null, 5], ["created_at", "timestamp with time zone", null, null, null, "NO", null, 6], ["updated_at", "timestamp with time zone", null, null, null, "NO", null, 7], ["employee_id", "bigint", null, 64, 0, "NO", null, 8], ["leave_type_id", "bigint", null, 64, 0, "NO", null, 9], ["year", "integer", null, 32, 0, "NO", null, 10]], "create_sql": "CREATE TABLE IF NOT EXISTS \"hr_leavebalance\" (\"id\" BIGINT NOT NULL, \"days_accrued\" NUMERIC(5,1) NOT NULL, \"last_accrual_date\" DATE, \"created_at\" TIMESTAMP WITH TIME ZONE NOT NULL, \"updated_at\" TIMESTAMP WITH TIME ZONE NOT NULL, \"employee_id\" BIGINT NOT NULL, \"leave_type_id\" BIGINT NOT NULL, \"year\" INTEGER NOT NULL)"}, "hr_leaverequest": {"columns": [["id", "bigint", null, 64, 0, "NO", null, 1], ["start_date", "date", null, null, null, "NO", null, 2], ["end_date", "date", null, null, null, "NO", null, 3], ["half_day_start", "boolean", null, null, null, "NO", null, 4], ["half_day_end", "boolean", null, null, null, "NO", null, 5], ["reason", "text", null, null, null, "NO", null, 6], ["attachment", "character varying", 100, null, null, "YES", null, 7], ["status", "character varying", 20, null, null, "NO", null, 8], ["created_at", "timestamp with time zone", null, null, null, "NO", null, 13], ["updated_at", "timestamp with time zone", null, null, null, "NO", null, 14], ["approved_by_id", "bigint", null, 64, 0, "YES", null, 15], ["employee_id", "bigint", null, 64, 0, "NO", null, 16], ["leave_type_id", "bigint", null, 64, 0, "NO", null, 17], ["duration", "numeric", null, 5, 1, "NO", null, 18], ["status_changed_at", "timestamp with time zone", null, null, null, "YES", null, 19], ["status_reason", "text", null, null, null, "YES", null, 20]], "create_sql": "CREATE TABLE IF NOT EXISTS \"hr_leaverequest\" (\"id\" BIGINT NOT NULL, \"start_date\" DATE NOT NULL, \"end_date\" DATE NOT NULL, \"half_day_start\" BOOLEAN NOT NULL, \"half_day_end\" BOOLEAN NOT NULL, \"reason\" TEXT NOT NULL, \"attachment\" VARCHAR(100), \"status\" VARCHAR(20) NOT NULL, \"created_at\" TIMESTAMP WITH TIME ZONE NOT NULL, \"updated_at\" TIMESTAMP WITH TIME ZONE NOT NULL, \"approved_by_id\" BIGINT, \"employee_id\" BIGINT NOT NULL, \"leave_type_id\" BIGINT NOT NULL, \"duration\" NUMERIC(5,1) NOT NULL, \"status_changed_at\" TIMESTAMP WITH TIME ZONE, \"status_reason\" TEXT)"}, "hr_leavetype": {"columns": [["id", "bigint", null, 64, 0, "NO", null, 1], ["name", "character varying", 100, null, null, "NO", null, 2], ["description", "text", null, null, null, "YES", null, 3], ["max_annual_days", "numeric", null, 5, 1, "YES", null, 4], ["is_paid", "boolean", null, null, null, "NO", null, 5], ["requires_approval", "boolean", null, null, null, "NO", null, 6], ["is_active", "boolean", null, null, null, "NO", null, 7], ["created_at", "timestamp with time zone", null, null, null, "NO", null, 8], ["updated_at", "timestamp with time zone", null, null, null, "NO", null, 9], ["max_days_per_year_grant", "numeric", null, 5, 2, "YES", null, 10], ["accrual_frequency", "character varying", 20, null, null, "NO", null, 11], ["accrual_rate", "numeric", null, 5, 2, "NO", null, 12], ["max_accrual_balance", "integer", null, 32, 0, "YES", null, 13], ["prorate_accrual", "boolean", null, null, null, "NO", null, 14]], "create_sql": "CREATE TABLE IF NOT EXISTS \"hr_leavetype\" (\"id\" BIGINT NOT NULL, \"name\" VARCHAR(100) NOT NULL, \"description\" TEXT, \"max_annual_days\" NUMERIC(5,1), \"is_paid\" BOOLEAN NOT NULL, \"requires_approval\" BOOLEAN NOT NULL, \"is_active\" BOOLEAN NOT NULL, \"created_at\" TIMESTAMP WITH TIME ZONE NOT NULL, \"updated_at\" TIMESTAMP WITH TIME ZONE NOT NULL, \"max_days_per_year_grant\" NUMERIC(5,2), \"accrual_frequency\" VARCHAR(20) NOT NULL, \"accrual_rate\" NUMERIC(5,2) NOT NULL, \"max_accrual_balance\" INTEGER, \"prorate_accrual\" BOOLEAN NOT NULL)"}, "hr_payrollrun": {"columns": [["id", "bigint", null, 64, 0, "NO", null, 1], ["pay_period_start", "date", null, null, null, "NO", null, 2], ["pay_period_end", "date", null, null, null, "NO", null, 3], ["payment_date", "date", null, null, null, "NO", null, 4], ["status", "character varying", 20, null, null, "NO", null, 5], ["notes", "text", null, null, null, "YES", null, 6], ["processed_at", "timestamp with time zone", null, null, null, "YES", null, 7], ["created_at", "timestamp with time zone", null, null, null, "NO", null, 8], ["updated_at", "timestamp with time zone", null, null, null, "NO", null, 9], ["processed_by_id", "bigint", null, 64, 0, "YES", null, 10]], "create_sql": "CREATE TABLE IF NOT EXISTS \"hr_payrollrun\" (\"id\" BIGINT NOT NULL, \"pay_period_start\" DATE NOT NULL, \"pay_period_end\" DATE NOT NULL, \"payment_date\" DATE NOT NULL, \"status\" VARCHAR(20) NOT NULL, \"notes\" TEXT, \"processed_at\" TIMESTAMP WITH TIME ZONE, \"created_at\" TIMESTAMP WITH TIME ZONE NOT NULL, \"updated_at\" TIMESTAMP WITH TIME ZONE NOT NULL, \"processed_by_id\" BIGINT)"}, "hr_payslip": {"columns": [["id", "bigint", null, 64, 0, "NO", null, 1], ["gross_earnings", "numeric", null, 10, 2, "NO", null, 4], ["total_deductions", "numeric", null, 10, 2, "NO", null, 5], ["net_pay", "numeric", null, 10, 2, "NO", null, 6], ["created_at", "timestamp with time zone", null, null, null, "NO", null, 8], ["staff_member_id", "bigint", null, 64, 0, "NO", null, 10], ["allowances", "numeric", null, 10, 2, "NO", null, 11], ["basic_salary", "numeric", null, 10, 2, "NO", null, 12], ["bonuses", "numeric", null, 10, 2, "NO", null, 13], ["loan_repayments", "numeric", null, 10, 2, "NO", null, 14], ["notes", "text", null, null, null, "YES", null, 15], ["other_deductions", "numeric", null, 10, 2, "NO", null, 16], ["pension_deductions", "numeric", null, 10, 2, "NO", null, 17], ["tax_deductions", "numeric", null, 10, 2, "NO", null, 18], ["payroll_run_id", "bigint", null, 64, 0, "YES", null, 19], ["adjustments", "numeric", null, 10, 2, "NO", null, 20]], "create_sql": "CREATE TABLE IF NOT EXISTS \"hr_payslip\" (\"id\" BIGINT NOT NULL, \"gross_earnings\" NUMERIC(10,2) NOT NULL, \"total_deductions\" NUMERIC(10,2) NOT NULL, \"net_pay\" NUMERIC(10,2) NOT NULL, \"created_at\" TIMESTAMP WITH TIME ZONE NOT NULL, \"staff_member_id\" BIGINT NOT NULL, \"allowances\" NUMERIC(10,2) NOT NULL, \"basic_salary\" NUMERIC(10,2) NOT NULL, \"bonuses\" NUMERIC(10,2) NOT NULL, \"loan_repayments\" NUMERIC(10,2) NOT NULL, \"notes\" TEXT, \"other_deductions\" NUMERIC(10,2) NOT NULL, \"pension_deductions\" NUMERIC(10,2) NOT NULL, \"tax_deductions\" NUMERIC(10,2) NOT NULL, \"payroll_run_id\" BIGINT, \"adjustments\" NUMERIC(10,2) NOT NULL)"}, "hr_paysliplineitem": {"columns": [["id", "bigint", null, 64, 0, "NO", null, 1], ["name", "character varying", 100, null, null, "NO", null, 2], ["type", "character varying", 10, null, null, "NO", null, 3], ["amount", "numeric", null, 12, 2, "NO", null, 4], ["payslip_id", "bigint", null, 64, 0, "NO", null, 5], ["source_component_id", "bigint", null, 64, 0, "YES", null, 6]], "create_sql": "CREATE TABLE IF NOT EXISTS \"hr_paysliplineitem\" (\"id\" BIGINT NOT NULL, \"name\" VARCHAR(100) NOT NULL, \"type\" VARCHAR(10) NOT NULL, \"amount\" NUMERIC(12,2) NOT NULL, \"payslip_id\" BIGINT NOT NULL, \"source_component_id\" BIGINT)"}, "hr_salarycomponent": {"columns": [["id", "bigint", null, 64, 0, "NO", null, 1], ["name", "character varying", 100, null, null, "NO", null, 2], ["type", "character varying", 10, null, null, "NO", null, 3], ["description", "text", null, null, null, "YES", null, 6], ["is_percentage", "boolean", null, null, null, "NO", null, 7]], "create_sql": "CREATE TABLE IF NOT EXISTS \"hr_salarycomponent\" (\"id\" BIGINT NOT NULL, \"name\" VARCHAR(100) NOT NULL, \"type\" VARCHAR(10) NOT NULL, \"description\" TEXT, \"is_percentage\" BOOLEAN NOT NULL)"}, "hr_salarygrade": {"columns": [["id", "bigint", null, 64, 0, "NO", null, 1], ["name", "character varying", 100, null, null, "NO", null, 2], ["description", "text", null, null, null, "NO", null, 3], ["is_active", "boolean", null, null, null, "NO", null, 5]], "create_sql": "CREATE TABLE IF NOT EXISTS \"hr_salarygrade\" (\"id\" BIGINT NOT NULL, \"name\" VARCHAR(100) NOT NULL, \"description\" TEXT NOT NULL, \"is_active\" BOOLEAN NOT NULL)"}, "hr_salarygradecomponent": {"columns": [["id", "bigint", null, 64, 0, "NO", null, 1], ["amount", "numeric", null, 10, 2, "NO", null, 2], ["component_id", "bigint", null, 64, 0, "NO", null, 3], ["grade_id", "bigint", null, 64, 0, "NO", null, 4]], "create_sql": "CREATE TABLE IF NOT EXISTS \"hr_salarygradecomponent\" (\"id\" BIGINT NOT NULL, \"amount\" NUMERIC(10,2) NOT NULL, \"component_id\" BIGINT NOT NULL, \"grade_id\" BIGINT NOT NULL)"}, "hr_salarystructurecomponent": {"columns": [["id", "bigint", null, 64, 0, "NO", null, 1], ["amount", "numeric", null, 10, 2, "NO", null, 2], ["component_id", "bigint", null, 64, 0, "NO", null, 3], ["salary_structure_id", "bigint", null, 64, 0, "NO", null, 4]], "create_sql": "CREATE TABLE IF NOT EXISTS \"hr_salarystructurecomponent\" (\"id\" BIGINT NOT NULL, \"amount\" NUMERIC(10,2) NOT NULL, \"component_id\" BIGINT NOT NULL, \"salary_structure_id\" BIGINT NOT NULL)"}, "hr_staffsalary": {"columns": [["id", "bigint", null, 64, 0, "NO", null, 1], ["basic_salary", "numeric", null, 10, 2, "NO", null, 2], ["effective_from", "date", null, null, null, "NO", null, 3], ["updated_at", "timestamp with time zone", null, null, null, "NO", null, 4], ["staff_member_id", "bigint", null, 64, 0, "NO", null, 5], ["grade_id", "bigint", null, 64, 0, "YES", null, 6]], "create_sql": "CREATE TABLE IF NOT EXISTS \"hr_staffsalary\" (\"id\" BIGINT NOT NULL, \"basic_salary\" NUMERIC(10,2) NOT NULL, \"effective_from\" DATE NOT NULL, \"updated_at\" TIMESTAMP WITH TIME ZONE NOT NULL, \"staff_member_id\" BIGINT NOT NULL, \"grade_id\" BIGINT)"}, "hr_staffsalarystructure": {"columns": [["id", "bigint", null, 64, 0, "NO", null, 1], ["effective_date", "date", null, null, null, "NO", null, 2], ["staff_user_id", "bigint", null, 64, 0, "NO", null, 3]], "create_sql": "CREATE TABLE IF NOT EXISTS \"hr_staffsalarystructure\" (\"id\" BIGINT NOT NULL, \"effective_date\" DATE NOT NULL, \"staff_user_id\" BIGINT NOT NULL)"}, "hr_statutorydeduction": {"columns": [["id", "bigint", null, 64, 0, "NO", null, 1], ["name", "character varying", 100, null, null, "NO", null, 2], ["employee_contribution_rate", "numeric", null, 5, 2, "NO", null, 3], ["employer_contribution_rate", "numeric", null, 5, 2, "NO", null, 4], ["is_active", "boolean", null, null, null, "NO", null, 5], ["payslip_label", "character varying", 20, null, null, "NO", null, 6]], "create_sql": "CREATE TABLE IF NOT EXISTS \"hr_statutorydeduction\" (\"id\" BIGINT NOT NULL, \"name\" VARCHAR(100) NOT NULL, \"employee_contribution_rate\" NUMERIC(5,2) NOT NULL, \"employer_contribution_rate\" NUMERIC(5,2) NOT NULL, \"is_active\" BOOLEAN NOT NULL, \"payslip_label\" VARCHAR(20) NOT NULL)"}, "hr_taxbracket": {"columns": [["id", "bigint", null, 64, 0, "NO", null, 1], ["name", "character varying", 100, null, null, "NO", null, 2], ["from_amount", "numeric", null, 12, 2, "NO", null, 3], ["to_amount", "numeric", null, 12, 2, "YES", null, 4], ["rate_percent", "numeric", null, 5, 2, "NO", null, 5], ["deduction_amount", "numeric", null, 12, 2, "NO", null, 6], ["is_active", "boolean", null, null, null, "NO", null, 7]], "create_sql": "CREATE TABLE IF NOT EXISTS \"hr_taxbracket\" (\"id\" BIGINT NOT NULL, \"name\" VARCHAR(100) NOT NULL, \"from_amount\" NUMERIC(12,2) NOT NULL, \"to_amount\" NUMERIC(12,2), \"rate_percent\" NUMERIC(5,2) NOT NULL, \"deduction_amount\" NUMERIC(12,2) NOT NULL, \"is_active\" BOOLEAN NOT NULL)"}, "payments_payment": {"columns": [["id", "bigint", null, 64, 0, "NO", null, 1], ["amount", "numeric", null, 12, 2, "NO", null, 2], ["payment_date", "timestamp with time zone", null, null, null, "NO", null, 3], ["transaction_reference", "character varying", 150, null, null, "YES", null, 4], ["notes", "text", null, null, null, "YES", null, 5], ["payment_type", "character varying", 20, null, null, "NO", null, 6], ["status", "character varying", 20, null, null, "NO", null, 7], ["unallocated_amount", "numeric", null, 12, 2, "NO", null, 8], ["created_at", "timestamp with time zone", null, null, null, "NO", null, 9], ["updated_at", "timestamp with time zone", null, null, null, "NO", null, 10], ["academic_year_id", "bigint", null, 64, 0, "YES", null, 11], ["created_by_id", "bigint", null, 64, 0, "YES", null, 12], ["parent_payer_id", "bigint", null, 64, 0, "YES", null, 13], ["processed_by_staff_id", "bigint", null, 64, 0, "YES", null, 14], ["student_id", "bigint", null, 64, 0, "YES", null, 15], ["payment_method_id", "bigint", null, 64, 0, "YES", null, 16], ["receipt_number", "character varying", 20, null, null, "YES", null, 17], ["payment_reference", "character varying", 100, null, null, "YES", "''::character varying", 18]], "create_sql": "CREATE TABLE IF NOT EXISTS \"payments_payment\" (\"id\" BIGINT NOT NULL, \"amount\" NUMERIC(12,2) NOT NULL, \"payment_date\" TIMESTAMP WITH TIME ZONE NOT NULL, \"transaction_reference\" VARCHAR(150), \"notes\" TEXT, \"payment_type\" VARCHAR(20) NOT NULL, \"status\" VARCHAR(20) NOT NULL, \"unallocated_amount\" NUMERIC(12,2) NOT NULL, \"created_at\" TIMESTAMP WITH TIME ZONE NOT NULL, \"updated_at\" TIMESTAMP WITH TIME ZONE NOT NULL, \"academic_year_id\" BIGINT, \"created_by_id\" BIGINT, \"parent_payer_id\" BIGINT, \"processed_by_staff_id\" BIGINT, \"student_id\" BIGINT, \"payment_method_id\" BIGINT, \"receipt_number\" VARCHAR(20), \"payment_reference\" VARCHAR(100) DEFAULT ''::character varying)"}, "payments_paymentallocation": {"columns": [["id", "bigint", null, 64, 0, "NO", null, 1], ["amount_allocated", "numeric", null, 12, 2, "NO", null, 2], ["allocation_date", "date", null, null, null, "NO", null, 3], ["created_at", "timestamp with time zone", null, null, null, "NO", null, 4], ["invoice_id", "bigint", null, 64, 0, "YES", null, 5], ["payment_id", "bigint", null, 64, 0, "YES", null, 6]], "create_sql": "CREATE TABLE IF NOT EXISTS \"payments_paymentallocation\" (\"id\" BIGINT NOT NULL, \"amount_allocated\" NUMERIC(12,2) NOT NULL, \"allocation_date\" DATE NOT NULL, \"created_at\" TIMESTAMP WITH TIME ZONE NOT NULL, \"invoice_id\" BIGINT, \"payment_id\" BIGINT)"}, "payments_paymentmethod": {"columns": [["id", "bigint", null, 64, 0, "NO", null, 1], ["created_at", "timestamp with time zone", null, null, null, "YES", null, 2], ["updated_at", "timestamp with time zone", null, null, null, "YES", null, 3], ["name", "character varying", 100, null, null, "NO", null, 4], ["type", "character varying", 30, null, null, "NO", null, 5], ["description", "text", null, null, null, "YES", null, 6], ["is_active", "boolean", null, null, null, "NO", null, 7], ["linked_account_id", "bigint", null, 64, 0, "YES", null, 8]], "create_sql": "CREATE TABLE IF NOT EXISTS \"payments_paymentmethod\" (\"id\" BIGINT NOT NULL, \"created_at\" TIMESTAMP WITH TIME ZONE, \"updated_at\" TIMESTAMP WITH TIME ZONE, \"name\" VARCHAR(100) NOT NULL, \"type\" VARCHAR(30) NOT NULL, \"description\" TEXT, \"is_active\" BOOLEAN NOT NULL, \"linked_account_id\" BIGINT)"}, "portal_admin_adminactivitylog": {"columns": [["id", "bigint", null, 64, 0, "NO", null, 1], ["action_type", "character varying", 50, null, null, "NO", null, 2], ["timestamp", "timestamp with time zone", null, null, null, "NO", null, 3], ["actor_description", "character varying", 255, null, null, "YES", null, 4], ["ip_address", "inet", null, null, null, "YES", null, 5], ["user_agent", "text", null, null, null, "NO", null, 6], ["target_object_id", "character varying", 255, null, null, "YES", null, 7], ["target_object_repr", "character varying", 300, null, null, "YES", null, 8], ["description", "text", null, null, null, "NO", null, 9], ["staff_user_id", "bigint", null, 64, 0, "YES", null, 10], ["target_content_type_id", "integer", null, 32, 0, "YES", null, 11], ["tenant_id", "bigint", null, 64, 0, "YES", null, 12], ["user_id", "bigint", null, 64, 0, "YES", null, 13]], "create_sql": "CREATE TABLE IF NOT EXISTS \"portal_admin_adminactivitylog\" (\"id\" BIGINT NOT NULL, \"action_type\" VARCHAR(50) NOT NULL, \"timestamp\" TIMESTAMP WITH TIME ZONE NOT NULL, \"actor_description\" VARCHAR(255), \"ip_address\" INET, \"user_agent\" TEXT NOT NULL, \"target_object_id\" VARCHAR(255), \"target_object_repr\" VARCHAR(300), \"description\" TEXT NOT NULL, \"staff_user_id\" BIGINT, \"target_content_type_id\" INTEGER, \"tenant_id\" BIGINT, \"user_id\" BIGINT)"}, "school_calendar_eventattendee": {"columns": [["id", "bigint", null, 64, 0, "NO", null, 1], ["rsvp_status", "character varying", 15, null, null, "NO", null, 2], ["rsvp_date", "timestamp with time zone", null, null, null, "NO", null, 3], ["notes", "text", null, null, null, "NO", null, 4], ["user_id", "bigint", null, 64, 0, "NO", null, 5], ["event_id", "bigint", null, 64, 0, "NO", null, 6]], "create_sql": "CREATE TABLE IF NOT EXISTS \"school_calendar_eventattendee\" (\"id\" BIGINT NOT NULL, \"rsvp_status\" VARCHAR(15) NOT NULL, \"rsvp_date\" TIMESTAMP WITH TIME ZONE NOT NULL, \"notes\" TEXT NOT NULL, \"user_id\" BIGINT NOT NULL, \"event_id\" BIGINT NOT NULL)"}, "school_calendar_eventcategory": {"columns": [["id", "bigint", null, 64, 0, "NO", null, 1], ["name", "character varying", 100, null, null, "NO", null, 2], ["color", "character varying", 7, null, null, "NO", null, 3], ["icon", "character varying", 50, null, null, "NO", null, 4], ["description", "text", null, null, null, "NO", null, 5], ["is_active", "boolean", null, null, null, "NO", null, 6], ["created_at", "timestamp with time zone", null, null, null, "NO", null, 7], ["updated_at", "timestamp with time zone", null, null, null, "NO", null, 8]], "create_sql": "CREATE TABLE IF NOT EXISTS \"school_calendar_eventcategory\" (\"id\" BIGINT NOT NULL, \"name\" VARCHAR(100) NOT NULL, \"color\" VARCHAR(7) NOT NULL, \"icon\" VARCHAR(50) NOT NULL, \"description\" TEXT NOT NULL, \"is_active\" BOOLEAN NOT NULL, \"created_at\" TIMESTAMP WITH TIME ZONE NOT NULL, \"updated_at\" TIMESTAMP WITH TIME ZONE NOT NULL)"}, "school_calendar_schoolevent": {"columns": [["id", "bigint", null, 64, 0, "NO", null, 1], ["title", "character varying", 200, null, null, "NO", null, 2], ["description", "text", null, null, null, "NO", null, 3], ["event_type", "character varying", 20, null, null, "NO", null, 4], ["priority", "character varying", 10, null, null, "NO", null, 5], ["start_date", "date", null, null, null, "NO", null, 6], ["end_date", "date", null, null, null, "NO", null, 7], ["start_time", "time without time zone", null, null, null, "YES", null, 8], ["end_time", "time without time zone", null, null, null, "YES", null, 9], ["is_all_day", "boolean", null, null, null, "NO", null, 10], ["location", "character varying", 200, null, null, "NO", null, 11], ["venue_details", "text", null, null, null, "NO", null, 12], ["recurrence", "character varying", 10, null, null, "NO", null, 13], ["recurrence_end_date", "date", null, null, null, "YES", null, 14], ["is_public", "boolean", null, null, null, "NO", null, 15], ["visible_to_parents", "boolean", null, null, null, "NO", null, 16], ["visible_to_staff", "boolean", null, null, null, "NO", null, 17], ["visible_to_students", "boolean", null, null, null, "NO", null, 18], ["created_at", "timestamp with time zone", null, null, null, "NO", null, 19], ["updated_at", "timestamp with time zone", null, null, null, "NO", null, 20], ["is_active", "boolean", null, null, null, "NO", null, 21], ["requires_rsvp", "boolean", null, null, null, "NO", null, 22], ["max_attendees", "integer", null, 32, 0, "YES", null, 23], ["contact_person", "character varying", 100, null, null, "NO", null, 24], ["contact_email", "character varying", 254, null, null, "NO", null, 25], ["contact_phone", "character varying", 20, null, null, "NO", null, 26], ["category_id", "bigint", null, 64, 0, "YES", null, 27], ["created_by_id", "bigint", null, 64, 0, "YES", null, 28], ["created_by_staff_id", "bigint", null, 64, 0, "YES", null, 29]], "create_sql": "CREATE TABLE IF NOT EXISTS \"school_calendar_schoolevent\" (\"id\" BIGINT NOT NULL, \"title\" VARCHAR(200) NOT NULL, \"description\" TEXT NOT NULL, \"event_type\" VARCHAR(20) NOT NULL, \"priority\" VARCHAR(10) NOT NULL, \"start_date\" DATE NOT NULL, \"end_date\" DATE NOT NULL, \"start_time\" TIME WITHOUT TIME ZONE, \"end_time\" TIME WITHOUT TIME ZONE, \"is_all_day\" BOOLEAN NOT NULL, \"location\" VARCHAR(200) NOT NULL, \"venue_details\" TEXT NOT NULL, \"recurrence\" VARCHAR(10) NOT NULL, \"recurrence_end_date\" DATE, \"is_public\" BOOLEAN NOT NULL, \"visible_to_parents\" BOOLEAN NOT NULL, \"visible_to_staff\" BOOLEAN NOT NULL, \"visible_to_students\" BOOLEAN NOT NULL, \"created_at\" TIMES<PERSON>MP WITH TIME ZONE NOT NULL, \"updated_at\" TIMESTAMP WITH TIME ZONE NOT NULL, \"is_active\" BOOLEAN NOT NULL, \"requires_rsvp\" BOOLEAN NOT NULL, \"max_attendees\" INTEGER, \"contact_person\" VARCHAR(100) NOT NULL, \"contact_email\" VARCHAR(254) NOT NULL, \"contact_phone\" VARCHAR(20) NOT NULL, \"category_id\" BIGINT, \"created_by_id\" BIGINT, \"created_by_staff_id\" BIGINT)"}, "schools_academicsetting": {"columns": [["id", "bigint", null, 64, 0, "NO", null, 1], ["grading_system", "character varying", 20, null, null, "NO", null, 2], ["current_academic_year_id", "bigint", null, 64, 0, "YES", null, 3]], "create_sql": "CREATE TABLE IF NOT EXISTS \"schools_academicsetting\" (\"id\" BIGINT NOT NULL, \"grading_system\" VARCHAR(20) NOT NULL, \"current_academic_year_id\" BIGINT)"}, "schools_academicyear": {"columns": [["id", "bigint", null, 64, 0, "NO", null, 1], ["name", "character varying", 100, null, null, "NO", null, 2], ["start_date", "date", null, null, null, "NO", null, 3], ["end_date", "date", null, null, null, "NO", null, 4], ["is_active", "boolean", null, null, null, "NO", null, 5], ["is_current", "boolean", null, null, null, "NO", null, 6], ["created_at", "timestamp with time zone", null, null, null, "NO", null, 7], ["updated_at", "timestamp with time zone", null, null, null, "NO", null, 8]], "create_sql": "CREATE TABLE IF NOT EXISTS \"schools_academicyear\" (\"id\" BIGINT NOT NULL, \"name\" VARCHAR(100) NOT NULL, \"start_date\" DATE NOT NULL, \"end_date\" DATE NOT NULL, \"is_active\" BOOLEAN NOT NULL, \"is_current\" BOOLEAN NOT NULL, \"created_at\" TIMESTAMP WITH TIME ZONE NOT NULL, \"updated_at\" TIMESTAMP WITH TIME ZONE NOT NULL)"}, "schools_country": {"columns": [["id", "bigint", null, 64, 0, "NO", null, 1], ["name", "character varying", 100, null, null, "NO", null, 2], ["code", "character varying", 3, null, null, "NO", null, 3]], "create_sql": "CREATE TABLE IF NOT EXISTS \"schools_country\" (\"id\" BIGINT NOT NULL, \"name\" VARCHAR(100) NOT NULL, \"code\" VARCHAR(3) NOT NULL)"}, "schools_invoicesequence": {"columns": [["id", "bigint", null, 64, 0, "NO", null, 1], ["prefix", "character varying", 10, null, null, "NO", null, 2], ["last_number", "integer", null, 32, 0, "NO", null, 3], ["padding_digits", "smallint", null, 16, 0, "NO", null, 4], ["last_updated", "timestamp with time zone", null, null, null, "NO", null, 5], ["created_at", "timestamp with time zone", null, null, null, "NO", null, 6]], "create_sql": "CREATE TABLE IF NOT EXISTS \"schools_invoicesequence\" (\"id\" BIGINT NOT NULL, \"prefix\" VARCHAR(10) NOT NULL, \"last_number\" INTEGER NOT NULL, \"padding_digits\" SMALLINT NOT NULL, \"last_updated\" TIMES<PERSON>MP WITH TIME ZONE NOT NULL, \"created_at\" TIMESTAMP WITH TIME ZONE NOT NULL)"}, "schools_receiptsequence": {"columns": [["id", "bigint", null, 64, 0, "NO", null, 1], ["prefix", "character varying", 10, null, null, "NO", null, 2], ["last_number", "integer", null, 32, 0, "NO", null, 3], ["padding_digits", "smallint", null, 16, 0, "NO", null, 4], ["last_updated", "timestamp with time zone", null, null, null, "NO", null, 5], ["created_at", "timestamp with time zone", null, null, null, "NO", null, 6]], "create_sql": "CREATE TABLE IF NOT EXISTS \"schools_receiptsequence\" (\"id\" BIGINT NOT NULL, \"prefix\" VARCHAR(10) NOT NULL, \"last_number\" INTEGER NOT NULL, \"padding_digits\" SMALLINT NOT NULL, \"last_updated\" TIMES<PERSON>MP WITH TIME ZONE NOT NULL, \"created_at\" TIMESTAMP WITH TIME ZONE NOT NULL)"}, "schools_schoolclass": {"columns": [["id", "bigint", null, 64, 0, "NO", null, 1], ["name", "character varying", 100, null, null, "NO", null, 2], ["description", "text", null, null, null, "YES", null, 3], ["is_active", "boolean", null, null, null, "NO", null, 4], ["created_at", "timestamp with time zone", null, null, null, "NO", null, 5], ["updated_at", "timestamp with time zone", null, null, null, "NO", null, 6]], "create_sql": "CREATE TABLE IF NOT EXISTS \"schools_schoolclass\" (\"id\" BIGINT NOT NULL, \"name\" VARCHAR(100) NOT NULL, \"description\" TEXT, \"is_active\" BOOLEAN NOT NULL, \"created_at\" TIMESTAMP WITH TIME ZONE NOT NULL, \"updated_at\" TIMESTAMP WITH TIME ZONE NOT NULL)"}, "schools_schoolprofile": {"columns": [["school_name_override", "character varying", 255, null, null, "YES", null, 2], ["school_motto", "character varying", 255, null, null, "YES", null, 3], ["logo", "character varying", 100, null, null, "YES", null, 4], ["school_email", "character varying", 254, null, null, "YES", null, 5], ["phone_number", "character varying", 30, null, null, "YES", null, 6], ["address_line1", "character varying", 255, null, null, "YES", null, 7], ["address_line2", "character varying", 255, null, null, "YES", null, 8], ["city", "character varying", 100, null, null, "YES", null, 9], ["state_province", "character varying", 100, null, null, "YES", null, 10], ["postal_code", "character varying", 20, null, null, "YES", null, 11], ["country_name", "character varying", 100, null, null, "YES", null, 12], ["financial_year_start_month", "smallint", null, 16, 0, "NO", null, 13], ["currency_symbol", "character varying", 5, null, null, "YES", null, 14], ["school_name_on_reports", "character varying", 255, null, null, "YES", null, 15], ["default_due_days", "integer", null, 32, 0, "NO", null, 16], ["created_at", "timestamp with time zone", null, null, null, "NO", null, 17], ["updated_at", "timestamp with time zone", null, null, null, "NO", null, 18], ["current_academic_year_id", "bigint", null, 64, 0, "YES", null, 19], ["default_accounts_receivable_coa_id", "bigint", null, 64, 0, "YES", null, 20], ["default_bank_coa_id", "bigint", null, 64, 0, "YES", null, 21], ["default_cash_coa_id", "bigint", null, 64, 0, "YES", null, 22], ["default_discount_given_coa_id", "bigint", null, 64, 0, "YES", null, 23], ["default_expense_coa_id", "bigint", null, 64, 0, "YES", null, 24], ["default_fee_income_coa_id", "bigint", null, 64, 0, "YES", null, 25], ["id", "bigint", null, 64, 0, "NO", null, 26]], "create_sql": "CREATE TABLE IF NOT EXISTS \"schools_schoolprofile\" (\"school_name_override\" VARCHAR(255), \"school_motto\" VARCHAR(255), \"logo\" VARCHAR(100), \"school_email\" VARCHAR(254), \"phone_number\" VARCHAR(30), \"address_line1\" VARCHAR(255), \"address_line2\" VARCHAR(255), \"city\" VARCHAR(100), \"state_province\" VARCHAR(100), \"postal_code\" VARCHAR(20), \"country_name\" VARCHAR(100), \"financial_year_start_month\" SMALLINT NOT NULL, \"currency_symbol\" VARCHAR(5), \"school_name_on_reports\" VARCHAR(255), \"default_due_days\" INTEGER NOT NULL, \"created_at\" TIMESTAMP WITH TIME ZONE NOT NULL, \"updated_at\" TIMESTAMP WITH TIME ZONE NOT NULL, \"current_academic_year_id\" BIGINT, \"default_accounts_receivable_coa_id\" BIGINT, \"default_bank_coa_id\" BIGINT, \"default_cash_coa_id\" BIGINT, \"default_discount_given_coa_id\" BIGINT, \"default_expense_coa_id\" BIGINT, \"default_fee_income_coa_id\" BIGINT, \"id\" BIGINT NOT NULL)"}, "schools_section": {"columns": [["id", "bigint", null, 64, 0, "NO", null, 1], ["name", "character varying", 50, null, null, "NO", null, 2], ["room_number", "character varying", 50, null, null, "NO", null, 3], ["school_class_id", "bigint", null, 64, 0, "NO", null, 4], ["class_teacher_id", "bigint", null, 64, 0, "YES", null, 5]], "create_sql": "CREATE TABLE IF NOT EXISTS \"schools_section\" (\"id\" BIGINT NOT NULL, \"name\" VARCHAR(50) NOT NULL, \"room_number\" VARCHAR(50) NOT NULL, \"school_class_id\" BIGINT NOT NULL, \"class_teacher_id\" BIGINT)"}, "schools_staffuser": {"columns": [["id", "bigint", null, 64, 0, "NO", null, 1], ["password", "character varying", 128, null, null, "NO", null, 2], ["last_login", "timestamp with time zone", null, null, null, "YES", null, 3], ["is_superuser", "boolean", null, null, null, "NO", null, 4], ["email", "character varying", 254, null, null, "NO", null, 5], ["first_name", "character varying", 150, null, null, "NO", null, 6], ["last_name", "character varying", 150, null, null, "NO", null, 8], ["is_active", "boolean", null, null, null, "NO", null, 28], ["is_staff", "boolean", null, null, null, "NO", null, 29], ["date_joined", "timestamp with time zone", null, null, null, "NO", null, 30], ["employee_id", "character varying", 50, null, null, "YES", "''::character varying", 32], ["designation", "character varying", 100, null, null, "YES", "''::character varying", 33]], "create_sql": "CREATE TABLE IF NOT EXISTS \"schools_staffuser\" (\"id\" BIGINT NOT NULL, \"password\" VARCHAR(128) NOT NULL, \"last_login\" TIMESTAMP WITH TIME ZONE, \"is_superuser\" BOOLEAN NOT NULL, \"email\" VARCHAR(254) NOT NULL, \"first_name\" VARCHAR(150) NOT NULL, \"last_name\" VARCHAR(150) NOT NULL, \"is_active\" BOOLEAN NOT NULL, \"is_staff\" BOOLEAN NOT NULL, \"date_joined\" TIMESTAMP WITH TIME ZONE NOT NULL, \"employee_id\" VARCHAR(50) DEFAULT ''::character varying, \"designation\" VARCHAR(100) DEFAULT ''::character varying)"}, "schools_staffuser_groups": {"columns": [["id", "bigint", null, 64, 0, "NO", null, 1], ["staffuser_id", "bigint", null, 64, 0, "NO", null, 2], ["group_id", "integer", null, 32, 0, "NO", null, 3]], "create_sql": "CREATE TABLE IF NOT EXISTS \"schools_staffuser_groups\" (\"id\" BIGINT NOT NULL, \"staffuser_id\" BIGINT NOT NULL, \"group_id\" INTEGER NOT NULL)"}, "schools_staffuser_user_permissions": {"columns": [["id", "bigint", null, 64, 0, "NO", null, 1], ["staffuser_id", "bigint", null, 64, 0, "NO", null, 2], ["permission_id", "integer", null, 32, 0, "NO", null, 3]], "create_sql": "CREATE TABLE IF NOT EXISTS \"schools_staffuser_user_permissions\" (\"id\" BIGINT NOT NULL, \"staffuser_id\" BIGINT NOT NULL, \"permission_id\" INTEGER NOT NULL)"}, "schools_term": {"columns": [["id", "bigint", null, 64, 0, "NO", null, 1], ["name", "character varying", 100, null, null, "NO", null, 2], ["start_date", "date", null, null, null, "NO", null, 3], ["end_date", "date", null, null, null, "NO", null, 4], ["is_active", "boolean", null, null, null, "NO", null, 5], ["created_at", "timestamp with time zone", null, null, null, "NO", null, 6], ["updated_at", "timestamp with time zone", null, null, null, "NO", null, 7], ["academic_year_id", "bigint", null, 64, 0, "NO", null, 8]], "create_sql": "CREATE TABLE IF NOT EXISTS \"schools_term\" (\"id\" BIGINT NOT NULL, \"name\" VARCHAR(100) NOT NULL, \"start_date\" DATE NOT NULL, \"end_date\" DATE NOT NULL, \"is_active\" BOOLEAN NOT NULL, \"created_at\" TIMESTAMP WITH TIME ZONE NOT NULL, \"updated_at\" TIMESTAMP WITH TIME ZONE NOT NULL, \"academic_year_id\" BIGINT NOT NULL)"}, "students_parentuser": {"columns": [["id", "bigint", null, 64, 0, "NO", null, 1], ["password", "character varying", 128, null, null, "NO", null, 2], ["last_login", "timestamp with time zone", null, null, null, "YES", null, 3], ["is_superuser", "boolean", null, null, null, "NO", null, 4], ["email", "character varying", 254, null, null, "NO", null, 5], ["username", "character varying", 150, null, null, "YES", null, 6], ["first_name", "character varying", 150, null, null, "NO", null, 7], ["last_name", "character varying", 150, null, null, "NO", null, 8], ["phone_number", "character varying", 20, null, null, "NO", null, 9], ["address_line1", "character varying", 255, null, null, "YES", null, 10], ["address_line2", "character varying", 255, null, null, "YES", null, 11], ["city", "character varying", 100, null, null, "YES", null, 12], ["state_province", "character varying", 100, null, null, "YES", null, 13], ["postal_code", "character varying", 20, null, null, "YES", null, 14], ["country", "character varying", 100, null, null, "YES", null, 15], ["profile_picture", "character varying", 100, null, null, "YES", null, 16], ["is_active", "boolean", null, null, null, "NO", null, 17], ["is_staff", "boolean", null, null, null, "NO", null, 18], ["date_joined", "timestamp with time zone", null, null, null, "NO", null, 19]], "create_sql": "CREATE TABLE IF NOT EXISTS \"students_parentuser\" (\"id\" BIGINT NOT NULL, \"password\" VA<PERSON>HA<PERSON>(128) NOT NULL, \"last_login\" TIMESTAMP WITH TIME ZONE, \"is_superuser\" BOOLEAN NOT NULL, \"email\" VA<PERSON>HA<PERSON>(254) NOT NULL, \"username\" <PERSON><PERSON><PERSON><PERSON>(150), \"first_name\" VA<PERSON><PERSON><PERSON>(150) NOT NULL, \"last_name\" VARCHAR(150) NOT NULL, \"phone_number\" VARCHAR(20) NOT NULL, \"address_line1\" VARCHAR(255), \"address_line2\" VARCHAR(255), \"city\" VARCHAR(100), \"state_province\" VARCHAR(100), \"postal_code\" VARCHAR(20), \"country\" VARCHAR(100), \"profile_picture\" VARCHAR(100), \"is_active\" BOOLEAN NOT NULL, \"is_staff\" BOOLEAN NOT NULL, \"date_joined\" TIMESTAMP WITH TIME ZONE NOT NULL)"}, "students_parentuser_groups": {"columns": [["id", "bigint", null, 64, 0, "NO", null, 1], ["parentuser_id", "bigint", null, 64, 0, "NO", null, 2], ["group_id", "integer", null, 32, 0, "NO", null, 3]], "create_sql": "CREATE TABLE IF NOT EXISTS \"students_parentuser_groups\" (\"id\" BIGINT NOT NULL, \"parentuser_id\" BIGINT NOT NULL, \"group_id\" INTEGER NOT NULL)"}, "students_parentuser_user_permissions": {"columns": [["id", "bigint", null, 64, 0, "NO", null, 1], ["parentuser_id", "bigint", null, 64, 0, "NO", null, 2], ["permission_id", "integer", null, 32, 0, "NO", null, 3]], "create_sql": "CREATE TABLE IF NOT EXISTS \"students_parentuser_user_permissions\" (\"id\" BIGINT NOT NULL, \"parentuser_id\" BIGINT NOT NULL, \"permission_id\" INTEGER NOT NULL)"}, "students_student": {"columns": [["id", "bigint", null, 64, 0, "NO", null, 1], ["admission_number", "character varying", 50, null, null, "NO", null, 2], ["first_name", "character varying", 100, null, null, "NO", null, 3], ["middle_name", "character varying", 100, null, null, "NO", null, 4], ["last_name", "character varying", 100, null, null, "NO", null, 5], ["date_of_birth", "date", null, null, null, "YES", null, 6], ["gender", "character varying", 15, null, null, "NO", null, 7], ["photo", "character varying", 100, null, null, "YES", null, 8], ["date_of_admission", "date", null, null, null, "NO", null, 9], ["status", "character varying", 20, null, null, "NO", null, 10], ["is_active", "boolean", null, null, null, "NO", null, 11], ["roll_number", "character varying", 20, null, null, "NO", null, 12], ["student_email", "character varying", 254, null, null, "NO", null, 13], ["student_phone", "character varying", 30, null, null, "NO", null, 14], ["guardian1_full_name", "character varying", 150, null, null, "NO", null, 15], ["guardian1_relationship", "character varying", 50, null, null, "NO", null, 16], ["guardian1_phone", "character varying", 30, null, null, "NO", null, 17], ["guardian1_email", "character varying", 254, null, null, "NO", null, 18], ["guardian1_occupation", "character varying", 100, null, null, "NO", null, 19], ["guardian2_full_name", "character varying", 150, null, null, "NO", null, 20], ["guardian2_relationship", "character varying", 50, null, null, "NO", null, 21], ["guardian2_phone", "character varying", 30, null, null, "NO", null, 22], ["guardian2_email", "character varying", 254, null, null, "NO", null, 23], ["address_line1", "character varying", 255, null, null, "NO", null, 24], ["address_line2", "character varying", 255, null, null, "NO", null, 25], ["city", "character varying", 100, null, null, "NO", null, 26], ["state_province", "character varying", 100, null, null, "NO", null, 27], ["postal_code", "character varying", 20, null, null, "NO", null, 28], ["country", "character varying", 100, null, null, "NO", null, 29], ["blood_group", "character varying", 10, null, null, "NO", null, 30], ["allergies", "text", null, null, null, "NO", null, 31], ["medical_conditions", "text", null, null, null, "NO", null, 32], ["previous_school", "character varying", 200, null, null, "NO", null, 33], ["notes", "text", null, null, null, "NO", null, 34], ["created_at", "timestamp with time zone", null, null, null, "NO", null, 35], ["updated_at", "timestamp with time zone", null, null, null, "NO", null, 36], ["current_class_id", "bigint", null, 64, 0, "YES", null, 37], ["current_section_id", "bigint", null, 64, 0, "YES", null, 38], ["created_by_id", "bigint", null, 64, 0, "YES", null, 39], ["emergency_contact", "character varying", 200, null, null, "NO", null, 40], ["emergency_phone", "character varying", 20, null, null, "NO", null, 41], ["house", "character varying", 100, null, null, "NO", null, 42], ["nationality", "character varying", 100, null, null, "NO", null, 43], ["religion", "character varying", 100, null, null, "NO", null, 44], ["transport_mode", "character varying", 50, null, null, "NO", null, 45]], "create_sql": "CREATE TABLE IF NOT EXISTS \"students_student\" (\"id\" BIGINT NOT NULL, \"admission_number\" VARCHAR(50) NOT NULL, \"first_name\" VARCHAR(100) NOT NULL, \"middle_name\" VARCHAR(100) NOT NULL, \"last_name\" VARCHAR(100) NOT NULL, \"date_of_birth\" DATE, \"gender\" VARCHAR(15) NOT NULL, \"photo\" VARCHAR(100), \"date_of_admission\" DATE NOT NULL, \"status\" VARCHAR(20) NOT NULL, \"is_active\" BOOLEAN NOT NULL, \"roll_number\" VARCHAR(20) NOT NULL, \"student_email\" VARCHAR(254) NOT NULL, \"student_phone\" VARCHAR(30) NOT NULL, \"guardian1_full_name\" VARCHAR(150) NOT NULL, \"guardian1_relationship\" VARCHAR(50) NOT NULL, \"guardian1_phone\" VARCHAR(30) NOT NULL, \"guardian1_email\" VARCHAR(254) NOT NULL, \"guardian1_occupation\" VARCHAR(100) NOT NULL, \"guardian2_full_name\" VARCHAR(150) NOT NULL, \"guardian2_relationship\" VARCHAR(50) NOT NULL, \"guardian2_phone\" VARCHAR(30) NOT NULL, \"guardian2_email\" VARCHAR(254) NOT NULL, \"address_line1\" VARCHAR(255) NOT NULL, \"address_line2\" VARCHAR(255) NOT NULL, \"city\" VARCHAR(100) NOT NULL, \"state_province\" VARCHAR(100) NOT NULL, \"postal_code\" VARCHAR(20) NOT NULL, \"country\" VARCHAR(100) NOT NULL, \"blood_group\" VARCHAR(10) NOT NULL, \"allergies\" TEXT NOT NULL, \"medical_conditions\" TEXT NOT NULL, \"previous_school\" VARCHAR(200) NOT NULL, \"notes\" TEXT NOT NULL, \"created_at\" TIMESTAMP WITH TIME ZONE NOT NULL, \"updated_at\" TIMESTAMP WITH TIME ZONE NOT NULL, \"current_class_id\" BIGINT, \"current_section_id\" BIGINT, \"created_by_id\" BIGINT, \"emergency_contact\" VARCHAR(200) NOT NULL, \"emergency_phone\" VARCHAR(20) NOT NULL, \"house\" VARCHAR(100) NOT NULL, \"nationality\" VARCHAR(100) NOT NULL, \"religion\" VARCHAR(100) NOT NULL, \"transport_mode\" VARCHAR(50) NOT NULL)"}, "students_student_parents": {"columns": [["id", "bigint", null, 64, 0, "NO", null, 1], ["student_id", "bigint", null, 64, 0, "NO", null, 2], ["parentuser_id", "bigint", null, 64, 0, "NO", null, 3]], "create_sql": "CREATE TABLE IF NOT EXISTS \"students_student_parents\" (\"id\" BIGINT NOT NULL, \"student_id\" BIGINT NOT NULL, \"parentuser_id\" BIGINT NOT NULL)"}}, "essential_data": {"auth_group": {"columns": ["id", "name"], "rows": [[2, "Accountant"], [1, "School Administrators"], [3, "School Administrator"], [4, "Teacher"], [5, "HR Manager"], [6, "Librarian"], [7, "Reception/Front Desk"], [8, "Principal/Head Teacher"]]}, "auth_permission": {"columns": ["id", "name", "content_type_id", "codename"], "rows": [[1, "Can add log entry", 1, "add_logentry"], [2, "Can change log entry", 1, "change_logentry"], [3, "Can delete log entry", 1, "delete_logentry"], [4, "Can view log entry", 1, "view_logentry"], [5, "Can add permission", 2, "add_permission"], [6, "Can change permission", 2, "change_permission"], [7, "Can delete permission", 2, "delete_permission"], [8, "Can view permission", 2, "view_permission"], [9, "Can add group", 3, "add_group"], [10, "Can change group", 3, "change_group"], [11, "Can delete group", 3, "delete_group"], [12, "Can view group", 3, "view_group"], [13, "Can add content type", 4, "add_contenttype"], [14, "Can change content type", 4, "change_contenttype"], [15, "Can delete content type", 4, "delete_contenttype"], [16, "Can view content type", 4, "view_contenttype"], [17, "Can add session", 5, "add_session"], [18, "Can change session", 5, "change_session"], [19, "Can delete session", 5, "delete_session"], [20, "Can view session", 5, "view_session"], [21, "Can add School Domain", 6, "add_domain"], [22, "Can change School Domain", 6, "change_domain"], [23, "Can delete School Domain", 6, "delete_domain"], [24, "Can view School Domain", 6, "view_domain"], [25, "Can add School (Tenant)", 7, "add_school"], [26, "Can change School (Tenant)", 7, "change_school"], [27, "Can delete School (Tenant)", 7, "delete_school"], [28, "Can view School (Tenant)", 7, "view_school"], [29, "Can add Platform User", 8, "add_user"], [30, "Can change Platform User", 8, "change_user"], [31, "Can delete Platform User", 8, "delete_user"], [32, "Can view Platform User", 8, "view_user"], [33, "Can add contact inquiry", 9, "add_contactinquiry"], [34, "Can change contact inquiry", 9, "change_contactinquiry"], [35, "Can delete contact inquiry", 9, "delete_contactinquiry"], [36, "Can view contact inquiry", 9, "view_contactinquiry"], [37, "Can add Testimonial", 10, "add_testimonial"], [38, "Can change Testimonial", 10, "change_testimonial"], [39, "Can delete Testimonial", 10, "delete_testimonial"], [40, "Can view Testimonial", 10, "view_testimonial"], [41, "Can add Account Type", 11, "add_accounttype"], [42, "Can change Account Type", 11, "change_accounttype"], [43, "Can delete Account Type", 11, "delete_accounttype"], [44, "Can view Account Type", 11, "view_accounttype"], [45, "Can add General Ledger Transaction", 12, "add_generalledger"], [46, "Can change General Ledger Transaction", 12, "change_generalledger"], [47, "Can delete General Ledger Transaction", 12, "delete_generalledger"], [48, "Can view General Ledger Transaction", 12, "view_generalledger"], [49, "Can add Journal Entry", 13, "add_journalentry"], [50, "Can change Journal Entry", 13, "change_journalentry"], [51, "Can delete Journal Entry", 13, "delete_journalentry"], [52, "Can view Journal Entry", 13, "view_journalentry"], [53, "Can add Journal Entry Item", 14, "add_journalentryitem"], [54, "Can change Journal Entry Item", 14, "change_journalentryitem"], [55, "Can delete Journal Entry Item", 14, "delete_journalentryitem"], [56, "Can view Journal Entry Item", 14, "view_journalentryitem"], [57, "Can add Journal Line", 15, "add_journalline"], [58, "Can change Journal Line", 15, "change_journalline"], [59, "Can delete Journal Line", 15, "delete_journalline"], [60, "Can view Journal Line", 15, "view_journalline"], [61, "Can add Chart of Account Entry", 16, "add_account"], [62, "Can change Chart of Account Entry", 16, "change_account"], [63, "Can delete Chart of Account Entry", 16, "delete_account"], [64, "Can view Chart of Account Entry", 16, "view_account"], [65, "Can add Plan Feature", 17, "add_feature"], [66, "Can change Plan Feature", 17, "change_feature"], [67, "Can delete Plan Feature", 17, "delete_feature"], [68, "Can view Plan Feature", 17, "view_feature"], [69, "Can add School Subscription", 18, "add_subscription"], [70, "Can change School Subscription", 18, "change_subscription"], [71, "Can delete School Subscription", 18, "delete_subscription"], [72, "Can view School Subscription", 18, "view_subscription"], [73, "Can add Subscription Plan", 19, "add_subscriptionplan"], [74, "Can change Subscription Plan", 19, "change_subscriptionplan"], [75, "Can delete Subscription Plan", 19, "delete_subscriptionplan"], [76, "Can view Subscription Plan", 19, "view_subscriptionplan"], [77, "Can add crontab", 20, "add_crontabschedule"], [78, "Can change crontab", 20, "change_crontabschedule"], [79, "Can delete crontab", 20, "delete_crontabschedule"], [80, "Can view crontab", 20, "view_crontabschedule"], [81, "Can add interval", 21, "add_intervalschedule"], [82, "Can change interval", 21, "change_intervalschedule"], [83, "Can delete interval", 21, "delete_intervalschedule"], [84, "Can view interval", 21, "view_intervalschedule"], [85, "Can add periodic task", 22, "add_periodictask"], [86, "Can change periodic task", 22, "change_periodictask"], [87, "Can delete periodic task", 22, "delete_periodictask"], [88, "Can view periodic task", 22, "view_periodictask"], [89, "Can add periodic task track", 23, "add_periodictasks"], [90, "Can change periodic task track", 23, "change_periodictasks"], [91, "Can delete periodic task track", 23, "delete_periodictasks"], [92, "Can view periodic task track", 23, "view_periodictasks"], [93, "Can add solar event", 24, "add_solarschedule"], [94, "Can change solar event", 24, "change_solarschedule"], [95, "Can delete solar event", 24, "delete_solarschedule"], [96, "Can view solar event", 24, "view_solarschedule"], [97, "Can add clocked", 25, "add_clockedschedule"], [98, "Can change clocked", 25, "change_clockedschedule"], [99, "Can delete clocked", 25, "delete_clockedschedule"], [100, "Can view clocked", 25, "view_clockedschedule"], [101, "Can add Academic Setting", 26, "add_academicsetting"], [102, "Can change Academic Setting", 26, "change_academicsetting"], [103, "Can delete Academic Setting", 26, "delete_academicsetting"], [104, "Can view Academic Setting", 26, "view_academicsetting"], [105, "Can add Academic Year", 27, "add_academicyear"], [106, "Can change Academic Year", 27, "change_academicyear"], [107, "Can delete Academic Year", 27, "delete_academicyear"], [108, "Can view Academic Year", 27, "view_academicyear"], [109, "Can add country", 28, "add_country"], [110, "Can change country", 28, "change_country"], [111, "Can delete country", 28, "delete_country"], [112, "Can view country", 28, "view_country"], [113, "Can add School Class", 29, "add_schoolclass"], [114, "Can change School Class", 29, "change_schoolclass"], [115, "Can delete School Class", 29, "delete_schoolclass"], [116, "Can view School Class", 29, "view_schoolclass"], [117, "Can add School Profile", 30, "add_schoolprofile"], [118, "Can change School Profile", 30, "change_schoolprofile"], [119, "Can delete School Profile", 30, "delete_schoolprofile"], [120, "Can view School Profile", 30, "view_schoolprofile"], [121, "Can view outstanding fees report", 30, "view_outstanding_fees_report"], [122, "Can view fee collection report", 30, "view_fee_collection_report"], [123, "Can view expense report", 30, "view_expense_report"], [124, "Can view revenue report", 30, "view_revenue_report"], [125, "Can manage school profile settings", 30, "manage_school_profile"], [126, "Can manage academic year and term settings", 30, "manage_academic_settings"], [127, "Can add staff member", 31, "add_staffuser"], [128, "Can change staff member", 31, "change_staffuser"], [129, "Can delete staff member", 31, "delete_staffuser"], [130, "Can view staff member", 31, "view_staffuser"], [131, "Can add Section", 32, "add_section"], [132, "Can change Section", 32, "change_section"], [133, "Can delete Section", 32, "delete_section"], [134, "Can view Section", 32, "view_section"], [135, "Can add Term / Semester", 33, "add_term"], [136, "Can change Term / Semester", 33, "change_term"], [137, "Can delete Term / Semester", 33, "delete_term"], [138, "Can view Term / Semester", 33, "view_term"], [139, "Can add parent user", 34, "add_parentuser"], [140, "Can change parent user", 34, "change_parentuser"], [141, "Can delete parent user", 34, "delete_parentuser"], [142, "Can view parent user", 34, "view_parentuser"], [143, "Can add student", 35, "add_student"], [144, "Can change student", 35, "change_student"], [145, "Can delete student", 35, "delete_student"], [146, "Can view student", 35, "view_student"], [147, "Can add employee HR profile", 36, "add_employeeprofile"], [148, "Can change employee HR profile", 36, "change_employeeprofile"], [149, "Can delete employee HR profile", 36, "delete_employeeprofile"], [150, "Can view employee HR profile", 36, "view_employeeprofile"], [151, "Can add leave type", 37, "add_leavetype"], [152, "Can change leave type", 37, "change_leavetype"], [153, "Can delete leave type", 37, "delete_leavetype"], [154, "Can view leave type", 37, "view_leavetype"], [155, "Can add leave request", 38, "add_leaverequest"], [156, "Can change leave request", 38, "change_leaverequest"], [157, "Can delete leave request", 38, "delete_leaverequest"], [158, "Can view leave request", 38, "view_leaverequest"], [159, "Can add leave balance", 39, "add_leavebalance"], [160, "Can change leave balance", 39, "change_leavebalance"], [161, "Can delete leave balance", 39, "delete_leavebalance"], [162, "Can view leave balance", 39, "view_leavebalance"], [163, "Can add Account", 40, "add_account"], [164, "Can change Account", 40, "change_account"], [165, "Can delete Account", 40, "delete_account"], [166, "Can view Account", 40, "view_account"], [167, "Can add Concession Type", 41, "add_concessiontype"], [168, "Can change Concession Type", 41, "change_concessiontype"], [169, "Can delete Concession Type", 41, "delete_concessiontype"], [170, "Can view Concession Type", 41, "view_concessiontype"], [171, "Can add Fee Structure", 42, "add_feestructure"], [172, "Can change Fee Structure", 42, "change_feestructure"], [173, "Can delete Fee Structure", 42, "delete_feestructure"], [174, "Can view Fee Structure", 42, "view_feestructure"], [175, "Can add Fee Structure Item", 43, "add_feestructureitem"], [176, "Can change Fee Structure Item", 43, "change_feestructureitem"], [177, "Can delete Fee Structure Item", 43, "delete_feestructureitem"], [178, "Can view Fee Structure Item", 43, "view_feestructureitem"], [179, "Can add Invoice", 44, "add_invoice"], [180, "Can change Invoice", 44, "change_invoice"], [181, "Can delete Invoice", 44, "delete_invoice"], [182, "Can view Invoice", 44, "view_invoice"], [183, "Can add Invoice Detail", 45, "add_invoicedetail"], [184, "Can change Invoice Detail", 45, "change_invoicedetail"], [185, "Can delete Invoice Detail", 45, "delete_invoicedetail"], [186, "Can view Invoice Detail", 45, "view_invoicedetail"], [187, "Can add Student Specific Concession", 46, "add_studentconcession"], [188, "Can change Student Specific Concession", 46, "change_studentconcession"], [189, "Can delete Student Specific Concession", 46, "delete_studentconcession"], [190, "Can view Student Specific Concession", 46, "view_studentconcession"], [191, "Can add Student Fee Allocation", 47, "add_studentfeeallocation"], [192, "Can change Student Fee Allocation", 47, "change_studentfeeallocation"], [193, "Can delete Student Fee Allocation", 47, "delete_studentfeeallocation"], [194, "Can view Student Fee Allocation", 47, "view_studentfeeallocation"], [195, "Can add Fee Head", 48, "add_feehead"], [196, "Can change Fee Head", 48, "change_feehead"], [197, "Can delete Fee Head", 48, "delete_feehead"], [198, "Can view Fee Head", 48, "view_feehead"], [199, "Can add budget", 49, "add_budget"], [200, "Can change budget", 49, "change_budget"], [201, "Can delete budget", 49, "delete_budget"], [202, "Can view budget", 49, "view_budget"], [203, "Can add Budgeted Amount", 50, "add_budgetamount"], [204, "Can change Budgeted Amount", 50, "change_budgetamount"], [205, "Can delete Budgeted Amount", 50, "delete_budgetamount"], [206, "Can view Budgeted Amount", 50, "view_budgetamount"], [207, "Can add Budget Item Category", 51, "add_budgetitem"], [208, "Can change Budget Item Category", 51, "change_budgetitem"], [209, "Can delete Budget Item Category", 51, "delete_budgetitem"], [210, "Can view Budget Item Category", 51, "view_budgetitem"], [211, "Can add Expense Record", 52, "add_expense"], [212, "Can change Expense Record", 52, "change_expense"], [213, "Can delete Expense Record", 52, "delete_expense"], [214, "Can view Expense Record", 52, "view_expense"], [215, "Can add Expense Category", 53, "add_expensecategory"], [216, "Can change Expense Category", 53, "change_expensecategory"], [217, "Can delete Expense Category", 53, "delete_expensecategory"], [218, "Can view Expense Category", 53, "view_expensecategory"], [219, "Can add Vendor/Supplier", 54, "add_vendor"], [220, "Can change Vendor/Supplier", 54, "change_vendor"], [221, "Can delete Vendor/Supplier", 54, "delete_vendor"], [222, "Can view Vendor/Supplier", 54, "view_vendor"], [223, "Can add Payment", 55, "add_payment"], [224, "Can change Payment", 55, "change_payment"], [225, "Can delete Payment", 55, "delete_payment"], [226, "Can view Payment", 55, "view_payment"], [227, "Can add Payment Allocation", 56, "add_paymentallocation"], [228, "Can change Payment Allocation", 56, "change_paymentallocation"], [229, "Can delete Payment Allocation", 56, "delete_paymentallocation"], [230, "Can view Payment Allocation", 56, "view_paymentallocation"], [231, "Can add Payment Method", 57, "add_paymentmethod"], [232, "Can change Payment Method", 57, "change_paymentmethod"], [233, "Can delete Payment Method", 57, "delete_paymentmethod"], [234, "Can view Payment Method", 57, "view_paymentmethod"], [235, "Can add Admin Activity Log", 58, "add_adminactivitylog"], [236, "Can change Admin Activity Log", 58, "change_adminactivitylog"], [237, "Can delete Admin Activity Log", 58, "delete_adminactivitylog"], [238, "Can view Admin Activity Log", 58, "view_adminactivitylog"], [239, "Can add Announcement", 59, "add_announcement"], [240, "Can change Announcement", 59, "change_announcement"], [241, "Can delete Announcement", 59, "delete_announcement"], [242, "Can view Announcement", 59, "view_announcement"], [243, "Can add Communication Log", 60, "add_communicationlog"], [244, "Can change Communication Log", 60, "change_communicationlog"], [245, "Can delete Communication Log", 60, "delete_communicationlog"], [246, "Can view Communication Log", 60, "view_communicationlog"], [247, "Can view Collection Report", 61, "view_collection_report"], [248, "Can view Outstanding Fees Report", 61, "view_outstanding_fees_report"], [249, "Can view Student Ledger Report", 61, "view_student_ledger_report"], [250, "Can view Payment Summary Report", 61, "view_payment_summary_report"], [251, "Can view Trial Balance Report", 61, "view_trial_balance_report"], [252, "Can view Income Statement (P&L)", 61, "view_income_statement_report"], [253, "Can view Balance Sheet Report", 61, "view_balance_sheet_report"], [254, "Can view Cash Flow Statement Report", 61, "view_cash_flow_statement_report"], [255, "Can view Budget Variance Report", 61, "view_budget_variance_report"], [256, "Can view Expense Report", 61, "view_expense_report"], [257, "Can view Fee Projection Report", 61, "view_fee_projection_report"], [258, "Can add Platform Setting", 62, "add_platformsetting"], [259, "Can change Platform Setting", 62, "change_platformsetting"], [260, "Can delete Platform Setting", 62, "delete_platformsetting"], [261, "Can view Platform Setting", 62, "view_platformsetting"], [262, "Can add Platform Announcement", 63, "add_platformannouncement"], [263, "Can change Platform Announcement", 63, "change_platformannouncement"], [264, "Can delete Platform Announcement", 63, "delete_platformannouncement"], [265, "Can view Platform Announcement", 63, "view_platformannouncement"], [266, "Can add Maintenance Mode Setting", 64, "add_maintenancemode"], [267, "Can change Maintenance Mode Setting", 64, "change_maintenancemode"], [268, "Can delete Maintenance Mode Setting", 64, "delete_maintenancemode"], [269, "Can view Maintenance Mode Setting", 64, "view_maintenancemode"], [270, "Can add Platform Audit Log", 65, "add_auditlog"], [271, "Can change Platform Audit Log", 65, "change_auditlog"], [272, "Can delete Platform Audit Log", 65, "delete_auditlog"], [273, "Can view Platform Audit Log", 65, "view_auditlog"], [274, "Can add System Notification", 66, "add_systemnotification"], [275, "Can change System Notification", 66, "change_systemnotification"], [276, "Can delete System Notification", 66, "delete_systemnotification"], [277, "Can view System Notification", 66, "view_systemnotification"], [278, "Can add Platform Announcement", 67, "add_platformannouncement"], [279, "Can change Platform Announcement", 67, "change_platformannouncement"], [280, "Can delete Platform Announcement", 67, "delete_platformannouncement"], [281, "Can view Platform Announcement", 67, "view_platformannouncement"], [282, "Can add Academic Year", 68, "add_academicyear"], [283, "Can change Academic Year", 68, "change_academicyear"], [284, "Can delete Academic Year", 68, "delete_academicyear"], [285, "Can view Academic Year", 68, "view_academicyear"], [286, "Can add Term/Semester", 69, "add_term"], [287, "Can change Term/Semester", 69, "change_term"], [288, "Can delete Term/Semester", 69, "delete_term"], [289, "Can view Term/Semester", 69, "view_term"], [290, "Can add log entry", 70, "add_logentry"], [291, "Can change log entry", 70, "change_logentry"], [292, "Can delete log entry", 70, "delete_logentry"], [293, "Can view log entry", 70, "view_logentry"], [294, "Can add Event Category", 71, "add_eventcategory"], [295, "Can change Event Category", 71, "change_eventcategory"], [296, "Can delete Event Category", 71, "delete_eventcategory"], [297, "Can view Event Category", 71, "view_eventcategory"], [298, "Can add School Event", 72, "add_schoolevent"], [299, "Can change School Event", 72, "change_schoolevent"], [300, "Can delete School Event", 72, "delete_schoolevent"], [301, "Can view School Event", 72, "view_schoolevent"], [302, "Can add Event Attendee", 73, "add_eventattendee"], [303, "Can change Event Attendee", 73, "change_eventattendee"], [304, "Can delete Event Attendee", 73, "delete_eventattendee"], [305, "Can view Event Attendee", 73, "view_eventattendee"], [306, "Can add Invoice Sequence", 74, "add_invoicesequence"], [307, "Can change Invoice Sequence", 74, "change_invoicesequence"], [308, "Can delete Invoice Sequence", 74, "delete_invoicesequence"], [309, "Can view Invoice Sequence", 74, "view_invoicesequence"], [310, "Can view outstanding fees report", 75, "view_outstanding_fees_report"], [311, "Can view collection report", 75, "view_collection_report"], [312, "Can view student ledger report", 75, "view_student_ledger_report"], [313, "Can view Income & Expense Report", 61, "view_income_expense_report"], [314, "Can view Cash Flow Statement", 61, "view_cash_flow_statement"], [315, "Can view the main reports dashboard page", 61, "view_report_dashboard"], [316, "Can add Receipt Sequence", 76, "add_receiptsequence"], [317, "Can change Receipt Sequence", 76, "change_receiptsequence"], [318, "Can delete Receipt Sequence", 76, "delete_receiptsequence"], [319, "Can view Receipt Sequence", 76, "view_receiptsequence"], [320, "Can view the main staff dashboard summary", 77, "view_dashboard_summary"], [321, "Can view the main Announcements module link", 59, "view_announcements_module"], [322, "Can view the main Students & Parents module link", 78, "view_students_module"], [323, "Can view the main HR module and navbar link", 79, "view_hr_module"], [324, "Can create, edit, and manage staff accounts", 79, "manage_staff_users"], [325, "Can approve or reject leave requests", 79, "approve_leave_requests"], [326, "Can configure leave types", 79, "manage_leave_types"], [327, "Can view the main Fees Management module link", 80, "view_fees_module"], [328, "Can view the main Finance module and navbar link", 81, "view_finance_module"], [329, "Can create and manage budgets", 81, "manage_budgets"], [330, "Can record and approve expenses", 81, "manage_expenses"], [331, "Can view the main Setup & Admin module link", 82, "view_setup_admin_module"], [332, "Can assign staff members to roles (groups)", 82, "assign_staff_roles"], [333, "Can view the main Announcements module link", 73, "view_announcements_module"], [334, "Can view Payment Summary Report", 75, "view_payment_summary_report"], [335, "Can view Trial Balance Report", 75, "view_trial_balance_report"], [336, "Can view Income Statement (P&L)", 75, "view_income_statement_report"], [337, "Can view Income & Expense Report", 75, "view_income_expense_report"], [338, "Can view Balance Sheet Report", 75, "view_balance_sheet_report"], [339, "Can view Cash Flow Statement Report", 75, "view_cash_flow_statement_report"], [340, "Can view Cash Flow Statement", 75, "view_cash_flow_statement"], [341, "Can view Budget Variance Report", 75, "view_budget_variance_report"], [342, "Can view Expense Report", 75, "view_expense_report"], [343, "Can view Fee Projection Report", 75, "view_fee_projection_report"], [344, "Can view the main reports dashboard page", 75, "view_report_dashboard"], [345, "Can view the main Calendar module and navbar link", 73, "view_calendar_module"], [346, "Can create, edit, and delete any school event", 73, "manage_all_events"], [347, "Can view the General Ledger report", 81, "view_general_ledger_report"], [348, "Can view the Account Led<PERSON> report", 81, "view_account_ledger_report"], [349, "Can view the main HR module and navbar link", 37, "view_hr_module"], [350, "Can approve or reject leave requests for other staff", 37, "approve_leave_requests"], [351, "Can add Leave Balance Log", 83, "add_leavebalancelog"], [352, "Can change Leave Balance Log", 83, "change_leavebalancelog"], [353, "Can delete Leave Balance Log", 83, "delete_leavebalancelog"], [354, "Can view Leave Balance Log", 83, "view_leavebalancelog"], [355, "Can add Tax Bracket", 84, "add_taxbracket"], [356, "Can change Tax Bracket", 84, "change_taxbracket"], [357, "Can delete Tax Bracket", 84, "delete_taxbracket"], [358, "Can view Tax Bracket", 84, "view_taxbracket"], [359, "Can add Salary Component", 85, "add_salarycomponent"], [360, "Can change Salary Component", 85, "change_salarycomponent"], [361, "Can delete Salary Component", 85, "delete_salarycomponent"], [362, "Can view Salary Component", 85, "view_salarycomponent"], [363, "Can add Payslip Line Item", 86, "add_paysliplineitem"], [364, "Can change Payslip Line Item", 86, "change_paysliplineitem"], [365, "Can delete Payslip Line Item", 86, "delete_paysliplineitem"], [366, "Can view Payslip Line Item", 86, "view_paysliplineitem"], [367, "Can add Payslip", 87, "add_payslip"], [368, "Can change Payslip", 87, "change_payslip"], [369, "Can delete Payslip", 87, "delete_payslip"], [370, "Can view Payslip", 87, "view_payslip"], [371, "Can add Staff Salary Component", 88, "add_staffsalarycomponent"], [372, "Can change Staff Salary Component", 88, "change_staffsalarycomponent"], [373, "Can delete Staff Salary Component", 88, "delete_staffsalarycomponent"], [374, "Can view Staff Salary Component", 88, "view_staffsalarycomponent"], [375, "Can add Staff Salary", 89, "add_staffsalary"], [376, "Can change Staff Salary", 89, "change_staffsalary"], [377, "Can delete Staff Salary", 89, "delete_staffsalary"], [378, "Can view Staff Salary", 89, "view_staffsalary"], [379, "Can add Salary Grade", 90, "add_salarygrade"], [380, "Can change Salary Grade", 90, "change_salarygrade"], [381, "Can delete Salary Grade", 90, "delete_salarygrade"], [382, "Can view Salary Grade", 90, "view_salarygrade"], [383, "Can add Salary Grade Component", 91, "add_salarygradecomponent"], [384, "Can change Salary Grade Component", 91, "change_salarygradecomponent"], [385, "Can delete Salary Grade Component", 91, "delete_salarygradecomponent"], [386, "Can view Salary Grade Component", 91, "view_salarygradecomponent"], [387, "Can add Payroll Run", 92, "add_payrollrun"], [388, "Can change Payroll Run", 92, "change_payrollrun"], [389, "Can delete Payroll Run", 92, "delete_payrollrun"], [390, "Can view Payroll Run", 92, "view_payrollrun"], [391, "Can create and process payroll runs", 92, "manage_payroll"], [392, "Can add staff salary structure", 93, "add_staffsalarystructure"], [393, "Can change staff salary structure", 93, "change_staffsalarystructure"], [394, "Can delete staff salary structure", 93, "delete_staffsalarystructure"], [395, "Can view staff salary structure", 93, "view_staffsalarystructure"], [396, "Can add salary structure component", 94, "add_salarystructurecomponent"], [397, "Can change salary structure component", 94, "change_salarystructurecomponent"], [398, "Can delete salary structure component", 94, "delete_salarystructurecomponent"], [399, "Can view salary structure component", 94, "view_salarystructurecomponent"], [400, "Can add Statutory Deduction", 95, "add_statutorydeduction"], [401, "Can change Statutory Deduction", 95, "change_statutorydeduction"], [402, "Can delete Statutory Deduction", 95, "delete_statutorydeduction"], [403, "Can view Statutory Deduction", 95, "view_statutorydeduction"], [404, "Can add Grade Salary Rule", 96, "add_graderule"], [405, "Can change Grade Salary Rule", 96, "change_graderule"], [406, "Can delete Grade Salary Rule", 96, "delete_graderule"], [407, "Can view Grade Salary Rule", 96, "view_graderule"]]}, "django_content_type": {"columns": ["id", "app_label", "model"], "rows": [[1, "admin", "logentry"], [2, "auth", "permission"], [3, "auth", "group"], [4, "contenttypes", "contenttype"], [5, "sessions", "session"], [6, "tenants", "domain"], [7, "tenants", "school"], [8, "users", "user"], [9, "public_site", "contactinquiry"], [10, "public_site", "testimonial"], [11, "accounting", "accounttype"], [12, "accounting", "general<PERSON>r"], [13, "accounting", "journalentry"], [14, "accounting", "journalentryitem"], [15, "accounting", "journalline"], [16, "accounting", "account"], [17, "subscriptions", "feature"], [18, "subscriptions", "subscription"], [19, "subscriptions", "subscriptionplan"], [20, "django_celery_beat", "crontabschedule"], [21, "django_celery_beat", "intervalschedule"], [22, "django_celery_beat", "periodictask"], [23, "django_celery_beat", "periodictasks"], [24, "django_celery_beat", "solarschedule"], [25, "django_celery_beat", "clockedschedule"], [26, "schools", "academicsetting"], [27, "schools", "academicyear"], [28, "schools", "country"], [29, "schools", "schoolclass"], [30, "schools", "schoolprofile"], [31, "schools", "staffuser"], [32, "schools", "section"], [33, "schools", "term"], [34, "students", "parentuser"], [35, "students", "student"], [36, "hr", "employeeprofile"], [37, "hr", "leavetype"], [38, "hr", "leaverequest"], [39, "hr", "leavebalance"], [40, "fees", "account"], [41, "fees", "concessiontype"], [42, "fees", "feestructure"], [43, "fees", "feestructureitem"], [44, "fees", "invoice"], [45, "fees", "invoicedetail"], [46, "fees", "studentconcession"], [47, "fees", "studentfeeallocation"], [48, "fees", "feehead"], [49, "finance", "budget"], [50, "finance", "budgetamount"], [51, "finance", "budgetitem"], [52, "finance", "expense"], [53, "finance", "expensecategory"], [54, "finance", "vendor"], [55, "payments", "payment"], [56, "payments", "paymentallocation"], [57, "payments", "paymentmethod"], [58, "portal_admin", "adminactivitylog"], [59, "announcements", "announcement"], [60, "communication", "communicationlog"], [61, "reporting", "reportingpermissions"], [62, "platform_management", "platformsetting"], [63, "platform_management", "platformannouncement"], [64, "platform_management", "maintenancemode"], [65, "platform_management", "auditlog"], [66, "platform_management", "systemnotification"], [67, "announcements", "platformannouncement"], [68, "fees", "academicyear"], [69, "fees", "term"], [70, "auditlog", "logentry"], [71, "school_calendar", "eventcategory"], [72, "school_calendar", "schoolevent"], [73, "school_calendar", "eventattendee"], [74, "schools", "invoicesequence"], [75, "reporting", "reportpermissions"], [76, "schools", "receiptsequence"], [77, "schools", "schoolpermissions"], [78, "students", "studentpermissions"], [79, "hr", "hrpermissions"], [80, "fees", "feepermissions"], [81, "finance", "financepermissions"], [82, "portal_admin", "portaladminpermissions"], [83, "hr", "leavebalancelog"], [84, "hr", "taxbracket"], [85, "hr", "salarycomponent"], [86, "hr", "paysliplineitem"], [87, "hr", "payslip"], [88, "hr", "staffsalarycomponent"], [89, "hr", "staffsalary"], [90, "hr", "salarygrade"], [91, "hr", "salarygradecomponent"], [92, "hr", "payrollrun"], [93, "hr", "staffsalarystructure"], [94, "hr", "salarystructurecomponent"], [95, "hr", "statutorydeduction"], [96, "hr", "<PERSON><PERSON><PERSON>"]]}, "hr_leavetype": {"columns": ["id", "name", "description", "max_annual_days", "is_paid", "requires_approval", "is_active", "created_at", "updated_at", "max_days_per_year_grant", "accrual_frequency", "accrual_rate", "max_accrual_balance", "prorate_accrual"], "rows": [[1, "Annual Leave", "Yearly vacation leave for all staff members", null, true, true, true, "2025-07-07 19:22:02.639002+00:00", "2025-07-07 19:22:02.639015+00:00", "21.00", "MONTHLY", "1.75", 30, true], [3, "Maternity Leave", "Leave for new mothers", null, true, true, true, "2025-07-07 19:22:02.670193+00:00", "2025-07-07 19:22:02.670220+00:00", "90.00", "NONE", "0.00", null, false], [6, "Study Leave", "For Writing Exams", null, true, true, true, "2025-07-08 02:07:49.089895+00:00", "2025-07-08 02:07:49.089929+00:00", "21.00", "ANNUALLY", "0.00", 21, true], [2, "Sick Leave", "Medical leave for illness or medical appointments", null, true, false, true, "2025-07-07 19:22:02.665276+00:00", "2025-07-08 15:49:40.471712+00:00", "18.00", "ANNUALLY", "10.00", 15, false], [4, "Paternity Leave", "Leave for new fathers", null, true, true, true, "2025-07-07 19:22:02.674347+00:00", "2025-07-08 15:49:53.067161+00:00", "30.00", "NONE", "0.00", null, false], [5, "Emergency Leave", "Unpaid leave for emergency situations", null, false, true, true, "2025-07-07 19:22:02.682847+00:00", "2025-07-08 15:50:03.498188+00:00", "6.00", "NONE", "0.00", null, false], [7, "Compassionate Leave", "Bereavement leave", null, true, true, true, "2025-07-09 08:58:30.611260+00:00", "2025-07-09 08:58:30.611319+00:00", "12.00", "NONE", "0.00", 12, true]]}, "payments_paymentmethod": {"columns": ["id", "created_at", "updated_at", "name", "type", "description", "is_active", "linked_account_id"], "rows": [[1, "2025-06-27 09:27:58.854317+00:00", "2025-06-28 19:40:05.917304+00:00", "Cash", "CASH", "", true, 5], [2, "2025-06-27 09:28:34.166388+00:00", "2025-06-28 19:40:21.556042+00:00", "Online Payments (Parent)", "ONLINE_MOCK", "", true, 5], [3, "2025-06-27 09:29:02.952478+00:00", "2025-06-28 19:40:37.598814+00:00", "Online Portal Payment", "CARD_PAYMENT", "", true, 5], [4, "2025-06-28 19:41:28.087934+00:00", "2025-06-28 19:41:28.087950+00:00", "Bank Transfer", "BANK_TRANSFER", "", true, 7]]}, "fees_concessiontype": {"columns": ["id", "name", "description", "value", "is_active", "created_at", "updated_at", "type"], "rows": [[3, "Test Concession", "Test description", "10.00", true, "2025-06-25 08:17:02.960478+00:00", "2025-06-25 08:17:02.960499+00:00", "PERCENTAGE"], [4, "Test Sibling Discount", "Test discount for siblings", "15.00", true, "2025-06-25 08:17:39.162464+00:00", "2025-06-25 08:17:39.162483+00:00", "PERCENTAGE"], [5, "<PERSON><PERSON><PERSON> Discount", "Second child", "150.00", true, "2025-06-25 08:17:44.275613+00:00", "2025-06-25 08:17:44.275623+00:00", "FIXED_AMOUNT"]]}, "school_calendar_eventcategory": {"columns": ["id", "name", "color", "icon", "description", "is_active", "created_at", "updated_at"], "rows": [[1, "Academic", "#007bff", "bi-book", "Academic events and activities", true, "2025-06-26 15:40:57.821171+00:00", "2025-06-26 15:40:57.821187+00:00"], [2, "Sports", "#28a745", "bi-trophy", "Sports and athletic events", true, "2025-06-26 15:40:57.825081+00:00", "2025-06-26 15:40:57.825097+00:00"], [3, "Cultural", "#ffc107", "bi-palette", "Cultural and artistic events", true, "2025-06-26 15:40:57.827471+00:00", "2025-06-26 15:40:57.827483+00:00"], [4, "Parent Events", "#17a2b8", "bi-people", "Events for parents and families", true, "2025-06-26 15:40:57.829134+00:00", "2025-06-26 15:40:57.829142+00:00"], [5, "Holidays", "#dc3545", "bi-calendar-heart", "School holidays and breaks", true, "2025-06-26 15:40:57.830762+00:00", "2025-06-26 15:40:57.830770+00:00"], [6, "Examinations", "#6f42c1", "bi-pencil-square", "Exams and assessments", true, "2025-06-26 15:40:57.833575+00:00", "2025-06-26 15:40:57.833592+00:00"]]}, "hr_salarycomponent": {"columns": ["id", "name", "type", "description", "is_percentage"], "rows": [[1, "Basic Salary", "EARNING", "Basic Monthly Salary", false], [2, "Housing Allowance", "EARNING", "Monthly Housing Allowance", false], [3, "Pension Deduction", "DEDUCTION", "Statutory Deduction at a rate of 7.5% of the Gross Salary", false], [4, "Transport Allowance", "EARNING", "Transport relief", false], [5, "PAYE Tax", "DEDUCTION", "Pay As You Earn", true]]}, "hr_statutorydeduction": {"columns": ["id", "name", "employee_contribution_rate", "employer_contribution_rate", "is_active", "payslip_label"], "rows": [[1, "Pension Fund", "7.50", "7.50", true, ""], [2, "Aids Levy", "0.01", "0.01", true, ""], [3, "Dr<PERSON> Levy", "0.01", "0.03", true, ""]]}, "hr_taxbracket": {"columns": ["id", "name", "from_amount", "to_amount", "rate_percent", "deduction_amount", "is_active"], "rows": [[1, "Low_income", "0.00", "50000.00", "10.00", "0.00", true], [3, "high_income", "120000.01", null, "30.00", "19000.00", true], [2, "middle_income", "50000.01", "120000.00", "20.00", "5000.00", true]]}}, "missing_table_fixes": {}}