# D:\school_fees_saas_v2\apps\tenants\forms.py

from django import forms
from django.conf import settings
from django.urls import reverse_lazy, NoReverseMatch
from django.utils.translation import gettext_lazy as _
from django.contrib.auth import get_user_model

from .models import School
from apps.subscriptions.models import SubscriptionPlan

User = get_user_model()

class RegistrationForm(forms.Form):
    """
    A single, comprehensive form for new tenant registration.
    """
    # --- School Information ---
    #     # --- School Information ---
    school_name = forms.CharField(
        max_length=100, required=True, label=_("School's Full Name"),
        widget=forms.TextInput(attrs={'placeholder': _("e.g., Bright Future Academy")})
    )
    schema_name = forms.SlugField( # Using SlugField for better validation
        max_length=63, required=True, label=_("Portal Address (Subdomain)"),
        widget=forms.TextInput(attrs={'placeholder': _("e.g., brightfuture")})
    )

    # --- Administrator Account Details ---
    full_name = forms.CharField(max_length=150, required=True, label=_("Your Full Name"))
    email = forms.EmailField(
        required=True, label=_("Your Email Address (This will be your login)"),
        widget=forms.EmailInput(attrs={'autocomplete': 'email'})
    )
    password = forms.CharField(
        label=_("Create Password"),
        widget=forms.PasswordInput(attrs={'autocomplete': 'new-password'}),
        required=True, min_length=8
    )
    password_confirm = forms.CharField(label=_("Confirm Password"), widget=forms.PasswordInput, required=True)

    # --- Plan and Payment Fields ---
    plan_id = forms.IntegerField(widget=forms.HiddenInput())
    payment_method_id = forms.CharField(widget=forms.HiddenInput(), required=False) # For Stripe token

    # --- Terms and Conditions ---
    agree_to_terms = forms.BooleanField(
        required=True,
        label=_("I agree to the Terms of Service and Privacy Policy."),
        widget=forms.CheckboxInput(attrs={'class': 'form-check-input'})
    )



    # --- THIS IS THE SINGLE, CORRECT __init__ METHOD ---
    def __init__(self, *args, **kwargs):
        # We don't need to pass 'base_domain_for_help_text' anymore,
        # as we can get it directly from settings.
        super().__init__(*args, **kwargs)
        
        # Apply Bootstrap classes
        for field_name, field in self.fields.items():
            if not isinstance(field.widget, (forms.RadioSelect, forms.CheckboxInput, forms.HiddenInput)):
                field.widget.attrs.update({'class': 'form-control'})

        # Set dynamic help_text for the subdomain
        base_domain = getattr(settings, 'TENANT_BASE_DOMAIN', 'myapp.test:8000').split(':')[0]
        self.fields['schema_name'].help_text = _(
            "Your portal will be at [prefix].%(hostname)s. Use only lowercase letters, numbers, and hyphens."
        ) % {'hostname': base_domain}

        # Set dynamic label for terms agreement with links
        try:
            terms_url = reverse_lazy('public_site:terms')
            privacy_url = reverse_lazy('public_site:privacy')
            self.fields['agree_to_terms'].label = _(
                'I agree to the <a href="%(terms_url)s" target="_blank">Terms of Service</a> '
                'and <a href="%(privacy_url)s" target="_blank">Privacy Policy</a>.'
            ) % {'terms_url': terms_url, 'privacy_url': privacy_url}
        except NoReverseMatch:
            self.fields['agree_to_terms'].label = _("I agree to the Terms of Service and Privacy Policy.")

    def clean_email(self):
        email = self.cleaned_data.get('email')
        if email and User.objects.filter(email__iexact=email).exists():
            raise forms.ValidationError(_("An account with this email already exists."))
        return email

    def clean_schema_name(self):
        schema_name = self.cleaned_data.get('schema_name', '').lower()
        
        RESERVED_SUBDOMAINS = {
            'www', 'admin', 'mail', 'ftp', 'blog', 'shop', 'support', 'dev', 'staging', 'app', 'portal',
            'api', 'static', 'media', 'user', 'users', 'account', 'accounts', 'root', 'info', 'test',
            'payment', 'billing', 'secure', 'system', 'public', 'assets', 'files', 'cdn', 'status'
        }
        
        if schema_name in RESERVED_SUBDOMAINS:
            raise forms.ValidationError(_("This portal address is reserved. Please choose another."))
        
        if len(schema_name) < 3:
            raise forms.ValidationError(_("Portal address must be at least 3 characters long."))

        if School.objects.filter(schema_name__iexact=schema_name).exists():
            raise forms.ValidationError(_("This portal address is already in use. Please choose another."))
            
        return schema_name

    def clean(self):
        cleaned_data = super().clean()
        password = cleaned_data.get("password")
        password_confirm = cleaned_data.get("password_confirm")

        if password and password_confirm and password != password_confirm:
            self.add_error('password_confirm', _("The two password fields didn't match."))
            
        return cleaned_data



# # D:\school_fees_saas_v2\apps\tenants\forms.py

# from django import forms
# from django.conf import settings
# from django.urls import reverse_lazy, NoReverseMatch
# from django.utils.translation import gettext_lazy as _
# from django.contrib.auth import get_user_model

# from .models import School
# from apps.subscriptions.models import SubscriptionPlan, Subscription

# User = get_user_model() # This is your public 'SchoolOwner' model

# class RegistrationForm(forms.Form):
#     """
#     A single, comprehensive form for new tenant registration, including
#     school info, admin account creation, and plan selection.
#     """
#     # --- School Information ---
#     school_name = forms.CharField(
#         max_length=100, required=True, label=_("School's Full Name"),
#         widget=forms.TextInput(attrs={'placeholder': _("e.g., Bright Future Academy")})
#     )
#     schema_name = forms.SlugField( # Using SlugField for better validation
#         max_length=63, required=True, label=_("Portal Address (Subdomain)"),
#         widget=forms.TextInput(attrs={'placeholder': _("e.g., brightfuture")})
#     )

#     # --- Administrator Account Details ---
#     full_name = forms.CharField(max_length=150, required=True, label=_("Your Full Name"))
#     email = forms.EmailField(
#         required=True, label=_("Your Email Address (This will be your login)"),
#         widget=forms.EmailInput(attrs={'autocomplete': 'email'})
#     )
#     password = forms.CharField(
#         label=_("Create Password"),
#         widget=forms.PasswordInput(attrs={'autocomplete': 'new-password'}),
#         required=True, min_length=8
#     )
#     password_confirm = forms.CharField(label=_("Confirm Password"), widget=forms.PasswordInput, required=True)

#     # --- Plan and Payment Fields ---
#     plan_id = forms.IntegerField(widget=forms.HiddenInput())
#     payment_method_id = forms.CharField(widget=forms.HiddenInput(), required=False) # For Stripe token

#     # --- Terms and Conditions ---
#     agree_to_terms = forms.BooleanField(
#         required=True,
#         label=_("I agree to the Terms of Service and Privacy Policy."),
#         widget=forms.CheckboxInput(attrs={'class': 'form-check-input'})
#     )

#     def __init__(self, *args, **kwargs):
#         """
#         Consolidated __init__ to apply styling and dynamic help text.
#         """
#         super().__init__(*args, **kwargs)
        
#         # Apply Bootstrap classes to all fields except checkbox/radio
#         for field_name, field in self.fields.items():
#             if not isinstance(field.widget, (forms.RadioSelect, forms.CheckboxInput)):
#                 # Add a default class and allow for others to be added
#                 current_class = field.widget.attrs.get('class', '')
#                 field.widget.attrs['class'] = f'form-control {current_class}'.strip()

#         # Set dynamic help_text for the subdomain
#         base_domain = getattr(settings, 'TENANT_BASE_DOMAIN', 'myapp.test:8000').split(':')[0]
#         self.fields['schema_name'].help_text = _(
#             "Your portal will be at [prefix].%(hostname)s. Use only lowercase letters, numbers, and hyphens."
#         ) % {'hostname': base_domain}

#         # Set dynamic help_text for terms agreement with links
#         try:
#             terms_url = reverse_lazy('public_site:terms')
#             privacy_url = reverse_lazy('public_site:privacy')
#             self.fields['agree_to_terms'].label = _(
#                 'I agree to the <a href="%(terms_url)s" target="_blank">Terms of Service</a> '
#                 'and <a href="%(privacy_url)s" target="_blank">Privacy Policy</a>.'
#             ) % {'terms_url': terms_url, 'privacy_url': privacy_url}
#         except NoReverseMatch:
#             # Fallback if URLs are not defined, prevents a crash
#             self.fields['agree_to_terms'].label = _("I agree to the Terms of Service and Privacy Policy.")

#     def clean_email(self):
#         email = self.cleaned_data.get('email')
#         if email and User.objects.filter(email__iexact=email).exists():
#             raise forms.ValidationError(_("An account with this email already exists."))
#         return email

#     def clean_schema_name(self):
#         schema_name = self.cleaned_data.get('schema_name', '').lower()
        
#         RESERVED_SUBDOMAINS = {
#             'www', 'admin', 'mail', 'ftp', 'blog', 'shop', 'support', 'dev', 'staging', 'app', 'portal',
#             'api', 'static', 'media', 'user', 'users', 'account', 'accounts', 'root', 'info', 'test',
#             'payment', 'billing', 'secure', 'system', 'public', 'assets', 'files', 'cdn', 'status'
#         }
        
#         if schema_name in RESERVED_SUBDOMAINS:
#             raise forms.ValidationError(_("This portal address is reserved. Please choose another."))
        
#         if len(schema_name) < 3:
#             raise forms.ValidationError(_("Portal address must be at least 3 characters long."))

#         if School.objects.filter(schema_name__iexact=schema_name).exists():
#             raise forms.ValidationError(_("This portal address is already in use. Please choose another."))
            
#         return schema_name

#     def clean(self):
#         cleaned_data = super().clean()
#         password = cleaned_data.get("password")
#         password_confirm = cleaned_data.get("password_confirm")

#         if password and password_confirm and password != password_confirm:
#             self.add_error('password_confirm', _("The two password fields didn't match."))
            
#         return cleaned_data
    
    
    







# from django import forms
# from django.contrib.auth import get_user_model
# from django.urls import reverse_lazy, NoReverseMatch
# from django.utils.translation import gettext_lazy as _
# from django.conf import settings
# from .models import School
# from apps.subscriptions.models import SubscriptionPlan, Subscription

# User = get_user_model() # This will be your public User model (e.g., SchoolOwner)

# class RegistrationForm(forms.Form):
#     # --- School Information ---
#     school_name = forms.CharField(
#         max_length=100, 
#         required=True, 
#         label=_("School's Full Name"),
#         widget=forms.TextInput(attrs={'class': 'form-control mb-3', 'placeholder': _("e.g., Bright Future Academy")})
#     )
#     subdomain = forms.SlugField(
#         max_length=63,
#         required=True,
#         label=_("Portal Address Prefix"), 
#         widget=forms.TextInput(attrs={'class': 'form-control mb-3', 'placeholder': _("e.g., brightfuture")})
#     )

#     # --- Administrator Account Details ---
#     admin_first_name = forms.CharField(max_length=150, required=True, label=_("Your First Name"))
#     admin_last_name = forms.CharField(max_length=150, required=True, label=_("Your Last Name"))
#     admin_email = forms.EmailField(
#         required=True, 
#         label=_("Your Email Address (This will be your login)"),
#         widget=forms.EmailInput(attrs={'autocomplete': 'email'})
#     )
#     admin_password1 = forms.CharField(
#         label=_("Create Password"),
#         widget=forms.PasswordInput(attrs={'autocomplete': 'new-password'}),
#         required=True, 
#         min_length=8
#     )
#     admin_password2 = forms.CharField(label=_("Confirm Password"), widget=forms.PasswordInput, required=True)

#     # --- Subscription Plan Selection ---
#     plan = forms.ModelChoiceField(
#         queryset=SubscriptionPlan.objects.filter(is_active=True, is_public=True).order_by('display_order'),
#         label=_("Choose Your Plan"),
#         empty_label=None,
#         widget=forms.RadioSelect,
#         required=True
#     )
#     billing_cycle = forms.ChoiceField(
#         label="Billing Cycle",
#         choices=Subscription.BillingCycle.choices, # <<< CORRECTED SOURCE
#         widget=forms.RadioSelect(attrs={'class': 'form-check-input'}),
#         initial=Subscription.BillingCycle.MONTHLY,
#     )
    
#     # --- Terms and Conditions ---
#     agree_to_terms = forms.BooleanField(
#         required=True,
#         label=_("I agree to the Terms of Service and Privacy Policy."),
#         widget=forms.CheckboxInput(attrs={'class': 'form-check-input'})
#     )

#     def __init__(self, *args, **kwargs):
#         # --- THIS IS THE FIX ---
#         # 1. Pop the custom argument out of kwargs and store it.
#         #    Provide a default value in case it's not passed.
#         base_domain = kwargs.pop('base_domain_for_help_text', 'school.test') 
        
#         # 2. Now that the unexpected argument is removed, call the parent's __init__.
#         super().__init__(*args, **kwargs)
        
#         # 3. Now you can safely use the 'base_domain' variable to set your help text.
#         if 'schema_name' in self.fields:
#             self.fields['schema_name'].help_text = (
#                 f"This will be your unique URL prefix. "
#                 f"Example: if you enter 'alpha', your URL will be alpha.{base_domain}"
#             )
        
#         # You can also add other dynamic initializations here
#         # For example, if you want to filter the 'plan' choices
#         if 'plan' in self.fields:
#             self.fields['plan'].queryset = SubscriptionPlan.objects.filter(is_public=True, is_active=True)
            
            
#     # def __init__(self, *args, **kwargs):
#     #     super().__init__(*args, **kwargs)
        
#     #     # Apply Bootstrap classes to all fields
#     #     for field_name, field in self.fields.items():
#     #         # Skip radio/checkbox fields as they are styled differently
#     #         if not isinstance(field.widget, (forms.RadioSelect, forms.CheckboxInput)):
#     #             field.widget.attrs.update({'class': 'form-control mb-3'})
        
#     #     # --- Set dynamic help_text and labels ---
#     #     base_domain = getattr(settings, 'TENANT_BASE_DOMAIN', 'myapp.test:8000').split(':')[0]
#     #     self.fields['subdomain'].help_text = _(
#     #         "Your portal will be at [prefix].%(hostname)s. Use only lowercase letters, numbers, and hyphens."
#     #     ) % {'hostname': base_domain}

#     #     try:
#     #         terms_url = reverse_lazy('public_site:terms') # Use the correct URL name
#     #         privacy_url = reverse_lazy('public_site:privacy') # Use the correct URL name
#     #         self.fields['agree_to_terms'].label = _(
#     #             "I agree to the <a href=\"%(terms_url)s\" target=\"_blank\">Terms of Service</a> "
#     #             "and <a href=\"%(privacy_url)s\" target=\"_blank\">Privacy Policy</a>."
#     #         ) % {'terms_url': terms_url, 'privacy_url': privacy_url}
#     #     except NoReverseMatch:
#     #         # Fallback if URLs are not defined, prevents a crash
#     #         pass

#     def clean_admin_email(self):
#         email = self.cleaned_data.get('admin_email')
#         if email and User.objects.filter(email__iexact=email).exists():
#             raise forms.ValidationError(_("An account with this email already exists."))
#         return email

#     def clean_subdomain(self):
#         subdomain = self.cleaned_data.get('subdomain', '').lower()
        
#         RESERVED_SUBDOMAINS = {
#             'www', 'admin', 'mail', 'ftp', 'blog', 'shop', 'support', 'dev', 'staging', 'app', 'portal',
#             'api', 'static', 'media', 'user', 'users', 'account', 'accounts', 'root', 'info', 'test',
#             'payment', 'billing', 'secure', 'system', 'public', 'assets', 'files', 'cdn', 'status'
#         }
        
#         if subdomain in RESERVED_SUBDOMAINS:
#             raise forms.ValidationError(_("This portal address is reserved. Please choose another."))
        
#         if len(subdomain) < 3:
#             raise forms.ValidationError(_("Portal address must be at least 3 characters long."))

#         if School.objects.filter(schema_name__iexact=subdomain).exists():
#             raise forms.ValidationError(_("This portal address is already in use. Please choose another."))
            
#         return subdomain

#     def clean(self):
#         cleaned_data = super().clean()
#         password_1 = cleaned_data.get("admin_password1")
#         password_2 = cleaned_data.get("admin_password2")

#         if password_1 and password_2 and password_1 != password_2:
#             self.add_error('admin_password2', _("The two password fields didn't match."))
            
#         return cleaned_data
    

    