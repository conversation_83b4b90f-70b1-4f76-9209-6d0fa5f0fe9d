-- Fix the corrupted fees_studentconcession table structure
-- This script will drop and recreate the table with proper structure

-- For alpha schema
SET search_path TO alpha;

-- Drop the corrupted table completely
DROP TABLE IF EXISTS fees_studentconcession CASCADE;

-- Recreate the table with proper structure
CREATE TABLE fees_studentconcession (
    id bigint NOT NULL,
    notes text,
    granted_at timestamp with time zone NOT NULL DEFAULT NOW(),
    academic_year_id bigint NOT NULL,
    concession_type_id bigint NOT NULL,
    granted_by_id bigint,
    student_id bigint NOT NULL,
    term_id bigint
);

-- Create sequence for the id field
CREATE SEQUENCE fees_studentconcession_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

-- Set the sequence as default for id column
ALTER TABLE fees_studentconcession ALTER COLUMN id SET DEFAULT nextval('fees_studentconcession_id_seq'::regclass);

-- Set the sequence owner
ALTER SEQUENCE fees_studentconcession_id_seq OWNED BY fees_studentconcession.id;

-- Add primary key constraint
ALTER TABLE fees_studentconcession ADD CONSTRAINT fees_studentconcession_pkey PRIMARY KEY (id);

-- Add foreign key constraints
ALTER TABLE fees_studentconcession 
ADD CONSTRAINT fees_studentconcession_academic_year_id_fkey 
FOREIGN KEY (academic_year_id) REFERENCES schools_academicyear(id) DEFERRABLE INITIALLY DEFERRED;

ALTER TABLE fees_studentconcession 
ADD CONSTRAINT fees_studentconcession_concession_type_id_fkey 
FOREIGN KEY (concession_type_id) REFERENCES fees_concessiontype(id) DEFERRABLE INITIALLY DEFERRED;

ALTER TABLE fees_studentconcession 
ADD CONSTRAINT fees_studentconcession_granted_by_id_fkey 
FOREIGN KEY (granted_by_id) REFERENCES users_staffuser(id) DEFERRABLE INITIALLY DEFERRED;

ALTER TABLE fees_studentconcession 
ADD CONSTRAINT fees_studentconcession_student_id_fkey 
FOREIGN KEY (student_id) REFERENCES students_student(id) DEFERRABLE INITIALLY DEFERRED;

ALTER TABLE fees_studentconcession 
ADD CONSTRAINT fees_studentconcession_term_id_fkey 
FOREIGN KEY (term_id) REFERENCES schools_term(id) DEFERRABLE INITIALLY DEFERRED;

-- Add unique constraint
ALTER TABLE fees_studentconcession 
ADD CONSTRAINT unique_student_concession 
UNIQUE (student_id, concession_type_id, academic_year_id, term_id);

-- Create indexes
CREATE INDEX fees_studentconcession_academic_year_id_idx ON fees_studentconcession(academic_year_id);
CREATE INDEX fees_studentconcession_concession_type_id_idx ON fees_studentconcession(concession_type_id);
CREATE INDEX fees_studentconcession_granted_by_id_idx ON fees_studentconcession(granted_by_id);
CREATE INDEX fees_studentconcession_student_id_idx ON fees_studentconcession(student_id);
CREATE INDEX fees_studentconcession_term_id_idx ON fees_studentconcession(term_id);

-- Verify the table structure
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'fees_studentconcession' 
ORDER BY ordinal_position;
