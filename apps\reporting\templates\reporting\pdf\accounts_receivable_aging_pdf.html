<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>{{ report_title }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            margin: 0;
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
        }
        .school-name {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .report-title {
            font-size: 16px;
            font-weight: bold;
            margin: 10px 0;
        }
        .report-date {
            font-size: 12px;
            color: #666;
        }
        .summary-section {
            margin: 20px 0;
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
        }
        .summary-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
        }
        .summary-table th,
        .summary-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
        }
        .summary-table th {
            background-color: #e9ecef;
            font-weight: bold;
        }
        .current-amount {
            color: #28a745;
            font-weight: bold;
        }
        .overdue-amount {
            color: #dc3545;
            font-weight: bold;
        }
        .warning-amount {
            color: #ffc107;
            font-weight: bold;
        }
        .total-amount {
            color: #007bff;
            font-weight: bold;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 6px;
            text-align: left;
            font-size: 10px;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .text-right {
            text-align: right;
        }
        .text-center {
            text-align: center;
        }
        .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 10px;
            color: #666;
            border-top: 1px solid #ddd;
            padding-top: 10px;
        }
        .page-break {
            page-break-before: always;
        }
        .totals-row {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .student-name {
            font-weight: bold;
        }
        .parent-info {
            font-size: 9px;
            color: #666;
        }
        .admission-badge {
            background-color: #6c757d;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 9px;
        }
        .class-badge {
            background-color: #17a2b8;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 9px;
        }
    </style>
</head>
<body>
    <!-- Header Section -->
    <div class="header">
        {% if school_profile %}
        <div class="school-name">{{ school_profile.school_name }}</div>
        {% if school_profile.address %}
        <div>{{ school_profile.address }}</div>
        {% endif %}
        {% if school_profile.phone %}
        <div>Phone: {{ school_profile.phone }}</div>
        {% endif %}
        {% if school_profile.email %}
        <div>Email: {{ school_profile.email }}</div>
        {% endif %}
        {% else %}
        <div class="school-name">{{ tenant.name }}</div>
        {% endif %}
        
        <div class="report-title">{{ report_title }}</div>
        <div class="report-date">
            As of Date: {{ as_of_date|date:"M d, Y" }}
        </div>
    </div>

    <!-- Summary Section -->
    <div class="summary-section">
        <h3>Aging Summary</h3>
        <table class="summary-table">
            <thead>
                <tr>
                    {% for period in aging_periods %}
                    <th>{{ period.name }}</th>
                    {% endfor %}
                    <th>Total Outstanding</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    {% for period in aging_periods %}
                    <td class="text-right {% if period.name == 'Current' %}current-amount{% elif 'Over' in period.name %}overdue-amount{% else %}warning-amount{% endif %}">
                        {{ summary_totals|get_item:period.name|floatformat:2 }}
                    </td>
                    {% endfor %}
                    <td class="text-right total-amount">{{ total_outstanding|floatformat:2 }}</td>
                </tr>
            </tbody>
        </table>
        
        <!-- Percentage Breakdown -->
        <div style="margin-top: 15px;">
            <strong>Percentage Distribution:</strong><br>
            {% for period in aging_periods %}
            {% with period_amount=summary_totals|get_item:period.name %}
            {% if total_outstanding > 0 %}
            {% with percentage=period_amount|percentage:total_outstanding %}
            {{ period.name }}: {{ percentage|floatformat:1 }}% | 
            {% endwith %}
            {% endif %}
            {% endwith %}
            {% endfor %}
        </div>
    </div>

    <!-- Student Details Table -->
    {% if aging_data %}
    <table>
        <thead>
            <tr>
                <th style="width: 20%;">Student</th>
                <th style="width: 10%;">Admission No</th>
                <th style="width: 10%;">Class</th>
                <th style="width: 12%;" class="text-right">Total Outstanding</th>
                {% for period in aging_periods %}
                <th style="width: 12%;" class="text-right">{{ period.name }}</th>
                {% endfor %}
            </tr>
        </thead>
        <tbody>
            {% for student_data in aging_data %}
            <tr>
                <td>
                    <div class="student-name">{{ student_data.student.get_full_name }}</div>
                    {% if student_data.student.parent %}
                    <div class="parent-info">Parent: {{ student_data.student.parent.get_full_name }}</div>
                    {% endif %}
                </td>
                <td class="text-center">
                    <span class="admission-badge">{{ student_data.student.admission_number }}</span>
                </td>
                <td class="text-center">
                    {% if student_data.student.current_class %}
                    <span class="class-badge">{{ student_data.student.current_class.name }}</span>
                    {% else %}
                    N/A
                    {% endif %}
                </td>
                <td class="text-right total-amount">
                    {{ student_data.total_outstanding|floatformat:2 }}
                </td>
                {% for period in aging_periods %}
                <td class="text-right">
                    {% with period_amount=student_data.periods|get_item:period.name %}
                    {% if period_amount > 0 %}
                    <span class="{% if period.name == 'Current' %}current-amount{% elif 'Over' in period.name %}overdue-amount{% else %}warning-amount{% endif %}">
                        {{ period_amount|floatformat:2 }}
                    </span>
                    {% else %}
                    -
                    {% endif %}
                    {% endwith %}
                </td>
                {% endfor %}
            </tr>
            {% endfor %}
        </tbody>
        <tfoot>
            <tr class="totals-row">
                <td colspan="3" class="text-right"><strong>Totals:</strong></td>
                <td class="text-right total-amount"><strong>{{ total_outstanding|floatformat:2 }}</strong></td>
                {% for period in aging_periods %}
                <td class="text-right {% if period.name == 'Current' %}current-amount{% elif 'Over' in period.name %}overdue-amount{% else %}warning-amount{% endif %}">
                    <strong>{{ summary_totals|get_item:period.name|floatformat:2 }}</strong>
                </td>
                {% endfor %}
            </tr>
        </tfoot>
    </table>
    {% endif %}

    <!-- Analysis Section -->
    {% if aging_data %}
    <div style="margin-top: 30px; background-color: #f8f9fa; padding: 15px; border-radius: 5px;">
        <h3>Analysis</h3>
        <ul>
            <li><strong>Total Students with Outstanding Balances:</strong> {{ aging_data|length }}</li>
            <li><strong>Average Outstanding per Student:</strong> 
                {% if aging_data %}
                {{ total_outstanding|divide:aging_data|length|floatformat:2 }}
                {% else %}
                0.00
                {% endif %}
            </li>
            {% for period in aging_periods %}
            {% with period_amount=summary_totals|get_item:period.name %}
            {% if total_outstanding > 0 %}
            {% with percentage=period_amount|percentage:total_outstanding %}
            <li><strong>{{ period.name }}:</strong> {{ percentage|floatformat:1 }}% of total outstanding</li>
            {% endwith %}
            {% endif %}
            {% endwith %}
            {% endfor %}
        </ul>
    </div>
    {% endif %}

    <!-- Footer -->
    <div class="footer">
        <p>Generated on {{ "now"|date:"F d, Y \a\\t g:i A" }} | {{ tenant.name }} | Accounts Receivable Aging Report</p>
    </div>
</body>
</html>
