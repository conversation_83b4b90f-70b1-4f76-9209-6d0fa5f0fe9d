{# D:\school_fees_saas_v2\apps\parent_portal\templates\parent_portal\invoice_detail.html #}
{% extends "parent_portal/parent_portal_base.html" %}
{% load static humanize i18n fees_tags %}

{% block parent_portal_page_title %}{{ view_title }}{% endblock parent_portal_page_title %}

{% block parent_portal_main_content %}
<div class="container mt-4">

    {# Breadcrumbs #}
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'parent_portal:dashboard' %}">{% trans "Dashboard" %}</a></li>
            <li class="breadcrumb-item"><a href="{% url 'parent_portal:student_fees' student_pk=student.pk %}">{{ student.get_full_name }}'s Fees</a></li>
            <li class="breadcrumb-item active" aria-current="page">{{ invoice.invoice_number_display }}</li>
        </ol>
    </nav>

    {# Action Buttons Header #}
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="display-6 mb-0">{{ view_title }}</h1>
        <div>
            <a href="{% url 'parent_portal:invoice_pdf' pk=invoice.pk %}" target="_blank" class="btn btn-danger">
                <i class="bi bi-file-earmark-pdf-fill me-1"></i> {% trans "Download PDF" %}
            </a>
            {% if invoice.is_payable and tenant_features.ONLINE_PAYMENTS %}
                <a href="{% url 'parent_portal:select_invoices_for_payment' %}?invoice={{ invoice.pk }}" class="btn btn-success">
                    <i class="bi bi-credit-card-fill me-1"></i> {% trans "Pay Now" %}
                </a>
            {% endif %}
        </div>
    </div>

    {# Re-use the staff-facing invoice detail partial if it's suitable #}
    {# This is a great way to keep your display consistent #}
    {% include 'fees/partials/_invoice_detail_card.html' with invoice=invoice school_profile=school_profile %}

    <div class="text-center mt-4">
        <a href="{{ request.META.HTTP_REFERER|default:request.get_full_path|add:'..' }}" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left-circle me-1"></i> {% trans "Go Back" %}
        </a>
    </div>

</div>
{% endblock parent_portal_main_content %}


