{# templates/public_site/public_messages.html #}
{% extends "public_base.html" %}
{% load static %}

{% block title %}{{ view_title }} - School Fees Management Platform{% endblock %}

{% block extra_public_css %}
<style>
    .messages-hero {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 4rem 0;
        text-align: center;
        position: relative;
        overflow: hidden;
    }
    
    .messages-hero::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        opacity: 0.3;
    }
    
    .messages-hero h1 {
        font-size: 3rem;
        font-weight: 700;
        margin-bottom: 1rem;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        position: relative;
        z-index: 1;
    }
    
    .messages-hero p {
        font-size: 1.2rem;
        opacity: 0.9;
        margin-bottom: 0;
        position: relative;
        z-index: 1;
        max-width: 600px;
        margin: 0 auto;
    }
    
    .messages-container {
        padding: 4rem 0;
        background: #f8fafc;
    }
    
    .stats-section {
        background: white;
        border-radius: 16px;
        padding: 2rem;
        margin-bottom: 3rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    }
    
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 2rem;
        text-align: center;
    }
    
    .stat-item h3 {
        font-size: 2.5rem;
        color: #667eea;
        margin-bottom: 0.5rem;
        font-weight: 700;
    }
    
    .stat-item p {
        color: #718096;
        margin: 0;
        font-weight: 500;
    }
    
    .featured-section {
        margin-bottom: 3rem;
    }
    
    .section-title {
        text-align: center;
        margin-bottom: 2rem;
    }
    
    .section-title h2 {
        color: #2d3748;
        font-size: 2rem;
        margin-bottom: 0.5rem;
    }
    
    .section-title p {
        color: #718096;
        margin: 0;
    }
    
    .message-card {
        background: white;
        border-radius: 16px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        border: 1px solid #e2e8f0;
        transition: all 0.3s ease;
    }
    
    .message-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }
    
    .message-header {
        display: flex;
        align-items: center;
        margin-bottom: 1rem;
    }
    
    .message-avatar {
        width: 50px;
        height: 50px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: 600;
        font-size: 1.2rem;
        margin-right: 1rem;
    }
    
    .message-info h4 {
        margin: 0 0 0.25rem 0;
        color: #2d3748;
        font-size: 1.1rem;
    }
    
    .message-meta {
        color: #718096;
        font-size: 0.9rem;
        display: flex;
        align-items: center;
        gap: 1rem;
    }
    
    .inquiry-badge {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 500;
    }
    
    .featured-badge {
        background: linear-gradient(135deg, #f6ad55 0%, #ed8936 100%);
        color: white;
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 500;
        margin-left: 0.5rem;
    }
    
    .message-content {
        margin: 1rem 0;
    }
    
    .message-title {
        color: #2d3748;
        font-size: 1.2rem;
        font-weight: 600;
        margin-bottom: 0.75rem;
    }
    
    .message-text {
        color: #4a5568;
        line-height: 1.6;
        margin-bottom: 1rem;
    }
    
    .message-excerpt {
        color: #718096;
        font-style: italic;
        border-left: 3px solid #667eea;
        padding-left: 1rem;
        margin-top: 1rem;
    }
    
    .message-footer {
        display: flex;
        justify-content: between;
        align-items: center;
        padding-top: 1rem;
        border-top: 1px solid #e2e8f0;
    }
    
    .message-date {
        color: #718096;
        font-size: 0.9rem;
    }
    
    .featured-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 2rem;
        margin-bottom: 3rem;
    }
    
    .featured-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 16px;
        padding: 2rem;
        position: relative;
        overflow: hidden;
    }
    
    .featured-card::before {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 100px;
        height: 100px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 50%;
        transform: translate(30px, -30px);
    }
    
    .featured-card .message-avatar {
        background: rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(10px);
    }
    
    .featured-card .message-info h4 {
        color: white;
    }
    
    .featured-card .message-meta {
        color: rgba(255, 255, 255, 0.8);
    }
    
    .featured-card .message-title {
        color: white;
    }
    
    .featured-card .message-text {
        color: rgba(255, 255, 255, 0.9);
    }
    
    .pagination-container {
        display: flex;
        justify-content: center;
        margin-top: 3rem;
    }
    
    .pagination {
        display: flex;
        gap: 0.5rem;
    }
    
    .pagination a,
    .pagination span {
        padding: 0.75rem 1rem;
        border-radius: 8px;
        text-decoration: none;
        font-weight: 500;
        transition: all 0.3s ease;
    }
    
    .pagination a {
        background: white;
        color: #667eea;
        border: 1px solid #e2e8f0;
    }
    
    .pagination a:hover {
        background: #667eea;
        color: white;
        transform: translateY(-1px);
    }
    
    .pagination .current {
        background: #667eea;
        color: white;
        border: 1px solid #667eea;
    }
    
    .cta-section {
        background: white;
        border-radius: 16px;
        padding: 3rem;
        text-align: center;
        margin-top: 3rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    }
    
    .cta-section h3 {
        color: #2d3748;
        margin-bottom: 1rem;
    }
    
    .cta-section p {
        color: #718096;
        margin-bottom: 2rem;
    }
    
    .btn-primary-gradient {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        color: white;
        padding: 0.875rem 2rem;
        border-radius: 10px;
        font-weight: 600;
        text-decoration: none;
        display: inline-block;
        transition: all 0.3s ease;
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
    }
    
    .btn-primary-gradient:hover {
        background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        color: white;
        text-decoration: none;
    }
    
    @media (max-width: 768px) {
        .messages-hero h1 {
            font-size: 2.5rem;
        }
        
        .messages-hero {
            padding: 3rem 0;
        }
        
        .messages-container {
            padding: 3rem 0;
        }
        
        .message-card {
            padding: 1.5rem;
        }
        
        .featured-grid {
            grid-template-columns: 1fr;
        }
        
        .stats-grid {
            grid-template-columns: repeat(2, 1fr);
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="messages-hero">
    <div class="container">
        <h1>Community Messages</h1>
        <p>See what our community is saying about our school fees management platform</p>
    </div>
</section>

<!-- Messages Container -->
<section class="messages-container">
    <div class="container">
        <!-- Statistics Section -->
        <div class="stats-section">
            <div class="stats-grid">
                <div class="stat-item">
                    <h3>{{ total_messages }}</h3>
                    <p>Community Messages</p>
                </div>
                <div class="stat-item">
                    <h3>{{ featured_messages|length }}</h3>
                    <p>Featured Stories</p>
                </div>
                <div class="stat-item">
                    <h3>{{ inquiry_types|length }}</h3>
                    <p>Inquiry Categories</p>
                </div>
                <div class="stat-item">
                    <h3>24h</h3>
                    <p>Average Response Time</p>
                </div>
            </div>
        </div>
        
        <!-- Featured Messages -->
        {% if featured_messages %}
        <div class="featured-section">
            <div class="section-title">
                <h2>Featured Messages</h2>
                <p>Highlighted stories from our community</p>
            </div>
            
            <div class="featured-grid">
                {% for message in featured_messages %}
                <div class="featured-card">
                    <div class="message-header">
                        <div class="message-avatar">
                            {{ message.name|first|upper }}
                        </div>
                        <div class="message-info">
                            <h4>{{ message.name }}</h4>
                            <div class="message-meta">
                                <span class="inquiry-badge">{{ message.get_inquiry_type_display }}</span>
                                <span>{{ message.submitted_at|date:"M d, Y" }}</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="message-content">
                        <div class="message-title">
                            {{ message.broadcast_title|default:message.subject }}
                        </div>
                        <div class="message-text">
                            {{ message.broadcast_excerpt|default:message.message|truncatewords:30 }}
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}
        
        <!-- All Messages -->
        <div class="section-title">
            <h2>All Community Messages</h2>
            <p>Messages from schools and educators using our platform</p>
        </div>
        
        {% for message in messages %}
        <div class="message-card">
            <div class="message-header">
                <div class="message-avatar">
                    {{ message.name|first|upper }}
                </div>
                <div class="message-info">
                    <h4>{{ message.name }}{% if message.organization %} - {{ message.organization }}{% endif %}</h4>
                    <div class="message-meta">
                        <span class="inquiry-badge">{{ message.get_inquiry_type_display }}</span>
                        {% if message.is_featured %}
                            <span class="featured-badge">Featured</span>
                        {% endif %}
                        <span>{{ message.submitted_at|date:"M d, Y" }}</span>
                    </div>
                </div>
            </div>
            
            <div class="message-content">
                <div class="message-title">
                    {{ message.broadcast_title|default:message.subject }}
                </div>
                <div class="message-text">
                    {{ message.broadcast_excerpt|default:message.message|truncatewords:50 }}
                </div>
            </div>
            
            <div class="message-footer">
                <div class="message-date">
                    Submitted {{ message.submitted_at|timesince }} ago
                </div>
            </div>
        </div>
        {% empty %}
        <div class="message-card">
            <div class="text-center">
                <h4>No messages yet</h4>
                <p>Be the first to share your experience with our platform!</p>
                <a href="{% url 'public_site:contact' %}" class="btn-primary-gradient">
                    <i class="bi bi-chat-dots me-2"></i>
                    Send a Message
                </a>
            </div>
        </div>
        {% endfor %}
        
        <!-- Pagination -->
        {% if is_paginated %}
        <div class="pagination-container">
            <div class="pagination">
                {% if page_obj.has_previous %}
                    <a href="?page=1">&laquo; First</a>
                    <a href="?page={{ page_obj.previous_page_number }}">Previous</a>
                {% endif %}
                
                {% for num in page_obj.paginator.page_range %}
                    {% if page_obj.number == num %}
                        <span class="current">{{ num }}</span>
                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                        <a href="?page={{ num }}">{{ num }}</a>
                    {% endif %}
                {% endfor %}
                
                {% if page_obj.has_next %}
                    <a href="?page={{ page_obj.next_page_number }}">Next</a>
                    <a href="?page={{ page_obj.paginator.num_pages }}">Last &raquo;</a>
                {% endif %}
            </div>
        </div>
        {% endif %}
        
        <!-- Call to Action -->
        <div class="cta-section">
            <h3>Share Your Experience</h3>
            <p>Have a question or want to share your success story? We'd love to hear from you!</p>
            <a href="{% url 'public_site:contact' %}" class="btn-primary-gradient">
                <i class="bi bi-envelope me-2"></i>
                Contact Us
            </a>
        </div>
    </div>
</section>
{% endblock %}
