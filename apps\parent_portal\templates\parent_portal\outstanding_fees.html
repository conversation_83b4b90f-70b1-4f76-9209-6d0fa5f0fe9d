{# templates/parent_portal/outstanding_fees.html #}
{% extends "parent_portal/parent_portal_base.html" %}

{% load humanize core_tags %}

{% block parent_portal_page_title %}{{ view_title|default:"Outstanding Fees" }}{% endblock %}

{% block parent_portal_main_content %}
<div class="container mt-4">
    <h1>{{ view_title }}</h1>
    <p>Showing all invoices with a current outstanding balance for your children.</p>

    {% include "includes/_messages.html" %}

    {# Display Total #}
    <div class="alert alert-warning text-center">
        <strong>Total Outstanding Balance: {{ total_outstanding|currency:school_profile.currency_symbol }}</strong>
    </div>

    {% if invoices %}
        <div class="table-responsive">
            <table class="table table-striped table-bordered table-hover table-sm">
                <thead class="table-light">
                    <tr>
                        <th>Inv #</th>
                        <th>Student</th>
                        <th>Issue Date</th>
                        <th>Due Date</th>
                        <th>Period</th>
                        <th style="text-align: right;">Net Amt</th>
                        <th style="text-align: right;">Paid</th>
                        <th style="text-align: right;">Due Now</th>
                        <th style="text-align: center;">Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for invoice in invoices %}
                    <tr>
                        <td>{{ invoice.invoice_number|default:invoice.pk }}</td>
                        <td>{{ invoice.student.full_name|default:"N/A" }}</td>
                        <td>{{ invoice.issue_date|date:"Y-m-d" }}</td>
                        <td>{{ invoice.due_date|date:"Y-m-d" }}</td>
                        <td>{{ invoice.period_description|default:"N/A" }}</td>
                        <td style="text-align: right;">{{ invoice.net_amount|floatformat:2|intcomma }}</td>
                        <td style="text-align: right;">{{ invoice.amount_paid|floatformat:2|intcomma }}</td>
                        <td style="text-align: right;">{{ invoice.amount_due|floatformat:2|intcomma }}</td>
                        <td style="text-align: center;">
                            <span class="badge bg-{% if invoice.status == 'PAID' %}success{% elif invoice.status == 'OVERDUE'%}danger{% elif invoice.status == 'PARTIAL'%}warning text-dark{% elif invoice.status == 'CANCELLED'%}secondary{% else %}primary{% endif %}">
                                {{ invoice.get_status_display }}
                            </span>
                        </td>
                        <td> {# Actions #}
                            <a href="#" class="btn btn-info btn-sm" title="View Details">View</a> {# Link to Invoice Detail later #}
                            <a href="#" class="btn btn-secondary btn-sm" target="_blank" title="View PDF">PDF</a> {# Link to Invoice PDF later #}
                            <a href="#" class="btn btn-success btn-sm" title="Pay Online">Pay</a> {# Link to Payment Gateway later #}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        {# Pagination Controls #}
        {% if is_paginated %}
            <nav aria-label="Invoice navigation" class="mt-3">
                <ul class="pagination justify-content-center">
                    {# Standard pagination block - copy from payment_history.html #}
                    {% if page_obj.has_previous %}
                        <li class="page-item"><a class="page-link" href="?page=1">« First</a></li>
                        <li class="page-item"><a class="page-link" href="?page={{ page_obj.previous_page_number }}">Previous</a></li>
                    {% else %}
                        <li class="page-item disabled"><span class="page-link">« First</span></li>
                        <li class="page-item disabled"><span class="page-link">Previous</span></li>
                    {% endif %}
                    <li class="page-item active"><span class="page-link">Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}</span></li>
                    {% if page_obj.has_next %}
                        <li class="page-item"><a class="page-link" href="?page={{ page_obj.next_page_number }}">Next</a></li>
                        <li class="page-item"><a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">Last »</a></li>
                    {% else %}
                        <li class="page-item disabled"><span class="page-link">Next</span></li>
                        <li class="page-item disabled"><span class="page-link">Last »</span></li>
                    {% endif %}
                </ul>
            </nav>
        {% endif %} {# End Pagination #}

    {% else %}
        <div class="alert alert-success">No outstanding fees found for your children.</div>
    {% endif %}

    <div class="footer-actions mt-3">
        <a href="{% url 'parent_portal:dashboard' %}" class="btn btn-secondary">Back to Dashboard</a>
    </div>
</div>
{% endblock parent_portal_main_content %}

