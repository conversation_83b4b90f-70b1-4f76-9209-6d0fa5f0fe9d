#!/usr/bin/env python
"""
Create Subscription Plans
Creates sample subscription plans for the upgrade system

Usage: python create_subscription_plans.py
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from decimal import Decimal
from apps.subscriptions.models import SubscriptionPlan, Feature
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_features():
    """Create feature flags for plans"""
    logger.info("Creating feature flags...")
    
    features_data = [
        {
            'name': 'Advanced Reporting',
            'code': 'ADVANCED_REPORTING',
            'description': 'Access to all 33 reports including financial statements'
        },
        {
            'name': 'Multi-Class Management',
            'code': 'MULTI_CLASS',
            'description': 'Manage multiple classes and sections'
        },
        {
            'name': 'Parent Portal',
            'code': 'PARENT_PORTAL',
            'description': 'Dedicated portal for parents to view student information'
        },
        {
            'name': 'SMS Notifications',
            'code': 'SMS_NOTIFICATIONS',
            'description': 'Send SMS notifications to parents and staff'
        },
        {
            'name': 'Email Integration',
            'code': 'EMAIL_INTEGRATION',
            'description': 'Automated email notifications and communications'
        },
        {
            'name': 'Custom Branding',
            'code': 'CUSTOM_BRANDING',
            'description': 'Customize the system with your school logo and colors'
        },
        {
            'name': 'API Access',
            'code': 'API_ACCESS',
            'description': 'REST API access for third-party integrations'
        },
        {
            'name': 'Priority Support',
            'code': 'PRIORITY_SUPPORT',
            'description': '24/7 priority customer support'
        }
    ]

    created_features = {}
    for feature_data in features_data:
        feature, created = Feature.objects.get_or_create(
            code=feature_data['code'],
            defaults={
                'name': feature_data['name'],
                'description': feature_data['description']
            }
        )
        created_features[feature_data['code']] = feature
        if created:
            logger.info(f"✅ Created feature: {feature.name}")
        else:
            logger.info(f"✅ Feature exists: {feature.name}")
    
    return created_features

def create_subscription_plans():
    """Create subscription plans"""
    logger.info("Creating subscription plans...")
    
    # First create features
    features = create_features()
    
    plans_data = [
        {
            'name': 'Starter',
            'slug': 'starter',
            'description': 'Perfect for small schools just getting started',
            'price_monthly': Decimal('29.99'),
            'price_annually': Decimal('299.99'),
            'max_students': 100,
            'max_staff': 5,
            'trial_period_days': 14,
            'display_order': 1,
            'features': ['EMAIL_INTEGRATION']
        },
        {
            'name': 'Professional',
            'slug': 'professional',
            'description': 'Ideal for growing schools with comprehensive needs',
            'price_monthly': Decimal('79.99'),
            'price_annually': Decimal('799.99'),
            'max_students': 500,
            'max_staff': 20,
            'trial_period_days': 14,
            'display_order': 2,
            'features': ['ADVANCED_REPORTING', 'MULTI_CLASS', 'PARENT_PORTAL', 'EMAIL_INTEGRATION', 'CUSTOM_BRANDING']
        },
        {
            'name': 'Enterprise',
            'slug': 'enterprise',
            'description': 'Complete solution for large schools and institutions',
            'price_monthly': Decimal('149.99'),
            'price_annually': Decimal('1499.99'),
            'max_students': 2000,
            'max_staff': None,  # Unlimited
            'trial_period_days': 30,
            'display_order': 3,
            'features': ['ADVANCED_REPORTING', 'MULTI_CLASS', 'PARENT_PORTAL', 'SMS_NOTIFICATIONS',
                        'EMAIL_INTEGRATION', 'CUSTOM_BRANDING', 'API_ACCESS', 'PRIORITY_SUPPORT']
        },
        {
            'name': 'Trial Plan',
            'slug': 'trial-plan',
            'description': 'Free trial with full access to all features',
            'price_monthly': Decimal('0.00'),
            'price_annually': Decimal('0.00'),
            'max_students': 50,
            'max_staff': 3,
            'trial_period_days': 7,
            'display_order': 0,
            'is_public': False,  # Don't show on pricing page
            'features': ['ADVANCED_REPORTING', 'MULTI_CLASS', 'PARENT_PORTAL', 'EMAIL_INTEGRATION']
        }
    ]
    
    created_plans = []
    for plan_data in plans_data:
        plan_features = plan_data.pop('features', [])
        
        plan, created = SubscriptionPlan.objects.get_or_create(
            slug=plan_data['slug'],
            defaults=plan_data
        )
        
        if created:
            logger.info(f"✅ Created plan: {plan.name}")
            
            # Add features to the plan
            for feature_code in plan_features:
                if feature_code in features:
                    plan.features.add(features[feature_code])
            
            logger.info(f"   Added {len(plan_features)} features to {plan.name}")
        else:
            logger.info(f"✅ Plan exists: {plan.name}")
        
        created_plans.append(plan)
    
    return created_plans

def display_plans_summary():
    """Display a summary of all plans"""
    logger.info("\n=== SUBSCRIPTION PLANS SUMMARY ===")
    
    plans = SubscriptionPlan.objects.all().order_by('display_order')
    
    for plan in plans:
        logger.info(f"\n📋 {plan.name}")
        logger.info(f"   💰 Monthly: ${plan.price_monthly} | Annual: ${plan.price_annually}")
        logger.info(f"   👥 Students: {plan.max_students} | Staff: {plan.max_staff or 'Unlimited'}")
        logger.info(f"   🎯 Trial: {plan.trial_period_days} days")
        logger.info(f"   🔧 Features: {', '.join([f.name for f in plan.features.all()])}")
        logger.info(f"   📊 Public: {plan.is_public} | Active: {plan.is_active}")

def main():
    """Main function"""
    logger.info("=== CREATING SUBSCRIPTION PLANS ===")
    
    try:
        # Create plans
        plans = create_subscription_plans()
        
        # Display summary
        display_plans_summary()
        
        logger.info(f"\n🎉 SUBSCRIPTION PLANS SETUP COMPLETE!")
        logger.info(f"Created/verified {len(plans)} subscription plans.")
        logger.info("\nUsers can now:")
        logger.info("- View plans at: /subscriptions/pricing/")
        logger.info("- Select plans at: /subscriptions/select-plan/")
        logger.info("- Upgrade when trial expires")
        
        return True
        
    except Exception as e:
        logger.error(f"Failed to create subscription plans: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
