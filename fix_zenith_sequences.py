#!/usr/bin/env python
"""
Fix Zenith Sequences
Fix the sequence tables in zenith tenant

Usage: python fix_zenith_sequences.py
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from django.db import connection
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def fix_zenith_sequences():
    """Fix sequence tables in zenith tenant"""
    
    logger.info("=== FIXING ZENITH SEQUENCES ===")
    
    try:
        with connection.cursor() as cursor:
            cursor.execute('SET search_path TO "zenith"')
            
            # Check and fix invoice sequence
            try:
                cursor.execute("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.columns 
                        WHERE table_schema = 'zenith' 
                        AND table_name = 'schools_invoicesequence'
                        AND column_name = 'current_number'
                    )
                """)
                
                if not cursor.fetchone()[0]:
                    cursor.execute("ALTER TABLE schools_invoicesequence ADD COLUMN current_number INTEGER DEFAULT 1")
                    logger.info("✅ Added current_number column to schools_invoicesequence")
                
                # Insert data if empty
                cursor.execute("SELECT COUNT(*) FROM schools_invoicesequence")
                if cursor.fetchone()[0] == 0:
                    cursor.execute("""
                        INSERT INTO schools_invoicesequence (prefix, current_number, created_at, updated_at) 
                        VALUES ('INV', 1, NOW(), NOW())
                    """)
                    logger.info("✅ Created invoice sequence data")
                    
            except Exception as e:
                logger.error(f"❌ Error fixing invoice sequence: {e}")
            
            # Check and fix receipt sequence
            try:
                cursor.execute("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.columns 
                        WHERE table_schema = 'zenith' 
                        AND table_name = 'schools_receiptsequence'
                        AND column_name = 'current_number'
                    )
                """)
                
                if not cursor.fetchone()[0]:
                    cursor.execute("ALTER TABLE schools_receiptsequence ADD COLUMN current_number INTEGER DEFAULT 1")
                    logger.info("✅ Added current_number column to schools_receiptsequence")
                
                # Insert data if empty
                cursor.execute("SELECT COUNT(*) FROM schools_receiptsequence")
                if cursor.fetchone()[0] == 0:
                    cursor.execute("""
                        INSERT INTO schools_receiptsequence (prefix, current_number, created_at, updated_at) 
                        VALUES ('RCP', 1, NOW(), NOW())
                    """)
                    logger.info("✅ Created receipt sequence data")
                    
            except Exception as e:
                logger.error(f"❌ Error fixing receipt sequence: {e}")
            
            # Create academic year if missing
            try:
                cursor.execute("SELECT COUNT(*) FROM schools_academicyear")
                if cursor.fetchone()[0] == 0:
                    cursor.execute("""
                        INSERT INTO schools_academicyear (name, start_date, end_date, is_active, is_current, created_at, updated_at) 
                        VALUES ('2024-2025', '2024-09-01', '2025-08-31', TRUE, TRUE, NOW(), NOW())
                    """)
                    logger.info("✅ Created academic year")
                    
            except Exception as e:
                logger.error(f"❌ Error creating academic year: {e}")
            
            # Create academic settings if missing
            try:
                cursor.execute("SELECT COUNT(*) FROM schools_academicsetting")
                if cursor.fetchone()[0] == 0:
                    cursor.execute("""
                        INSERT INTO schools_academicsetting (academic_year_id, created_at, updated_at) 
                        VALUES (1, NOW(), NOW())
                    """)
                    logger.info("✅ Created academic settings")
                    
            except Exception as e:
                logger.error(f"❌ Error creating academic settings: {e}")
        
        logger.info("✅ Zenith sequences fixed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to fix zenith sequences: {e}")
        return False

def main():
    """Main function"""
    logger.info("=== FIX ZENITH SEQUENCES ===")
    
    try:
        success = fix_zenith_sequences()
        
        if success:
            logger.info("\n🎉 ZENITH SEQUENCES FIXED!")
        else:
            logger.error("\n❌ ZENITH SEQUENCES FIX FAILED!")
        
        return success
        
    except Exception as e:
        logger.error(f"Fix zenith sequences failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
