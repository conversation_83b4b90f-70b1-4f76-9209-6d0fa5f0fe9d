# apps/subscriptions/decorators.py

from functools import wraps
from django.shortcuts import redirect
from django.contrib import messages
from django.urls import reverse
from django.utils.translation import gettext_lazy as _
from django.utils import timezone

from apps.subscriptions.models import Subscription


def trial_student_limit_required(view_func):
    """
    Decorator to enforce student limits during trial period.
    Redirects to plan selection if limit is exceeded.
    """
    @wraps(view_func)
    def wrapper(request, *args, **kwargs):
        # Skip for superusers
        if request.user.is_authenticated and request.user.is_superuser:
            return view_func(request, *args, **kwargs)
        
        # Get current tenant
        tenant = getattr(request, 'tenant', None)
        if not tenant:
            return view_func(request, *args, **kwargs)
        
        try:
            subscription = tenant.subscription
        except Subscription.DoesNotExist:
            # No subscription - redirect to plan selection
            messages.warning(request, _("Please select a subscription plan to continue."))
            return redirect(reverse('subscriptions:plan_selection'))
        
        # Check if this is a trial subscription with student limits
        if (subscription.status == Subscription.Status.TRIALING and 
            subscription.plan.max_students <= 10):  # Trial plan limit
            
            from apps.students.models import Student
            current_student_count = Student.objects.filter(is_active=True).count()
            
            # If at or over limit, block student creation
            if current_student_count >= subscription.plan.max_students:
                messages.error(
                    request,
                    _(f"Trial limit reached! You can only have {subscription.plan.max_students} students "
                      f"during your trial. Upgrade to a paid plan to add more students.")
                )
                return redirect(reverse('subscriptions:plan_selection'))
        
        return view_func(request, *args, **kwargs)
    
    return wrapper


def subscription_required(view_func):
    """
    Decorator to ensure user has an active subscription.
    Redirects to plan selection if no subscription or subscription is not usable.
    """
    @wraps(view_func)
    def wrapper(request, *args, **kwargs):
        # Skip for superusers
        if request.user.is_authenticated and request.user.is_superuser:
            return view_func(request, *args, **kwargs)
        
        # Get current tenant
        tenant = getattr(request, 'tenant', None)
        if not tenant:
            return view_func(request, *args, **kwargs)
        
        try:
            subscription = tenant.subscription
        except Subscription.DoesNotExist:
            messages.warning(request, _("Please select a subscription plan to continue."))
            return redirect(reverse('subscriptions:plan_selection'))
        
        # Check if subscription is usable
        if not subscription.is_usable:
            if subscription.status == Subscription.Status.TRIALING:
                if subscription.trial_end_date and subscription.trial_end_date <= timezone.now():
                    messages.error(
                        request,
                        _("Your trial has expired. Please upgrade to a paid plan to continue.")
                    )
                else:
                    # Trial is still active, allow access
                    return view_func(request, *args, **kwargs)
            elif subscription.status == Subscription.Status.PAST_DUE:
                messages.error(
                    request,
                    _("Your subscription payment is past due. Please update your payment method.")
                )
            elif subscription.status == Subscription.Status.CANCELLED:
                messages.error(
                    request,
                    _("Your subscription has been cancelled. Please reactivate or choose a new plan.")
                )
            else:
                messages.error(
                    request,
                    _("Your subscription is not active. Please contact support or choose a new plan.")
                )
            
            return redirect(reverse('subscriptions:plan_selection'))
        
        return view_func(request, *args, **kwargs)
    
    return wrapper


def trial_feature_required(feature_name):
    """
    Decorator to check if a specific feature is available in the current subscription.
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            # Skip for superusers
            if request.user.is_authenticated and request.user.is_superuser:
                return view_func(request, *args, **kwargs)
            
            # Get current tenant
            tenant = getattr(request, 'tenant', None)
            if not tenant:
                return view_func(request, *args, **kwargs)
            
            try:
                subscription = tenant.subscription
            except Subscription.DoesNotExist:
                messages.warning(request, _("Please select a subscription plan to access this feature."))
                return redirect(reverse('subscriptions:plan_selection'))
            
            # Check if feature is available in current plan
            if not subscription.plan.features.filter(code=feature_name).exists():
                messages.error(
                    request,
                    _(f"This feature is not available in your current plan. Please upgrade to access {feature_name.replace('_', ' ').title()}.")
                )
                return redirect(reverse('subscriptions:plan_selection'))
            
            return view_func(request, *args, **kwargs)
        
        return wrapper
    return decorator
