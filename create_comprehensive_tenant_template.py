#!/usr/bin/env python
"""
Create Comprehensive Tenant Template
Creates a complete tenant template that ensures smooth tenant creation

Usage: python create_comprehensive_tenant_template.py
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from django.db import connection
from django.core.management import call_command
import logging
import json

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_comprehensive_tenant_template():
    """Create a comprehensive tenant template from alpha"""
    
    logger.info("=== CREATING COMPREHENSIVE TENANT TEMPLATE ===")
    
    try:
        with connection.cursor() as cursor:
            cursor.execute('SET search_path TO "alpha"')
            
            # 1. Get complete table structures
            cursor.execute("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'alpha' 
                AND table_type = 'BASE TABLE'
                ORDER BY table_name
            """)
            
            tables = [row[0] for row in cursor.fetchall()]
            logger.info(f"Found {len(tables)} tables in alpha")
            
            template_data = {
                'tables': {},
                'sequences': {},
                'indexes': {},
                'constraints': {},
                'essential_data': {}
            }
            
            # 2. Get detailed structure for each table
            for table in tables:
                logger.info(f"Processing table: {table}")
                
                # Get column definitions
                cursor.execute(f"""
                    SELECT 
                        column_name, 
                        data_type, 
                        character_maximum_length,
                        numeric_precision,
                        numeric_scale,
                        is_nullable, 
                        column_default,
                        ordinal_position
                    FROM information_schema.columns 
                    WHERE table_schema = 'alpha' 
                    AND table_name = '{table}'
                    ORDER BY ordinal_position
                """)
                
                columns = cursor.fetchall()
                template_data['tables'][table] = columns
                
                # Get indexes
                cursor.execute(f"""
                    SELECT indexname, indexdef
                    FROM pg_indexes 
                    WHERE schemaname = 'alpha' 
                    AND tablename = '{table}'
                """)
                
                indexes = cursor.fetchall()
                if indexes:
                    template_data['indexes'][table] = indexes
                
                # Get constraints
                cursor.execute(f"""
                    SELECT 
                        tc.constraint_name,
                        tc.constraint_type,
                        kcu.column_name,
                        ccu.table_name AS foreign_table_name,
                        ccu.column_name AS foreign_column_name
                    FROM information_schema.table_constraints tc
                    LEFT JOIN information_schema.key_column_usage kcu 
                        ON tc.constraint_name = kcu.constraint_name
                    LEFT JOIN information_schema.constraint_column_usage ccu 
                        ON ccu.constraint_name = tc.constraint_name
                    WHERE tc.table_schema = 'alpha'
                    AND tc.table_name = '{table}'
                """)
                
                constraints = cursor.fetchall()
                if constraints:
                    template_data['constraints'][table] = constraints
            
            # 3. Get essential data from key tables
            essential_tables = [
                'auth_group', 'auth_permission', 'django_content_type',
                'fees_concessiontype', 'hr_leavetype', 'payments_paymentmethod',
                'school_calendar_eventcategory', 'hr_salarycomponent', 
                'hr_statutorydeduction', 'hr_taxbracket', 'finance_expensecategory'
            ]
            
            for table in essential_tables:
                if table in template_data['tables']:
                    try:
                        cursor.execute(f"SELECT * FROM {table}")
                        rows = cursor.fetchall()
                        
                        # Get column names
                        cursor.execute(f"""
                            SELECT column_name 
                            FROM information_schema.columns 
                            WHERE table_schema = 'alpha' 
                            AND table_name = '{table}'
                            ORDER BY ordinal_position
                        """)
                        
                        col_names = [row[0] for row in cursor.fetchall()]
                        
                        template_data['essential_data'][table] = {
                            'columns': col_names,
                            'rows': rows
                        }
                        
                        logger.info(f"Captured {len(rows)} rows from {table}")
                        
                    except Exception as e:
                        logger.warning(f"Could not capture data from {table}: {e}")
            
            # 4. Save template to file
            template_file = "tenant_template.json"
            with open(template_file, 'w') as f:
                # Convert to JSON-serializable format
                json_template = {}
                for key, value in template_data.items():
                    if key == 'essential_data':
                        json_template[key] = {}
                        for table, data in value.items():
                            json_template[key][table] = {
                                'columns': data['columns'],
                                'rows': [list(row) for row in data['rows']]
                            }
                    else:
                        json_template[key] = {k: [list(row) for row in v] if isinstance(v, list) else v for k, v in value.items()}
                
                json.dump(json_template, f, indent=2, default=str)
            
            logger.info(f"✅ Comprehensive tenant template saved to {template_file}")
            
            # 5. Create SQL template file
            create_sql_template(template_data)
            
            return True
            
    except Exception as e:
        logger.error(f"❌ Failed to create comprehensive tenant template: {e}")
        return False

def create_sql_template(template_data):
    """Create SQL template file"""
    
    logger.info("Creating SQL template file...")
    
    sql_statements = []
    sql_statements.append("-- Comprehensive Tenant Template SQL")
    sql_statements.append("-- Auto-generated from alpha schema")
    sql_statements.append("-- This ensures smooth tenant creation")
    sql_statements.append("")
    
    # Create tables
    for table_name, columns in template_data['tables'].items():
        sql_statements.append(f"-- Table: {table_name}")
        
        column_defs = []
        for col_name, data_type, max_len, num_prec, num_scale, nullable, default, position in columns:
            col_def = f'"{col_name}" '
            
            # Handle data types
            if data_type == 'character varying':
                if max_len:
                    col_def += f'VARCHAR({max_len})'
                else:
                    col_def += 'VARCHAR(255)'
            elif data_type == 'numeric' and num_prec and num_scale:
                col_def += f'NUMERIC({num_prec},{num_scale})'
            elif data_type == 'timestamp with time zone':
                col_def += 'TIMESTAMP WITH TIME ZONE'
            elif data_type == 'timestamp without time zone':
                col_def += 'TIMESTAMP'
            else:
                col_def += data_type.upper()
            
            # Handle nullable
            if nullable == 'NO':
                col_def += ' NOT NULL'
            
            # Handle defaults (skip sequences)
            if default and not default.startswith('nextval'):
                col_def += f' DEFAULT {default}'
            
            column_defs.append(col_def)
        
        create_sql = f'CREATE TABLE IF NOT EXISTS "{table_name}" ({", ".join(column_defs)});'
        sql_statements.append(create_sql)
        
        # Add sequence if needed
        if any(col[0] == 'id' for col in columns):
            sql_statements.append(f'CREATE SEQUENCE IF NOT EXISTS {table_name}_id_seq;')
            sql_statements.append(f'ALTER TABLE "{table_name}" ALTER COLUMN id SET DEFAULT nextval(\'{table_name}_id_seq\');')
        
        sql_statements.append("")
    
    # Add essential data
    sql_statements.append("-- Essential Data")
    for table_name, data in template_data['essential_data'].items():
        if data['rows']:
            columns_str = ', '.join([f'"{col}"' for col in data['columns']])
            
            sql_statements.append(f"-- Data for {table_name}")
            sql_statements.append(f"DELETE FROM {table_name};")
            
            for row in data['rows']:
                values = []
                for value in row:
                    if value is None:
                        values.append('NULL')
                    elif isinstance(value, str):
                        escaped_value = value.replace("'", "''")
                        values.append(f"'{escaped_value}'")
                    elif isinstance(value, bool):
                        values.append('TRUE' if value else 'FALSE')
                    else:
                        values.append(str(value))
                
                insert_sql = f"INSERT INTO {table_name} ({columns_str}) VALUES ({', '.join(values)});"
                sql_statements.append(insert_sql)
            
            sql_statements.append("")
    
    # Write SQL file
    with open("tenant_template.sql", 'w') as f:
        f.write('\n'.join(sql_statements))
    
    logger.info("✅ SQL template file created: tenant_template.sql")

def main():
    """Main function"""
    logger.info("=== COMPREHENSIVE TENANT TEMPLATE CREATION ===")
    
    try:
        success = create_comprehensive_tenant_template()
        
        if success:
            logger.info("\n🎉 COMPREHENSIVE TENANT TEMPLATE CREATED!")
            logger.info("Files created:")
            logger.info("- tenant_template.json (complete structure data)")
            logger.info("- tenant_template.sql (SQL for tenant creation)")
            logger.info("\nThis template ensures smooth tenant creation without manual fixes!")
        else:
            logger.error("❌ Failed to create comprehensive tenant template")
        
        return success
        
    except Exception as e:
        logger.error(f"Comprehensive tenant template creation failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
