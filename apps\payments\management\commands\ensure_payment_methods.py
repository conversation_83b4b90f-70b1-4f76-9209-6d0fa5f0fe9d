# D:\school_fees_saas_v2\apps\payments\management\commands\ensure_payment_methods.py

from django.core.management.base import BaseCommand
from django_tenants.utils import schema_context
from apps.tenants.models import School
from apps.payments.models import PaymentMethod


class Command(BaseCommand):
    help = 'Ensures essential payment methods exist for all tenants'

    def handle(self, *args, **options):
        # Get all tenants
        tenants = School.objects.all()
        
        for tenant in tenants:
            with schema_context(tenant.schema_name):
                self.stdout.write(f"Processing tenant: {tenant.name} ({tenant.schema_name})")
                
                # Essential payment methods to ensure exist
                essential_methods = [
                    {
                        'name': 'Online Mock Payment',
                        'type': 'ONLINE_MOCK',
                        'description': 'Mock payment method for testing online payments'
                    },
                    {
                        'name': 'Cash Payment',
                        'type': 'CASH',
                        'description': 'Cash payments received at school'
                    },
                    {
                        'name': 'Bank Transfer',
                        'type': 'BANK_TRANSFER',
                        'description': 'Bank transfer payments'
                    }
                ]
                
                for method_data in essential_methods:
                    payment_method, created = PaymentMethod.objects.get_or_create(
                        type=method_data['type'],
                        defaults={
                            'name': method_data['name'],
                            'description': method_data['description'],
                            'is_active': True
                        }
                    )
                    
                    if created:
                        self.stdout.write(
                            self.style.SUCCESS(f"  Created: {payment_method.name}")
                        )
                    else:
                        self.stdout.write(f"  Exists: {payment_method.name}")
                        
                self.stdout.write("")
