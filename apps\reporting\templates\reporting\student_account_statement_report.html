{% extends "tenant_base.html" %}
{% load humanize static core_tags %}

{% block title %}{{ view_title|default:"Student Account Statement" }}{% endblock %}

{% block content %}
<div class="container-fluid mt-3">
    {% with report_icon_class="bi bi-person-lines-fill" %}
        {% include "partials/_report_header.html" %}
    {% endwith %}

    {% include "partials/_messages.html" %}
    {% include "partials/_report_filter_form.html" with filter_form=filter_form report_code=report_code specific_filter_title="Student Statement Filters" %}

    {% if student %}
    <!-- Student Information Card -->
    <div class="card mb-4">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="bi bi-person-badge me-2"></i>Student Information</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <p><strong>Name:</strong> {{ student.get_full_name }}</p>
                    <p><strong>Student ID:</strong> {{ student.student_id }}</p>
                    <p><strong>Class:</strong> {{ student.current_class.name|default:"Not Assigned" }}</p>
                </div>
                <div class="col-md-6">
                    <p><strong>Statement Period:</strong> {{ start_date|date:"M d, Y" }} to {{ end_date|date:"M d, Y" }}</p>
                    <p><strong>Statement Type:</strong> 
                        {% if statement_type == 'detailed' %}Detailed Statement
                        {% elif statement_type == 'summary' %}Summary Statement
                        {% else %}Outstanding Only{% endif %}
                    </p>
                    {% if student.parent %}
                    <p><strong>Parent:</strong> {{ student.parent.get_full_name }}</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Account Summary Card -->
    <div class="card mb-4">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="bi bi-calculator me-2"></i>Account Summary</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-2">
                    <div class="text-center">
                        <h6 class="text-muted">Opening Balance</h6>
                        <p class="mb-0 fw-bold {% if summary.opening_balance >= 0 %}text-danger{% else %}text-success{% endif %}">
                            {{ summary.opening_balance|currency }}
                        </p>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="text-center">
                        <h6 class="text-muted">Total Charges</h6>
                        <p class="mb-0 fw-bold text-danger">{{ summary.total_charges|currency }}</p>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="text-center">
                        <h6 class="text-muted">Total Payments</h6>
                        <p class="mb-0 fw-bold text-success">{{ summary.total_payments|currency }}</p>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="text-center">
                        <h6 class="text-muted">Closing Balance</h6>
                        <p class="mb-0 fw-bold {% if summary.closing_balance >= 0 %}text-danger{% else %}text-success{% endif %} fs-5">
                            {{ summary.closing_balance|currency }}
                        </p>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="text-center">
                        <h6 class="text-muted">Outstanding</h6>
                        <p class="mb-0 fw-bold text-warning">{{ summary.total_outstanding|currency }}</p>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="text-center">
                        <h6 class="text-muted">Outstanding Items</h6>
                        <p class="mb-0 fw-bold text-info">{{ summary.outstanding_invoices }}</p>
                    </div>
                </div>
            </div>
            
            <!-- Balance Status -->
            <div class="mt-3">
                {% if summary.closing_balance > 0 %}
                <div class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    <strong>Outstanding Balance:</strong> This account has an outstanding balance of {{ summary.closing_balance|currency }}.
                </div>
                {% elif summary.closing_balance < 0 %}
                <div class="alert alert-info">
                    <i class="bi bi-info-circle me-2"></i>
                    <strong>Credit Balance:</strong> This account has a credit balance of {{ summary.closing_balance|make_positive|currency }}.
                </div>
                {% else %}
                <div class="alert alert-success">
                    <i class="bi bi-check-circle me-2"></i>
                    <strong>Account Balanced:</strong> This account is fully paid up.
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Statement Details -->
    {% if statement_items %}
    <div class="card">
        <div class="card-header bg-light d-flex justify-content-between align-items-center">
            <h5 class="mb-0"><i class="bi bi-list-ul me-2"></i>
                {% if statement_type == 'summary' %}Monthly Summary
                {% elif statement_type == 'outstanding_only' %}Outstanding Items
                {% else %}Transaction Details{% endif %}
            </h5>
            <span class="badge bg-primary">{{ statement_items|length }} items</span>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-striped table-hover mb-0">
                    <thead class="table-dark">
                        <tr>
                            {% if statement_type == 'summary' %}
                            <th>Month</th>
                            <th class="text-center">Invoices</th>
                            <th class="text-center">Payments</th>
                            <th class="text-end">Charges</th>
                            <th class="text-end">Payments</th>
                            <th class="text-end">Net Amount</th>
                            {% else %}
                            <th>Date</th>
                            <th>Description</th>
                            <th>Reference</th>
                            <th>Academic Year</th>
                            <th>Term</th>
                            <th class="text-end">Charges</th>
                            <th class="text-end">Payments</th>
                            <th class="text-end">Balance</th>
                            <th class="text-center">Status</th>
                            {% endif %}
                        </tr>
                    </thead>
                    <tbody>
                        {% for item in statement_items %}
                        <tr {% if item.type == 'invoice' and item.outstanding > 0 %}class="table-warning"{% endif %}>
                            {% if statement_type == 'summary' %}
                            <td><strong>{{ item.month }}</strong></td>
                            <td class="text-center">
                                <span class="badge bg-info">{{ item.invoice_count }}</span>
                            </td>
                            <td class="text-center">
                                <span class="badge bg-success">{{ item.payment_count }}</span>
                            </td>
                            <td class="text-end">
                                <span class="text-danger fw-bold">{{ item.charges|currency }}</span>
                            </td>
                            <td class="text-end">
                                <span class="text-success fw-bold">{{ item.payments|currency }}</span>
                            </td>
                            <td class="text-end">
                                <span class="{% if item.charges|subtract:item.payments >= 0 %}text-danger{% else %}text-success{% endif %} fw-bold">
                                    {{ item.charges|subtract:item.payments|currency }}
                                </span>
                            </td>
                            {% else %}
                            <td>{{ item.date|date:"M d, Y" }}</td>
                            <td>
                                <strong>{{ item.description }}</strong>
                                {% if item.type == 'invoice' and item.outstanding > 0 %}
                                <br><small class="text-warning">Outstanding: {{ item.outstanding|currency }}</small>
                                {% endif %}
                            </td>
                            <td>
                                <span class="badge bg-secondary">{{ item.reference }}</span>
                                {% if item.type == 'payment' and item.invoice_number %}
                                <br><small class="text-muted">For: {{ item.invoice_number }}</small>
                                {% endif %}
                            </td>
                            <td>{{ item.academic_year }}</td>
                            <td>{{ item.term }}</td>
                            <td class="text-end">
                                {% if item.debit_amount > 0 %}
                                <span class="text-danger fw-bold">{{ item.debit_amount|currency }}</span>
                                {% else %}
                                <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td class="text-end">
                                {% if item.credit_amount > 0 %}
                                <span class="text-success fw-bold">{{ item.credit_amount|currency }}</span>
                                {% else %}
                                <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td class="text-end">
                                <span class="{% if item.balance >= 0 %}text-danger{% else %}text-success{% endif %} fw-bold">
                                    {{ item.balance|currency }}
                                </span>
                            </td>
                            <td class="text-center">
                                {% if item.status == 'Completed' %}
                                <span class="badge bg-success">{{ item.status }}</span>
                                {% elif item.status == 'Paid' %}
                                <span class="badge bg-success">{{ item.status }}</span>
                                {% elif item.status == 'Outstanding' %}
                                <span class="badge bg-warning">{{ item.status }}</span>
                                {% else %}
                                <span class="badge bg-secondary">{{ item.status }}</span>
                                {% endif %}
                            </td>
                            {% endif %}
                        </tr>
                        {% endfor %}
                    </tbody>
                    {% if statement_type != 'summary' %}
                    <tfoot class="table-light">
                        <tr>
                            <th colspan="5" class="text-end">Totals:</th>
                            <th class="text-end text-danger">{{ summary.total_charges|currency }}</th>
                            <th class="text-end text-success">{{ summary.total_payments|currency }}</th>
                            <th class="text-end {% if summary.closing_balance >= 0 %}text-danger{% else %}text-success{% endif %}">
                                {{ summary.closing_balance|currency }}
                            </th>
                            <th></th>
                        </tr>
                    </tfoot>
                    {% endif %}
                </table>
            </div>
        </div>
    </div>
    {% endif %}

    {% else %}
    <!-- No Student Selected -->
    <div class="card">
        <div class="card-body text-center py-5">
            <i class="bi bi-person-lines-fill display-1 text-muted mb-3"></i>
            <h4 class="text-muted">Select Student for Account Statement</h4>
            <p class="text-muted">Please select a student from the filter above to view their account statement.</p>
        </div>
    </div>
    {% endif %}

    <!-- Account Statement Notes -->
    {% if student %}
    <div class="alert alert-info mt-4">
        <i class="bi bi-info-circle me-2"></i>
        <strong>Statement Notes:</strong>
        <ul class="mb-0 mt-2">
            <li>Charges (positive amounts) increase the account balance</li>
            <li>Payments (negative amounts) reduce the account balance</li>
            <li>A positive balance indicates amount owed by the student</li>
            <li>A negative balance indicates credit in favor of the student</li>
            <li>Outstanding items are highlighted in yellow</li>
        </ul>
    </div>
    {% endif %}

</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Highlight outstanding items
    $('.table-warning').hover(
        function() {
            $(this).addClass('table-danger');
        },
        function() {
            $(this).removeClass('table-danger');
        }
    );
    
    // Add click handlers for transaction rows
    $('tbody tr').on('click', function() {
        $(this).toggleClass('table-active');
    });
    
    // Auto-submit form when student is selected
    $('#id_student').on('change', function() {
        if ($(this).val()) {
            $(this).closest('form').submit();
        }
    });
});
</script>
{% endblock %}
