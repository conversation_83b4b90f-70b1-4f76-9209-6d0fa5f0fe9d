# Generated by Django 5.1.7 on 2025-07-11 19:26

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('public_site', '0002_contactinquiry_addressed_at_and_more'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='testimonial',
            options={'ordering': ['-is_featured', '-submitted_on'], 'verbose_name': 'Review & Testimonial', 'verbose_name_plural': 'Reviews & Testimonials'},
        ),
        migrations.RemoveField(
            model_name='testimonial',
            name='author_title_school',
        ),
        migrations.RemoveField(
            model_name='testimonial',
            name='rating',
        ),
        migrations.AddField(
            model_name='testimonial',
            name='admin_notes',
            field=models.TextField(blank=True, help_text='Internal admin notes', null=True),
        ),
        migrations.AddField(
            model_name='testimonial',
            name='approved_on',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='testimonial',
            name='author_title',
            field=models.CharField(default='Administrator', help_text='e.g., Principal, Finance Manager', max_length=100, verbose_name='Job Title'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='testimonial',
            name='customer_support_rating',
            field=models.PositiveSmallIntegerField(blank=True, help_text='Quality of customer support (1-5 stars)', null=True, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)], verbose_name='Customer Support'),
        ),
        migrations.AddField(
            model_name='testimonial',
            name='ease_of_use_rating',
            field=models.PositiveSmallIntegerField(blank=True, help_text='How easy is the platform to use? (1-5 stars)', null=True, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)], verbose_name='Ease of Use'),
        ),
        migrations.AddField(
            model_name='testimonial',
            name='email',
            field=models.EmailField(blank=True, help_text='Optional - for follow-up questions', max_length=254, null=True),
        ),
        migrations.AddField(
            model_name='testimonial',
            name='ip_address',
            field=models.GenericIPAddressField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='testimonial',
            name='is_featured',
            field=models.BooleanField(default=False, help_text='Feature this review prominently'),
        ),
        migrations.AddField(
            model_name='testimonial',
            name='location',
            field=models.CharField(blank=True, help_text='City, State/Country', max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='testimonial',
            name='overall_rating',
            field=models.PositiveSmallIntegerField(default=5, help_text='Overall satisfaction (1-5 stars)', validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)], verbose_name='Overall Rating'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='testimonial',
            name='phone',
            field=models.CharField(blank=True, help_text='Optional phone number', max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='testimonial',
            name='previous_solution',
            field=models.CharField(blank=True, help_text='What did you use before our platform? (optional)', max_length=100, null=True, verbose_name='Previous Solution'),
        ),
        migrations.AddField(
            model_name='testimonial',
            name='review_category',
            field=models.CharField(choices=[('general', 'General Experience'), ('ease_of_use', 'Ease of Use'), ('customer_support', 'Customer Support'), ('features', 'Features & Functionality'), ('value_for_money', 'Value for Money'), ('implementation', 'Implementation Process'), ('reporting', 'Reporting & Analytics'), ('parent_portal', 'Parent Portal'), ('mobile_app', 'Mobile Experience'), ('integration', 'System Integration')], default='general', max_length=20, verbose_name='Review Category'),
        ),
        migrations.AddField(
            model_name='testimonial',
            name='school_name',
            field=models.CharField(default='Sample School', max_length=150, verbose_name='School/Organization Name'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='testimonial',
            name='school_size',
            field=models.CharField(blank=True, choices=[('small', 'Small (1-200 students)'), ('medium', 'Medium (201-500 students)'), ('large', 'Large (501-1000 students)'), ('very_large', 'Very Large (1000+ students)')], max_length=20, null=True, verbose_name='School Size'),
        ),
        migrations.AddField(
            model_name='testimonial',
            name='school_type',
            field=models.CharField(choices=[('primary', 'Primary School'), ('secondary', 'Secondary School'), ('high_school', 'High School'), ('college', 'College'), ('university', 'University'), ('private', 'Private School'), ('public', 'Public School'), ('international', 'International School'), ('boarding', 'Boarding School'), ('online', 'Online School'), ('other', 'Other')], default='other', max_length=20, verbose_name='School Type'),
        ),
        migrations.AddField(
            model_name='testimonial',
            name='title',
            field=models.CharField(default='Great Experience', help_text='Brief title for your review', max_length=200, verbose_name='Review Title'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='testimonial',
            name='usage_duration',
            field=models.CharField(blank=True, help_text='e.g., 6 months, 2 years', max_length=50, null=True, verbose_name='How long have you been using our platform?'),
        ),
        migrations.AddField(
            model_name='testimonial',
            name='user_agent',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='testimonial',
            name='value_rating',
            field=models.PositiveSmallIntegerField(blank=True, help_text='Value for money (1-5 stars)', null=True, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)], verbose_name='Value for Money'),
        ),
        migrations.AddField(
            model_name='testimonial',
            name='website',
            field=models.URLField(blank=True, help_text='School website (optional)', null=True),
        ),
        migrations.AddField(
            model_name='testimonial',
            name='would_recommend',
            field=models.BooleanField(default=True, help_text='Would you recommend our platform to other schools?', verbose_name='Would you recommend our platform?'),
        ),
        migrations.AlterField(
            model_name='testimonial',
            name='author_name',
            field=models.CharField(max_length=100, verbose_name='Full Name'),
        ),
        migrations.AlterField(
            model_name='testimonial',
            name='quote',
            field=models.TextField(help_text='Share your detailed experience', verbose_name='Review Content'),
        ),
    ]
