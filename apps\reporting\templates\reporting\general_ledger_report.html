{% extends "tenant_base.html" %}
{% load humanize static core_tags %}

{% block title %}{{ view_title|default:"General Ledger Report" }}{% endblock %}

{% block content %}
<div class="container-fluid mt-3">
    {% with report_icon_class="bi bi-journal-text" %}
        {% include "partials/_report_header.html" %}
    {% endwith %}

    {% include "partials/_messages.html" %}
    {% include "partials/_report_filter_form.html" with filter_form=filter_form report_code=report_code specific_filter_title="General Ledger Filters" %}

    <!-- Report Summary Card -->
    <div class="card mb-4">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="bi bi-bar-chart me-2"></i>Report Summary</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-3">
                    <div class="text-center">
                        <h6 class="text-muted">Period</h6>
                        <p class="mb-0">{{ start_date|date:"M d, Y" }} to {{ end_date|date:"M d, Y" }}</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h6 class="text-muted">Total Debits</h6>
                        <p class="mb-0 text-success fw-bold">{{ total_debits|currency }}</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h6 class="text-muted">Total Credits</h6>
                        <p class="mb-0 text-danger fw-bold">{{ total_credits|currency }}</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h6 class="text-muted">Difference</h6>
                        <p class="mb-0 fw-bold {% if total_debits == total_credits %}text-success{% else %}text-warning{% endif %}">
                            {{ total_debits|subtract:total_credits|currency }}
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Account Filter Info -->
    {% if selected_account %}
    <div class="alert alert-info">
        <i class="bi bi-info-circle me-2"></i>
        Showing transactions for account: <strong>{{ selected_account.name }}</strong> 
        ({{ selected_account.account_type.name }})
    </div>
    {% elif selected_account_type %}
    <div class="alert alert-info">
        <i class="bi bi-info-circle me-2"></i>
        Showing transactions for account type: <strong>{{ selected_account_type.name }}</strong>
    </div>
    {% endif %}

    <!-- Journal Lines Table -->
    {% if journal_lines %}
    <div class="card">
        <div class="card-header bg-light d-flex justify-content-between align-items-center">
            <h5 class="mb-0"><i class="bi bi-table me-2"></i>Journal Entries</h5>
            <span class="badge bg-primary">{{ journal_lines|length }} entries</span>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-striped table-hover mb-0">
                    <thead class="table-dark">
                        <tr>
                            <th>Date</th>
                            <th>Entry #</th>
                            <th>Account</th>
                            <th>Account Type</th>
                            <th>Description</th>
                            <th class="text-end">Debit</th>
                            <th class="text-end">Credit</th>
                            <th class="text-center">Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for line in journal_lines %}
                        <tr>
                            <td>{{ line.journal_entry.date|date:"M d, Y" }}</td>
                            <td>
                                <span class="badge bg-secondary">
                                    {{ line.journal_entry.entry_number|default:line.journal_entry.id }}
                                </span>
                            </td>
                            <td>
                                <strong>{{ line.account.name }}</strong>
                                {% if line.account.code %}
                                <br><small class="text-muted">{{ line.account.code }}</small>
                                {% endif %}
                            </td>
                            <td>
                                <span class="badge bg-light text-dark">{{ line.account.account_type.name }}</span>
                            </td>
                            <td>
                                {{ line.description|default:line.journal_entry.narration|truncatechars:50 }}
                                {% if line.journal_entry.entry_type != 'MANUAL' %}
                                <br><small class="text-muted">{{ line.journal_entry.get_entry_type_display }}</small>
                                {% endif %}
                            </td>
                            <td class="text-end">
                                {% if line.debit_amount > 0 %}
                                <span class="text-success fw-bold">{{ line.debit_amount|currency }}</span>
                                {% else %}
                                -
                                {% endif %}
                            </td>
                            <td class="text-end">
                                {% if line.credit_amount > 0 %}
                                <span class="text-danger fw-bold">{{ line.credit_amount|currency }}</span>
                                {% else %}
                                -
                                {% endif %}
                            </td>
                            <td class="text-center">
                                {% if line.journal_entry.status == 'POSTED' %}
                                <span class="badge bg-success">Posted</span>
                                {% elif line.journal_entry.status == 'DRAFT' %}
                                <span class="badge bg-warning">Draft</span>
                                {% else %}
                                <span class="badge bg-secondary">{{ line.journal_entry.get_status_display }}</span>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                    <tfoot class="table-light">
                        <tr>
                            <th colspan="5" class="text-end">Totals:</th>
                            <th class="text-end text-success">{{ total_debits|currency }}</th>
                            <th class="text-end text-danger">{{ total_credits|currency }}</th>
                            <th></th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Accounts Summary (when no specific account is selected) -->
    {% if accounts_summary and not selected_account %}
    <div class="card mt-4">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="bi bi-list-ul me-2"></i>Accounts Summary</h5>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-striped table-hover mb-0">
                    <thead class="table-dark">
                        <tr>
                            <th>Account</th>
                            <th>Account Type</th>
                            <th class="text-center">Entries</th>
                            <th class="text-end">Total Debits</th>
                            <th class="text-end">Total Credits</th>
                            <th class="text-end">Net Balance</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for summary in accounts_summary %}
                        <tr>
                            <td>
                                <strong>{{ summary.account.name }}</strong>
                                {% if summary.account.code %}
                                <br><small class="text-muted">{{ summary.account.code }}</small>
                                {% endif %}
                            </td>
                            <td>
                                <span class="badge bg-light text-dark">{{ summary.account.account_type.name }}</span>
                            </td>
                            <td class="text-center">
                                <span class="badge bg-info">{{ summary.line_count }}</span>
                            </td>
                            <td class="text-end">
                                {% if summary.debits > 0 %}
                                <span class="text-success">{{ summary.debits|currency }}</span>
                                {% else %}
                                -
                                {% endif %}
                            </td>
                            <td class="text-end">
                                {% if summary.credits > 0 %}
                                <span class="text-danger">{{ summary.credits|currency }}</span>
                                {% else %}
                                -
                                {% endif %}
                            </td>
                            <td class="text-end">
                                {% if summary.net_balance > 0 %}
                                <span class="text-success fw-bold">{{ summary.net_balance|currency }}</span>
                                {% elif summary.net_balance < 0 %}
                                <span class="text-danger fw-bold">{{ summary.net_balance|currency }}</span>
                                {% else %}
                                <span class="text-muted">{{ summary.net_balance|currency }}</span>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- No Data Message -->
    {% if not journal_lines %}
    <div class="card">
        <div class="card-body text-center py-5">
            <i class="bi bi-journal-x display-1 text-muted mb-3"></i>
            <h4 class="text-muted">No Journal Entries Found</h4>
            <p class="text-muted">No journal entries match your current filter criteria for the selected period.</p>
            <a href="{% url 'reporting:general_ledger_report' %}" class="btn btn-outline-primary">
                <i class="bi bi-arrow-clockwise me-2"></i>Reset Filters
            </a>
        </div>
    </div>
    {% endif %}

</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Add click handlers for account links if needed
    $('.account-link').on('click', function(e) {
        e.preventDefault();
        var accountId = $(this).data('account-id');
        // Add account filter to current URL
        var url = new URL(window.location);
        url.searchParams.set('account', accountId);
        window.location.href = url.toString();
    });
});
</script>
{% endblock %}
