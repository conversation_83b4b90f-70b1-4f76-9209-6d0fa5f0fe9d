# Generated by Django 5.1.9 on 2025-07-10 05:41

import django.db.models.deletion
import mptt.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounting', '0002_initial'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='account',
            options={'ordering': ['tree_id', 'lft'], 'verbose_name': 'Account', 'verbose_name_plural': 'Chart of Accounts'},
        ),
        migrations.RemoveConstraint(
            model_name='account',
            name='unique_account_code_mptt_accounting',
        ),
        migrations.AlterUniqueTogether(
            name='account',
            unique_together={('code',)},
        ),
        migrations.AddField(
            model_name='account',
            name='account_holder_name',
            field=models.CharField(blank=True, max_length=150, null=True),
        ),
        migrations.AddField(
            model_name='account',
            name='account_number',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='account',
            name='bank_name',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='account',
            name='branch_code',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='Branch/Sort Code'),
        ),
        migrations.AddField(
            model_name='account',
            name='parent',
            field=mptt.fields.TreeForeignKey(blank=True, help_text='Select if this is a sub-account of another account.', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='children', to='accounting.account', verbose_name='Parent Account'),
        ),
        migrations.AddField(
            model_name='account',
            name='swift_code',
            field=models.CharField(blank=True, max_length=20, null=True, verbose_name='SWIFT/BIC'),
        ),
        migrations.AlterField(
            model_name='account',
            name='code',
            field=models.CharField(blank=True, help_text='Unique account code/number (e.g., 1010, 4000, 5010).', max_length=20, null=True, verbose_name='Account Code'),
        ),
        migrations.AlterField(
            model_name='account',
            name='description',
            field=models.TextField(blank=True, help_text='Optional description for the account.', null=True, verbose_name='Description'),
        ),
        migrations.AlterField(
            model_name='account',
            name='is_control_account',
            field=models.BooleanField(default=False, help_text='Control accounts summarize sub-accounts. Direct posting to them should be restricted.', verbose_name='Is Control Account?'),
        ),
        migrations.AlterField(
            model_name='account',
            name='name',
            field=models.CharField(help_text='e.g., Cash in Bank, Tuition Fees, Salaries Expense.', max_length=150, verbose_name='Account Name'),
        ),
        migrations.RemoveField(
            model_name='account',
            name='parent_account',
        ),
        migrations.RemoveField(
            model_name='account',
            name='tenant',
        ),
    ]
