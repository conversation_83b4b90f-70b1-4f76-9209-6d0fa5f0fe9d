{% extends "tenant_base.html" %}
{% load humanize static core_tags reporting_filters %}

{% block title %}{{ view_title|default:"Cash Flow Forecasting Report" }}{% endblock %}

{% block content %}
<div class="container-fluid mt-3">
    {% with report_icon_class="bi bi-graph-up" %}
        {% include "partials/_report_header.html" %}
    {% endwith %}

    {% include "partials/_messages.html" %}
    {% include "partials/_report_filter_form.html" with filter_form=filter_form report_code=report_code specific_filter_title="Cash Flow Forecasting Filters" %}

    <!-- Forecast Summary Card -->
    <div class="card mb-4">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="bi bi-graph-up-arrow me-2"></i>Cash Flow Forecast Summary</h5>
        </div>
        <div class="card-body">
            <div class="row mb-3">
                <div class="col-md-12">
                    <p>
                        <strong>Forecast Period:</strong> {{ forecast_period|title_replace }} |
                        <strong>Historical Period:</strong> {{ historical_period|title_replace }} |
                        <strong>Method:</strong> {{ forecast_method|title_replace }} |
                        <strong>Confidence:</strong> {{ confidence_level|title }}
                    </p>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-3">
                    <div class="text-center">
                        <h6 class="text-muted">Historical Total</h6>
                        <p class="mb-0 fw-bold text-info">{{ summary.total_historical_inflow|currency }}</p>
                        <small class="text-muted">{{ summary.historical_months }} months</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h6 class="text-muted">Forecasted Total</h6>
                        <p class="mb-0 fw-bold text-success">{{ summary.total_forecasted_inflow|currency }}</p>
                        <small class="text-muted">{{ summary.forecast_months }} months</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h6 class="text-muted">Monthly Average</h6>
                        <p class="mb-0 fw-bold text-primary">{{ summary.avg_monthly_forecast|currency }}</p>
                        <small class="text-muted">vs {{ summary.avg_monthly_historical|currency }} historical</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h6 class="text-muted">Growth Trend</h6>
                        <p class="mb-0 fw-bold {% if summary.growth_rate >= 0 %}text-success{% else %}text-danger{% endif %}">
                            {% if summary.growth_rate >= 0 %}+{% endif %}{{ summary.growth_rate|floatformat:1 }}%
                        </p>
                        <small class="text-muted">projected growth</small>
                    </div>
                </div>
            </div>
            
            <!-- Growth Trend Indicator -->
            <div class="mt-3">
                <h6>Growth Trend Analysis</h6>
                <div class="progress" style="height: 25px;">
                    {% with growth_display=summary.growth_rate|add:50 %}
                    <div class="progress-bar {% if summary.growth_rate >= 5 %}bg-success{% elif summary.growth_rate >= 0 %}bg-info{% elif summary.growth_rate >= -5 %}bg-warning{% else %}bg-danger{% endif %}" 
                         role="progressbar" style="width: {{ growth_display|floatformat:0 }}%">
                        {% if summary.growth_rate >= 0 %}+{% endif %}{{ summary.growth_rate|floatformat:1 }}%
                    </div>
                    {% endwith %}
                </div>
                <small class="text-muted">
                    {% if summary.growth_rate >= 5 %}
                    Strong positive growth trend
                    {% elif summary.growth_rate >= 0 %}
                    Moderate positive growth
                    {% elif summary.growth_rate >= -5 %}
                    Slight decline trend
                    {% else %}
                    Significant decline trend
                    {% endif %}
                </small>
            </div>
        </div>
    </div>

    <!-- Historical vs Forecast Comparison -->
    <div class="row">
        <!-- Historical Data -->
        {% if historical_data %}
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header bg-light d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="bi bi-clock-history me-2"></i>Historical Cash Flow</h5>
                    <span class="badge bg-info">{{ historical_data|length }} months</span>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover mb-0">
                            <thead class="table-dark">
                                <tr>
                                    <th>Month</th>
                                    <th class="text-end">Cash Inflow</th>
                                    <th class="text-end">Collection Rate</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in historical_data %}
                                <tr>
                                    <td>{{ item.month_name }}</td>
                                    <td class="text-end">
                                        <span class="text-success fw-bold">{{ item.cash_inflow|currency }}</span>
                                    </td>
                                    <td class="text-end">
                                        <span class="{% if item.collection_rate >= 80 %}text-success{% elif item.collection_rate >= 60 %}text-warning{% else %}text-danger{% endif %}">
                                            {{ item.collection_rate|floatformat:1 }}%
                                        </span>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                            <tfoot class="table-light">
                                <tr>
                                    <th>Average:</th>
                                    <th class="text-end text-success">{{ summary.avg_monthly_historical|currency }}</th>
                                    <th class="text-end">{{ summary.avg_collection_rate|floatformat:1 }}%</th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Forecast Data -->
        {% if forecast_data %}
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header bg-light d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="bi bi-graph-up me-2"></i>Cash Flow Forecast</h5>
                    <span class="badge bg-success">{{ forecast_data|length }} months</span>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover mb-0">
                            <thead class="table-dark">
                                <tr>
                                    <th>Month</th>
                                    <th class="text-end">Forecasted Inflow</th>
                                    <th class="text-center">Confidence</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in forecast_data %}
                                <tr>
                                    <td>{{ item.month_name }}</td>
                                    <td class="text-end">
                                        <span class="text-primary fw-bold">{{ item.total_expected|currency }}</span>
                                    </td>
                                    <td class="text-center">
                                        <span class="badge {% if item.confidence_level == 'optimistic' %}bg-success{% elif item.confidence_level == 'moderate' %}bg-info{% else %}bg-warning{% endif %}">
                                            {{ item.confidence_level|title }}
                                        </span>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                            <tfoot class="table-light">
                                <tr>
                                    <th>Average:</th>
                                    <th class="text-end text-primary">{{ summary.avg_monthly_forecast|currency }}</th>
                                    <th class="text-center">{{ confidence_level|title }}</th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>

    <!-- Combined Analysis -->
    {% if historical_data and forecast_data %}
    <div class="card mb-4">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="bi bi-bar-chart-line me-2"></i>Historical vs Forecast Analysis</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-8">
                    <!-- This would be where a chart could be displayed -->
                    <div class="alert alert-info">
                        <h6><i class="bi bi-info-circle me-2"></i>Cash Flow Trend Analysis</h6>
                        <p class="mb-2">
                            <strong>Historical Performance:</strong> 
                            Average monthly inflow of {{ summary.avg_monthly_historical|currency }} over {{ summary.historical_months }} months.
                        </p>
                        <p class="mb-2">
                            <strong>Forecast Projection:</strong> 
                            Expected average monthly inflow of {{ summary.avg_monthly_forecast|currency }} over {{ summary.forecast_months }} months.
                        </p>
                        <p class="mb-0">
                            <strong>Growth Outlook:</strong> 
                            {% if summary.growth_rate > 0 %}
                            Positive growth trend of {{ summary.growth_rate|floatformat:1 }}% indicates improving cash flow.
                            {% elif summary.growth_rate == 0 %}
                            Stable cash flow with no significant growth or decline.
                            {% else %}
                            Declining trend of {{ summary.growth_rate|floatformat:1 }}% requires attention.
                            {% endif %}
                        </p>
                    </div>
                </div>
                <div class="col-md-4">
                    <h6>Key Metrics Comparison</h6>
                    <table class="table table-sm">
                        <tbody>
                            <tr>
                                <td>Historical Total:</td>
                                <td class="text-end">{{ summary.total_historical_inflow|currency }}</td>
                            </tr>
                            <tr>
                                <td>Forecasted Total:</td>
                                <td class="text-end">{{ summary.total_forecasted_inflow|currency }}</td>
                            </tr>
                            <tr class="table-light">
                                <td><strong>Difference:</strong></td>
                                <td class="text-end">
                                    <strong class="{% if summary.total_forecasted_inflow >= summary.total_historical_inflow %}text-success{% else %}text-danger{% endif %}">
                                        {% if summary.total_forecasted_inflow >= summary.total_historical_inflow %}+{% endif %}{{ summary.total_forecasted_inflow|subtract:summary.total_historical_inflow|currency }}
                                    </strong>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Forecasting Insights -->
    {% if forecast_data %}
    <div class="card">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="bi bi-lightbulb me-2"></i>Forecasting Insights & Recommendations</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h6>Key Insights</h6>
                    <ul class="list-unstyled">
                        {% if summary.growth_rate > 5 %}
                        <li><i class="bi bi-arrow-up-circle text-success me-2"></i>Strong growth trend indicates healthy financial trajectory</li>
                        {% elif summary.growth_rate > 0 %}
                        <li><i class="bi bi-arrow-up text-success me-2"></i>Positive growth trend shows improving cash flow</li>
                        {% elif summary.growth_rate == 0 %}
                        <li><i class="bi bi-dash-circle text-info me-2"></i>Stable cash flow with consistent performance</li>
                        {% else %}
                        <li><i class="bi bi-arrow-down-circle text-warning me-2"></i>Declining trend requires strategic attention</li>
                        {% endif %}
                        
                        <li><i class="bi bi-calendar-check me-2"></i>
                            Forecast based on {{ summary.historical_months }} months of historical data
                        </li>
                        
                        <li><i class="bi bi-graph-up me-2"></i>
                            Using {{ forecast_method|title_replace }} methodology with {{ confidence_level }} confidence
                        </li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6>Strategic Recommendations</h6>
                    <ul class="list-unstyled">
                        {% if summary.growth_rate < 0 %}
                        <li><i class="bi bi-exclamation-triangle text-warning me-2"></i>Review fee collection processes and payment terms</li>
                        <li><i class="bi bi-people text-primary me-2"></i>Implement retention strategies to maintain enrollment</li>
                        {% endif %}
                        
                        <li><i class="bi bi-cash-stack text-success me-2"></i>Plan for {{ summary.avg_monthly_forecast|currency }} average monthly inflow</li>
                        <li><i class="bi bi-calendar-event text-info me-2"></i>Monitor actual vs forecast monthly for accuracy</li>
                        <li><i class="bi bi-graph-down text-primary me-2"></i>Prepare contingency plans for forecast variations</li>
                        
                        {% if confidence_level == 'conservative' %}
                        <li><i class="bi bi-shield-check text-success me-2"></i>Conservative estimates provide safe planning baseline</li>
                        {% elif confidence_level == 'optimistic' %}
                        <li><i class="bi bi-exclamation-circle text-warning me-2"></i>Optimistic projections - monitor closely for deviations</li>
                        {% endif %}
                    </ul>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- No Data Message -->
    {% if not historical_data and not forecast_data %}
    <div class="card">
        <div class="card-body text-center py-5">
            <i class="bi bi-graph-up display-1 text-muted mb-3"></i>
            <h4 class="text-muted">No Cash Flow Data Available</h4>
            <p class="text-muted">Insufficient historical data to generate cash flow forecasts. Please ensure you have payment and invoice data.</p>
        </div>
    </div>
    {% endif %}

</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Animate progress bars
    $('.progress-bar').each(function() {
        var $this = $(this);
        var width = $this.css('width');
        $this.css('width', '0%').animate({width: width}, 1500);
    });
    
    // Add hover effects for forecast rows
    $('.table tbody tr').hover(
        function() {
            $(this).addClass('table-active');
        },
        function() {
            $(this).removeClass('table-active');
        }
    );
});
</script>
{% endblock %}

