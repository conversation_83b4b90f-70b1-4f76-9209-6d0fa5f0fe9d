{# D:\school_fees_saas_v2\apps\schools\templates\schools\staff_list.html #}
{% extends "tenant_base.html" %}
{% load static humanize widget_tweaks core_tags %} 

{% block title %}{{ view_title|default:"Staff Members" }}{% endblock %}

{% block tenant_specific_content %}
<div class="container mt-4">
    <div class="card shadow-sm">
        <div class="card-header bg-light d-flex justify-content-between align-items-center py-3">
            <h4 class="mb-0">{{ view_title }}</h4>
            {% if perms.schools.add_staffuser %}
                <a href="{% url 'schools:staff_create' %}" class="btn btn-primary btn-sm">
                    <i class="bi bi-plus-circle-fill me-1"></i> Add New Staff
                </a>
            {% endif %}
        </div>
        <div class="card-body p-0"> {# p-0 to make table flush with card edges #}
            {% include "partials/_messages.html" %}

            {% if staff_members %} {# Matches context_object_name in StaffListView #}
                <div class="table-responsive">
                    <table class="table table-hover table-striped mb-0">
                        <thead class="table-dark">
                            <tr>
                                <th>Name</th>
                                <th>Email</th>
                                <th>Employee ID</th>
                                <th>Designation</th>
                                <th>Department</th>
                                <th>Date Hired</th>
                                <th>Status</th>
                                <th class="text-end">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for staff in staff_members %}
                            <tr>
                                <td>
                                    {% if perms.schools.view_staffuser %}
                                        <a href="{% url 'schools:staff_detail' staff.pk %}">{{ staff.get_full_name|default:staff.email }}</a>
                                    {% else %}
                                        {{ staff.get_full_name|default:staff.email }}
                                    {% endif %}
                                </td>
                                <td>{{ staff.email }}</td>
                                <td>
                                    {% if staff.hr_profile %}
                                        {{ staff.hr_profile.employee_id|default:"-" }}
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                                <td>
                                    {% if staff.hr_profile %}
                                        {{ staff.hr_profile.designation|default:"-" }}
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                                <td>
                                    {% if staff.hr_profile %}
                                        {{ staff.hr_profile.department|default:"-" }}
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                                <td>
                                    {% if staff.hr_profile %}
                                        {{ staff.hr_profile.date_hired|date:"Y-m-d"|default:"-" }}
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                                <td>
                                    {% if staff.is_active %}
                                        <span class="badge bg-success status-badge">Active</span>
                                    {% else %}
                                        <span class="badge bg-danger status-badge">Inactive</span>
                                    {% endif %}
                                </td>
                                <td class="text-end actions-column">
                                    {% if perms.schools.view_staffuser %}
                                        <a href="{% url 'schools:staff_detail' staff.pk %}" class="btn btn-info btn-sm" title="View Details"><i class="bi bi-eye-fill"></i></a>
                                    {% endif %}
                                    {% if perms.schools.change_staffuser %}
                                        <a href="{% url 'schools:staff_update' staff.pk %}" class="btn btn-warning btn-sm" title="Edit Staff"><i class="bi bi-pencil-fill"></i></a>
                                    {% endif %}
                                    {# Delete button would typically be a POST request via a small form #}
                                    {% comment %}
                                    {% if perms.schools.delete_staffuser %}
                                        <form action="{% url 'schools:staff_delete' staff.pk %}" method="post" class="d-inline" onsubmit="return confirm('Are you sure you want to delete {{ staff.get_full_name }}?');">
                                            {% csrf_token %}
                                            <button type="submit" class="btn btn-danger btn-sm" title="Delete Staff"><i class="bi bi-trash-fill"></i></button>
                                        </form>
                                    {% endif %}
                                    {% endcomment %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="alert alert-info mt-3 mx-3">
                    No staff members found. 
                    {% if perms.schools.add_staffuser %}
                        <a href="{% url 'schools:staff_create' %}">Add the first one?</a>
                    {% endif %}
                </div>
            {% endif %}
        </div>

        {% if is_paginated %}
        <div class="card-footer bg-light">
            <nav aria-label="Staff pagination">
                <ul class="pagination justify-content-center mb-0">
                    {% if page_obj.has_previous %}
                        <li class="page-item"><a class="page-link" href="?page=1">« First</a></li>
                        <li class="page-item"><a class="page-link" href="?page={{ page_obj.previous_page_number }}">Previous</a></li>
                    {% else %}
                        <li class="page-item disabled"><span class="page-link">« First</span></li>
                        <li class="page-item disabled"><span class="page-link">Previous</span></li>
                    {% endif %}

                    <li class="page-item active" aria-current="page"><span class="page-link">Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}</span></li>

                    {% if page_obj.has_next %}
                        <li class="page-item"><a class="page-link" href="?page={{ page_obj.next_page_number }}">Next</a></li>
                        <li class="page-item"><a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">Last »</a></li>
                    {% else %}
                        <li class="page-item disabled"><span class="page-link">Next</span></li>
                        <li class="page-item disabled"><span class="page-link">Last »</span></li>
                    {% endif %}
                </ul>
            </nav>
        </div>
        {% endif %}
    </div>
</div>
{% endblock tenant_specific_content %}















