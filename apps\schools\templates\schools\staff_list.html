{% extends "tenant_base.html" %}
{% load static core_tags humanize widget_tweaks %}

{% block title %}{{ view_title|default:"Manage Staff Accounts" }} - {{ request.tenant.name }}{% endblock title %}

{% block page_specific_css %}
    {{ block.super }}
    <style>
        .filter-form .form-label-sm {
            font-size: .875em;
            margin-bottom: .25rem;
            font-weight: 500;
        }
        .actions-column {
            min-width: 150px;
            white-space: nowrap;
        }
        .data-table th, .data-table td {
            vertical-align: middle;
        }
        .staff-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
            margin-right: 10px;
            flex-shrink: 0;
        }
        .staff-info {
            display: flex;
            align-items: center;
        }
        .filter-toggle {
            cursor: pointer;
        }
        .filter-card.collapsed {
            display: none;
        }
        @media (min-width: 768px) {
            .filter-card.collapsed {
                display: block !important;
            }
            #filterToggle {
                display: none;
            }
        }
    </style>
{% endblock page_specific_css %}

{% block tenant_specific_content %}
<div class="container-fluid mt-4">
    {% include "partials/_breadcrumb.html" with current_page_title=view_title|default:"Manage Staff Accounts" %}

    <div class="row">
        <div class="col-12">
            <!-- Header Section -->
            <div class="d-flex justify-content-between align-items-center mb-3 flex-wrap">
                <div>
                    <h1 class="mb-0 h2">{{ view_title|default:"Manage Staff Accounts" }}</h1>
                    <small class="text-muted">
                        {% if staff_members %}
                            {% if paginator %}
                                Showing {{ staff_members|length }} of {{ paginator.count }} staff members
                            {% else %}
                                Showing {{ staff_members|length }} staff members
                            {% endif %}
                            {% if request.GET.name or request.GET.email or request.GET.designation or request.GET.status %}
                                (filtered)
                            {% endif %}
                        {% else %}
                            No staff members found
                        {% endif %}
                    </small>
                </div>
                <div class="d-flex gap-2 flex-wrap mt-2 mt-md-0">
                    {% if perms.schools.add_staffuser %}
                        <a href="{% url 'schools:staff_create' %}" class="btn btn-primary">
                            <i class="bi bi-person-plus-fill me-1"></i>Add Staff
                        </a>
                    {% endif %}
                    <button type="button" class="btn btn-outline-secondary filter-toggle" id="filterToggle">
                        <i class="bi bi-funnel me-1"></i>Filters <i class="bi bi-chevron-down ms-1"></i>
                    </button>
                </div>
            </div>

            {% include "partials/_messages.html" %}

            <!-- Toggle Filter Section -->
            {% include "schools/_staff_filter_toggle.html" %}

            <!-- Staff Members Table -->
            {% if staff_members %}
            <div class="card shadow-sm">
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover table-sm data-table mb-0">
                            <thead class="table-dark">
                                <tr>
                                    <th>Staff Member</th>
                                    <th>Email</th>
                                    <th>Employee ID</th>
                                    <th>Designation</th>
                                    <th>Department</th>
                                    <th>Date Hired</th>
                                    <th>Status</th>
                                    <th class="text-end actions-column">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for staff in staff_members %}
                                <tr class="staff-row">
                                    <td>
                                        <div class="staff-info">
                                            <div class="staff-avatar bg-success">
                                                {{ staff.get_full_name|default:staff.email|first|upper }}
                                            </div>
                                            <div>
                                                <div class="fw-medium">
                                                    {% if perms.schools.view_staffuser %}
                                                        <a href="{% url 'schools:staff_detail' staff.pk %}" class="text-decoration-none">
                                                            {{ staff.get_full_name|default:staff.email }}
                                                        </a>
                                                    {% else %}
                                                        {{ staff.get_full_name|default:staff.email }}
                                                    {% endif %}
                                                </div>
                                                {% if staff.get_full_name and staff.email != staff.get_full_name %}
                                                    <small class="text-muted">{{ staff.email }}</small>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </td>
                                    <td>{{ staff.email }}</td>
                                    <td>
                                        {% if staff.hr_profile %}
                                            {{ staff.hr_profile.employee_id|default:"-" }}
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if staff.hr_profile %}
                                            {{ staff.hr_profile.designation|default:"-" }}
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if staff.hr_profile %}
                                            {{ staff.hr_profile.department|default:"-" }}
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if staff.hr_profile %}
                                            {{ staff.hr_profile.date_hired|date:"Y-m-d"|default:"-" }}
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if staff.is_active %}
                                            <span class="badge bg-success">Active</span>
                                        {% else %}
                                            <span class="badge bg-danger">Inactive</span>
                                        {% endif %}
                                    </td>
                                    <td class="text-end actions-column">
                                        <div class="btn-group btn-group-sm" role="group">
                                            {% if perms.schools.view_staffuser %}
                                            <a href="{% url 'schools:staff_detail' staff.pk %}"
                                               class="btn btn-outline-info btn-sm"
                                               title="View Details">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                            {% endif %}
                                            {% if perms.schools.change_staffuser %}
                                            <a href="{% url 'schools:staff_update' staff.pk %}"
                                               class="btn btn-outline-primary btn-sm"
                                               title="Edit Staff">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                            {% endif %}
                                            {% if perms.schools.change_staffuser %}
                                            <a href="{% url 'schools:staff_assign_roles' staff.pk %}"
                                               class="btn btn-outline-warning btn-sm"
                                               title="Assign Roles">
                                                <i class="bi bi-shield-check"></i>
                                            </a>
                                            {% endif %}
                                            {% if perms.schools.delete_staffuser %}
                                            <a href="{% url 'schools:staff_delete' staff.pk %}"
                                               class="btn btn-outline-danger btn-sm"
                                               title="Delete Staff">
                                                <i class="bi bi-trash"></i>
                                            </a>
                                            {% endif %}
                                        </div>
                                    </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="card shadow-sm">
                    <div class="card-body text-center py-5">
                        <i class="bi bi-people display-1 text-muted"></i>
                        <h4 class="mt-3">No Staff Members Found</h4>
                        <p class="text-muted">There are no staff members in the system yet.</p>
                        {% if perms.schools.add_staffuser %}
                        <a href="{% url 'schools:staff_create' %}" class="btn btn-primary">
                            <i class="bi bi-person-plus-fill me-1"></i> Add First Staff Member
                        </a>
                        {% endif %}
                    </div>
                </div>
            {% endif %}

            <!-- Pagination -->
            {% if is_paginated %}
            <div class="d-flex justify-content-center mt-4">
                {% include "partials/_pagination.html" with page_obj=page_obj filter_params=filter_params %}
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock tenant_specific_content %}

{% block extra_tenant_js %}
{{ block.super }}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Filter toggle functionality
    const filterToggle = document.getElementById('filterToggle');
    const filterCard = document.querySelector('.premium-staff-filter-card');

    if (filterToggle && filterCard) {
        filterToggle.addEventListener('click', function() {
            const isCollapsed = filterCard.style.display === 'none';
            filterCard.style.display = isCollapsed ? 'block' : 'none';

            const icon = this.querySelector('.bi-chevron-down, .bi-chevron-up');
            if (icon) {
                icon.classList.toggle('bi-chevron-down');
                icon.classList.toggle('bi-chevron-up');
            }
        });
    }
});
</script>
{% endblock %}
                        <li class="page-item"><a class="page-link" href="?page={{ page_obj.next_page_number }}">Next</a></li>
                        <li class="page-item"><a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">Last »</a></li>
                    {% else %}
                        <li class="page-item disabled"><span class="page-link">Next</span></li>
                        <li class="page-item disabled"><span class="page-link">Last »</span></li>
                    {% endif %}
                </ul>
            </nav>
        </div>
        {% endif %}
    </div>
</div>
{% endblock tenant_specific_content %}















