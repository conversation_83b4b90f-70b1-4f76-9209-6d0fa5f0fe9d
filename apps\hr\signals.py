# D:\school_fees_saas_v2\apps\hr\signals.py
from django.db.models.signals import post_save
from django.dispatch import receiver
import logging

from apps.schools.models import StaffUser
from .models import EmployeeProfile

logger = logging.getLogger(__name__)

@receiver(post_save, sender=StaffUser)
def create_employee_profile_on_staff_creation(sender, instance, created, **kwargs):
    """
    Signal to automatically create an EmployeeProfile when a new StaffUser is created.
    """
    if created:
        try:
            # Use get_or_create to be safe in case something else already created it
            profile, profile_created = EmployeeProfile.objects.get_or_create(user=instance)
            if profile_created:
                logger.info(f"Automatically created EmployeeProfile for new StaffUser: {instance.email}")
        except Exception as e:
            logger.error(f"Failed to auto-create EmployeeProfile for {instance.email}: {e}")



# D:\school_fees_saas_v2\apps\hr\signals.py
import logging
from django.db.models.signals import post_save
from django.dispatch import receiver
from django.utils import timezone

# Import the models involved
from .models import EmployeeProfile, LeaveType, LeaveBalance

logger = logging.getLogger(__name__)

@receiver(post_save, sender=EmployeeProfile)
def create_leave_balances_for_new_employee(sender, instance, created, **kwargs):
    """
    Signal handler to automatically create default leave balances for a newly
    created EmployeeProfile.
    """
    # 'created' is a boolean that is True only when a new record is saved for the first time.
    if created:
        logger.info(f"New EmployeeProfile created for '{instance}'. Triggering leave balance setup.")
        
        # Get the current year
        current_year = timezone.now().year
        
        # Get all active leave types in the system
        active_leave_types = LeaveType.objects.filter(is_active=True)
        
        if not active_leave_types.exists():
            logger.warning(f"No active leave types found. Cannot create default balances for {instance}.")
            return

        # Loop through each active leave type and create a balance record
        balances_to_create = []
        for leave_type in active_leave_types:
            # Check if a balance for this type and year already exists, just in case
            if not LeaveBalance.objects.filter(employee=instance, leave_type=leave_type, year=current_year).exists():
                balances_to_create.append(
                    LeaveBalance(
                        employee=instance,
                        leave_type=leave_type,
                        year=current_year,
                        # Use the default 'days_allocated' from the LeaveType
                        days_accrued=leave_type.days_allocated
                    )
                )
                logger.debug(f"Prepared LeaveBalance for {instance} - {leave_type.name} ({leave_type.days_allocated} days).")
        
        # Use bulk_create for efficiency if there are many leave types
        if balances_to_create:
            LeaveBalance.objects.bulk_create(balances_to_create)
            logger.info(f"Successfully bulk-created {len(balances_to_create)} leave balance records for {instance}.")
            
            
            