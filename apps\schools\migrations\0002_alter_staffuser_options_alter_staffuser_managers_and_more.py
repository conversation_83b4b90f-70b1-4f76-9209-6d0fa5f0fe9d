# Generated by Django 5.1.9 on 2025-07-08 12:24

import apps.schools.managers
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
        ('schools', '0001_initial'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='staffuser',
            options={'ordering': ['last_name', 'first_name'], 'verbose_name': 'staff member', 'verbose_name_plural': 'staff members'},
        ),
        migrations.AlterModelManagers(
            name='staffuser',
            managers=[
                ('objects', apps.schools.managers.StaffUserManager()),
            ],
        ),
        migrations.RemoveField(
            model_name='staffuser',
            name='address_line1',
        ),
        migrations.RemoveField(
            model_name='staffuser',
            name='address_line2',
        ),
        migrations.RemoveField(
            model_name='staffuser',
            name='city',
        ),
        migrations.RemoveField(
            model_name='staffuser',
            name='country',
        ),
        migrations.RemoveField(
            model_name='staffuser',
            name='date_hired',
        ),
        migrations.RemoveField(
            model_name='staffuser',
            name='date_left',
        ),
        migrations.RemoveField(
            model_name='staffuser',
            name='date_of_birth',
        ),
        migrations.RemoveField(
            model_name='staffuser',
            name='department',
        ),
        migrations.RemoveField(
            model_name='staffuser',
            name='designation',
        ),
        migrations.RemoveField(
            model_name='staffuser',
            name='employee_id',
        ),
        migrations.RemoveField(
            model_name='staffuser',
            name='employment_type',
        ),
        migrations.RemoveField(
            model_name='staffuser',
            name='gender',
        ),
        migrations.RemoveField(
            model_name='staffuser',
            name='is_owner_profile',
        ),
        migrations.RemoveField(
            model_name='staffuser',
            name='marital_status',
        ),
        migrations.RemoveField(
            model_name='staffuser',
            name='middle_name',
        ),
        migrations.RemoveField(
            model_name='staffuser',
            name='notes',
        ),
        migrations.RemoveField(
            model_name='staffuser',
            name='phone_number_alternate',
        ),
        migrations.RemoveField(
            model_name='staffuser',
            name='phone_number_primary',
        ),
        migrations.RemoveField(
            model_name='staffuser',
            name='photo',
        ),
        migrations.RemoveField(
            model_name='staffuser',
            name='postal_code',
        ),
        migrations.RemoveField(
            model_name='staffuser',
            name='state_province',
        ),
        migrations.AlterField(
            model_name='staffuser',
            name='date_joined',
            field=models.DateTimeField(default=django.utils.timezone.now, verbose_name='date joined'),
        ),
        migrations.AlterField(
            model_name='staffuser',
            name='email',
            field=models.EmailField(help_text='Required. Used for login.', max_length=254, unique=True, verbose_name='email address'),
        ),
        migrations.AlterField(
            model_name='staffuser',
            name='groups',
            field=models.ManyToManyField(blank=True, help_text='The groups this user belongs to. A user will get all permissions granted to each of their groups.', related_name='staff_user_set', related_query_name='staff_user', to='auth.group', verbose_name='groups'),
        ),
        migrations.AlterField(
            model_name='staffuser',
            name='is_active',
            field=models.BooleanField(default=True, help_text='Designates whether this user should be treated as active.', verbose_name='active'),
        ),
        migrations.AlterField(
            model_name='staffuser',
            name='is_staff',
            field=models.BooleanField(default=True, help_text="Designates whether the user can log into this tenant's Django admin site.", verbose_name='staff status'),
        ),
        migrations.AlterField(
            model_name='staffuser',
            name='is_superuser',
            field=models.BooleanField(default=False, help_text='Designates that this user has all permissions within this tenant without explicitly assigning them.', verbose_name='tenant superuser status'),
        ),
        migrations.AlterField(
            model_name='staffuser',
            name='user_permissions',
            field=models.ManyToManyField(blank=True, help_text='Specific permissions for this user.', related_name='staff_user_permissions_set', related_query_name='staff_user', to='auth.permission', verbose_name='user permissions'),
        ),
    ]
