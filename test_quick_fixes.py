#!/usr/bin/env python
"""
Quick Test for Registration Fixes
Tests the specific fixes we just made

Usage: python test_quick_fixes.py
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from django.db import connection
from django.core.management import call_command
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_subscription_model():
    """Test subscription model has correct field names"""
    logger.info("=== TESTING SUBSCRIPTION MODEL ===")
    
    try:
        from apps.subscriptions.models import Subscription
        
        # Check that 'school' field exists (not 'tenant')
        if hasattr(Subscription, 'school'):
            logger.info("✅ Subscription model has 'school' field")
        else:
            logger.error("❌ Subscription model missing 'school' field")
            return False
        
        # Check that 'tenant' field does NOT exist
        if not hasattr(Subscription, 'tenant'):
            logger.info("✅ Subscription model does not have 'tenant' field")
        else:
            logger.error("❌ Subscription model still has 'tenant' field")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"Subscription model test failed: {e}")
        return False

def test_schema_creation():
    """Test schema creation with our safe command"""
    logger.info("=== TESTING SCHEMA CREATION ===")
    
    test_schema = 'test_quick_fix'
    
    try:
        # Clean up any existing test schema
        with connection.cursor() as cursor:
            cursor.execute(f'DROP SCHEMA IF EXISTS "{test_schema}" CASCADE')
        
        # Test our safe migration command
        logger.info(f"Testing schema creation for: {test_schema}")
        call_command('migrate_tenant_safe', test_schema, verbosity=1)
        
        # Verify schema was created
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.schemata 
                    WHERE schema_name = %s
                );
            """, [test_schema])
            
            if cursor.fetchone()[0]:
                logger.info("✅ Schema created successfully")
                
                # Check django_migrations table exists
                cursor.execute(f'SET search_path TO "{test_schema}";')
                cursor.execute("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_name = 'django_migrations'
                    );
                """)
                
                if cursor.fetchone()[0]:
                    logger.info("✅ django_migrations table created")
                    
                    # Clean up
                    cursor.execute(f'DROP SCHEMA "{test_schema}" CASCADE')
                    return True
                else:
                    logger.error("❌ django_migrations table not created")
                    return False
            else:
                logger.error("❌ Schema not created")
                return False
                
    except Exception as e:
        logger.error(f"Schema creation test failed: {e}")
        return False

def test_trial_plan_exists():
    """Test that trial plan exists for subscription creation"""
    logger.info("=== TESTING TRIAL PLAN ===")
    
    try:
        from apps.subscriptions.models import SubscriptionPlan
        
        # Check if trial plan exists
        trial_plans = SubscriptionPlan.objects.filter(
            name__icontains='trial'
        )
        
        if trial_plans.exists():
            plan = trial_plans.first()
            logger.info(f"✅ Trial plan exists: {plan.name}")
            
            # Check required fields
            if hasattr(plan, 'price_monthly') and hasattr(plan, 'trial_period_days'):
                logger.info("✅ Trial plan has required fields")
                return True
            else:
                logger.error("❌ Trial plan missing required fields")
                return False
        else:
            logger.warning("⚠️ No trial plan found - creating one...")
            
            # Create a basic trial plan
            trial_plan = SubscriptionPlan.objects.create(
                name='Trial Plan',
                description='7-day trial plan',
                price_monthly=0.00,
                price_annually=0.00,
                trial_period_days=7,
                max_students=50,
                is_active=True,
                is_public=True
            )
            
            logger.info(f"✅ Created trial plan: {trial_plan.name}")
            return True
            
    except Exception as e:
        logger.error(f"Trial plan test failed: {e}")
        return False

def run_quick_tests():
    """Run all quick tests"""
    logger.info("=== RUNNING QUICK REGISTRATION FIXES TESTS ===")
    
    tests = [
        ("Subscription Model", test_subscription_model),
        ("Schema Creation", test_schema_creation),
        ("Trial Plan", test_trial_plan_exists),
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        logger.info(f"\n--- {test_name} ---")
        try:
            if test_func():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            logger.error(f"❌ {test_name} ERROR: {e}")
            failed += 1
    
    logger.info(f"\n=== QUICK TEST SUMMARY ===")
    logger.info(f"✅ Passed: {passed}")
    logger.info(f"❌ Failed: {failed}")
    
    if failed == 0:
        logger.info("🎉 ALL QUICK TESTS PASSED! Registration should work now.")
        return True
    else:
        logger.error("💥 SOME TESTS FAILED. Check errors above.")
        return False

if __name__ == "__main__":
    success = run_quick_tests()
    sys.exit(0 if success else 1)
