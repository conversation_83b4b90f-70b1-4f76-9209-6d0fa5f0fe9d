# Generated by Django 5.1.9 on 2025-07-09 15:50

from django.db import migrations


def fix_leavebalance_unique_constraint(apps, schema_editor):
    """
    Manually fix the unique_together constraint for LeaveBalance.
    This is needed because the old constraint references 'year_or_period_info'
    which no longer exists.
    """
    db_alias = schema_editor.connection.alias

    # Drop the old constraint if it exists
    with schema_editor.connection.cursor() as cursor:
        # Check if the old constraint exists and drop it
        cursor.execute("""
            SELECT constraint_name
            FROM information_schema.table_constraints
            WHERE table_name = 'hr_leavebalance'
            AND constraint_type = 'UNIQUE'
            AND table_schema = %s
        """, [schema_editor.connection.settings_dict['NAME']])

        constraints = cursor.fetchall()
        for constraint in constraints:
            constraint_name = constraint[0]
            try:
                cursor.execute(f'ALTER TABLE hr_leavebalance DROP CONSTRAINT "{constraint_name}"')
            except Exception:
                # Constraint might not exist or already dropped
                pass

        # Create the new constraint
        try:
            cursor.execute("""
                ALTER TABLE hr_leavebalance
                ADD CONSTRAINT hr_leavebalance_employee_id_leave_type_id_year_uniq
                UNIQUE (employee_id, leave_type_id, year)
            """)
        except Exception:
            # Constraint might already exist
            pass


def reverse_fix_leavebalance_unique_constraint(apps, schema_editor):
    """
    Reverse the constraint fix - this is a no-op since we can't restore
    the old constraint that referenced a non-existent field.
    """
    pass


class Migration(migrations.Migration):

    dependencies = [
        ('hr', '0017_alter_salarycomponent_options_and_more'),
    ]

    operations = [
        migrations.RunPython(
            fix_leavebalance_unique_constraint,
            reverse_fix_leavebalance_unique_constraint,
        ),
    ]
