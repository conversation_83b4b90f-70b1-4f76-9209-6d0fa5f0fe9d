#!/usr/bin/env python
"""
Simple Column Fix
Adds the missing is_control_account column and fixes cursor issues

Usage: python simple_column_fix.py
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from django.db import connection
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def add_missing_column():
    """Add the missing is_control_account column"""
    schema_name = 'mandiva'
    
    logger.info(f"=== ADDING MISSING COLUMN FOR {schema_name} ===")
    
    try:
        with connection.cursor() as cursor:
            # Set search path to mandiva schema
            cursor.execute(f'SET search_path TO "{schema_name}"')
            
            # Add missing accounting_account.is_control_account
            logger.info("Adding accounting_account.is_control_account...")
            try:
                cursor.execute("ALTER TABLE accounting_account ADD COLUMN IF NOT EXISTS is_control_account BOOLEAN DEFAULT FALSE")
                logger.info("✅ Column added successfully")
            except Exception as e:
                logger.warning(f"Column might already exist: {e}")
            
            # Set control account flags
            try:
                cursor.execute("""
                    UPDATE accounting_account 
                    SET is_control_account = CASE 
                        WHEN account_type IN ('ASSET', 'LIABILITY', 'EQUITY', 'INCOME', 'EXPENSE') AND parent_id IS NULL THEN TRUE
                        ELSE FALSE
                    END
                """)
                logger.info("✅ Control account flags set")
            except Exception as e:
                logger.warning(f"Update issue: {e}")
            
            # Add other potentially missing columns one by one
            missing_columns = [
                ("is_reconcilable", "BOOLEAN DEFAULT FALSE"),
                ("reconciliation_account_id", "BIGINT"),
                ("tax_rate", "DECIMAL(5,2) DEFAULT 0.00"),
                ("currency_code", "VARCHAR(3) DEFAULT 'USD'"),
            ]
            
            for col_name, col_def in missing_columns:
                try:
                    cursor.execute(f"ALTER TABLE accounting_account ADD COLUMN IF NOT EXISTS {col_name} {col_def}")
                    logger.info(f"✅ Added column: {col_name}")
                except Exception as e:
                    logger.warning(f"Column {col_name} issue: {e}")
            
            logger.info("✅ All missing columns processed!")
            
    except Exception as e:
        logger.error(f"Failed to add missing columns: {e}")
        raise

def test_column_access():
    """Test that the column can be accessed"""
    schema_name = 'mandiva'
    
    logger.info("=== TESTING COLUMN ACCESS ===")
    
    try:
        with connection.cursor() as cursor:
            cursor.execute(f'SET search_path TO "{schema_name}"')
            
            # Test the problematic column
            cursor.execute("SELECT is_control_account FROM accounting_account LIMIT 1")
            result = cursor.fetchone()
            logger.info(f"✅ is_control_account: query successful (result: {result})")
            
            # Test a simple balance sheet query
            cursor.execute("""
                SELECT name, account_type, is_control_account, balance 
                FROM accounting_account 
                WHERE account_type IN ('ASSET', 'LIABILITY', 'EQUITY')
                ORDER BY account_type, name
                LIMIT 5
            """)
            results = cursor.fetchall()
            logger.info(f"✅ Balance sheet query successful ({len(results)} accounts)")
            
            for row in results:
                logger.info(f"   Account: {row[0]} | Type: {row[1]} | Control: {row[2]} | Balance: {row[3]}")
                
    except Exception as e:
        logger.error(f"Column testing failed: {e}")

def restart_database_connection():
    """Restart the database connection to clear any cursor issues"""
    logger.info("=== RESTARTING DATABASE CONNECTION ===")
    
    try:
        # Close all connections
        connection.close()
        logger.info("✅ Database connection closed")
        
        # Force a new connection
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            logger.info(f"✅ New database connection established (test: {result})")
            
    except Exception as e:
        logger.error(f"Connection restart failed: {e}")

def main():
    """Main function"""
    logger.info("=== SIMPLE COLUMN FIX ===")
    
    try:
        # Restart database connection to clear cursor issues
        restart_database_connection()
        
        # Add missing columns
        add_missing_column()
        
        # Test column access
        test_column_access()
        
        logger.info("\n🎉 MISSING COLUMN FIXED!")
        logger.info("The is_control_account column has been added to accounting_account.")
        logger.info("This should resolve the Balance Sheet report error.")
        logger.info("\nFor cursor issues in reports:")
        logger.info("1. The database connection has been restarted")
        logger.info("2. Try refreshing the report pages")
        logger.info("3. If issues persist, restart the Django development server")
        logger.info("\nTest these reports now:")
        logger.info("- Balance Sheet Report: http://mandiva.myapp.test:8000/portal/reporting/balance-sheet/")
        logger.info("- General Ledger Report: http://mandiva.myapp.test:8000/portal/reporting/general-ledger/")
        logger.info("- Bank Reconciliation Report: http://mandiva.myapp.test:8000/portal/reporting/bank-reconciliation/")
        
        return True
        
    except Exception as e:
        logger.error(f"Simple column fix failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
