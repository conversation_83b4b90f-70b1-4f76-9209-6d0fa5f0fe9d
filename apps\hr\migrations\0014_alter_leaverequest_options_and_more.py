# Generated by Django 5.1.9 on 2025-07-08 19:45

import django.db.models.deletion
from decimal import Decimal
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('hr', '0013_add_missing_employeeprofile_fields'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='leaverequest',
            options={'ordering': ['-created_at'], 'verbose_name': 'Leave Request', 'verbose_name_plural': 'Leave Requests'},
        ),
        migrations.RemoveField(
            model_name='leaverequest',
            name='admin_notes',
        ),
        migrations.RemoveField(
            model_name='leaverequest',
            name='approval_date',
        ),
        migrations.RemoveField(
            model_name='leaverequest',
            name='number_of_days_requested_calc',
        ),
        migrations.RemoveField(
            model_name='leaverequest',
            name='request_date',
        ),
        migrations.AddField(
            model_name='leaverequest',
            name='duration',
            field=models.DecimalField(decimal_places=1, default=Decimal('0.0'), editable=False, help_text='The effective number of leave days, calculated by the system.', max_digits=5, verbose_name='Calculated Duration (Days)'),
        ),
        migrations.AddField(
            model_name='leaverequest',
            name='status_changed_at',
            field=models.DateTimeField(blank=True, null=True, verbose_name='Actioned On'),
        ),
        migrations.AddField(
            model_name='leaverequest',
            name='status_reason',
            field=models.TextField(blank=True, null=True, verbose_name='Admin/Manager Notes'),
        ),
        migrations.AlterField(
            model_name='leaverequest',
            name='approved_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_leave_requests', to='hr.employeeprofile', verbose_name='Actioned By'),
        ),
        migrations.AlterField(
            model_name='leaverequest',
            name='attachment',
            field=models.FileField(blank=True, help_text='Optional: Attach any supporting document.', null=True, upload_to='leave_attachments/%Y/%m/', verbose_name='Attachment'),
        ),
        migrations.AlterField(
            model_name='leaverequest',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, verbose_name='Submitted On'),
        ),
        migrations.AlterField(
            model_name='leaverequest',
            name='employee',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='leave_requests', to='hr.employeeprofile', verbose_name='Employee'),
        ),
        migrations.AlterField(
            model_name='leaverequest',
            name='end_date',
            field=models.DateField(verbose_name='End Date'),
        ),
        migrations.AlterField(
            model_name='leaverequest',
            name='half_day_end',
            field=models.BooleanField(default=False, verbose_name='Request Half-day on End Date'),
        ),
        migrations.AlterField(
            model_name='leaverequest',
            name='half_day_start',
            field=models.BooleanField(default=False, verbose_name='Request Half-day on Start Date'),
        ),
        migrations.AlterField(
            model_name='leaverequest',
            name='leave_type',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='leave_requests', to='hr.leavetype', verbose_name='Leave Type'),
        ),
        migrations.AlterField(
            model_name='leaverequest',
            name='reason',
            field=models.TextField(verbose_name='Reason for Leave'),
        ),
        migrations.AlterField(
            model_name='leaverequest',
            name='start_date',
            field=models.DateField(verbose_name='Start Date'),
        ),
        migrations.AlterField(
            model_name='leaverequest',
            name='status',
            field=models.CharField(choices=[('PENDING', 'Pending'), ('APPROVED', 'Approved'), ('REJECTED', 'Rejected'), ('CANCELLED_BY_STAFF', 'Cancelled by Staff')], default='PENDING', max_length=20, verbose_name='Status'),
        ),
    ]
