# D:\school_fees_saas_v2\apps\schools\admin.py
from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from django.urls import reverse
from django.utils.html import format_html
from django.utils import timezone # For the admin display method
from django.utils.translation import gettext_lazy as _
from .models import AcademicYear, Term, SchoolClass, Section, StaffUser, SchoolProfile

@admin.register(AcademicYear)
class AcademicYearAdmin(admin.ModelAdmin):
    list_display = ('name', 'start_date', 'end_date', 'is_active', 'is_current') # 'is_active' is a direct field
    list_filter = ('is_active', 'is_current')
    search_fields = ('name',)
    ordering = ('-start_date',)
    list_editable = ('is_active',)

    def save_model(self, request, obj, form, change):
        if obj.is_active: # If this AcademicYear is being set to active
            AcademicYear.objects.filter(is_active=True).exclude(pk=obj.pk).update(is_active=False)
        super().save_model(request, obj, form, change)

@admin.register(Term)
class TermAdmin(admin.ModelAdmin):
    list_display = ('name', 'academic_year', 'start_date', 'end_date', 'is_active')
    list_filter = ('academic_year', 'is_active')
    search_fields = ('name', 'academic_year__name')
    autocomplete_fields = ['academic_year']
    ordering = ('-academic_year__start_date', '-start_date')
    list_select_related = ('academic_year',)

    @admin.display(description=_('Academic Year'), ordering='academic_year__name')
    def get_academic_year_display(self, obj):
        if obj.academic_year:
            link = reverse("admin:schools_academicyear_change", args=[obj.academic_year.id])
            return format_html('<a href="{}">{}</a>', link, obj.academic_year.name)
        return None

    @admin.display(description=_('Is Current Term?'), boolean=True, ordering='start_date') # ordering by date as a proxy
    def display_is_currently_active_term(self, obj):
        # This method calls the property on the model instance
        return obj.is_currently_active_term

# --- Your Existing and Correct SchoolClassAdmin ---
from django.contrib import admin
from .models import SchoolClass
@admin.register(SchoolClass)
class SchoolClassAdmin(admin.ModelAdmin):
    list_display = ('name', 'description', 'is_active', 'created_at', 'updated_at')
    list_filter = ('is_active',) # Add academic_year here if you use it
    search_fields = ('name', 'description')
    # readonly_fields = ('created_at', 'updated_at') # If not using a base model that handles this
# @admin.register(SchoolClass)
# class SchoolClassAdmin(admin.ModelAdmin):
#     list_display = ('name', 'description_snippet')
#     search_fields = ('name', 'description')
#     ordering = ('name',)

    @admin.display(description=_('Description'))
    def description_snippet(self, obj):
        if obj.description:
            return (obj.description[:75] + '...') if len(obj.description) > 75 else obj.description
        return ""

# --- Your Existing and Correct SectionAdmin ---
@admin.register(Section)
class SectionAdmin(admin.ModelAdmin):
    list_display = ('name', 'get_school_class_name_display', 'get_class_teacher_display', 'room_number')
    search_fields = ('name', 'school_class__name', 'class_teacher__email', 'class_teacher__first_name', 'class_teacher__last_name', 'room_number')
    list_filter = ('school_class', 'class_teacher',)
    autocomplete_fields = ['school_class', 'class_teacher']
    list_select_related = ('school_class', 'class_teacher')
    ordering = ('school_class__name', 'name')
    fieldsets = (
        (None, {'fields': ('name', 'school_class')}),
        ('Details', {'fields': ('class_teacher', 'room_number')}),
    )

    @admin.display(description=_('Class/Grade'), ordering='school_class__name')
    def get_school_class_name_display(self, obj):
        return obj.school_class.name if obj.school_class else None

    @admin.display(description=_('Class Teacher'), ordering='class_teacher__last_name')
    def get_class_teacher_display(self, obj):
        if obj.class_teacher:
            return obj.class_teacher.get_full_name() or obj.class_teacher.email
        return None
    
    
# @admin.register(StaffUser)
# class StaffUserAdmin(admin.ModelAdmin): # Or extend BaseUserAdmin if you created custom forms
#     list_display = (
#         'email', 
#         'get_full_name_display', 
#         'designation', 
#         'department',
#         'is_active', 
#         'is_staff_for_django_admin_display', # Clarified purpose
#         'get_date_hired_display',
#     )
#     search_fields = (
#         'email', 
#         'first_name', 
#         'last_name', 
#         'middle_name',
#         'employee_id', 
#         'designation',
#         'department'
#     )
#     list_filter = (
#         'is_active', 
#         'is_staff',       # Filters by the 'is_staff' (Django admin access) field
#         'designation', 
#         'department',
#         'employment_type',
#         'date_hired',    # Use 'date_hired' if it's the primary employment start date
#         'groups',        # Filters by Django auth groups
#     )
#     filter_horizontal = ('groups', 'user_permissions') # Standard for M2M, works if using PermissionsMixin defaults
#     ordering = ('last_name', 'first_name', 'email')
#     # raw_id_fields = ('some_fk_if_many_choices',) # Example

#     # Define fieldsets for better form organization
#     fieldsets = (
#         (None, {'fields': ('email', 'password')}), # Password handled by Django admin
#         (_('Personal Information'), {'fields': ('first_name', 'middle_name', 'last_name', 'gender', 'date_of_birth', 'marital_status', 'photo')}),
#         (_('Contact Information'), {'fields': ('phone_number_primary', 'phone_number_alternate')}),
#         (_('Address'), {'fields': ('address_line1', 'address_line2', 'city', 'state_province', 'postal_code', 'country')}),
#         (_('Employment Details'), {'fields': ('employee_id', 'designation', 'department', 'employment_type', 'date_hired', 'date_left')}),
#         (_('System Access & Status'), {'fields': ('is_active', 'is_staff', 'notes')}),
#         (_('Permissions'), {'fields': ('is_superuser', 'groups', 'user_permissions')}),
#         (_('Important Dates'), {'fields': ('date_joined', 'last_login',)}),
#     )
#     # For the 'add' form, if not using BaseUserAdmin, you might need to specify add_fieldsets
#     # or rely on the default form generated from 'fieldsets'.
#     # If using BaseUserAdmin, it has its own add_form and add_fieldsets.
#     add_fieldsets = ( # Example if not using BaseUserAdmin's add_form
#         (None, {
#             'classes': ('wide',),
#             'fields': ('email', 'first_name', 'last_name', 'password_first', 'password_second'), # Needs form handling password
#         }),
#         (_('Initial Employment'), {'fields': ('designation', 'department', 'date_hired', 'employment_type')}),
#         (_('Initial Status'), {'fields': ('is_active', 'is_staff')})
#     )
#     readonly_fields = ('last_login', 'date_joined')


#     @admin.display(description=_('Full Name'), ordering='last_name')
#     def get_full_name_display(self, obj):
#         return obj.get_full_name()
    
#     @admin.display(description=_('Django Admin Access?'), boolean=True, ordering='is_staff')
#     def is_staff_for_django_admin_display(self, obj):
#         # This refers to the StaffUser.is_staff field, which you've designated for Django Admin Access
#         return obj.is_staff 
    
#     @admin.display(description=_('Date Hired'), ordering='date_hired')
#     def get_date_hired_display(self, obj):
#         return obj.date_hired.strftime("%Y-%m-%d") if obj.date_hired else None

# D:\school_fees_saas_v2\apps\schools\admin.py

from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.utils.translation import gettext_lazy as _

from .models import StaffUser
# Import the related profile model from the HR app
from apps.hr.models import EmployeeProfile 

# 1. Define an Inline for the related EmployeeProfile
# This allows you to edit the profile directly on the StaffUser change page.
class EmployeeProfileInline(admin.StackedInline):
    model = EmployeeProfile
    can_delete = False # You don't want to delete the profile without deleting the user
    verbose_name_plural = 'HR & Employment Details'
    # fk_name is used if the OneToOneField on EmployeeProfile has a non-standard name.
    # If the field is named 'user' or 'staff_user', Django usually finds it automatically.
    # fk_name = 'user' 

# 2. Correct the main StaffUserAdmin class
@admin.register(StaffUser)
class StaffUserAdmin(BaseUserAdmin):
    # BaseUserAdmin provides a solid foundation for user management.
    
    # --- Corrected list_display ---
    # We can only list fields directly on StaffUser or callables.
    list_display = (
        'email', 
        'get_full_name', # Use the model's method directly
        'get_designation', # Custom method to get data from related profile
        'is_active', 
        'is_staff', # is_staff controls Django admin access
        'is_superuser'
    )

    # --- Corrected list_filter ---
    # To filter by a related field, use the '__' syntax.
    # Let's assume the related_name on StaffUser for EmployeeProfile is 'hr_profile'
    list_filter = (
        'is_active', 
        'is_staff', 
        'groups',
        'hr_profile__designation', # Filter by designation on the related profile
        'hr_profile__department',  # Filter by department on the related profile
        'hr_profile__employment_type'
    )
    
    # --- Corrected search_fields ---
    search_fields = (
        'email', 
        'first_name', 
        'last_name',
        'hr_profile__employee_id', # Search by employee_id on the related profile
        'hr_profile__designation',
    )
    
    ordering = ('last_name', 'first_name')
    filter_horizontal = ('groups', 'user_permissions',)
    
    # --- Fieldsets for the CHANGE form ---
    # This now only contains fields directly on the StaffUser model.
    # The HR fields will be handled by the EmployeeProfileInline.
    fieldsets = (
        (None, {'fields': ('email', 'password')}),
        (_('Personal Info'), {'fields': ('first_name', 'last_name')}),
        (_('Permissions'), {'fields': ('is_active', 'is_staff', 'is_superuser', 
                                    'groups', 'user_permissions')}),
        (_('Important dates'), {'fields': ('last_login', 'date_joined')}),
    )
    
    # --- Inlines ---
    # This is the key part: it tells the admin to display the
    # EmployeeProfile form directly on this page.
    inlines = (EmployeeProfileInline,)
    
    readonly_fields = ('last_login', 'date_joined')

    # BaseUserAdmin handles the add_fieldsets and creation form correctly.
    # We don't need to redefine add_fieldsets unless we are heavily customizing the creation process.

    # --- Custom methods to display data from the related profile in the list view ---
    @admin.display(description=_('Designation'), ordering='hr_profile__designation')
    def get_designation(self, obj):
        # Safely access the related profile's designation
        if hasattr(obj, 'hr_profile') and obj.hr_profile:
            return obj.hr_profile.designation
        return '-'

@admin.register(SchoolProfile)
class SchoolProfileAdmin(admin.ModelAdmin):
    list_display = ('get_profile_display_name', 'school_email', 'phone_number', 'updated_at')
    search_fields = ('school_name_override', 'school_name_on_reports', 'school_email')
    # list_filter is typically not needed for a singleton-per-tenant model.

    @admin.display(description=_('School Profile For'), ordering='school_name_on_reports')
    def get_profile_display_name(self, obj):
        # This method assumes SchoolProfile is tied to the current tenant.
        # In admin list views for tenant models, request.tenant isn't directly on 'obj'.
        # For a singleton model like SchoolProfile, there's only one.
        # A more robust way might be to have a ForeignKey from SchoolProfile to School(tenant)
        # if you ever intend more than one profile or need to list them across tenants (not typical).
        # For now, using its own fields:
        return obj.school_name_on_reports or obj.school_name_override or f"Profile (ID: {obj.pk})"
    
