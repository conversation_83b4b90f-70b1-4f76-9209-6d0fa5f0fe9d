{% extends "base.html" %}
{% load i18n %}

{% block title %}{{ page_title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <h1 class="h3 mb-4 text-gray-800">{{ page_title }}</h1>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                {% blocktrans with class_name=from_class.name from_year=from_year.name to_year=to_year.name %}
                Step 2: Set actions for students in <strong>{{ class_name }}</strong> ({{ from_year }}) to be moved to {{ to_year }}
                {% endblocktrans %}
            </h6>
        </div>
        <div class="card-body">
            <form id="promotion-review-form" method="post">
                {% csrf_token %}
                <!-- Hidden fields to pass the context to the POST view -->
                <input type="hidden" name="from_year_id" value="{{ from_year.pk }}">
                <input type="hidden" name="to_year_id" value="{{ to_year.pk }}">
                <input type="hidden" name="from_class_id" value="{{ from_class.pk }}">

                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>{% trans "Student Name" %}</th>
                                <th>{% trans "Admission No." %}</th>
                                <th>{% trans "Action" %}</th>
                                <th>{% trans "Destination Class" %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for candidate in promotion_candidates %}
                            <tr>
                                <!-- Hidden input to track all students submitted in the form -->
                                <input type="hidden" name="student_ids" value="{{ candidate.student.pk }}">
                                <td>{{ candidate.student.full_name }}</td>
                                <td>{{ candidate.student.admission_number }}</td>
                                <td>
                                    <select name="action_{{ candidate.student.pk }}" class="form-select action-select">
                                        <option value="PROMOTE" {% if candidate.default_action == 'PROMOTE' %}selected{% endif %}>
                                            Promote
                                        </option>
                                        <option value="RETAIN" {% if candidate.default_action == 'RETAIN' %}selected{% endif %}>
                                            Retain in {{ from_class.name }}
                                        </option>
                                        <option value="GRADUATE" {% if candidate.default_action == 'GRADUATE' %}selected{% endif %}>
                                            Graduate
                                        </option>
                                    </select>
                                </td>
                                <td>
                                    <select name="to_class_{{ candidate.student.pk }}" class="form-select to-class-select">
                                        <option value="">-- {% trans "Select Class" %} --</option>
                                        {% for class_option in all_classes %}
                                            <option value="{{ class_option.pk }}" {% if candidate.default_to_class.pk == class_option.pk %}selected{% endif %}>
                                                {{ class_option.name }}
                                            </option>
                                        {% endfor %}
                                    </select>
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="4" class="text-center">
                                    {% trans "No active students found in this class for the selected academic year." %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <div class="mt-4 d-flex justify-content-between">
                    <a href="{% url 'students:promotion_setup' %}" class="btn btn-secondary">
                        ← {% trans "Go Back" %}
                    </a>
                    <button type="submit" class="btn btn-primary btn-lg">
                        {% trans "Proceed to Confirmation" %} →
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock content %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Function to toggle the visibility of the 'to_class' dropdown
    function toggleToClassSelect(actionSelect) {
        const selectedAction = actionSelect.value;
        const tableRow = actionSelect.closest('tr');
        const toClassSelect = tableRow.querySelector('.to-class-select');
        
        if (selectedAction === 'PROMOTE') {
            toClassSelect.style.display = 'block';
        } else {
            toClassSelect.style.display = 'none';
        }
    }

    // Find all 'action' dropdowns
    const actionSelects = document.querySelectorAll('.action-select');
    
    // Add an event listener to each one
    actionSelects.forEach(function(select) {
        // Run on page load to set the initial state
        toggleToClassSelect(select);
        
        // Run on change
        select.addEventListener('change', function() {
            toggleToClassSelect(this);
        });
    });
});
</script>
{% endblock extra_js %}

