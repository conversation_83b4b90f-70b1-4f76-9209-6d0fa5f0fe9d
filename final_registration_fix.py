#!/usr/bin/env python
"""
Final Registration Fix
Completely resolves all remaining issues

Usage: python final_registration_fix.py
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from django.db import connection, transaction
from django.core.management import call_command
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def fix_migration_state_completely():
    """Completely fix the migration state"""
    logger.info("=== FIXING MIGRATION STATE COMPLETELY ===")
    
    try:
        with transaction.atomic():
            with connection.cursor() as cursor:
                # Step 1: Remove problematic hr.0013 migration
                logger.info("Removing hr.0013 migration record...")
                cursor.execute("""
                    DELETE FROM django_migrations 
                    WHERE app = 'hr' AND name = '0013_add_missing_employeeprofile_fields';
                """)
                
                # Step 2: Ensure hr.0012_production is marked as applied
                logger.info("Ensuring hr.0012_production is applied...")
                cursor.execute("""
                    INSERT INTO django_migrations (app, name, applied) 
                    VALUES ('hr', '0012_add_missing_leavetype_fields_production', NOW())
                    ON CONFLICT (app, name) DO NOTHING;
                """)
                
                # Step 3: Apply hr.0013 properly
                logger.info("Applying hr.0013 migration...")
                try:
                    call_command('migrate', 'hr', '0013', verbosity=1)
                    logger.info("✅ hr.0013 applied successfully")
                except Exception as e:
                    logger.warning(f"hr.0013 migration failed, but continuing: {e}")
                
                logger.info("✅ Migration state fixed")
                
    except Exception as e:
        logger.error(f"Failed to fix migration state: {e}")
        raise

def apply_all_migrations():
    """Apply all migrations to public schema"""
    logger.info("=== APPLYING ALL MIGRATIONS ===")
    
    try:
        call_command('migrate', verbosity=1)
        logger.info("✅ All migrations applied to public schema")
    except Exception as e:
        logger.error(f"Failed to apply migrations: {e}")
        raise

def create_complete_tenant_migration_command():
    """Create a bulletproof tenant migration command"""
    logger.info("=== CREATING BULLETPROOF TENANT MIGRATION ===")
    
    command_content = '''#!/usr/bin/env python
"""
Bulletproof Tenant Migration Command
Creates all necessary tables for a tenant schema
"""

from django.core.management.base import BaseCommand
from django.core.management import call_command
from django.db import connection
from django_tenants.utils import schema_context
import logging

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Create a complete tenant schema with all tables'

    def add_arguments(self, parser):
        parser.add_argument('schema_name', type=str, help='Schema name to create')

    def handle(self, *args, **options):
        schema_name = options['schema_name']
        
        self.stdout.write(f"Creating complete tenant schema: {schema_name}")
        
        try:
            with connection.cursor() as cursor:
                # Create schema
                cursor.execute(f'CREATE SCHEMA IF NOT EXISTS "{schema_name}"')
                cursor.execute(f'SET search_path TO "{schema_name}"')
                
                # Create all necessary tables directly
                self.create_essential_tables(cursor)
                
                # Mark migrations as applied
                self.mark_migrations_applied(cursor)
                
            self.stdout.write(f"✅ Complete tenant schema created: {schema_name}")
            
        except Exception as e:
            self.stdout.write(f"❌ Failed to create tenant schema: {e}")
            raise

    def create_essential_tables(self, cursor):
        """Create all essential tables"""
        
        # Django migrations table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS django_migrations (
                id SERIAL PRIMARY KEY,
                app VARCHAR(255) NOT NULL,
                name VARCHAR(255) NOT NULL,
                applied TIMESTAMP WITH TIME ZONE NOT NULL,
                UNIQUE(app, name)
            )
        """)
        
        # Auth tables
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS auth_group (
                id SERIAL PRIMARY KEY,
                name VARCHAR(150) UNIQUE NOT NULL
            )
        """)
        
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS auth_permission (
                id SERIAL PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                content_type_id INTEGER NOT NULL,
                codename VARCHAR(100) NOT NULL
            )
        """)
        
        # Schools StaffUser table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS schools_staffuser (
                id BIGSERIAL PRIMARY KEY,
                password VARCHAR(128),
                last_login TIMESTAMP WITH TIME ZONE,
                is_superuser BOOLEAN NOT NULL DEFAULT FALSE,
                email VARCHAR(254) UNIQUE NOT NULL,
                first_name VARCHAR(150) NOT NULL,
                last_name VARCHAR(150) NOT NULL,
                phone VARCHAR(20),
                address TEXT,
                emergency_contact TEXT,
                date_of_birth DATE,
                profile_picture VARCHAR(100),
                is_active BOOLEAN NOT NULL DEFAULT TRUE,
                is_staff BOOLEAN NOT NULL DEFAULT FALSE,
                date_joined TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
            )
        """)
        
        # Schools StaffUser groups table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS schools_staffuser_groups (
                id BIGSERIAL PRIMARY KEY,
                staffuser_id BIGINT NOT NULL,
                group_id INTEGER NOT NULL,
                UNIQUE(staffuser_id, group_id)
            )
        """)
        
        # HR EmployeeProfile table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS hr_employeeprofile (
                id BIGSERIAL PRIMARY KEY,
                employee_id VARCHAR(50) UNIQUE,
                department VARCHAR(100),
                position VARCHAR(100),
                hire_date DATE,
                salary DECIMAL(10,2),
                is_active BOOLEAN NOT NULL DEFAULT TRUE,
                created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
                user_id BIGINT UNIQUE
            )
        """)
        
        self.stdout.write("✅ Essential tables created")

    def mark_migrations_applied(self, cursor):
        """Mark essential migrations as applied"""
        
        migrations_to_mark = [
            ('auth', '0001_initial'),
            ('contenttypes', '0001_initial'),
            ('schools', '0001_initial'),
            ('hr', '0001_initial'),
            ('hr', '0012_add_missing_leavetype_fields_production'),
            ('hr', '0013_add_missing_employeeprofile_fields'),
        ]
        
        for app, migration in migrations_to_mark:
            cursor.execute("""
                INSERT INTO django_migrations (app, name, applied) 
                VALUES (%s, %s, NOW())
                ON CONFLICT (app, name) DO NOTHING
            """, [app, migration])
        
        self.stdout.write("✅ Migrations marked as applied")
'''
    
    # Write the command file
    command_path = 'apps/tenants/management/commands/create_complete_tenant.py'
    with open(command_path, 'w') as f:
        f.write(command_content)
    
    logger.info(f"✅ Created bulletproof tenant migration command: {command_path}")

def test_complete_tenant_creation():
    """Test the complete tenant creation"""
    logger.info("=== TESTING COMPLETE TENANT CREATION ===")
    
    test_schema = 'test_complete_tenant'
    
    try:
        # Clean up any existing test schema
        with connection.cursor() as cursor:
            cursor.execute(f'DROP SCHEMA IF EXISTS "{test_schema}" CASCADE')
        
        # Test the new command
        call_command('create_complete_tenant', test_schema)
        
        # Verify tables exist
        with connection.cursor() as cursor:
            cursor.execute(f'SET search_path TO "{test_schema}"')
            
            required_tables = [
                'django_migrations',
                'auth_group',
                'schools_staffuser',
                'schools_staffuser_groups',
                'hr_employeeprofile'
            ]
            
            for table in required_tables:
                cursor.execute(f"""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_name = '{table}'
                    )
                """)
                
                if cursor.fetchone()[0]:
                    logger.info(f"✅ Table {table} exists")
                else:
                    logger.error(f"❌ Table {table} missing")
                    return False
        
        # Clean up
        with connection.cursor() as cursor:
            cursor.execute(f'DROP SCHEMA "{test_schema}" CASCADE')
        
        logger.info("✅ Complete tenant creation test passed")
        return True
        
    except Exception as e:
        logger.error(f"Complete tenant creation test failed: {e}")
        return False

def update_school_model_to_use_new_command():
    """Update School model to use the new bulletproof command"""
    logger.info("=== UPDATING SCHOOL MODEL ===")
    
    # This will be done manually after running this script
    logger.info("⚠️ Manual step required: Update School model to use 'create_complete_tenant' command")
    logger.info("Change line 75 in apps/tenants/models.py:")
    logger.info("FROM: call_command('migrate_tenant_safe', self.schema_name, verbosity=1)")
    logger.info("TO:   call_command('create_complete_tenant', self.schema_name)")

def main():
    """Run the final registration fix"""
    logger.info("=== FINAL REGISTRATION FIX ===")
    
    steps = [
        ("Fix Migration State", fix_migration_state_completely),
        ("Apply All Migrations", apply_all_migrations),
        ("Create Bulletproof Command", create_complete_tenant_migration_command),
        ("Test Complete Tenant Creation", test_complete_tenant_creation),
        ("Update Instructions", update_school_model_to_use_new_command),
    ]
    
    for step_name, step_func in steps:
        logger.info(f"\\n--- {step_name} ---")
        try:
            result = step_func()
            if result is False:
                logger.error(f"❌ {step_name} failed")
                return False
        except Exception as e:
            logger.error(f"❌ {step_name} error: {e}")
            return False
    
    logger.info("\\n🎉 FINAL REGISTRATION FIX COMPLETED!")
    logger.info("Next steps:")
    logger.info("1. Update School model as instructed above")
    logger.info("2. Test registration")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
'''
