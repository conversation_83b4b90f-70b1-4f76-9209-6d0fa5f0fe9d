# Generated by Django 5.1.9 on 2025-07-09 15:46

import django.db.models.deletion
import django.utils.timezone
from decimal import Decimal
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('hr', '0016_alter_leavebalance_unique_together'),
        ('schools', '0002_alter_staffuser_options_alter_staffuser_managers_and_more'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='salarycomponent',
            options={'ordering': ['type', 'name'], 'verbose_name': 'Salary Component', 'verbose_name_plural': 'Salary Components'},
        ),
        migrations.RenameField(
            model_name='salarycomponent',
            old_name='component_type',
            new_name='type',
        ),
        migrations.AlterUniqueTogether(
            name='salarycomponent',
            unique_together=set(),
        ),
        migrations.AddField(
            model_name='salarycomponent',
            name='is_percentage',
            field=models.BooleanField(default=False, help_text='Is this component calculated as a percentage of the basic salary?'),
        ),
        migrations.AlterField(
            model_name='salarycomponent',
            name='name',
            field=models.CharField(max_length=100, unique=True, verbose_name='Component Name'),
        ),
        migrations.CreateModel(
            name='StaffSalary',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('basic_salary', models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='The gross basic salary per month.', max_digits=10, verbose_name='Basic Salary')),
                ('effective_from', models.DateField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('staff_member', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='salary_details', to='schools.staffuser')),
            ],
            options={
                'verbose_name': 'Staff Salary',
                'verbose_name_plural': 'Staff Salaries',
            },
        ),
        migrations.RemoveField(
            model_name='salarycomponent',
            name='is_active',
        ),
        migrations.RemoveField(
            model_name='salarycomponent',
            name='is_statutory',
        ),
        migrations.CreateModel(
            name='StaffSalaryComponent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('value', models.DecimalField(decimal_places=2, help_text='Enter a fixed amount, or a percentage (e.g., 7.5 for 7.5%).', max_digits=10, verbose_name='Value/Percentage')),
                ('component', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='hr.salarycomponent')),
                ('staff_salary', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='components', to='hr.staffsalary')),
            ],
            options={
                'verbose_name': 'Staff Salary Component',
                'verbose_name_plural': 'Staff Salary Components',
                'unique_together': {('staff_salary', 'component')},
            },
        ),
    ]
