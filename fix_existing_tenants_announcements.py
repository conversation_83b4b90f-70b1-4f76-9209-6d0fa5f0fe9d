#!/usr/bin/env python
"""
Fix Existing Tenants - Add Announcements Table
Adds the missing announcements table to all existing tenant schemas

Usage: python fix_existing_tenants_announcements.py
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from django.db import connection
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def get_all_tenant_schemas():
    """Get list of all tenant schemas"""
    with connection.cursor() as cursor:
        cursor.execute("""
            SELECT schema_name FROM information_schema.schemata 
            WHERE schema_name NOT IN ('public', 'information_schema') 
            AND schema_name NOT LIKE 'pg_%'
            ORDER BY schema_name
        """)
        return [row[0] for row in cursor.fetchall()]

def check_announcements_table_exists(schema_name):
    """Check if announcements table exists in schema"""
    with connection.cursor() as cursor:
        cursor.execute(f'SET search_path TO "{schema_name}"')
        cursor.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_name = 'announcements_announcement'
            );
        """)
        return cursor.fetchone()[0]

def create_announcements_table(schema_name):
    """Create announcements table in schema"""
    with connection.cursor() as cursor:
        cursor.execute(f'SET search_path TO "{schema_name}"')
        
        # Create announcements table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS announcements_announcement (
                id BIGSERIAL PRIMARY KEY,
                title VARCHAR(255) NOT NULL,
                content TEXT NOT NULL,
                target_all_tenant_staff BOOLEAN NOT NULL DEFAULT FALSE,
                target_all_tenant_parents BOOLEAN NOT NULL DEFAULT FALSE,
                is_global BOOLEAN NOT NULL DEFAULT FALSE,
                target_global_audience_type VARCHAR(50),
                publish_date TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
                expiry_date TIMESTAMP WITH TIME ZONE,
                is_published BOOLEAN NOT NULL DEFAULT TRUE,
                is_sticky BOOLEAN NOT NULL DEFAULT FALSE,
                created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
                author_id BIGINT,
                tenant_id BIGINT
            );
        """)
        
        # Create indexes
        cursor.execute("""
            CREATE INDEX IF NOT EXISTS announcements_announcement_publish_date_idx 
                ON announcements_announcement (publish_date);
        """)
        
        cursor.execute("""
            CREATE INDEX IF NOT EXISTS announcements_announcement_is_published_idx 
                ON announcements_announcement (is_published);
        """)
        
        # Mark migrations as applied
        cursor.execute("""
            INSERT INTO django_migrations (app, name, applied) 
            VALUES ('announcements', '0001_initial', NOW())
            ON CONFLICT (app, name) DO NOTHING;
        """)
        
        cursor.execute("""
            INSERT INTO django_migrations (app, name, applied) 
            VALUES ('announcements', '0002_initial', NOW())
            ON CONFLICT (app, name) DO NOTHING;
        """)

def fix_all_existing_tenants():
    """Fix all existing tenant schemas"""
    logger.info("=== FIXING ANNOUNCEMENTS TABLE FOR ALL EXISTING TENANTS ===")
    
    schemas = get_all_tenant_schemas()
    logger.info(f"Found {len(schemas)} tenant schemas: {schemas}")
    
    fixed_count = 0
    already_exists_count = 0
    error_count = 0
    
    for schema in schemas:
        try:
            logger.info(f"Checking schema: {schema}")
            
            if check_announcements_table_exists(schema):
                logger.info(f"  ✅ Announcements table already exists in {schema}")
                already_exists_count += 1
            else:
                logger.info(f"  🔧 Creating announcements table in {schema}")
                create_announcements_table(schema)
                logger.info(f"  ✅ Announcements table created in {schema}")
                fixed_count += 1
                
        except Exception as e:
            logger.error(f"  ❌ Failed to fix {schema}: {e}")
            error_count += 1
    
    logger.info(f"\n=== SUMMARY ===")
    logger.info(f"✅ Fixed: {fixed_count}")
    logger.info(f"✅ Already existed: {already_exists_count}")
    logger.info(f"❌ Errors: {error_count}")
    
    if error_count == 0:
        logger.info("🎉 ALL TENANT SCHEMAS FIXED SUCCESSFULLY!")
        return True
    else:
        logger.error("💥 SOME SCHEMAS HAD ERRORS")
        return False

def test_announcements_access():
    """Test that announcements can be accessed in all schemas"""
    logger.info("=== TESTING ANNOUNCEMENTS ACCESS ===")
    
    schemas = get_all_tenant_schemas()
    
    for schema in schemas:
        try:
            with connection.cursor() as cursor:
                cursor.execute(f'SET search_path TO "{schema}"')
                
                # Test basic query
                cursor.execute("SELECT COUNT(*) FROM announcements_announcement;")
                count = cursor.fetchone()[0]
                
                logger.info(f"✅ {schema}: announcements table accessible (count: {count})")
                
        except Exception as e:
            logger.error(f"❌ {schema}: announcements table not accessible: {e}")
            return False
    
    logger.info("✅ All schemas can access announcements table")
    return True

def main():
    """Main function"""
    logger.info("=== FIXING EXISTING TENANTS ANNOUNCEMENTS ===")
    
    try:
        # Fix all existing tenants
        if not fix_all_existing_tenants():
            return False
        
        # Test access
        if not test_announcements_access():
            return False
        
        logger.info("\n🎉 ALL EXISTING TENANTS FIXED!")
        logger.info("The announcements table error should now be resolved.")
        logger.info("Try accessing the staff login page again.")
        
        return True
        
    except Exception as e:
        logger.error(f"Fix failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
