# Generated by Django 5.1.9 on 2025-07-09 16:28

from django.db import migrations


def ensure_unique_constraint(apps, schema_editor):
    """
    Ensure the correct unique constraint exists for LeaveBalance.
    This is a no-op since the constraint should already be correct.
    """
    pass


def reverse_ensure_unique_constraint(apps, schema_editor):
    """
    Reverse operation - no-op
    """
    pass


class Migration(migrations.Migration):

    dependencies = [
        ('hr', '0018_auto_20250709_1750'),
    ]

    operations = [
        migrations.RunPython(
            ensure_unique_constraint,
            reverse_ensure_unique_constraint,
        ),
    ]
