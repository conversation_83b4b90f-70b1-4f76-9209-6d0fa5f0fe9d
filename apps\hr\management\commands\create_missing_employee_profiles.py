from django.core.management.base import BaseCommand
from django.db import transaction
from django_tenants.utils import tenant_context
from apps.schools.models import School, StaffUser
from apps.hr.models import EmployeeProfile
import logging

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Creates EmployeeProfile instances for any StaffUser that does not have one'

    def add_arguments(self, parser):
        parser.add_argument(
            '--tenant',
            type=str,
            help='Specific tenant schema to process (optional, processes all if not specified)',
        )

    def handle(self, *args, **options):
        tenant_schema = options.get('tenant')
        
        if tenant_schema:
            # Process specific tenant
            try:
                tenant = School.objects.get(schema_name=tenant_schema)
                self.process_tenant(tenant)
            except School.DoesNotExist:
                self.stdout.write(
                    self.style.ERROR(f'Tenant with schema "{tenant_schema}" does not exist')
                )
        else:
            # Process all tenants
            tenants = School.objects.all()
            self.stdout.write(f'Processing {tenants.count()} tenants...')
            
            for tenant in tenants:
                self.process_tenant(tenant)

    def process_tenant(self, tenant):
        """Process a single tenant to create missing EmployeeProfile instances"""
        self.stdout.write(f'\n--- Processing tenant: {tenant.name} ({tenant.schema_name}) ---')
        
        with tenant_context(tenant):
            try:
                # Get all StaffUsers
                staff_users = StaffUser.objects.all()
                self.stdout.write(f'Found {staff_users.count()} staff users')
                
                created_count = 0
                error_count = 0
                
                for staff_user in staff_users:
                    try:
                        # Check if EmployeeProfile already exists
                        if hasattr(staff_user, 'hr_profile'):
                            # Profile already exists
                            continue
                        
                        # Create EmployeeProfile
                        with transaction.atomic():
                            profile, created = EmployeeProfile.objects.get_or_create(
                                user=staff_user,
                                defaults={
                                    'employee_id': None,  # Will be set manually by admin
                                    'designation': '',
                                    'department': '',
                                    'notes': 'Auto-created profile',
                                }
                            )
                            
                            if created:
                                created_count += 1
                                self.stdout.write(
                                    self.style.SUCCESS(
                                        f'  ✓ Created EmployeeProfile for {staff_user.get_full_name()} ({staff_user.email})'
                                    )
                                )
                            else:
                                self.stdout.write(
                                    f'  - EmployeeProfile already exists for {staff_user.get_full_name()}'
                                )
                                
                    except Exception as e:
                        error_count += 1
                        self.stdout.write(
                            self.style.ERROR(
                                f'  ✗ Error creating profile for {staff_user.email}: {e}'
                            )
                        )
                        logger.error(f'Error creating EmployeeProfile for {staff_user.email}: {e}')
                
                # Summary for this tenant
                self.stdout.write(
                    self.style.SUCCESS(
                        f'Tenant {tenant.schema_name}: Created {created_count} profiles, {error_count} errors'
                    )
                )
                
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'Error processing tenant {tenant.schema_name}: {e}')
                )
                logger.error(f'Error processing tenant {tenant.schema_name}: {e}')
