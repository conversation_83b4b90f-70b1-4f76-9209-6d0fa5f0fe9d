#!/usr/bin/env python
"""
Create Proper Django Migrations
This creates actual Django migrations that will apply to ALL tenants automatically

Usage: python create_proper_migrations.py
"""

import os
import sys
import django
from datetime import datetime

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_migration_files():
    """Create proper Django migration files for all missing fields"""
    
    logger.info("=== CREATING PROPER DJANGO MIGRATIONS ===")
    
    # Migration templates for each app
    migrations = {
        'students': {
            'filename': f'apps/students/migrations/{datetime.now().strftime("%Y%m%d_%H%M%S")}_add_missing_fields.py',
            'content': '''# Generated migration for missing student fields
from django.db import migrations, models

class Migration(migrations.Migration):
    dependencies = [
        ('students', '0001_initial'),  # Adjust based on your last migration
    ]

    operations = [
        migrations.AddField(
            model_name='student',
            name='photo',
            field=models.ImageField(upload_to='student_photos/', blank=True, null=True),
        ),
        migrations.AddField(
            model_name='student',
            name='middle_name',
            field=models.CharField(max_length=150, blank=True),
        ),
        migrations.AddField(
            model_name='student',
            name='blood_group',
            field=models.CharField(max_length=10, blank=True),
        ),
        migrations.AddField(
            model_name='student',
            name='nationality',
            field=models.CharField(max_length=100, default='Unknown'),
        ),
        migrations.AddField(
            model_name='student',
            name='religion',
            field=models.CharField(max_length=100, blank=True),
        ),
        migrations.AddField(
            model_name='student',
            name='emergency_contact',
            field=models.CharField(max_length=200, blank=True),
        ),
        migrations.AddField(
            model_name='student',
            name='emergency_phone',
            field=models.CharField(max_length=20, blank=True),
        ),
        migrations.AddField(
            model_name='student',
            name='medical_conditions',
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name='student',
            name='transport_mode',
            field=models.CharField(max_length=50, blank=True),
        ),
        migrations.AddField(
            model_name='student',
            name='house',
            field=models.CharField(max_length=100, blank=True),
        ),
        migrations.AddField(
            model_name='student',
            name='previous_school',
            field=models.CharField(max_length=255, blank=True),
        ),
        migrations.AddField(
            model_name='student',
            name='current_section_id',
            field=models.BigIntegerField(null=True, blank=True),
        ),
        migrations.AddField(
            model_name='student',
            name='admission_number',
            field=models.CharField(max_length=50, unique=True, null=True, blank=True),
        ),
    ]
'''
        },
        
        'accounting': {
            'filename': f'apps/accounting/migrations/{datetime.now().strftime("%Y%m%d_%H%M%S")}_add_missing_fields.py',
            'content': '''# Generated migration for missing accounting fields
from django.db import migrations, models

class Migration(migrations.Migration):
    dependencies = [
        ('accounting', '0001_initial'),  # Adjust based on your last migration
    ]

    operations = [
        migrations.AddField(
            model_name='account',
            name='is_control_account',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='account',
            name='can_be_used_in_je',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='account',
            name='bank_name',
            field=models.CharField(max_length=255, blank=True),
        ),
        migrations.AddField(
            model_name='account',
            name='bank_account_number',
            field=models.CharField(max_length=100, blank=True),
        ),
        migrations.AddField(
            model_name='account',
            name='bank_routing_number',
            field=models.CharField(max_length=50, blank=True),
        ),
        migrations.AddField(
            model_name='account',
            name='bank_swift_code',
            field=models.CharField(max_length=20, blank=True),
        ),
        migrations.AddField(
            model_name='account',
            name='bank_branch',
            field=models.CharField(max_length=255, blank=True),
        ),
        migrations.AddField(
            model_name='account',
            name='description',
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name='account',
            name='parent_id',
            field=models.BigIntegerField(null=True, blank=True),
        ),
        migrations.AddField(
            model_name='accounttype',
            name='classification',
            field=models.CharField(max_length=50, blank=True),
        ),
    ]
'''
        },
        
        'finance': {
            'filename': f'apps/finance/migrations/{datetime.now().strftime("%Y%m%d_%H%M%S")}_add_missing_models.py',
            'content': '''# Generated migration for missing finance models
from django.db import migrations, models

class Migration(migrations.Migration):
    dependencies = [
        ('finance', '0001_initial'),  # Adjust based on your last migration
    ]

    operations = [
        migrations.CreateModel(
            name='Vendor',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('contact_person', models.CharField(max_length=200, blank=True)),
                ('email', models.EmailField(max_length=254, blank=True)),
                ('phone', models.CharField(max_length=20, blank=True)),
                ('phone_number', models.CharField(max_length=20, blank=True)),
                ('address', models.TextField(blank=True)),
                ('tax_id', models.CharField(max_length=50, blank=True)),
                ('payment_terms', models.CharField(max_length=100, blank=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='BudgetItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('description', models.TextField(blank=True)),
                ('budget_amount', models.DecimalField(decimal_places=2, default=0, max_digits=15)),
                ('actual_amount', models.DecimalField(decimal_places=2, default=0, max_digits=15)),
                ('variance_amount', models.DecimalField(decimal_places=2, default=0, max_digits=15)),
                ('budget_period_start', models.DateField(null=True, blank=True)),
                ('budget_period_end', models.DateField(null=True, blank=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
    ]
'''
        }
    }
    
    # Create migration files
    for app_name, migration_data in migrations.items():
        try:
            # Create migrations directory if it doesn't exist
            migration_dir = f"apps/{app_name}/migrations"
            os.makedirs(migration_dir, exist_ok=True)
            
            # Create __init__.py if it doesn't exist
            init_file = os.path.join(migration_dir, '__init__.py')
            if not os.path.exists(init_file):
                with open(init_file, 'w') as f:
                    f.write('')
            
            # Write migration file
            with open(migration_data['filename'], 'w') as f:
                f.write(migration_data['content'])
            
            logger.info(f"✅ Created migration for {app_name}: {migration_data['filename']}")
            
        except Exception as e:
            logger.error(f"❌ Failed to create migration for {app_name}: {e}")

def create_management_command():
    """Create a management command to apply migrations to all tenants"""
    
    command_content = '''"""
Management command to migrate all tenants
Usage: python manage.py migrate_all_tenants
"""

from django.core.management.base import BaseCommand
from django.core.management import call_command
from apps.tenants.models import School
from django_tenants.utils import schema_context

class Command(BaseCommand):
    help = 'Apply migrations to all tenant schemas'

    def handle(self, *args, **options):
        self.stdout.write("Starting migration of all tenants...")
        
        # Migrate public schema first
        self.stdout.write("Migrating public schema...")
        call_command('migrate_schemas', '--schema=public')
        
        # Get all tenants
        tenants = School.objects.all()
        
        for tenant in tenants:
            self.stdout.write(f"Migrating tenant: {tenant.schema_name}")
            try:
                with schema_context(tenant.schema_name):
                    call_command('migrate')
                self.stdout.write(
                    self.style.SUCCESS(f"✅ Successfully migrated {tenant.schema_name}")
                )
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f"❌ Failed to migrate {tenant.schema_name}: {e}")
                )
        
        self.stdout.write(
            self.style.SUCCESS("Migration of all tenants completed!")
        )
'''
    
    # Create management command directory
    command_dir = "apps/tenants/management/commands"
    os.makedirs(command_dir, exist_ok=True)
    
    # Create __init__.py files
    for path in ["apps/tenants/management", command_dir]:
        init_file = os.path.join(path, '__init__.py')
        if not os.path.exists(init_file):
            with open(init_file, 'w') as f:
                f.write('')
    
    # Write command file
    command_file = os.path.join(command_dir, 'migrate_all_tenants.py')
    with open(command_file, 'w') as f:
        f.write(command_content)
    
    logger.info(f"✅ Created management command: {command_file}")

def main():
    """Main function"""
    logger.info("=== CREATING PROPER DJANGO MIGRATIONS ===")
    
    try:
        # Create migration files
        create_migration_files()
        
        # Create management command
        create_management_command()
        
        logger.info("\n🎉 PROPER MIGRATIONS CREATED!")
        logger.info("\nNow run these commands to fix ALL tenants:")
        logger.info("1. python manage.py makemigrations")
        logger.info("2. python manage.py migrate_all_tenants")
        logger.info("\nThis will:")
        logger.info("- Apply migrations to ALL existing tenants")
        logger.info("- Automatically apply to ALL future tenants")
        logger.info("- Use proper Django migration system")
        logger.info("- Ensure consistency across all schemas")
        logger.info("\nThis is the CORRECT way to handle multi-tenant databases!")
        
        return True
        
    except Exception as e:
        logger.error(f"Failed to create proper migrations: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
