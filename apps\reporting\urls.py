# D:\school_fees_saas_V2\apps\reporting\urls.py
from django.urls import path
from . import views

app_name = 'reporting' # Define the namespace

urlpatterns = [
    
    path('', views.ReportingHomeView.as_view(), name='reporting_home'),
    
    
    # Payment Summary Report URLs
    path('payment-summary/', views.PaymentSummaryReportView.as_view(), name='payment_summary_report'),
    path('payment-summary/csv/', views.PaymentSummaryExportCSVView.as_view(), name='payment_summary_export_csv'),
    path('payment-summary/pdf/', views.PaymentSummaryExportPDFView.as_view(), name='payment_summary_export_pdf'),
    path('payment-summary/excel/', views.PaymentSummaryExportExcelView.as_view(), name='payment_summary_export_excel'),
    

    # Outstanding Fees Report URLs
    path('outstanding-fees/', views.OutstandingFeesReportView.as_view(), name='outstanding_fees_report'),
    path('outstanding-fees/csv/', views.OutstandingFeesExportCSVView.as_view(), name='outstanding_fees_export_csv'),
    path('outstanding-fees/pdf/', views.OutstandingFeesExportPDFView.as_view(), name='outstanding_fees_export_pdf'),
    path('outstanding-fees/excel/', views.OutstandingFeesExportExcelView.as_view(), name='outstanding_fees_export_excel'),
  
    
    # Collection Report URLs
    path('collection-report/', views.CollectionReportView.as_view(), name='collection_report'),
    path('collection-report/csv/', views.CollectionReportExportCSVView.as_view(), name='collection_report_export_csv'),
    path('collection-report/pdf/', views.CollectionReportExportPDFView.as_view(), name='collection_report_export_pdf'),
    path('collection-report/excel/', views.CollectionReportExportExcelView.as_view(), name='collection_report_export_excel'),
    
    
    # Expense Report URLs
    path('expense-report/', views.ExpenseReportView.as_view(), name='expense_report'),
    path('expense-report/csv/', views.ExpenseReportExportCSVView.as_view(), name='expense_report_export_csv'),
    path('expense-report/pdf/', views.ExpenseReportExportPDFView.as_view(), name='expense_report_export_pdf'),
    path('expense-report/excel/', views.ExpenseReportExportExcelView.as_view(), name='expense_report_export_excel'),
    
    
    # Income & Expense Report (Profit & Loss) URLs
    path('income-expense-report/', views.IncomeExpenseReportView.as_view(), name='income_expense_report'),
    path('income-expense-report/csv/', views.IncomeExpenseReportExportCSVView.as_view(), name='income_expense_export_csv'),
    path('income-expense-report/pdf/', views.IncomeExpenseReportExportPDFView.as_view(), name='income_expense_export_pdf'),
    path('income-expense-report/excel/', views.IncomeExpenseReportExportExcelView.as_view(), name='income_expense_export_excel'),
    

    # Trial Balance Report URLs
    path('trial-balance/', views.TrialBalanceReportView.as_view(), name='trial_balance_report'),
    path('trial-balance/csv/', views.TrialBalanceExportCSVView.as_view(), name='trial_balance_export_csv'),
    path('trial-balance/pdf/', views.TrialBalanceExportPDFView.as_view(), name='trial_balance_export_pdf'),
    path('trial-balance/excel/', views.TrialBalanceExportExcelView.as_view(), name='trial_balance_export_excel'),
    
    
    # Balance Sheet Report URLs
    path('balance-sheet/', views.BalanceSheetReportView.as_view(), name='balance_sheet_report'),
    path('balance-sheet/csv/', views.BalanceSheetExportCSVView.as_view(), name='balance_sheet_export_csv'),
    path('balance-sheet/pdf/', views.BalanceSheetExportPDFView.as_view(), name='balance_sheet_export_pdf'),
    path('balance-sheet/excel/', views.BalanceSheetExportExcelView.as_view(), name='balance_sheet_export_excel'),
    
    
    # Cash Flow Statement Report URLs
    path('cash-flow-statement/', views.CashFlowStatementReportView.as_view(), name='cash_flow_statement_report'),
    path('cash-flow-statement/csv/', views.CashFlowStatementExportCSVView.as_view(), name='cash_flow_export_csv'),
    path('cash-flow-statement/pdf/', views.CashFlowStatementExportPDFView.as_view(), name='cash_flow_export_pdf'),
    path('cash-flow-statement/excel/', views.CashFlowStatementExportExcelView.as_view(), name='cash_flow_export_excel'),
        
    
    # Fee Projection Report URLs
    path('fee-projection/', views.FeeProjectionReportView.as_view(), name='fee_projection_report'),
    path('fee-projection/csv/', views.FeeProjectionExportCSVView.as_view(), name='fee_projection_export_csv'),
    path('fee-projection/pdf/', views.FeeProjectionExportPDFView.as_view(), name='fee_projection_export_pdf'),
    path('fee-projection/excel/', views.FeeProjectionExportExcelView.as_view(), name='fee_projection_export_excel'),
    
    
    # Budget Variance Report URLs
    path('budget-variance/', views.BudgetVarianceReportView.as_view(), name='budget_variance_report'),
    path('budget-variance/csv/', views.BudgetVarianceExportCSVView.as_view(), name='budget_variance_export_csv'),
    path('budget-variance/pdf/', views.BudgetVarianceExportPDFView.as_view(), name='budget_variance_export_pdf'),
    path('budget-variance/excel/', views.BudgetVarianceExportExcelView.as_view(), name='budget_variance_export_excel'),
    
    # Student Ledger Report URLs
    path('student-ledger/', views.StudentLedgerReportView.as_view(), name='student_ledger_report'),
    path('student-ledger/csv/', views.StudentLedgerExportCSVView.as_view(), name='student_ledger_export_csv'),
    path('student-ledger/excel/', views.StudentLedgerExportExcelView.as_view(), name='student_ledger_export_excel'),
    path('student-ledger/pdf/', views.StudentLedgerExportPDFView.as_view(), name='student_ledger_export_pdf'),


    # General Ledger Report URLs
    path('general-ledger/', views.GeneralLedgerReportView.as_view(), name='general_ledger_report'),
    path('general-ledger/csv/', views.GeneralLedgerExportCSVView.as_view(), name='general_ledger_export_csv'),
    path('general-ledger/pdf/', views.GeneralLedgerExportPDFView.as_view(), name='general_ledger_export_pdf'),
    path('general-ledger/excel/', views.GeneralLedgerExportExcelView.as_view(), name='general_ledger_export_excel'),


    # Accounts Receivable Aging Report URLs
    path('accounts-receivable-aging/', views.AccountsReceivableAgingReportView.as_view(), name='accounts_receivable_aging_report'),
    path('accounts-receivable-aging/csv/', views.AccountsReceivableAgingExportCSVView.as_view(), name='accounts_receivable_aging_export_csv'),
    path('accounts-receivable-aging/pdf/', views.AccountsReceivableAgingExportPDFView.as_view(), name='accounts_receivable_aging_export_pdf'),
    path('accounts-receivable-aging/excel/', views.AccountsReceivableAgingExportExcelView.as_view(), name='accounts_receivable_aging_export_excel'),


    # Accounts Payable Report URLs
    path('accounts-payable/', views.AccountsPayableReportView.as_view(), name='accounts_payable_report'),
    path('accounts-payable/csv/', views.AccountsPayableExportCSVView.as_view(), name='accounts_payable_export_csv'),
    path('accounts-payable/pdf/', views.AccountsPayableExportPDFView.as_view(), name='accounts_payable_export_pdf'),
    path('accounts-payable/excel/', views.AccountsPayableExportExcelView.as_view(), name='accounts_payable_export_excel'),


    # Bank Reconciliation Report URLs
    path('bank-reconciliation/', views.BankReconciliationReportView.as_view(), name='bank_reconciliation_report'),
    path('bank-reconciliation/csv/', views.BankReconciliationExportCSVView.as_view(), name='bank_reconciliation_export_csv'),
    path('bank-reconciliation/pdf/', views.BankReconciliationExportPDFView.as_view(), name='bank_reconciliation_export_pdf'),
    path('bank-reconciliation/excel/', views.BankReconciliationExportExcelView.as_view(), name='bank_reconciliation_export_excel'),


    # Budget vs Actual Report URLs
    path('budget-vs-actual/', views.BudgetVsActualReportView.as_view(), name='budget_vs_actual_report'),
    path('budget-vs-actual/csv/', views.BudgetVsActualExportCSVView.as_view(), name='budget_vs_actual_export_csv'),
    path('budget-vs-actual/pdf/', views.BudgetVsActualExportPDFView.as_view(), name='budget_vs_actual_export_pdf'),
    path('budget-vs-actual/excel/', views.BudgetVsActualExportExcelView.as_view(), name='budget_vs_actual_export_excel'),


    # Fee Collection Analysis Report URLs
    path('fee-collection-analysis/', views.FeeCollectionAnalysisReportView.as_view(), name='fee_collection_analysis_report'),
    path('fee-collection-analysis/csv/', views.FeeCollectionAnalysisExportCSVView.as_view(), name='fee_collection_analysis_export_csv'),
    path('fee-collection-analysis/pdf/', views.FeeCollectionAnalysisExportPDFView.as_view(), name='fee_collection_analysis_export_pdf'),
    path('fee-collection-analysis/excel/', views.FeeCollectionAnalysisExportExcelView.as_view(), name='fee_collection_analysis_export_excel'),


    # Student Account Statement URLs
    path('student-account-statement/', views.StudentAccountStatementReportView.as_view(), name='student_account_statement_report'),
    path('student-account-statement/csv/', views.StudentAccountStatementExportCSVView.as_view(), name='student_account_statement_export_csv'),
    path('student-account-statement/pdf/', views.StudentAccountStatementExportPDFView.as_view(), name='student_account_statement_export_pdf'),
    path('student-account-statement/excel/', views.StudentAccountStatementExportExcelView.as_view(), name='student_account_statement_export_excel'),


    # Class-wise Fee Collection Report URLs
    path('classwise-fee-collection/', views.ClasswiseFeeCollectionReportView.as_view(), name='classwise_fee_collection_report'),
    path('classwise-fee-collection/csv/', views.ClasswiseFeeCollectionExportCSVView.as_view(), name='classwise_fee_collection_export_csv'),
    path('classwise-fee-collection/pdf/', views.ClasswiseFeeCollectionExportPDFView.as_view(), name='classwise_fee_collection_export_pdf'),
    path('classwise-fee-collection/excel/', views.ClasswiseFeeCollectionExportExcelView.as_view(), name='classwise_fee_collection_export_excel'),


    # Fee Defaulters Report URLs
    path('fee-defaulters/', views.FeeDefaultersReportView.as_view(), name='fee_defaulters_report'),
    path('fee-defaulters/csv/', views.FeeDefaultersExportCSVView.as_view(), name='fee_defaulters_export_csv'),
    path('fee-defaulters/pdf/', views.FeeDefaultersExportPDFView.as_view(), name='fee_defaulters_export_pdf'),
    path('fee-defaulters/excel/', views.FeeDefaultersExportExcelView.as_view(), name='fee_defaulters_export_excel'),


    # Cash Flow Forecasting Report URLs
    path('cash-flow-forecasting/', views.CashFlowForecastingReportView.as_view(), name='cash_flow_forecasting_report'),
    path('cash-flow-forecasting/csv/', views.CashFlowForecastingExportCSVView.as_view(), name='cash_flow_forecasting_export_csv'),
    path('cash-flow-forecasting/pdf/', views.CashFlowForecastingExportPDFView.as_view(), name='cash_flow_forecasting_export_pdf'),
    path('cash-flow-forecasting/excel/', views.CashFlowForecastingExportExcelView.as_view(), name='cash_flow_forecasting_export_excel'),


    # Profitability Analysis Report URLs
    path('profitability-analysis/', views.ProfitabilityAnalysisReportView.as_view(), name='profitability_analysis_report'),
    path('profitability-analysis/csv/', views.ProfitabilityAnalysisExportCSVView.as_view(), name='profitability_analysis_export_csv'),
    path('profitability-analysis/pdf/', views.ProfitabilityAnalysisExportPDFView.as_view(), name='profitability_analysis_export_pdf'),
    path('profitability-analysis/excel/', views.ProfitabilityAnalysisExportExcelView.as_view(), name='profitability_analysis_export_excel'),


    # Financial Ratio Analysis Report URLs
    path('financial-ratio-analysis/', views.FinancialRatioAnalysisReportView.as_view(), name='financial_ratio_analysis_report'),
    path('financial-ratio-analysis/csv/', views.FinancialRatioAnalysisExportCSVView.as_view(), name='financial_ratio_analysis_export_csv'),
    path('financial-ratio-analysis/pdf/', views.FinancialRatioAnalysisExportPDFView.as_view(), name='financial_ratio_analysis_export_pdf'),
    path('financial-ratio-analysis/excel/', views.FinancialRatioAnalysisExportExcelView.as_view(), name='financial_ratio_analysis_export_excel'),


    # Revenue Recognition Report URLs
    path('revenue-recognition/', views.RevenueRecognitionReportView.as_view(), name='revenue_recognition_report'),
    path('revenue-recognition/csv/', views.RevenueRecognitionExportCSVView.as_view(), name='revenue_recognition_export_csv'),
    path('revenue-recognition/pdf/', views.RevenueRecognitionExportPDFView.as_view(), name='revenue_recognition_export_pdf'),
    path('revenue-recognition/excel/', views.RevenueRecognitionExportExcelView.as_view(), name='revenue_recognition_export_excel'),


    # Journal Entry Register Report URLs
    path('journal-entry-register/', views.JournalEntryRegisterReportView.as_view(), name='journal_entry_register_report'),
    path('journal-entry-register/csv/', views.JournalEntryRegisterExportCSVView.as_view(), name='journal_entry_register_export_csv'),
    path('journal-entry-register/pdf/', views.JournalEntryRegisterExportPDFView.as_view(), name='journal_entry_register_export_pdf'),
    path('journal-entry-register/excel/', views.JournalEntryRegisterExportExcelView.as_view(), name='journal_entry_register_export_excel'),


    # Period Closing Report URLs
    path('period-closing/', views.PeriodClosingReportView.as_view(), name='period_closing_report'),
    path('period-closing/csv/', views.PeriodClosingExportCSVView.as_view(), name='period_closing_export_csv'),
    path('period-closing/pdf/', views.PeriodClosingExportPDFView.as_view(), name='period_closing_export_pdf'),
    path('period-closing/excel/', views.PeriodClosingExportExcelView.as_view(), name='period_closing_export_excel'),


    # Audit Trail Report URLs
    path('audit-trail/', views.AuditTrailReportView.as_view(), name='audit_trail_report'),
    path('audit-trail/csv/', views.AuditTrailExportCSVView.as_view(), name='audit_trail_export_csv'),
    path('audit-trail/pdf/', views.AuditTrailExportPDFView.as_view(), name='audit_trail_export_pdf'),
    path('audit-trail/excel/', views.AuditTrailExportExcelView.as_view(), name='audit_trail_export_excel'),


    # Tax Compliance Report URLs
    path('tax-compliance/', views.TaxComplianceReportView.as_view(), name='tax_compliance_report'),
    path('tax-compliance/csv/', views.TaxComplianceExportCSVView.as_view(), name='tax_compliance_export_csv'),
    path('tax-compliance/pdf/', views.TaxComplianceExportPDFView.as_view(), name='tax_compliance_export_pdf'),
    path('tax-compliance/excel/', views.TaxComplianceExportExcelView.as_view(), name='tax_compliance_export_excel'),


    # Payment Method Analysis Report URLs
    path('payment-method-analysis/', views.PaymentMethodAnalysisReportView.as_view(), name='payment_method_analysis_report'),
    path('payment-method-analysis/csv/', views.PaymentMethodAnalysisExportCSVView.as_view(), name='payment_method_analysis_export_csv'),
    path('payment-method-analysis/pdf/', views.PaymentMethodAnalysisExportPDFView.as_view(), name='payment_method_analysis_export_pdf'),
    path('payment-method-analysis/excel/', views.PaymentMethodAnalysisExportExcelView.as_view(), name='payment_method_analysis_export_excel'),


    # Refund Analysis Report URLs
    path('refund-analysis/', views.RefundAnalysisReportView.as_view(), name='refund_analysis_report'),
    path('refund-analysis/csv/', views.RefundAnalysisExportCSVView.as_view(), name='refund_analysis_export_csv'),
    path('refund-analysis/pdf/', views.RefundAnalysisExportPDFView.as_view(), name='refund_analysis_export_pdf'),
    path('refund-analysis/excel/', views.RefundAnalysisExportExcelView.as_view(), name='refund_analysis_export_excel'),


    # Collection Performance Report URLs
    path('collection-performance/', views.CollectionPerformanceReportView.as_view(), name='collection_performance_report'),
    path('collection-performance/csv/', views.CollectionPerformanceExportCSVView.as_view(), name='collection_performance_export_csv'),
    path('collection-performance/pdf/', views.CollectionPerformanceExportPDFView.as_view(), name='collection_performance_export_pdf'),
    path('collection-performance/excel/', views.CollectionPerformanceExportExcelView.as_view(), name='collection_performance_export_excel'),


    # Fee Structure Analysis Report URLs
    path('fee-structure-analysis/', views.FeeStructureAnalysisReportView.as_view(), name='fee_structure_analysis_report'),
    path('fee-structure-analysis/csv/', views.FeeStructureAnalysisExportCSVView.as_view(), name='fee_structure_analysis_export_csv'),
    path('fee-structure-analysis/pdf/', views.FeeStructureAnalysisExportPDFView.as_view(), name='fee_structure_analysis_export_pdf'),
    path('fee-structure-analysis/excel/', views.FeeStructureAnalysisExportExcelView.as_view(), name='fee_structure_analysis_export_excel'),

]

