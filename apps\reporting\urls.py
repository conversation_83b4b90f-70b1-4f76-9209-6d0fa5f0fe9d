# D:\school_fees_saas_V2\apps\reporting\urls.py
from django.urls import path
from . import views

app_name = 'reporting' # Define the namespace

urlpatterns = [
    
    path('', views.ReportingHomeView.as_view(), name='reporting_home'),
    
    path('payment-summary/', views.PaymentSummaryReportView.as_view(), name='payment_summary_report'),
    
    # URL for student ledger:
    # Option 1: Student selection on the page via form, one URL for the report
    path('student-ledger/', views.StudentLedgerReportView.as_view(), name='student_ledger_report'),
    # Option 2: Student PK in URL (requires link to specific student ledger from elsewhere)
    # path('student-ledger/<uuid:student_pk>/', views.StudentLedgerReportView.as_view(), name='student_ledger_report_for_student'),
    
    path('outstanding-fees/', views.OutstandingFeesReportView.as_view(), name='outstanding_fees_report'),
    
    path('student-ledger-summary/', views.StudentLedgerReportView.as_view(), name='student_ledger_report_summary'),
    
    path('collections/', views.CollectionReportView.as_view(), name='collection_report'),
    
    path('expenses/', views.ExpenseReportView.as_view(), name='expense_report'),

    path('income-expense/', views.IncomeExpenseReportView.as_view(), name='income_expense_report'),
    path('trial-balance/', views.TrialBalanceView.as_view(), name='trial_balance_report'),
    path('balance-sheet/', views.BalanceSheetView.as_view(), name='balance_sheet_report'),
    path('cash-flow/', views.CashFlowStatementView.as_view(), name='cash_flow_statement_report'),
    path('budget-variance/', views.BudgetVarianceReportView.as_view(), name='budget_variance_report'),
    
    # ADD THIS NEW URL PATTERN:
    path('fee-projection/', views.FeeProjectionReportView.as_view(), name='fee_projection_report'),
    
    # path('payment-summary/', views.payment_summary_report_view, name='payment_summary_report'), 
    
    # path('student-ledger/', views.student_ledger_report_view, name='student_ledger_report'),
    # Add other report URLs here
    
    path('student-ledger/', views.StudentLedgerReportView.as_view(), name='student_ledger_report'),
    
    # Student Ledger Export URLs
    path('student-ledger/csv/', views.StudentLedgerExportCSVView.as_view(), name='student_ledger_export_csv'),
    path('student-ledger/excel/', views.StudentLedgerExportExcelView.as_view(), name='student_ledger_export_excel'),
    path('student-ledger/pdf/', views.StudentLedgerExportPDFView.as_view(), name='student_ledger_export_pdf'),

]

