#!/usr/bin/env python
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from django_tenants.utils import schema_context
from apps.fees.models import Invoice, InvoiceDetail
from apps.payments.models import Payment, PaymentAllocation
from apps.students.models import Student

def check_invoice_data():
    with schema_context('alpha'):
        print(f"Invoices: {Invoice.objects.count()}")
        print(f"Invoice Details: {InvoiceDetail.objects.count()}")
        print(f"Payment Allocations: {PaymentAllocation.objects.count()}")
        print(f"Students: {Student.objects.count()}")
        print(f"Payments: {Payment.objects.count()}")
        
        # Show sample invoices
        invoices = Invoice.objects.all()[:5]
        print(f"\nSample Invoices:")
        for inv in invoices:
            total = inv.subtotal_amount - inv.total_concession_amount
            print(f"Invoice {inv.invoice_number}: {inv.subtotal_amount} - {inv.total_concession_amount} = {total}")
            print(f"  Student: {inv.student.get_full_name()}")
            print(f"  Status: {inv.status}")
            print(f"  Details count: {inv.details.count()}")
            print(f"  Allocations count: {inv.allocations.count()}")
            
            # Show allocations
            for alloc in inv.allocations.all():
                print(f"    Allocation: {alloc.amount_allocated} from Payment {alloc.payment.pk}")
            print()

if __name__ == "__main__":
    check_invoice_data()
