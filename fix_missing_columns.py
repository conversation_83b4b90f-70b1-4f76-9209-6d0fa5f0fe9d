#!/usr/bin/env python
"""
Fix Missing Columns
Manually add missing columns to all tenant schemas

Usage: python fix_missing_columns.py
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from django.db import connection
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def fix_missing_columns():
    """Fix missing columns across all tenant schemas"""
    
    tenant_schemas = ['alpha', 'mandiva', 'aischool', 'bea', 'ruzivo', 'fox']
    
    # Missing columns that need to be added
    missing_columns = [
        {
            'table': 'schools_staffuser',
            'column': 'employee_id',
            'definition': 'VARCHAR(50) DEFAULT \'\''
        },
        {
            'table': 'schools_staffuser', 
            'column': 'designation',
            'definition': 'VARCHAR(100) DEFAULT \'\''
        },
        {
            'table': 'payments_payment',
            'column': 'payment_reference',
            'definition': 'VARCHAR(100) DEFAULT \'\''
        },
        {
            'table': 'students_student',
            'column': 'roll_number', 
            'definition': 'VARCHAR(50) DEFAULT \'\''
        },
        {
            'table': 'fees_invoice',
            'column': 'invoice_number',
            'definition': 'VARCHAR(50) DEFAULT \'\''
        }
    ]
    
    logger.info("=== FIXING MISSING COLUMNS ===")
    
    for tenant_schema in tenant_schemas:
        logger.info(f"\nFixing columns in {tenant_schema}")
        
        try:
            with connection.cursor() as cursor:
                cursor.execute(f'SET search_path TO "{tenant_schema}"')
                
                for col_info in missing_columns:
                    table = col_info['table']
                    column = col_info['column']
                    definition = col_info['definition']
                    
                    try:
                        # Check if table exists
                        cursor.execute(f"""
                            SELECT EXISTS (
                                SELECT FROM information_schema.tables 
                                WHERE table_schema = '{tenant_schema}' 
                                AND table_name = '{table}'
                            )
                        """)
                        
                        if not cursor.fetchone()[0]:
                            logger.warning(f"  ⚠️  Table {table} does not exist in {tenant_schema}")
                            continue
                        
                        # Check if column exists
                        cursor.execute(f"""
                            SELECT EXISTS (
                                SELECT FROM information_schema.columns 
                                WHERE table_schema = '{tenant_schema}' 
                                AND table_name = '{table}'
                                AND column_name = '{column}'
                            )
                        """)
                        
                        if cursor.fetchone()[0]:
                            logger.info(f"  ✅ {table}.{column} already exists")
                            continue
                        
                        # Add missing column
                        alter_sql = f"ALTER TABLE {table} ADD COLUMN IF NOT EXISTS {column} {definition}"
                        cursor.execute(alter_sql)
                        
                        logger.info(f"  ✅ Added {table}.{column} to {tenant_schema}")
                        
                    except Exception as e:
                        logger.error(f"  ❌ Failed to add {table}.{column} to {tenant_schema}: {e}")
                
        except Exception as e:
            logger.error(f"❌ Failed to process {tenant_schema}: {e}")

def create_missing_tables():
    """Create missing tables in fox schema"""
    
    logger.info("\n=== CREATING MISSING TABLES IN FOX ===")
    
    try:
        with connection.cursor() as cursor:
            cursor.execute('SET search_path TO "fox"')
            
            # Check which essential tables are missing
            essential_tables = [
                'schools_term', 'schools_schoolclass', 'schools_section',
                'fees_feehead', 'fees_feestructure', 'fees_feestructure_applicable_classes',
                'fees_invoice', 'fees_invoicedetail', 'payments_payment', 'payments_paymentallocation',
                'hr_leavebalance', 'hr_leaverequest', 'hr_leavetype',
                'payments_paymentmethod', 'fees_concessiontype', 'school_calendar_eventcategory'
            ]
            
            missing_tables = []
            for table in essential_tables:
                cursor.execute(f"""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_schema = 'fox' 
                        AND table_name = '{table}'
                    )
                """)
                
                if not cursor.fetchone()[0]:
                    missing_tables.append(table)
            
            if missing_tables:
                logger.info(f"Missing tables in fox: {missing_tables}")
                
                # Copy table structures from alpha
                cursor.execute('SET search_path TO "alpha"')
                
                for table in missing_tables:
                    try:
                        # Get CREATE TABLE statement from alpha
                        cursor.execute(f"""
                            SELECT 
                                'CREATE TABLE ' || table_name || ' (' ||
                                string_agg(
                                    column_name || ' ' || 
                                    CASE 
                                        WHEN data_type = 'character varying' THEN 
                                            CASE WHEN character_maximum_length IS NOT NULL 
                                            THEN 'VARCHAR(' || character_maximum_length || ')'
                                            ELSE 'VARCHAR(255)' END
                                        WHEN data_type = 'character' THEN 'CHAR(' || character_maximum_length || ')'
                                        WHEN data_type = 'numeric' THEN 'NUMERIC(' || numeric_precision || ',' || numeric_scale || ')'
                                        WHEN data_type = 'integer' THEN 'INTEGER'
                                        WHEN data_type = 'bigint' THEN 'BIGINT'
                                        WHEN data_type = 'boolean' THEN 'BOOLEAN'
                                        WHEN data_type = 'timestamp with time zone' THEN 'TIMESTAMP WITH TIME ZONE'
                                        WHEN data_type = 'timestamp without time zone' THEN 'TIMESTAMP'
                                        WHEN data_type = 'date' THEN 'DATE'
                                        WHEN data_type = 'time without time zone' THEN 'TIME'
                                        WHEN data_type = 'text' THEN 'TEXT'
                                        WHEN data_type = 'double precision' THEN 'DOUBLE PRECISION'
                                        WHEN data_type = 'real' THEN 'REAL'
                                        WHEN data_type = 'smallint' THEN 'SMALLINT'
                                        WHEN data_type = 'uuid' THEN 'UUID'
                                        ELSE UPPER(data_type)
                                    END ||
                                    CASE WHEN is_nullable = 'NO' THEN ' NOT NULL' ELSE '' END ||
                                    CASE 
                                        WHEN column_default IS NOT NULL AND column_default NOT LIKE 'nextval%' 
                                        THEN ' DEFAULT ' || column_default 
                                        ELSE '' 
                                    END,
                                    ', ' ORDER BY ordinal_position
                                ) || ');'
                            FROM information_schema.columns 
                            WHERE table_name = %s 
                            AND table_schema = 'alpha'
                            GROUP BY table_name
                        """, [table])
                        
                        result = cursor.fetchone()
                        if result:
                            create_sql = result[0]
                            
                            # Switch to fox schema and create table
                            cursor.execute('SET search_path TO "fox"')
                            cursor.execute(create_sql)
                            
                            # Create sequence if needed
                            cursor.execute('SET search_path TO "alpha"')
                            cursor.execute(f"""
                                SELECT column_name 
                                FROM information_schema.columns 
                                WHERE table_name = '{table}' 
                                AND table_schema = 'alpha'
                                AND column_name = 'id'
                            """)
                            
                            if cursor.fetchone():
                                cursor.execute('SET search_path TO "fox"')
                                cursor.execute(f'CREATE SEQUENCE IF NOT EXISTS {table}_id_seq')
                                cursor.execute(f'ALTER TABLE {table} ALTER COLUMN id SET DEFAULT nextval(\'{table}_id_seq\')')
                            
                            logger.info(f"  ✅ Created table {table} in fox")
                            
                            # Switch back to alpha for next iteration
                            cursor.execute('SET search_path TO "alpha"')
                        
                    except Exception as e:
                        logger.error(f"  ❌ Failed to create {table} in fox: {e}")
            else:
                logger.info("✅ All essential tables exist in fox")
                
    except Exception as e:
        logger.error(f"❌ Failed to create missing tables: {e}")

def create_essential_data():
    """Create essential data in all schemas"""
    
    logger.info("\n=== CREATING ESSENTIAL DATA ===")
    
    tenant_schemas = ['mandiva', 'aischool', 'bea', 'ruzivo', 'fox']
    
    for tenant_schema in tenant_schemas:
        logger.info(f"\nCreating essential data in {tenant_schema}")
        
        try:
            with connection.cursor() as cursor:
                cursor.execute(f'SET search_path TO "{tenant_schema}"')
                
                # Create academic year if missing
                try:
                    cursor.execute("SELECT COUNT(*) FROM schools_academicyear")
                    if cursor.fetchone()[0] == 0:
                        cursor.execute("""
                            INSERT INTO schools_academicyear (name, start_date, end_date, is_active, is_current, created_at, updated_at) 
                            VALUES ('Academic Year', CURRENT_DATE, CURRENT_DATE + INTERVAL '1 year', TRUE, TRUE, NOW(), NOW())
                        """)
                        logger.info(f"  ✅ Created academic year in {tenant_schema}")
                except Exception as e:
                    logger.warning(f"  ⚠️  Academic year creation failed in {tenant_schema}: {e}")
                
                # Create essential reference data
                essential_data = [
                    ("hr_leavetype", """
                        INSERT INTO hr_leavetype (name, description, max_days_per_year, is_paid, requires_approval, is_active, created_at, updated_at) VALUES
                        ('Annual Leave', 'Annual vacation leave', 21, TRUE, TRUE, TRUE, NOW(), NOW()),
                        ('Sick Leave', 'Medical leave', 10, TRUE, FALSE, TRUE, NOW(), NOW()),
                        ('Maternity Leave', 'Maternity leave', 90, TRUE, TRUE, TRUE, NOW(), NOW()),
                        ('Emergency Leave', 'Emergency leave', 3, FALSE, TRUE, TRUE, NOW(), NOW())
                        ON CONFLICT DO NOTHING
                    """),
                    ("payments_paymentmethod", """
                        INSERT INTO payments_paymentmethod (name, description, is_active, created_at, updated_at) VALUES
                        ('Cash', 'Cash payment', TRUE, NOW(), NOW()),
                        ('Bank Transfer', 'Bank transfer payment', TRUE, NOW(), NOW()),
                        ('Mobile Money', 'Mobile money payment', TRUE, NOW(), NOW()),
                        ('Cheque', 'Cheque payment', TRUE, NOW(), NOW())
                        ON CONFLICT DO NOTHING
                    """),
                    ("fees_concessiontype", """
                        INSERT INTO fees_concessiontype (name, description, is_percentage, default_value, is_active, created_at, updated_at) VALUES
                        ('Sibling Discount', 'Discount for siblings', TRUE, 10.00, TRUE, NOW(), NOW()),
                        ('Staff Child Discount', 'Discount for staff children', TRUE, 50.00, TRUE, NOW(), NOW()),
                        ('Merit Scholarship', 'Merit-based scholarship', TRUE, 25.00, TRUE, NOW(), NOW())
                        ON CONFLICT DO NOTHING
                    """),
                    ("school_calendar_eventcategory", """
                        INSERT INTO school_calendar_eventcategory (name, color, icon, description, is_active, created_at, updated_at) VALUES
                        ('Academic', '#007bff', 'bi-book', 'Academic events', TRUE, NOW(), NOW()),
                        ('Sports', '#28a745', 'bi-trophy', 'Sports events', TRUE, NOW(), NOW()),
                        ('Cultural', '#ffc107', 'bi-music-note', 'Cultural events', TRUE, NOW(), NOW()),
                        ('Meeting', '#6c757d', 'bi-people', 'Meetings', TRUE, NOW(), NOW()),
                        ('Holiday', '#dc3545', 'bi-calendar-x', 'Holidays', TRUE, NOW(), NOW())
                        ON CONFLICT DO NOTHING
                    """)
                ]
                
                for table_name, sql in essential_data:
                    try:
                        # Check if table exists
                        cursor.execute(f"""
                            SELECT EXISTS (
                                SELECT FROM information_schema.tables 
                                WHERE table_schema = '{tenant_schema}' 
                                AND table_name = '{table_name}'
                            )
                        """)
                        
                        if cursor.fetchone()[0]:
                            cursor.execute(sql)
                            logger.info(f"  ✅ Created {table_name} data in {tenant_schema}")
                        else:
                            logger.warning(f"  ⚠️  Table {table_name} does not exist in {tenant_schema}")
                            
                    except Exception as e:
                        logger.warning(f"  ⚠️  Failed to create {table_name} data in {tenant_schema}: {e}")
                
        except Exception as e:
            logger.error(f"❌ Failed to create essential data in {tenant_schema}: {e}")

def main():
    """Main function"""
    logger.info("=== COMPREHENSIVE TENANT STRUCTURE FIX ===")
    
    try:
        # 1. Fix missing columns
        fix_missing_columns()
        
        # 2. Create missing tables (specifically for fox)
        create_missing_tables()
        
        # 3. Create essential data
        create_essential_data()
        
        logger.info("\n🎉 TENANT STRUCTURE FIX COMPLETE!")
        logger.info("All tenants should now have:")
        logger.info("- All required columns")
        logger.info("- All essential tables")
        logger.info("- Essential reference data")
        logger.info("- Working dropdown menus")
        logger.info("- Proper form functionality")
        
        return True
        
    except Exception as e:
        logger.error(f"Comprehensive fix failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
