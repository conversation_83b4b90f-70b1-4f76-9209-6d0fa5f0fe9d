{# templates/reporting/outstanding_fees_report.html #}
{% extends "tenant_base.html" %}

{% load humanize core_tags %} {# Ensure both are loaded #}

{% block title %}{{ view_title|default:"Outstanding Fees Report" }}{% endblock %}

{% block content %}

<div class="container-fluid mt-3">
    {% with report_icon_class="bi bi-collection-fill" %} {# Define icon for this specific report #}
        {% include "partials/_report_header.html" %}
    {% endwith %}
{% comment %} <div class="container-fluid mt-4"> {# Use fluid for wide table #}
    <h1>{{ view_title|default:"Outstanding Fees Report" }}</h1> {% endcomment %}



    {# --- Premium Filter Form --- #}
    <style>
        /* Premium Outstanding Fees Filter Form Design */
        .premium-outstanding-filter-card {
            border: none;
            border-radius: 1.5rem;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            transition: all 0.3s ease;
            margin-bottom: 2rem;
        }

        .premium-outstanding-filter-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .premium-outstanding-filter-header {
            background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
            color: white;
            padding: 1.5rem;
            position: relative;
            overflow: hidden;
        }

        .premium-outstanding-filter-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transform: rotate(45deg);
            animation: shimmer 3s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        .premium-outstanding-filter-title {
            margin: 0;
            font-size: 1.25rem;
            font-weight: 600;
            position: relative;
            z-index: 1;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .premium-outstanding-filter-toggle {
            color: rgba(255, 255, 255, 0.9);
            text-decoration: none;
            font-size: 0.9rem;
            font-weight: 500;
            transition: all 0.3s ease;
            position: relative;
            z-index: 1;
        }

        .premium-outstanding-filter-toggle:hover {
            color: white;
            text-decoration: underline;
        }

        .premium-outstanding-filter-body {
            padding: 2rem;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
        }

        .form-floating-outstanding {
            position: relative;
            margin-bottom: 1.5rem;
        }

        .form-floating-outstanding .form-control,
        .form-floating-outstanding .form-select {
            height: calc(3.5rem + 2px);
            line-height: 1.25;
            border: 2px solid #e9ecef;
            border-radius: 1rem;
            padding: 1rem 0.75rem 0.25rem 0.75rem;
            background: rgba(255, 255, 255, 0.9);
            transition: all 0.3s ease;
            font-size: 1rem;
        }

        .form-floating-outstanding .form-control:focus,
        .form-floating-outstanding .form-select:focus {
            border-color: #28a745;
            box-shadow: 0 0 0 0.25rem rgba(40, 167, 69, 0.25);
            background: white;
            transform: translateY(-2px);
        }

        .form-floating-outstanding > label {
            position: absolute;
            top: 0;
            left: 0;
            height: 100%;
            padding: 1rem 0.75rem;
            pointer-events: none;
            border: 2px solid transparent;
            transform-origin: 0 0;
            transition: opacity 0.1s ease-in-out, transform 0.1s ease-in-out;
            color: #6c757d;
            font-weight: 500;
        }

        .form-floating-outstanding > .form-control:focus ~ label,
        .form-floating-outstanding > .form-control:not(:placeholder-shown) ~ label,
        .form-floating-outstanding > .form-select ~ label {
            opacity: 0.65;
            transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
            color: #28a745;
        }

        .form-floating-outstanding .form-control:focus ~ label {
            color: #28a745;
        }

        .premium-outstanding-field-icon {
            position: absolute;
            right: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: #6c757d;
            font-size: 1.1rem;
            z-index: 5;
            pointer-events: none;
            transition: color 0.3s ease;
        }

        .form-floating-outstanding .form-control:focus ~ .premium-outstanding-field-icon,
        .form-floating-outstanding .form-select:focus ~ .premium-outstanding-field-icon {
            color: #28a745;
        }

        .btn-premium-outstanding {
            border-radius: 0.75rem;
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
            border: none;
            position: relative;
            overflow: hidden;
            min-width: 140px;
        }

        .btn-premium-outstanding::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .btn-premium-outstanding:hover::before {
            left: 100%;
        }

        .btn-premium-outstanding-primary {
            background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
        }

        .btn-premium-outstanding-primary:hover {
            background: linear-gradient(135deg, #1e7e34 0%, #155724 100%);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
            color: white;
        }

        .btn-premium-outstanding-secondary {
            background: transparent;
            border: 2px solid #6c757d;
            color: #6c757d;
        }

        .btn-premium-outstanding-secondary:hover {
            background: #6c757d;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
        }

        .outstanding-filter-actions-container {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 1rem;
            padding: 1.5rem;
            margin-top: 1.5rem;
            border: 1px solid #dee2e6;
        }

        @media (max-width: 768px) {
            .premium-outstanding-filter-header {
                padding: 1rem;
            }

            .premium-outstanding-filter-title {
                font-size: 1.1rem;
            }

            .premium-outstanding-filter-body {
                padding: 1.5rem;
            }

            .btn-premium-outstanding {
                padding: 0.6rem 1.2rem;
                font-size: 0.9rem;
                min-width: 120px;
            }
        }

        @media (max-width: 576px) {
            .form-floating-outstanding .form-control,
            .form-floating-outstanding .form-select {
                height: calc(3rem + 2px);
                padding: 0.75rem 0.5rem 0.25rem 0.5rem;
            }

            .premium-outstanding-field-icon {
                right: 0.75rem;
            }
        }
    </style>

    <div class="card premium-outstanding-filter-card">
        <div class="premium-outstanding-filter-header">
            <div class="premium-outstanding-filter-title">
                <span>
                    <i class="bi bi-funnel-fill me-2"></i>Filter Outstanding Fees
                </span>
                <a class="premium-outstanding-filter-toggle" data-bs-toggle="collapse" href="#outstandingFilterCollapse" role="button" aria-expanded="{% if current_class_filter or current_min_due_filter %}true{% else %}false{% endif %}" aria-controls="outstandingFilterCollapse">
                    <i class="bi bi-chevron-down me-1"></i>Toggle Filters
                </a>
            </div>
        </div>
        <div class="collapse {% if current_class_filter or current_min_due_filter %}show{% endif %}" id="outstandingFilterCollapse">
            <div class="premium-outstanding-filter-body">
                <form method="get" novalidate id="outstandingFeesFilterForm">
                    <div class="row g-4">
                        <div class="col-md-6">
                            <div class="form-floating-outstanding">
                                <select name="class" id="class_filter" class="form-select" placeholder="Select Class">
                                    <option value="">-- All Classes --</option>
                                    {% for class_obj in available_classes %}
                                        <option value="{{ class_obj.pk }}" {% if class_obj.pk|stringformat:"s" == current_class_filter %}selected{% endif %}>
                                            {{ class_obj.name }}
                                        </option>
                                    {% endfor %}
                                </select>
                                <i class="premium-outstanding-field-icon bi bi-mortarboard"></i>
                                <label for="class_filter">Class</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating-outstanding">
                                <input type="number" step="0.01" min="0" name="min_due" id="min_due_filter" value="{{ current_min_due_filter|default:'' }}" class="form-control" placeholder="Minimum Amount Due">
                                <i class="premium-outstanding-field-icon bi bi-currency-dollar"></i>
                                <label for="min_due_filter">Minimum Amount Due</label>
                            </div>
                        </div>
                    </div>

                    {# Action Buttons #}
                    <div class="outstanding-filter-actions-container">
                        <div class="row g-3 justify-content-end">
                            {% if current_class_filter or current_min_due_filter %}
                            <div class="col-auto">
                                <a href="{% url 'reporting:outstanding_fees_report' %}" class="btn btn-premium-outstanding btn-premium-outstanding-secondary">
                                    <i class="bi bi-arrow-clockwise me-2"></i>Clear Filters
                                </a>
                            </div>
                            {% endif %}
                            <div class="col-auto">
                                <button type="submit" class="btn btn-premium-outstanding btn-premium-outstanding-primary" id="applyOutstandingFiltersBtn">
                                    <i class="bi bi-funnel-fill me-2"></i>Apply Filters
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Form submission enhancement
        const form = document.getElementById('outstandingFeesFilterForm');
        const submitBtn = document.getElementById('applyOutstandingFiltersBtn');

        if (form && submitBtn) {
            form.addEventListener('submit', function(e) {
                // Add loading state
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>Applying...';

                // Re-enable after 5 seconds in case of issues
                setTimeout(function() {
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = '<i class="bi bi-funnel-fill me-2"></i>Apply Filters';
                }, 5000);
            });
        }

        // Enhanced form field interactions
        const formControls = document.querySelectorAll('.form-floating-outstanding .form-control, .form-floating-outstanding .form-select');
        formControls.forEach(function(control) {
            // Focus enhancement
            control.addEventListener('focus', function() {
                this.closest('.form-floating-outstanding').style.transform = 'translateY(-2px)';
                this.closest('.form-floating-outstanding').style.boxShadow = '0 5px 15px rgba(40, 167, 69, 0.1)';
            });

            // Blur enhancement
            control.addEventListener('blur', function() {
                this.closest('.form-floating-outstanding').style.transform = 'translateY(0)';
                this.closest('.form-floating-outstanding').style.boxShadow = 'none';
            });
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Enter to submit form
            if (e.key === 'Enter' && e.ctrlKey) {
                e.preventDefault();
                if (submitBtn) {
                    submitBtn.click();
                }
            }

            // Escape to reset form
            if (e.key === 'Escape') {
                const resetBtn = document.querySelector('a[href="{% url 'reporting:outstanding_fees_report' %}"]');
                if (resetBtn) {
                    resetBtn.click();
                }
            }
        });

        console.log('Premium outstanding fees filter form initialized');
    });
    </script>

    {# --- Summary Section --- #}
    {# Assumes view passes overall_total_due and students_report (queryset) #}
    <div class="alert alert-info" role="alert">
        <strong>Total Outstanding (Filtered): {{ school_profile.currency_symbol|default:'$' }}{{ overall_total_due|default:'0.00'|floatformat:2|intcomma }}</strong>
        ({{ students_report.count|default:0|intcomma }} Student{{ students_report.count|pluralize }} Found)
    </div>

    {# --- Report Table --- #}
    {% if students_report %}
        <div class="card shadow-sm">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-striped table-hover table-sm align-middle">
                        <thead class="table-light">
                            <tr>
                                {# Headers should match data below #}
                                <th>Student Name</th>
                                <th>Admission #</th>
                                <th>Class</th>
                                <th>Parent Name</th>
                                <th>Parent Phone</th>
                                <th class="text-end">Total Billed</th>
                                <th class="text-end">Total Discount</th>
                                <th class="text-end">Total Paid</th>
                                <th class="text-end">Amount Due Now</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for student in students_report %}
                            <tr>
                                <td><a href="{% url 'students:student_detail' student.pk %}">{{ student.full_name }}</a></td>
                                <td>{{ student.admission_number }}</td>
                                <td>{{ student.current_class.name|default:"-" }} {% if student.current_section %}- {{ student.current_section.name }}{% endif %}</td>
                                <td>
                                    {% for parent in student.parents.all %}
                                        {{ parent.get_full_name }}{% if not forloop.last %}, {% endif %}
                                    {% empty %}
                                        -
                                    {% endfor %}
                                </td>
                                <td>
                                    {% for parent in student.parents.all %}
                                        {{ parent.phone_number|default:"-" }}{% if not forloop.last %}, {% endif %}
                                    {% empty %}
                                        -
                                    {% endfor %}
                                </td>
                                {# --- Access CORRECT ANNOTATED fields --- #}
                                <td class="text-end">{{ school_profile.currency_symbol|default:"$" }}{{ student.total_billed|default:"0.00"|floatformat:2|intcomma }}</td>
                                <td class="text-end">
                                    {% if student.total_discount and student.total_discount > 0 %}
                                        {{ school_profile.currency_symbol|default:"$" }}{{ student.total_discount|floatformat:2|intcomma }}
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                                <td class="text-end">{{ school_profile.currency_symbol|default:"$" }}{{ student.total_paid|default:"0.00"|floatformat:2|intcomma }}</td>
                                <td class="text-end fw-bold">{{ school_profile.currency_symbol|default:"$" }}{{ student.outstanding_balance|default:"0.00"|floatformat:2|intcomma }}</td>
                                {# --- End Annotated Fields --- #}
                            </tr>
                            {% endfor %}
                        </tbody>
                        <tfoot class="table-light">
                            <tr>
                                <td colspan="8" class="text-end fw-bold">Grand Total Outstanding:</td>
                                <td class="text-end fw-bold">{{ school_profile.currency_symbol|default:"$" }}{{ overall_total_due|default:"0.00"|floatformat:2|intcomma }}</td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>
        {# Pagination placeholder #}
        {% comment %}
        {% if is_paginated %} <div class="mt-3"> {% include "includes/_pagination.html" with page_obj=page_obj query_params=request.GET.urlencode %} </div> {% endif %}
        {% endcomment %}
    {% else %}
        <div class="alert alert-warning">No students with outstanding fees found matching the criteria.</div>
        {% if current_class_filter or current_min_due_filter %}
            <p><a href="{% url 'reporting:outstanding_fees_report' %}" class="btn btn-secondary btn-sm">Show All Students</a></p>
        {% endif %}
    {% endif %}

    {# --- Back Button --- #}
    <div class="footer-actions mt-3">
        <a href="{% url 'schools:dashboard' %}" class="btn btn-secondary">
            <i class="bi bi-arrow-left-circle"></i> Back to Dashboard
        </a>
    </div>

</div> {# End container #}
{% endblock %}

