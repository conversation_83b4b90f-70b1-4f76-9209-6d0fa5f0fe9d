{% extends "tenant_base.html" %}
{% load humanize static core_tags reporting_filters %}

{% block title %}{{ view_title|default:"Period Closing Report" }}{% endblock %}

{% block content %}
<div class="container-fluid mt-3">
    {% with report_icon_class="bi bi-calendar-check" %}
        {% include "partials/_report_header.html" %}
    {% endwith %}

    {% include "partials/_messages.html" %}
    {% include "partials/_report_filter_form.html" with filter_form=filter_form report_code=report_code specific_filter_title="Period Closing Filters" %}

    <!-- Period Overview -->
    <div class="card mb-4">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="bi bi-calendar-event me-2"></i>Period Closing Overview</h5>
        </div>
        <div class="card-body">
            <div class="row mb-3">
                <div class="col-md-12">
                    <p>
                        <strong>Period:</strong> {{ validation_results.period_name }} | 
                        <strong>Closing Type:</strong> {{ closing_period|title }} | 
                        <strong>Validation Level:</strong> {{ validation_level|title }}
                    </p>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-4">
                    <div class="text-center">
                        <h6 class="text-muted">Overall Status</h6>
                        <p class="mb-0 fw-bold {% if validation_results.overall_status == 'READY' %}text-success{% elif validation_results.overall_status == 'WARNINGS' %}text-warning{% else %}text-danger{% endif %} fs-4">
                            {{ validation_results.overall_status }}
                        </p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="text-center">
                        <h6 class="text-muted">Validations Passed</h6>
                        <p class="mb-0 fw-bold text-success">{{ validation_results.validations|length }}</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="text-center">
                        <h6 class="text-muted">Issues Found</h6>
                        <p class="mb-0 fw-bold {% if validation_results.errors %}text-danger{% elif validation_results.warnings %}text-warning{% else %}text-success{% endif %}">
                            {{ validation_results.errors|length|add:validation_results.warnings|length }}
                        </p>
                    </div>
                </div>
            </div>
            
            <!-- Status Progress Bar -->
            <div class="mt-3">
                <h6>Closing Readiness</h6>
                <div class="progress" style="height: 25px;">
                    {% if validation_results.overall_status == 'READY' %}
                    <div class="progress-bar bg-success" role="progressbar" style="width: 100%">
                        Ready for Closing
                    </div>
                    {% elif validation_results.overall_status == 'WARNINGS' %}
                    <div class="progress-bar bg-warning" role="progressbar" style="width: 75%">
                        Warnings - Review Required
                    </div>
                    {% else %}
                    <div class="progress-bar bg-danger" role="progressbar" style="width: 50%">
                        Errors - Action Required
                    </div>
                    {% endif %}
                </div>
                <small class="text-muted">
                    {% if validation_results.overall_status == 'READY' %}
                    All validations passed - period is ready for closing
                    {% elif validation_results.overall_status == 'WARNINGS' %}
                    Some warnings found - review before proceeding with closing
                    {% else %}
                    Critical errors found - must be resolved before closing
                    {% endif %}
                </small>
            </div>
        </div>
    </div>

    <!-- Closing Checklist -->
    <div class="card mb-4">
        <div class="card-header bg-light d-flex justify-content-between align-items-center">
            <h5 class="mb-0"><i class="bi bi-list-check me-2"></i>Period Closing Checklist</h5>
            <span class="badge bg-primary">{{ closing_checklist|length }} tasks</span>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-striped table-hover mb-0">
                    <thead class="table-dark">
                        <tr>
                            <th>Task</th>
                            <th class="text-center">Status</th>
                            <th class="text-center">Issues</th>
                            <th>Description</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for task in closing_checklist %}
                        <tr class="{% if task.status == 'PENDING' and task.issues > 0 %}table-warning{% elif task.status == 'COMPLETED' %}table-light{% endif %}">
                            <td>
                                <strong>{{ task.task }}</strong>
                            </td>
                            <td class="text-center">
                                {% if task.status == 'COMPLETED' %}
                                <span class="badge bg-success">
                                    <i class="bi bi-check-circle me-1"></i>{{ task.status }}
                                </span>
                                {% else %}
                                <span class="badge bg-warning">
                                    <i class="bi bi-clock me-1"></i>{{ task.status }}
                                </span>
                                {% endif %}
                            </td>
                            <td class="text-center">
                                {% if task.issues > 0 %}
                                <span class="badge bg-danger">{{ task.issues }}</span>
                                {% else %}
                                <span class="badge bg-success">0</span>
                                {% endif %}
                            </td>
                            <td>
                                {{ task.description }}
                                {% if task.issues > 0 %}
                                <br><small class="text-danger">{{ task.issues }} issue{{ task.issues|pluralize }} require{{ task.issues|pluralize:"s," }} attention</small>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Validation Results -->
    {% if validation_results.validations or validation_results.errors or validation_results.warnings %}
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="bi bi-check-circle me-2"></i>Validation Results</h5>
                </div>
                <div class="card-body">
                    {% for validation in validation_results.validations %}
                    <div class="mb-2">
                        <i class="bi bi-check-circle text-success me-2"></i>
                        <strong>{{ validation.type|title_replace }}:</strong> {{ validation.message }}
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="bi bi-exclamation-triangle me-2"></i>Issues & Warnings</h5>
                </div>
                <div class="card-body">
                    {% for error in validation_results.errors %}
                    <div class="mb-2">
                        <i class="bi bi-x-circle text-danger me-2"></i>
                        <strong>ERROR:</strong> {{ error.message }}
                    </div>
                    {% endfor %}
                    
                    {% for warning in validation_results.warnings %}
                    <div class="mb-2">
                        <i class="bi bi-exclamation-triangle text-warning me-2"></i>
                        <strong>WARNING:</strong> {{ warning.message }}
                    </div>
                    {% endfor %}
                    
                    {% if not validation_results.errors and not validation_results.warnings %}
                    <p class="text-success mb-0">
                        <i class="bi bi-check-circle me-2"></i>No issues or warnings found
                    </p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Account Balances Summary -->
    {% if account_balances %}
    <div class="card">
        <div class="card-header bg-light d-flex justify-content-between align-items-center">
            <h5 class="mb-0"><i class="bi bi-table me-2"></i>Account Balances Summary</h5>
            <span class="badge bg-primary">{{ account_balances|length }} accounts</span>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-striped table-hover mb-0">
                    <thead class="table-dark">
                        <tr>
                            <th>Account Code</th>
                            <th>Account Name</th>
                            <th class="text-center">Type</th>
                            <th class="text-end">Balance</th>
                            <th class="text-end">Total Debits</th>
                            <th class="text-end">Total Credits</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for account in account_balances %}
                        <tr>
                            <td>
                                <strong>{{ account.account_code }}</strong>
                            </td>
                            <td>{{ account.account_name }}</td>
                            <td class="text-center">
                                <span class="badge {% if account.account_type == 'ASSET' %}bg-primary{% elif account.account_type == 'LIABILITY' %}bg-danger{% elif account.account_type == 'EQUITY' %}bg-success{% elif account.account_type == 'REVENUE' %}bg-info{% else %}bg-warning{% endif %}">
                                    {{ account.account_type }}
                                </span>
                            </td>
                            <td class="text-end">
                                <span class="fw-bold {% if account.balance >= 0 %}text-success{% else %}text-danger{% endif %}">
                                    {{ account.balance|currency }}
                                </span>
                            </td>
                            <td class="text-end">
                                <span class="text-primary">{{ account.total_debits|currency }}</span>
                            </td>
                            <td class="text-end">
                                <span class="text-success">{{ account.total_credits|currency }}</span>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Closing Instructions -->
    <div class="card mt-4">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="bi bi-info-circle me-2"></i>Period Closing Instructions</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h6>Before Closing</h6>
                    <ul class="list-unstyled">
                        <li><i class="bi bi-arrow-right text-primary me-2"></i>Ensure all transactions are recorded and posted</li>
                        <li><i class="bi bi-arrow-right text-primary me-2"></i>Complete all bank reconciliations</li>
                        <li><i class="bi bi-arrow-right text-primary me-2"></i>Review and resolve any unbalanced entries</li>
                        <li><i class="bi bi-arrow-right text-primary me-2"></i>Verify all account balances are reasonable</li>
                        <li><i class="bi bi-arrow-right text-primary me-2"></i>Generate and review trial balance</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6>After Closing</h6>
                    <ul class="list-unstyled">
                        <li><i class="bi bi-arrow-right text-success me-2"></i>Generate final financial statements</li>
                        <li><i class="bi bi-arrow-right text-success me-2"></i>Archive all supporting documentation</li>
                        <li><i class="bi bi-arrow-right text-success me-2"></i>Backup accounting data</li>
                        <li><i class="bi bi-arrow-right text-success me-2"></i>Prepare for next period opening</li>
                        <li><i class="bi bi-arrow-right text-success me-2"></i>Review closing procedures for improvements</li>
                    </ul>
                </div>
            </div>
            
            <hr>
            
            <div class="alert {% if validation_results.overall_status == 'READY' %}alert-success{% elif validation_results.overall_status == 'WARNINGS' %}alert-warning{% else %}alert-danger{% endif %}">
                <h6 class="alert-heading">
                    {% if validation_results.overall_status == 'READY' %}
                    <i class="bi bi-check-circle me-2"></i>Ready to Close
                    {% elif validation_results.overall_status == 'WARNINGS' %}
                    <i class="bi bi-exclamation-triangle me-2"></i>Review Required
                    {% else %}
                    <i class="bi bi-x-circle me-2"></i>Action Required
                    {% endif %}
                </h6>
                <p class="mb-0">
                    {% if validation_results.overall_status == 'READY' %}
                    All validations have passed successfully. The period is ready for closing. Proceed with generating final reports and archiving documentation.
                    {% elif validation_results.overall_status == 'WARNINGS' %}
                    Some warnings were found during validation. Review the issues above and determine if they need to be addressed before closing the period.
                    {% else %}
                    Critical errors were found that must be resolved before the period can be closed. Address all errors in the validation results above.
                    {% endif %}
                </p>
            </div>
        </div>
    </div>

    <!-- No Data Message -->
    {% if not closing_checklist %}
    <div class="card">
        <div class="card-body text-center py-5">
            <i class="bi bi-calendar-check display-1 text-muted mb-3"></i>
            <h4 class="text-muted">No Period Data Available</h4>
            <p class="text-muted">Unable to generate period closing report for the selected criteria.</p>
            <div class="mt-3">
                <a href="{% url 'reporting:period_closing_report' %}" class="btn btn-outline-primary me-2">
                    <i class="bi bi-arrow-clockwise me-2"></i>Reset Filters
                </a>
            </div>
        </div>
    </div>
    {% endif %}

</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Animate progress bars
    $('.progress-bar').each(function() {
        var $this = $(this);
        var width = $this.css('width');
        $this.css('width', '0%').animate({width: width}, 1500);
    });
    
    // Highlight pending tasks with issues
    $('.table-warning').hover(
        function() {
            $(this).addClass('table-active');
        },
        function() {
            $(this).removeClass('table-active');
        }
    );
    
    // Add click handlers for checklist rows
    $('tbody tr').on('click', function() {
        $(this).toggleClass('table-info');
    });
});
</script>
{% endblock %}

