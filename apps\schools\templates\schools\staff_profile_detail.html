{# D:\school_fees_saas_v2\templates\schools\staff_profile_detail.html #}
{% extends "tenant_base.html" %}
{% load static humanize %} {# humanize for date formatting etc. #}

{% block tenant_page_title %}{{ view_title|default:"Staff Profile" }}{% endblock %}

{% block tenant_specific_content %}
<div class="container">
    <div class="pagetitle mb-3">
        <h1>{{ view_title }}</h1>
        <nav>
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'schools:dashboard' %}">Dashboard</a></li>
                {% if not is_own_profile %} {# If admin is viewing another staff #}
                    <li class="breadcrumb-item"><a href="{% url 'schools:staff_list' %}">Staff Management</a></li>
                {% endif %}
                <li class="breadcrumb-item active">Profile</li>
            </ol>
        </nav>
    </div>

    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">Staff Member Details</h5>
        </div>
        <div class="card-body mt-3">
            <div class="row">
                <div class="col-md-3 text-center">
                    {% if staff_member.hr_profile and staff_member.hr_profile.profile_picture %}
                        <img src="{{ staff_member.hr_profile.profile_picture.url }}" alt="{{ staff_member.get_full_name }}'s profile picture" class="img-fluid rounded-circle mb-3" style="width: 150px; height: 150px; object-fit: cover;">
                    {% else %}
                        <i class="bi bi-person-circle" style="font-size: 100px; color: #6c757d;"></i>
                    {% endif %}
                </div>
                <div class="col-md-9">
                    <dl class="row">
                        <dt class="col-sm-4">Full Name:</dt>
                        <dd class="col-sm-8">{{ staff_member.get_full_name }}</dd>

                        <dt class="col-sm-4">Email:</dt>
                        <dd class="col-sm-8">{{ staff_member.email }}</dd>

                        <dt class="col-sm-4">Employee ID:</dt>
                        <dd class="col-sm-8">
                            {% if staff_member.hr_profile %}
                                {{ staff_member.hr_profile.employee_id|default:"N/A" }}
                            {% else %}
                                N/A
                            {% endif %}
                        </dd>

                        <dt class="col-sm-4">Designation:</dt>
                        <dd class="col-sm-8">
                            {% if staff_member.hr_profile %}
                                {{ staff_member.hr_profile.designation|default:"N/A" }}
                            {% else %}
                                N/A
                            {% endif %}
                        </dd>

                        <dt class="col-sm-4">Date Hired:</dt>
                        <dd class="col-sm-8">
                            {% if staff_member.hr_profile %}
                                {{ staff_member.hr_profile.date_hired|date:"d M Y"|default:"N/A" }}
                            {% else %}
                                N/A
                            {% endif %}
                        </dd>
                        
                        <dt class="col-sm-4">Phone Number:</dt>
                        <dd class="col-sm-8">
                            {% if staff_member.hr_profile %}
                                {{ staff_member.hr_profile.phone_number|default:"N/A" }}
                            {% else %}
                                N/A
                            {% endif %}
                        </dd>

                        <dt class="col-sm-4">Address:</dt>
                        <dd class="col-sm-8">
                            {% if staff_member.hr_profile %}
                                {{ staff_member.hr_profile.address|linebreaksbr|default:"N/A" }}
                            {% else %}
                                N/A
                            {% endif %}
                        </dd>

                        <dt class="col-sm-4">Status:</dt>
                        <dd class="col-sm-8">
                            {% if staff_member.is_active %}
                                <span class="badge bg-success status-badge">Active</span>
                            {% else %}
                                <span class="badge bg-danger status-badge">Inactive</span>
                            {% endif %}
                            {% if staff_member.is_superuser %}
                                <span class="badge bg-primary status-badge">Superuser (Tenant)</span>
                            {% endif %}
                        </dd>
                        
                        <dt class="col-sm-4">Roles/Groups:</dt>
                        <dd class="col-sm-8">
                            {% for group in staff_member.groups.all %}
                                <span class="badge bg-secondary me-1">{{ group.name }}</span>
                            {% empty %}
                                N/A
                            {% endfor %}
                        </dd>
                    </dl>
                </div>
            </div>

            {% if is_own_profile or request.user.is_superuser or perms.schools.change_staffuser %}
            <hr>
            <div class="mt-3">
                <a href="{% url 'schools:staff_update' pk=staff_member.pk %}" class="btn btn-primary">
                    <i class="bi bi-pencil-square me-1"></i> Edit Profile
                </a>
                {# Add other actions like "Change Password" (for own profile) or "Deactivate" (for admins) #}
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock tenant_specific_content %}


