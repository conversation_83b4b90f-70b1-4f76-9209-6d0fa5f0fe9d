#!/usr/bin/env python
"""
Proper Schema Analysis
Actually analyze what's working in alpha vs broken in other schemas

Usage: python proper_schema_analysis.py
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from django.db import connection
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def get_complete_schema_dump(schema_name):
    """Get complete schema structure including all tables, columns, indexes, constraints"""
    
    logger.info(f"Analyzing {schema_name} schema completely...")
    
    schema_info = {
        'tables': {},
        'indexes': {},
        'constraints': {},
        'sequences': {},
        'data_counts': {}
    }
    
    try:
        with connection.cursor() as cursor:
            cursor.execute(f'SET search_path TO "{schema_name}"')
            
            # 1. Get all tables with complete column info
            cursor.execute("""
                SELECT 
                    t.table_name,
                    c.column_name,
                    c.data_type,
                    c.character_maximum_length,
                    c.numeric_precision,
                    c.numeric_scale,
                    c.is_nullable,
                    c.column_default,
                    c.ordinal_position
                FROM information_schema.tables t
                LEFT JOIN information_schema.columns c ON t.table_name = c.table_name
                WHERE t.table_schema = %s 
                AND t.table_type = 'BASE TABLE'
                ORDER BY t.table_name, c.ordinal_position
            """, [schema_name])
            
            for row in cursor.fetchall():
                table_name, col_name, data_type, max_len, num_prec, num_scale, nullable, default, pos = row
                
                if table_name not in schema_info['tables']:
                    schema_info['tables'][table_name] = []
                
                if col_name:  # Skip if no columns (shouldn't happen)
                    schema_info['tables'][table_name].append({
                        'name': col_name,
                        'type': data_type,
                        'max_length': max_len,
                        'precision': num_prec,
                        'scale': num_scale,
                        'nullable': nullable,
                        'default': default,
                        'position': pos
                    })
            
            # 2. Get data counts for important tables
            important_tables = [
                'schools_academicyear', 'schools_term', 'schools_schoolclass', 'schools_section',
                'accounting_accounttype', 'accounting_account', 'fees_feehead', 'payments_paymentmethod',
                'students_student', 'schools_staffuser', 'fees_invoice', 'payments_payment'
            ]
            
            for table in important_tables:
                if table in schema_info['tables']:
                    try:
                        cursor.execute(f"SELECT COUNT(*) FROM {table}")
                        count = cursor.fetchone()[0]
                        schema_info['data_counts'][table] = count
                    except Exception as e:
                        schema_info['data_counts'][table] = f"ERROR: {e}"
            
            # 3. Get indexes
            cursor.execute("""
                SELECT 
                    schemaname, tablename, indexname, indexdef
                FROM pg_indexes 
                WHERE schemaname = %s
                ORDER BY tablename, indexname
            """, [schema_name])
            
            for row in cursor.fetchall():
                schema, table, index_name, index_def = row
                if table not in schema_info['indexes']:
                    schema_info['indexes'][table] = []
                schema_info['indexes'][table].append({
                    'name': index_name,
                    'definition': index_def
                })
            
            # 4. Get constraints
            cursor.execute("""
                SELECT 
                    tc.table_name,
                    tc.constraint_name,
                    tc.constraint_type,
                    kcu.column_name,
                    ccu.table_name AS foreign_table_name,
                    ccu.column_name AS foreign_column_name
                FROM information_schema.table_constraints tc
                LEFT JOIN information_schema.key_column_usage kcu 
                    ON tc.constraint_name = kcu.constraint_name
                LEFT JOIN information_schema.constraint_column_usage ccu 
                    ON ccu.constraint_name = tc.constraint_name
                WHERE tc.table_schema = %s
                ORDER BY tc.table_name, tc.constraint_name
            """, [schema_name])
            
            for row in cursor.fetchall():
                table, constraint_name, constraint_type, column, foreign_table, foreign_column = row
                if table not in schema_info['constraints']:
                    schema_info['constraints'][table] = []
                schema_info['constraints'][table].append({
                    'name': constraint_name,
                    'type': constraint_type,
                    'column': column,
                    'foreign_table': foreign_table,
                    'foreign_column': foreign_column
                })
            
            logger.info(f"✅ Analyzed {schema_name}: {len(schema_info['tables'])} tables")
            
    except Exception as e:
        logger.error(f"❌ Failed to analyze {schema_name}: {e}")
    
    return schema_info

def compare_schemas_properly():
    """Properly compare alpha with other schemas"""
    
    logger.info("=== PROPER SCHEMA COMPARISON ===")
    
    # Get complete schema info
    alpha_info = get_complete_schema_dump('alpha')
    mandiva_info = get_complete_schema_dump('mandiva')
    
    logger.info("\n=== DETAILED COMPARISON: ALPHA vs MANDIVA ===")
    
    # Compare tables
    alpha_tables = set(alpha_info['tables'].keys())
    mandiva_tables = set(mandiva_info['tables'].keys())
    
    missing_in_mandiva = alpha_tables - mandiva_tables
    extra_in_mandiva = mandiva_tables - alpha_tables
    
    logger.info(f"\n📊 TABLE COMPARISON:")
    logger.info(f"Alpha has {len(alpha_tables)} tables")
    logger.info(f"Mandiva has {len(mandiva_tables)} tables")
    logger.info(f"Missing in mandiva: {len(missing_in_mandiva)}")
    logger.info(f"Extra in mandiva: {len(extra_in_mandiva)}")
    
    if missing_in_mandiva:
        logger.error(f"❌ MISSING TABLES IN MANDIVA: {sorted(missing_in_mandiva)}")
    
    if extra_in_mandiva:
        logger.info(f"ℹ️  EXTRA TABLES IN MANDIVA: {sorted(extra_in_mandiva)}")
    
    # Compare columns for common tables
    common_tables = alpha_tables & mandiva_tables
    logger.info(f"\n📊 COLUMN COMPARISON FOR {len(common_tables)} COMMON TABLES:")
    
    column_issues = {}
    
    for table in sorted(common_tables):
        alpha_cols = {col['name']: col for col in alpha_info['tables'][table]}
        mandiva_cols = {col['name']: col for col in mandiva_info['tables'][table]}
        
        alpha_col_names = set(alpha_cols.keys())
        mandiva_col_names = set(mandiva_cols.keys())
        
        missing_cols = alpha_col_names - mandiva_col_names
        extra_cols = mandiva_col_names - alpha_col_names
        
        if missing_cols or extra_cols:
            column_issues[table] = {
                'missing': missing_cols,
                'extra': extra_cols
            }
            
            logger.error(f"❌ {table}:")
            if missing_cols:
                logger.error(f"   Missing columns: {sorted(missing_cols)}")
            if extra_cols:
                logger.info(f"   Extra columns: {sorted(extra_cols)}")
    
    # Compare data counts
    logger.info(f"\n📊 DATA COUNT COMPARISON:")
    
    for table in sorted(alpha_info['data_counts'].keys()):
        alpha_count = alpha_info['data_counts'][table]
        mandiva_count = mandiva_info['data_counts'].get(table, 'TABLE_MISSING')
        
        if isinstance(alpha_count, int) and isinstance(mandiva_count, int):
            if alpha_count > 0 and mandiva_count == 0:
                logger.error(f"❌ {table}: Alpha={alpha_count}, Mandiva={mandiva_count} (EMPTY)")
            elif alpha_count != mandiva_count:
                logger.warning(f"⚠️  {table}: Alpha={alpha_count}, Mandiva={mandiva_count}")
            else:
                logger.info(f"✅ {table}: Both have {alpha_count} records")
        else:
            logger.error(f"❌ {table}: Alpha={alpha_count}, Mandiva={mandiva_count}")
    
    return {
        'missing_tables': missing_in_mandiva,
        'column_issues': column_issues,
        'alpha_info': alpha_info,
        'mandiva_info': mandiva_info
    }

def check_specific_errors():
    """Check specific error-prone areas"""
    
    logger.info("\n=== CHECKING SPECIFIC ERROR AREAS ===")
    
    schemas = ['alpha', 'mandiva']
    
    # Check financial reports tables
    financial_tables = [
        'accounting_account', 'accounting_accounttype', 'accounting_journalentry',
        'fees_invoice', 'fees_invoicedetail', 'payments_payment', 'payments_paymentallocation'
    ]
    
    for schema in schemas:
        logger.info(f"\n{schema.upper()} - Financial Tables:")
        try:
            with connection.cursor() as cursor:
                cursor.execute(f'SET search_path TO "{schema}"')
                
                for table in financial_tables:
                    try:
                        cursor.execute(f"SELECT COUNT(*) FROM {table}")
                        count = cursor.fetchone()[0]
                        
                        # Get sample data
                        cursor.execute(f"SELECT * FROM {table} LIMIT 1")
                        sample = cursor.fetchone()
                        
                        logger.info(f"  ✅ {table}: {count} records")
                        
                    except Exception as e:
                        logger.error(f"  ❌ {table}: {e}")
                        
        except Exception as e:
            logger.error(f"Failed to check {schema}: {e}")

def main():
    """Main function"""
    logger.info("=== PROPER SCHEMA ANALYSIS ===")
    
    try:
        # Do proper comparison
        comparison = compare_schemas_properly()
        
        # Check specific error areas
        check_specific_errors()
        
        logger.info("\n🎯 SUMMARY OF REAL ISSUES:")
        logger.info(f"Missing tables: {len(comparison['missing_tables'])}")
        logger.info(f"Tables with column issues: {len(comparison['column_issues'])}")
        
        if comparison['missing_tables']:
            logger.error("❌ CRITICAL: Missing tables will cause page errors")
        
        if comparison['column_issues']:
            logger.error("❌ CRITICAL: Column mismatches will cause form/report errors")
        
        logger.info("\nNow I understand the real issues. Let me fix them properly.")
        
        return comparison
        
    except Exception as e:
        logger.error(f"Analysis failed: {e}")
        return None

if __name__ == "__main__":
    result = main()
    sys.exit(0 if result else 1)
