# Generated by Django 5.1.9 on 2025-07-08 12:35

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('hr', '0012_add_missing_leavetype_fields_production'),
    ]

    operations = [
        migrations.AddField(
            model_name='employeeprofile',
            name='date_hired',
            field=models.DateField(blank=True, null=True, verbose_name='date hired'),
        ),
        migrations.AddField(
            model_name='employeeprofile',
            name='department',
            field=models.CharField(blank=True, max_length=100, verbose_name='department'),
        ),
        migrations.AddField(
            model_name='employeeprofile',
            name='designation',
            field=models.CharField(blank=True, max_length=100, verbose_name='designation'),
        ),
        migrations.AddField(
            model_name='employeeprofile',
            name='employee_id',
            field=models.CharField(blank=True, help_text='Unique school-assigned employee ID.', max_length=50, null=True, unique=True, verbose_name='employee ID'),
        ),
        migrations.AddField(
            model_name='employeeprofile',
            name='phone_number_primary',
            field=models.CharField(blank=True, max_length=30, verbose_name='primary phone'),
        ),
        migrations.AlterField(
            model_name='employeeprofile',
            name='notes',
            field=models.TextField(blank=True, verbose_name='internal HR notes'),
        ),
        migrations.AlterField(
            model_name='employeeprofile',
            name='phone_number_alternate',
            field=models.CharField(blank=True, max_length=30, verbose_name='alternate phone'),
        ),
    ]
