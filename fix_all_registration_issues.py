#!/usr/bin/env python
"""
Comprehensive Registration Issues Fix Script
Fixes all critical issues preventing tenant registration

This script will:
1. Fix migration dependency chains
2. Ensure announcements migrations are applied
3. Clean up migration state
4. Test tenant creation process
5. Provide detailed logging and verification

Usage: python fix_all_registration_issues.py
"""

import os
import sys
import django
import logging
from django.conf import settings

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from django.db import connection, transaction
from django.core.management import call_command
from django_tenants.utils import schema_context
from django.core.management.base import CommandError

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('registration_fix.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class RegistrationFixer:
    def __init__(self):
        self.errors = []
        self.warnings = []
        self.success_steps = []
    
    def log_error(self, message):
        self.errors.append(message)
        logger.error(f"❌ {message}")
    
    def log_warning(self, message):
        self.warnings.append(message)
        logger.warning(f"⚠️ {message}")
    
    def log_success(self, message):
        self.success_steps.append(message)
        logger.info(f"✅ {message}")
    
    def check_table_exists(self, table_name, schema='public'):
        """Check if a table exists"""
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_schema = %s AND table_name = %s
                );
            """, [schema, table_name])
            return cursor.fetchone()[0]
    
    def get_applied_migrations(self, app_name):
        """Get list of applied migrations for an app"""
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT name FROM django_migrations 
                WHERE app = %s ORDER BY applied;
            """, [app_name])
            return [row[0] for row in cursor.fetchall()]
    
    def fix_migration_dependencies(self):
        """Fix migration dependency chain issues"""
        logger.info("=== FIXING MIGRATION DEPENDENCIES ===")
        
        try:
            # Remove any conflicting migration records
            with connection.cursor() as cursor:
                # Remove old HR migration records that might conflict
                cursor.execute("""
                    DELETE FROM django_migrations 
                    WHERE app = 'hr' AND name = '0012_add_missing_leavetype_fields'
                """)
                
                # Check if our production migration is applied
                cursor.execute("""
                    SELECT COUNT(*) FROM django_migrations 
                    WHERE app = 'hr' AND name = '0012_add_missing_leavetype_fields_production'
                """)
                production_applied = cursor.fetchone()[0] > 0
                
                if not production_applied:
                    self.log_success("Cleaned up conflicting HR migration records")
                else:
                    self.log_success("Production HR migration already applied")
            
            return True
            
        except Exception as e:
            self.log_error(f"Failed to fix migration dependencies: {str(e)}")
            return False
    
    def apply_migrations_safely(self):
        """Apply migrations in the correct order"""
        logger.info("=== APPLYING MIGRATIONS SAFELY ===")
        
        try:
            # Apply HR migrations first
            logger.info("Applying HR migrations...")
            call_command('migrate', 'hr', verbosity=1)
            self.log_success("HR migrations applied")
            
            # Apply announcements migrations
            logger.info("Applying announcements migrations...")
            call_command('migrate', 'announcements', verbosity=1)
            self.log_success("Announcements migrations applied")
            
            # Apply all other migrations
            logger.info("Applying all remaining migrations...")
            call_command('migrate', verbosity=1)
            self.log_success("All migrations applied")
            
            return True
            
        except Exception as e:
            self.log_error(f"Failed to apply migrations: {str(e)}")
            return False
    
    def fix_tenant_migrations(self):
        """Fix migrations in existing tenant schemas"""
        logger.info("=== FIXING TENANT MIGRATIONS ===")
        
        try:
            from apps.tenants.models import School
            tenants = School.objects.all()
            
            for tenant in tenants:
                logger.info(f"Fixing migrations for tenant: {tenant.schema_name}")
                
                try:
                    # Apply HR migrations to tenant
                    call_command('tenant_command', 'migrate', 'hr', 
                               schema=tenant.schema_name, verbosity=1)
                    
                    # Apply announcements migrations to tenant
                    call_command('tenant_command', 'migrate', 'announcements', 
                               schema=tenant.schema_name, verbosity=1)
                    
                    # Apply all migrations to tenant
                    call_command('tenant_command', 'migrate', 
                               schema=tenant.schema_name, verbosity=1)
                    
                    self.log_success(f"Migrations fixed for tenant {tenant.schema_name}")
                    
                except Exception as e:
                    self.log_error(f"Failed to fix migrations for {tenant.schema_name}: {str(e)}")
            
            return True
            
        except Exception as e:
            self.log_error(f"Failed to fix tenant migrations: {str(e)}")
            return False
    
    def verify_announcements_tables(self):
        """Verify announcements tables exist in all schemas"""
        logger.info("=== VERIFYING ANNOUNCEMENTS TABLES ===")
        
        try:
            # Check public schema
            if not self.check_table_exists('announcements_announcement'):
                self.log_error("Announcements table missing in public schema")
                return False
            
            self.log_success("Announcements table exists in public schema")
            
            # Check tenant schemas
            from apps.tenants.models import School
            tenants = School.objects.all()
            
            for tenant in tenants:
                if not self.check_table_exists('announcements_announcement', tenant.schema_name):
                    self.log_error(f"Announcements table missing in {tenant.schema_name}")
                    return False
                else:
                    self.log_success(f"Announcements table exists in {tenant.schema_name}")
            
            return True
            
        except Exception as e:
            self.log_error(f"Failed to verify announcements tables: {str(e)}")
            return False
    
    def test_subscription_creation(self):
        """Test that subscription creation works"""
        logger.info("=== TESTING SUBSCRIPTION CREATION ===")
        
        try:
            # Import models to test they work
            from apps.subscriptions.models import Subscription, SubscriptionPlan
            from apps.tenants.models import School
            
            # Check if trial plan exists
            trial_plans = SubscriptionPlan.objects.filter(name__icontains='trial')
            if not trial_plans.exists():
                self.log_warning("No trial plan found - subscription creation may fail")
            else:
                self.log_success("Trial plan exists")
            
            # Test subscription model fields
            subscription_fields = [f.name for f in Subscription._meta.fields]
            required_fields = ['school', 'plan', 'status', 'billing_cycle', 'price_at_subscription']
            
            missing_fields = [f for f in required_fields if f not in subscription_fields]
            if missing_fields:
                self.log_error(f"Subscription model missing fields: {missing_fields}")
                return False
            
            self.log_success("Subscription model has all required fields")
            return True
            
        except Exception as e:
            self.log_error(f"Subscription creation test failed: {str(e)}")
            return False
    
    def run_comprehensive_fix(self):
        """Run the complete fix process"""
        logger.info("=== STARTING COMPREHENSIVE REGISTRATION FIX ===")
        
        steps = [
            ("Fix Migration Dependencies", self.fix_migration_dependencies),
            ("Apply Migrations Safely", self.apply_migrations_safely),
            ("Fix Tenant Migrations", self.fix_tenant_migrations),
            ("Verify Announcements Tables", self.verify_announcements_tables),
            ("Test Subscription Creation", self.test_subscription_creation),
        ]
        
        passed = 0
        failed = 0
        
        for step_name, step_func in steps:
            logger.info(f"\n--- {step_name} ---")
            try:
                if step_func():
                    logger.info(f"✅ {step_name} PASSED")
                    passed += 1
                else:
                    logger.error(f"❌ {step_name} FAILED")
                    failed += 1
            except Exception as e:
                logger.error(f"❌ {step_name} ERROR: {e}")
                failed += 1
        
        # Summary
        logger.info("\n=== COMPREHENSIVE FIX SUMMARY ===")
        logger.info(f"✅ Passed: {passed}")
        logger.info(f"❌ Failed: {failed}")
        logger.info(f"⚠️ Warnings: {len(self.warnings)}")
        
        if failed == 0:
            logger.info("🎉 ALL FIXES COMPLETED SUCCESSFULLY!")
            logger.info("You can now test tenant registration.")
            return True
        else:
            logger.error("💥 SOME FIXES FAILED. Review errors above.")
            return False

if __name__ == "__main__":
    fixer = RegistrationFixer()
    success = fixer.run_comprehensive_fix()
    sys.exit(0 if success else 1)
