#!/usr/bin/env python
"""
Script to delete unwanted tenant schemas and keep only alpha, mandiva, zenith, and fox
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from django.db import connection
from apps.tenants.models import School

def delete_school_records():
    """Delete School records for schemas that no longer exist"""

    # Schemas to keep
    keep_schemas = ['alpha', 'mandiva', 'zenith', 'fox']

    # Get all schools
    schools = School.objects.all()
    print("Current schools:")
    for school in schools:
        print(f"  {school.schema_name}: {school.name} (Active: {school.is_active})")

    # Find schools to delete
    schools_to_delete = schools.exclude(schema_name__in=keep_schemas)
    print(f"\nSchools to delete ({schools_to_delete.count()}):")
    for school in schools_to_delete:
        print(f"  {school.schema_name}: {school.name}")

    if schools_to_delete.count() == 0:
        print("No schools to delete.")
        return

    # Confirm deletion
    confirm = input(f"\nAre you sure you want to delete {schools_to_delete.count()} school records? (yes/no): ")
    if confirm.lower() != 'yes':
        print("Deletion cancelled.")
        return

    print("\nStarting deletion process...")

    # Delete School records and their dependencies using raw SQL
    schemas_to_delete = [school.schema_name for school in schools_to_delete]

    with connection.cursor() as cursor:
        for schema_name in schemas_to_delete:
            try:
                # Get the school ID first
                cursor.execute("SELECT id FROM tenants_school WHERE schema_name = %s", [schema_name])
                result = cursor.fetchone()
                if result:
                    school_id = result[0]

                    # Delete subscription records first
                    cursor.execute("DELETE FROM subscriptions_subscription WHERE school_id = %s", [school_id])
                    print(f"  ✓ Deleted subscription records for school ID: {school_id}")

                    # Delete domain records
                    cursor.execute("DELETE FROM tenants_domain WHERE tenant_id = %s", [school_id])
                    print(f"  ✓ Deleted domain records for school ID: {school_id}")

                    # Delete the school record
                    cursor.execute("DELETE FROM tenants_school WHERE id = %s", [school_id])
                    print(f"  ✓ Deleted School record for schema: {schema_name}")
                else:
                    print(f"  ! School record for '{schema_name}' not found")

            except Exception as e:
                print(f"  ✗ Error deleting School record for '{schema_name}': {e}")

    print("\nDeletion process completed!")

    # Show remaining schools
    remaining_schools = School.objects.all()
    print(f"\nRemaining schools ({remaining_schools.count()}):")
    for school in remaining_schools:
        print(f"  {school.schema_name}: {school.name}")

if __name__ == "__main__":
    delete_school_records()
