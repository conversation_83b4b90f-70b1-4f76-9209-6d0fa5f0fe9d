# Generated migration for missing accounting fields
from django.db import migrations, models

class Migration(migrations.Migration):
    dependencies = [
        ('accounting', '0001_initial'),  # Adjust based on your last migration
    ]

    operations = [
        migrations.AddField(
            model_name='account',
            name='is_control_account',
            field=models.<PERSON>olean<PERSON>ield(default=False),
        ),
        migrations.AddField(
            model_name='account',
            name='can_be_used_in_je',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='account',
            name='bank_name',
            field=models.CharField(max_length=255, blank=True),
        ),
        migrations.AddField(
            model_name='account',
            name='bank_account_number',
            field=models.Char<PERSON>ield(max_length=100, blank=True),
        ),
        migrations.AddField(
            model_name='account',
            name='bank_routing_number',
            field=models.CharField(max_length=50, blank=True),
        ),
        migrations.AddField(
            model_name='account',
            name='bank_swift_code',
            field=models.Char<PERSON>ield(max_length=20, blank=True),
        ),
        migrations.AddField(
            model_name='account',
            name='bank_branch',
            field=models.CharField(max_length=255, blank=True),
        ),
        migrations.AddField(
            model_name='account',
            name='description',
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name='account',
            name='parent_id',
            field=models.BigIntegerField(null=True, blank=True),
        ),
        migrations.AddField(
            model_name='accounttype',
            name='classification',
            field=models.CharField(max_length=50, blank=True),
        ),
    ]
