# D:\school_fees_saas_v2\apps\payments\management\commands\test_payment.py

from decimal import Decimal
from django.core.management.base import BaseCommand
from django_tenants.utils import schema_context
from django.utils import timezone
from apps.tenants.models import School
from apps.students.models import ParentUser
from apps.fees.models import Invoice
from apps.payments.services import record_parent_payment


class Command(BaseCommand):
    help = 'Test payment functionality to debug issues'

    def add_arguments(self, parser):
        parser.add_argument('schema_name', type=str, help='The schema name of the tenant')
        parser.add_argument('parent_email', type=str, help='The email of the parent user')

    def handle(self, *args, **options):
        schema_name = options['schema_name']
        parent_email = options['parent_email']
        
        try:
            tenant = School.objects.get(schema_name=schema_name)
        except School.DoesNotExist:
            self.stdout.write(self.style.ERROR(f"Tenant '{schema_name}' not found"))
            return
            
        with schema_context(tenant.schema_name):
            self.stdout.write(f"Testing payment for tenant: {tenant.name}")
            
            # Get parent user
            try:
                parent = ParentUser.objects.get(email=parent_email)
                self.stdout.write(f"Found parent: {parent.get_full_name()}")
            except ParentUser.DoesNotExist:
                self.stdout.write(self.style.ERROR(f"Parent '{parent_email}' not found"))
                return
                
            # Check if parent has children
            children = parent.children.all()
            self.stdout.write(f"Parent has {children.count()} children")
            
            if not children.exists():
                self.stdout.write(self.style.WARNING("Parent has no children linked"))
                return
                
            # Get outstanding invoices
            outstanding_statuses = ['SENT', 'PARTIALLY_PAID', 'OVERDUE']
            children_pks = children.values_list('pk', flat=True)
            
            invoices = Invoice.objects.filter(
                student_id__in=children_pks,
                status__in=outstanding_statuses
            )
            
            self.stdout.write(f"Found {invoices.count()} outstanding invoices")
            
            if not invoices.exists():
                self.stdout.write(self.style.WARNING("No outstanding invoices found"))
                return
                
            # Test payment with first invoice
            test_invoice = invoices.first()
            test_amount = min(Decimal('100.00'), test_invoice.balance_due)
            
            self.stdout.write(f"Testing payment of {test_amount} for invoice {test_invoice.invoice_number}")
            
            try:
                success, message, payment = record_parent_payment(
                    parent_user=parent,
                    invoice_pks_to_pay=[test_invoice.pk],
                    amount_paid_by_parent=test_amount,
                    payment_method_code="ONLINE_MOCK",
                    transaction_reference=f"TEST_{timezone.now().strftime('%Y%m%d%H%M%S')}"
                )
                
                if success:
                    self.stdout.write(self.style.SUCCESS(f"Payment successful: {message}"))
                    self.stdout.write(f"Payment ID: {payment.pk if payment else 'None'}")
                else:
                    self.stdout.write(self.style.ERROR(f"Payment failed: {message}"))
                    
            except Exception as e:
                self.stdout.write(self.style.ERROR(f"Exception during payment: {e}"))
                import traceback
                self.stdout.write(traceback.format_exc())
