{% extends "./_pdf_base.html" %}
{% load humanize %}

{% block pdf_title %}Outstanding Fees Report - {{ school_profile.name|default:"School" }}{% endblock %}

{% block report_period_header %}
    {% if filter_form_data.start_date and filter_form_data.end_date %}
        <p class="mb-0">Period: {{ filter_form_data.start_date|date:"d M Y" }} to {{ filter_form_data.end_date|date:"d M Y" }}</p>
    {% endif %}
    {% if filter_form_data.school_class %}
        <p class="mb-0">Class: {{ filter_form_data.school_class.name }}</p>
    {% endif %}
{% endblock %}

{% block pdf_content %}
    <table>
        <thead>
            <tr>
                <th>Admission No.</th>
                <th>Student Name</th>
                <th>Class</th>
                <th>Section</th>
                <th>Parent Name</th>
                <th>Parent Phone</th>
                <th class="text-end">Outstanding Balance</th>
            </tr>
        </thead>
        <tbody>
            {% for student in report_data %} {# report_data from view context #}
            <tr>
                <td>{{ student.admission_number }}</td>
                <td>{{ student.full_name }}</td>
                <td>{{ student.current_class.name|default:"N/A" }}</td>
                <td>{{ student.current_section.name|default:"N/A" }}</td>
                <td>{% if student.parents.first %}{{ student.parents.first.get_full_name }}{% else %}N/A{% endif %}</td>
                <td>{% if student.parents.first and student.parents.first.phone_number %}{{ student.parents.first.phone_number }}{% else %}N/A{% endif %}</td>
                <td class="text-end">{{ student.outstanding_balance|default:"0.00"|floatformat:2|intcomma }}</td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="7" class="text-center">No outstanding fees found.</td>
            </tr>
            {% endfor %}
        </tbody>
        {% if report_data %}
        <tfoot>
            <tr>
                <th colspan="5" class="text-end fw-bold">Total Outstanding:</th>
                <th class="text-end fw-bold">{{ total_outstanding_all_pages|default:"0.00"|floatformat:2|intcomma }}</th>
            </tr>
        </tfoot>
        {% endif %}
    </table>
{% endblock %}
