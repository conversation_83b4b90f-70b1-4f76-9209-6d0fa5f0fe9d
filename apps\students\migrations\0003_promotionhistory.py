# Generated by Django 5.1.9 on 2025-07-17 20:43

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('schools', '0003_alter_schoolclass_options_schoolclass_order_and_more'),
        ('students', '0002_student_emergency_contact_student_emergency_phone_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='PromotionHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('action', models.CharField(choices=[('PROMOTED', 'Promoted'), ('RETAINED', 'Retained'), ('GRADUATED', 'Graduated')], max_length=10)),
                ('promotion_date', models.DateTimeField(auto_now_add=True)),
                ('notes', models.TextField(blank=True, help_text="Optional notes, e.g., 'Promoted on academic probation'.")),
                ('from_academic_year', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='promotions_from', to='schools.academicyear')),
                ('from_class', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='promotions_from', to='schools.schoolclass')),
                ('performed_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='promotion_history', to='students.student')),
                ('to_academic_year', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='promotions_to', to='schools.academicyear')),
                ('to_class', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='promotions_to', to='schools.schoolclass')),
            ],
            options={
                'verbose_name': 'Promotion History',
                'verbose_name_plural': 'Promotion Histories',
                'ordering': ['-promotion_date', 'student__last_name'],
            },
        ),
    ]
