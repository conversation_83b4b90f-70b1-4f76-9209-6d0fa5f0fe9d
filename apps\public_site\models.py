# apps/public_site/models.py
from django.db import models
from django.core.validators import MaxValueValidator, MinValueValidator


class Testimonial(models.Model):
    REVIEW_CATEGORIES = [
        ('general', 'General Experience'),
        ('ease_of_use', 'Ease of Use'),
        ('customer_support', 'Customer Support'),
        ('features', 'Features & Functionality'),
        ('value_for_money', 'Value for Money'),
        ('implementation', 'Implementation Process'),
        ('reporting', 'Reporting & Analytics'),
        ('parent_portal', 'Parent Portal'),
        ('mobile_app', 'Mobile Experience'),
        ('integration', 'System Integration'),
    ]

    SCHOOL_TYPES = [
        ('primary', 'Primary School'),
        ('secondary', 'Secondary School'),
        ('high_school', 'High School'),
        ('college', 'College'),
        ('university', 'University'),
        ('private', 'Private School'),
        ('public', 'Public School'),
        ('international', 'International School'),
        ('boarding', 'Boarding School'),
        ('online', 'Online School'),
        ('other', 'Other'),
    ]

    SCHOOL_SIZES = [
        ('small', 'Small (1-200 students)'),
        ('medium', 'Medium (201-500 students)'),
        ('large', 'Large (501-1000 students)'),
        ('very_large', 'Very Large (1000+ students)'),
    ]

    # Basic Information
    author_name = models.CharField(max_length=100, verbose_name="Full Name")
    author_title = models.CharField(max_length=100, verbose_name="Job Title", help_text="e.g., Principal, Finance Manager")
    school_name = models.CharField(max_length=150, verbose_name="School/Organization Name")
    school_type = models.CharField(max_length=20, choices=SCHOOL_TYPES, default='other', verbose_name="School Type")
    school_size = models.CharField(max_length=20, choices=SCHOOL_SIZES, blank=True, null=True, verbose_name="School Size")
    location = models.CharField(max_length=100, blank=True, null=True, help_text="City, State/Country")

    # Contact Information (optional)
    email = models.EmailField(blank=True, null=True, help_text="Optional - for follow-up questions")
    phone = models.CharField(max_length=20, blank=True, null=True, help_text="Optional phone number")
    website = models.URLField(blank=True, null=True, help_text="School website (optional)")

    # Review Content
    review_category = models.CharField(max_length=20, choices=REVIEW_CATEGORIES, default='general', verbose_name="Review Category")
    title = models.CharField(max_length=200, verbose_name="Review Title", help_text="Brief title for your review")
    quote = models.TextField(verbose_name="Review Content", help_text="Share your detailed experience")

    # Ratings (1-5 scale)
    overall_rating = models.PositiveSmallIntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        verbose_name="Overall Rating",
        help_text="Overall satisfaction (1-5 stars)"
    )
    ease_of_use_rating = models.PositiveSmallIntegerField(
        null=True, blank=True,
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        verbose_name="Ease of Use",
        help_text="How easy is the platform to use? (1-5 stars)"
    )
    customer_support_rating = models.PositiveSmallIntegerField(
        null=True, blank=True,
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        verbose_name="Customer Support",
        help_text="Quality of customer support (1-5 stars)"
    )
    value_rating = models.PositiveSmallIntegerField(
        null=True, blank=True,
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        verbose_name="Value for Money",
        help_text="Value for money (1-5 stars)"
    )

    # Usage Information
    usage_duration = models.CharField(
        max_length=50, blank=True, null=True,
        verbose_name="How long have you been using our platform?",
        help_text="e.g., 6 months, 2 years"
    )
    previous_solution = models.CharField(
        max_length=100, blank=True, null=True,
        verbose_name="Previous Solution",
        help_text="What did you use before our platform? (optional)"
    )

    # Recommendation
    would_recommend = models.BooleanField(
        default=True,
        verbose_name="Would you recommend our platform?",
        help_text="Would you recommend our platform to other schools?"
    )

    # Admin fields
    is_approved = models.BooleanField(default=False, help_text="Approve to display on site")
    is_featured = models.BooleanField(default=False, help_text="Feature this review prominently")
    admin_notes = models.TextField(blank=True, null=True, help_text="Internal admin notes")

    # Timestamps
    submitted_on = models.DateTimeField(auto_now_add=True)
    approved_on = models.DateTimeField(blank=True, null=True)

    # Metadata
    ip_address = models.GenericIPAddressField(blank=True, null=True)
    user_agent = models.TextField(blank=True, null=True)

    def __str__(self):
        return f"Review by {self.author_name} - {self.school_name}"

    @property
    def author_title_school(self):
        """Backward compatibility property"""
        return f"{self.author_title}, {self.school_name}"

    @property
    def rating(self):
        """Backward compatibility property"""
        return self.overall_rating

    @property
    def average_rating(self):
        """Calculate average of all provided ratings"""
        ratings = [self.overall_rating]
        if self.ease_of_use_rating:
            ratings.append(self.ease_of_use_rating)
        if self.customer_support_rating:
            ratings.append(self.customer_support_rating)
        if self.value_rating:
            ratings.append(self.value_rating)
        return sum(ratings) / len(ratings) if ratings else 0

    @property
    def star_display(self):
        """Get star display for overall rating"""
        return '★' * self.overall_rating + '☆' * (5 - self.overall_rating)

    @property
    def is_recent(self):
        """Check if review is recent (within last 30 days)"""
        from django.utils import timezone
        from datetime import timedelta
        return self.submitted_on >= timezone.now() - timedelta(days=30)

    class Meta:
        ordering = ['-is_featured', '-submitted_on']
        verbose_name = "Review & Testimonial"
        verbose_name_plural = "Reviews & Testimonials"

        
        
# apps/public_site/models.py (continued)
class ContactInquiry(models.Model):
    INQUIRY_TYPES = [
        ('general', 'General Inquiry'),
        ('support', 'Technical Support'),
        ('sales', 'Sales Question'),
        ('partnership', 'Partnership Opportunity'),
        ('feedback', 'Feedback & Suggestions'),
        ('demo', 'Request Demo'),
        ('other', 'Other'),
    ]

    PRIORITY_LEVELS = [
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('urgent', 'Urgent'),
    ]

    name = models.CharField(max_length=100)
    email = models.EmailField()
    phone = models.CharField(max_length=20, blank=True, null=True, help_text="Optional phone number")
    organization = models.CharField(max_length=150, blank=True, null=True, help_text="School or organization name")
    inquiry_type = models.CharField(max_length=20, choices=INQUIRY_TYPES, default='general')
    subject = models.CharField(max_length=200)
    message = models.TextField()
    priority = models.CharField(max_length=10, choices=PRIORITY_LEVELS, default='medium')

    # Status and workflow fields
    submitted_at = models.DateTimeField(auto_now_add=True)
    is_addressed = models.BooleanField(default=False)
    addressed_at = models.DateTimeField(blank=True, null=True)
    addressed_by = models.CharField(max_length=100, blank=True, null=True, help_text="Staff member who addressed this")

    # Broadcasting fields
    is_approved_for_broadcast = models.BooleanField(default=False, help_text="Approve to display on public platform")
    broadcast_title = models.CharField(max_length=200, blank=True, null=True, help_text="Custom title for public display")
    broadcast_excerpt = models.TextField(blank=True, null=True, help_text="Short excerpt for public display")
    is_featured = models.BooleanField(default=False, help_text="Feature this inquiry prominently")

    # Response fields
    admin_response = models.TextField(blank=True, null=True, help_text="Admin response to the inquiry")
    response_sent_at = models.DateTimeField(blank=True, null=True)

    # Metadata
    ip_address = models.GenericIPAddressField(blank=True, null=True)
    user_agent = models.TextField(blank=True, null=True)

    def __str__(self):
        return f"Inquiry from {self.name} - {self.subject}"

    @property
    def is_recent(self):
        from django.utils import timezone
        from datetime import timedelta
        return self.submitted_at >= timezone.now() - timedelta(days=7)

    @property
    def response_time(self):
        if self.addressed_at and self.submitted_at:
            return self.addressed_at - self.submitted_at
        return None

    class Meta:
        ordering = ['-submitted_at']
        verbose_name_plural = "Contact Inquiries"
        

