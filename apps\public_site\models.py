# apps/public_site/models.py
from django.db import models
from django.core.validators import MaxValueValidator, MinValueValidator


class Testimonial(models.Model):
    author_name = models.CharField(max_length=100)
    author_title_school = models.CharField(max_length=150, help_text="e.g., Principal, XYZ School")
    quote = models.TextField()
    rating = models.PositiveSmallIntegerField(
        null=True, blank=True,
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        help_text="Optional rating (1-5 stars)"
    )
    is_approved = models.BooleanField(default=False, help_text="Approve to display on site")
    submitted_on = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Testimonial by {self.author_name}"
    class Meta:
        ordering = ['-submitted_on']
        verbose_name = "Testimonial" # Added
        verbose_name_plural = "Testimonials" # Added

        
        
# apps/public_site/models.py (continued)
class ContactInquiry(models.Model):
    INQUIRY_TYPES = [
        ('general', 'General Inquiry'),
        ('support', 'Technical Support'),
        ('sales', 'Sales Question'),
        ('partnership', 'Partnership Opportunity'),
        ('feedback', 'Feedback & Suggestions'),
        ('demo', 'Request Demo'),
        ('other', 'Other'),
    ]

    PRIORITY_LEVELS = [
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('urgent', 'Urgent'),
    ]

    name = models.CharField(max_length=100)
    email = models.EmailField()
    phone = models.CharField(max_length=20, blank=True, null=True, help_text="Optional phone number")
    organization = models.CharField(max_length=150, blank=True, null=True, help_text="School or organization name")
    inquiry_type = models.CharField(max_length=20, choices=INQUIRY_TYPES, default='general')
    subject = models.CharField(max_length=200)
    message = models.TextField()
    priority = models.CharField(max_length=10, choices=PRIORITY_LEVELS, default='medium')

    # Status and workflow fields
    submitted_at = models.DateTimeField(auto_now_add=True)
    is_addressed = models.BooleanField(default=False)
    addressed_at = models.DateTimeField(blank=True, null=True)
    addressed_by = models.CharField(max_length=100, blank=True, null=True, help_text="Staff member who addressed this")

    # Broadcasting fields
    is_approved_for_broadcast = models.BooleanField(default=False, help_text="Approve to display on public platform")
    broadcast_title = models.CharField(max_length=200, blank=True, null=True, help_text="Custom title for public display")
    broadcast_excerpt = models.TextField(blank=True, null=True, help_text="Short excerpt for public display")
    is_featured = models.BooleanField(default=False, help_text="Feature this inquiry prominently")

    # Response fields
    admin_response = models.TextField(blank=True, null=True, help_text="Admin response to the inquiry")
    response_sent_at = models.DateTimeField(blank=True, null=True)

    # Metadata
    ip_address = models.GenericIPAddressField(blank=True, null=True)
    user_agent = models.TextField(blank=True, null=True)

    def __str__(self):
        return f"Inquiry from {self.name} - {self.subject}"

    @property
    def is_recent(self):
        from django.utils import timezone
        from datetime import timedelta
        return self.submitted_at >= timezone.now() - timedelta(days=7)

    @property
    def response_time(self):
        if self.addressed_at and self.submitted_at:
            return self.addressed_at - self.submitted_at
        return None

    class Meta:
        ordering = ['-submitted_at']
        verbose_name_plural = "Contact Inquiries"
        

