#!/usr/bin/env python
"""
Test Tenant Template
Test the comprehensive tenant template system

Usage: python test_tenant_template.py
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from django.db import connection
from django.core.management import call_command
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_tenant_template():
    """Test the tenant template system"""
    
    logger.info("=== TESTING TENANT TEMPLATE SYSTEM ===")
    
    # Test schema name
    test_schema = "test_tenant_template"
    
    try:
        # 1. Create test schema
        logger.info(f"Creating test schema: {test_schema}")
        
        with connection.cursor() as cursor:
            # Drop if exists
            cursor.execute(f'DROP SCHEMA IF EXISTS "{test_schema}" CASCADE')
            
            # Create new schema
            cursor.execute(f'CREATE SCHEMA "{test_schema}"')
            logger.info(f"✅ Created test schema: {test_schema}")
        
        # 2. Apply tenant template
        logger.info(f"Applying tenant template to {test_schema}")
        
        # Check if template file exists
        template_file = "tenant_template.sql"
        if not os.path.exists(template_file):
            logger.error(f"❌ Template file {template_file} not found")
            return False
        
        with connection.cursor() as cursor:
            cursor.execute(f'SET search_path TO "{test_schema}"')
            
            # Read and execute template
            with open(template_file, 'r', encoding='utf-8') as f:
                sql_content = f.read()
            
            statements = [stmt.strip() for stmt in sql_content.split(';') if stmt.strip() and not stmt.strip().startswith('--')]
            
            executed_count = 0
            error_count = 0
            
            for statement in statements:
                if statement:
                    try:
                        cursor.execute(statement)
                        executed_count += 1
                    except Exception as e:
                        error_count += 1
                        if "already exists" not in str(e).lower():
                            logger.warning(f"Statement error: {e}")
            
            logger.info(f"✅ Executed {executed_count} statements, {error_count} errors")
        
        # 3. Verify essential tables exist
        logger.info("Verifying essential tables...")
        
        essential_tables = [
            'schools_academicyear', 'schools_term', 'schools_schoolclass', 'schools_section',
            'fees_feehead', 'fees_feestructure', 'fees_invoice', 'payments_payment',
            'hr_leavetype', 'payments_paymentmethod', 'fees_concessiontype',
            'school_calendar_eventcategory', 'auth_group', 'auth_permission',
            'django_content_type', 'students_student', 'schools_staffuser'
        ]
        
        with connection.cursor() as cursor:
            cursor.execute(f'SET search_path TO "{test_schema}"')
            
            existing_tables = []
            missing_tables = []
            
            for table in essential_tables:
                cursor.execute(f"""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_schema = '{test_schema}' 
                        AND table_name = '{table}'
                    )
                """)
                
                if cursor.fetchone()[0]:
                    existing_tables.append(table)
                else:
                    missing_tables.append(table)
            
            logger.info(f"✅ Existing tables: {len(existing_tables)}/{len(essential_tables)}")
            
            if missing_tables:
                logger.warning(f"⚠️  Missing tables: {missing_tables}")
            else:
                logger.info("✅ All essential tables present")
        
        # 4. Verify essential data exists
        logger.info("Verifying essential data...")
        
        data_tables = [
            ('auth_group', 5),
            ('auth_permission', 100),
            ('django_content_type', 50),
            ('hr_leavetype', 3),
            ('payments_paymentmethod', 3),
            ('fees_concessiontype', 3),
            ('school_calendar_eventcategory', 3)
        ]
        
        with connection.cursor() as cursor:
            cursor.execute(f'SET search_path TO "{test_schema}"')
            
            data_ok = True
            
            for table, min_expected in data_tables:
                try:
                    cursor.execute(f"SELECT COUNT(*) FROM {table}")
                    count = cursor.fetchone()[0]
                    
                    if count >= min_expected:
                        logger.info(f"✅ {table}: {count} records (expected >= {min_expected})")
                    else:
                        logger.warning(f"⚠️  {table}: {count} records (expected >= {min_expected})")
                        data_ok = False
                        
                except Exception as e:
                    logger.error(f"❌ {table}: Error - {e}")
                    data_ok = False
            
            if data_ok:
                logger.info("✅ All essential data present")
            else:
                logger.warning("⚠️  Some essential data missing")
        
        # 5. Test basic functionality
        logger.info("Testing basic functionality...")
        
        with connection.cursor() as cursor:
            cursor.execute(f'SET search_path TO "{test_schema}"')
            
            # Test academic year creation
            try:
                cursor.execute("SELECT COUNT(*) FROM schools_academicyear")
                ay_count = cursor.fetchone()[0]
                
                if ay_count == 0:
                    cursor.execute("""
                        INSERT INTO schools_academicyear (name, start_date, end_date, is_active, is_current, created_at, updated_at) 
                        VALUES ('Test Year', '2024-09-01', '2025-08-31', TRUE, TRUE, NOW(), NOW())
                    """)
                    logger.info("✅ Academic year creation test passed")
                else:
                    logger.info(f"✅ Academic year already exists: {ay_count} records")
                    
            except Exception as e:
                logger.error(f"❌ Academic year test failed: {e}")
            
            # Test staff user creation
            try:
                cursor.execute("SELECT COUNT(*) FROM schools_staffuser")
                staff_count = cursor.fetchone()[0]
                logger.info(f"✅ Staff user table accessible: {staff_count} records")
                
            except Exception as e:
                logger.error(f"❌ Staff user test failed: {e}")
            
            # Test student creation
            try:
                cursor.execute("SELECT COUNT(*) FROM students_student")
                student_count = cursor.fetchone()[0]
                logger.info(f"✅ Student table accessible: {student_count} records")
                
            except Exception as e:
                logger.error(f"❌ Student test failed: {e}")
        
        # 6. Cleanup
        logger.info(f"Cleaning up test schema: {test_schema}")
        
        with connection.cursor() as cursor:
            cursor.execute(f'DROP SCHEMA IF EXISTS "{test_schema}" CASCADE')
            logger.info(f"✅ Cleaned up test schema: {test_schema}")
        
        logger.info("✅ Tenant template test completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Tenant template test failed: {e}")
        
        # Cleanup on error
        try:
            with connection.cursor() as cursor:
                cursor.execute(f'DROP SCHEMA IF EXISTS "{test_schema}" CASCADE')
                logger.info(f"✅ Cleaned up test schema after error")
        except:
            pass
        
        return False

def main():
    """Main function"""
    logger.info("=== TENANT TEMPLATE TEST ===")
    
    try:
        success = test_tenant_template()
        
        if success:
            logger.info("\n🎉 TENANT TEMPLATE TEST PASSED!")
            logger.info("The tenant template system is working correctly!")
            logger.info("New tenants will be created smoothly without manual fixes!")
        else:
            logger.error("\n❌ TENANT TEMPLATE TEST FAILED!")
            logger.error("The tenant template system needs fixes!")
        
        return success
        
    except Exception as e:
        logger.error(f"Tenant template test failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
