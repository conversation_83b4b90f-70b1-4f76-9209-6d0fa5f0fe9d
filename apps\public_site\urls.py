# D:\school_fees_saas_v2\apps\public_site\urls.py
from django.urls import path
from . import views

app_name = 'public_site'

urlpatterns = [
    path('', views.home_view, name='home'),
    
    # --- ADD THIS URL FOR THE PRICING PAGE ---
    path('pricing/', views.PricingPageView.as_view(), name='pricing'),
    
    path('s/<slug:school_slug>/', views.school_public_profile_view, name='school_profile'),
    path('find-school/', views.find_school_view, name='find_school'),
    
    path('', views.HomePageView.as_view(), name='home'), # Use HomePageView for home
    path('features/', views.FeaturesPageView.as_view(), name='features'),
    path('pricing/', views.PricingPageView.as_view(), name='pricing'),
    
    path('register/', views.RegistrationView.as_view(), name='register'),
    
    
    path('about/', views.AboutPageView.as_view(), name='about'),
    path('contact/', views.ContactPageView.as_view(), name='contact'),
    path('contact/success/', views.ContactSuccessView.as_view(), name='contact_success'),
    path('community/', views.PublicMessagesView.as_view(), name='public_messages'),
    path('terms/', views.TermsPageView.as_view(), name='terms'),
    path('privacy/', views.PrivacyPageView.as_view(), name='privacy'),
    path('reviews/', views.TestimonialListView.as_view(), name='testimonial_list'),
    path('reviews/add/', views.AddTestimonialView.as_view(), name='testimonial_add'),
    path('reviews/thank-you/', views.TestimonialThankYouView.as_view(), name='testimonial_thank_you'),
    # Add SchoolFinder and SchoolPublicProfile views/urls here if implementing
    # path('find-school/', views.school_search_view, name='school_search'),
    # path('s/<slug:schema_name>/', views.school_public_profile_view, name='school_public_profile'),
]

