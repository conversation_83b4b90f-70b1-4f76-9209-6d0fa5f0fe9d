{"tables": {"announcements_announcement": [["id", "bigint", null, 64, 0, "NO", null, 1], ["title", "character varying", 255, null, null, "NO", null, 2], ["content", "text", null, null, null, "NO", null, 3], ["publish_date", "timestamp with time zone", null, null, null, "NO", null, 6], ["expiry_date", "timestamp with time zone", null, null, null, "YES", null, 7], ["is_published", "boolean", null, null, null, "NO", null, 8], ["is_sticky", "boolean", null, null, null, "NO", null, 9], ["created_at", "timestamp with time zone", null, null, null, "NO", null, 10], ["updated_at", "timestamp with time zone", null, null, null, "NO", null, 11], ["author_id", "bigint", null, 64, 0, "YES", null, 12], ["is_global", "boolean", null, null, null, "NO", null, 13], ["target_all_tenant_parents", "boolean", null, null, null, "NO", null, 14], ["target_all_tenant_staff", "boolean", null, null, null, "NO", null, 15], ["target_global_audience_type", "character varying", 50, null, null, "YES", null, 16], ["tenant_id", "bigint", null, 64, 0, "YES", null, 17]], "announcements_announcement_target_tenant_staff_groups": [["id", "bigint", null, 64, 0, "NO", null, 1], ["announcement_id", "bigint", null, 64, 0, "NO", null, 2], ["group_id", "integer", null, 32, 0, "NO", null, 3]], "auth_group": [["id", "integer", null, 32, 0, "NO", null, 1], ["name", "character varying", 150, null, null, "NO", null, 2]], "auth_group_permissions": [["id", "bigint", null, 64, 0, "NO", null, 1], ["group_id", "integer", null, 32, 0, "NO", null, 2], ["permission_id", "integer", null, 32, 0, "NO", null, 3]], "auth_permission": [["id", "integer", null, 32, 0, "NO", null, 1], ["name", "character varying", 255, null, null, "NO", null, 2], ["content_type_id", "integer", null, 32, 0, "NO", null, 3], ["codename", "character varying", 100, null, null, "NO", null, 4]], "communication_communicationlog": [["id", "bigint", null, 64, 0, "NO", null, 1], ["message_type", "character varying", 30, null, null, "NO", null, 2], ["status", "character varying", 20, null, null, "NO", null, 3], ["recipient_email", "character varying", 254, null, null, "YES", null, 4], ["recipient_phone", "character varying", 30, null, null, "YES", null, 5], ["subject", "character varying", 255, null, null, "YES", null, 6], ["body_preview", "text", null, null, null, "YES", null, 7], ["object_id", "character varying", 100, null, null, "YES", null, 8], ["sent_at", "timestamp with time zone", null, null, null, "YES", null, 9], ["created_at", "timestamp with time zone", null, null, null, "NO", null, 10], ["error_message", "text", null, null, null, "YES", null, 11], ["content_type_id", "integer", null, 32, 0, "YES", null, 12], ["sent_by_id", "bigint", null, 64, 0, "YES", null, 13], ["task_id", "character varying", 255, null, null, "YES", null, 14], ["updated_at", "timestamp with time zone", null, null, null, "NO", null, 15]], "django_admin_log": [["id", "integer", null, 32, 0, "NO", null, 1], ["action_time", "timestamp with time zone", null, null, null, "NO", null, 2], ["object_id", "text", null, null, null, "YES", null, 3], ["object_repr", "character varying", 200, null, null, "NO", null, 4], ["action_flag", "smallint", null, 16, 0, "NO", null, 5], ["change_message", "text", null, null, null, "NO", null, 6], ["content_type_id", "integer", null, 32, 0, "YES", null, 7], ["user_id", "bigint", null, 64, 0, "NO", null, 8]], "django_content_type": [["id", "integer", null, 32, 0, "NO", null, 1], ["app_label", "character varying", 100, null, null, "NO", null, 3], ["model", "character varying", 100, null, null, "NO", null, 4]], "django_migrations": [["id", "bigint", null, 64, 0, "NO", null, 1], ["app", "character varying", 255, null, null, "NO", null, 2], ["name", "character varying", 255, null, null, "NO", null, 3], ["applied", "timestamp with time zone", null, null, null, "NO", null, 4]], "fees_account": [["id", "bigint", null, 64, 0, "NO", null, 1], ["created_at", "timestamp with time zone", null, null, null, "NO", null, 2], ["updated_at", "timestamp with time zone", null, null, null, "NO", null, 3], ["name", "character varying", 100, null, null, "NO", null, 4], ["account_number", "character varying", 50, null, null, "YES", null, 5], ["account_type", "character varying", 10, null, null, "NO", null, 6], ["description", "text", null, null, null, "YES", null, 7], ["is_active", "boolean", null, null, null, "NO", null, 8]], "fees_concessiontype": [["id", "bigint", null, 64, 0, "NO", null, 1], ["name", "character varying", 100, null, null, "NO", null, 2], ["description", "text", null, null, null, "YES", null, 3], ["value", "numeric", null, 10, 2, "NO", null, 5], ["is_active", "boolean", null, null, null, "NO", null, 6], ["created_at", "timestamp with time zone", null, null, null, "NO", null, 7], ["updated_at", "timestamp with time zone", null, null, null, "NO", null, 8], ["type", "character varying", 20, null, null, "NO", "'PERCENTAGE'::character varying", 9]], "fees_feehead": [["id", "bigint", null, 64, 0, "NO", "nextval('fees_feehead_id_seq'::regclass)", 1], ["name", "character varying", 150, null, null, "NO", null, 2], ["description", "text", null, null, null, "YES", null, 3], ["income_account_link_id", "bigint", null, 64, 0, "YES", null, 4], ["is_active", "boolean", null, null, null, "NO", "true", 5], ["created_at", "timestamp with time zone", null, null, null, "NO", "now()", 6], ["updated_at", "timestamp with time zone", null, null, null, "NO", "now()", 7]], "fees_feestructure": [["id", "bigint", null, 64, 0, "NO", null, 1], ["name", "character varying", 200, null, null, "NO", null, 2], ["description", "text", null, null, null, "YES", null, 3], ["is_active", "boolean", null, null, null, "NO", null, 4], ["total_amount", "numeric", null, 12, 2, "NO", null, 5], ["created_at", "timestamp with time zone", null, null, null, "NO", null, 6], ["updated_at", "timestamp with time zone", null, null, null, "NO", null, 7], ["academic_year_id", "bigint", null, 64, 0, "NO", null, 8], ["term_id", "bigint", null, 64, 0, "YES", null, 10]], "fees_feestructure_applicable_classes": [["id", "bigint", null, 64, 0, "NO", "nextval('fees_feestructure_applicable_classes_id_seq'::regclass)", 1], ["feestructure_id", "bigint", null, 64, 0, "NO", null, 2], ["schoolclass_id", "bigint", null, 64, 0, "NO", null, 3]], "fees_feestructureitem": [["id", "bigint", null, 64, 0, "NO", null, 1], ["amount", "numeric", null, 10, 2, "NO", null, 2], ["is_optional", "boolean", null, null, null, "NO", null, 3], ["created_at", "timestamp with time zone", null, null, null, "YES", null, 4], ["updated_at", "timestamp with time zone", null, null, null, "YES", null, 5], ["concession_type_id", "bigint", null, 64, 0, "YES", null, 6], ["fee_head_id", "bigint", null, 64, 0, "NO", null, 7], ["fee_structure_id", "bigint", null, 64, 0, "NO", null, 8], ["description", "character varying", 255, null, null, "YES", null, 9]], "fees_invoice": [["id", "bigint", null, 64, 0, "NO", null, 1], ["invoice_number", "character varying", 50, null, null, "NO", null, 2], ["issue_date", "date", null, null, null, "NO", null, 3], ["due_date", "date", null, null, null, "YES", null, 4], ["subtotal_amount_calc", "numeric", null, 12, 2, "YES", null, 5], ["discount_amount_calc", "numeric", null, 12, 2, "YES", null, 6], ["total_amount", "numeric", null, 12, 2, "YES", null, 7], ["amount_paid", "numeric", null, 12, 2, "NO", null, 8], ["status", "character varying", 20, null, null, "NO", null, 9], ["period_description_override", "character varying", 255, null, null, "YES", null, 10], ["notes", "text", null, null, null, "YES", null, 11], ["internal_notes", "text", null, null, null, "YES", null, 12], ["created_at", "timestamp with time zone", null, null, null, "NO", null, 13], ["updated_at", "timestamp with time zone", null, null, null, "NO", null, 14], ["academic_year_id", "bigint", null, 64, 0, "NO", null, 15], ["created_by_id", "bigint", null, 64, 0, "YES", null, 16], ["fee_structure_id", "bigint", null, 64, 0, "YES", null, 17], ["related_journal_entry_id", "bigint", null, 64, 0, "YES", null, 18], ["student_id", "bigint", null, 64, 0, "NO", null, 19], ["term_id", "bigint", null, 64, 0, "YES", null, 20], ["subtotal_amount", "numeric", null, 12, 2, "NO", "0.00", 21], ["total_concession_amount", "numeric", null, 12, 2, "NO", "0.00", 22], ["notes_to_parent", "text", null, null, null, "YES", null, 23]], "fees_invoicedetail": [["id", "bigint", null, 64, 0, "NO", null, 1], ["line_type", "character varying", 20, null, null, "NO", null, 2], ["description", "character varying", 255, null, null, "NO", null, 3], ["quantity", "numeric", null, 10, 2, "NO", null, 4], ["unit_price", "numeric", null, 12, 2, "NO", null, 5], ["created_at", "timestamp with time zone", null, null, null, "NO", null, 6], ["updated_at", "timestamp with time zone", null, null, null, "NO", null, 7], ["applies_to_line_id", "bigint", null, 64, 0, "YES", null, 8], ["concession_type_id", "bigint", null, 64, 0, "YES", null, 9], ["fee_head_id", "bigint", null, 64, 0, "YES", null, 10], ["invoice_id", "bigint", null, 64, 0, "NO", null, 11], ["amount", "numeric", null, 12, 2, "NO", "0.00", 12]], "fees_studentconcession": [["id", "bigint", null, 64, 0, "NO", null, 1], ["notes", "text", null, null, null, "YES", null, 2], ["granted_at", "timestamp with time zone", null, null, null, "NO", null, 3], ["academic_year_id", "bigint", null, 64, 0, "NO", null, 4], ["concession_type_id", "bigint", null, 64, 0, "NO", null, 5], ["granted_by_id", "bigint", null, 64, 0, "YES", null, 6], ["student_id", "bigint", null, 64, 0, "NO", null, 7], ["term_id", "bigint", null, 64, 0, "YES", null, 8]], "fees_studentfeeallocation": [["id", "bigint", null, 64, 0, "NO", null, 1], ["effective_from", "date", null, null, null, "NO", null, 3], ["effective_to", "date", null, null, null, "YES", null, 4], ["is_active", "boolean", null, null, null, "NO", null, 5], ["created_at", "timestamp with time zone", null, null, null, "YES", null, 6], ["updated_at", "timestamp with time zone", null, null, null, "YES", null, 7], ["academic_year_id", "bigint", null, 64, 0, "NO", null, 8], ["fee_structure_id", "bigint", null, 64, 0, "NO", null, 9], ["student_id", "bigint", null, 64, 0, "NO", null, 10], ["term_id", "bigint", null, 64, 0, "YES", null, 11], ["notes", "text", null, null, null, "YES", null, 12]], "finance_budget": [["id", "bigint", null, 64, 0, "NO", null, 1], ["name", "character varying", 255, null, null, "NO", null, 2], ["financial_year_start", "date", null, null, null, "NO", null, 3], ["financial_year_end", "date", null, null, null, "NO", null, 4], ["description", "text", null, null, null, "YES", null, 5], ["is_active", "boolean", null, null, null, "NO", null, 6]], "finance_budgetamount": [["id", "bigint", null, 64, 0, "NO", null, 1], ["created_at", "timestamp with time zone", null, null, null, "YES", null, 2], ["updated_at", "timestamp with time zone", null, null, null, "YES", null, 3], ["budgeted_amount", "numeric", null, 12, 2, "NO", null, 4], ["notes", "text", null, null, null, "YES", null, 5], ["academic_year_id", "bigint", null, 64, 0, "NO", null, 6], ["term_id", "bigint", null, 64, 0, "YES", null, 7], ["budget_item_id", "bigint", null, 64, 0, "NO", null, 8]], "finance_budgetitem": [["id", "bigint", null, 64, 0, "NO", null, 1], ["created_at", "timestamp with time zone", null, null, null, "YES", null, 2], ["updated_at", "timestamp with time zone", null, null, null, "YES", null, 3], ["name", "character varying", 150, null, null, "NO", null, 4], ["description", "text", null, null, null, "YES", null, 5], ["budget_item_type", "character varying", 10, null, null, "NO", null, 6], ["linked_coa_account_id", "bigint", null, 64, 0, "NO", null, 7]], "finance_expense": [["id", "bigint", null, 64, 0, "NO", null, 1], ["created_at", "timestamp with time zone", null, null, null, "YES", null, 2], ["updated_at", "timestamp with time zone", null, null, null, "YES", null, 3], ["expense_date", "date", null, null, null, "NO", null, 4], ["amount", "numeric", null, 12, 2, "NO", null, 5], ["description", "text", null, null, null, "NO", null, 6], ["reference_number", "character varying", 100, null, null, "YES", null, 7], ["payment_method_id", "bigint", null, 64, 0, "YES", null, 8], ["recorded_by_id", "bigint", null, 64, 0, "YES", null, 9], ["category_id", "bigint", null, 64, 0, "NO", null, 10], ["vendor_id", "bigint", null, 64, 0, "YES", null, 11]], "finance_expensecategory": [["id", "bigint", null, 64, 0, "NO", null, 1], ["created_at", "timestamp with time zone", null, null, null, "YES", null, 2], ["updated_at", "timestamp with time zone", null, null, null, "YES", null, 3], ["name", "character varying", 100, null, null, "NO", null, 4], ["description", "text", null, null, null, "YES", null, 5], ["is_active", "boolean", null, null, null, "NO", null, 6], ["expense_account_id", "bigint", null, 64, 0, "NO", null, 7]], "finance_vendor": [["id", "bigint", null, 64, 0, "NO", null, 1], ["created_at", "timestamp with time zone", null, null, null, "YES", null, 2], ["updated_at", "timestamp with time zone", null, null, null, "YES", null, 3], ["name", "character varying", 150, null, null, "NO", null, 4], ["contact_person", "character varying", 100, null, null, "YES", null, 5], ["email", "character varying", 254, null, null, "YES", null, 6], ["phone_number", "character varying", 30, null, null, "YES", null, 7], ["address_line1", "character varying", 255, null, null, "YES", null, 8], ["address_line2", "character varying", 255, null, null, "YES", null, 9], ["city", "character varying", 100, null, null, "YES", null, 10], ["state_province", "character varying", 100, null, null, "YES", null, 11], ["postal_code", "character varying", 20, null, null, "YES", null, 12], ["country", "character varying", 100, null, null, "YES", null, 13], ["notes", "text", null, null, null, "YES", null, 14], ["is_active", "boolean", null, null, null, "NO", null, 15]], "hr_employeeprofile": [["user_id", "bigint", null, 64, 0, "NO", null, 1], ["middle_name", "character varying", 100, null, null, "NO", null, 2], ["gender", "character varying", 15, null, null, "YES", null, 3], ["date_of_birth", "date", null, null, null, "YES", null, 4], ["marital_status", "character varying", 15, null, null, "YES", null, 5], ["phone_number_alternate", "character varying", 30, null, null, "NO", null, 6], ["address_line1", "character varying", 255, null, null, "NO", null, 7], ["address_line2", "character varying", 255, null, null, "NO", null, 8], ["city", "character varying", 100, null, null, "NO", null, 9], ["state_province", "character varying", 100, null, null, "NO", null, 10], ["postal_code", "character varying", 20, null, null, "NO", null, 11], ["country", "character varying", 100, null, null, "NO", null, 12], ["employment_type", "character varying", 20, null, null, "YES", null, 13], ["date_left", "date", null, null, null, "YES", null, 14], ["photo", "character varying", 100, null, null, "YES", null, 15], ["notes", "text", null, null, null, "NO", null, 16], ["created_at", "timestamp with time zone", null, null, null, "NO", null, 17], ["updated_at", "timestamp with time zone", null, null, null, "NO", null, 18], ["date_hired", "date", null, null, null, "YES", null, 19], ["department", "character varying", 100, null, null, "NO", null, 20], ["designation", "character varying", 100, null, null, "NO", null, 21], ["employee_id", "character varying", 50, null, null, "YES", null, 22], ["phone_number_primary", "character varying", 30, null, null, "NO", null, 23]], "hr_graderule": [["id", "bigint", null, 64, 0, "NO", null, 1], ["value", "numeric", null, 10, 2, "NO", null, 2], ["component_id", "bigint", null, 64, 0, "NO", null, 3], ["grade_id", "bigint", null, 64, 0, "NO", null, 4]], "hr_leavebalance": [["id", "bigint", null, 64, 0, "NO", null, 1], ["days_accrued", "numeric", null, 5, 1, "NO", null, 3], ["last_accrual_date", "date", null, null, null, "YES", null, 5], ["created_at", "timestamp with time zone", null, null, null, "NO", null, 6], ["updated_at", "timestamp with time zone", null, null, null, "NO", null, 7], ["employee_id", "bigint", null, 64, 0, "NO", null, 8], ["leave_type_id", "bigint", null, 64, 0, "NO", null, 9], ["year", "integer", null, 32, 0, "NO", null, 10]], "hr_leaverequest": [["id", "bigint", null, 64, 0, "NO", null, 1], ["start_date", "date", null, null, null, "NO", null, 2], ["end_date", "date", null, null, null, "NO", null, 3], ["half_day_start", "boolean", null, null, null, "NO", null, 4], ["half_day_end", "boolean", null, null, null, "NO", null, 5], ["reason", "text", null, null, null, "NO", null, 6], ["attachment", "character varying", 100, null, null, "YES", null, 7], ["status", "character varying", 20, null, null, "NO", null, 8], ["created_at", "timestamp with time zone", null, null, null, "NO", null, 13], ["updated_at", "timestamp with time zone", null, null, null, "NO", null, 14], ["approved_by_id", "bigint", null, 64, 0, "YES", null, 15], ["employee_id", "bigint", null, 64, 0, "NO", null, 16], ["leave_type_id", "bigint", null, 64, 0, "NO", null, 17], ["duration", "numeric", null, 5, 1, "NO", null, 18], ["status_changed_at", "timestamp with time zone", null, null, null, "YES", null, 19], ["status_reason", "text", null, null, null, "YES", null, 20]], "hr_leavetype": [["id", "bigint", null, 64, 0, "NO", null, 1], ["name", "character varying", 100, null, null, "NO", null, 2], ["description", "text", null, null, null, "YES", null, 3], ["max_annual_days", "numeric", null, 5, 1, "YES", null, 4], ["is_paid", "boolean", null, null, null, "NO", null, 5], ["requires_approval", "boolean", null, null, null, "NO", null, 6], ["is_active", "boolean", null, null, null, "NO", null, 7], ["created_at", "timestamp with time zone", null, null, null, "NO", null, 8], ["updated_at", "timestamp with time zone", null, null, null, "NO", null, 9], ["max_days_per_year_grant", "numeric", null, 5, 2, "YES", null, 10], ["accrual_frequency", "character varying", 20, null, null, "NO", null, 11], ["accrual_rate", "numeric", null, 5, 2, "NO", null, 12], ["max_accrual_balance", "integer", null, 32, 0, "YES", null, 13], ["prorate_accrual", "boolean", null, null, null, "NO", null, 14]], "hr_payrollrun": [["id", "bigint", null, 64, 0, "NO", null, 1], ["pay_period_start", "date", null, null, null, "NO", null, 2], ["pay_period_end", "date", null, null, null, "NO", null, 3], ["payment_date", "date", null, null, null, "NO", null, 4], ["status", "character varying", 20, null, null, "NO", null, 5], ["notes", "text", null, null, null, "YES", null, 6], ["processed_at", "timestamp with time zone", null, null, null, "YES", null, 7], ["created_at", "timestamp with time zone", null, null, null, "NO", null, 8], ["updated_at", "timestamp with time zone", null, null, null, "NO", null, 9], ["processed_by_id", "bigint", null, 64, 0, "YES", null, 10]], "hr_payslip": [["id", "bigint", null, 64, 0, "NO", null, 1], ["gross_earnings", "numeric", null, 10, 2, "NO", null, 4], ["total_deductions", "numeric", null, 10, 2, "NO", null, 5], ["net_pay", "numeric", null, 10, 2, "NO", null, 6], ["created_at", "timestamp with time zone", null, null, null, "NO", null, 8], ["staff_member_id", "bigint", null, 64, 0, "NO", null, 10], ["allowances", "numeric", null, 10, 2, "NO", null, 11], ["basic_salary", "numeric", null, 10, 2, "NO", null, 12], ["bonuses", "numeric", null, 10, 2, "NO", null, 13], ["loan_repayments", "numeric", null, 10, 2, "NO", null, 14], ["notes", "text", null, null, null, "YES", null, 15], ["other_deductions", "numeric", null, 10, 2, "NO", null, 16], ["pension_deductions", "numeric", null, 10, 2, "NO", null, 17], ["tax_deductions", "numeric", null, 10, 2, "NO", null, 18], ["payroll_run_id", "bigint", null, 64, 0, "YES", null, 19], ["adjustments", "numeric", null, 10, 2, "NO", null, 20]], "hr_paysliplineitem": [["id", "bigint", null, 64, 0, "NO", null, 1], ["name", "character varying", 100, null, null, "NO", null, 2], ["type", "character varying", 10, null, null, "NO", null, 3], ["amount", "numeric", null, 12, 2, "NO", null, 4], ["payslip_id", "bigint", null, 64, 0, "NO", null, 5], ["source_component_id", "bigint", null, 64, 0, "YES", null, 6]], "hr_salarycomponent": [["id", "bigint", null, 64, 0, "NO", null, 1], ["name", "character varying", 100, null, null, "NO", null, 2], ["type", "character varying", 10, null, null, "NO", null, 3], ["description", "text", null, null, null, "YES", null, 6], ["is_percentage", "boolean", null, null, null, "NO", null, 7]], "hr_salarygrade": [["id", "bigint", null, 64, 0, "NO", null, 1], ["name", "character varying", 100, null, null, "NO", null, 2], ["description", "text", null, null, null, "NO", null, 3], ["is_active", "boolean", null, null, null, "NO", null, 5]], "hr_salarygradecomponent": [["id", "bigint", null, 64, 0, "NO", null, 1], ["amount", "numeric", null, 10, 2, "NO", null, 2], ["component_id", "bigint", null, 64, 0, "NO", null, 3], ["grade_id", "bigint", null, 64, 0, "NO", null, 4]], "hr_salarystructurecomponent": [["id", "bigint", null, 64, 0, "NO", null, 1], ["amount", "numeric", null, 10, 2, "NO", null, 2], ["component_id", "bigint", null, 64, 0, "NO", null, 3], ["salary_structure_id", "bigint", null, 64, 0, "NO", null, 4]], "hr_staffsalary": [["id", "bigint", null, 64, 0, "NO", null, 1], ["basic_salary", "numeric", null, 10, 2, "NO", null, 2], ["effective_from", "date", null, null, null, "NO", null, 3], ["updated_at", "timestamp with time zone", null, null, null, "NO", null, 4], ["staff_member_id", "bigint", null, 64, 0, "NO", null, 5], ["grade_id", "bigint", null, 64, 0, "YES", null, 6]], "hr_staffsalarystructure": [["id", "bigint", null, 64, 0, "NO", null, 1], ["effective_date", "date", null, null, null, "NO", null, 2], ["staff_user_id", "bigint", null, 64, 0, "NO", null, 3]], "hr_statutorydeduction": [["id", "bigint", null, 64, 0, "NO", null, 1], ["name", "character varying", 100, null, null, "NO", null, 2], ["employee_contribution_rate", "numeric", null, 5, 2, "NO", null, 3], ["employer_contribution_rate", "numeric", null, 5, 2, "NO", null, 4], ["is_active", "boolean", null, null, null, "NO", null, 5], ["payslip_label", "character varying", 20, null, null, "NO", null, 6]], "hr_taxbracket": [["id", "bigint", null, 64, 0, "NO", null, 1], ["name", "character varying", 100, null, null, "NO", null, 2], ["from_amount", "numeric", null, 12, 2, "NO", null, 3], ["to_amount", "numeric", null, 12, 2, "YES", null, 4], ["rate_percent", "numeric", null, 5, 2, "NO", null, 5], ["deduction_amount", "numeric", null, 12, 2, "NO", null, 6], ["is_active", "boolean", null, null, null, "NO", null, 7]], "payments_payment": [["id", "bigint", null, 64, 0, "NO", null, 1], ["amount", "numeric", null, 12, 2, "NO", null, 2], ["payment_date", "timestamp with time zone", null, null, null, "NO", null, 3], ["transaction_reference", "character varying", 150, null, null, "YES", null, 4], ["notes", "text", null, null, null, "YES", null, 5], ["payment_type", "character varying", 20, null, null, "NO", null, 6], ["status", "character varying", 20, null, null, "NO", null, 7], ["unallocated_amount", "numeric", null, 12, 2, "NO", null, 8], ["created_at", "timestamp with time zone", null, null, null, "NO", null, 9], ["updated_at", "timestamp with time zone", null, null, null, "NO", null, 10], ["academic_year_id", "bigint", null, 64, 0, "YES", null, 11], ["created_by_id", "bigint", null, 64, 0, "YES", null, 12], ["parent_payer_id", "bigint", null, 64, 0, "YES", null, 13], ["processed_by_staff_id", "bigint", null, 64, 0, "YES", null, 14], ["student_id", "bigint", null, 64, 0, "YES", null, 15], ["payment_method_id", "bigint", null, 64, 0, "YES", null, 16], ["receipt_number", "character varying", 20, null, null, "YES", null, 17], ["payment_reference", "character varying", 100, null, null, "YES", "''::character varying", 18]], "payments_paymentallocation": [["id", "bigint", null, 64, 0, "NO", null, 1], ["amount_allocated", "numeric", null, 12, 2, "NO", null, 2], ["allocation_date", "date", null, null, null, "NO", null, 3], ["created_at", "timestamp with time zone", null, null, null, "NO", null, 4], ["invoice_id", "bigint", null, 64, 0, "YES", null, 5], ["payment_id", "bigint", null, 64, 0, "YES", null, 6]], "payments_paymentmethod": [["id", "bigint", null, 64, 0, "NO", null, 1], ["created_at", "timestamp with time zone", null, null, null, "YES", null, 2], ["updated_at", "timestamp with time zone", null, null, null, "YES", null, 3], ["name", "character varying", 100, null, null, "NO", null, 4], ["type", "character varying", 30, null, null, "NO", null, 5], ["description", "text", null, null, null, "YES", null, 6], ["is_active", "boolean", null, null, null, "NO", null, 7], ["linked_account_id", "bigint", null, 64, 0, "YES", null, 8]], "portal_admin_adminactivitylog": [["id", "bigint", null, 64, 0, "NO", null, 1], ["action_type", "character varying", 50, null, null, "NO", null, 2], ["timestamp", "timestamp with time zone", null, null, null, "NO", null, 3], ["actor_description", "character varying", 255, null, null, "YES", null, 4], ["ip_address", "inet", null, null, null, "YES", null, 5], ["user_agent", "text", null, null, null, "NO", null, 6], ["target_object_id", "character varying", 255, null, null, "YES", null, 7], ["target_object_repr", "character varying", 300, null, null, "YES", null, 8], ["description", "text", null, null, null, "NO", null, 9], ["staff_user_id", "bigint", null, 64, 0, "YES", null, 10], ["target_content_type_id", "integer", null, 32, 0, "YES", null, 11], ["tenant_id", "bigint", null, 64, 0, "YES", null, 12], ["user_id", "bigint", null, 64, 0, "YES", null, 13]], "school_calendar_eventattendee": [["id", "bigint", null, 64, 0, "NO", null, 1], ["rsvp_status", "character varying", 15, null, null, "NO", null, 2], ["rsvp_date", "timestamp with time zone", null, null, null, "NO", null, 3], ["notes", "text", null, null, null, "NO", null, 4], ["user_id", "bigint", null, 64, 0, "NO", null, 5], ["event_id", "bigint", null, 64, 0, "NO", null, 6]], "school_calendar_eventcategory": [["id", "bigint", null, 64, 0, "NO", null, 1], ["name", "character varying", 100, null, null, "NO", null, 2], ["color", "character varying", 7, null, null, "NO", null, 3], ["icon", "character varying", 50, null, null, "NO", null, 4], ["description", "text", null, null, null, "NO", null, 5], ["is_active", "boolean", null, null, null, "NO", null, 6], ["created_at", "timestamp with time zone", null, null, null, "NO", null, 7], ["updated_at", "timestamp with time zone", null, null, null, "NO", null, 8]], "school_calendar_schoolevent": [["id", "bigint", null, 64, 0, "NO", null, 1], ["title", "character varying", 200, null, null, "NO", null, 2], ["description", "text", null, null, null, "NO", null, 3], ["event_type", "character varying", 20, null, null, "NO", null, 4], ["priority", "character varying", 10, null, null, "NO", null, 5], ["start_date", "date", null, null, null, "NO", null, 6], ["end_date", "date", null, null, null, "NO", null, 7], ["start_time", "time without time zone", null, null, null, "YES", null, 8], ["end_time", "time without time zone", null, null, null, "YES", null, 9], ["is_all_day", "boolean", null, null, null, "NO", null, 10], ["location", "character varying", 200, null, null, "NO", null, 11], ["venue_details", "text", null, null, null, "NO", null, 12], ["recurrence", "character varying", 10, null, null, "NO", null, 13], ["recurrence_end_date", "date", null, null, null, "YES", null, 14], ["is_public", "boolean", null, null, null, "NO", null, 15], ["visible_to_parents", "boolean", null, null, null, "NO", null, 16], ["visible_to_staff", "boolean", null, null, null, "NO", null, 17], ["visible_to_students", "boolean", null, null, null, "NO", null, 18], ["created_at", "timestamp with time zone", null, null, null, "NO", null, 19], ["updated_at", "timestamp with time zone", null, null, null, "NO", null, 20], ["is_active", "boolean", null, null, null, "NO", null, 21], ["requires_rsvp", "boolean", null, null, null, "NO", null, 22], ["max_attendees", "integer", null, 32, 0, "YES", null, 23], ["contact_person", "character varying", 100, null, null, "NO", null, 24], ["contact_email", "character varying", 254, null, null, "NO", null, 25], ["contact_phone", "character varying", 20, null, null, "NO", null, 26], ["category_id", "bigint", null, 64, 0, "YES", null, 27], ["created_by_id", "bigint", null, 64, 0, "YES", null, 28], ["created_by_staff_id", "bigint", null, 64, 0, "YES", null, 29]], "schools_academicsetting": [["id", "bigint", null, 64, 0, "NO", null, 1], ["grading_system", "character varying", 20, null, null, "NO", null, 2], ["current_academic_year_id", "bigint", null, 64, 0, "YES", null, 3]], "schools_academicyear": [["id", "bigint", null, 64, 0, "NO", null, 1], ["name", "character varying", 100, null, null, "NO", null, 2], ["start_date", "date", null, null, null, "NO", null, 3], ["end_date", "date", null, null, null, "NO", null, 4], ["is_active", "boolean", null, null, null, "NO", null, 5], ["is_current", "boolean", null, null, null, "NO", null, 6], ["created_at", "timestamp with time zone", null, null, null, "NO", null, 7], ["updated_at", "timestamp with time zone", null, null, null, "NO", null, 8]], "schools_country": [["id", "bigint", null, 64, 0, "NO", null, 1], ["name", "character varying", 100, null, null, "NO", null, 2], ["code", "character varying", 3, null, null, "NO", null, 3]], "schools_invoicesequence": [["id", "bigint", null, 64, 0, "NO", null, 1], ["prefix", "character varying", 10, null, null, "NO", null, 2], ["last_number", "integer", null, 32, 0, "NO", null, 3], ["padding_digits", "smallint", null, 16, 0, "NO", null, 4], ["last_updated", "timestamp with time zone", null, null, null, "NO", null, 5], ["created_at", "timestamp with time zone", null, null, null, "NO", null, 6]], "schools_receiptsequence": [["id", "bigint", null, 64, 0, "NO", null, 1], ["prefix", "character varying", 10, null, null, "NO", null, 2], ["last_number", "integer", null, 32, 0, "NO", null, 3], ["padding_digits", "smallint", null, 16, 0, "NO", null, 4], ["last_updated", "timestamp with time zone", null, null, null, "NO", null, 5], ["created_at", "timestamp with time zone", null, null, null, "NO", null, 6]], "schools_schoolclass": [["id", "bigint", null, 64, 0, "NO", null, 1], ["name", "character varying", 100, null, null, "NO", null, 2], ["description", "text", null, null, null, "YES", null, 3], ["is_active", "boolean", null, null, null, "NO", null, 4], ["created_at", "timestamp with time zone", null, null, null, "NO", null, 5], ["updated_at", "timestamp with time zone", null, null, null, "NO", null, 6]], "schools_schoolprofile": [["school_name_override", "character varying", 255, null, null, "YES", null, 2], ["school_motto", "character varying", 255, null, null, "YES", null, 3], ["logo", "character varying", 100, null, null, "YES", null, 4], ["school_email", "character varying", 254, null, null, "YES", null, 5], ["phone_number", "character varying", 30, null, null, "YES", null, 6], ["address_line1", "character varying", 255, null, null, "YES", null, 7], ["address_line2", "character varying", 255, null, null, "YES", null, 8], ["city", "character varying", 100, null, null, "YES", null, 9], ["state_province", "character varying", 100, null, null, "YES", null, 10], ["postal_code", "character varying", 20, null, null, "YES", null, 11], ["country_name", "character varying", 100, null, null, "YES", null, 12], ["financial_year_start_month", "smallint", null, 16, 0, "NO", null, 13], ["currency_symbol", "character varying", 5, null, null, "YES", null, 14], ["school_name_on_reports", "character varying", 255, null, null, "YES", null, 15], ["default_due_days", "integer", null, 32, 0, "NO", null, 16], ["created_at", "timestamp with time zone", null, null, null, "NO", null, 17], ["updated_at", "timestamp with time zone", null, null, null, "NO", null, 18], ["current_academic_year_id", "bigint", null, 64, 0, "YES", null, 19], ["default_accounts_receivable_coa_id", "bigint", null, 64, 0, "YES", null, 20], ["default_bank_coa_id", "bigint", null, 64, 0, "YES", null, 21], ["default_cash_coa_id", "bigint", null, 64, 0, "YES", null, 22], ["default_discount_given_coa_id", "bigint", null, 64, 0, "YES", null, 23], ["default_expense_coa_id", "bigint", null, 64, 0, "YES", null, 24], ["default_fee_income_coa_id", "bigint", null, 64, 0, "YES", null, 25], ["id", "bigint", null, 64, 0, "NO", null, 26]], "schools_section": [["id", "bigint", null, 64, 0, "NO", null, 1], ["name", "character varying", 50, null, null, "NO", null, 2], ["room_number", "character varying", 50, null, null, "NO", null, 3], ["school_class_id", "bigint", null, 64, 0, "NO", null, 4], ["class_teacher_id", "bigint", null, 64, 0, "YES", null, 5]], "schools_staffuser": [["id", "bigint", null, 64, 0, "NO", null, 1], ["password", "character varying", 128, null, null, "NO", null, 2], ["last_login", "timestamp with time zone", null, null, null, "YES", null, 3], ["is_superuser", "boolean", null, null, null, "NO", null, 4], ["email", "character varying", 254, null, null, "NO", null, 5], ["first_name", "character varying", 150, null, null, "NO", null, 6], ["last_name", "character varying", 150, null, null, "NO", null, 8], ["is_active", "boolean", null, null, null, "NO", null, 28], ["is_staff", "boolean", null, null, null, "NO", null, 29], ["date_joined", "timestamp with time zone", null, null, null, "NO", null, 30], ["employee_id", "character varying", 50, null, null, "YES", "''::character varying", 32], ["designation", "character varying", 100, null, null, "YES", "''::character varying", 33]], "schools_staffuser_groups": [["id", "bigint", null, 64, 0, "NO", null, 1], ["staffuser_id", "bigint", null, 64, 0, "NO", null, 2], ["group_id", "integer", null, 32, 0, "NO", null, 3]], "schools_staffuser_user_permissions": [["id", "bigint", null, 64, 0, "NO", null, 1], ["staffuser_id", "bigint", null, 64, 0, "NO", null, 2], ["permission_id", "integer", null, 32, 0, "NO", null, 3]], "schools_term": [["id", "bigint", null, 64, 0, "NO", null, 1], ["name", "character varying", 100, null, null, "NO", null, 2], ["start_date", "date", null, null, null, "NO", null, 3], ["end_date", "date", null, null, null, "NO", null, 4], ["is_active", "boolean", null, null, null, "NO", null, 5], ["created_at", "timestamp with time zone", null, null, null, "NO", null, 6], ["updated_at", "timestamp with time zone", null, null, null, "NO", null, 7], ["academic_year_id", "bigint", null, 64, 0, "NO", null, 8]], "students_parentuser": [["id", "bigint", null, 64, 0, "NO", null, 1], ["password", "character varying", 128, null, null, "NO", null, 2], ["last_login", "timestamp with time zone", null, null, null, "YES", null, 3], ["is_superuser", "boolean", null, null, null, "NO", null, 4], ["email", "character varying", 254, null, null, "NO", null, 5], ["username", "character varying", 150, null, null, "YES", null, 6], ["first_name", "character varying", 150, null, null, "NO", null, 7], ["last_name", "character varying", 150, null, null, "NO", null, 8], ["phone_number", "character varying", 20, null, null, "NO", null, 9], ["address_line1", "character varying", 255, null, null, "YES", null, 10], ["address_line2", "character varying", 255, null, null, "YES", null, 11], ["city", "character varying", 100, null, null, "YES", null, 12], ["state_province", "character varying", 100, null, null, "YES", null, 13], ["postal_code", "character varying", 20, null, null, "YES", null, 14], ["country", "character varying", 100, null, null, "YES", null, 15], ["profile_picture", "character varying", 100, null, null, "YES", null, 16], ["is_active", "boolean", null, null, null, "NO", null, 17], ["is_staff", "boolean", null, null, null, "NO", null, 18], ["date_joined", "timestamp with time zone", null, null, null, "NO", null, 19]], "students_parentuser_groups": [["id", "bigint", null, 64, 0, "NO", null, 1], ["parentuser_id", "bigint", null, 64, 0, "NO", null, 2], ["group_id", "integer", null, 32, 0, "NO", null, 3]], "students_parentuser_user_permissions": [["id", "bigint", null, 64, 0, "NO", null, 1], ["parentuser_id", "bigint", null, 64, 0, "NO", null, 2], ["permission_id", "integer", null, 32, 0, "NO", null, 3]], "students_student": [["id", "bigint", null, 64, 0, "NO", null, 1], ["admission_number", "character varying", 50, null, null, "NO", null, 2], ["first_name", "character varying", 100, null, null, "NO", null, 3], ["middle_name", "character varying", 100, null, null, "NO", null, 4], ["last_name", "character varying", 100, null, null, "NO", null, 5], ["date_of_birth", "date", null, null, null, "YES", null, 6], ["gender", "character varying", 15, null, null, "NO", null, 7], ["photo", "character varying", 100, null, null, "YES", null, 8], ["date_of_admission", "date", null, null, null, "NO", null, 9], ["status", "character varying", 20, null, null, "NO", null, 10], ["is_active", "boolean", null, null, null, "NO", null, 11], ["roll_number", "character varying", 20, null, null, "NO", null, 12], ["student_email", "character varying", 254, null, null, "NO", null, 13], ["student_phone", "character varying", 30, null, null, "NO", null, 14], ["guardian1_full_name", "character varying", 150, null, null, "NO", null, 15], ["guardian1_relationship", "character varying", 50, null, null, "NO", null, 16], ["guardian1_phone", "character varying", 30, null, null, "NO", null, 17], ["guardian1_email", "character varying", 254, null, null, "NO", null, 18], ["guardian1_occupation", "character varying", 100, null, null, "NO", null, 19], ["guardian2_full_name", "character varying", 150, null, null, "NO", null, 20], ["guardian2_relationship", "character varying", 50, null, null, "NO", null, 21], ["guardian2_phone", "character varying", 30, null, null, "NO", null, 22], ["guardian2_email", "character varying", 254, null, null, "NO", null, 23], ["address_line1", "character varying", 255, null, null, "NO", null, 24], ["address_line2", "character varying", 255, null, null, "NO", null, 25], ["city", "character varying", 100, null, null, "NO", null, 26], ["state_province", "character varying", 100, null, null, "NO", null, 27], ["postal_code", "character varying", 20, null, null, "NO", null, 28], ["country", "character varying", 100, null, null, "NO", null, 29], ["blood_group", "character varying", 10, null, null, "NO", null, 30], ["allergies", "text", null, null, null, "NO", null, 31], ["medical_conditions", "text", null, null, null, "NO", null, 32], ["previous_school", "character varying", 200, null, null, "NO", null, 33], ["notes", "text", null, null, null, "NO", null, 34], ["created_at", "timestamp with time zone", null, null, null, "NO", null, 35], ["updated_at", "timestamp with time zone", null, null, null, "NO", null, 36], ["current_class_id", "bigint", null, 64, 0, "YES", null, 37], ["current_section_id", "bigint", null, 64, 0, "YES", null, 38], ["created_by_id", "bigint", null, 64, 0, "YES", null, 39], ["emergency_contact", "character varying", 200, null, null, "NO", null, 40], ["emergency_phone", "character varying", 20, null, null, "NO", null, 41], ["house", "character varying", 100, null, null, "NO", null, 42], ["nationality", "character varying", 100, null, null, "NO", null, 43], ["religion", "character varying", 100, null, null, "NO", null, 44], ["transport_mode", "character varying", 50, null, null, "NO", null, 45]], "students_student_parents": [["id", "bigint", null, 64, 0, "NO", null, 1], ["student_id", "bigint", null, 64, 0, "NO", null, 2], ["parentuser_id", "bigint", null, 64, 0, "NO", null, 3]]}, "sequences": {}, "indexes": {"announcements_announcement": [["announcements_announcement_pkey", "CREATE UNIQUE INDEX announcements_announcement_pkey ON alpha.announcements_announcement USING btree (id)"], ["announcements_announcement_is_published_0783aeee", "CREATE INDEX announcements_announcement_is_published_0783aeee ON alpha.announcements_announcement USING btree (is_published)"], ["announcements_announcement_is_sticky_be940b61", "CREATE INDEX announcements_announcement_is_sticky_be940b61 ON alpha.announcements_announcement USING btree (is_sticky)"], ["announcements_announcement_author_id_47b59c69", "CREATE INDEX announcements_announcement_author_id_47b59c69 ON alpha.announcements_announcement USING btree (author_id)"], ["announcements_announcement_expiry_date_85d8cd59", "CREATE INDEX announcements_announcement_expiry_date_85d8cd59 ON alpha.announcements_announcement USING btree (expiry_date)"], ["announcements_announcement_publish_date_ed7e7b6a", "CREATE INDEX announcements_announcement_publish_date_ed7e7b6a ON alpha.announcements_announcement USING btree (publish_date)"], ["announcements_announcement_tenant_id_f2ddd667", "CREATE INDEX announcements_announcement_tenant_id_f2ddd667 ON alpha.announcements_announcement USING btree (tenant_id)"]], "announcements_announcement_target_tenant_staff_groups": [["announcements_announcement_target_tenant_staff_groups_pkey", "CREATE UNIQUE INDEX announcements_announcement_target_tenant_staff_groups_pkey ON alpha.announcements_announcement_target_tenant_staff_groups USING btree (id)"], ["announcements_announceme_announcement_id_group_id_be9cdcd1_uniq", "CREATE UNIQUE INDEX announcements_announceme_announcement_id_group_id_be9cdcd1_uniq ON alpha.announcements_announcement_target_tenant_staff_groups USING btree (announcement_id, group_id)"], ["announcements_announcement_announcement_id_d30296c8", "CREATE INDEX announcements_announcement_announcement_id_d30296c8 ON alpha.announcements_announcement_target_tenant_staff_groups USING btree (announcement_id)"], ["announcements_announcement_group_id_40a184a0", "CREATE INDEX announcements_announcement_group_id_40a184a0 ON alpha.announcements_announcement_target_tenant_staff_groups USING btree (group_id)"]], "auth_group": [["auth_group_pkey", "CREATE UNIQUE INDEX auth_group_pkey ON alpha.auth_group USING btree (id)"], ["auth_group_name_key", "CREATE UNIQUE INDEX auth_group_name_key ON alpha.auth_group USING btree (name)"], ["auth_group_name_a6ea08ec_like", "CREATE INDEX auth_group_name_a6ea08ec_like ON alpha.auth_group USING btree (name varchar_pattern_ops)"]], "auth_group_permissions": [["auth_group_permissions_pkey", "CREATE UNIQUE INDEX auth_group_permissions_pkey ON alpha.auth_group_permissions USING btree (id)"], ["auth_group_permissions_group_id_permission_id_0cd325b0_uniq", "CREATE UNIQUE INDEX auth_group_permissions_group_id_permission_id_0cd325b0_uniq ON alpha.auth_group_permissions USING btree (group_id, permission_id)"], ["auth_group_permissions_group_id_b120cbf9", "CREATE INDEX auth_group_permissions_group_id_b120cbf9 ON alpha.auth_group_permissions USING btree (group_id)"], ["auth_group_permissions_permission_id_84c5c92e", "CREATE INDEX auth_group_permissions_permission_id_84c5c92e ON alpha.auth_group_permissions USING btree (permission_id)"]], "auth_permission": [["auth_permission_pkey", "CREATE UNIQUE INDEX auth_permission_pkey ON alpha.auth_permission USING btree (id)"], ["auth_permission_content_type_id_codename_01ab375a_uniq", "CREATE UNIQUE INDEX auth_permission_content_type_id_codename_01ab375a_uniq ON alpha.auth_permission USING btree (content_type_id, codename)"], ["auth_permission_content_type_id_2f476e4b", "CREATE INDEX auth_permission_content_type_id_2f476e4b ON alpha.auth_permission USING btree (content_type_id)"]], "communication_communicationlog": [["communication_communicationlog_pkey", "CREATE UNIQUE INDEX communication_communicationlog_pkey ON alpha.communication_communicationlog USING btree (id)"], ["communication_communicationlog_content_type_id_ebcdf3fc", "CREATE INDEX communication_communicationlog_content_type_id_ebcdf3fc ON alpha.communication_communicationlog USING btree (content_type_id)"], ["communication_communicationlog_sent_by_id_9d71ed5c", "CREATE INDEX communication_communicationlog_sent_by_id_9d71ed5c ON alpha.communication_communicationlog USING btree (sent_by_id)"], ["communication_communicationlog_task_id_key", "CREATE UNIQUE INDEX communication_communicationlog_task_id_key ON alpha.communication_communicationlog USING btree (task_id)"], ["communication_communicationlog_object_id_3abc9ae3", "CREATE INDEX communication_communicationlog_object_id_3abc9ae3 ON alpha.communication_communicationlog USING btree (object_id)"], ["communication_communicationlog_object_id_3abc9ae3_like", "CREATE INDEX communication_communicationlog_object_id_3abc9ae3_like ON alpha.communication_communicationlog USING btree (object_id varchar_pattern_ops)"], ["communication_communicationlog_recipient_email_3e4bec2c", "CREATE INDEX communication_communicationlog_recipient_email_3e4bec2c ON alpha.communication_communicationlog USING btree (recipient_email)"], ["communication_communicationlog_recipient_email_3e4bec2c_like", "CREATE INDEX communication_communicationlog_recipient_email_3e4bec2c_like ON alpha.communication_communicationlog USING btree (recipient_email varchar_pattern_ops)"], ["communication_communicationlog_recipient_phone_935068eb", "CREATE INDEX communication_communicationlog_recipient_phone_935068eb ON alpha.communication_communicationlog USING btree (recipient_phone)"], ["communication_communicationlog_recipient_phone_935068eb_like", "CREATE INDEX communication_communicationlog_recipient_phone_935068eb_like ON alpha.communication_communicationlog USING btree (recipient_phone varchar_pattern_ops)"], ["communication_communicationlog_status_c1b37062", "CREATE INDEX communication_communicationlog_status_c1b37062 ON alpha.communication_communicationlog USING btree (status)"], ["communication_communicationlog_status_c1b37062_like", "CREATE INDEX communication_communicationlog_status_c1b37062_like ON alpha.communication_communicationlog USING btree (status varchar_pattern_ops)"], ["communicati_content_40ada6_idx", "CREATE INDEX communicati_content_40ada6_idx ON alpha.communication_communicationlog USING btree (content_type_id, object_id)"], ["communication_communicationlog_task_id_1c9118a2_like", "CREATE INDEX communication_communicationlog_task_id_1c9118a2_like ON alpha.communication_communicationlog USING btree (task_id varchar_pattern_ops)"]], "django_admin_log": [["django_admin_log_pkey", "CREATE UNIQUE INDEX django_admin_log_pkey ON alpha.django_admin_log USING btree (id)"], ["django_admin_log_content_type_id_c4bce8eb", "CREATE INDEX django_admin_log_content_type_id_c4bce8eb ON alpha.django_admin_log USING btree (content_type_id)"], ["django_admin_log_user_id_c564eba6", "CREATE INDEX django_admin_log_user_id_c564eba6 ON alpha.django_admin_log USING btree (user_id)"]], "django_content_type": [["django_content_type_pkey", "CREATE UNIQUE INDEX django_content_type_pkey ON alpha.django_content_type USING btree (id)"], ["django_content_type_app_label_model_76bd3d3b_uniq", "CREATE UNIQUE INDEX django_content_type_app_label_model_76bd3d3b_uniq ON alpha.django_content_type USING btree (app_label, model)"]], "django_migrations": [["django_migrations_pkey", "CREATE UNIQUE INDEX django_migrations_pkey ON alpha.django_migrations USING btree (id)"]], "fees_account": [["fees_account_pkey", "CREATE UNIQUE INDEX fees_account_pkey ON alpha.fees_account USING btree (id)"], ["fees_account_name_key", "CREATE UNIQUE INDEX fees_account_name_key ON alpha.fees_account USING btree (name)"], ["fees_account_account_number_key", "CREATE UNIQUE INDEX fees_account_account_number_key ON alpha.fees_account USING btree (account_number)"], ["fees_account_name_5847e7ac_like", "CREATE INDEX fees_account_name_5847e7ac_like ON alpha.fees_account USING btree (name varchar_pattern_ops)"], ["fees_account_account_number_3f6d7533_like", "CREATE INDEX fees_account_account_number_3f6d7533_like ON alpha.fees_account USING btree (account_number varchar_pattern_ops)"]], "fees_concessiontype": [["fees_concessiontype_pkey", "CREATE UNIQUE INDEX fees_concessiontype_pkey ON alpha.fees_concessiontype USING btree (id)"], ["fees_concessiontype_name_key", "CREATE UNIQUE INDEX fees_concessiontype_name_key ON alpha.fees_concessiontype USING btree (name)"], ["fees_concessiontype_name_1f17cbfe_like", "CREATE INDEX fees_concessiontype_name_1f17cbfe_like ON alpha.fees_concessiontype USING btree (name varchar_pattern_ops)"]], "fees_feehead": [["fees_feehead_pkey", "CREATE UNIQUE INDEX fees_feehead_pkey ON alpha.fees_feehead USING btree (id)"], ["fees_feehead_name_key", "CREATE UNIQUE INDEX fees_feehead_name_key ON alpha.fees_feehead USING btree (name)"], ["fees_feehead_income_account_link_id_idx", "CREATE INDEX fees_feehead_income_account_link_id_idx ON alpha.fees_feehead USING btree (income_account_link_id)"]], "fees_feestructure": [["fees_feestructure_pkey", "CREATE UNIQUE INDEX fees_feestructure_pkey ON alpha.fees_feestructure USING btree (id)"], ["fees_feestructure_academic_year_id_730f45dc", "CREATE INDEX fees_feestructure_academic_year_id_730f45dc ON alpha.fees_feestructure USING btree (academic_year_id)"], ["fees_feestructure_term_id_8b1a43ce", "CREATE INDEX fees_feestructure_term_id_8b1a43ce ON alpha.fees_feestructure USING btree (term_id)"]], "fees_feestructure_applicable_classes": [["fees_feestructure_applicable_classes_pkey", "CREATE UNIQUE INDEX fees_feestructure_applicable_classes_pkey ON alpha.fees_feestructure_applicable_classes USING btree (id)"], ["fees_feestructure_applicable_classes_unique", "CREATE UNIQUE INDEX fees_feestructure_applicable_classes_unique ON alpha.fees_feestructure_applicable_classes USING btree (feestructure_id, schoolclass_id)"], ["fees_feestructure_applicable_classes_feestructure_id_idx", "CREATE INDEX fees_feestructure_applicable_classes_feestructure_id_idx ON alpha.fees_feestructure_applicable_classes USING btree (feestructure_id)"], ["fees_feestructure_applicable_classes_schoolclass_id_idx", "CREATE INDEX fees_feestructure_applicable_classes_schoolclass_id_idx ON alpha.fees_feestructure_applicable_classes USING btree (schoolclass_id)"]], "fees_feestructureitem": [["fees_feestructureitem_pkey", "CREATE UNIQUE INDEX fees_feestructureitem_pkey ON alpha.fees_feestructureitem USING btree (id)"], ["fees_feestructureitem_fee_structure_id_fee_hea_66f0a719_uniq", "CREATE UNIQUE INDEX fees_feestructureitem_fee_structure_id_fee_hea_66f0a719_uniq ON alpha.fees_feestructureitem USING btree (fee_structure_id, fee_head_id)"], ["fees_feestructureitem_concession_type_id_b95b9b88", "CREATE INDEX fees_feestructureitem_concession_type_id_b95b9b88 ON alpha.fees_feestructureitem USING btree (concession_type_id)"], ["fees_feestructureitem_fee_head_id_dc43e6fa", "CREATE INDEX fees_feestructureitem_fee_head_id_dc43e6fa ON alpha.fees_feestructureitem USING btree (fee_head_id)"], ["fees_feestructureitem_fee_structure_id_3df07fe1", "CREATE INDEX fees_feestructureitem_fee_structure_id_3df07fe1 ON alpha.fees_feestructureitem USING btree (fee_structure_id)"]], "fees_invoice": [["fees_invoice_pkey", "CREATE UNIQUE INDEX fees_invoice_pkey ON alpha.fees_invoice USING btree (id)"], ["fees_invoice_invoice_number_key", "CREATE UNIQUE INDEX fees_invoice_invoice_number_key ON alpha.fees_invoice USING btree (invoice_number)"], ["fees_invoice_invoice_number_f12ddf7f_like", "CREATE INDEX fees_invoice_invoice_number_f12ddf7f_like ON alpha.fees_invoice USING btree (invoice_number varchar_pattern_ops)"], ["fees_invoice_status_c97327f3", "CREATE INDEX fees_invoice_status_c97327f3 ON alpha.fees_invoice USING btree (status)"], ["fees_invoice_status_c97327f3_like", "CREATE INDEX fees_invoice_status_c97327f3_like ON alpha.fees_invoice USING btree (status varchar_pattern_ops)"], ["fees_invoice_academic_year_id_4fe02078", "CREATE INDEX fees_invoice_academic_year_id_4fe02078 ON alpha.fees_invoice USING btree (academic_year_id)"], ["fees_invoice_created_by_id_e087a26e", "CREATE INDEX fees_invoice_created_by_id_e087a26e ON alpha.fees_invoice USING btree (created_by_id)"], ["fees_invoice_fee_structure_id_cb5097a3", "CREATE INDEX fees_invoice_fee_structure_id_cb5097a3 ON alpha.fees_invoice USING btree (fee_structure_id)"], ["fees_invoice_related_journal_entry_id_95e6b3e8", "CREATE INDEX fees_invoice_related_journal_entry_id_95e6b3e8 ON alpha.fees_invoice USING btree (related_journal_entry_id)"], ["fees_invoice_student_id_c6e2fae9", "CREATE INDEX fees_invoice_student_id_c6e2fae9 ON alpha.fees_invoice USING btree (student_id)"], ["fees_invoice_term_id_0b2de24c", "CREATE INDEX fees_invoice_term_id_0b2de24c ON alpha.fees_invoice USING btree (term_id)"]], "fees_invoicedetail": [["fees_invoicedetail_pkey", "CREATE UNIQUE INDEX fees_invoicedetail_pkey ON alpha.fees_invoicedetail USING btree (id)"], ["fees_invoicedetail_applies_to_line_id_e7593d17", "CREATE INDEX fees_invoicedetail_applies_to_line_id_e7593d17 ON alpha.fees_invoicedetail USING btree (applies_to_line_id)"], ["fees_invoicedetail_concession_type_id_0126f378", "CREATE INDEX fees_invoicedetail_concession_type_id_0126f378 ON alpha.fees_invoicedetail USING btree (concession_type_id)"], ["fees_invoicedetail_fee_head_id_8cd4fa98", "CREATE INDEX fees_invoicedetail_fee_head_id_8cd4fa98 ON alpha.fees_invoicedetail USING btree (fee_head_id)"], ["fees_invoicedetail_invoice_id_75a0a342", "CREATE INDEX fees_invoicedetail_invoice_id_75a0a342 ON alpha.fees_invoicedetail USING btree (invoice_id)"]], "fees_studentconcession": [["fees_studentconcession_student_id_concession_ty_94595c1d_uniq", "CREATE UNIQUE INDEX fees_studentconcession_student_id_concession_ty_94595c1d_uniq ON alpha.fees_studentconcession USING btree (student_id, concession_type_id, academic_year_id, term_id)"], ["fees_studentconcession_pkey", "CREATE UNIQUE INDEX fees_studentconcession_pkey ON alpha.fees_studentconcession USING btree (id)"], ["fees_studentconcession_academic_year_id_494ebe1a", "CREATE INDEX fees_studentconcession_academic_year_id_494ebe1a ON alpha.fees_studentconcession USING btree (academic_year_id)"], ["fees_studentconcession_concession_type_id_2cf81a15", "CREATE INDEX fees_studentconcession_concession_type_id_2cf81a15 ON alpha.fees_studentconcession USING btree (concession_type_id)"], ["fees_studentconcession_granted_by_id_0bb338b7", "CREATE INDEX fees_studentconcession_granted_by_id_0bb338b7 ON alpha.fees_studentconcession USING btree (granted_by_id)"], ["fees_studentconcession_student_id_57dd883e", "CREATE INDEX fees_studentconcession_student_id_57dd883e ON alpha.fees_studentconcession USING btree (student_id)"], ["fees_studentconcession_term_id_df2bba03", "CREATE INDEX fees_studentconcession_term_id_df2bba03 ON alpha.fees_studentconcession USING btree (term_id)"]], "fees_studentfeeallocation": [["fees_studentfeeallocation_pkey", "CREATE UNIQUE INDEX fees_studentfeeallocation_pkey ON alpha.fees_studentfeeallocation USING btree (id)"], ["fees_studentfeeallocatio_student_id_academic_year_d71698b1_uniq", "CREATE UNIQUE INDEX fees_studentfeeallocatio_student_id_academic_year_d71698b1_uniq ON alpha.fees_studentfeeallocation USING btree (student_id, academic_year_id, fee_structure_id, term_id)"], ["fees_studentfeeallocation_academic_year_id_9dec69f3", "CREATE INDEX fees_studentfeeallocation_academic_year_id_9dec69f3 ON alpha.fees_studentfeeallocation USING btree (academic_year_id)"], ["fees_studentfeeallocation_fee_structure_id_d2ecf918", "CREATE INDEX fees_studentfeeallocation_fee_structure_id_d2ecf918 ON alpha.fees_studentfeeallocation USING btree (fee_structure_id)"], ["fees_studentfeeallocation_student_id_d66a65b7", "CREATE INDEX fees_studentfeeallocation_student_id_d66a65b7 ON alpha.fees_studentfeeallocation USING btree (student_id)"], ["fees_studentfeeallocation_term_id_bdf3f734", "CREATE INDEX fees_studentfeeallocation_term_id_bdf3f734 ON alpha.fees_studentfeeallocation USING btree (term_id)"]], "finance_budget": [["finance_budget_pkey", "CREATE UNIQUE INDEX finance_budget_pkey ON alpha.finance_budget USING btree (id)"], ["finance_budget_name_key", "CREATE UNIQUE INDEX finance_budget_name_key ON alpha.finance_budget USING btree (name)"], ["finance_budget_name_56e7652a_like", "CREATE INDEX finance_budget_name_56e7652a_like ON alpha.finance_budget USING btree (name varchar_pattern_ops)"]], "finance_budgetamount": [["finance_budgetamount_pkey", "CREATE UNIQUE INDEX finance_budgetamount_pkey ON alpha.finance_budgetamount USING btree (id)"], ["finance_budgetamount_academic_year_id_fd015deb", "CREATE INDEX finance_budgetamount_academic_year_id_fd015deb ON alpha.finance_budgetamount USING btree (academic_year_id)"], ["finance_budgetamount_term_id_9e43a8dc", "CREATE INDEX finance_budgetamount_term_id_9e43a8dc ON alpha.finance_budgetamount USING btree (term_id)"], ["finance_budgetamount_budget_item_id_7bc4cd50", "CREATE INDEX finance_budgetamount_budget_item_id_7bc4cd50 ON alpha.finance_budgetamount USING btree (budget_item_id)"], ["finance_budgetamount_budget_item_id_academic__b03151f9_uniq", "CREATE UNIQUE INDEX finance_budgetamount_budget_item_id_academic__b03151f9_uniq ON alpha.finance_budgetamount USING btree (budget_item_id, academic_year_id, term_id)"]], "finance_budgetitem": [["finance_budgetitem_pkey", "CREATE UNIQUE INDEX finance_budgetitem_pkey ON alpha.finance_budgetitem USING btree (id)"], ["finance_budgetitem_name_key", "CREATE UNIQUE INDEX finance_budgetitem_name_key ON alpha.finance_budgetitem USING btree (name)"], ["finance_budgetitem_name_279816dc_like", "CREATE INDEX finance_budgetitem_name_279816dc_like ON alpha.finance_budgetitem USING btree (name varchar_pattern_ops)"], ["finance_budgetitem_linked_coa_account_id_0474281c", "CREATE INDEX finance_budgetitem_linked_coa_account_id_0474281c ON alpha.finance_budgetitem USING btree (linked_coa_account_id)"]], "finance_expense": [["finance_expense_pkey", "CREATE UNIQUE INDEX finance_expense_pkey ON alpha.finance_expense USING btree (id)"], ["finance_expense_payment_method_id_2c721e79", "CREATE INDEX finance_expense_payment_method_id_2c721e79 ON alpha.finance_expense USING btree (payment_method_id)"], ["finance_expense_recorded_by_id_d3dcb51f", "CREATE INDEX finance_expense_recorded_by_id_d3dcb51f ON alpha.finance_expense USING btree (recorded_by_id)"], ["finance_expense_category_id_19b015b6", "CREATE INDEX finance_expense_category_id_19b015b6 ON alpha.finance_expense USING btree (category_id)"], ["finance_expense_vendor_id_bef4c853", "CREATE INDEX finance_expense_vendor_id_bef4c853 ON alpha.finance_expense USING btree (vendor_id)"]], "finance_expensecategory": [["finance_expensecategory_pkey", "CREATE UNIQUE INDEX finance_expensecategory_pkey ON alpha.finance_expensecategory USING btree (id)"], ["finance_expensecategory_name_key", "CREATE UNIQUE INDEX finance_expensecategory_name_key ON alpha.finance_expensecategory USING btree (name)"], ["finance_expensecategory_name_5049c457_like", "CREATE INDEX finance_expensecategory_name_5049c457_like ON alpha.finance_expensecategory USING btree (name varchar_pattern_ops)"], ["finance_expensecategory_expense_account_id_da668e48", "CREATE INDEX finance_expensecategory_expense_account_id_da668e48 ON alpha.finance_expensecategory USING btree (expense_account_id)"]], "finance_vendor": [["finance_vendor_pkey", "CREATE UNIQUE INDEX finance_vendor_pkey ON alpha.finance_vendor USING btree (id)"], ["finance_vendor_name_key", "CREATE UNIQUE INDEX finance_vendor_name_key ON alpha.finance_vendor USING btree (name)"], ["finance_vendor_name_006ff117_like", "CREATE INDEX finance_vendor_name_006ff117_like ON alpha.finance_vendor USING btree (name varchar_pattern_ops)"]], "hr_employeeprofile": [["hr_employeeprofile_pkey", "CREATE UNIQUE INDEX hr_employeeprofile_pkey ON alpha.hr_employeeprofile USING btree (user_id)"], ["hr_employeeprofile_employee_id_key", "CREATE UNIQUE INDEX hr_employeeprofile_employee_id_key ON alpha.hr_employeeprofile USING btree (employee_id)"], ["hr_employeeprofile_employee_id_0cbc0608_like", "CREATE INDEX hr_employeeprofile_employee_id_0cbc0608_like ON alpha.hr_employeeprofile USING btree (employee_id varchar_pattern_ops)"]], "hr_graderule": [["hr_graderule_pkey", "CREATE UNIQUE INDEX hr_graderule_pkey ON alpha.hr_graderule USING btree (id)"], ["hr_graderule_grade_id_component_id_5da9c08d_uniq", "CREATE UNIQUE INDEX hr_graderule_grade_id_component_id_5da9c08d_uniq ON alpha.hr_graderule USING btree (grade_id, component_id)"], ["hr_graderule_component_id_0c15791b", "CREATE INDEX hr_graderule_component_id_0c15791b ON alpha.hr_graderule USING btree (component_id)"], ["hr_graderule_grade_id_458beb48", "CREATE INDEX hr_graderule_grade_id_458beb48 ON alpha.hr_graderule USING btree (grade_id)"]], "hr_leavebalance": [["hr_leavebalance_pkey", "CREATE UNIQUE INDEX hr_leavebalance_pkey ON alpha.hr_leavebalance USING btree (id)"], ["hr_leavebalance_employee_id_bd7dc9c1", "CREATE INDEX hr_leavebalance_employee_id_bd7dc9c1 ON alpha.hr_leavebalance USING btree (employee_id)"], ["hr_leavebalance_leave_type_id_e7032b80", "CREATE INDEX hr_leavebalance_leave_type_id_e7032b80 ON alpha.hr_leavebalance USING btree (leave_type_id)"], ["hr_leavebalance_employee_id_leave_type_id_year_uniq", "CREATE UNIQUE INDEX hr_leavebalance_employee_id_leave_type_id_year_uniq ON alpha.hr_leavebalance USING btree (employee_id, leave_type_id, year)"]], "hr_leaverequest": [["hr_leaverequest_pkey", "CREATE UNIQUE INDEX hr_leaverequest_pkey ON alpha.hr_leaverequest USING btree (id)"], ["hr_leaverequest_approved_by_id_1584e857", "CREATE INDEX hr_leaverequest_approved_by_id_1584e857 ON alpha.hr_leaverequest USING btree (approved_by_id)"], ["hr_leaverequest_employee_id_7b0019ae", "CREATE INDEX hr_leaverequest_employee_id_7b0019ae ON alpha.hr_leaverequest USING btree (employee_id)"], ["hr_leaverequest_leave_type_id_a69fe07b", "CREATE INDEX hr_leaverequest_leave_type_id_a69fe07b ON alpha.hr_leaverequest USING btree (leave_type_id)"]], "hr_leavetype": [["hr_leavetype_pkey", "CREATE UNIQUE INDEX hr_leavetype_pkey ON alpha.hr_leavetype USING btree (id)"], ["hr_leavetype_name_key", "CREATE UNIQUE INDEX hr_leavetype_name_key ON alpha.hr_leavetype USING btree (name)"], ["hr_leavetype_name_401ef0d1_like", "CREATE INDEX hr_leavetype_name_401ef0d1_like ON alpha.hr_leavetype USING btree (name varchar_pattern_ops)"]], "hr_payrollrun": [["hr_payrollrun_pkey", "CREATE UNIQUE INDEX hr_payrollrun_pkey ON alpha.hr_payrollrun USING btree (id)"], ["hr_payrollrun_processed_by_id_034b4248", "CREATE INDEX hr_payrollrun_processed_by_id_034b4248 ON alpha.hr_payrollrun USING btree (processed_by_id)"]], "hr_payslip": [["hr_payslip_pkey", "CREATE UNIQUE INDEX hr_payslip_pkey ON alpha.hr_payslip USING btree (id)"], ["hr_payslip_staff_user_id_e70a7aa9", "CREATE INDEX hr_payslip_staff_user_id_e70a7aa9 ON alpha.hr_payslip USING btree (staff_member_id)"], ["hr_payslip_payroll_run_id_staff_member_id_b6ceea3a_uniq", "CREATE UNIQUE INDEX hr_payslip_payroll_run_id_staff_member_id_b6ceea3a_uniq ON alpha.hr_payslip USING btree (payroll_run_id, staff_member_id)"], ["hr_payslip_payroll_run_id_af85678b", "CREATE INDEX hr_payslip_payroll_run_id_af85678b ON alpha.hr_payslip USING btree (payroll_run_id)"]], "hr_paysliplineitem": [["hr_paysliplineitem_pkey", "CREATE UNIQUE INDEX hr_paysliplineitem_pkey ON alpha.hr_paysliplineitem USING btree (id)"], ["hr_paysliplineitem_payslip_id_8110fb3f", "CREATE INDEX hr_paysliplineitem_payslip_id_8110fb3f ON alpha.hr_paysliplineitem USING btree (payslip_id)"], ["hr_paysliplineitem_source_component_id_f45ffb82", "CREATE INDEX hr_paysliplineitem_source_component_id_f45ffb82 ON alpha.hr_paysliplineitem USING btree (source_component_id)"]], "hr_salarycomponent": [["hr_salarycomponent_name_95438b93_uniq", "CREATE UNIQUE INDEX hr_salarycomponent_name_95438b93_uniq ON alpha.hr_salarycomponent USING btree (name)"], ["hr_salarycomponent_name_95438b93_like", "CREATE INDEX hr_salarycomponent_name_95438b93_like ON alpha.hr_salarycomponent USING btree (name varchar_pattern_ops)"], ["hr_salarycomponent_pkey", "CREATE UNIQUE INDEX hr_salarycomponent_pkey ON alpha.hr_salarycomponent USING btree (id)"]], "hr_salarygrade": [["hr_salarygrade_pkey", "CREATE UNIQUE INDEX hr_salarygrade_pkey ON alpha.hr_salarygrade USING btree (id)"], ["hr_salarygrade_name_1824c6e6_uniq", "CREATE UNIQUE INDEX hr_salarygrade_name_1824c6e6_uniq ON alpha.hr_salarygrade USING btree (name)"]], "hr_salarygradecomponent": [["hr_salarygradecomponent_pkey", "CREATE UNIQUE INDEX hr_salarygradecomponent_pkey ON alpha.hr_salarygradecomponent USING btree (id)"], ["hr_salarygradecomponent_grade_id_component_id_125e67a8_uniq", "CREATE UNIQUE INDEX hr_salarygradecomponent_grade_id_component_id_125e67a8_uniq ON alpha.hr_salarygradecomponent USING btree (grade_id, component_id)"], ["hr_salarygradecomponent_component_id_21bafc82", "CREATE INDEX hr_salarygradecomponent_component_id_21bafc82 ON alpha.hr_salarygradecomponent USING btree (component_id)"], ["hr_salarygradecomponent_grade_id_c5c6e463", "CREATE INDEX hr_salarygradecomponent_grade_id_c5c6e463 ON alpha.hr_salarygradecomponent USING btree (grade_id)"]], "hr_salarystructurecomponent": [["hr_salarystructurecomponent_pkey", "CREATE UNIQUE INDEX hr_salarystructurecomponent_pkey ON alpha.hr_salarystructurecomponent USING btree (id)"], ["hr_salarystructurecompon_salary_structure_id_comp_acdf8590_uniq", "CREATE UNIQUE INDEX hr_salarystructurecompon_salary_structure_id_comp_acdf8590_uniq ON alpha.hr_salarystructurecomponent USING btree (salary_structure_id, component_id)"], ["hr_salarystructurecomponent_component_id_9a63f21e", "CREATE INDEX hr_salarystructurecomponent_component_id_9a63f21e ON alpha.hr_salarystructurecomponent USING btree (component_id)"], ["hr_salarystructurecomponent_salary_structure_id_6b1b6db5", "CREATE INDEX hr_salarystructurecomponent_salary_structure_id_6b1b6db5 ON alpha.hr_salarystructurecomponent USING btree (salary_structure_id)"]], "hr_staffsalary": [["hr_staffsalary_pkey", "CREATE UNIQUE INDEX hr_staffsalary_pkey ON alpha.hr_staffsalary USING btree (id)"], ["hr_staffsalary_staff_member_id_key", "CREATE UNIQUE INDEX hr_staffsalary_staff_member_id_key ON alpha.hr_staffsalary USING btree (staff_member_id)"], ["hr_staffsalary_grade_id_78ab9c67", "CREATE INDEX hr_staffsalary_grade_id_78ab9c67 ON alpha.hr_staffsalary USING btree (grade_id)"]], "hr_staffsalarystructure": [["hr_staffsalarystructure_pkey", "CREATE UNIQUE INDEX hr_staffsalarystructure_pkey ON alpha.hr_staffsalarystructure USING btree (id)"], ["hr_staffsalarystructure_staff_user_id_key", "CREATE UNIQUE INDEX hr_staffsalarystructure_staff_user_id_key ON alpha.hr_staffsalarystructure USING btree (staff_user_id)"]], "hr_statutorydeduction": [["hr_statutorydeduction_pkey", "CREATE UNIQUE INDEX hr_statutorydeduction_pkey ON alpha.hr_statutorydeduction USING btree (id)"], ["hr_statutorydeduction_name_key", "CREATE UNIQUE INDEX hr_statutorydeduction_name_key ON alpha.hr_statutorydeduction USING btree (name)"], ["hr_statutorydeduction_name_bf4b77cf_like", "CREATE INDEX hr_statutorydeduction_name_bf4b77cf_like ON alpha.hr_statutorydeduction USING btree (name varchar_pattern_ops)"]], "hr_taxbracket": [["hr_taxbracket_pkey", "CREATE UNIQUE INDEX hr_taxbracket_pkey ON alpha.hr_taxbracket USING btree (id)"]], "payments_payment": [["payments_payment_pkey", "CREATE UNIQUE INDEX payments_payment_pkey ON alpha.payments_payment USING btree (id)"], ["payments_payment_academic_year_id_5287e9d6", "CREATE INDEX payments_payment_academic_year_id_5287e9d6 ON alpha.payments_payment USING btree (academic_year_id)"], ["payments_payment_created_by_id_28f0e284", "CREATE INDEX payments_payment_created_by_id_28f0e284 ON alpha.payments_payment USING btree (created_by_id)"], ["payments_payment_parent_payer_id_d6961b02", "CREATE INDEX payments_payment_parent_payer_id_d6961b02 ON alpha.payments_payment USING btree (parent_payer_id)"], ["payments_payment_processed_by_staff_id_1dc7a0a7", "CREATE INDEX payments_payment_processed_by_staff_id_1dc7a0a7 ON alpha.payments_payment USING btree (processed_by_staff_id)"], ["payments_payment_student_id_b5fab56a", "CREATE INDEX payments_payment_student_id_b5fab56a ON alpha.payments_payment USING btree (student_id)"], ["payments_payment_payment_method_id_c909ff25", "CREATE INDEX payments_payment_payment_method_id_c909ff25 ON alpha.payments_payment USING btree (payment_method_id)"], ["payments_payment_receipt_number_key", "CREATE UNIQUE INDEX payments_payment_receipt_number_key ON alpha.payments_payment USING btree (receipt_number)"], ["payments_payment_receipt_number_dd09394e_like", "CREATE INDEX payments_payment_receipt_number_dd09394e_like ON alpha.payments_payment USING btree (receipt_number varchar_pattern_ops)"]], "payments_paymentallocation": [["payments_paymentallocation_pkey", "CREATE UNIQUE INDEX payments_paymentallocation_pkey ON alpha.payments_paymentallocation USING btree (id)"], ["payments_paymentallocation_payment_id_invoice_id_3dccc35e_uniq", "CREATE UNIQUE INDEX payments_paymentallocation_payment_id_invoice_id_3dccc35e_uniq ON alpha.payments_paymentallocation USING btree (payment_id, invoice_id)"], ["payments_paymentallocation_invoice_id_c7c856f3", "CREATE INDEX payments_paymentallocation_invoice_id_c7c856f3 ON alpha.payments_paymentallocation USING btree (invoice_id)"], ["payments_paymentallocation_payment_id_17cbc394", "CREATE INDEX payments_paymentallocation_payment_id_17cbc394 ON alpha.payments_paymentallocation USING btree (payment_id)"]], "payments_paymentmethod": [["payments_paymentmethod_pkey", "CREATE UNIQUE INDEX payments_paymentmethod_pkey ON alpha.payments_paymentmethod USING btree (id)"], ["payments_paymentmethod_name_key", "CREATE UNIQUE INDEX payments_paymentmethod_name_key ON alpha.payments_paymentmethod USING btree (name)"], ["payments_paymentmethod_name_1e111573_like", "CREATE INDEX payments_paymentmethod_name_1e111573_like ON alpha.payments_paymentmethod USING btree (name varchar_pattern_ops)"], ["payments_paymentmethod_linked_account_id_70d00568", "CREATE INDEX payments_paymentmethod_linked_account_id_70d00568 ON alpha.payments_paymentmethod USING btree (linked_account_id)"]], "portal_admin_adminactivitylog": [["portal_admin_adminactivitylog_pkey", "CREATE UNIQUE INDEX portal_admin_adminactivitylog_pkey ON alpha.portal_admin_adminactivitylog USING btree (id)"], ["portal_admin_adminactivitylog_action_type_5277d7f3", "CREATE INDEX portal_admin_adminactivitylog_action_type_5277d7f3 ON alpha.portal_admin_adminactivitylog USING btree (action_type)"], ["portal_admin_adminactivitylog_action_type_5277d7f3_like", "CREATE INDEX portal_admin_adminactivitylog_action_type_5277d7f3_like ON alpha.portal_admin_adminactivitylog USING btree (action_type varchar_pattern_ops)"], ["portal_admin_adminactivitylog_timestamp_088efd49", "CREATE INDEX portal_admin_adminactivitylog_timestamp_088efd49 ON alpha.portal_admin_adminactivitylog USING btree (\"timestamp\")"], ["portal_admin_adminactivitylog_target_object_id_9ee19228", "CREATE INDEX portal_admin_adminactivitylog_target_object_id_9ee19228 ON alpha.portal_admin_adminactivitylog USING btree (target_object_id)"], ["portal_admin_adminactivitylog_target_object_id_9ee19228_like", "CREATE INDEX portal_admin_adminactivitylog_target_object_id_9ee19228_like ON alpha.portal_admin_adminactivitylog USING btree (target_object_id varchar_pattern_ops)"], ["portal_admin_adminactivitylog_staff_user_id_b5542baf", "CREATE INDEX portal_admin_adminactivitylog_staff_user_id_b5542baf ON alpha.portal_admin_adminactivitylog USING btree (staff_user_id)"], ["portal_admin_adminactivitylog_target_content_type_id_cd3c915a", "CREATE INDEX portal_admin_adminactivitylog_target_content_type_id_cd3c915a ON alpha.portal_admin_adminactivitylog USING btree (target_content_type_id)"], ["portal_admin_adminactivitylog_tenant_id_46051055", "CREATE INDEX portal_admin_adminactivitylog_tenant_id_46051055 ON alpha.portal_admin_adminactivitylog USING btree (tenant_id)"], ["portal_admin_adminactivitylog_user_id_137e02c8", "CREATE INDEX portal_admin_adminactivitylog_user_id_137e02c8 ON alpha.portal_admin_adminactivitylog USING btree (user_id)"]], "school_calendar_eventattendee": [["school_calendar_eventattendee_pkey", "CREATE UNIQUE INDEX school_calendar_eventattendee_pkey ON alpha.school_calendar_eventattendee USING btree (id)"], ["school_calendar_eventattendee_user_id_9729f782", "CREATE INDEX school_calendar_eventattendee_user_id_9729f782 ON alpha.school_calendar_eventattendee USING btree (user_id)"], ["school_calendar_eventattendee_event_id_b6383215", "CREATE INDEX school_calendar_eventattendee_event_id_b6383215 ON alpha.school_calendar_eventattendee USING btree (event_id)"]], "school_calendar_eventcategory": [["school_calendar_eventcategory_pkey", "CREATE UNIQUE INDEX school_calendar_eventcategory_pkey ON alpha.school_calendar_eventcategory USING btree (id)"]], "school_calendar_schoolevent": [["school_calendar_schoolevent_pkey", "CREATE UNIQUE INDEX school_calendar_schoolevent_pkey ON alpha.school_calendar_schoolevent USING btree (id)"], ["school_cale_start_d_c27ace_idx", "CREATE INDEX school_cale_start_d_c27ace_idx ON alpha.school_calendar_schoolevent USING btree (start_date, end_date)"], ["school_cale_event_t_5c7941_idx", "CREATE INDEX school_cale_event_t_5c7941_idx ON alpha.school_calendar_schoolevent USING btree (event_type)"], ["school_cale_is_publ_162aa9_idx", "CREATE INDEX school_cale_is_publ_162aa9_idx ON alpha.school_calendar_schoolevent USING btree (is_public, is_active)"], ["school_calendar_schoolevent_category_id_88f7d536", "CREATE INDEX school_calendar_schoolevent_category_id_88f7d536 ON alpha.school_calendar_schoolevent USING btree (category_id)"], ["school_calendar_schoolevent_created_by_id_8abf6bd2", "CREATE INDEX school_calendar_schoolevent_created_by_id_8abf6bd2 ON alpha.school_calendar_schoolevent USING btree (created_by_id)"], ["school_calendar_schoolevent_created_by_staff_id_7580a2a5", "CREATE INDEX school_calendar_schoolevent_created_by_staff_id_7580a2a5 ON alpha.school_calendar_schoolevent USING btree (created_by_staff_id)"]], "schools_academicsetting": [["schools_academicsetting_pkey", "CREATE UNIQUE INDEX schools_academicsetting_pkey ON alpha.schools_academicsetting USING btree (id)"], ["schools_academicsetting_current_academic_year_id_bbefa819", "CREATE INDEX schools_academicsetting_current_academic_year_id_bbefa819 ON alpha.schools_academicsetting USING btree (current_academic_year_id)"]], "schools_academicyear": [["schools_academicyear_pkey", "CREATE UNIQUE INDEX schools_academicyear_pkey ON alpha.schools_academicyear USING btree (id)"], ["schools_academicyear_name_key", "CREATE UNIQUE INDEX schools_academicyear_name_key ON alpha.schools_academicyear USING btree (name)"], ["schools_academicyear_name_f8951877_like", "CREATE INDEX schools_academicyear_name_f8951877_like ON alpha.schools_academicyear USING btree (name varchar_pattern_ops)"]], "schools_country": [["schools_country_pkey", "CREATE UNIQUE INDEX schools_country_pkey ON alpha.schools_country USING btree (id)"]], "schools_invoicesequence": [["schools_invoicesequence_pkey", "CREATE UNIQUE INDEX schools_invoicesequence_pkey ON alpha.schools_invoicesequence USING btree (id)"]], "schools_receiptsequence": [["schools_receiptsequence_pkey", "CREATE UNIQUE INDEX schools_receiptsequence_pkey ON alpha.schools_receiptsequence USING btree (id)"]], "schools_schoolclass": [["schools_schoolclass_pkey", "CREATE UNIQUE INDEX schools_schoolclass_pkey ON alpha.schools_schoolclass USING btree (id)"], ["schools_schoolclass_is_active_5a950647", "CREATE INDEX schools_schoolclass_is_active_5a950647 ON alpha.schools_schoolclass USING btree (is_active)"], ["schools_schoolclass_name_c44938eb_uniq", "CREATE UNIQUE INDEX schools_schoolclass_name_c44938eb_uniq ON alpha.schools_schoolclass USING btree (name)"]], "schools_schoolprofile": [["schools_schoolprofile_current_academic_year_id_8ad46456", "CREATE INDEX schools_schoolprofile_current_academic_year_id_8ad46456 ON alpha.schools_schoolprofile USING btree (current_academic_year_id)"], ["schools_schoolprofile_default_accounts_receivabl_e522b278", "CREATE INDEX schools_schoolprofile_default_accounts_receivabl_e522b278 ON alpha.schools_schoolprofile USING btree (default_accounts_receivable_coa_id)"], ["schools_schoolprofile_default_bank_coa_id_2e75e2b8", "CREATE INDEX schools_schoolprofile_default_bank_coa_id_2e75e2b8 ON alpha.schools_schoolprofile USING btree (default_bank_coa_id)"], ["schools_schoolprofile_default_cash_coa_id_da0d87f0", "CREATE INDEX schools_schoolprofile_default_cash_coa_id_da0d87f0 ON alpha.schools_schoolprofile USING btree (default_cash_coa_id)"], ["schools_schoolprofile_default_discount_given_coa_id_0acae16d", "CREATE INDEX schools_schoolprofile_default_discount_given_coa_id_0acae16d ON alpha.schools_schoolprofile USING btree (default_discount_given_coa_id)"], ["schools_schoolprofile_default_expense_coa_id_18c0d202", "CREATE INDEX schools_schoolprofile_default_expense_coa_id_18c0d202 ON alpha.schools_schoolprofile USING btree (default_expense_coa_id)"], ["schools_schoolprofile_default_fee_income_coa_id_9fe91809", "CREATE INDEX schools_schoolprofile_default_fee_income_coa_id_9fe91809 ON alpha.schools_schoolprofile USING btree (default_fee_income_coa_id)"], ["schools_schoolprofile_pkey", "CREATE UNIQUE INDEX schools_schoolprofile_pkey ON alpha.schools_schoolprofile USING btree (id)"]], "schools_section": [["schools_section_pkey", "CREATE UNIQUE INDEX schools_section_pkey ON alpha.schools_section USING btree (id)"], ["schools_section_name_school_class_id_1727d65f_uniq", "CREATE UNIQUE INDEX schools_section_name_school_class_id_1727d65f_uniq ON alpha.schools_section USING btree (name, school_class_id)"], ["schools_section_school_class_id_2732815a", "CREATE INDEX schools_section_school_class_id_2732815a ON alpha.schools_section USING btree (school_class_id)"], ["schools_section_class_teacher_id_97d67a70", "CREATE INDEX schools_section_class_teacher_id_97d67a70 ON alpha.schools_section USING btree (class_teacher_id)"]], "schools_staffuser": [["schools_staffuser_pkey", "CREATE UNIQUE INDEX schools_staffuser_pkey ON alpha.schools_staffuser USING btree (id)"], ["schools_staffuser_email_key", "CREATE UNIQUE INDEX schools_staffuser_email_key ON alpha.schools_staffuser USING btree (email)"], ["schools_staffuser_email_ed64ca56_like", "CREATE INDEX schools_staffuser_email_ed64ca56_like ON alpha.schools_staffuser USING btree (email varchar_pattern_ops)"]], "schools_staffuser_groups": [["schools_staffuser_groups_pkey", "CREATE UNIQUE INDEX schools_staffuser_groups_pkey ON alpha.schools_staffuser_groups USING btree (id)"], ["schools_staffuser_groups_staffuser_id_group_id_2595d24f_uniq", "CREATE UNIQUE INDEX schools_staffuser_groups_staffuser_id_group_id_2595d24f_uniq ON alpha.schools_staffuser_groups USING btree (staffuser_id, group_id)"], ["schools_staffuser_groups_staffuser_id_d8796370", "CREATE INDEX schools_staffuser_groups_staffuser_id_d8796370 ON alpha.schools_staffuser_groups USING btree (staffuser_id)"], ["schools_staffuser_groups_group_id_282cab88", "CREATE INDEX schools_staffuser_groups_group_id_282cab88 ON alpha.schools_staffuser_groups USING btree (group_id)"]], "schools_staffuser_user_permissions": [["schools_staffuser_user_permissions_pkey", "CREATE UNIQUE INDEX schools_staffuser_user_permissions_pkey ON alpha.schools_staffuser_user_permissions USING btree (id)"], ["schools_staffuser_user_p_staffuser_id_permission__3a62d52a_uniq", "CREATE UNIQUE INDEX schools_staffuser_user_p_staffuser_id_permission__3a62d52a_uniq ON alpha.schools_staffuser_user_permissions USING btree (staffuser_id, permission_id)"], ["schools_staffuser_user_permissions_staffuser_id_15a429df", "CREATE INDEX schools_staffuser_user_permissions_staffuser_id_15a429df ON alpha.schools_staffuser_user_permissions USING btree (staffuser_id)"], ["schools_staffuser_user_permissions_permission_id_97ee70ba", "CREATE INDEX schools_staffuser_user_permissions_permission_id_97ee70ba ON alpha.schools_staffuser_user_permissions USING btree (permission_id)"]], "schools_term": [["schools_term_pkey", "CREATE UNIQUE INDEX schools_term_pkey ON alpha.schools_term USING btree (id)"], ["schools_term_academic_year_id_name_c078c2af_uniq", "CREATE UNIQUE INDEX schools_term_academic_year_id_name_c078c2af_uniq ON alpha.schools_term USING btree (academic_year_id, name)"], ["schools_term_academic_year_id_303ad53e", "CREATE INDEX schools_term_academic_year_id_303ad53e ON alpha.schools_term USING btree (academic_year_id)"]], "students_parentuser": [["students_parentuser_pkey", "CREATE UNIQUE INDEX students_parentuser_pkey ON alpha.students_parentuser USING btree (id)"], ["students_parentuser_email_key", "CREATE UNIQUE INDEX students_parentuser_email_key ON alpha.students_parentuser USING btree (email)"], ["students_parentuser_email_0ee66aac_like", "CREATE INDEX students_parentuser_email_0ee66aac_like ON alpha.students_parentuser USING btree (email varchar_pattern_ops)"]], "students_parentuser_groups": [["students_parentuser_groups_pkey", "CREATE UNIQUE INDEX students_parentuser_groups_pkey ON alpha.students_parentuser_groups USING btree (id)"], ["students_parentuser_groups_parentuser_id_group_id_7250462b_uniq", "CREATE UNIQUE INDEX students_parentuser_groups_parentuser_id_group_id_7250462b_uniq ON alpha.students_parentuser_groups USING btree (parentuser_id, group_id)"], ["students_parentuser_groups_parentuser_id_046c895a", "CREATE INDEX students_parentuser_groups_parentuser_id_046c895a ON alpha.students_parentuser_groups USING btree (parentuser_id)"], ["students_parentuser_groups_group_id_36ee7381", "CREATE INDEX students_parentuser_groups_group_id_36ee7381 ON alpha.students_parentuser_groups USING btree (group_id)"]], "students_parentuser_user_permissions": [["students_parentuser_user_permissions_pkey", "CREATE UNIQUE INDEX students_parentuser_user_permissions_pkey ON alpha.students_parentuser_user_permissions USING btree (id)"], ["students_parentuser_user_parentuser_id_permission_db90c74e_uniq", "CREATE UNIQUE INDEX students_parentuser_user_parentuser_id_permission_db90c74e_uniq ON alpha.students_parentuser_user_permissions USING btree (parentuser_id, permission_id)"], ["students_parentuser_user_permissions_parentuser_id_3abc264a", "CREATE INDEX students_parentuser_user_permissions_parentuser_id_3abc264a ON alpha.students_parentuser_user_permissions USING btree (parentuser_id)"], ["students_parentuser_user_permissions_permission_id_1279c4cb", "CREATE INDEX students_parentuser_user_permissions_permission_id_1279c4cb ON alpha.students_parentuser_user_permissions USING btree (permission_id)"]], "students_student": [["students_student_pkey", "CREATE UNIQUE INDEX students_student_pkey ON alpha.students_student USING btree (id)"], ["students_student_admission_number_key", "CREATE UNIQUE INDEX students_student_admission_number_key ON alpha.students_student USING btree (admission_number)"], ["students_student_admission_number_87d096c3_like", "CREATE INDEX students_student_admission_number_87d096c3_like ON alpha.students_student USING btree (admission_number varchar_pattern_ops)"], ["students_student_current_class_id_cf5f558b", "CREATE INDEX students_student_current_class_id_cf5f558b ON alpha.students_student USING btree (current_class_id)"], ["students_student_current_section_id_3239129f", "CREATE INDEX students_student_current_section_id_3239129f ON alpha.students_student USING btree (current_section_id)"], ["students_student_created_by_id_5fd5c0b3", "CREATE INDEX students_student_created_by_id_5fd5c0b3 ON alpha.students_student USING btree (created_by_id)"]], "students_student_parents": [["students_student_parents_pkey", "CREATE UNIQUE INDEX students_student_parents_pkey ON alpha.students_student_parents USING btree (id)"], ["students_student_parents_student_id_parentuser_id_6dcb9a33_uniq", "CREATE UNIQUE INDEX students_student_parents_student_id_parentuser_id_6dcb9a33_uniq ON alpha.students_student_parents USING btree (student_id, parentuser_id)"], ["students_student_parents_student_id_987ca87a", "CREATE INDEX students_student_parents_student_id_987ca87a ON alpha.students_student_parents USING btree (student_id)"], ["students_student_parents_parentuser_id_6b614841", "CREATE INDEX students_student_parents_parentuser_id_6b614841 ON alpha.students_student_parents USING btree (parentuser_id)"]]}, "constraints": {"announcements_announcement": [["announcements_announ_author_id_47b59c69_fk_schools_s", "FOREIGN KEY", "author_id", "schools_staffuser", "id"], ["announcements_announ_tenant_id_f2ddd667_fk_tenants_s", "FOREIGN KEY", "tenant_id", "tenants_school", "id"], ["announcements_announcement_pkey", "PRIMARY KEY", "id", "announcements_announcement", "id"], ["announcements_announcement_pkey", "PRIMARY KEY", "id", "announcements_announcement", "id"], ["announcements_announcement_pkey", "PRIMARY KEY", "id", "announcements_announcement", "id"], ["announcements_announcement_pkey", "PRIMARY KEY", "id", "announcements_announcement", "id"], ["116045_117008_1_not_null", "CHECK", null, null, null], ["116045_117008_2_not_null", "CHECK", null, null, null], ["116045_117008_3_not_null", "CHECK", null, null, null], ["116045_117008_6_not_null", "CHECK", null, null, null], ["116045_117008_8_not_null", "CHECK", null, null, null], ["116045_117008_9_not_null", "CHECK", null, null, null], ["116045_117008_10_not_null", "CHECK", null, null, null], ["116045_117008_11_not_null", "CHECK", null, null, null], ["116045_117008_13_not_null", "CHECK", null, null, null], ["116045_117008_14_not_null", "CHECK", null, null, null], ["116045_117008_15_not_null", "CHECK", null, null, null]], "announcements_announcement_target_tenant_staff_groups": [["announcements_announ_announcement_id_d30296c8_fk_announcem", "FOREIGN KEY", "announcement_id", "announcements_announcement", "id"], ["announcements_announ_group_id_40a184a0_fk_auth_grou", "FOREIGN KEY", "group_id", "auth_group", "id"], ["announcements_announceme_announcement_id_group_id_be9cdcd1_uniq", "UNIQUE", "announcement_id", "announcements_announcement_target_tenant_staff_groups", "announcement_id"], ["announcements_announceme_announcement_id_group_id_be9cdcd1_uniq", "UNIQUE", "announcement_id", "announcements_announcement_target_tenant_staff_groups", "group_id"], ["announcements_announceme_announcement_id_group_id_be9cdcd1_uniq", "UNIQUE", "group_id", "announcements_announcement_target_tenant_staff_groups", "announcement_id"], ["announcements_announceme_announcement_id_group_id_be9cdcd1_uniq", "UNIQUE", "group_id", "announcements_announcement_target_tenant_staff_groups", "group_id"], ["announcements_announcement_target_tenant_staff_groups_pkey", "PRIMARY KEY", "id", "announcements_announcement_target_tenant_staff_groups", "id"], ["116045_117178_1_not_null", "CHECK", null, null, null], ["116045_117178_2_not_null", "CHECK", null, null, null], ["116045_117178_3_not_null", "CHECK", null, null, null]], "auth_group": [["auth_group_name_key", "UNIQUE", "name", "auth_group", "name"], ["auth_group_name_key", "UNIQUE", "name", "auth_group", "name"], ["auth_group_name_key", "UNIQUE", "name", "auth_group", "name"], ["auth_group_name_key", "UNIQUE", "name", "auth_group", "name"], ["auth_group_name_key", "UNIQUE", "name", "auth_group", "name"], ["auth_group_name_key", "UNIQUE", "name", "auth_group", "name"], ["auth_group_name_key", "UNIQUE", "name", "auth_group", "name"], ["auth_group_name_key", "UNIQUE", "name", "auth_group", "name"], ["auth_group_name_key", "UNIQUE", "name", "auth_group", "name"], ["auth_group_pkey", "PRIMARY KEY", "id", "auth_group", "id"], ["auth_group_pkey", "PRIMARY KEY", "id", "auth_group", "id"], ["auth_group_pkey", "PRIMARY KEY", "id", "auth_group", "id"], ["auth_group_pkey", "PRIMARY KEY", "id", "auth_group", "id"], ["auth_group_pkey", "PRIMARY KEY", "id", "auth_group", "id"], ["auth_group_pkey", "PRIMARY KEY", "id", "auth_group", "id"], ["auth_group_pkey", "PRIMARY KEY", "id", "auth_group", "id"], ["auth_group_pkey", "PRIMARY KEY", "id", "auth_group", "id"], ["auth_group_pkey", "PRIMARY KEY", "id", "auth_group", "id"], ["116045_116069_1_not_null", "CHECK", null, null, null], ["116045_116069_2_not_null", "CHECK", null, null, null]], "auth_group_permissions": [["auth_group_permissio_permission_id_84c5c92e_fk_auth_perm", "FOREIGN KEY", "permission_id", "auth_permission", "id"], ["auth_group_permissio_permission_id_84c5c92e_fk_auth_perm", "FOREIGN KEY", "permission_id", "auth_permission", "id"], ["auth_group_permissio_permission_id_84c5c92e_fk_auth_perm", "FOREIGN KEY", "permission_id", "auth_permission", "id"], ["auth_group_permissio_permission_id_84c5c92e_fk_auth_perm", "FOREIGN KEY", "permission_id", "auth_permission", "id"], ["auth_group_permissions_group_id_b120cbf9_fk_auth_group_id", "FOREIGN KEY", "group_id", "auth_group", "id"], ["auth_group_permissions_group_id_b120cbf9_fk_auth_group_id", "FOREIGN KEY", "group_id", "auth_group", "id"], ["auth_group_permissions_group_id_b120cbf9_fk_auth_group_id", "FOREIGN KEY", "group_id", "auth_group", "id"], ["auth_group_permissions_group_id_b120cbf9_fk_auth_group_id", "FOREIGN KEY", "group_id", "auth_group", "id"], ["auth_group_permissions_group_id_permission_id_0cd325b0_uniq", "UNIQUE", "group_id", "auth_group_permissions", "group_id"], ["auth_group_permissions_group_id_permission_id_0cd325b0_uniq", "UNIQUE", "group_id", "auth_group_permissions", "permission_id"], ["auth_group_permissions_group_id_permission_id_0cd325b0_uniq", "UNIQUE", "group_id", "auth_group_permissions", "group_id"], ["auth_group_permissions_group_id_permission_id_0cd325b0_uniq", "UNIQUE", "group_id", "auth_group_permissions", "permission_id"], ["auth_group_permissions_group_id_permission_id_0cd325b0_uniq", "UNIQUE", "permission_id", "auth_group_permissions", "group_id"], ["auth_group_permissions_group_id_permission_id_0cd325b0_uniq", "UNIQUE", "permission_id", "auth_group_permissions", "permission_id"], ["auth_group_permissions_group_id_permission_id_0cd325b0_uniq", "UNIQUE", "permission_id", "auth_group_permissions", "group_id"], ["auth_group_permissions_group_id_permission_id_0cd325b0_uniq", "UNIQUE", "permission_id", "auth_group_permissions", "permission_id"], ["auth_group_permissions_group_id_permission_id_0cd325b0_uniq", "UNIQUE", "group_id", "auth_group_permissions", "group_id"], ["auth_group_permissions_group_id_permission_id_0cd325b0_uniq", "UNIQUE", "group_id", "auth_group_permissions", "permission_id"], ["auth_group_permissions_group_id_permission_id_0cd325b0_uniq", "UNIQUE", "group_id", "auth_group_permissions", "group_id"], ["auth_group_permissions_group_id_permission_id_0cd325b0_uniq", "UNIQUE", "group_id", "auth_group_permissions", "permission_id"], ["auth_group_permissions_group_id_permission_id_0cd325b0_uniq", "UNIQUE", "permission_id", "auth_group_permissions", "group_id"], ["auth_group_permissions_group_id_permission_id_0cd325b0_uniq", "UNIQUE", "permission_id", "auth_group_permissions", "permission_id"], ["auth_group_permissions_group_id_permission_id_0cd325b0_uniq", "UNIQUE", "permission_id", "auth_group_permissions", "group_id"], ["auth_group_permissions_group_id_permission_id_0cd325b0_uniq", "UNIQUE", "permission_id", "auth_group_permissions", "permission_id"], ["auth_group_permissions_pkey", "PRIMARY KEY", "id", "auth_group_permissions", "id"], ["auth_group_permissions_pkey", "PRIMARY KEY", "id", "auth_group_permissions", "id"], ["auth_group_permissions_pkey", "PRIMARY KEY", "id", "auth_group_permissions", "id"], ["auth_group_permissions_pkey", "PRIMARY KEY", "id", "auth_group_permissions", "id"], ["116045_116077_1_not_null", "CHECK", null, null, null], ["116045_116077_2_not_null", "CHECK", null, null, null], ["116045_116077_3_not_null", "CHECK", null, null, null]], "auth_permission": [["auth_permission_content_type_id_2f476e4b_fk_django_co", "FOREIGN KEY", "content_type_id", "django_content_type", "id"], ["auth_permission_content_type_id_2f476e4b_fk_django_co", "FOREIGN KEY", "content_type_id", "django_content_type", "id"], ["auth_permission_content_type_id_2f476e4b_fk_django_co", "FOREIGN KEY", "content_type_id", "django_content_type", "id"], ["auth_permission_content_type_id_2f476e4b_fk_django_co", "FOREIGN KEY", "content_type_id", "django_content_type", "id"], ["auth_permission_content_type_id_codename_01ab375a_uniq", "UNIQUE", "content_type_id", "auth_permission", "codename"], ["auth_permission_content_type_id_codename_01ab375a_uniq", "UNIQUE", "content_type_id", "auth_permission", "content_type_id"], ["auth_permission_content_type_id_codename_01ab375a_uniq", "UNIQUE", "content_type_id", "auth_permission", "codename"], ["auth_permission_content_type_id_codename_01ab375a_uniq", "UNIQUE", "content_type_id", "auth_permission", "content_type_id"], ["auth_permission_content_type_id_codename_01ab375a_uniq", "UNIQUE", "codename", "auth_permission", "codename"], ["auth_permission_content_type_id_codename_01ab375a_uniq", "UNIQUE", "codename", "auth_permission", "content_type_id"], ["auth_permission_content_type_id_codename_01ab375a_uniq", "UNIQUE", "codename", "auth_permission", "codename"], ["auth_permission_content_type_id_codename_01ab375a_uniq", "UNIQUE", "codename", "auth_permission", "content_type_id"], ["auth_permission_content_type_id_codename_01ab375a_uniq", "UNIQUE", "content_type_id", "auth_permission", "codename"], ["auth_permission_content_type_id_codename_01ab375a_uniq", "UNIQUE", "content_type_id", "auth_permission", "content_type_id"], ["auth_permission_content_type_id_codename_01ab375a_uniq", "UNIQUE", "content_type_id", "auth_permission", "codename"], ["auth_permission_content_type_id_codename_01ab375a_uniq", "UNIQUE", "content_type_id", "auth_permission", "content_type_id"], ["auth_permission_content_type_id_codename_01ab375a_uniq", "UNIQUE", "codename", "auth_permission", "codename"], ["auth_permission_content_type_id_codename_01ab375a_uniq", "UNIQUE", "codename", "auth_permission", "content_type_id"], ["auth_permission_content_type_id_codename_01ab375a_uniq", "UNIQUE", "codename", "auth_permission", "codename"], ["auth_permission_content_type_id_codename_01ab375a_uniq", "UNIQUE", "codename", "auth_permission", "content_type_id"], ["auth_permission_pkey", "PRIMARY KEY", "id", "auth_permission", "id"], ["auth_permission_pkey", "PRIMARY KEY", "id", "auth_permission", "id"], ["auth_permission_pkey", "PRIMARY KEY", "id", "auth_permission", "id"], ["auth_permission_pkey", "PRIMARY KEY", "id", "auth_permission", "id"], ["auth_permission_pkey", "PRIMARY KEY", "id", "auth_permission", "id"], ["auth_permission_pkey", "PRIMARY KEY", "id", "auth_permission", "id"], ["auth_permission_pkey", "PRIMARY KEY", "id", "auth_permission", "id"], ["auth_permission_pkey", "PRIMARY KEY", "id", "auth_permission", "id"], ["auth_permission_pkey", "PRIMARY KEY", "id", "auth_permission", "id"], ["116045_116063_1_not_null", "CHECK", null, null, null], ["116045_116063_2_not_null", "CHECK", null, null, null], ["116045_116063_3_not_null", "CHECK", null, null, null], ["116045_116063_4_not_null", "CHECK", null, null, null]], "communication_communicationlog": [["communication_commun_content_type_id_ebcdf3fc_fk_django_co", "FOREIGN KEY", "content_type_id", "django_content_type", "id"], ["communication_commun_sent_by_id_9d71ed5c_fk_users_use", "FOREIGN KEY", "sent_by_id", "users_user", "id"], ["communication_communicationlog_pkey", "PRIMARY KEY", "id", "communication_communicationlog", "id"], ["communication_communicationlog_task_id_key", "UNIQUE", "task_id", "communication_communicationlog", "task_id"], ["116045_116346_1_not_null", "CHECK", null, null, null], ["116045_116346_2_not_null", "CHECK", null, null, null], ["116045_116346_3_not_null", "CHECK", null, null, null], ["116045_116346_10_not_null", "CHECK", null, null, null], ["116045_116346_15_not_null", "CHECK", null, null, null]], "django_admin_log": [["django_admin_log_action_flag_check", "CHECK", null, "django_admin_log", "action_flag"], ["django_admin_log_action_flag_check", "CHECK", null, "django_admin_log", "action_flag"], ["django_admin_log_content_type_id_c4bce8eb_fk_django_co", "FOREIGN KEY", "content_type_id", "django_content_type", "id"], ["django_admin_log_content_type_id_c4bce8eb_fk_django_co", "FOREIGN KEY", "content_type_id", "django_content_type", "id"], ["django_admin_log_content_type_id_c4bce8eb_fk_django_co", "FOREIGN KEY", "content_type_id", "django_content_type", "id"], ["django_admin_log_content_type_id_c4bce8eb_fk_django_co", "FOREIGN KEY", "content_type_id", "django_content_type", "id"], ["django_admin_log_pkey", "PRIMARY KEY", "id", "django_admin_log", "id"], ["django_admin_log_pkey", "PRIMARY KEY", "id", "django_admin_log", "id"], ["django_admin_log_pkey", "PRIMARY KEY", "id", "django_admin_log", "id"], ["django_admin_log_pkey", "PRIMARY KEY", "id", "django_admin_log", "id"], ["django_admin_log_user_id_c564eba6_fk_users_user_id", "FOREIGN KEY", "user_id", "users_user", "id"], ["django_admin_log_user_id_c564eba6_fk_users_user_id", "FOREIGN KEY", "user_id", "users_user", "id"], ["django_admin_log_user_id_c564eba6_fk_users_user_id", "FOREIGN KEY", "user_id", "users_user", "id"], ["django_admin_log_user_id_c564eba6_fk_users_user_id", "FOREIGN KEY", "user_id", "users_user", "id"], ["116045_116291_1_not_null", "CHECK", null, null, null], ["116045_116291_2_not_null", "CHECK", null, null, null], ["116045_116291_4_not_null", "CHECK", null, null, null], ["116045_116291_5_not_null", "CHECK", null, null, null], ["116045_116291_6_not_null", "CHECK", null, null, null], ["116045_116291_8_not_null", "CHECK", null, null, null]], "django_content_type": [["django_content_type_app_label_model_76bd3d3b_uniq", "UNIQUE", "app_label", "django_content_type", "app_label"], ["django_content_type_app_label_model_76bd3d3b_uniq", "UNIQUE", "app_label", "django_content_type", "model"], ["django_content_type_app_label_model_76bd3d3b_uniq", "UNIQUE", "app_label", "django_content_type", "app_label"], ["django_content_type_app_label_model_76bd3d3b_uniq", "UNIQUE", "app_label", "django_content_type", "model"], ["django_content_type_app_label_model_76bd3d3b_uniq", "UNIQUE", "model", "django_content_type", "app_label"], ["django_content_type_app_label_model_76bd3d3b_uniq", "UNIQUE", "model", "django_content_type", "model"], ["django_content_type_app_label_model_76bd3d3b_uniq", "UNIQUE", "model", "django_content_type", "app_label"], ["django_content_type_app_label_model_76bd3d3b_uniq", "UNIQUE", "model", "django_content_type", "model"], ["django_content_type_app_label_model_76bd3d3b_uniq", "UNIQUE", "app_label", "django_content_type", "app_label"], ["django_content_type_app_label_model_76bd3d3b_uniq", "UNIQUE", "app_label", "django_content_type", "model"], ["django_content_type_app_label_model_76bd3d3b_uniq", "UNIQUE", "app_label", "django_content_type", "app_label"], ["django_content_type_app_label_model_76bd3d3b_uniq", "UNIQUE", "app_label", "django_content_type", "model"], ["django_content_type_app_label_model_76bd3d3b_uniq", "UNIQUE", "model", "django_content_type", "app_label"], ["django_content_type_app_label_model_76bd3d3b_uniq", "UNIQUE", "model", "django_content_type", "model"], ["django_content_type_app_label_model_76bd3d3b_uniq", "UNIQUE", "model", "django_content_type", "app_label"], ["django_content_type_app_label_model_76bd3d3b_uniq", "UNIQUE", "model", "django_content_type", "model"], ["django_content_type_pkey", "PRIMARY KEY", "id", "django_content_type", "id"], ["django_content_type_pkey", "PRIMARY KEY", "id", "django_content_type", "id"], ["django_content_type_pkey", "PRIMARY KEY", "id", "django_content_type", "id"], ["django_content_type_pkey", "PRIMARY KEY", "id", "django_content_type", "id"], ["django_content_type_pkey", "PRIMARY KEY", "id", "django_content_type", "id"], ["django_content_type_pkey", "PRIMARY KEY", "id", "django_content_type", "id"], ["django_content_type_pkey", "PRIMARY KEY", "id", "django_content_type", "id"], ["django_content_type_pkey", "PRIMARY KEY", "id", "django_content_type", "id"], ["django_content_type_pkey", "PRIMARY KEY", "id", "django_content_type", "id"], ["116045_116055_1_not_null", "CHECK", null, null, null], ["116045_116055_3_not_null", "CHECK", null, null, null], ["116045_116055_4_not_null", "CHECK", null, null, null]], "django_migrations": [["django_migrations_pkey", "PRIMARY KEY", "id", "django_migrations", "id"], ["django_migrations_pkey", "PRIMARY KEY", "id", "django_migrations", "id"], ["django_migrations_pkey", "PRIMARY KEY", "id", "django_migrations", "id"], ["django_migrations_pkey", "PRIMARY KEY", "id", "django_migrations", "id"], ["django_migrations_pkey", "PRIMARY KEY", "id", "django_migrations", "id"], ["django_migrations_pkey", "PRIMARY KEY", "id", "django_migrations", "id"], ["django_migrations_pkey", "PRIMARY KEY", "id", "django_migrations", "id"], ["django_migrations_pkey", "PRIMARY KEY", "id", "django_migrations", "id"], ["django_migrations_pkey", "PRIMARY KEY", "id", "django_migrations", "id"], ["116045_116047_1_not_null", "CHECK", null, null, null], ["116045_116047_2_not_null", "CHECK", null, null, null], ["116045_116047_3_not_null", "CHECK", null, null, null], ["116045_116047_4_not_null", "CHECK", null, null, null]], "fees_account": [["fees_account_account_number_key", "UNIQUE", "account_number", "fees_account", "account_number"], ["fees_account_name_key", "UNIQUE", "name", "fees_account", "name"], ["fees_account_pkey", "PRIMARY KEY", "id", "fees_account", "id"], ["116045_116461_1_not_null", "CHECK", null, null, null], ["116045_116461_2_not_null", "CHECK", null, null, null], ["116045_116461_3_not_null", "CHECK", null, null, null], ["116045_116461_4_not_null", "CHECK", null, null, null], ["116045_116461_6_not_null", "CHECK", null, null, null], ["116045_116461_8_not_null", "CHECK", null, null, null]], "fees_concessiontype": [["fees_concessiontype_name_key", "UNIQUE", "name", "fees_concessiontype", "name"], ["fees_concessiontype_pkey", "PRIMARY KEY", "id", "fees_concessiontype", "id"], ["116045_116473_1_not_null", "CHECK", null, null, null], ["116045_116473_2_not_null", "CHECK", null, null, null], ["116045_116473_5_not_null", "CHECK", null, null, null], ["116045_116473_6_not_null", "CHECK", null, null, null], ["116045_116473_7_not_null", "CHECK", null, null, null], ["116045_116473_8_not_null", "CHECK", null, null, null], ["116045_116473_9_not_null", "CHECK", null, null, null]], "fees_feehead": [["fees_feehead_income_account_link_id_fkey", "FOREIGN KEY", "income_account_link_id", "accounting_account", "id"], ["fees_feehead_name_key", "UNIQUE", "name", "fees_feehead", "name"], ["fees_feehead_pkey", "PRIMARY KEY", "id", "fees_feehead", "id"], ["116045_117328_1_not_null", "CHECK", null, null, null], ["116045_117328_2_not_null", "CHECK", null, null, null], ["116045_117328_5_not_null", "CHECK", null, null, null], ["116045_117328_6_not_null", "CHECK", null, null, null], ["116045_117328_7_not_null", "CHECK", null, null, null]], "fees_feestructure": [["fees_feestructure_academic_year_id_730f45dc_fk_schools_a", "FOREIGN KEY", "academic_year_id", "schools_academicyear", "id"], ["fees_feestructure_pkey", "PRIMARY KEY", "id", "fees_feestructure", "id"], ["fees_feestructure_term_id_8b1a43ce_fk_schools_term_id", "FOREIGN KEY", "term_id", "schools_term", "id"], ["116045_116483_1_not_null", "CHECK", null, null, null], ["116045_116483_2_not_null", "CHECK", null, null, null], ["116045_116483_4_not_null", "CHECK", null, null, null], ["116045_116483_5_not_null", "CHECK", null, null, null], ["116045_116483_6_not_null", "CHECK", null, null, null], ["116045_116483_7_not_null", "CHECK", null, null, null], ["116045_116483_8_not_null", "CHECK", null, null, null]], "fees_feestructure_applicable_classes": [["fees_feestructure_applicable_classes_feestructure_id_fkey", "FOREIGN KEY", "feestructure_id", "fees_feestructure", "id"], ["fees_feestructure_applicable_classes_pkey", "PRIMARY KEY", "id", "fees_feestructure_applicable_classes", "id"], ["fees_feestructure_applicable_classes_schoolclass_id_fkey", "FOREIGN KEY", "schoolclass_id", "schools_schoolclass", "id"], ["fees_feestructure_applicable_classes_unique", "UNIQUE", "feestructure_id", "fees_feestructure_applicable_classes", "feestructure_id"], ["fees_feestructure_applicable_classes_unique", "UNIQUE", "feestructure_id", "fees_feestructure_applicable_classes", "schoolclass_id"], ["fees_feestructure_applicable_classes_unique", "UNIQUE", "schoolclass_id", "fees_feestructure_applicable_classes", "feestructure_id"], ["fees_feestructure_applicable_classes_unique", "UNIQUE", "schoolclass_id", "fees_feestructure_applicable_classes", "schoolclass_id"], ["116045_145210_1_not_null", "CHECK", null, null, null], ["116045_145210_2_not_null", "CHECK", null, null, null], ["116045_145210_3_not_null", "CHECK", null, null, null]], "fees_feestructureitem": [["fees_feestructureite_concession_type_id_b95b9b88_fk_fees_conc", "FOREIGN KEY", "concession_type_id", "fees_concessiontype", "id"], ["fees_feestructureite_fee_structure_id_3df07fe1_fk_fees_fees", "FOREIGN KEY", "fee_structure_id", "fees_feestructure", "id"], ["fees_feestructureitem_fee_structure_id_fee_hea_66f0a719_uniq", "UNIQUE", "fee_structure_id", "fees_feestructureitem", "fee_head_id"], ["fees_feestructureitem_fee_structure_id_fee_hea_66f0a719_uniq", "UNIQUE", "fee_structure_id", "fees_feestructureitem", "fee_structure_id"], ["fees_feestructureitem_fee_structure_id_fee_hea_66f0a719_uniq", "UNIQUE", "fee_head_id", "fees_feestructureitem", "fee_head_id"], ["fees_feestructureitem_fee_structure_id_fee_hea_66f0a719_uniq", "UNIQUE", "fee_head_id", "fees_feestructureitem", "fee_structure_id"], ["fees_feestructureitem_pkey", "PRIMARY KEY", "id", "fees_feestructureitem", "id"], ["116045_116491_1_not_null", "CHECK", null, null, null], ["116045_116491_2_not_null", "CHECK", null, null, null], ["116045_116491_3_not_null", "CHECK", null, null, null], ["116045_116491_7_not_null", "CHECK", null, null, null], ["116045_116491_8_not_null", "CHECK", null, null, null]], "fees_invoice": [["fees_invoice_academic_year_id_4fe02078_fk_schools_a", "FOREIGN KEY", "academic_year_id", "schools_academicyear", "id"], ["fees_invoice_created_by_id_e087a26e_fk_schools_staffuser_id", "FOREIGN KEY", "created_by_id", "schools_staffuser", "id"], ["fees_invoice_fee_structure_id_cb5097a3_fk_fees_feestructure_id", "FOREIGN KEY", "fee_structure_id", "fees_feestructure", "id"], ["fees_invoice_invoice_number_key", "UNIQUE", "invoice_number", "fees_invoice", "invoice_number"], ["fees_invoice_pkey", "PRIMARY KEY", "id", "fees_invoice", "id"], ["fees_invoice_related_journal_entr_95e6b3e8_fk_accountin", "FOREIGN KEY", "related_journal_entry_id", "accounting_journalentry", "id"], ["fees_invoice_student_id_c6e2fae9_fk_students_student_id", "FOREIGN KEY", "student_id", "students_student", "id"], ["fees_invoice_term_id_0b2de24c_fk_schools_term_id", "FOREIGN KEY", "term_id", "schools_term", "id"], ["116045_116497_1_not_null", "CHECK", null, null, null], ["116045_116497_2_not_null", "CHECK", null, null, null], ["116045_116497_3_not_null", "CHECK", null, null, null], ["116045_116497_8_not_null", "CHECK", null, null, null], ["116045_116497_9_not_null", "CHECK", null, null, null], ["116045_116497_13_not_null", "CHECK", null, null, null], ["116045_116497_14_not_null", "CHECK", null, null, null], ["116045_116497_15_not_null", "CHECK", null, null, null], ["116045_116497_19_not_null", "CHECK", null, null, null], ["116045_116497_21_not_null", "CHECK", null, null, null], ["116045_116497_22_not_null", "CHECK", null, null, null]], "fees_invoicedetail": [["fees_invoicedetail_applies_to_line_id_e7593d17_fk_fees_invo", "FOREIGN KEY", "applies_to_line_id", "fees_invoicedetail", "id"], ["fees_invoicedetail_concession_type_id_0126f378_fk_fees_conc", "FOREIGN KEY", "concession_type_id", "fees_concessiontype", "id"], ["fees_invoicedetail_invoice_id_75a0a342_fk_fees_invoice_id", "FOREIGN KEY", "invoice_id", "fees_invoice", "id"], ["fees_invoicedetail_pkey", "PRIMARY KEY", "id", "fees_invoicedetail", "id"], ["116045_116507_1_not_null", "CHECK", null, null, null], ["116045_116507_2_not_null", "CHECK", null, null, null], ["116045_116507_3_not_null", "CHECK", null, null, null], ["116045_116507_4_not_null", "CHECK", null, null, null], ["116045_116507_5_not_null", "CHECK", null, null, null], ["116045_116507_6_not_null", "CHECK", null, null, null], ["116045_116507_7_not_null", "CHECK", null, null, null], ["116045_116507_11_not_null", "CHECK", null, null, null], ["116045_116507_12_not_null", "CHECK", null, null, null]], "fees_studentconcession": [["fees_studentconcessi_academic_year_id_494ebe1a_fk_schools_a", "FOREIGN KEY", "academic_year_id", "schools_academicyear", "id"], ["fees_studentconcessi_concession_type_id_2cf81a15_fk_fees_conc", "FOREIGN KEY", "concession_type_id", "fees_concessiontype", "id"], ["fees_studentconcessi_student_id_57dd883e_fk_students_", "FOREIGN KEY", "student_id", "students_student", "id"], ["fees_studentconcession_granted_by_id_0bb338b7_fk_users_user_id", "FOREIGN KEY", "granted_by_id", "users_user", "id"], ["fees_studentconcession_pkey", "PRIMARY KEY", "id", "fees_studentconcession", "id"], ["fees_studentconcession_pkey", "PRIMARY KEY", "id", "fees_studentconcession", "id"], ["fees_studentconcession_pkey", "PRIMARY KEY", "id", "fees_studentconcession", "id"], ["fees_studentconcession_pkey", "PRIMARY KEY", "id", "fees_studentconcession", "id"], ["fees_studentconcession_student_id_concession_ty_94595c1d_uniq", "UNIQUE", "student_id", "fees_studentconcession", "academic_year_id"], ["fees_studentconcession_student_id_concession_ty_94595c1d_uniq", "UNIQUE", "student_id", "fees_studentconcession", "concession_type_id"], ["fees_studentconcession_student_id_concession_ty_94595c1d_uniq", "UNIQUE", "student_id", "fees_studentconcession", "student_id"], ["fees_studentconcession_student_id_concession_ty_94595c1d_uniq", "UNIQUE", "student_id", "fees_studentconcession", "term_id"], ["fees_studentconcession_student_id_concession_ty_94595c1d_uniq", "UNIQUE", "concession_type_id", "fees_studentconcession", "academic_year_id"], ["fees_studentconcession_student_id_concession_ty_94595c1d_uniq", "UNIQUE", "concession_type_id", "fees_studentconcession", "concession_type_id"], ["fees_studentconcession_student_id_concession_ty_94595c1d_uniq", "UNIQUE", "concession_type_id", "fees_studentconcession", "student_id"], ["fees_studentconcession_student_id_concession_ty_94595c1d_uniq", "UNIQUE", "concession_type_id", "fees_studentconcession", "term_id"], ["fees_studentconcession_student_id_concession_ty_94595c1d_uniq", "UNIQUE", "academic_year_id", "fees_studentconcession", "academic_year_id"], ["fees_studentconcession_student_id_concession_ty_94595c1d_uniq", "UNIQUE", "academic_year_id", "fees_studentconcession", "concession_type_id"], ["fees_studentconcession_student_id_concession_ty_94595c1d_uniq", "UNIQUE", "academic_year_id", "fees_studentconcession", "student_id"], ["fees_studentconcession_student_id_concession_ty_94595c1d_uniq", "UNIQUE", "academic_year_id", "fees_studentconcession", "term_id"], ["fees_studentconcession_student_id_concession_ty_94595c1d_uniq", "UNIQUE", "term_id", "fees_studentconcession", "academic_year_id"], ["fees_studentconcession_student_id_concession_ty_94595c1d_uniq", "UNIQUE", "term_id", "fees_studentconcession", "concession_type_id"], ["fees_studentconcession_student_id_concession_ty_94595c1d_uniq", "UNIQUE", "term_id", "fees_studentconcession", "student_id"], ["fees_studentconcession_student_id_concession_ty_94595c1d_uniq", "UNIQUE", "term_id", "fees_studentconcession", "term_id"], ["fees_studentconcession_term_id_df2bba03_fk_schools_term_id", "FOREIGN KEY", "term_id", "schools_term", "id"], ["116045_149368_1_not_null", "CHECK", null, null, null], ["116045_149368_3_not_null", "CHECK", null, null, null], ["116045_149368_4_not_null", "CHECK", null, null, null], ["116045_149368_5_not_null", "CHECK", null, null, null], ["116045_149368_7_not_null", "CHECK", null, null, null]], "fees_studentfeeallocation": [["fees_studentfeealloc_academic_year_id_9dec69f3_fk_schools_a", "FOREIGN KEY", "academic_year_id", "schools_academicyear", "id"], ["fees_studentfeealloc_fee_structure_id_d2ecf918_fk_fees_fees", "FOREIGN KEY", "fee_structure_id", "fees_feestructure", "id"], ["fees_studentfeealloc_student_id_d66a65b7_fk_students_", "FOREIGN KEY", "student_id", "students_student", "id"], ["fees_studentfeeallocatio_student_id_academic_year_d71698b1_uniq", "UNIQUE", "student_id", "fees_studentfeeallocation", "academic_year_id"], ["fees_studentfeeallocatio_student_id_academic_year_d71698b1_uniq", "UNIQUE", "student_id", "fees_studentfeeallocation", "fee_structure_id"], ["fees_studentfeeallocatio_student_id_academic_year_d71698b1_uniq", "UNIQUE", "student_id", "fees_studentfeeallocation", "student_id"], ["fees_studentfeeallocatio_student_id_academic_year_d71698b1_uniq", "UNIQUE", "student_id", "fees_studentfeeallocation", "term_id"], ["fees_studentfeeallocatio_student_id_academic_year_d71698b1_uniq", "UNIQUE", "academic_year_id", "fees_studentfeeallocation", "academic_year_id"], ["fees_studentfeeallocatio_student_id_academic_year_d71698b1_uniq", "UNIQUE", "academic_year_id", "fees_studentfeeallocation", "fee_structure_id"], ["fees_studentfeeallocatio_student_id_academic_year_d71698b1_uniq", "UNIQUE", "academic_year_id", "fees_studentfeeallocation", "student_id"], ["fees_studentfeeallocatio_student_id_academic_year_d71698b1_uniq", "UNIQUE", "academic_year_id", "fees_studentfeeallocation", "term_id"], ["fees_studentfeeallocatio_student_id_academic_year_d71698b1_uniq", "UNIQUE", "fee_structure_id", "fees_studentfeeallocation", "academic_year_id"], ["fees_studentfeeallocatio_student_id_academic_year_d71698b1_uniq", "UNIQUE", "fee_structure_id", "fees_studentfeeallocation", "fee_structure_id"], ["fees_studentfeeallocatio_student_id_academic_year_d71698b1_uniq", "UNIQUE", "fee_structure_id", "fees_studentfeeallocation", "student_id"], ["fees_studentfeeallocatio_student_id_academic_year_d71698b1_uniq", "UNIQUE", "fee_structure_id", "fees_studentfeeallocation", "term_id"], ["fees_studentfeeallocatio_student_id_academic_year_d71698b1_uniq", "UNIQUE", "term_id", "fees_studentfeeallocation", "academic_year_id"], ["fees_studentfeeallocatio_student_id_academic_year_d71698b1_uniq", "UNIQUE", "term_id", "fees_studentfeeallocation", "fee_structure_id"], ["fees_studentfeeallocatio_student_id_academic_year_d71698b1_uniq", "UNIQUE", "term_id", "fees_studentfeeallocation", "student_id"], ["fees_studentfeeallocatio_student_id_academic_year_d71698b1_uniq", "UNIQUE", "term_id", "fees_studentfeeallocation", "term_id"], ["fees_studentfeeallocation_pkey", "PRIMARY KEY", "id", "fees_studentfeeallocation", "id"], ["fees_studentfeeallocation_term_id_bdf3f734_fk_schools_term_id", "FOREIGN KEY", "term_id", "schools_term", "id"], ["116045_116521_1_not_null", "CHECK", null, null, null], ["116045_116521_3_not_null", "CHECK", null, null, null], ["116045_116521_5_not_null", "CHECK", null, null, null], ["116045_116521_8_not_null", "CHECK", null, null, null], ["116045_116521_9_not_null", "CHECK", null, null, null], ["116045_116521_10_not_null", "CHECK", null, null, null]], "finance_budget": [["finance_budget_name_key", "UNIQUE", "name", "finance_budget", "name"], ["finance_budget_pkey", "PRIMARY KEY", "id", "finance_budget", "id"], ["116045_116733_1_not_null", "CHECK", null, null, null], ["116045_116733_2_not_null", "CHECK", null, null, null], ["116045_116733_3_not_null", "CHECK", null, null, null], ["116045_116733_4_not_null", "CHECK", null, null, null], ["116045_116733_6_not_null", "CHECK", null, null, null]], "finance_budgetamount": [["finance_budgetamount_academic_year_id_fd015deb_fk_schools_a", "FOREIGN KEY", "academic_year_id", "schools_academicyear", "id"], ["finance_budgetamount_budget_item_id_7bc4cd50_fk_finance_b", "FOREIGN KEY", "budget_item_id", "finance_budgetitem", "id"], ["finance_budgetamount_budget_item_id_academic__b03151f9_uniq", "UNIQUE", "budget_item_id", "finance_budgetamount", "academic_year_id"], ["finance_budgetamount_budget_item_id_academic__b03151f9_uniq", "UNIQUE", "budget_item_id", "finance_budgetamount", "budget_item_id"], ["finance_budgetamount_budget_item_id_academic__b03151f9_uniq", "UNIQUE", "budget_item_id", "finance_budgetamount", "term_id"], ["finance_budgetamount_budget_item_id_academic__b03151f9_uniq", "UNIQUE", "academic_year_id", "finance_budgetamount", "academic_year_id"], ["finance_budgetamount_budget_item_id_academic__b03151f9_uniq", "UNIQUE", "academic_year_id", "finance_budgetamount", "budget_item_id"], ["finance_budgetamount_budget_item_id_academic__b03151f9_uniq", "UNIQUE", "academic_year_id", "finance_budgetamount", "term_id"], ["finance_budgetamount_budget_item_id_academic__b03151f9_uniq", "UNIQUE", "term_id", "finance_budgetamount", "academic_year_id"], ["finance_budgetamount_budget_item_id_academic__b03151f9_uniq", "UNIQUE", "term_id", "finance_budgetamount", "budget_item_id"], ["finance_budgetamount_budget_item_id_academic__b03151f9_uniq", "UNIQUE", "term_id", "finance_budgetamount", "term_id"], ["finance_budgetamount_pkey", "PRIMARY KEY", "id", "finance_budgetamount", "id"], ["finance_budgetamount_term_id_9e43a8dc_fk_schools_term_id", "FOREIGN KEY", "term_id", "schools_term", "id"], ["116045_116743_1_not_null", "CHECK", null, null, null], ["116045_116743_4_not_null", "CHECK", null, null, null], ["116045_116743_6_not_null", "CHECK", null, null, null], ["116045_116743_8_not_null", "CHECK", null, null, null]], "finance_budgetitem": [["finance_budgetitem_linked_coa_account_i_0474281c_fk_accountin", "FOREIGN KEY", "linked_coa_account_id", "accounting_account", "id"], ["finance_budgetitem_name_key", "UNIQUE", "name", "finance_budgetitem", "name"], ["finance_budgetitem_pkey", "PRIMARY KEY", "id", "finance_budgetitem", "id"], ["116045_116751_1_not_null", "CHECK", null, null, null], ["116045_116751_4_not_null", "CHECK", null, null, null], ["116045_116751_6_not_null", "CHECK", null, null, null], ["116045_116751_7_not_null", "CHECK", null, null, null]], "finance_expense": [["finance_expense_category_id_19b015b6_fk_finance_e", "FOREIGN KEY", "category_id", "finance_expensecategory", "id"], ["finance_expense_payment_method_id_2c721e79_fk_payments_", "FOREIGN KEY", "payment_method_id", "payments_paymentmethod", "id"], ["finance_expense_pkey", "PRIMARY KEY", "id", "finance_expense", "id"], ["finance_expense_recorded_by_id_d3dcb51f_fk_users_user_id", "FOREIGN KEY", "recorded_by_id", "users_user", "id"], ["finance_expense_vendor_id_bef4c853_fk_finance_vendor_id", "FOREIGN KEY", "vendor_id", "finance_vendor", "id"], ["116045_116761_1_not_null", "CHECK", null, null, null], ["116045_116761_4_not_null", "CHECK", null, null, null], ["116045_116761_5_not_null", "CHECK", null, null, null], ["116045_116761_6_not_null", "CHECK", null, null, null], ["116045_116761_10_not_null", "CHECK", null, null, null]], "finance_expensecategory": [["finance_expensecateg_expense_account_id_da668e48_fk_accountin", "FOREIGN KEY", "expense_account_id", "accounting_account", "id"], ["finance_expensecategory_name_key", "UNIQUE", "name", "finance_expensecategory", "name"], ["finance_expensecategory_pkey", "PRIMARY KEY", "id", "finance_expensecategory", "id"], ["116045_116769_1_not_null", "CHECK", null, null, null], ["116045_116769_4_not_null", "CHECK", null, null, null], ["116045_116769_6_not_null", "CHECK", null, null, null], ["116045_116769_7_not_null", "CHECK", null, null, null]], "finance_vendor": [["finance_vendor_name_key", "UNIQUE", "name", "finance_vendor", "name"], ["finance_vendor_pkey", "PRIMARY KEY", "id", "finance_vendor", "id"], ["116045_116779_1_not_null", "CHECK", null, null, null], ["116045_116779_4_not_null", "CHECK", null, null, null], ["116045_116779_15_not_null", "CHECK", null, null, null]], "hr_employeeprofile": [["hr_employeeprofile_employee_id_key", "UNIQUE", "employee_id", "hr_employeeprofile", "employee_id"], ["hr_employeeprofile_employee_id_key", "UNIQUE", "employee_id", "hr_employeeprofile", "employee_id"], ["hr_employeeprofile_employee_id_key", "UNIQUE", "employee_id", "hr_employeeprofile", "employee_id"], ["hr_employeeprofile_employee_id_key", "UNIQUE", "employee_id", "hr_employeeprofile", "employee_id"], ["hr_employeeprofile_pkey", "PRIMARY KEY", "id", "hr_employeeprofile", "id"], ["hr_employeeprofile_pkey", "PRIMARY KEY", "id", "hr_employeeprofile", "user_id"], ["hr_employeeprofile_pkey", "PRIMARY KEY", "user_id", "hr_employeeprofile", "id"], ["hr_employeeprofile_pkey", "PRIMARY KEY", "user_id", "hr_employeeprofile", "user_id"], ["hr_employeeprofile_user_id_dc5a85dd_fk_schools_staffuser_id", "FOREIGN KEY", "user_id", "schools_staffuser", "id"], ["116045_116848_1_not_null", "CHECK", null, null, null], ["116045_116848_2_not_null", "CHECK", null, null, null], ["116045_116848_6_not_null", "CHECK", null, null, null], ["116045_116848_7_not_null", "CHECK", null, null, null], ["116045_116848_8_not_null", "CHECK", null, null, null], ["116045_116848_9_not_null", "CHECK", null, null, null], ["116045_116848_10_not_null", "CHECK", null, null, null], ["116045_116848_11_not_null", "CHECK", null, null, null], ["116045_116848_12_not_null", "CHECK", null, null, null], ["116045_116848_16_not_null", "CHECK", null, null, null], ["116045_116848_17_not_null", "CHECK", null, null, null], ["116045_116848_18_not_null", "CHECK", null, null, null], ["116045_116848_20_not_null", "CHECK", null, null, null], ["116045_116848_21_not_null", "CHECK", null, null, null], ["116045_116848_23_not_null", "CHECK", null, null, null]], "hr_graderule": [["hr_graderule_component_id_0c15791b_fk_hr_salarycomponent_id", "FOREIGN KEY", "component_id", "hr_salarycomponent", "id"], ["hr_graderule_grade_id_458beb48_fk_hr_salarygrade_id", "FOREIGN KEY", "grade_id", "hr_salarygrade", "id"], ["hr_graderule_grade_id_component_id_5da9c08d_uniq", "UNIQUE", "grade_id", "hr_graderule", "component_id"], ["hr_graderule_grade_id_component_id_5da9c08d_uniq", "UNIQUE", "grade_id", "hr_graderule", "grade_id"], ["hr_graderule_grade_id_component_id_5da9c08d_uniq", "UNIQUE", "component_id", "hr_graderule", "component_id"], ["hr_graderule_grade_id_component_id_5da9c08d_uniq", "UNIQUE", "component_id", "hr_graderule", "grade_id"], ["hr_graderule_pkey", "PRIMARY KEY", "id", "hr_graderule", "id"], ["116045_149261_1_not_null", "CHECK", null, null, null], ["116045_149261_2_not_null", "CHECK", null, null, null], ["116045_149261_3_not_null", "CHECK", null, null, null], ["116045_149261_4_not_null", "CHECK", null, null, null]], "hr_leavebalance": [["hr_leavebalance_employee_id_bd7dc9c1_fk_hr_employ", "FOREIGN KEY", "employee_id", "hr_employeeprofile", "user_id"], ["hr_leavebalance_employee_id_leave_type_id_year_uniq", "UNIQUE", "employee_id", "hr_leavebalance", "employee_id"], ["hr_leavebalance_employee_id_leave_type_id_year_uniq", "UNIQUE", "employee_id", "hr_leavebalance", "leave_type_id"], ["hr_leavebalance_employee_id_leave_type_id_year_uniq", "UNIQUE", "employee_id", "hr_leavebalance", "year"], ["hr_leavebalance_employee_id_leave_type_id_year_uniq", "UNIQUE", "leave_type_id", "hr_leavebalance", "employee_id"], ["hr_leavebalance_employee_id_leave_type_id_year_uniq", "UNIQUE", "leave_type_id", "hr_leavebalance", "leave_type_id"], ["hr_leavebalance_employee_id_leave_type_id_year_uniq", "UNIQUE", "leave_type_id", "hr_leavebalance", "year"], ["hr_leavebalance_employee_id_leave_type_id_year_uniq", "UNIQUE", "year", "hr_leavebalance", "employee_id"], ["hr_leavebalance_employee_id_leave_type_id_year_uniq", "UNIQUE", "year", "hr_leavebalance", "leave_type_id"], ["hr_leavebalance_employee_id_leave_type_id_year_uniq", "UNIQUE", "year", "hr_leavebalance", "year"], ["hr_leavebalance_leave_type_id_e7032b80_fk_hr_leavetype_id", "FOREIGN KEY", "leave_type_id", "hr_leavetype", "id"], ["hr_leavebalance_pkey", "PRIMARY KEY", "id", "hr_leavebalance", "id"], ["hr_leavebalance_year_check", "CHECK", null, "hr_leavebalance", "year"], ["116045_116874_1_not_null", "CHECK", null, null, null], ["116045_116874_3_not_null", "CHECK", null, null, null], ["116045_116874_6_not_null", "CHECK", null, null, null], ["116045_116874_7_not_null", "CHECK", null, null, null], ["116045_116874_8_not_null", "CHECK", null, null, null], ["116045_116874_9_not_null", "CHECK", null, null, null], ["116045_116874_10_not_null", "CHECK", null, null, null]], "hr_leaverequest": [["hr_leaverequest_approved_by_id_1584e857_fk_hr_employ", "FOREIGN KEY", "approved_by_id", "hr_employeeprofile", "user_id"], ["hr_leaverequest_employee_id_7b0019ae_fk_hr_employ", "FOREIGN KEY", "employee_id", "hr_employeeprofile", "user_id"], ["hr_leaverequest_leave_type_id_a69fe07b_fk_hr_leavetype_id", "FOREIGN KEY", "leave_type_id", "hr_leavetype", "id"], ["hr_leaverequest_pkey", "PRIMARY KEY", "id", "hr_leaverequest", "id"], ["116045_116866_1_not_null", "CHECK", null, null, null], ["116045_116866_2_not_null", "CHECK", null, null, null], ["116045_116866_3_not_null", "CHECK", null, null, null], ["116045_116866_4_not_null", "CHECK", null, null, null], ["116045_116866_5_not_null", "CHECK", null, null, null], ["116045_116866_6_not_null", "CHECK", null, null, null], ["116045_116866_8_not_null", "CHECK", null, null, null], ["116045_116866_13_not_null", "CHECK", null, null, null], ["116045_116866_14_not_null", "CHECK", null, null, null], ["116045_116866_16_not_null", "CHECK", null, null, null], ["116045_116866_17_not_null", "CHECK", null, null, null], ["116045_116866_18_not_null", "CHECK", null, null, null]], "hr_leavetype": [["hr_leavetype_max_accrual_balance_check", "CHECK", null, "hr_leavetype", "max_accrual_balance"], ["hr_leavetype_name_key", "UNIQUE", "name", "hr_leavetype", "name"], ["hr_leavetype_pkey", "PRIMARY KEY", "id", "hr_leavetype", "id"], ["116045_116856_1_not_null", "CHECK", null, null, null], ["116045_116856_2_not_null", "CHECK", null, null, null], ["116045_116856_5_not_null", "CHECK", null, null, null], ["116045_116856_6_not_null", "CHECK", null, null, null], ["116045_116856_7_not_null", "CHECK", null, null, null], ["116045_116856_8_not_null", "CHECK", null, null, null], ["116045_116856_9_not_null", "CHECK", null, null, null], ["116045_116856_11_not_null", "CHECK", null, null, null], ["116045_116856_12_not_null", "CHECK", null, null, null], ["116045_116856_14_not_null", "CHECK", null, null, null]], "hr_payrollrun": [["hr_payrollrun_pkey", "PRIMARY KEY", "id", "hr_payrollrun", "id"], ["hr_payrollrun_processed_by_id_034b4248_fk_schools_staffuser_id", "FOREIGN KEY", "processed_by_id", "schools_staffuser", "id"], ["116045_145522_1_not_null", "CHECK", null, null, null], ["116045_145522_2_not_null", "CHECK", null, null, null], ["116045_145522_3_not_null", "CHECK", null, null, null], ["116045_145522_4_not_null", "CHECK", null, null, null], ["116045_145522_5_not_null", "CHECK", null, null, null], ["116045_145522_8_not_null", "CHECK", null, null, null], ["116045_145522_9_not_null", "CHECK", null, null, null]], "hr_payslip": [["hr_payslip_payroll_run_id_af85678b_fk_hr_payrollrun_id", "FOREIGN KEY", "payroll_run_id", "hr_payrollrun", "id"], ["hr_payslip_payroll_run_id_staff_member_id_b6ceea3a_uniq", "UNIQUE", "payroll_run_id", "hr_payslip", "payroll_run_id"], ["hr_payslip_payroll_run_id_staff_member_id_b6ceea3a_uniq", "UNIQUE", "payroll_run_id", "hr_payslip", "staff_member_id"], ["hr_payslip_payroll_run_id_staff_member_id_b6ceea3a_uniq", "UNIQUE", "staff_member_id", "hr_payslip", "payroll_run_id"], ["hr_payslip_payroll_run_id_staff_member_id_b6ceea3a_uniq", "UNIQUE", "staff_member_id", "hr_payslip", "staff_member_id"], ["hr_payslip_pkey", "PRIMARY KEY", "id", "hr_payslip", "id"], ["hr_payslip_staff_member_id_49251d5b_fk_schools_staffuser_id", "FOREIGN KEY", "staff_member_id", "schools_staffuser", "id"], ["116045_145259_1_not_null", "CHECK", null, null, null], ["116045_145259_4_not_null", "CHECK", null, null, null], ["116045_145259_5_not_null", "CHECK", null, null, null], ["116045_145259_6_not_null", "CHECK", null, null, null], ["116045_145259_8_not_null", "CHECK", null, null, null], ["116045_145259_10_not_null", "CHECK", null, null, null], ["116045_145259_11_not_null", "CHECK", null, null, null], ["116045_145259_12_not_null", "CHECK", null, null, null], ["116045_145259_13_not_null", "CHECK", null, null, null], ["116045_145259_14_not_null", "CHECK", null, null, null], ["116045_145259_16_not_null", "CHECK", null, null, null], ["116045_145259_17_not_null", "CHECK", null, null, null], ["116045_145259_18_not_null", "CHECK", null, null, null], ["116045_145259_20_not_null", "CHECK", null, null, null]], "hr_paysliplineitem": [["hr_paysliplineitem_payslip_id_8110fb3f_fk_hr_payslip_id", "FOREIGN KEY", "payslip_id", "hr_payslip", "id"], ["hr_paysliplineitem_pkey", "PRIMARY KEY", "id", "hr_paysliplineitem", "id"], ["hr_paysliplineitem_source_component_id_f45ffb82_fk_hr_salary", "FOREIGN KEY", "source_component_id", "hr_salarycomponent", "id"], ["116045_149314_1_not_null", "CHECK", null, null, null], ["116045_149314_2_not_null", "CHECK", null, null, null], ["116045_149314_3_not_null", "CHECK", null, null, null], ["116045_149314_4_not_null", "CHECK", null, null, null], ["116045_149314_5_not_null", "CHECK", null, null, null]], "hr_salarycomponent": [["hr_salarycomponent_name_95438b93_uniq", "UNIQUE", "name", "hr_salarycomponent", "name"], ["hr_salarycomponent_pkey", "PRIMARY KEY", "id", "hr_salarycomponent", "id"], ["116045_145265_1_not_null", "CHECK", null, null, null], ["116045_145265_2_not_null", "CHECK", null, null, null], ["116045_145265_3_not_null", "CHECK", null, null, null], ["116045_145265_7_not_null", "CHECK", null, null, null]], "hr_salarygrade": [["hr_salarygrade_name_1824c6e6_uniq", "UNIQUE", "name", "hr_salarygrade", "name"], ["hr_salarygrade_pkey", "PRIMARY KEY", "id", "hr_salarygrade", "id"], ["116045_145412_1_not_null", "CHECK", null, null, null], ["116045_145412_2_not_null", "CHECK", null, null, null], ["116045_145412_3_not_null", "CHECK", null, null, null], ["116045_145412_5_not_null", "CHECK", null, null, null]], "hr_salarygradecomponent": [["hr_salarygradecompon_component_id_21bafc82_fk_hr_salary", "FOREIGN KEY", "component_id", "hr_salarycomponent", "id"], ["hr_salarygradecomponent_grade_id_c5c6e463_fk_hr_salarygrade_id", "FOREIGN KEY", "grade_id", "hr_salarygrade", "id"], ["hr_salarygradecomponent_grade_id_component_id_125e67a8_uniq", "UNIQUE", "grade_id", "hr_salarygradecomponent", "component_id"], ["hr_salarygradecomponent_grade_id_component_id_125e67a8_uniq", "UNIQUE", "grade_id", "hr_salarygradecomponent", "grade_id"], ["hr_salarygradecomponent_grade_id_component_id_125e67a8_uniq", "UNIQUE", "component_id", "hr_salarygradecomponent", "component_id"], ["hr_salarygradecomponent_grade_id_component_id_125e67a8_uniq", "UNIQUE", "component_id", "hr_salarygradecomponent", "grade_id"], ["hr_salarygradecomponent_pkey", "PRIMARY KEY", "id", "hr_salarygradecomponent", "id"], ["116045_145425_1_not_null", "CHECK", null, null, null], ["116045_145425_2_not_null", "CHECK", null, null, null], ["116045_145425_3_not_null", "CHECK", null, null, null], ["116045_145425_4_not_null", "CHECK", null, null, null]], "hr_salarystructurecomponent": [["hr_salarystructureco_component_id_9a63f21e_fk_hr_salary", "FOREIGN KEY", "component_id", "hr_salarycomponent", "id"], ["hr_salarystructureco_salary_structure_id_6b1b6db5_fk_hr_staffs", "FOREIGN KEY", "salary_structure_id", "hr_staffsalarystructure", "id"], ["hr_salarystructurecompon_salary_structure_id_comp_acdf8590_uniq", "UNIQUE", "salary_structure_id", "hr_salarystructurecomponent", "component_id"], ["hr_salarystructurecompon_salary_structure_id_comp_acdf8590_uniq", "UNIQUE", "salary_structure_id", "hr_salarystructurecomponent", "salary_structure_id"], ["hr_salarystructurecompon_salary_structure_id_comp_acdf8590_uniq", "UNIQUE", "component_id", "hr_salarystructurecomponent", "component_id"], ["hr_salarystructurecompon_salary_structure_id_comp_acdf8590_uniq", "UNIQUE", "component_id", "hr_salarystructurecomponent", "salary_structure_id"], ["hr_salarystructurecomponent_pkey", "PRIMARY KEY", "id", "hr_salarystructurecomponent", "id"], ["116045_145626_1_not_null", "CHECK", null, null, null], ["116045_145626_2_not_null", "CHECK", null, null, null], ["116045_145626_3_not_null", "CHECK", null, null, null], ["116045_145626_4_not_null", "CHECK", null, null, null]], "hr_staffsalary": [["hr_staffsalary_grade_id_78ab9c67_fk_hr_salarygrade_id", "FOREIGN KEY", "grade_id", "hr_salarygrade", "id"], ["hr_staffsalary_pkey", "PRIMARY KEY", "id", "hr_staffsalary", "id"], ["hr_staffsalary_staff_member_id_ccee4d4e_fk_schools_staffuser_id", "FOREIGN KEY", "staff_member_id", "schools_staffuser", "id"], ["hr_staffsalary_staff_member_id_key", "UNIQUE", "staff_member_id", "hr_staffsalary", "staff_member_id"], ["116045_149179_1_not_null", "CHECK", null, null, null], ["116045_149179_2_not_null", "CHECK", null, null, null], ["116045_149179_3_not_null", "CHECK", null, null, null], ["116045_149179_4_not_null", "CHECK", null, null, null], ["116045_149179_5_not_null", "CHECK", null, null, null]], "hr_staffsalarystructure": [["hr_staffsalarystruct_staff_user_id_ff37facd_fk_schools_s", "FOREIGN KEY", "staff_user_id", "schools_staffuser", "id"], ["hr_staffsalarystructure_pkey", "PRIMARY KEY", "id", "hr_staffsalarystructure", "id"], ["hr_staffsalarystructure_staff_user_id_key", "UNIQUE", "staff_user_id", "hr_staffsalarystructure", "staff_user_id"], ["116045_145618_1_not_null", "CHECK", null, null, null], ["116045_145618_2_not_null", "CHECK", null, null, null], ["116045_145618_3_not_null", "CHECK", null, null, null]], "hr_statutorydeduction": [["hr_statutorydeduction_name_key", "UNIQUE", "name", "hr_statutorydeduction", "name"], ["hr_statutorydeduction_pkey", "PRIMARY KEY", "id", "hr_statutorydeduction", "id"], ["116045_145696_1_not_null", "CHECK", null, null, null], ["116045_145696_2_not_null", "CHECK", null, null, null], ["116045_145696_3_not_null", "CHECK", null, null, null], ["116045_145696_4_not_null", "CHECK", null, null, null], ["116045_145696_5_not_null", "CHECK", null, null, null], ["116045_145696_6_not_null", "CHECK", null, null, null]], "hr_taxbracket": [["hr_taxbracket_pkey", "PRIMARY KEY", "id", "hr_taxbracket", "id"], ["116045_145253_1_not_null", "CHECK", null, null, null], ["116045_145253_2_not_null", "CHECK", null, null, null], ["116045_145253_3_not_null", "CHECK", null, null, null], ["116045_145253_5_not_null", "CHECK", null, null, null], ["116045_145253_6_not_null", "CHECK", null, null, null], ["116045_145253_7_not_null", "CHECK", null, null, null]], "payments_payment": [["payments_payment_academic_year_id_5287e9d6_fk_schools_a", "FOREIGN KEY", "academic_year_id", "schools_academicyear", "id"], ["payments_payment_created_by_id_28f0e284_fk_users_user_id", "FOREIGN KEY", "created_by_id", "users_user", "id"], ["payments_payment_parent_payer_id_d6961b02_fk_students_", "FOREIGN KEY", "parent_payer_id", "students_parentuser", "id"], ["payments_payment_payment_method_id_c909ff25_fk_payments_", "FOREIGN KEY", "payment_method_id", "payments_paymentmethod", "id"], ["payments_payment_pkey", "PRIMARY KEY", "id", "payments_payment", "id"], ["payments_payment_processed_by_staff_i_1dc7a0a7_fk_schools_s", "FOREIGN KEY", "processed_by_staff_id", "schools_staffuser", "id"], ["payments_payment_receipt_number_key", "UNIQUE", "receipt_number", "payments_payment", "receipt_number"], ["payments_payment_student_id_b5fab56a_fk_students_student_id", "FOREIGN KEY", "student_id", "students_student", "id"], ["116045_116702_1_not_null", "CHECK", null, null, null], ["116045_116702_2_not_null", "CHECK", null, null, null], ["116045_116702_3_not_null", "CHECK", null, null, null], ["116045_116702_6_not_null", "CHECK", null, null, null], ["116045_116702_7_not_null", "CHECK", null, null, null], ["116045_116702_8_not_null", "CHECK", null, null, null], ["116045_116702_9_not_null", "CHECK", null, null, null], ["116045_116702_10_not_null", "CHECK", null, null, null]], "payments_paymentallocation": [["payments_paymentallo_invoice_id_c7c856f3_fk_fees_invo", "FOREIGN KEY", "invoice_id", "fees_invoice", "id"], ["payments_paymentallo_payment_id_17cbc394_fk_payments_", "FOREIGN KEY", "payment_id", "payments_payment", "id"], ["payments_paymentallocation_payment_id_invoice_id_3dccc35e_uniq", "UNIQUE", "payment_id", "payments_paymentallocation", "invoice_id"], ["payments_paymentallocation_payment_id_invoice_id_3dccc35e_uniq", "UNIQUE", "payment_id", "payments_paymentallocation", "payment_id"], ["payments_paymentallocation_payment_id_invoice_id_3dccc35e_uniq", "UNIQUE", "invoice_id", "payments_paymentallocation", "invoice_id"], ["payments_paymentallocation_payment_id_invoice_id_3dccc35e_uniq", "UNIQUE", "invoice_id", "payments_paymentallocation", "payment_id"], ["payments_paymentallocation_pkey", "PRIMARY KEY", "id", "payments_paymentallocation", "id"], ["116045_116710_1_not_null", "CHECK", null, null, null], ["116045_116710_2_not_null", "CHECK", null, null, null], ["116045_116710_3_not_null", "CHECK", null, null, null], ["116045_116710_4_not_null", "CHECK", null, null, null]], "payments_paymentmethod": [["payments_paymentmeth_linked_account_id_70d00568_fk_accountin", "FOREIGN KEY", "linked_account_id", "accounting_account", "id"], ["payments_paymentmethod_name_key", "UNIQUE", "name", "payments_paymentmethod", "name"], ["payments_paymentmethod_pkey", "PRIMARY KEY", "id", "payments_paymentmethod", "id"], ["116045_116716_1_not_null", "CHECK", null, null, null], ["116045_116716_4_not_null", "CHECK", null, null, null], ["116045_116716_5_not_null", "CHECK", null, null, null], ["116045_116716_7_not_null", "CHECK", null, null, null]], "portal_admin_adminactivitylog": [["portal_admin_adminac_staff_user_id_b5542baf_fk_schools_s", "FOREIGN KEY", "staff_user_id", "schools_staffuser", "id"], ["portal_admin_adminac_target_content_type__cd3c915a_fk_django_co", "FOREIGN KEY", "target_content_type_id", "django_content_type", "id"], ["portal_admin_adminac_tenant_id_46051055_fk_tenants_s", "FOREIGN KEY", "tenant_id", "tenants_school", "id"], ["portal_admin_adminactivitylog_pkey", "PRIMARY KEY", "id", "portal_admin_adminactivitylog", "id"], ["portal_admin_adminactivitylog_user_id_137e02c8_fk_users_user_id", "FOREIGN KEY", "user_id", "users_user", "id"], ["116045_116968_1_not_null", "CHECK", null, null, null], ["116045_116968_2_not_null", "CHECK", null, null, null], ["116045_116968_3_not_null", "CHECK", null, null, null], ["116045_116968_6_not_null", "CHECK", null, null, null], ["116045_116968_9_not_null", "CHECK", null, null, null]], "school_calendar_eventattendee": [["school_calendar_even_event_id_b6383215_fk_school_ca", "FOREIGN KEY", "event_id", "school_calendar_schoolevent", "id"], ["school_calendar_eventattendee_pkey", "PRIMARY KEY", "id", "school_calendar_eventattendee", "id"], ["school_calendar_eventattendee_user_id_9729f782_fk_users_user_id", "FOREIGN KEY", "user_id", "users_user", "id"], ["116045_117372_1_not_null", "CHECK", null, null, null], ["116045_117372_2_not_null", "CHECK", null, null, null], ["116045_117372_3_not_null", "CHECK", null, null, null], ["116045_117372_4_not_null", "CHECK", null, null, null], ["116045_117372_5_not_null", "CHECK", null, null, null], ["116045_117372_6_not_null", "CHECK", null, null, null]], "school_calendar_eventcategory": [["school_calendar_eventcategory_pkey", "PRIMARY KEY", "id", "school_calendar_eventcategory", "id"], ["116045_117355_1_not_null", "CHECK", null, null, null], ["116045_117355_2_not_null", "CHECK", null, null, null], ["116045_117355_3_not_null", "CHECK", null, null, null], ["116045_117355_4_not_null", "CHECK", null, null, null], ["116045_117355_5_not_null", "CHECK", null, null, null], ["116045_117355_6_not_null", "CHECK", null, null, null], ["116045_117355_7_not_null", "CHECK", null, null, null], ["116045_117355_8_not_null", "CHECK", null, null, null]], "school_calendar_schoolevent": [["school_calendar_scho_category_id_88f7d536_fk_school_ca", "FOREIGN KEY", "category_id", "school_calendar_eventcategory", "id"], ["school_calendar_scho_created_by_id_8abf6bd2_fk_users_use", "FOREIGN KEY", "created_by_id", "users_user", "id"], ["school_calendar_scho_created_by_staff_id_7580a2a5_fk_schools_s", "FOREIGN KEY", "created_by_staff_id", "schools_staffuser", "id"], ["school_calendar_schoolevent_max_attendees_check", "CHECK", null, "school_calendar_schoolevent", "max_attendees"], ["school_calendar_schoolevent_pkey", "PRIMARY KEY", "id", "school_calendar_schoolevent", "id"], ["116045_117363_1_not_null", "CHECK", null, null, null], ["116045_117363_2_not_null", "CHECK", null, null, null], ["116045_117363_3_not_null", "CHECK", null, null, null], ["116045_117363_4_not_null", "CHECK", null, null, null], ["116045_117363_5_not_null", "CHECK", null, null, null], ["116045_117363_6_not_null", "CHECK", null, null, null], ["116045_117363_7_not_null", "CHECK", null, null, null], ["116045_117363_10_not_null", "CHECK", null, null, null], ["116045_117363_11_not_null", "CHECK", null, null, null], ["116045_117363_12_not_null", "CHECK", null, null, null], ["116045_117363_13_not_null", "CHECK", null, null, null], ["116045_117363_15_not_null", "CHECK", null, null, null], ["116045_117363_16_not_null", "CHECK", null, null, null], ["116045_117363_17_not_null", "CHECK", null, null, null], ["116045_117363_18_not_null", "CHECK", null, null, null], ["116045_117363_19_not_null", "CHECK", null, null, null], ["116045_117363_20_not_null", "CHECK", null, null, null], ["116045_117363_21_not_null", "CHECK", null, null, null], ["116045_117363_22_not_null", "CHECK", null, null, null], ["116045_117363_24_not_null", "CHECK", null, null, null], ["116045_117363_25_not_null", "CHECK", null, null, null], ["116045_117363_26_not_null", "CHECK", null, null, null]], "schools_academicsetting": [["schools_academicsett_current_academic_yea_bbefa819_fk_schools_a", "FOREIGN KEY", "current_academic_year_id", "schools_academicyear", "id"], ["schools_academicsetting_pkey", "PRIMARY KEY", "id", "schools_academicsetting", "id"], ["116045_116109_1_not_null", "CHECK", null, null, null], ["116045_116109_2_not_null", "CHECK", null, null, null]], "schools_academicyear": [["schools_academicyear_name_key", "UNIQUE", "name", "schools_academicyear", "name"], ["schools_academicyear_pkey", "PRIMARY KEY", "id", "schools_academicyear", "id"], ["schools_academicyear_pkey", "PRIMARY KEY", "id", "schools_academicyear", "id"], ["schools_academicyear_pkey", "PRIMARY KEY", "id", "schools_academicyear", "id"], ["schools_academicyear_pkey", "PRIMARY KEY", "id", "schools_academicyear", "id"], ["116045_116115_1_not_null", "CHECK", null, null, null], ["116045_116115_2_not_null", "CHECK", null, null, null], ["116045_116115_3_not_null", "CHECK", null, null, null], ["116045_116115_4_not_null", "CHECK", null, null, null], ["116045_116115_5_not_null", "CHECK", null, null, null], ["116045_116115_6_not_null", "CHECK", null, null, null], ["116045_116115_7_not_null", "CHECK", null, null, null], ["116045_116115_8_not_null", "CHECK", null, null, null]], "schools_country": [["schools_country_pkey", "PRIMARY KEY", "id", "schools_country", "id"], ["116045_116123_1_not_null", "CHECK", null, null, null], ["116045_116123_2_not_null", "CHECK", null, null, null], ["116045_116123_3_not_null", "CHECK", null, null, null]], "schools_invoicesequence": [["schools_invoicesequence_last_number_check", "CHECK", null, "schools_invoicesequence", "last_number"], ["schools_invoicesequence_padding_digits_check", "CHECK", null, "schools_invoicesequence", "padding_digits"], ["schools_invoicesequence_pkey", "PRIMARY KEY", "id", "schools_invoicesequence", "id"], ["116045_132754_1_not_null", "CHECK", null, null, null], ["116045_132754_2_not_null", "CHECK", null, null, null], ["116045_132754_3_not_null", "CHECK", null, null, null], ["116045_132754_4_not_null", "CHECK", null, null, null], ["116045_132754_5_not_null", "CHECK", null, null, null], ["116045_132754_6_not_null", "CHECK", null, null, null]], "schools_receiptsequence": [["schools_receiptsequence_last_number_check", "CHECK", null, "schools_receiptsequence", "last_number"], ["schools_receiptsequence_padding_digits_check", "CHECK", null, "schools_receiptsequence", "padding_digits"], ["schools_receiptsequence_pkey", "PRIMARY KEY", "id", "schools_receiptsequence", "id"], ["116045_137892_1_not_null", "CHECK", null, null, null], ["116045_137892_2_not_null", "CHECK", null, null, null], ["116045_137892_3_not_null", "CHECK", null, null, null], ["116045_137892_4_not_null", "CHECK", null, null, null], ["116045_137892_5_not_null", "CHECK", null, null, null], ["116045_137892_6_not_null", "CHECK", null, null, null]], "schools_schoolclass": [["schools_schoolclass_name_c44938eb_uniq", "UNIQUE", "name", "schools_schoolclass", "name"], ["schools_schoolclass_pkey", "PRIMARY KEY", "id", "schools_schoolclass", "id"], ["116045_116129_1_not_null", "CHECK", null, null, null], ["116045_116129_2_not_null", "CHECK", null, null, null], ["116045_116129_4_not_null", "CHECK", null, null, null], ["116045_116129_5_not_null", "CHECK", null, null, null], ["116045_116129_6_not_null", "CHECK", null, null, null]], "schools_schoolprofile": [["schools_schoolprofil_current_academic_yea_8ad46456_fk_schools_a", "FOREIGN KEY", "current_academic_year_id", "schools_academicyear", "id"], ["schools_schoolprofil_default_accounts_rec_e522b278_fk_accountin", "FOREIGN KEY", "default_accounts_receivable_coa_id", "accounting_account", "id"], ["schools_schoolprofil_default_bank_coa_id_2e75e2b8_fk_accountin", "FOREIGN KEY", "default_bank_coa_id", "accounting_account", "id"], ["schools_schoolprofil_default_cash_coa_id_da0d87f0_fk_accountin", "FOREIGN KEY", "default_cash_coa_id", "accounting_account", "id"], ["schools_schoolprofil_default_discount_giv_0acae16d_fk_accountin", "FOREIGN KEY", "default_discount_given_coa_id", "accounting_account", "id"], ["schools_schoolprofil_default_expense_coa__18c0d202_fk_accountin", "FOREIGN KEY", "default_expense_coa_id", "accounting_account", "id"], ["schools_schoolprofil_default_fee_income_c_9fe91809_fk_accountin", "FOREIGN KEY", "default_fee_income_coa_id", "accounting_account", "id"], ["schools_schoolprofile_default_due_days_check", "CHECK", null, "schools_schoolprofile", "default_due_days"], ["schools_schoolprofile_financial_year_start_month_check", "CHECK", null, "schools_schoolprofile", "financial_year_start_month"], ["schools_schoolprofile_pkey", "PRIMARY KEY", "id", "schools_schoolprofile", "id"], ["116045_116145_13_not_null", "CHECK", null, null, null], ["116045_116145_16_not_null", "CHECK", null, null, null], ["116045_116145_17_not_null", "CHECK", null, null, null], ["116045_116145_18_not_null", "CHECK", null, null, null], ["116045_116145_26_not_null", "CHECK", null, null, null]], "schools_section": [["schools_section_class_teacher_id_97d67a70_fk_schools_s", "FOREIGN KEY", "class_teacher_id", "schools_staffuser", "id"], ["schools_section_name_school_class_id_1727d65f_uniq", "UNIQUE", "name", "schools_section", "name"], ["schools_section_name_school_class_id_1727d65f_uniq", "UNIQUE", "name", "schools_section", "school_class_id"], ["schools_section_name_school_class_id_1727d65f_uniq", "UNIQUE", "school_class_id", "schools_section", "name"], ["schools_section_name_school_class_id_1727d65f_uniq", "UNIQUE", "school_class_id", "schools_section", "school_class_id"], ["schools_section_pkey", "PRIMARY KEY", "id", "schools_section", "id"], ["schools_section_school_class_id_2732815a_fk_schools_s", "FOREIGN KEY", "school_class_id", "schools_schoolclass", "id"], ["116045_116179_1_not_null", "CHECK", null, null, null], ["116045_116179_2_not_null", "CHECK", null, null, null], ["116045_116179_3_not_null", "CHECK", null, null, null], ["116045_116179_4_not_null", "CHECK", null, null, null]], "schools_staffuser": [["schools_staffuser_email_key", "UNIQUE", "email", "schools_staffuser", "email"], ["schools_staffuser_email_key", "UNIQUE", "email", "schools_staffuser", "email"], ["schools_staffuser_email_key", "UNIQUE", "email", "schools_staffuser", "email"], ["schools_staffuser_email_key", "UNIQUE", "email", "schools_staffuser", "email"], ["schools_staffuser_email_key", "UNIQUE", "email", "schools_staffuser", "email"], ["schools_staffuser_email_key", "UNIQUE", "email", "schools_staffuser", "email"], ["schools_staffuser_email_key", "UNIQUE", "email", "schools_staffuser", "email"], ["schools_staffuser_email_key", "UNIQUE", "email", "schools_staffuser", "email"], ["schools_staffuser_email_key", "UNIQUE", "email", "schools_staffuser", "email"], ["schools_staffuser_pkey", "PRIMARY KEY", "id", "schools_staffuser", "id"], ["schools_staffuser_pkey", "PRIMARY KEY", "id", "schools_staffuser", "id"], ["schools_staffuser_pkey", "PRIMARY KEY", "id", "schools_staffuser", "id"], ["schools_staffuser_pkey", "PRIMARY KEY", "id", "schools_staffuser", "id"], ["schools_staffuser_pkey", "PRIMARY KEY", "id", "schools_staffuser", "id"], ["schools_staffuser_pkey", "PRIMARY KEY", "id", "schools_staffuser", "id"], ["schools_staffuser_pkey", "PRIMARY KEY", "id", "schools_staffuser", "id"], ["schools_staffuser_pkey", "PRIMARY KEY", "id", "schools_staffuser", "id"], ["schools_staffuser_pkey", "PRIMARY KEY", "id", "schools_staffuser", "id"], ["116045_116155_1_not_null", "CHECK", null, null, null], ["116045_116155_2_not_null", "CHECK", null, null, null], ["116045_116155_4_not_null", "CHECK", null, null, null], ["116045_116155_5_not_null", "CHECK", null, null, null], ["116045_116155_6_not_null", "CHECK", null, null, null], ["116045_116155_8_not_null", "CHECK", null, null, null], ["116045_116155_28_not_null", "CHECK", null, null, null], ["116045_116155_29_not_null", "CHECK", null, null, null], ["116045_116155_30_not_null", "CHECK", null, null, null]], "schools_staffuser_groups": [["schools_staffuser_gr_staffuser_id_d8796370_fk_schools_s", "FOREIGN KEY", "staffuser_id", "schools_staffuser", "id"], ["schools_staffuser_groups_group_id_282cab88_fk_auth_group_id", "FOREIGN KEY", "group_id", "auth_group", "id"], ["schools_staffuser_groups_pkey", "PRIMARY KEY", "id", "schools_staffuser_groups", "id"], ["schools_staffuser_groups_pkey", "PRIMARY KEY", "id", "schools_staffuser_groups", "id"], ["schools_staffuser_groups_pkey", "PRIMARY KEY", "id", "schools_staffuser_groups", "id"], ["schools_staffuser_groups_pkey", "PRIMARY KEY", "id", "schools_staffuser_groups", "id"], ["schools_staffuser_groups_staffuser_id_group_id_2595d24f_uniq", "UNIQUE", "staffuser_id", "schools_staffuser_groups", "group_id"], ["schools_staffuser_groups_staffuser_id_group_id_2595d24f_uniq", "UNIQUE", "staffuser_id", "schools_staffuser_groups", "staffuser_id"], ["schools_staffuser_groups_staffuser_id_group_id_2595d24f_uniq", "UNIQUE", "group_id", "schools_staffuser_groups", "group_id"], ["schools_staffuser_groups_staffuser_id_group_id_2595d24f_uniq", "UNIQUE", "group_id", "schools_staffuser_groups", "staffuser_id"], ["116045_116167_1_not_null", "CHECK", null, null, null], ["116045_116167_2_not_null", "CHECK", null, null, null], ["116045_116167_3_not_null", "CHECK", null, null, null]], "schools_staffuser_user_permissions": [["schools_staffuser_us_permission_id_97ee70ba_fk_auth_perm", "FOREIGN KEY", "permission_id", "auth_permission", "id"], ["schools_staffuser_us_staffuser_id_15a429df_fk_schools_s", "FOREIGN KEY", "staffuser_id", "schools_staffuser", "id"], ["schools_staffuser_user_p_staffuser_id_permission__3a62d52a_uniq", "UNIQUE", "staffuser_id", "schools_staffuser_user_permissions", "permission_id"], ["schools_staffuser_user_p_staffuser_id_permission__3a62d52a_uniq", "UNIQUE", "staffuser_id", "schools_staffuser_user_permissions", "staffuser_id"], ["schools_staffuser_user_p_staffuser_id_permission__3a62d52a_uniq", "UNIQUE", "permission_id", "schools_staffuser_user_permissions", "permission_id"], ["schools_staffuser_user_p_staffuser_id_permission__3a62d52a_uniq", "UNIQUE", "permission_id", "schools_staffuser_user_permissions", "staffuser_id"], ["schools_staffuser_user_permissions_pkey", "PRIMARY KEY", "id", "schools_staffuser_user_permissions", "id"], ["116045_116173_1_not_null", "CHECK", null, null, null], ["116045_116173_2_not_null", "CHECK", null, null, null], ["116045_116173_3_not_null", "CHECK", null, null, null]], "schools_term": [["schools_term_academic_year_id_303ad53e_fk_schools_a", "FOREIGN KEY", "academic_year_id", "schools_academicyear", "id"], ["schools_term_academic_year_id_name_c078c2af_uniq", "UNIQUE", "academic_year_id", "schools_term", "academic_year_id"], ["schools_term_academic_year_id_name_c078c2af_uniq", "UNIQUE", "academic_year_id", "schools_term", "name"], ["schools_term_academic_year_id_name_c078c2af_uniq", "UNIQUE", "name", "schools_term", "academic_year_id"], ["schools_term_academic_year_id_name_c078c2af_uniq", "UNIQUE", "name", "schools_term", "name"], ["schools_term_pkey", "PRIMARY KEY", "id", "schools_term", "id"], ["116045_116185_1_not_null", "CHECK", null, null, null], ["116045_116185_2_not_null", "CHECK", null, null, null], ["116045_116185_3_not_null", "CHECK", null, null, null], ["116045_116185_4_not_null", "CHECK", null, null, null], ["116045_116185_5_not_null", "CHECK", null, null, null], ["116045_116185_6_not_null", "CHECK", null, null, null], ["116045_116185_7_not_null", "CHECK", null, null, null], ["116045_116185_8_not_null", "CHECK", null, null, null]], "students_parentuser": [["students_parentuser_email_key", "UNIQUE", "email", "students_parentuser", "email"], ["students_parentuser_pkey", "PRIMARY KEY", "id", "students_parentuser", "id"], ["116045_116367_1_not_null", "CHECK", null, null, null], ["116045_116367_2_not_null", "CHECK", null, null, null], ["116045_116367_4_not_null", "CHECK", null, null, null], ["116045_116367_5_not_null", "CHECK", null, null, null], ["116045_116367_7_not_null", "CHECK", null, null, null], ["116045_116367_8_not_null", "CHECK", null, null, null], ["116045_116367_9_not_null", "CHECK", null, null, null], ["116045_116367_17_not_null", "CHECK", null, null, null], ["116045_116367_18_not_null", "CHECK", null, null, null], ["116045_116367_19_not_null", "CHECK", null, null, null]], "students_parentuser_groups": [["students_parentuser__parentuser_id_046c895a_fk_students_", "FOREIGN KEY", "parentuser_id", "students_parentuser", "id"], ["students_parentuser_groups_group_id_36ee7381_fk_auth_group_id", "FOREIGN KEY", "group_id", "auth_group", "id"], ["students_parentuser_groups_parentuser_id_group_id_7250462b_uniq", "UNIQUE", "parentuser_id", "students_parentuser_groups", "group_id"], ["students_parentuser_groups_parentuser_id_group_id_7250462b_uniq", "UNIQUE", "parentuser_id", "students_parentuser_groups", "parentuser_id"], ["students_parentuser_groups_parentuser_id_group_id_7250462b_uniq", "UNIQUE", "group_id", "students_parentuser_groups", "group_id"], ["students_parentuser_groups_parentuser_id_group_id_7250462b_uniq", "UNIQUE", "group_id", "students_parentuser_groups", "parentuser_id"], ["students_parentuser_groups_pkey", "PRIMARY KEY", "id", "students_parentuser_groups", "id"], ["116045_116377_1_not_null", "CHECK", null, null, null], ["116045_116377_2_not_null", "CHECK", null, null, null], ["116045_116377_3_not_null", "CHECK", null, null, null]], "students_parentuser_user_permissions": [["students_parentuser__parentuser_id_3abc264a_fk_students_", "FOREIGN KEY", "parentuser_id", "students_parentuser", "id"], ["students_parentuser__permission_id_1279c4cb_fk_auth_perm", "FOREIGN KEY", "permission_id", "auth_permission", "id"], ["students_parentuser_user_parentuser_id_permission_db90c74e_uniq", "UNIQUE", "parentuser_id", "students_parentuser_user_permissions", "parentuser_id"], ["students_parentuser_user_parentuser_id_permission_db90c74e_uniq", "UNIQUE", "parentuser_id", "students_parentuser_user_permissions", "permission_id"], ["students_parentuser_user_parentuser_id_permission_db90c74e_uniq", "UNIQUE", "permission_id", "students_parentuser_user_permissions", "parentuser_id"], ["students_parentuser_user_parentuser_id_permission_db90c74e_uniq", "UNIQUE", "permission_id", "students_parentuser_user_permissions", "permission_id"], ["students_parentuser_user_permissions_pkey", "PRIMARY KEY", "id", "students_parentuser_user_permissions", "id"], ["116045_116383_1_not_null", "CHECK", null, null, null], ["116045_116383_2_not_null", "CHECK", null, null, null], ["116045_116383_3_not_null", "CHECK", null, null, null]], "students_student": [["students_student_admission_number_key", "UNIQUE", "admission_number", "students_student", "admission_number"], ["students_student_created_by_id_5fd5c0b3_fk_schools_staffuser_id", "FOREIGN KEY", "created_by_id", "schools_staffuser", "id"], ["students_student_current_class_id_cf5f558b_fk_schools_s", "FOREIGN KEY", "current_class_id", "schools_schoolclass", "id"], ["students_student_current_section_id_3239129f_fk_schools_s", "FOREIGN KEY", "current_section_id", "schools_section", "id"], ["students_student_pkey", "PRIMARY KEY", "id", "students_student", "id"], ["116045_116389_1_not_null", "CHECK", null, null, null], ["116045_116389_2_not_null", "CHECK", null, null, null], ["116045_116389_3_not_null", "CHECK", null, null, null], ["116045_116389_4_not_null", "CHECK", null, null, null], ["116045_116389_5_not_null", "CHECK", null, null, null], ["116045_116389_7_not_null", "CHECK", null, null, null], ["116045_116389_9_not_null", "CHECK", null, null, null], ["116045_116389_10_not_null", "CHECK", null, null, null], ["116045_116389_11_not_null", "CHECK", null, null, null], ["116045_116389_12_not_null", "CHECK", null, null, null], ["116045_116389_13_not_null", "CHECK", null, null, null], ["116045_116389_14_not_null", "CHECK", null, null, null], ["116045_116389_15_not_null", "CHECK", null, null, null], ["116045_116389_16_not_null", "CHECK", null, null, null], ["116045_116389_17_not_null", "CHECK", null, null, null], ["116045_116389_18_not_null", "CHECK", null, null, null], ["116045_116389_19_not_null", "CHECK", null, null, null], ["116045_116389_20_not_null", "CHECK", null, null, null], ["116045_116389_21_not_null", "CHECK", null, null, null], ["116045_116389_22_not_null", "CHECK", null, null, null], ["116045_116389_23_not_null", "CHECK", null, null, null], ["116045_116389_24_not_null", "CHECK", null, null, null], ["116045_116389_25_not_null", "CHECK", null, null, null], ["116045_116389_26_not_null", "CHECK", null, null, null], ["116045_116389_27_not_null", "CHECK", null, null, null], ["116045_116389_28_not_null", "CHECK", null, null, null], ["116045_116389_29_not_null", "CHECK", null, null, null], ["116045_116389_30_not_null", "CHECK", null, null, null], ["116045_116389_31_not_null", "CHECK", null, null, null], ["116045_116389_32_not_null", "CHECK", null, null, null], ["116045_116389_33_not_null", "CHECK", null, null, null], ["116045_116389_34_not_null", "CHECK", null, null, null], ["116045_116389_35_not_null", "CHECK", null, null, null], ["116045_116389_36_not_null", "CHECK", null, null, null], ["116045_116389_40_not_null", "CHECK", null, null, null], ["116045_116389_41_not_null", "CHECK", null, null, null], ["116045_116389_42_not_null", "CHECK", null, null, null], ["116045_116389_43_not_null", "CHECK", null, null, null], ["116045_116389_44_not_null", "CHECK", null, null, null], ["116045_116389_45_not_null", "CHECK", null, null, null]], "students_student_parents": [["students_student_par_parentuser_id_6b614841_fk_students_", "FOREIGN KEY", "parentuser_id", "students_parentuser", "id"], ["students_student_par_student_id_987ca87a_fk_students_", "FOREIGN KEY", "student_id", "students_student", "id"], ["students_student_parents_pkey", "PRIMARY KEY", "id", "students_student_parents", "id"], ["students_student_parents_student_id_parentuser_id_6dcb9a33_uniq", "UNIQUE", "student_id", "students_student_parents", "parentuser_id"], ["students_student_parents_student_id_parentuser_id_6dcb9a33_uniq", "UNIQUE", "student_id", "students_student_parents", "student_id"], ["students_student_parents_student_id_parentuser_id_6dcb9a33_uniq", "UNIQUE", "parentuser_id", "students_student_parents", "parentuser_id"], ["students_student_parents_student_id_parentuser_id_6dcb9a33_uniq", "UNIQUE", "parentuser_id", "students_student_parents", "student_id"], ["116045_116440_1_not_null", "CHECK", null, null, null], ["116045_116440_2_not_null", "CHECK", null, null, null], ["116045_116440_3_not_null", "CHECK", null, null, null]]}, "essential_data": {"auth_group": {"columns": ["id", "name"], "rows": [[2, "Accountant"], [1, "School Administrators"], [3, "School Administrator"], [4, "Teacher"], [5, "HR Manager"], [6, "Librarian"], [7, "Reception/Front Desk"], [8, "Principal/Head Teacher"]]}, "auth_permission": {"columns": ["id", "name", "content_type_id", "codename"], "rows": [[1, "Can add log entry", 1, "add_logentry"], [2, "Can change log entry", 1, "change_logentry"], [3, "Can delete log entry", 1, "delete_logentry"], [4, "Can view log entry", 1, "view_logentry"], [5, "Can add permission", 2, "add_permission"], [6, "Can change permission", 2, "change_permission"], [7, "Can delete permission", 2, "delete_permission"], [8, "Can view permission", 2, "view_permission"], [9, "Can add group", 3, "add_group"], [10, "Can change group", 3, "change_group"], [11, "Can delete group", 3, "delete_group"], [12, "Can view group", 3, "view_group"], [13, "Can add content type", 4, "add_contenttype"], [14, "Can change content type", 4, "change_contenttype"], [15, "Can delete content type", 4, "delete_contenttype"], [16, "Can view content type", 4, "view_contenttype"], [17, "Can add session", 5, "add_session"], [18, "Can change session", 5, "change_session"], [19, "Can delete session", 5, "delete_session"], [20, "Can view session", 5, "view_session"], [21, "Can add School Domain", 6, "add_domain"], [22, "Can change School Domain", 6, "change_domain"], [23, "Can delete School Domain", 6, "delete_domain"], [24, "Can view School Domain", 6, "view_domain"], [25, "Can add School (Tenant)", 7, "add_school"], [26, "Can change School (Tenant)", 7, "change_school"], [27, "Can delete School (Tenant)", 7, "delete_school"], [28, "Can view School (Tenant)", 7, "view_school"], [29, "Can add Platform User", 8, "add_user"], [30, "Can change Platform User", 8, "change_user"], [31, "Can delete Platform User", 8, "delete_user"], [32, "Can view Platform User", 8, "view_user"], [33, "Can add contact inquiry", 9, "add_contactinquiry"], [34, "Can change contact inquiry", 9, "change_contactinquiry"], [35, "Can delete contact inquiry", 9, "delete_contactinquiry"], [36, "Can view contact inquiry", 9, "view_contactinquiry"], [37, "Can add Testimonial", 10, "add_testimonial"], [38, "Can change Testimonial", 10, "change_testimonial"], [39, "Can delete Testimonial", 10, "delete_testimonial"], [40, "Can view Testimonial", 10, "view_testimonial"], [41, "Can add Account Type", 11, "add_accounttype"], [42, "Can change Account Type", 11, "change_accounttype"], [43, "Can delete Account Type", 11, "delete_accounttype"], [44, "Can view Account Type", 11, "view_accounttype"], [45, "Can add General Ledger Transaction", 12, "add_generalledger"], [46, "Can change General Ledger Transaction", 12, "change_generalledger"], [47, "Can delete General Ledger Transaction", 12, "delete_generalledger"], [48, "Can view General Ledger Transaction", 12, "view_generalledger"], [49, "Can add Journal Entry", 13, "add_journalentry"], [50, "Can change Journal Entry", 13, "change_journalentry"], [51, "Can delete Journal Entry", 13, "delete_journalentry"], [52, "Can view Journal Entry", 13, "view_journalentry"], [53, "Can add Journal Entry Item", 14, "add_journalentryitem"], [54, "Can change Journal Entry Item", 14, "change_journalentryitem"], [55, "Can delete Journal Entry Item", 14, "delete_journalentryitem"], [56, "Can view Journal Entry Item", 14, "view_journalentryitem"], [57, "Can add Journal Line", 15, "add_journalline"], [58, "Can change Journal Line", 15, "change_journalline"], [59, "Can delete Journal Line", 15, "delete_journalline"], [60, "Can view Journal Line", 15, "view_journalline"], [61, "Can add Chart of Account Entry", 16, "add_account"], [62, "Can change Chart of Account Entry", 16, "change_account"], [63, "Can delete Chart of Account Entry", 16, "delete_account"], [64, "Can view Chart of Account Entry", 16, "view_account"], [65, "Can add Plan Feature", 17, "add_feature"], [66, "Can change Plan Feature", 17, "change_feature"], [67, "Can delete Plan Feature", 17, "delete_feature"], [68, "Can view Plan Feature", 17, "view_feature"], [69, "Can add School Subscription", 18, "add_subscription"], [70, "Can change School Subscription", 18, "change_subscription"], [71, "Can delete School Subscription", 18, "delete_subscription"], [72, "Can view School Subscription", 18, "view_subscription"], [73, "Can add Subscription Plan", 19, "add_subscriptionplan"], [74, "Can change Subscription Plan", 19, "change_subscriptionplan"], [75, "Can delete Subscription Plan", 19, "delete_subscriptionplan"], [76, "Can view Subscription Plan", 19, "view_subscriptionplan"], [77, "Can add crontab", 20, "add_crontabschedule"], [78, "Can change crontab", 20, "change_crontabschedule"], [79, "Can delete crontab", 20, "delete_crontabschedule"], [80, "Can view crontab", 20, "view_crontabschedule"], [81, "Can add interval", 21, "add_intervalschedule"], [82, "Can change interval", 21, "change_intervalschedule"], [83, "Can delete interval", 21, "delete_intervalschedule"], [84, "Can view interval", 21, "view_intervalschedule"], [85, "Can add periodic task", 22, "add_periodictask"], [86, "Can change periodic task", 22, "change_periodictask"], [87, "Can delete periodic task", 22, "delete_periodictask"], [88, "Can view periodic task", 22, "view_periodictask"], [89, "Can add periodic task track", 23, "add_periodictasks"], [90, "Can change periodic task track", 23, "change_periodictasks"], [91, "Can delete periodic task track", 23, "delete_periodictasks"], [92, "Can view periodic task track", 23, "view_periodictasks"], [93, "Can add solar event", 24, "add_solarschedule"], [94, "Can change solar event", 24, "change_solarschedule"], [95, "Can delete solar event", 24, "delete_solarschedule"], [96, "Can view solar event", 24, "view_solarschedule"], [97, "Can add clocked", 25, "add_clockedschedule"], [98, "Can change clocked", 25, "change_clockedschedule"], [99, "Can delete clocked", 25, "delete_clockedschedule"], [100, "Can view clocked", 25, "view_clockedschedule"], [101, "Can add Academic Setting", 26, "add_academicsetting"], [102, "Can change Academic Setting", 26, "change_academicsetting"], [103, "Can delete Academic Setting", 26, "delete_academicsetting"], [104, "Can view Academic Setting", 26, "view_academicsetting"], [105, "Can add Academic Year", 27, "add_academicyear"], [106, "Can change Academic Year", 27, "change_academicyear"], [107, "Can delete Academic Year", 27, "delete_academicyear"], [108, "Can view Academic Year", 27, "view_academicyear"], [109, "Can add country", 28, "add_country"], [110, "Can change country", 28, "change_country"], [111, "Can delete country", 28, "delete_country"], [112, "Can view country", 28, "view_country"], [113, "Can add School Class", 29, "add_schoolclass"], [114, "Can change School Class", 29, "change_schoolclass"], [115, "Can delete School Class", 29, "delete_schoolclass"], [116, "Can view School Class", 29, "view_schoolclass"], [117, "Can add School Profile", 30, "add_schoolprofile"], [118, "Can change School Profile", 30, "change_schoolprofile"], [119, "Can delete School Profile", 30, "delete_schoolprofile"], [120, "Can view School Profile", 30, "view_schoolprofile"], [121, "Can view outstanding fees report", 30, "view_outstanding_fees_report"], [122, "Can view fee collection report", 30, "view_fee_collection_report"], [123, "Can view expense report", 30, "view_expense_report"], [124, "Can view revenue report", 30, "view_revenue_report"], [125, "Can manage school profile settings", 30, "manage_school_profile"], [126, "Can manage academic year and term settings", 30, "manage_academic_settings"], [127, "Can add staff member", 31, "add_staffuser"], [128, "Can change staff member", 31, "change_staffuser"], [129, "Can delete staff member", 31, "delete_staffuser"], [130, "Can view staff member", 31, "view_staffuser"], [131, "Can add Section", 32, "add_section"], [132, "Can change Section", 32, "change_section"], [133, "Can delete Section", 32, "delete_section"], [134, "Can view Section", 32, "view_section"], [135, "Can add Term / Semester", 33, "add_term"], [136, "Can change Term / Semester", 33, "change_term"], [137, "Can delete Term / Semester", 33, "delete_term"], [138, "Can view Term / Semester", 33, "view_term"], [139, "Can add parent user", 34, "add_parentuser"], [140, "Can change parent user", 34, "change_parentuser"], [141, "Can delete parent user", 34, "delete_parentuser"], [142, "Can view parent user", 34, "view_parentuser"], [143, "Can add student", 35, "add_student"], [144, "Can change student", 35, "change_student"], [145, "Can delete student", 35, "delete_student"], [146, "Can view student", 35, "view_student"], [147, "Can add employee HR profile", 36, "add_employeeprofile"], [148, "Can change employee HR profile", 36, "change_employeeprofile"], [149, "Can delete employee HR profile", 36, "delete_employeeprofile"], [150, "Can view employee HR profile", 36, "view_employeeprofile"], [151, "Can add leave type", 37, "add_leavetype"], [152, "Can change leave type", 37, "change_leavetype"], [153, "Can delete leave type", 37, "delete_leavetype"], [154, "Can view leave type", 37, "view_leavetype"], [155, "Can add leave request", 38, "add_leaverequest"], [156, "Can change leave request", 38, "change_leaverequest"], [157, "Can delete leave request", 38, "delete_leaverequest"], [158, "Can view leave request", 38, "view_leaverequest"], [159, "Can add leave balance", 39, "add_leavebalance"], [160, "Can change leave balance", 39, "change_leavebalance"], [161, "Can delete leave balance", 39, "delete_leavebalance"], [162, "Can view leave balance", 39, "view_leavebalance"], [163, "Can add Account", 40, "add_account"], [164, "Can change Account", 40, "change_account"], [165, "Can delete Account", 40, "delete_account"], [166, "Can view Account", 40, "view_account"], [167, "Can add Concession Type", 41, "add_concessiontype"], [168, "Can change Concession Type", 41, "change_concessiontype"], [169, "Can delete Concession Type", 41, "delete_concessiontype"], [170, "Can view Concession Type", 41, "view_concessiontype"], [171, "Can add Fee Structure", 42, "add_feestructure"], [172, "Can change Fee Structure", 42, "change_feestructure"], [173, "Can delete Fee Structure", 42, "delete_feestructure"], [174, "Can view Fee Structure", 42, "view_feestructure"], [175, "Can add Fee Structure Item", 43, "add_feestructureitem"], [176, "Can change Fee Structure Item", 43, "change_feestructureitem"], [177, "Can delete Fee Structure Item", 43, "delete_feestructureitem"], [178, "Can view Fee Structure Item", 43, "view_feestructureitem"], [179, "Can add Invoice", 44, "add_invoice"], [180, "Can change Invoice", 44, "change_invoice"], [181, "Can delete Invoice", 44, "delete_invoice"], [182, "Can view Invoice", 44, "view_invoice"], [183, "Can add Invoice Detail", 45, "add_invoicedetail"], [184, "Can change Invoice Detail", 45, "change_invoicedetail"], [185, "Can delete Invoice Detail", 45, "delete_invoicedetail"], [186, "Can view Invoice Detail", 45, "view_invoicedetail"], [187, "Can add Student Specific Concession", 46, "add_studentconcession"], [188, "Can change Student Specific Concession", 46, "change_studentconcession"], [189, "Can delete Student Specific Concession", 46, "delete_studentconcession"], [190, "Can view Student Specific Concession", 46, "view_studentconcession"], [191, "Can add Student Fee Allocation", 47, "add_studentfeeallocation"], [192, "Can change Student Fee Allocation", 47, "change_studentfeeallocation"], [193, "Can delete Student Fee Allocation", 47, "delete_studentfeeallocation"], [194, "Can view Student Fee Allocation", 47, "view_studentfeeallocation"], [195, "Can add Fee Head", 48, "add_feehead"], [196, "Can change Fee Head", 48, "change_feehead"], [197, "Can delete Fee Head", 48, "delete_feehead"], [198, "Can view Fee Head", 48, "view_feehead"], [199, "Can add budget", 49, "add_budget"], [200, "Can change budget", 49, "change_budget"], [201, "Can delete budget", 49, "delete_budget"], [202, "Can view budget", 49, "view_budget"], [203, "Can add Budgeted Amount", 50, "add_budgetamount"], [204, "Can change Budgeted Amount", 50, "change_budgetamount"], [205, "Can delete Budgeted Amount", 50, "delete_budgetamount"], [206, "Can view Budgeted Amount", 50, "view_budgetamount"], [207, "Can add Budget Item Category", 51, "add_budgetitem"], [208, "Can change Budget Item Category", 51, "change_budgetitem"], [209, "Can delete Budget Item Category", 51, "delete_budgetitem"], [210, "Can view Budget Item Category", 51, "view_budgetitem"], [211, "Can add Expense Record", 52, "add_expense"], [212, "Can change Expense Record", 52, "change_expense"], [213, "Can delete Expense Record", 52, "delete_expense"], [214, "Can view Expense Record", 52, "view_expense"], [215, "Can add Expense Category", 53, "add_expensecategory"], [216, "Can change Expense Category", 53, "change_expensecategory"], [217, "Can delete Expense Category", 53, "delete_expensecategory"], [218, "Can view Expense Category", 53, "view_expensecategory"], [219, "Can add Vendor/Supplier", 54, "add_vendor"], [220, "Can change Vendor/Supplier", 54, "change_vendor"], [221, "Can delete Vendor/Supplier", 54, "delete_vendor"], [222, "Can view Vendor/Supplier", 54, "view_vendor"], [223, "Can add Payment", 55, "add_payment"], [224, "Can change Payment", 55, "change_payment"], [225, "Can delete Payment", 55, "delete_payment"], [226, "Can view Payment", 55, "view_payment"], [227, "Can add Payment Allocation", 56, "add_paymentallocation"], [228, "Can change Payment Allocation", 56, "change_paymentallocation"], [229, "Can delete Payment Allocation", 56, "delete_paymentallocation"], [230, "Can view Payment Allocation", 56, "view_paymentallocation"], [231, "Can add Payment Method", 57, "add_paymentmethod"], [232, "Can change Payment Method", 57, "change_paymentmethod"], [233, "Can delete Payment Method", 57, "delete_paymentmethod"], [234, "Can view Payment Method", 57, "view_paymentmethod"], [235, "Can add Admin Activity Log", 58, "add_adminactivitylog"], [236, "Can change Admin Activity Log", 58, "change_adminactivitylog"], [237, "Can delete Admin Activity Log", 58, "delete_adminactivitylog"], [238, "Can view Admin Activity Log", 58, "view_adminactivitylog"], [239, "Can add Announcement", 59, "add_announcement"], [240, "Can change Announcement", 59, "change_announcement"], [241, "Can delete Announcement", 59, "delete_announcement"], [242, "Can view Announcement", 59, "view_announcement"], [243, "Can add Communication Log", 60, "add_communicationlog"], [244, "Can change Communication Log", 60, "change_communicationlog"], [245, "Can delete Communication Log", 60, "delete_communicationlog"], [246, "Can view Communication Log", 60, "view_communicationlog"], [247, "Can view Collection Report", 61, "view_collection_report"], [248, "Can view Outstanding Fees Report", 61, "view_outstanding_fees_report"], [249, "Can view Student Ledger Report", 61, "view_student_ledger_report"], [250, "Can view Payment Summary Report", 61, "view_payment_summary_report"], [251, "Can view Trial Balance Report", 61, "view_trial_balance_report"], [252, "Can view Income Statement (P&L)", 61, "view_income_statement_report"], [253, "Can view Balance Sheet Report", 61, "view_balance_sheet_report"], [254, "Can view Cash Flow Statement Report", 61, "view_cash_flow_statement_report"], [255, "Can view Budget Variance Report", 61, "view_budget_variance_report"], [256, "Can view Expense Report", 61, "view_expense_report"], [257, "Can view Fee Projection Report", 61, "view_fee_projection_report"], [258, "Can add Platform Setting", 62, "add_platformsetting"], [259, "Can change Platform Setting", 62, "change_platformsetting"], [260, "Can delete Platform Setting", 62, "delete_platformsetting"], [261, "Can view Platform Setting", 62, "view_platformsetting"], [262, "Can add Platform Announcement", 63, "add_platformannouncement"], [263, "Can change Platform Announcement", 63, "change_platformannouncement"], [264, "Can delete Platform Announcement", 63, "delete_platformannouncement"], [265, "Can view Platform Announcement", 63, "view_platformannouncement"], [266, "Can add Maintenance Mode Setting", 64, "add_maintenancemode"], [267, "Can change Maintenance Mode Setting", 64, "change_maintenancemode"], [268, "Can delete Maintenance Mode Setting", 64, "delete_maintenancemode"], [269, "Can view Maintenance Mode Setting", 64, "view_maintenancemode"], [270, "Can add Platform Audit Log", 65, "add_auditlog"], [271, "Can change Platform Audit Log", 65, "change_auditlog"], [272, "Can delete Platform Audit Log", 65, "delete_auditlog"], [273, "Can view Platform Audit Log", 65, "view_auditlog"], [274, "Can add System Notification", 66, "add_systemnotification"], [275, "Can change System Notification", 66, "change_systemnotification"], [276, "Can delete System Notification", 66, "delete_systemnotification"], [277, "Can view System Notification", 66, "view_systemnotification"], [278, "Can add Platform Announcement", 67, "add_platformannouncement"], [279, "Can change Platform Announcement", 67, "change_platformannouncement"], [280, "Can delete Platform Announcement", 67, "delete_platformannouncement"], [281, "Can view Platform Announcement", 67, "view_platformannouncement"], [282, "Can add Academic Year", 68, "add_academicyear"], [283, "Can change Academic Year", 68, "change_academicyear"], [284, "Can delete Academic Year", 68, "delete_academicyear"], [285, "Can view Academic Year", 68, "view_academicyear"], [286, "Can add Term/Semester", 69, "add_term"], [287, "Can change Term/Semester", 69, "change_term"], [288, "Can delete Term/Semester", 69, "delete_term"], [289, "Can view Term/Semester", 69, "view_term"], [290, "Can add log entry", 70, "add_logentry"], [291, "Can change log entry", 70, "change_logentry"], [292, "Can delete log entry", 70, "delete_logentry"], [293, "Can view log entry", 70, "view_logentry"], [294, "Can add Event Category", 71, "add_eventcategory"], [295, "Can change Event Category", 71, "change_eventcategory"], [296, "Can delete Event Category", 71, "delete_eventcategory"], [297, "Can view Event Category", 71, "view_eventcategory"], [298, "Can add School Event", 72, "add_schoolevent"], [299, "Can change School Event", 72, "change_schoolevent"], [300, "Can delete School Event", 72, "delete_schoolevent"], [301, "Can view School Event", 72, "view_schoolevent"], [302, "Can add Event Attendee", 73, "add_eventattendee"], [303, "Can change Event Attendee", 73, "change_eventattendee"], [304, "Can delete Event Attendee", 73, "delete_eventattendee"], [305, "Can view Event Attendee", 73, "view_eventattendee"], [306, "Can add Invoice Sequence", 74, "add_invoicesequence"], [307, "Can change Invoice Sequence", 74, "change_invoicesequence"], [308, "Can delete Invoice Sequence", 74, "delete_invoicesequence"], [309, "Can view Invoice Sequence", 74, "view_invoicesequence"], [310, "Can view outstanding fees report", 75, "view_outstanding_fees_report"], [311, "Can view collection report", 75, "view_collection_report"], [312, "Can view student ledger report", 75, "view_student_ledger_report"], [313, "Can view Income & Expense Report", 61, "view_income_expense_report"], [314, "Can view Cash Flow Statement", 61, "view_cash_flow_statement"], [315, "Can view the main reports dashboard page", 61, "view_report_dashboard"], [316, "Can add Receipt Sequence", 76, "add_receiptsequence"], [317, "Can change Receipt Sequence", 76, "change_receiptsequence"], [318, "Can delete Receipt Sequence", 76, "delete_receiptsequence"], [319, "Can view Receipt Sequence", 76, "view_receiptsequence"], [320, "Can view the main staff dashboard summary", 77, "view_dashboard_summary"], [321, "Can view the main Announcements module link", 59, "view_announcements_module"], [322, "Can view the main Students & Parents module link", 78, "view_students_module"], [323, "Can view the main HR module and navbar link", 79, "view_hr_module"], [324, "Can create, edit, and manage staff accounts", 79, "manage_staff_users"], [325, "Can approve or reject leave requests", 79, "approve_leave_requests"], [326, "Can configure leave types", 79, "manage_leave_types"], [327, "Can view the main Fees Management module link", 80, "view_fees_module"], [328, "Can view the main Finance module and navbar link", 81, "view_finance_module"], [329, "Can create and manage budgets", 81, "manage_budgets"], [330, "Can record and approve expenses", 81, "manage_expenses"], [331, "Can view the main Setup & Admin module link", 82, "view_setup_admin_module"], [332, "Can assign staff members to roles (groups)", 82, "assign_staff_roles"], [333, "Can view the main Announcements module link", 73, "view_announcements_module"], [334, "Can view Payment Summary Report", 75, "view_payment_summary_report"], [335, "Can view Trial Balance Report", 75, "view_trial_balance_report"], [336, "Can view Income Statement (P&L)", 75, "view_income_statement_report"], [337, "Can view Income & Expense Report", 75, "view_income_expense_report"], [338, "Can view Balance Sheet Report", 75, "view_balance_sheet_report"], [339, "Can view Cash Flow Statement Report", 75, "view_cash_flow_statement_report"], [340, "Can view Cash Flow Statement", 75, "view_cash_flow_statement"], [341, "Can view Budget Variance Report", 75, "view_budget_variance_report"], [342, "Can view Expense Report", 75, "view_expense_report"], [343, "Can view Fee Projection Report", 75, "view_fee_projection_report"], [344, "Can view the main reports dashboard page", 75, "view_report_dashboard"], [345, "Can view the main Calendar module and navbar link", 73, "view_calendar_module"], [346, "Can create, edit, and delete any school event", 73, "manage_all_events"], [347, "Can view the General Ledger report", 81, "view_general_ledger_report"], [348, "Can view the Account Led<PERSON> report", 81, "view_account_ledger_report"], [349, "Can view the main HR module and navbar link", 37, "view_hr_module"], [350, "Can approve or reject leave requests for other staff", 37, "approve_leave_requests"], [351, "Can add Leave Balance Log", 83, "add_leavebalancelog"], [352, "Can change Leave Balance Log", 83, "change_leavebalancelog"], [353, "Can delete Leave Balance Log", 83, "delete_leavebalancelog"], [354, "Can view Leave Balance Log", 83, "view_leavebalancelog"], [355, "Can add Tax Bracket", 84, "add_taxbracket"], [356, "Can change Tax Bracket", 84, "change_taxbracket"], [357, "Can delete Tax Bracket", 84, "delete_taxbracket"], [358, "Can view Tax Bracket", 84, "view_taxbracket"], [359, "Can add Salary Component", 85, "add_salarycomponent"], [360, "Can change Salary Component", 85, "change_salarycomponent"], [361, "Can delete Salary Component", 85, "delete_salarycomponent"], [362, "Can view Salary Component", 85, "view_salarycomponent"], [363, "Can add Payslip Line Item", 86, "add_paysliplineitem"], [364, "Can change Payslip Line Item", 86, "change_paysliplineitem"], [365, "Can delete Payslip Line Item", 86, "delete_paysliplineitem"], [366, "Can view Payslip Line Item", 86, "view_paysliplineitem"], [367, "Can add Payslip", 87, "add_payslip"], [368, "Can change Payslip", 87, "change_payslip"], [369, "Can delete Payslip", 87, "delete_payslip"], [370, "Can view Payslip", 87, "view_payslip"], [371, "Can add Staff Salary Component", 88, "add_staffsalarycomponent"], [372, "Can change Staff Salary Component", 88, "change_staffsalarycomponent"], [373, "Can delete Staff Salary Component", 88, "delete_staffsalarycomponent"], [374, "Can view Staff Salary Component", 88, "view_staffsalarycomponent"], [375, "Can add Staff Salary", 89, "add_staffsalary"], [376, "Can change Staff Salary", 89, "change_staffsalary"], [377, "Can delete Staff Salary", 89, "delete_staffsalary"], [378, "Can view Staff Salary", 89, "view_staffsalary"], [379, "Can add Salary Grade", 90, "add_salarygrade"], [380, "Can change Salary Grade", 90, "change_salarygrade"], [381, "Can delete Salary Grade", 90, "delete_salarygrade"], [382, "Can view Salary Grade", 90, "view_salarygrade"], [383, "Can add Salary Grade Component", 91, "add_salarygradecomponent"], [384, "Can change Salary Grade Component", 91, "change_salarygradecomponent"], [385, "Can delete Salary Grade Component", 91, "delete_salarygradecomponent"], [386, "Can view Salary Grade Component", 91, "view_salarygradecomponent"], [387, "Can add Payroll Run", 92, "add_payrollrun"], [388, "Can change Payroll Run", 92, "change_payrollrun"], [389, "Can delete Payroll Run", 92, "delete_payrollrun"], [390, "Can view Payroll Run", 92, "view_payrollrun"], [391, "Can create and process payroll runs", 92, "manage_payroll"], [392, "Can add staff salary structure", 93, "add_staffsalarystructure"], [393, "Can change staff salary structure", 93, "change_staffsalarystructure"], [394, "Can delete staff salary structure", 93, "delete_staffsalarystructure"], [395, "Can view staff salary structure", 93, "view_staffsalarystructure"], [396, "Can add salary structure component", 94, "add_salarystructurecomponent"], [397, "Can change salary structure component", 94, "change_salarystructurecomponent"], [398, "Can delete salary structure component", 94, "delete_salarystructurecomponent"], [399, "Can view salary structure component", 94, "view_salarystructurecomponent"], [400, "Can add Statutory Deduction", 95, "add_statutorydeduction"], [401, "Can change Statutory Deduction", 95, "change_statutorydeduction"], [402, "Can delete Statutory Deduction", 95, "delete_statutorydeduction"], [403, "Can view Statutory Deduction", 95, "view_statutorydeduction"], [404, "Can add Grade Salary Rule", 96, "add_graderule"], [405, "Can change Grade Salary Rule", 96, "change_graderule"], [406, "Can delete Grade Salary Rule", 96, "delete_graderule"], [407, "Can view Grade Salary Rule", 96, "view_graderule"]]}, "django_content_type": {"columns": ["id", "app_label", "model"], "rows": [[1, "admin", "logentry"], [2, "auth", "permission"], [3, "auth", "group"], [4, "contenttypes", "contenttype"], [5, "sessions", "session"], [6, "tenants", "domain"], [7, "tenants", "school"], [8, "users", "user"], [9, "public_site", "contactinquiry"], [10, "public_site", "testimonial"], [11, "accounting", "accounttype"], [12, "accounting", "general<PERSON>r"], [13, "accounting", "journalentry"], [14, "accounting", "journalentryitem"], [15, "accounting", "journalline"], [16, "accounting", "account"], [17, "subscriptions", "feature"], [18, "subscriptions", "subscription"], [19, "subscriptions", "subscriptionplan"], [20, "django_celery_beat", "crontabschedule"], [21, "django_celery_beat", "intervalschedule"], [22, "django_celery_beat", "periodictask"], [23, "django_celery_beat", "periodictasks"], [24, "django_celery_beat", "solarschedule"], [25, "django_celery_beat", "clockedschedule"], [26, "schools", "academicsetting"], [27, "schools", "academicyear"], [28, "schools", "country"], [29, "schools", "schoolclass"], [30, "schools", "schoolprofile"], [31, "schools", "staffuser"], [32, "schools", "section"], [33, "schools", "term"], [34, "students", "parentuser"], [35, "students", "student"], [36, "hr", "employeeprofile"], [37, "hr", "leavetype"], [38, "hr", "leaverequest"], [39, "hr", "leavebalance"], [40, "fees", "account"], [41, "fees", "concessiontype"], [42, "fees", "feestructure"], [43, "fees", "feestructureitem"], [44, "fees", "invoice"], [45, "fees", "invoicedetail"], [46, "fees", "studentconcession"], [47, "fees", "studentfeeallocation"], [48, "fees", "feehead"], [49, "finance", "budget"], [50, "finance", "budgetamount"], [51, "finance", "budgetitem"], [52, "finance", "expense"], [53, "finance", "expensecategory"], [54, "finance", "vendor"], [55, "payments", "payment"], [56, "payments", "paymentallocation"], [57, "payments", "paymentmethod"], [58, "portal_admin", "adminactivitylog"], [59, "announcements", "announcement"], [60, "communication", "communicationlog"], [61, "reporting", "reportingpermissions"], [62, "platform_management", "platformsetting"], [63, "platform_management", "platformannouncement"], [64, "platform_management", "maintenancemode"], [65, "platform_management", "auditlog"], [66, "platform_management", "systemnotification"], [67, "announcements", "platformannouncement"], [68, "fees", "academicyear"], [69, "fees", "term"], [70, "auditlog", "logentry"], [71, "school_calendar", "eventcategory"], [72, "school_calendar", "schoolevent"], [73, "school_calendar", "eventattendee"], [74, "schools", "invoicesequence"], [75, "reporting", "reportpermissions"], [76, "schools", "receiptsequence"], [77, "schools", "schoolpermissions"], [78, "students", "studentpermissions"], [79, "hr", "hrpermissions"], [80, "fees", "feepermissions"], [81, "finance", "financepermissions"], [82, "portal_admin", "portaladminpermissions"], [83, "hr", "leavebalancelog"], [84, "hr", "taxbracket"], [85, "hr", "salarycomponent"], [86, "hr", "paysliplineitem"], [87, "hr", "payslip"], [88, "hr", "staffsalarycomponent"], [89, "hr", "staffsalary"], [90, "hr", "salarygrade"], [91, "hr", "salarygradecomponent"], [92, "hr", "payrollrun"], [93, "hr", "staffsalarystructure"], [94, "hr", "salarystructurecomponent"], [95, "hr", "statutorydeduction"], [96, "hr", "<PERSON><PERSON><PERSON>"]]}, "fees_concessiontype": {"columns": ["id", "name", "description", "value", "is_active", "created_at", "updated_at", "type"], "rows": [[3, "Test Concession", "Test description", "10.00", true, "2025-06-25 08:17:02.960478+00:00", "2025-06-25 08:17:02.960499+00:00", "PERCENTAGE"], [4, "Test Sibling Discount", "Test discount for siblings", "15.00", true, "2025-06-25 08:17:39.162464+00:00", "2025-06-25 08:17:39.162483+00:00", "PERCENTAGE"], [5, "<PERSON><PERSON><PERSON> Discount", "Second child", "150.00", true, "2025-06-25 08:17:44.275613+00:00", "2025-06-25 08:17:44.275623+00:00", "FIXED_AMOUNT"]]}, "hr_leavetype": {"columns": ["id", "name", "description", "max_annual_days", "is_paid", "requires_approval", "is_active", "created_at", "updated_at", "max_days_per_year_grant", "accrual_frequency", "accrual_rate", "max_accrual_balance", "prorate_accrual"], "rows": [[1, "Annual Leave", "Yearly vacation leave for all staff members", null, true, true, true, "2025-07-07 19:22:02.639002+00:00", "2025-07-07 19:22:02.639015+00:00", "21.00", "MONTHLY", "1.75", 30, true], [3, "Maternity Leave", "Leave for new mothers", null, true, true, true, "2025-07-07 19:22:02.670193+00:00", "2025-07-07 19:22:02.670220+00:00", "90.00", "NONE", "0.00", null, false], [6, "Study Leave", "For Writing Exams", null, true, true, true, "2025-07-08 02:07:49.089895+00:00", "2025-07-08 02:07:49.089929+00:00", "21.00", "ANNUALLY", "0.00", 21, true], [2, "Sick Leave", "Medical leave for illness or medical appointments", null, true, false, true, "2025-07-07 19:22:02.665276+00:00", "2025-07-08 15:49:40.471712+00:00", "18.00", "ANNUALLY", "10.00", 15, false], [4, "Paternity Leave", "Leave for new fathers", null, true, true, true, "2025-07-07 19:22:02.674347+00:00", "2025-07-08 15:49:53.067161+00:00", "30.00", "NONE", "0.00", null, false], [5, "Emergency Leave", "Unpaid leave for emergency situations", null, false, true, true, "2025-07-07 19:22:02.682847+00:00", "2025-07-08 15:50:03.498188+00:00", "6.00", "NONE", "0.00", null, false], [7, "Compassionate Leave", "Bereavement leave", null, true, true, true, "2025-07-09 08:58:30.611260+00:00", "2025-07-09 08:58:30.611319+00:00", "12.00", "NONE", "0.00", 12, true]]}, "payments_paymentmethod": {"columns": ["id", "created_at", "updated_at", "name", "type", "description", "is_active", "linked_account_id"], "rows": [[1, "2025-06-27 09:27:58.854317+00:00", "2025-06-28 19:40:05.917304+00:00", "Cash", "CASH", "", true, 5], [2, "2025-06-27 09:28:34.166388+00:00", "2025-06-28 19:40:21.556042+00:00", "Online Payments (Parent)", "ONLINE_MOCK", "", true, 5], [3, "2025-06-27 09:29:02.952478+00:00", "2025-06-28 19:40:37.598814+00:00", "Online Portal Payment", "CARD_PAYMENT", "", true, 5], [4, "2025-06-28 19:41:28.087934+00:00", "2025-06-28 19:41:28.087950+00:00", "Bank Transfer", "BANK_TRANSFER", "", true, 7]]}, "school_calendar_eventcategory": {"columns": ["id", "name", "color", "icon", "description", "is_active", "created_at", "updated_at"], "rows": [[1, "Academic", "#007bff", "bi-book", "Academic events and activities", true, "2025-06-26 15:40:57.821171+00:00", "2025-06-26 15:40:57.821187+00:00"], [2, "Sports", "#28a745", "bi-trophy", "Sports and athletic events", true, "2025-06-26 15:40:57.825081+00:00", "2025-06-26 15:40:57.825097+00:00"], [3, "Cultural", "#ffc107", "bi-palette", "Cultural and artistic events", true, "2025-06-26 15:40:57.827471+00:00", "2025-06-26 15:40:57.827483+00:00"], [4, "Parent Events", "#17a2b8", "bi-people", "Events for parents and families", true, "2025-06-26 15:40:57.829134+00:00", "2025-06-26 15:40:57.829142+00:00"], [5, "Holidays", "#dc3545", "bi-calendar-heart", "School holidays and breaks", true, "2025-06-26 15:40:57.830762+00:00", "2025-06-26 15:40:57.830770+00:00"], [6, "Examinations", "#6f42c1", "bi-pencil-square", "Exams and assessments", true, "2025-06-26 15:40:57.833575+00:00", "2025-06-26 15:40:57.833592+00:00"]]}, "hr_salarycomponent": {"columns": ["id", "name", "type", "description", "is_percentage"], "rows": [[1, "Basic Salary", "EARNING", "Basic Monthly Salary", false], [2, "Housing Allowance", "EARNING", "Monthly Housing Allowance", false], [3, "Pension Deduction", "DEDUCTION", "Statutory Deduction at a rate of 7.5% of the Gross Salary", false], [4, "Transport Allowance", "EARNING", "Transport relief", false], [5, "PAYE Tax", "DEDUCTION", "Pay As You Earn", true]]}, "hr_statutorydeduction": {"columns": ["id", "name", "employee_contribution_rate", "employer_contribution_rate", "is_active", "payslip_label"], "rows": [[1, "Pension Fund", "7.50", "7.50", true, ""], [2, "Aids Levy", "0.01", "0.01", true, ""], [3, "Dr<PERSON> Levy", "0.01", "0.03", true, ""]]}, "hr_taxbracket": {"columns": ["id", "name", "from_amount", "to_amount", "rate_percent", "deduction_amount", "is_active"], "rows": [[1, "Low_income", "0.00", "50000.00", "10.00", "0.00", true], [3, "high_income", "120000.01", null, "30.00", "19000.00", true], [2, "middle_income", "50000.01", "120000.00", "20.00", "5000.00", true]]}, "finance_expensecategory": {"columns": ["id", "created_at", "updated_at", "name", "description", "is_active", "expense_account_id"], "rows": []}}}