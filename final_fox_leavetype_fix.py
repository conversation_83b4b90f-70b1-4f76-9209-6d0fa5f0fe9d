#!/usr/bin/env python
"""
Final Fox Leave Type Fix
Fix hr_leavetype with all required values

Usage: python final_fox_leavetype_fix.py
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from django.db import connection
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def final_fox_leavetype_fix():
    """Final fix for hr_leavetype in fox"""
    
    logger.info("=== FINAL FOX LEAVETYPE FIX ===")
    
    try:
        with connection.cursor() as cursor:
            cursor.execute('SET search_path TO "fox"')
            
            # Clear existing data
            cursor.execute("DELETE FROM hr_leavetype")
            
            # Insert with all required fields
            cursor.execute("""
                INSERT INTO hr_leavetype (
                    name, description, max_annual_days, is_paid, requires_approval, 
                    is_active, created_at, updated_at, max_days_per_year_grant,
                    accrual_frequency, accrual_rate, max_accrual_balance, prorate_accrual
                ) VALUES
                ('Annual Leave', 'Annual vacation leave', 21, TRUE, TRUE, TRUE, NOW(), NOW(), 21, 'YEARLY', 1.75, 30, FALSE),
                ('Sick Leave', 'Medical leave', 10, TRUE, FALSE, TRUE, NOW(), NOW(), 10, 'YEARLY', 0.83, 15, FALSE),
                ('Maternity Leave', 'Maternity leave', 90, TRUE, TRUE, TRUE, NOW(), NOW(), 90, 'ONCE', 90.0, 90, FALSE),
                ('Emergency Leave', 'Emergency leave', 3, FALSE, TRUE, TRUE, NOW(), NOW(), 3, 'YEARLY', 0.25, 5, FALSE)
            """)
            
            # Verify
            cursor.execute("SELECT COUNT(*) FROM hr_leavetype")
            count = cursor.fetchone()[0]
            logger.info(f"✅ hr_leavetype now has {count} records")
            
            # Show the data
            cursor.execute("SELECT name, max_annual_days, accrual_rate FROM hr_leavetype")
            records = cursor.fetchall()
            for record in records:
                logger.info(f"  - {record[0]}: {record[1]} days, {record[2]} accrual rate")
            
            logger.info("✅ Final fox leavetype fix completed!")
            
    except Exception as e:
        logger.error(f"❌ Failed final fox leavetype fix: {e}")

def main():
    """Main function"""
    logger.info("=== FINAL FOX LEAVETYPE FIX ===")
    
    try:
        final_fox_leavetype_fix()
        
        logger.info("\n🎉 FINAL FOX LEAVETYPE FIX COMPLETE!")
        logger.info("Fox hr_leavetype should now work properly!")
        
        return True
        
    except Exception as e:
        logger.error(f"Final fox leavetype fix failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
