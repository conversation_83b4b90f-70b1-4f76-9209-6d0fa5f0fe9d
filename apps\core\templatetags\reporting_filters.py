from django import template
from decimal import Decimal

register = template.Library()

@register.filter
def subtract(value, arg):
    """Subtract arg from value"""
    try:
        return float(value) - float(arg)
    except (ValueError, TypeError):
        return 0

@register.filter
def get_item(dictionary, key):
    """Get item from dictionary by key"""
    if isinstance(dictionary, dict):
        return dictionary.get(key)
    return None

@register.filter
def make_positive(value):
    """Make a number positive (absolute value)"""
    try:
        return abs(float(value))
    except (ValueError, TypeError):
        return 0

@register.filter
def percentage(value, total):
    """Calculate percentage of value from total"""
    try:
        if float(total) == 0:
            return 0
        return (float(value) / float(total)) * 100
    except (ValueError, TypeError, ZeroDivisionError):
        return 0

@register.filter
def widthsubtract(total, value):
    """Subtract value from total (for progress bar widths)"""
    try:
        result = float(total) - float(value)
        return max(0, result)  # Don't return negative widths
    except (ValueError, TypeError):
        return 0

@register.filter
def div(value, divisor):
    """Divide value by divisor"""
    try:
        if float(divisor) == 0:
            return 0
        return float(value) / float(divisor)
    except (ValueError, TypeError, ZeroDivisionError):
        return 0

@register.filter
def multiply(value, multiplier):
    """Multiply value by multiplier"""
    try:
        return float(value) * float(multiplier)
    except (ValueError, TypeError):
        return 0

@register.filter
def add_decimal(value, arg):
    """Add two decimal values"""
    try:
        return Decimal(str(value)) + Decimal(str(arg))
    except (ValueError, TypeError):
        return Decimal('0.00')

@register.filter
def subtract_decimal(value, arg):
    """Subtract two decimal values"""
    try:
        return Decimal(str(value)) - Decimal(str(arg))
    except (ValueError, TypeError):
        return Decimal('0.00')

@register.filter
def format_currency(value):
    """Format value as currency"""
    try:
        return f"${float(value):,.2f}"
    except (ValueError, TypeError):
        return "$0.00"

@register.filter
def abs_value(value):
    """Return absolute value"""
    try:
        return abs(float(value))
    except (ValueError, TypeError):
        return 0

@register.filter
def default_zero(value):
    """Return 0 if value is None or empty"""
    if value is None or value == '':
        return 0
    return value

@register.filter
def safe_float(value):
    """Safely convert to float"""
    try:
        return float(value)
    except (ValueError, TypeError):
        return 0.0

@register.filter
def safe_int(value):
    """Safely convert to int"""
    try:
        return int(float(value))
    except (ValueError, TypeError):
        return 0

@register.filter
def format_percentage(value):
    """Format value as percentage"""
    try:
        return f"{float(value):.1f}%"
    except (ValueError, TypeError):
        return "0.0%"

@register.filter
def get_variance_class(variance):
    """Get CSS class for variance display"""
    try:
        var_value = float(variance)
        if var_value > 0:
            return "text-success"
        elif var_value < 0:
            return "text-danger"
        else:
            return "text-muted"
    except (ValueError, TypeError):
        return "text-muted"

@register.filter
def get_status_badge_class(status):
    """Get CSS class for status badges"""
    status_classes = {
        'ACTIVE': 'bg-success',
        'INACTIVE': 'bg-secondary',
        'PENDING': 'bg-warning',
        'COMPLETED': 'bg-success',
        'FAILED': 'bg-danger',
        'CANCELLED': 'bg-secondary',
        'OVERDUE': 'bg-danger',
        'PAID': 'bg-success',
        'UNPAID': 'bg-warning',
        'PARTIAL': 'bg-info',
    }
    return status_classes.get(str(status).upper(), 'bg-secondary')

@register.filter
def range_filter(value):
    """Create a range for template loops"""
    try:
        return range(int(value))
    except (ValueError, TypeError):
        return range(0)

@register.filter
def dict_get(dictionary, key):
    """Get value from dictionary - alias for get_item"""
    return get_item(dictionary, key)

@register.filter
def replace(value, args):
    """Replace text in string - args should be 'old,new'"""
    try:
        if ',' in str(args):
            old, new = str(args).split(',', 1)
            return str(value).replace(old, new)
        return value
    except (ValueError, TypeError):
        return value

@register.filter
def divide(value, divisor):
    """Divide value by divisor - alias for div"""
    return div(value, divisor)

@register.filter
def floatformat_custom(value, decimal_places=2):
    """Custom float formatting"""
    try:
        return f"{float(value):.{decimal_places}f}"
    except (ValueError, TypeError):
        return "0.00"

@register.filter
def currency_format(value):
    """Format as currency with commas"""
    try:
        return f"${float(value):,.2f}"
    except (ValueError, TypeError):
        return "$0.00"

@register.filter
def sub(value, arg):
    """Subtract arg from value - alias for subtract"""
    return subtract(value, arg)

@register.filter
def title_replace(value):
    """Apply title case and replace underscores with spaces"""
    try:
        return str(value).replace('_', ' ').title()
    except (ValueError, TypeError):
        return str(value)
