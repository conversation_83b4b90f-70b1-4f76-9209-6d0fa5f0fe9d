#!/usr/bin/env python
"""
Final Fox Fixes
Fix the last remaining issues in fox schema

Usage: python final_fox_fixes.py
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from django.db import connection
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def final_fox_fixes():
    """Fix the last remaining issues in fox"""
    
    logger.info("=== FINAL FOX FIXES ===")
    
    try:
        with connection.cursor() as cursor:
            cursor.execute('SET search_path TO "fox"')
            
            # 1. Add missing roll_number column to students_student
            try:
                cursor.execute("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.columns 
                        WHERE table_schema = 'fox' 
                        AND table_name = 'students_student'
                        AND column_name = 'roll_number'
                    )
                """)
                
                if not cursor.fetchone()[0]:
                    cursor.execute("ALTER TABLE students_student ADD COLUMN roll_number VARCHAR(50) DEFAULT ''")
                    logger.info("✅ Added roll_number column to students_student")
                else:
                    logger.info("✅ roll_number column already exists")
                    
            except Exception as e:
                logger.error(f"❌ Failed to add roll_number column: {e}")
            
            # 2. Create academic year data
            try:
                cursor.execute("SELECT COUNT(*) FROM schools_academicyear")
                if cursor.fetchone()[0] == 0:
                    cursor.execute("""
                        INSERT INTO schools_academicyear (name, start_date, end_date, is_active, is_current, created_at, updated_at) 
                        VALUES ('2024-2025', '2024-09-01', '2025-08-31', TRUE, TRUE, NOW(), NOW())
                    """)
                    logger.info("✅ Created academic year in fox")
                else:
                    logger.info("✅ Academic year already exists")
                    
            except Exception as e:
                logger.error(f"❌ Failed to create academic year: {e}")
            
            # 3. Verify fixes
            logger.info("Verifying fixes...")
            
            # Check roll_number column
            cursor.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.columns 
                    WHERE table_schema = 'fox' 
                    AND table_name = 'students_student'
                    AND column_name = 'roll_number'
                )
            """)
            
            if cursor.fetchone()[0]:
                logger.info("✅ roll_number column verified")
            else:
                logger.error("❌ roll_number column missing")
            
            # Check academic year data
            cursor.execute("SELECT COUNT(*) FROM schools_academicyear")
            count = cursor.fetchone()[0]
            if count > 0:
                logger.info(f"✅ Academic year data verified: {count} records")
            else:
                logger.error("❌ No academic year data")
            
            logger.info("✅ Final fox fixes completed!")
            
    except Exception as e:
        logger.error(f"❌ Failed final fox fixes: {e}")

def main():
    """Main function"""
    logger.info("=== FINAL FOX FIXES ===")
    
    try:
        final_fox_fixes()
        
        logger.info("\n🎉 FINAL FOX FIXES COMPLETE!")
        logger.info("Fox schema should now be fully functional!")
        
        return True
        
    except Exception as e:
        logger.error(f"Final fox fixes failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
