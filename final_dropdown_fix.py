#!/usr/bin/env python
"""
Final Dropdown Fix
Fixes remaining column issues and populates data correctly

Usage: python final_dropdown_fix.py
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from django.db import connection
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def fix_remaining_column_issues():
    """Fix remaining column issues across all schemas"""
    
    schemas = ['mandiva', 'aischool', 'bea', 'ruzivo']
    
    logger.info("=== FIXING REMAINING COLUMN ISSUES ===")
    
    for schema_name in schemas:
        logger.info(f"\nFixing remaining issues for: {schema_name}")
        
        try:
            with connection.cursor() as cursor:
                cursor.execute(f'SET search_path TO "{schema_name}"')
                
                # Fix schools_section missing is_active column
                try:
                    cursor.execute("ALTER TABLE schools_section ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT TRUE")
                    logger.info(f"✅ Added is_active to schools_section in {schema_name}")
                except Exception as e:
                    logger.warning(f"schools_section.is_active in {schema_name}: {e}")
                
                # Fix schools_academicyear missing is_current column
                try:
                    cursor.execute("ALTER TABLE schools_academicyear ADD COLUMN IF NOT EXISTS is_current BOOLEAN DEFAULT FALSE")
                    logger.info(f"✅ Added is_current to schools_academicyear in {schema_name}")
                except Exception as e:
                    logger.warning(f"schools_academicyear.is_current in {schema_name}: {e}")
                
                # Fix ID sequence issues for tables that need them
                tables_needing_sequences = [
                    'schools_academicyear',
                    'schools_term', 
                    'schools_schoolclass',
                    'schools_section',
                    'accounting_accounttype',
                    'accounting_account',
                    'fees_feehead',
                    'payments_paymentmethod',
                    'fees_concessiontype',
                    'hr_leavetype',
                    'school_calendar_eventcategory',
                    'school_calendar_schoolevent'
                ]
                
                for table in tables_needing_sequences:
                    try:
                        # Create sequence if it doesn't exist
                        cursor.execute(f"CREATE SEQUENCE IF NOT EXISTS {table}_id_seq")
                        # Set the sequence as default for id column
                        cursor.execute(f"ALTER TABLE {table} ALTER COLUMN id SET DEFAULT nextval('{table}_id_seq')")
                        # Update sequence to current max value
                        cursor.execute(f"SELECT setval('{table}_id_seq', COALESCE((SELECT MAX(id) FROM {table}), 1))")
                        logger.info(f"✅ Fixed sequence for {table} in {schema_name}")
                    except Exception as e:
                        logger.warning(f"Sequence for {table} in {schema_name}: {e}")
                
        except Exception as e:
            logger.error(f"❌ Failed to fix remaining issues for {schema_name}: {e}")

def populate_data_safely():
    """Populate data safely with proper error handling"""
    
    schemas = ['mandiva', 'aischool', 'bea', 'ruzivo']
    
    logger.info("=== POPULATING DATA SAFELY ===")
    
    for schema_name in schemas:
        logger.info(f"\nPopulating data for: {schema_name}")
        
        try:
            with connection.cursor() as cursor:
                cursor.execute(f'SET search_path TO "{schema_name}"')
                
                # 1. Academic Years (with is_current)
                try:
                    cursor.execute("""
                        INSERT INTO schools_academicyear (name, start_date, end_date, is_active, is_current, created_at, updated_at) VALUES
                        ('2024-2025', '2024-09-01', '2025-08-31', TRUE, TRUE, NOW(), NOW()),
                        ('2025-2026', '2025-09-01', '2026-08-31', FALSE, FALSE, NOW(), NOW())
                        ON CONFLICT DO NOTHING;
                    """)
                    logger.info(f"✅ Created academic years in {schema_name}")
                except Exception as e:
                    logger.warning(f"Academic years in {schema_name}: {e}")
                
                # 2. Terms
                try:
                    cursor.execute("""
                        INSERT INTO schools_term (name, start_date, end_date, academic_year_id, is_active, created_at, updated_at) VALUES
                        ('Term 1', '2024-09-01', '2024-12-15', 1, TRUE, NOW(), NOW()),
                        ('Term 2', '2025-01-15', '2025-04-15', 1, TRUE, NOW(), NOW()),
                        ('Term 3', '2025-04-30', '2025-08-31', 1, TRUE, NOW(), NOW())
                        ON CONFLICT DO NOTHING;
                    """)
                    logger.info(f"✅ Created terms in {schema_name}")
                except Exception as e:
                    logger.warning(f"Terms in {schema_name}: {e}")
                
                # 3. School Classes
                try:
                    cursor.execute("""
                        INSERT INTO schools_schoolclass (name, description, is_active, created_at, updated_at) VALUES
                        ('Grade 1', 'First Grade', TRUE, NOW(), NOW()),
                        ('Grade 2', 'Second Grade', TRUE, NOW(), NOW()),
                        ('Grade 3', 'Third Grade', TRUE, NOW(), NOW()),
                        ('Grade 4', 'Fourth Grade', TRUE, NOW(), NOW()),
                        ('Grade 5', 'Fifth Grade', TRUE, NOW(), NOW()),
                        ('Grade 6', 'Sixth Grade', TRUE, NOW(), NOW())
                        ON CONFLICT DO NOTHING;
                    """)
                    logger.info(f"✅ Created school classes in {schema_name}")
                except Exception as e:
                    logger.warning(f"School classes in {schema_name}: {e}")
                
                # 4. Sections (with is_active)
                try:
                    cursor.execute("""
                        INSERT INTO schools_section (name, school_class_id, is_active, created_at, updated_at) VALUES
                        ('A', 1, TRUE, NOW(), NOW()),
                        ('B', 1, TRUE, NOW(), NOW()),
                        ('A', 2, TRUE, NOW(), NOW()),
                        ('B', 2, TRUE, NOW(), NOW()),
                        ('A', 3, TRUE, NOW(), NOW()),
                        ('B', 3, TRUE, NOW(), NOW())
                        ON CONFLICT DO NOTHING;
                    """)
                    logger.info(f"✅ Created sections in {schema_name}")
                except Exception as e:
                    logger.warning(f"Sections in {schema_name}: {e}")
                
                # 5. Account Types
                try:
                    cursor.execute("""
                        INSERT INTO accounting_accounttype (name, code, classification, normal_balance, statement_section, is_active, created_at, updated_at) VALUES
                        ('Cash', 'CASH', 'ASSET', 'DEBIT', 'BS', TRUE, NOW(), NOW()),
                        ('Bank', 'BANK', 'ASSET', 'DEBIT', 'BS', TRUE, NOW(), NOW()),
                        ('Accounts Receivable', 'AR', 'ASSET', 'DEBIT', 'BS', TRUE, NOW(), NOW()),
                        ('Tuition Income', 'TUITION', 'REVENUE', 'CREDIT', 'IS', TRUE, NOW(), NOW()),
                        ('Other Income', 'OTHER_INC', 'REVENUE', 'CREDIT', 'IS', TRUE, NOW(), NOW())
                        ON CONFLICT DO NOTHING;
                    """)
                    logger.info(f"✅ Created account types in {schema_name}")
                except Exception as e:
                    logger.warning(f"Account types in {schema_name}: {e}")
                
                # 6. Chart of Accounts
                try:
                    cursor.execute("""
                        INSERT INTO accounting_account (name, code, account_type_id, is_active, is_control_account, can_be_used_in_je, created_at, updated_at) VALUES
                        ('Cash in Hand', '1001', 1, TRUE, FALSE, TRUE, NOW(), NOW()),
                        ('Bank - Main Account', '1002', 2, TRUE, FALSE, TRUE, NOW(), NOW()),
                        ('Student Fees Receivable', '1101', 3, TRUE, FALSE, TRUE, NOW(), NOW()),
                        ('Tuition Fees Income', '4001', 4, TRUE, FALSE, TRUE, NOW(), NOW()),
                        ('Registration Fees', '4002', 5, TRUE, FALSE, TRUE, NOW(), NOW())
                        ON CONFLICT DO NOTHING;
                    """)
                    logger.info(f"✅ Created chart of accounts in {schema_name}")
                except Exception as e:
                    logger.warning(f"Chart of accounts in {schema_name}: {e}")
                
                # 7. Fee Heads
                try:
                    cursor.execute("""
                        INSERT INTO fees_feehead (name, description, income_account_id, is_active, created_at, updated_at) VALUES
                        ('Tuition Fee', 'Monthly tuition fee', 4, TRUE, NOW(), NOW()),
                        ('Registration Fee', 'One-time registration fee', 5, TRUE, NOW(), NOW()),
                        ('Library Fee', 'Library usage fee', 5, TRUE, NOW(), NOW())
                        ON CONFLICT DO NOTHING;
                    """)
                    logger.info(f"✅ Created fee heads in {schema_name}")
                except Exception as e:
                    logger.warning(f"Fee heads in {schema_name}: {e}")
                
                # 8. Payment Methods
                try:
                    cursor.execute("""
                        INSERT INTO payments_paymentmethod (name, description, account_id, is_active, created_at, updated_at) VALUES
                        ('Cash', 'Cash payment', 1, TRUE, NOW(), NOW()),
                        ('Bank Transfer', 'Bank transfer payment', 2, TRUE, NOW(), NOW()),
                        ('Mobile Money', 'Mobile money payment', 2, TRUE, NOW(), NOW())
                        ON CONFLICT DO NOTHING;
                    """)
                    logger.info(f"✅ Created payment methods in {schema_name}")
                except Exception as e:
                    logger.warning(f"Payment methods in {schema_name}: {e}")
                
                logger.info(f"✅ Successfully populated data for {schema_name}")
                
        except Exception as e:
            logger.error(f"❌ Failed to populate data for {schema_name}: {e}")

def main():
    """Main function"""
    logger.info("=== FINAL DROPDOWN FIX ===")
    
    try:
        # Fix remaining column issues
        fix_remaining_column_issues()
        
        # Populate data safely
        populate_data_safely()
        
        logger.info("\n🎉 FINAL DROPDOWN FIX COMPLETE!")
        logger.info("All schemas should now have:")
        logger.info("- All required tables and columns")
        logger.info("- Essential data for dropdowns")
        logger.info("- Working dropdown menus")
        logger.info("\nTest all dropdown menus across all tenants!")
        
        return True
        
    except Exception as e:
        logger.error(f"Final dropdown fix failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
