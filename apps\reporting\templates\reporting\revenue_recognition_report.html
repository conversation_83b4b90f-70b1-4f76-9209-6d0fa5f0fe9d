{% extends "tenant_base.html" %}
{% load humanize static core_tags reporting_filters %}

{% block title %}{{ view_title|default:"Revenue Recognition Report" }}{% endblock %}

{% block content %}
<div class="container-fluid mt-3">
    {% with report_icon_class="bi bi-receipt" %}
        {% include "partials/_report_header.html" %}
    {% endwith %}

    {% include "partials/_messages.html" %}
    {% include "partials/_report_filter_form.html" with filter_form=filter_form report_code=report_code specific_filter_title="Revenue Recognition Filters" %}

    <!-- Recognition Summary -->
    <div class="card mb-4">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="bi bi-graph-up me-2"></i>Revenue Recognition Summary</h5>
        </div>
        <div class="card-body">
            <div class="row mb-3">
                <div class="col-md-12">
                    <p>
                        {% if academic_year %}<strong>Academic Year:</strong> {{ academic_year }} | {% endif %}
                        <strong>Recognition Method:</strong> {{ recognition_method|title }} | 
                        <strong>Total Invoices:</strong> {{ recognition_summary.total_invoices }}
                    </p>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-3">
                    <div class="text-center">
                        <h6 class="text-muted">Total Invoiced</h6>
                        <p class="mb-0 fw-bold text-primary fs-4">{{ recognition_summary.total_invoiced|currency }}</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h6 class="text-muted">Recognized Revenue</h6>
                        <p class="mb-0 fw-bold text-success fs-4">{{ recognition_summary.total_recognized|currency }}</p>
                        <small class="text-muted">{{ recognition_summary.recognition_rate|floatformat:1 }}%</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h6 class="text-muted">Deferred Revenue</h6>
                        <p class="mb-0 fw-bold text-warning fs-4">{{ recognition_summary.total_deferred|currency }}</p>
                        <small class="text-muted">{{ recognition_summary.deferred_rate|floatformat:1 }}%</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h6 class="text-muted">Collections</h6>
                        <p class="mb-0 fw-bold text-info fs-4">{{ recognition_summary.total_collected|currency }}</p>
                        <small class="text-muted">{{ recognition_summary.collection_rate|floatformat:1 }}%</small>
                    </div>
                </div>
            </div>
            
            <!-- Recognition Progress -->
            <div class="mt-3">
                <h6>Revenue Recognition Progress</h6>
                <div class="progress" style="height: 25px;">
                    <div class="progress-bar bg-success" role="progressbar" style="width: {{ recognition_summary.recognition_rate }}%">
                        Recognized: {{ recognition_summary.recognition_rate|floatformat:1 }}%
                    </div>
                    <div class="progress-bar bg-warning" role="progressbar" style="width: {{ recognition_summary.deferred_rate }}%">
                        Deferred: {{ recognition_summary.deferred_rate|floatformat:1 }}%
                    </div>
                </div>
                <small class="text-muted">
                    {{ recognition_summary.fully_recognized_count }} fully recognized, 
                    {{ recognition_summary.partially_recognized_count }} partially recognized, 
                    {{ recognition_summary.deferred_count }} deferred
                </small>
            </div>
        </div>
    </div>

    <!-- Recognition Status Breakdown -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="bi bi-pie-chart me-2"></i>Recognition Status</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6">
                            <div class="text-center">
                                <h6 class="text-success">Fully Recognized</h6>
                                <p class="mb-0 fw-bold">{{ recognition_summary.fully_recognized_count }}</p>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center">
                                <h6 class="text-info">Partially Recognized</h6>
                                <p class="mb-0 fw-bold">{{ recognition_summary.partially_recognized_count }}</p>
                            </div>
                        </div>
                    </div>
                    <hr>
                    <div class="row">
                        <div class="col-6">
                            <div class="text-center">
                                <h6 class="text-warning">Deferred</h6>
                                <p class="mb-0 fw-bold">{{ recognition_summary.deferred_count }}</p>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center">
                                <h6 class="text-muted">Not Recognized</h6>
                                <p class="mb-0 fw-bold">{{ recognition_summary.not_recognized_count }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="bi bi-clock me-2"></i>Deferred Revenue by Fee Type</h5>
                </div>
                <div class="card-body">
                    {% if deferred_revenue %}
                    {% for item in deferred_revenue|slice:":4" %}
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>{{ item.fee_type }}</span>
                            <span class="fw-bold">{{ item.total_deferred|currency }}</span>
                        </div>
                        <div class="progress" style="height: 15px;">
                            {% with max_deferred=deferred_revenue.0.total_deferred percentage=item.total_deferred|percentage:max_deferred %}
                            <div class="progress-bar bg-warning" role="progressbar" style="width: {{ percentage }}%">
                            </div>
                            {% endwith %}
                        </div>
                        <small class="text-muted">{{ item.invoice_count }} invoices</small>
                    </div>
                    {% endfor %}
                    {% else %}
                    <p class="text-muted text-center">No deferred revenue</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Revenue Recognition Details -->
    {% if revenue_analysis %}
    <div class="card">
        <div class="card-header bg-light d-flex justify-content-between align-items-center">
            <h5 class="mb-0"><i class="bi bi-table me-2"></i>Revenue Recognition Details</h5>
            <span class="badge bg-primary">{{ revenue_analysis|length }} invoices</span>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-striped table-hover mb-0">
                    <thead class="table-dark">
                        <tr>
                            <th>Invoice</th>
                            <th>Student</th>
                            <th>Fee Type</th>
                            <th class="text-end">Total Amount</th>
                            <th class="text-end">Payments</th>
                            <th class="text-end">Recognized</th>
                            <th class="text-end">Deferred</th>
                            <th class="text-center">Recognition %</th>
                            <th class="text-center">Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for item in revenue_analysis %}
                        <tr class="{% if item.recognition_status == 'Deferred' %}table-warning{% elif item.recognition_status == 'Not Recognized' %}table-light{% endif %}">
                            <td>
                                <strong>{{ item.invoice_number }}</strong>
                                <br><small class="text-muted">{{ item.issue_date }}</small>
                            </td>
                            <td>{{ item.student_name }}</td>
                            <td>
                                <span class="badge bg-secondary">{{ item.fee_type }}</span>
                            </td>
                            <td class="text-end">
                                <span class="fw-bold text-primary">{{ item.total_amount|currency }}</span>
                            </td>
                            <td class="text-end">
                                <span class="text-success">{{ item.payments_received|currency }}</span>
                                {% if item.outstanding_amount > 0 %}
                                <br><small class="text-danger">Outstanding: {{ item.outstanding_amount|currency }}</small>
                                {% endif %}
                            </td>
                            <td class="text-end">
                                <span class="fw-bold text-success">{{ item.recognized_revenue|currency }}</span>
                            </td>
                            <td class="text-end">
                                {% if item.deferred_revenue > 0 %}
                                <span class="fw-bold text-warning">{{ item.deferred_revenue|currency }}</span>
                                {% else %}
                                <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td class="text-center">
                                <div class="progress" style="height: 20px; width: 80px;">
                                    <div class="progress-bar {% if item.recognition_percentage >= 100 %}bg-success{% elif item.recognition_percentage >= 50 %}bg-info{% elif item.recognition_percentage > 0 %}bg-warning{% else %}bg-light{% endif %}" 
                                         role="progressbar" style="width: {{ item.recognition_percentage }}%">
                                    </div>
                                </div>
                                <small class="text-muted">{{ item.recognition_percentage|floatformat:0 }}%</small>
                            </td>
                            <td class="text-center">
                                {% if item.recognition_status == 'Fully Recognized' %}
                                <span class="badge bg-success">{{ item.recognition_status }}</span>
                                {% elif item.recognition_status == 'Partially Recognized' %}
                                <span class="badge bg-info">{{ item.recognition_status }}</span>
                                {% elif item.recognition_status == 'Deferred' %}
                                <span class="badge bg-warning">{{ item.recognition_status }}</span>
                                {% else %}
                                <span class="badge bg-secondary">{{ item.recognition_status }}</span>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                    <tfoot class="table-light">
                        <tr>
                            <th colspan="3">Totals:</th>
                            <th class="text-end">{{ recognition_summary.total_invoiced|currency }}</th>
                            <th class="text-end">{{ recognition_summary.total_collected|currency }}</th>
                            <th class="text-end text-success">{{ recognition_summary.total_recognized|currency }}</th>
                            <th class="text-end text-warning">{{ recognition_summary.total_deferred|currency }}</th>
                            <th class="text-center">{{ recognition_summary.recognition_rate|floatformat:1 }}%</th>
                            <th></th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Compliance & Insights -->
    {% if revenue_analysis %}
    <div class="card mt-4">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="bi bi-lightbulb me-2"></i>Revenue Recognition Insights</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h6>Recognition Method: {{ recognition_method|title }}</h6>
                    <ul class="list-unstyled">
                        {% if recognition_method == 'accrual' %}
                        <li><i class="bi bi-check-circle text-success me-2"></i>Revenue recognized based on service delivery period</li>
                        <li><i class="bi bi-info-circle text-info me-2"></i>Proportional recognition for ongoing academic periods</li>
                        <li><i class="bi bi-calendar text-primary me-2"></i>Complies with accrual accounting principles</li>
                        {% elif recognition_method == 'cash' %}
                        <li><i class="bi bi-cash text-success me-2"></i>Revenue recognized when payment is received</li>
                        <li><i class="bi bi-speedometer text-info me-2"></i>Simple and immediate recognition method</li>
                        <li><i class="bi bi-exclamation-triangle text-warning me-2"></i>May not reflect true earning pattern</li>
                        {% elif recognition_method == 'deferred' %}
                        <li><i class="bi bi-clock text-warning me-2"></i>Focus on unearned revenue tracking</li>
                        <li><i class="bi bi-shield-check text-success me-2"></i>Conservative approach to revenue recognition</li>
                        <li><i class="bi bi-graph-down text-info me-2"></i>Helps manage cash flow expectations</li>
                        {% else %}
                        <li><i class="bi bi-trophy text-success me-2"></i>Revenue recognized as services are earned</li>
                        <li><i class="bi bi-balance text-info me-2"></i>Balances payment timing with service delivery</li>
                        <li><i class="bi bi-check-square text-primary me-2"></i>Matches revenue with actual service provision</li>
                        {% endif %}
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6>Key Insights</h6>
                    <ul class="list-unstyled">
                        <li><i class="bi bi-graph-up text-success me-2"></i>
                            {{ recognition_summary.recognition_rate|floatformat:1 }}% of total revenue has been recognized
                        </li>
                        <li><i class="bi bi-clock text-warning me-2"></i>
                            {{ recognition_summary.total_deferred|currency }} in deferred revenue
                        </li>
                        <li><i class="bi bi-cash-stack text-info me-2"></i>
                            {{ recognition_summary.collection_rate|floatformat:1 }}% collection rate achieved
                        </li>
                        {% if recognition_summary.deferred_rate > 20 %}
                        <li><i class="bi bi-exclamation-triangle text-warning me-2"></i>
                            High deferred revenue percentage may indicate future service obligations
                        </li>
                        {% endif %}
                        {% if recognition_summary.collection_rate < 80 %}
                        <li><i class="bi bi-arrow-right text-primary me-2"></i>
                            Consider improving collection processes to enhance cash flow
                        </li>
                        {% endif %}
                    </ul>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- No Data Message -->
    {% if not revenue_analysis %}
    <div class="card">
        <div class="card-body text-center py-5">
            <i class="bi bi-receipt display-1 text-muted mb-3"></i>
            <h4 class="text-muted">No Revenue Data Available</h4>
            <p class="text-muted">No invoices found for the selected criteria.</p>
            <div class="mt-3">
                <a href="{% url 'reporting:revenue_recognition_report' %}" class="btn btn-outline-primary me-2">
                    <i class="bi bi-arrow-clockwise me-2"></i>Reset Filters
                </a>
            </div>
        </div>
    </div>
    {% endif %}

</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Animate progress bars
    $('.progress-bar').each(function() {
        var $this = $(this);
        var width = $this.css('width');
        $this.css('width', '0%').animate({width: width}, 1500);
    });
    
    // Highlight deferred and unrecognized rows
    $('.table-warning').hover(
        function() {
            $(this).addClass('table-active');
        },
        function() {
            $(this).removeClass('table-active');
        }
    );
    
    // Add click handlers for revenue rows
    $('tbody tr').on('click', function() {
        $(this).toggleClass('table-info');
    });
});
</script>
{% endblock %}

