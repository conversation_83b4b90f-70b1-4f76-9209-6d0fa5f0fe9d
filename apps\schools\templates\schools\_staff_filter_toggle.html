{# apps/schools/templates/schools/_staff_filter_toggle.html - Staff List Toggle Filter #}
{% load i18n widget_tweaks %}

{% if filter_form %}
<style>
    /* Premium Staff Filter Toggle Design */
    .premium-staff-filter-card {
        border: none;
        border-radius: 1.5rem;
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        transition: all 0.3s ease;
        margin-bottom: 2rem;
    }

    .premium-staff-filter-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    }

    .premium-staff-filter-header {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
        padding: 1.5rem 2rem;
        border-radius: 1.5rem 1.5rem 0 0;
        position: relative;
        overflow: hidden;
    }

    .premium-staff-filter-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 100%);
        pointer-events: none;
    }

    .premium-staff-filter-header h5 {
        margin: 0;
        font-weight: 600;
        font-size: 1.1rem;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .premium-staff-filter-header .toggle-icon {
        transition: transform 0.3s ease;
        font-size: 1.2rem;
    }

    .premium-staff-filter-header[aria-expanded="true"] .toggle-icon {
        transform: rotate(180deg);
    }

    .premium-staff-filter-body {
        padding: 2rem;
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
    }

    .premium-staff-form-group {
        position: relative;
        margin-bottom: 1.5rem;
    }

    .premium-staff-form-group label {
        position: absolute;
        top: 0.75rem;
        left: 1rem;
        background: white;
        padding: 0 0.5rem;
        font-size: 0.875rem;
        color: #6c757d;
        transition: all 0.3s ease;
        pointer-events: none;
        z-index: 2;
    }

    .premium-staff-form-group input:focus + label,
    .premium-staff-form-group input:not(:placeholder-shown) + label,
    .premium-staff-form-group select:focus + label,
    .premium-staff-form-group select:not([value=""]) + label {
        top: -0.5rem;
        font-size: 0.75rem;
        color: #28a745;
        font-weight: 500;
    }

    .premium-staff-form-control {
        width: 100%;
        padding: 1rem 1rem 1rem 3rem;
        border: 2px solid #e9ecef;
        border-radius: 1rem;
        font-size: 0.95rem;
        transition: all 0.3s ease;
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(5px);
    }

    .premium-staff-form-control:focus {
        border-color: #28a745;
        box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
        outline: none;
        background: white;
    }

    .premium-staff-form-icon {
        position: absolute;
        left: 1rem;
        top: 50%;
        transform: translateY(-50%);
        color: #6c757d;
        font-size: 1.1rem;
        z-index: 3;
        transition: color 0.3s ease;
    }

    .premium-staff-form-group:focus-within .premium-staff-form-icon {
        color: #28a745;
    }

    .premium-staff-action-buttons {
        display: flex;
        gap: 1rem;
        flex-wrap: wrap;
        align-items: center;
        justify-content: space-between;
        margin-top: 2rem;
        padding-top: 1.5rem;
        border-top: 1px solid #e9ecef;
    }

    .premium-staff-filter-actions {
        display: flex;
        gap: 0.75rem;
        flex-wrap: wrap;
    }

    .premium-staff-export-actions {
        display: flex;
        gap: 0.5rem;
        flex-wrap: wrap;
    }

    .premium-staff-btn {
        padding: 0.75rem 1.5rem;
        border-radius: 0.75rem;
        font-weight: 500;
        font-size: 0.9rem;
        transition: all 0.3s ease;
        border: none;
        cursor: pointer;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        text-decoration: none;
        white-space: nowrap;
    }

    .premium-staff-btn-primary {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
        box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
    }

    .premium-staff-btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(40, 167, 69, 0.4);
        color: white;
    }

    .premium-staff-btn-secondary {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        color: #495057;
        border: 1px solid #dee2e6;
    }

    .premium-staff-btn-secondary:hover {
        background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
        transform: translateY(-1px);
        color: #495057;
    }

    .premium-staff-btn-success {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
        box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
    }

    .premium-staff-btn-success:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(40, 167, 69, 0.4);
        color: white;
    }

    .premium-staff-btn-info {
        background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
        color: white;
        box-shadow: 0 4px 15px rgba(23, 162, 184, 0.3);
    }

    .premium-staff-btn-info:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(23, 162, 184, 0.4);
        color: white;
    }

    .premium-staff-btn-warning {
        background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
        color: #212529;
        box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3);
    }

    .premium-staff-btn-warning:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(255, 193, 7, 0.4);
        color: #212529;
    }

    .premium-staff-btn-danger {
        background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);
        color: white;
        box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
    }

    .premium-staff-btn-danger:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(220, 53, 69, 0.4);
        color: white;
    }

    .premium-staff-btn-sm {
        padding: 0.5rem 1rem;
        font-size: 0.8rem;
    }

    /* Loading states */
    .premium-staff-btn.loading {
        opacity: 0.7;
        pointer-events: none;
    }

    .premium-staff-btn.loading::after {
        content: '';
        width: 1rem;
        height: 1rem;
        border: 2px solid transparent;
        border-top: 2px solid currentColor;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-left: 0.5rem;
    }

    @keyframes spin {
        to { transform: rotate(360deg); }
    }

    /* Responsive design */
    @media (max-width: 768px) {
        .premium-staff-filter-header,
        .premium-staff-filter-body {
            padding: 1rem;
        }

        .premium-staff-action-buttons {
            flex-direction: column;
            align-items: stretch;
        }

        .premium-staff-filter-actions,
        .premium-staff-export-actions {
            justify-content: center;
        }

        .premium-staff-btn {
            justify-content: center;
        }
    }

    /* Animation for collapse */
    .premium-staff-filter-collapse {
        transition: all 0.3s ease;
    }

    .premium-staff-filter-collapse.collapsing {
        transition: height 0.3s ease;
    }
</style>

<div class="premium-staff-filter-card">
    <div class="premium-staff-filter-header" 
         data-bs-toggle="collapse" 
         data-bs-target="#staffFilterCollapse" 
         aria-expanded="false" 
         aria-controls="staffFilterCollapse"
         role="button">
        <h5>
            <i class="bi bi-funnel-fill"></i>
            {% trans "Toggle Filters" %}
            <div class="ms-auto d-flex align-items-center gap-3">
                <!-- Export Actions in Header -->
                <div class="premium-staff-export-actions">
                    <a href="?{% if filter_params %}{{ filter_params }}&{% endif %}export=csv"
                       class="premium-staff-btn premium-staff-btn-success premium-staff-btn-sm"
                       title="{% trans 'Export as CSV' %}">
                        <i class="bi bi-filetype-csv"></i>
                        <span class="d-none d-md-inline">CSV</span>
                    </a>
                    <a href="?{% if filter_params %}{{ filter_params }}&{% endif %}export=excel"
                       class="premium-staff-btn premium-staff-btn-info premium-staff-btn-sm"
                       title="{% trans 'Export as Excel' %}">
                        <i class="bi bi-file-earmark-excel"></i>
                        <span class="d-none d-md-inline">Excel</span>
                    </a>
                    <a href="?{% if filter_params %}{{ filter_params }}&{% endif %}export=pdf"
                       class="premium-staff-btn premium-staff-btn-danger premium-staff-btn-sm"
                       title="{% trans 'Export as PDF' %}">
                        <i class="bi bi-filetype-pdf"></i>
                        <span class="d-none d-md-inline">PDF</span>
                    </a>
                    <button type="button"
                            class="premium-staff-btn premium-staff-btn-warning premium-staff-btn-sm"
                            title="{% trans 'Print Report' %}">
                        <i class="bi bi-printer"></i>
                        <span class="d-none d-md-inline">{% trans "Print" %}</span>
                    </button>
                </div>
                <i class="bi bi-chevron-down toggle-icon"></i>
            </div>
        </h5>
    </div>
    
    <div class="collapse premium-staff-filter-collapse" id="staffFilterCollapse">
        <div class="premium-staff-filter-body">
            <form method="get" id="staffFilterForm">
                <div class="row g-4">
                    {% for field in filter_form %}
                        <div class="col-md-4 col-sm-6">
                            <div class="premium-staff-form-group">
                                {% if field.field.widget.input_type == 'text' %}
                                    <i class="premium-staff-form-icon bi bi-search"></i>
                                {% elif field.field.widget.input_type == 'email' %}
                                    <i class="premium-staff-form-icon bi bi-envelope"></i>
                                {% elif field.field.widget.input_type == 'select' or field.field.widget.input_type == 'select_multiple' %}
                                    <i class="premium-staff-form-icon bi bi-list"></i>
                                {% else %}
                                    <i class="premium-staff-form-icon bi bi-filter"></i>
                                {% endif %}
                                
                                {{ field|add_class:"premium-staff-form-control" }}
                                <label for="{{ field.id_for_label }}">{{ field.label }}</label>
                            </div>
                        </div>
                    {% endfor %}
                </div>

                <div class="premium-staff-action-buttons">
                    <div class="premium-staff-filter-actions">
                        <button type="submit" class="premium-staff-btn premium-staff-btn-primary">
                            <i class="bi bi-search"></i>
                            {% trans "Apply Filters" %}
                        </button>
                        {% if request.GET %}
                            <a href="{% url 'schools:staff_list' %}" 
                               class="premium-staff-btn premium-staff-btn-secondary">
                                <i class="bi bi-x-circle"></i>
                                {% trans "Clear Filters" %}
                            </a>
                        {% endif %}
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Enhanced form interactions
    const form = document.getElementById('staffFilterForm');
    const formControls = document.querySelectorAll('.premium-staff-form-control');
    
    // Add floating label behavior
    formControls.forEach(control => {
        // Set initial state
        if (control.value && control.value !== '') {
            control.classList.add('has-value');
        }
        
        // Handle input events
        control.addEventListener('input', function() {
            if (this.value && this.value !== '') {
                this.classList.add('has-value');
            } else {
                this.classList.remove('has-value');
            }
        });
        
        // Handle change events for selects
        control.addEventListener('change', function() {
            if (this.value && this.value !== '') {
                this.classList.add('has-value');
            } else {
                this.classList.remove('has-value');
            }
        });
    });
    
    // Add loading states to buttons
    const buttons = document.querySelectorAll('.premium-staff-btn');
    buttons.forEach(button => {
        if (button.type === 'submit' || button.href) {
            button.addEventListener('click', function() {
                if (!this.classList.contains('loading')) {
                    this.classList.add('loading');
                    // Remove loading state after 3 seconds as fallback
                    setTimeout(() => {
                        this.classList.remove('loading');
                    }, 3000);
                }
            });
        }
    });
});
</script>
{% endif %}
