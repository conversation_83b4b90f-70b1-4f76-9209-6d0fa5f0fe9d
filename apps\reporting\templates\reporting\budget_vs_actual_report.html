{% extends "tenant_base.html" %}
{% load humanize static core_tags %}

{% block title %}{{ view_title|default:"Budget vs Actual Report" }}{% endblock %}

{% block content %}
<div class="container-fluid mt-3">
    {% with report_icon_class="bi bi-graph-up-arrow" %}
        {% include "partials/_report_header.html" %}
    {% endwith %}

    {% include "partials/_messages.html" %}
    {% include "partials/_report_filter_form.html" with filter_form=filter_form report_code=report_code specific_filter_title="Budget Analysis Filters" %}

    <!-- Report Summary Card -->
    <div class="card mb-4">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="bi bi-bar-chart me-2"></i>Budget Performance Summary</h5>
        </div>
        <div class="card-body">
            {% if academic_year or term %}
            <div class="row mb-3">
                <div class="col-md-6">
                    {% if academic_year %}
                    <p><strong>Academic Year:</strong> {{ academic_year }}</p>
                    {% endif %}
                    {% if term %}
                    <p><strong>Term:</strong> {{ term }}</p>
                    {% endif %}
                </div>
            </div>
            {% endif %}
            
            <div class="row">
                <div class="col-md-3">
                    <div class="text-center">
                        <h6 class="text-muted">Budgeted Income</h6>
                        <p class="mb-0 fw-bold text-success">{{ total_budgeted_income|currency }}</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h6 class="text-muted">Actual Income</h6>
                        <p class="mb-0 fw-bold {% if variance_summary.income_variance >= 0 %}text-success{% else %}text-warning{% endif %}">
                            {{ total_actual_income|currency }}
                        </p>
                        {% if variance_summary.income_variance_pct %}
                        <small class="{% if variance_summary.income_variance >= 0 %}text-success{% else %}text-warning{% endif %}">
                            ({{ variance_summary.income_variance_pct|floatformat:1 }}%)
                        </small>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h6 class="text-muted">Budgeted Expense</h6>
                        <p class="mb-0 fw-bold text-danger">{{ total_budgeted_expense|currency }}</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h6 class="text-muted">Actual Expense</h6>
                        <p class="mb-0 fw-bold {% if variance_summary.expense_variance <= 0 %}text-success{% else %}text-danger{% endif %}">
                            {{ total_actual_expense|currency }}
                        </p>
                        {% if variance_summary.expense_variance_pct %}
                        <small class="{% if variance_summary.expense_variance <= 0 %}text-success{% else %}text-danger{% endif %}">
                            ({{ variance_summary.expense_variance_pct|floatformat:1 }}%)
                        </small>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <hr>
            
            <div class="row">
                <div class="col-md-4">
                    <div class="text-center">
                        <h6 class="text-muted">Net Budget</h6>
                        <p class="mb-0 fw-bold text-primary">{{ variance_summary.net_budget|currency }}</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="text-center">
                        <h6 class="text-muted">Net Actual</h6>
                        <p class="mb-0 fw-bold {% if variance_summary.net_actual >= variance_summary.net_budget %}text-success{% else %}text-warning{% endif %}">
                            {{ variance_summary.net_actual|currency }}
                        </p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="text-center">
                        <h6 class="text-muted">Net Variance</h6>
                        <p class="mb-0 fw-bold {% if variance_summary.net_variance >= 0 %}text-success{% else %}text-danger{% endif %}">
                            {{ variance_summary.net_variance|currency }}
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Variance Analysis Chart -->
    {% if variance_summary %}
    <div class="card mb-4">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="bi bi-pie-chart me-2"></i>Variance Analysis</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h6>Income Variance</h6>
                    <div class="progress mb-2" style="height: 25px;">
                        {% with income_pct=variance_summary.income_variance_pct|floatformat:1 %}
                        <div class="progress-bar {% if variance_summary.income_variance >= 0 %}bg-success{% else %}bg-warning{% endif %}" 
                             role="progressbar" style="width: {% if variance_summary.income_variance >= 0 %}{{ income_pct }}{% else %}{{ income_pct|make_positive }}{% endif %}%">
                            {{ income_pct }}%
                        </div>
                        {% endwith %}
                    </div>
                    <p class="small">
                        {% if variance_summary.income_variance >= 0 %}
                        <span class="text-success">{{ variance_summary.income_variance|currency }} above budget</span>
                        {% else %}
                        <span class="text-warning">{{ variance_summary.income_variance|make_positive|currency }} below budget</span>
                        {% endif %}
                    </p>
                </div>
                <div class="col-md-6">
                    <h6>Expense Variance</h6>
                    <div class="progress mb-2" style="height: 25px;">
                        {% with expense_pct=variance_summary.expense_variance_pct|floatformat:1 %}
                        <div class="progress-bar {% if variance_summary.expense_variance <= 0 %}bg-success{% else %}bg-danger{% endif %}" 
                             role="progressbar" style="width: {% if variance_summary.expense_variance <= 0 %}{{ expense_pct|make_positive }}{% else %}{{ expense_pct }}{% endif %}%">
                            {{ expense_pct }}%
                        </div>
                        {% endwith %}
                    </div>
                    <p class="small">
                        {% if variance_summary.expense_variance <= 0 %}
                        <span class="text-success">{{ variance_summary.expense_variance|make_positive|currency }} under budget</span>
                        {% else %}
                        <span class="text-danger">{{ variance_summary.expense_variance|currency }} over budget</span>
                        {% endif %}
                    </p>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Budget Analysis Details -->
    {% if budget_analysis %}
    <div class="card">
        <div class="card-header bg-light d-flex justify-content-between align-items-center">
            <h5 class="mb-0"><i class="bi bi-table me-2"></i>Budget Item Analysis</h5>
            <span class="badge bg-primary">{{ budget_analysis|length }} items</span>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-striped table-hover mb-0">
                    <thead class="table-dark">
                        <tr>
                            <th>Budget Item</th>
                            <th>Type</th>
                            <th>Account</th>
                            <th class="text-end">Budgeted</th>
                            <th class="text-end">Actual</th>
                            <th class="text-end">Variance</th>
                            <th class="text-end">Variance %</th>
                            <th class="text-center">Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for item in budget_analysis %}
                        <tr>
                            <td>
                                <strong>{{ item.budget_item.name }}</strong>
                                {% if item.budget_item.description %}
                                <br><small class="text-muted">{{ item.budget_item.description|truncatechars:50 }}</small>
                                {% endif %}
                            </td>
                            <td>
                                {% if item.budget_item.budget_item_type == 'INCOME' %}
                                <span class="badge bg-success">Income</span>
                                {% else %}
                                <span class="badge bg-danger">Expense</span>
                                {% endif %}
                            </td>
                            <td>
                                {{ item.account_name }}
                                {% if item.account_code %}
                                <br><small class="text-muted">{{ item.account_code }}</small>
                                {% endif %}
                            </td>
                            <td class="text-end">
                                <span class="{% if item.budget_item.budget_item_type == 'INCOME' %}text-success{% else %}text-danger{% endif %}">
                                    {{ item.budgeted_amount|currency }}
                                </span>
                            </td>
                            <td class="text-end">
                                <span class="{% if item.budget_item.budget_item_type == 'INCOME' %}text-success{% else %}text-danger{% endif %}">
                                    {{ item.actual_amount|currency }}
                                </span>
                            </td>
                            <td class="text-end">
                                <span class="{% if item.is_favorable %}text-success{% else %}text-warning{% endif %} fw-bold">
                                    {% if item.variance_amount >= 0 %}+{% endif %}{{ item.variance_amount|currency }}
                                </span>
                            </td>
                            <td class="text-end">
                                <span class="{% if item.is_favorable %}text-success{% else %}text-warning{% endif %} fw-bold">
                                    {% if item.variance_percentage >= 0 %}+{% endif %}{{ item.variance_percentage|floatformat:1 }}%
                                </span>
                            </td>
                            <td class="text-center">
                                {% if item.is_favorable %}
                                <span class="badge bg-success">Favorable</span>
                                {% else %}
                                <span class="badge bg-warning">Unfavorable</span>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                    <tfoot class="table-light">
                        <tr>
                            <th colspan="3" class="text-end">Totals:</th>
                            <th class="text-end">
                                Income: {{ total_budgeted_income|currency }}<br>
                                Expense: {{ total_budgeted_expense|currency }}
                            </th>
                            <th class="text-end">
                                Income: {{ total_actual_income|currency }}<br>
                                Expense: {{ total_actual_expense|currency }}
                            </th>
                            <th class="text-end">
                                Income: {% if variance_summary.income_variance >= 0 %}+{% endif %}{{ variance_summary.income_variance|currency }}<br>
                                Expense: {% if variance_summary.expense_variance >= 0 %}+{% endif %}{{ variance_summary.expense_variance|currency }}
                            </th>
                            <th class="text-end">
                                Net: {% if variance_summary.net_variance >= 0 %}+{% endif %}{{ variance_summary.net_variance|currency }}
                            </th>
                            <th></th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- No Data Message -->
    {% if not budget_analysis %}
    <div class="card">
        <div class="card-body text-center py-5">
            <i class="bi bi-graph-up-arrow display-1 text-muted mb-3"></i>
            <h4 class="text-muted">No Budget Data Found</h4>
            <p class="text-muted">No budget items match your current filter criteria or no budget has been set up for the selected period.</p>
            <div class="mt-3">
                <a href="{% url 'reporting:budget_vs_actual_report' %}" class="btn btn-outline-primary me-2">
                    <i class="bi bi-arrow-clockwise me-2"></i>Reset Filters
                </a>
                <a href="#" class="btn btn-outline-success">
                    <i class="bi bi-plus-circle me-2"></i>Set Up Budget
                </a>
            </div>
        </div>
    </div>
    {% endif %}

</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Highlight favorable/unfavorable variances
    $('.table tbody tr').each(function() {
        var varianceCell = $(this).find('td:nth-child(7)');
        var statusBadge = $(this).find('.badge');
        
        if (statusBadge.hasClass('bg-success')) {
            $(this).addClass('table-success');
        } else if (statusBadge.hasClass('bg-warning')) {
            $(this).addClass('table-warning');
        }
    });
    
    // Add hover effects for progress bars
    $('.progress-bar').hover(
        function() {
            $(this).addClass('opacity-75');
        },
        function() {
            $(this).removeClass('opacity-75');
        }
    );
});
</script>
{% endblock %}
