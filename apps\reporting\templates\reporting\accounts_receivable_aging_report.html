{% extends "tenant_base.html" %}
{% load humanize static core_tags reporting_filters %}

{% block title %}{{ view_title|default:"Accounts Receivable Aging Report" }}{% endblock %}

{% block content %}
<div class="container-fluid mt-3">
    {% with report_icon_class="bi bi-clock-history" %}
        {% include "partials/_report_header.html" %}
    {% endwith %}

    {% include "partials/_messages.html" %}
    {% include "partials/_report_filter_form.html" with filter_form=filter_form report_code=report_code specific_filter_title="Aging Report Filters" %}

    <!-- Report Summary Card -->
    <div class="card mb-4">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="bi bi-bar-chart me-2"></i>Aging Summary</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-2">
                    <div class="text-center">
                        <h6 class="text-muted">As of Date</h6>
                        <p class="mb-0">{{ as_of_date|date:"M d, Y" }}</p>
                    </div>
                </div>
                {% for period in aging_periods %}
                <div class="col-md-2">
                    <div class="text-center">
                        <h6 class="text-muted">{{ period.name }}</h6>
                        <p class="mb-0 fw-bold {% if period.name == 'Current' %}text-success{% elif 'Over' in period.name %}text-danger{% else %}text-warning{% endif %}">
                            {{ summary_totals|get_item:period.name|currency }}
                        </p>
                    </div>
                </div>
                {% endfor %}
                <div class="col-md-2">
                    <div class="text-center">
                        <h6 class="text-muted">Total Outstanding</h6>
                        <p class="mb-0 fw-bold text-primary">{{ total_outstanding|currency }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Aging Chart (Visual Summary) -->
    <div class="card mb-4">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="bi bi-pie-chart me-2"></i>Aging Distribution</h5>
        </div>
        <div class="card-body">
            <div class="row">
                {% for period in aging_periods %}
                {% with period_amount=summary_totals|get_item:period.name %}
                {% if total_outstanding > 0 %}
                {% with percentage=period_amount|percentage:total_outstanding %}
                <div class="col-md-3 mb-3">
                    <div class="card border-0 bg-light">
                        <div class="card-body text-center">
                            <h6 class="card-title">{{ period.name }}</h6>
                            <div class="progress mb-2" style="height: 20px;">
                                <div class="progress-bar {% if period.name == 'Current' %}bg-success{% elif 'Over' in period.name %}bg-danger{% else %}bg-warning{% endif %}" 
                                     role="progressbar" style="width: {{ percentage }}%">
                                    {{ percentage|floatformat:1 }}%
                                </div>
                            </div>
                            <p class="card-text">{{ period_amount|currency }}</p>
                        </div>
                    </div>
                </div>
                {% endwith %}
                {% endif %}
                {% endwith %}
                {% endfor %}
            </div>
        </div>
    </div>

    <!-- Student Aging Details -->
    {% if aging_data %}
    <div class="card">
        <div class="card-header bg-light d-flex justify-content-between align-items-center">
            <h5 class="mb-0"><i class="bi bi-people me-2"></i>Student Aging Details</h5>
            <span class="badge bg-primary">{{ aging_data|length }} students</span>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-striped table-hover mb-0">
                    <thead class="table-dark">
                        <tr>
                            <th>Student</th>
                            <th>Admission No</th>
                            <th>Class</th>
                            <th class="text-end">Total Outstanding</th>
                            {% for period in aging_periods %}
                            <th class="text-end">{{ period.name }}</th>
                            {% endfor %}
                        </tr>
                    </thead>
                    <tbody>
                        {% for student_data in aging_data %}
                        <tr>
                            <td>
                                <strong>{{ student_data.student.get_full_name }}</strong>
                                {% if student_data.student.parent %}
                                <br><small class="text-muted">Parent: {{ student_data.student.parent.get_full_name }}</small>
                                {% endif %}
                            </td>
                            <td>
                                <span class="badge bg-secondary">{{ student_data.student.admission_number }}</span>
                            </td>
                            <td>
                                {% if student_data.student.current_class %}
                                <span class="badge bg-info">{{ student_data.student.current_class.name }}</span>
                                {% else %}
                                <span class="text-muted">N/A</span>
                                {% endif %}
                            </td>
                            <td class="text-end">
                                <strong class="{% if student_data.total_outstanding > 0 %}text-danger{% else %}text-success{% endif %}">
                                    {{ student_data.total_outstanding|currency }}
                                </strong>
                            </td>
                            {% for period in aging_periods %}
                            <td class="text-end">
                                {% with period_amount=student_data.periods|get_item:period.name %}
                                {% if period_amount > 0 %}
                                <span class="{% if period.name == 'Current' %}text-success{% elif 'Over' in period.name %}text-danger{% else %}text-warning{% endif %}">
                                    {{ period_amount|currency }}
                                </span>
                                {% else %}
                                <span class="text-muted">-</span>
                                {% endif %}
                                {% endwith %}
                            </td>
                            {% endfor %}
                        </tr>
                        {% endfor %}
                    </tbody>
                    <tfoot class="table-light">
                        <tr>
                            <th colspan="3" class="text-end">Totals:</th>
                            <th class="text-end text-primary">{{ total_outstanding|currency }}</th>
                            {% for period in aging_periods %}
                            <th class="text-end {% if period.name == 'Current' %}text-success{% elif 'Over' in period.name %}text-danger{% else %}text-warning{% endif %}">
                                {{ summary_totals|get_item:period.name|currency }}
                            </th>
                            {% endfor %}
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- No Data Message -->
    {% if not aging_data %}
    <div class="card">
        <div class="card-body text-center py-5">
            <i class="bi bi-clock-history display-1 text-muted mb-3"></i>
            <h4 class="text-muted">No Outstanding Receivables Found</h4>
            <p class="text-muted">No students have outstanding fees matching your current filter criteria.</p>
            <a href="{% url 'reporting:accounts_receivable_aging_report' %}" class="btn btn-outline-primary">
                <i class="bi bi-arrow-clockwise me-2"></i>Reset Filters
            </a>
        </div>
    </div>
    {% endif %}

</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Add hover effects for aging periods
    $('.progress-bar').hover(
        function() {
            $(this).addClass('opacity-75');
        },
        function() {
            $(this).removeClass('opacity-75');
        }
    );
    
    // Add click handlers for student rows
    $('tbody tr').on('click', function() {
        $(this).toggleClass('table-active');
    });
});
</script>
{% endblock %}
