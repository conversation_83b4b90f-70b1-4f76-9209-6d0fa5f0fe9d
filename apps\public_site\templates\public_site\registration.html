{% extends "public_base.html" %}

{% load static i18n widget_tweaks %}

{% block public_page_title %}Register for {{ plan.name }}{% endblock %}

{% block extra_public_css %}
    {{ block.super }}
    <style>
        .StripeElement {
            background-color: white;
            padding: 10px 12px;
            border-radius: .25rem;
            border: 1px solid #ced4da;
            height: calc(1.5em + .75rem + 2px);
        }
        .StripeElement--focus {
            box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
            border-color: #86b7fe;
        }
        .StripeElement--invalid { border-color: #dc3545; }
        #card-errors { color: #dc3545; font-size: 0.875em; margin-top: 4px; }
        #submit-button .spinner-border { display: none; }
        #submit-button.processing .spinner-border { display: inline-block; }
    </style>
{% endblock %}

{% block content %}
<div class="container my-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow-sm">
                <div class="card-body p-4 p-md-5">
                    <div class="text-center mb-4">
                        <h2 class="card-title">{% trans "Create Your Account" %}</h2>
                        <p class="h5 text-muted">You've selected the <strong class="text-primary">{{ plan.name }}</strong> plan.</p>
                    </div>

                    <form id="payment-form" method="post" novalidate>
                        {% csrf_token %}
                        
                        {# Hidden fields for plan and payment method ID #}
                        {{ form.plan_id }}
                        {{ form.payment_method_id }}

                        {# Display non-field errors from Django form validation #}
                        {% for error in form.non_field_errors %}
                            <div class="alert alert-danger">{{ error }}</div>
                        {% endfor %}
                        {% include "partials/_messages.html" %}

                        <h5 class="mb-3">{% trans "Administrator Account" %}</h5>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.full_name.id_for_label }}" class="form-label">{{ form.full_name.label }}</label>
                                {% render_field form.full_name class="form-control" placeholder="Your Full Name" %}
                                {% for error in form.full_name.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.email.id_for_label }}" class="form-label">{{ form.email.label }}</label>
                                {% render_field form.email class="form-control" placeholder="<EMAIL>" %}
                                {% for error in form.email.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.password.id_for_label }}" class="form-label">{{ form.password.label }}</label>
                                {% render_field form.password class="form-control" %}
                                {% for error in form.password.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.password_confirm.id_for_label }}" class="form-label">{{ form.password_confirm.label }}</label>
                                {% render_field form.password_confirm class="form-control" %}
                                {% for error in form.password_confirm.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                            </div>
                        </div>

                        <hr class="my-4">

                        <h5 class="mb-3">{% trans "School Details" %}</h5>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.school_name.id_for_label }}" class="form-label">{{ form.school_name.label }}</label>
                                {% render_field form.school_name class="form-control" placeholder="e.g., Greenwood Academy" %}
                                {% for error in form.school_name.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.schema_name.id_for_label }}" class="form-label">{{ form.schema_name.label }}</label>
                                <div class="input-group">
                                    {% render_field form.schema_name class="form-control" placeholder="e.g., greenwood" %}
                                    <span class="input-group-text">.{{ request.get_host|cut:":8000" }}</span>
                                </div>
                                <div class="form-text">{{ form.schema_name.help_text }}</div>
                                {% for error in form.schema_name.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                            </div>
                        </div>

                        <hr class="my-4">

                        <h5 class="mb-3">{% trans "Payment Information" %}</h5>
                        <div class="mb-3">
                            <label for="card-element" class="form-label">{% trans "Credit or debit card" %}</label>
                            <div id="card-element">
                            <!-- A Stripe Element will be inserted here. -->
                            </div>
                            <div id="card-errors" role="alert" class="text-danger mt-2"></div>
                        </div>

                        <div class="d-grid mt-4">
                            <button id="submit-button" class="btn btn-primary btn-lg">
                                <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                {% blocktrans with price=plan.price_monthly|floatformat:0 %}Complete Sign Up & Pay ${{ price }}{% endblocktrans %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock content %}

{% block page_specific_js %}
{{ block.super }}
<script src="https://js.stripe.com/v3/"></script>
<script>
document.addEventListener("DOMContentLoaded", function() {
    const stripe = Stripe('{{ stripe_publishable_key }}');
    const elements = stripe.elements();
    
    const cardElement = elements.create('card', {
        style: {
            base: {
                fontSize: '16px',
                color: '#495057',
                '::placeholder': { color: '#aab7c4' },
            },
            invalid: { color: '#dc3545' },
        }
    });
    cardElement.mount('#card-element');

    cardElement.on('change', function(event) {
        const displayError = document.getElementById('card-errors');
        if (event.error) {
            displayError.textContent = event.error.message;
        } else {
            displayError.textContent = '';
        }
    });

    const form = document.getElementById('payment-form');
    const submitButton = document.getElementById('submit-button');
    const paymentMethodIdInput = document.getElementById('id_payment_method_id'); // Use Django's default ID

    form.addEventListener('submit', async function(event) {
        event.preventDefault();
        submitButton.disabled = true;
        submitButton.classList.add('processing');

        const billingDetails = {
            name: form.querySelector('input[name="full_name"]').value,
            email: form.querySelector('input[name="email"]').value,
        };

        const { paymentMethod, error } = await stripe.createPaymentMethod({
            type: 'card',
            card: cardElement,
            billing_details: billingDetails,
        });

        if (error) {
            document.getElementById('card-errors').textContent = error.message;
            submitButton.disabled = false;
            submitButton.classList.remove('processing');
        } else {
            paymentMethodIdInput.value = paymentMethod.id;
            form.submit();
        }
    });
});
</script>
{% endblock page_specific_js %}






{% comment %} {% extends "public_base.html" %}

{% load static i18n widget_tweaks %}

{% block public_page_title %}Register for {{ plan.name }}{% endblock %}

{% block extra_public_css %}
    {{ block.super }}
    <style>
        .StripeElement {
            background-color: white;
            padding: 10px 12px;
            border-radius: 4px;
            border: 1px solid #ced4da;
            height: 44px;
        }
        .StripeElement--focus {
            box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
            border-color: #86b7fe;
        }
        .StripeElement--invalid {
            border-color: #dc3545;
        }
        #card-errors {
            color: #dc3545;
            font-size: 0.875em;
            margin-top: 4px;
        }
        #submit-button .spinner-border {
            display: none;
        }
        #submit-button.processing .spinner-border {
            display: inline-block;
        }
    </style>
{% endblock %}

{% block content %}
<div class="container my-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="text-center mb-4">
                <h1 class="h2">{% trans "Create Your Account" %}</h1>
                <p class="lead">You are signing up for the <strong class="text-primary">{{ plan.name }}</strong> plan.</p>
            </div>

            {% include "partials/_messages.html" %}

            <div class="card shadow-sm">
                <div class="card-body p-4 p-md-5">
                    <form id="payment-form" method="post">
                        {% csrf_token %}
                        
                        {# Hidden fields for plan and payment method ID #}
                        <input type="hidden" name="plan_id" value="{{ plan.pk }}">
                        <input type="hidden" name="payment_method_id" id="payment-method-id">

                        <h5 class="mb-3">{% trans "Administrator Account" %}</h5>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.full_name.id_for_label }}" class="form-label">{{ form.full_name.label }}</label>
                                {% render_field form.full_name class="form-control" %}
                                {% for error in form.full_name.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.email.id_for_label }}" class="form-label">{{ form.email.label }}</label>
                                {% render_field form.email class="form-control" %}
                                {% for error in form.email.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.password.id_for_label }}" class="form-label">{{ form.password.label }}</label>
                                {% render_field form.password class="form-control" %}
                                {% for error in form.password.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.password_confirm.id_for_label }}" class="form-label">{{ form.password_confirm.label }}</label>
                                {% render_field form.password_confirm class="form-control" %}
                                {% for error in form.password_confirm.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                            </div>
                        </div>
                        {% for error in form.non_field_errors %}<div class="alert alert-danger">{{ error }}</div>{% endfor %}

                        <hr class="my-4">

                        <h5 class="mb-3">{% trans "School Details" %}</h5>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.school_name.id_for_label }}" class="form-label">{{ form.school_name.label }}</label>
                                {% render_field form.school_name class="form-control" %}
                                {% for error in form.school_name.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.schema_name.id_for_label }}" class="form-label">{{ form.schema_name.label }}</label>
                                <div class="input-group">
                                    {% render_field form.schema_name class="form-control" %}
                                    <span class="input-group-text">.{{ request.get_host|cut:":8000" }}</span>
                                </div>
                                <div class="form-text">{{ form.schema_name.help_text }}</div>
                                {% for error in form.schema_name.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                            </div>
                        </div>

                        <hr class="my-4">

                        <h5 class="mb-3">{% trans "Payment Information" %}</h5>
                        <div class="mb-3">
                            <label for="card-element" class="form-label">{% trans "Credit or debit card" %}</label>
                            <div id="card-element" class="form-control">
                              <!-- A Stripe Element will be inserted here. -->
                            </div>
                            <!-- Used to display form errors. -->
                            <div id="card-errors" role="alert" class="mt-1"></div>
                        </div>

                        <div class="d-grid mt-4">
                            <button id="submit-button" class="btn btn-primary btn-lg">
                                <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                {% blocktrans with price=plan.price_monthly|floatformat:0 %}Complete Sign Up & Pay ${{ price }}{% endblocktrans %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock content %}

{% block page_specific_js %}
{{ block.super }}
<script src="https://js.stripe.com/v3/"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const stripe = Stripe('{{ stripe_publishable_key }}');
    const elements = stripe.elements();
    const cardElement = elements.create('card');
    cardElement.mount('#card-element');

    const form = document.getElementById('payment-form');
    const submitButton = document.getElementById('submit-button');
    const cardErrors = document.getElementById('card-errors');
    const paymentMethodIdInput = document.getElementById('payment-method-id');

    form.addEventListener('submit', async function(event) {
        event.preventDefault();
        
        submitButton.disabled = true;
        submitButton.classList.add('processing');
        cardErrors.textContent = '';

        const { paymentMethod, error } = await stripe.createPaymentMethod({
            type: 'card',
            card: cardElement,
            billing_details: {
                name: form.querySelector('input[name="full_name"]').value,
                email: form.querySelector('input[name="email"]').value,
            },
        });

        if (error) {
            cardErrors.textContent = error.message;
            submitButton.disabled = false;
            submitButton.classList.remove('processing');
        } else {
            // The card has been successfully tokenized.
            // Set the hidden input's value to the paymentMethod.id
            paymentMethodIdInput.value = paymentMethod.id;
            
            // Now, submit the form to your Django backend.
            form.submit();
        }
    });
});
</script>
{% endblock page_specific_js %}


 {% endcomment %}


{% comment %} {% extends "public_base.html" %}
{% load static %}
{% block title %}Register for {{ plan.name }} Plan{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-7">
            <div class="card shadow">
                <div class="card-body p-5">
                    <h2 class="card-title text-center mb-4">Create Your Account</h2>
                    <h5 class="text-center text-muted mb-4">You've selected the <strong>{{ plan.name }}</strong> plan.</h5>

                    <form id="registration-form" method="post">
                        {% csrf_token %}
                        <!-- Hidden inputs to pass data to the backend -->
                        <input type="hidden" name="plan_id" value="{{ plan.pk }}">
                        <input type="hidden" id="payment-method-id" name="payment_method_id">

                        <!-- Display form validation errors -->
                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">{{ form.non_field_errors|first }}</div>
                        {% endif %}
                        {% if messages %}
                            {% for message in messages %}
                                <div class="alert alert-{% if message.tags %}{{ message.tags }}{% else %}info{% endif %}">{{ message }}</div>
                            {% endfor %}
                        {% endif %}

                        <!-- School/Tenant Info -->
                        <div class="mb-3">
                            <label for="{{ form.school_name.id_for_label }}" class="form-label">{{ form.school_name.label }}</label>
                            {{ form.school_name }}
                        </div>
                        <hr>
                        <!-- User Info -->
                        <div class="mb-3">
                            <label for="{{ form.username.id_for_label }}" class="form-label">{{ form.username.label }}</label>
                            {{ form.username }}
                        </div>
                        <div class="mb-3">
                            <label for="{{ form.email.id_for_label }}" class="form-label">{{ form.email.label }}</label>
                            {{ form.email }}
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.password.id_for_label }}" class="form-label">{{ form.password.label }}</label>
                                {{ form.password }}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.password2.id_for_label }}" class="form-label">{{ form.password2.label }}</label>
                                {{ form.password2 }}
                            </div>
                        </div>
                        <hr>
                        <!-- Stripe Card Element -->
                        <h5 class="mb-3">Payment Details</h5>
                        <div id="card-element" class="form-control">
                          <!-- A Stripe Element will be inserted here. -->
                        </div>
                        <!-- Used to display form errors from Stripe.js -->
                        <div id="card-errors" role="alert" class="text-danger mt-2"></div>

                        <div class="d-grid mt-4">
                            <button id="submit-button" class="btn btn-primary btn-lg">
                                Complete Registration & Pay ${{ plan.price|floatformat:0 }}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Include Stripe.js -->
<script src="https://js.stripe.com/v3/"></script>

<script>
document.addEventListener("DOMContentLoaded", function() {
    const stripe = Stripe('{{ stripe_publishable_key }}');
    const elements = stripe.elements();
    
    // Custom styling can be passed to options when creating an Element.
    const style = {
        base: {
            fontSize: '16px',
            color: '#32325d',
        },
    };

    // Create an instance of the card Element.
    const card = elements.create('card', {style: style});
    card.mount('#card-element');

    // Handle real-time validation errors from the card Element.
    card.on('change', function(event) {
        const displayError = document.getElementById('card-errors');
        if (event.error) {
            displayError.textContent = event.error.message;
        } else {
            displayError.textContent = '';
        }
    });

    // Handle form submission.
    const form = document.getElementById('registration-form');
    const submitButton = document.getElementById('submit-button');
    const paymentMethodIdInput = document.getElementById('payment-method-id');

    form.addEventListener('submit', function(event) {
        event.preventDefault(); // Prevent the form from submitting immediately
        submitButton.disabled = true; // Disable button to prevent multiple clicks
        submitButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Processing...';

        stripe.createPaymentMethod({
            type: 'card',
            card: card,
            billing_details: {
                // You can pre-fill this if you have the user's name
            },
        }).then(function(result) {
            if (result.error) {
                // Show error to your customer (e.g., incomplete card details)
                document.getElementById('card-errors').textContent = result.error.message;
                submitButton.disabled = false;
                submitButton.textContent = "Complete Registration & Pay ${{ plan.price|floatformat:0 }}";
            } else {
                // The payment method was created successfully!
                // Set the hidden input's value to the payment method ID
                paymentMethodIdInput.value = result.paymentMethod.id;
                // Now, submit the form to your backend.
                form.submit();
            }
        });
    });
});
</script>
{% endblock %} {% endcomment %}


