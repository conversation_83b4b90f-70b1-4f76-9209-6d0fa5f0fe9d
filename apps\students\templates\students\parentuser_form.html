{# D:\school_fees_saas_v2\apps\students\templates\students\parentuser_form.html #}
{% extends "tenant_base.html" %} {# Assuming this is the correct base for tenant staff facing forms #}
{% load static i18n widget_tweaks %} {# Ensure widget_tweaks is in INSTALLED_APPS if used #}

{% block tenant_page_title %}
    {# Using view_title passed from context; the trans block is a fallback if view_title is missing #}
    {% trans "Parent Account Management" as default_title_string %} 
    {{ view_title|default:default_title_string }}
{% endblock tenant_page_title %}

{% block extra_tenant_css %}
    {{ block.super }}
    <style>
        /* Premium Parent Form Styling */
        .premium-parent-form-card {
            border: none;
            border-radius: 1.5rem;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .premium-parent-form-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
            position: relative;
        }

        .premium-parent-form-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 100%);
            pointer-events: none;
        }

        .premium-parent-form-header h2 {
            margin: 0;
            font-weight: 600;
            font-size: 1.75rem;
        }

        .premium-parent-form-header p {
            margin: 0.5rem 0 0 0;
            opacity: 0.9;
        }

        .premium-parent-form-body {
            padding: 2.5rem;
        }

        .premium-parent-form-group {
            position: relative;
            margin-bottom: 2rem;
        }

        .premium-parent-form-control {
            width: 100%;
            padding: 1rem 1rem 1rem 3rem;
            border: 2px solid #e9ecef;
            border-radius: 1rem;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(5px);
        }

        .premium-parent-form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
            outline: none;
            background: white;
        }

        .premium-parent-form-control:not(:placeholder-shown) {
            background: white;
        }

        .premium-parent-form-label {
            position: absolute;
            top: 1rem;
            left: 3rem;
            background: transparent;
            padding: 0 0.5rem;
            font-size: 1rem;
            color: #6c757d;
            transition: all 0.3s ease;
            pointer-events: none;
            z-index: 2;
        }

        .premium-parent-form-control:focus + .premium-parent-form-label,
        .premium-parent-form-control:not(:placeholder-shown) + .premium-parent-form-label {
            top: -0.5rem;
            left: 2.5rem;
            font-size: 0.875rem;
            color: #667eea;
            font-weight: 500;
            background: white;
        }

        .premium-parent-form-icon {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: #6c757d;
            font-size: 1.2rem;
            z-index: 3;
            transition: color 0.3s ease;
        }

        .premium-parent-form-group:focus-within .premium-parent-form-icon {
            color: #667eea;
        }

        .premium-parent-help-text {
            font-size: 0.875rem;
            color: #6c757d;
            margin-top: 0.5rem;
            margin-left: 3rem;
        }

        .premium-parent-error {
            color: #dc3545;
            font-size: 0.875rem;
            margin-top: 0.5rem;
            margin-left: 3rem;
        }

        .premium-parent-btn {
            padding: 1rem 2rem;
            border-radius: 1rem;
            font-weight: 600;
            font-size: 1rem;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
            min-width: 150px;
            justify-content: center;
        }

        .premium-parent-btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .premium-parent-btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
            color: white;
        }

        .premium-parent-btn-secondary {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            color: #495057;
            border: 1px solid #dee2e6;
        }

        .premium-parent-btn-secondary:hover {
            background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
            transform: translateY(-1px);
            color: #495057;
        }

        .premium-parent-form-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid #e9ecef;
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .premium-parent-form-header,
            .premium-parent-form-body {
                padding: 1.5rem;
            }

            .premium-parent-form-actions {
                flex-direction: column;
                align-items: stretch;
            }

            .premium-parent-btn {
                justify-content: center;
            }
        }
    </style>
{% endblock %}

{% block tenant_specific_content %}
<div class="container mt-4">
    <div class="pagetitle mb-4"> {# Added mb-4 for spacing #}
        <h1>
            {% trans "Manage Parent Account" as default_h1_title %}
            {{ view_title|default:default_h1_title }}
        </h1>

        <nav>
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'schools:dashboard' %}">{% trans "Dashboard" %}</a></li>
                <li class="breadcrumb-item"><a href="{% url 'students:parentuser_list' %}">{% trans "Parent Accounts" %}</a></li> 
                <li class="breadcrumb-item active">
                    {% if object %}{% trans "Edit" %}{% else %}{% trans "Create" %}{% endif %}
                    {# Using form_mode is also good: {{ form_mode|capfirst|default:default_breadcrumb_text }} #}
                </li>
            </ol>
        </nav>
    </div><!-- End Page Title -->

    <div class="row justify-content-center">
        <div class="col-lg-8 col-md-10">
            <div class="premium-parent-form-card">
                <div class="premium-parent-form-header">
                    <h2>
                        <i class="bi bi-person-plus-fill me-2"></i>
                        {% if object %}
                            {% trans "Edit Parent Account" %}
                        {% else %}
                            {% trans "Create New Parent Account" %}
                        {% endif %}
                    </h2>
                    <p>
                        {% if object %}
                            {% trans "Update parent information and account details" %}
                        {% else %}
                            {% trans "Add a new parent account to the system" %}
                        {% endif %}
                    </p>
                </div>

                <div class="premium-parent-form-body">
                    {% include "partials/_messages.html" %}

                    <form method="post" novalidate>
                        {% csrf_token %}

                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {% for error in form.non_field_errors %}
                                    <p class="mb-0">{{ error }}</p>
                                {% endfor %}
                            </div>
                        {% endif %}

                        <!-- Email Address -->
                        <div class="premium-parent-form-group">
                            <i class="premium-parent-form-icon bi bi-envelope"></i>
                            {% render_field form.email class+="premium-parent-form-control" %}
                            <label for="{{ form.email.id_for_label }}" class="premium-parent-form-label">{{ form.email.label }}</label>
                            {% if form.email.help_text %}
                                <div class="premium-parent-help-text">{{ form.email.help_text }}</div>
                            {% endif %}
                            {% for error in form.email.errors %}
                                <div class="premium-parent-error">{{ error }}</div>
                            {% endfor %}
                        </div>

                        <!-- First Name -->
                        <div class="premium-parent-form-group">
                            <i class="premium-parent-form-icon bi bi-person"></i>
                            {% render_field form.first_name class+="premium-parent-form-control" %}
                            <label for="{{ form.first_name.id_for_label }}" class="premium-parent-form-label">{{ form.first_name.label }}</label>
                            {% for error in form.first_name.errors %}
                                <div class="premium-parent-error">{{ error }}</div>
                            {% endfor %}
                        </div>

                        <!-- Last Name -->
                        <div class="premium-parent-form-group">
                            <i class="premium-parent-form-icon bi bi-person-fill"></i>
                            {% render_field form.last_name class+="premium-parent-form-control" %}
                            <label for="{{ form.last_name.id_for_label }}" class="premium-parent-form-label">{{ form.last_name.label }}</label>
                            {% for error in form.last_name.errors %}
                                <div class="premium-parent-error">{{ error }}</div>
                            {% endfor %}
                        </div>

                        <!-- Phone Number -->
                        <div class="premium-parent-form-group">
                            <i class="premium-parent-form-icon bi bi-telephone"></i>
                            {% render_field form.phone_number class+="premium-parent-form-control" %}
                            <label for="{{ form.phone_number.id_for_label }}" class="premium-parent-form-label">{{ form.phone_number.label }}</label>
                            {% if form.phone_number.help_text %}
                                <div class="premium-parent-help-text">{{ form.phone_number.help_text }}</div>
                            {% endif %}
                            {% for error in form.phone_number.errors %}
                                <div class="premium-parent-error">{{ error }}</div>
                            {% endfor %}
                        </div>
                            
                        {# Password fields - only for create mode #}
                        {% if form_mode == "create" or not object %}
                            <!-- Password -->
                            <div class="premium-parent-form-group">
                                <i class="premium-parent-form-icon bi bi-lock"></i>
                                {% render_field form.password class+="premium-parent-form-control" %}
                                <label for="{{ form.password.id_for_label }}" class="premium-parent-form-label">{{ form.password.label|default:"Password" }}</label>
                                {% if form.password.help_text %}
                                    <div class="premium-parent-help-text">{{ form.password.help_text|safe }}</div>
                                {% endif %}
                                {% for error in form.password.errors %}
                                    <div class="premium-parent-error">{{ error }}</div>
                                {% endfor %}
                            </div>

                            <!-- Confirm Password -->
                            {% if form.password_confirm %}
                            <div class="premium-parent-form-group">
                                <i class="premium-parent-form-icon bi bi-lock-fill"></i>
                                {% render_field form.password_confirm class+="premium-parent-form-control" %}
                                <label for="{{ form.password_confirm.id_for_label }}" class="premium-parent-form-label">{{ form.password_confirm.label|default:"Confirm Password" }}</label>
                                {% for error in form.password_confirm.errors %}
                                    <div class="premium-parent-error">{{ error }}</div>
                                {% endfor %}
                            </div>
                            {% endif %}
                        {% endif %}

                        <!-- Active Status (if available) -->
                        {% if form.is_active %}
                        <div class="premium-parent-form-group">
                            <div class="form-check d-flex align-items-center">
                                {% render_field form.is_active class+="form-check-input me-3" %}
                                <label for="{{ form.is_active.id_for_label }}" class="form-check-label">{{ form.is_active.label }}</label>
                            </div>
                            {% if form.is_active.help_text %}
                                <div class="premium-parent-help-text">{{ form.is_active.help_text }}</div>
                            {% endif %}
                            {% for error in form.is_active.errors %}
                                <div class="premium-parent-error">{{ error }}</div>
                            {% endfor %}
                        </div>
                        {% endif %}

                        {# Hidden fields are not typically needed if form action and GET params are handled by the view #}
                        {# The view's get_success_url can access request.GET directly #}
                        {# However, if you need to pass them through specifically: #}
                        {% if request.GET.student_pk_to_link %}
                            {# <input type="hidden" name="student_pk_to_link_passthrough" value="{{ request.GET.student_pk_to_link }}"> #}
                        {% endif %}
                        {% if request.GET.next %}
                            {# <input type="hidden" name="next_passthrough" value="{{ request.GET.next }}"> #}
                        {% endif %}

                        <!-- Form Actions -->
                        <div class="premium-parent-form-actions">
                            <a href="{% url 'students:parentuser_list' %}" class="premium-parent-btn premium-parent-btn-secondary">
                                <i class="bi bi-arrow-left"></i>
                                {% trans "Back to List" %}
                            </a>
                            <button type="submit" class="premium-parent-btn premium-parent-btn-primary">
                                <i class="bi bi-check-circle"></i>
                                {% if object %}{% trans "Update Parent" %}{% else %}{% trans "Create Parent" %}{% endif %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock tenant_specific_content %}

{% block extra_tenant_js %}
{{ block.super }}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Enhanced form interactions for floating labels
    const formControls = document.querySelectorAll('.premium-parent-form-control');

    formControls.forEach(control => {
        // Set initial state for fields with values
        if (control.value && control.value.trim() !== '') {
            control.classList.add('has-value');
        }

        // Handle input events
        control.addEventListener('input', function() {
            if (this.value && this.value.trim() !== '') {
                this.classList.add('has-value');
            } else {
                this.classList.remove('has-value');
            }
        });

        // Handle focus events
        control.addEventListener('focus', function() {
            this.classList.add('focused');
        });

        // Handle blur events
        control.addEventListener('blur', function() {
            this.classList.remove('focused');
        });
    });

    // Form submission enhancement
    const form = document.querySelector('form');
    const submitBtn = document.querySelector('.premium-parent-btn-primary[type="submit"]');

    if (form && submitBtn) {
        form.addEventListener('submit', function(e) {
            // Add loading state
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>{% trans "Processing..." %}';

            // Re-enable after 10 seconds in case of issues
            setTimeout(function() {
                submitBtn.disabled = false;
                submitBtn.innerHTML = '<i class="bi bi-check-circle"></i> {% if object %}{% trans "Update Parent" %}{% else %}{% trans "Create Parent" %}{% endif %}';
            }, 10000);
        });
    }
});
</script>
{% endblock %}


