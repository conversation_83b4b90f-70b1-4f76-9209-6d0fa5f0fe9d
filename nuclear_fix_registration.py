#!/usr/bin/env python
"""
Nuclear Fix for Registration Issues
This is the most aggressive fix that bypasses all problematic components

Usage: python nuclear_fix_registration.py
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from django.db import connection, transaction
from django_tenants.utils import schema_context
from django.core.management import call_command
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def clean_failed_tenants():
    """Clean up any failed tenant creation attempts"""
    logger.info("=== CLEANING FAILED TENANTS ===")
    
    try:
        # Get list of schemas
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT schema_name FROM information_schema.schemata 
                WHERE schema_name NOT IN ('public', 'information_schema') 
                AND schema_name NOT LIKE 'pg_%'
            """)
            schemas = [row[0] for row in cursor.fetchall()]
        
        # Check each schema for completeness
        for schema in schemas:
            try:
                with connection.cursor() as cursor:
                    cursor.execute(f'SET search_path TO "{schema}"')
                    
                    # Check if django_migrations table exists
                    cursor.execute("""
                        SELECT EXISTS (
                            SELECT FROM information_schema.tables 
                            WHERE table_name = 'django_migrations'
                        );
                    """)
                    
                    if not cursor.fetchone()[0]:
                        logger.warning(f"Schema {schema} is incomplete, dropping...")
                        cursor.execute(f'DROP SCHEMA IF EXISTS "{schema}" CASCADE')
                        logger.info(f"✅ Dropped incomplete schema: {schema}")
                        
            except Exception as e:
                logger.error(f"Error checking schema {schema}: {e}")
        
        # Clean up tenant records for dropped schemas
        from apps.tenants.models import School
        for school in School.objects.all():
            try:
                with connection.cursor() as cursor:
                    cursor.execute("""
                        SELECT EXISTS (
                            SELECT FROM information_schema.schemata 
                            WHERE schema_name = %s
                        );
                    """, [school.schema_name])
                    
                    if not cursor.fetchone()[0]:
                        logger.warning(f"Tenant {school.name} has no schema, deleting record...")
                        school.delete()
                        logger.info(f"✅ Deleted orphaned tenant: {school.name}")
                        
            except Exception as e:
                logger.error(f"Error checking tenant {school.name}: {e}")
                
    except Exception as e:
        logger.error(f"Failed to clean failed tenants: {e}")

def fix_announcements_everywhere():
    """Create announcements tables in all schemas"""
    logger.info("=== FIXING ANNOUNCEMENTS EVERYWHERE ===")
    
    announcements_sql = """
    CREATE TABLE IF NOT EXISTS announcements_announcement (
        id BIGSERIAL PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        content TEXT NOT NULL,
        target_all_tenant_staff BOOLEAN NOT NULL DEFAULT FALSE,
        target_all_tenant_parents BOOLEAN NOT NULL DEFAULT FALSE,
        is_global BOOLEAN NOT NULL DEFAULT FALSE,
        target_global_audience_type VARCHAR(50),
        publish_date TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
        expiry_date TIMESTAMP WITH TIME ZONE,
        is_published BOOLEAN NOT NULL DEFAULT TRUE,
        is_sticky BOOLEAN NOT NULL DEFAULT FALSE,
        created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
        author_id BIGINT,
        tenant_id BIGINT
    );
    
    CREATE INDEX IF NOT EXISTS announcements_announcement_publish_date_idx 
        ON announcements_announcement (publish_date);
    CREATE INDEX IF NOT EXISTS announcements_announcement_is_published_idx 
        ON announcements_announcement (is_published);
    
    INSERT INTO django_migrations (app, name, applied) 
    VALUES ('announcements', '0001_initial', NOW())
    ON CONFLICT (app, name) DO NOTHING;
    
    INSERT INTO django_migrations (app, name, applied) 
    VALUES ('announcements', '0002_initial', NOW())
    ON CONFLICT (app, name) DO NOTHING;
    """
    
    try:
        # Fix public schema
        with connection.cursor() as cursor:
            cursor.execute('SET search_path TO "public"')
            cursor.execute(announcements_sql)
            logger.info("✅ Fixed announcements in public schema")
        
        # Fix all tenant schemas
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT schema_name FROM information_schema.schemata 
                WHERE schema_name NOT IN ('public', 'information_schema') 
                AND schema_name NOT LIKE 'pg_%'
            """)
            schemas = [row[0] for row in cursor.fetchall()]
        
        for schema in schemas:
            try:
                with connection.cursor() as cursor:
                    cursor.execute(f'SET search_path TO "{schema}"')
                    cursor.execute(announcements_sql)
                    logger.info(f"✅ Fixed announcements in schema: {schema}")
            except Exception as e:
                logger.error(f"Failed to fix announcements in {schema}: {e}")
                
    except Exception as e:
        logger.error(f"Failed to fix announcements: {e}")

def test_tenant_creation_process():
    """Test the tenant creation process"""
    logger.info("=== TESTING TENANT CREATION PROCESS ===")
    
    test_schema = 'test_nuclear_fix'
    
    try:
        # Clean up any existing test schema
        with connection.cursor() as cursor:
            cursor.execute(f'DROP SCHEMA IF EXISTS "{test_schema}" CASCADE')
        
        # Test our safe migration command
        call_command('migrate_tenant_safe', test_schema, verbosity=1)
        
        # Verify the schema was created properly
        with connection.cursor() as cursor:
            cursor.execute(f'SET search_path TO "{test_schema}"')
            
            # Check announcements table exists
            cursor.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_name = 'announcements_announcement'
                );
            """)
            
            if cursor.fetchone()[0]:
                logger.info("✅ Test schema created successfully with announcements table")
                
                # Clean up test schema
                cursor.execute(f'DROP SCHEMA "{test_schema}" CASCADE')
                return True
            else:
                logger.error("❌ Test schema missing announcements table")
                return False
                
    except Exception as e:
        logger.error(f"Tenant creation test failed: {e}")
        return False

def verify_existing_tenants():
    """Verify existing tenants are working"""
    logger.info("=== VERIFYING EXISTING TENANTS ===")
    
    try:
        from apps.tenants.models import School
        tenants = School.objects.all()
        
        for tenant in tenants:
            try:
                with connection.cursor() as cursor:
                    cursor.execute(f'SET search_path TO "{tenant.schema_name}"')
                    
                    # Check announcements table
                    cursor.execute("""
                        SELECT EXISTS (
                            SELECT FROM information_schema.tables 
                            WHERE table_name = 'announcements_announcement'
                        );
                    """)
                    
                    if cursor.fetchone()[0]:
                        logger.info(f"✅ Tenant {tenant.name} is working")
                    else:
                        logger.warning(f"⚠️ Tenant {tenant.name} missing announcements table")
                        
            except Exception as e:
                logger.error(f"Error verifying tenant {tenant.name}: {e}")
                
    except Exception as e:
        logger.error(f"Failed to verify tenants: {e}")

def main():
    """Run the nuclear fix"""
    logger.info("=== NUCLEAR FIX FOR REGISTRATION ISSUES ===")
    logger.info("This will aggressively fix all tenant creation issues")
    
    steps = [
        ("Clean Failed Tenants", clean_failed_tenants),
        ("Fix Announcements Everywhere", fix_announcements_everywhere),
        ("Test Tenant Creation Process", test_tenant_creation_process),
        ("Verify Existing Tenants", verify_existing_tenants),
    ]
    
    for step_name, step_func in steps:
        logger.info(f"\n--- {step_name} ---")
        try:
            result = step_func()
            if result is False:
                logger.error(f"❌ {step_name} failed")
                return False
        except Exception as e:
            logger.error(f"❌ {step_name} error: {e}")
            return False
    
    logger.info("\n🎉 NUCLEAR FIX COMPLETED!")
    logger.info("Registration should now work. Try creating a new tenant.")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
