"""
Create Simple Tenant
Creates a new tenant with a simple, reliable approach

Usage: 
python manage.py create_simple_tenant --name="School Name" --schema="schema_name" --domain="domain.com"
"""

from django.core.management.base import BaseCommand
from django.db import connection
from django.core.management import call_command
from apps.tenants.models import School
from django_tenants.utils import schema_context
import logging

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Create a new tenant with a simple, reliable approach'

    def add_arguments(self, parser):
        parser.add_argument(
            '--name',
            type=str,
            required=True,
            help='School name',
        )
        parser.add_argument(
            '--schema',
            type=str,
            required=True,
            help='Schema name (must be unique)',
        )
        parser.add_argument(
            '--domain',
            type=str,
            required=True,
            help='Domain name',
        )

    def handle(self, *args, **options):
        school_name = options['name']
        schema_name = options['schema']
        domain_name = options['domain']
        
        self.stdout.write(f"=== CREATING SIMPLE TENANT: {school_name} ===")
        self.stdout.write(f"Schema: {schema_name}")
        self.stdout.write(f"Domain: {domain_name}")
        
        try:
            # 1. Check if tenant already exists
            if School.objects.filter(schema_name=schema_name).exists():
                self.stdout.write(
                    self.style.ERROR(f"Tenant with schema '{schema_name}' already exists")
                )
                return
            
            # 2. Get default owner (first superuser)
            from django.contrib.auth import get_user_model
            User = get_user_model()
            
            owner = User.objects.filter(is_superuser=True).first()
            if not owner:
                self.stdout.write(
                    self.style.ERROR("No superuser found to assign as owner")
                )
                return
            
            self.stdout.write(f"Using owner: {owner.email}")
            
            # 3. Create tenant record (this will trigger schema creation via signals)
            self.stdout.write("Creating tenant record...")
            
            tenant = School.objects.create(
                schema_name=schema_name,
                name=school_name,
                owner=owner,
                is_active=True
            )
            
            # 4. Create domain
            from apps.tenants.models import Domain
            Domain.objects.create(
                domain=domain_name,
                tenant=tenant,
                is_primary=True
            )
            
            self.stdout.write(
                self.style.SUCCESS(f"✅ Created tenant: {tenant.name}")
            )
            
            # 5. Wait a moment for signals to complete
            import time
            time.sleep(2)
            
            # 6. Apply essential data manually
            self.stdout.write("Applying essential data...")
            self.apply_essential_data_safely(schema_name)
            
            # 7. Verify tenant
            self.stdout.write("Verifying tenant...")
            if self.verify_tenant_basic(schema_name):
                self.stdout.write(
                    self.style.SUCCESS("✅ Tenant verification passed")
                )
            else:
                self.stdout.write(
                    self.style.WARNING("⚠️  Tenant verification had issues")
                )
            
            # Success message
            self.stdout.write(
                self.style.SUCCESS(f"\n🎉 SIMPLE TENANT CREATED SUCCESSFULLY!")
            )
            self.stdout.write(f"School: {school_name}")
            self.stdout.write(f"Schema: {schema_name}")
            self.stdout.write(f"Domain: {domain_name}")
            self.stdout.write(f"URL: http://{domain_name}:8000/")
            self.stdout.write(f"Owner: {owner.email}")
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"❌ Failed to create tenant: {e}")
            )

    def apply_essential_data_safely(self, schema_name):
        """Apply essential data safely to the tenant"""
        
        try:
            with connection.cursor() as cursor:
                cursor.execute(f'SET search_path TO "{schema_name}"')
                
                # 1. Create academic year if table exists
                try:
                    cursor.execute("""
                        SELECT EXISTS (
                            SELECT FROM information_schema.tables 
                            WHERE table_schema = %s 
                            AND table_name = 'schools_academicyear'
                        )
                    """, [schema_name])
                    
                    if cursor.fetchone()[0]:
                        cursor.execute("SELECT COUNT(*) FROM schools_academicyear")
                        if cursor.fetchone()[0] == 0:
                            cursor.execute("""
                                INSERT INTO schools_academicyear (name, start_date, end_date, is_active, is_current, created_at, updated_at) 
                                VALUES ('2024-2025', '2024-09-01', '2025-08-31', TRUE, TRUE, NOW(), NOW())
                            """)
                            self.stdout.write("    ✅ Created academic year")
                except Exception as e:
                    self.stdout.write(f"    ⚠️  Academic year: {e}")
                
                # 2. Create leave types if table exists
                try:
                    cursor.execute("""
                        SELECT EXISTS (
                            SELECT FROM information_schema.tables 
                            WHERE table_schema = %s 
                            AND table_name = 'hr_leavetype'
                        )
                    """, [schema_name])
                    
                    if cursor.fetchone()[0]:
                        cursor.execute("SELECT COUNT(*) FROM hr_leavetype")
                        if cursor.fetchone()[0] == 0:
                            cursor.execute("""
                                INSERT INTO hr_leavetype (name, description, max_annual_days, is_paid, requires_approval, is_active, accrual_frequency, accrual_rate, prorate_accrual, created_at, updated_at) VALUES
                                ('Annual Leave', 'Annual vacation leave', 21, TRUE, TRUE, TRUE, 'YEARLY', 1.75, FALSE, NOW(), NOW()),
                                ('Sick Leave', 'Medical leave', 10, TRUE, FALSE, TRUE, 'YEARLY', 0.83, FALSE, NOW(), NOW()),
                                ('Emergency Leave', 'Emergency leave', 3, FALSE, TRUE, TRUE, 'YEARLY', 0.25, FALSE, NOW(), NOW())
                            """)
                            self.stdout.write("    ✅ Created leave types")
                except Exception as e:
                    self.stdout.write(f"    ⚠️  Leave types: {e}")
                
                # 3. Create payment methods if table exists
                try:
                    cursor.execute("""
                        SELECT EXISTS (
                            SELECT FROM information_schema.tables 
                            WHERE table_schema = %s 
                            AND table_name = 'payments_paymentmethod'
                        )
                    """, [schema_name])
                    
                    if cursor.fetchone()[0]:
                        cursor.execute("SELECT COUNT(*) FROM payments_paymentmethod")
                        if cursor.fetchone()[0] == 0:
                            cursor.execute("""
                                INSERT INTO payments_paymentmethod (name, description, type, is_active, created_at, updated_at) VALUES
                                ('Cash', 'Cash payment', 'CASH', TRUE, NOW(), NOW()),
                                ('Bank Transfer', 'Bank transfer payment', 'BANK_TRANSFER', TRUE, NOW(), NOW()),
                                ('Mobile Money', 'Mobile money payment', 'MOBILE_MONEY', TRUE, NOW(), NOW())
                            """)
                            self.stdout.write("    ✅ Created payment methods")
                except Exception as e:
                    self.stdout.write(f"    ⚠️  Payment methods: {e}")
                
                # 4. Create concession types if table exists
                try:
                    cursor.execute("""
                        SELECT EXISTS (
                            SELECT FROM information_schema.tables 
                            WHERE table_schema = %s 
                            AND table_name = 'fees_concessiontype'
                        )
                    """, [schema_name])
                    
                    if cursor.fetchone()[0]:
                        cursor.execute("SELECT COUNT(*) FROM fees_concessiontype")
                        if cursor.fetchone()[0] == 0:
                            cursor.execute("""
                                INSERT INTO fees_concessiontype (name, description, type, value, is_active, created_at, updated_at) VALUES
                                ('Sibling Discount', 'Discount for siblings', 'PERCENTAGE', 10.00, TRUE, NOW(), NOW()),
                                ('Staff Child Discount', 'Discount for staff children', 'PERCENTAGE', 50.00, TRUE, NOW(), NOW())
                            """)
                            self.stdout.write("    ✅ Created concession types")
                except Exception as e:
                    self.stdout.write(f"    ⚠️  Concession types: {e}")
                
        except Exception as e:
            self.stdout.write(f"    ❌ Error applying essential data: {e}")

    def verify_tenant_basic(self, schema_name):
        """Basic verification that tenant is working"""
        
        try:
            with connection.cursor() as cursor:
                cursor.execute(f'SET search_path TO "{schema_name}"')
                
                # Check if basic tables exist
                essential_tables = [
                    'schools_academicyear', 'schools_staffuser', 'students_student',
                    'auth_group', 'auth_permission'
                ]
                
                existing_count = 0
                for table in essential_tables:
                    cursor.execute(f"""
                        SELECT EXISTS (
                            SELECT FROM information_schema.tables 
                            WHERE table_schema = '{schema_name}' 
                            AND table_name = '{table}'
                        )
                    """)
                    
                    if cursor.fetchone()[0]:
                        existing_count += 1
                    else:
                        self.stdout.write(f"    ⚠️  Missing table: {table}")
                
                self.stdout.write(f"    ✅ Found {existing_count}/{len(essential_tables)} essential tables")
                
                # Basic functionality test
                try:
                    cursor.execute("SELECT COUNT(*) FROM schools_academicyear")
                    ay_count = cursor.fetchone()[0]
                    self.stdout.write(f"    ✅ Academic years: {ay_count}")
                except:
                    self.stdout.write("    ⚠️  Could not check academic years")
                
                return existing_count >= len(essential_tables) * 0.8  # 80% success rate
                
        except Exception as e:
            self.stdout.write(f"    ❌ Verification error: {e}")
            return False
