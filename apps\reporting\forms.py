# apps/reporting/forms.py
from django import forms
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django_tenants.utils import schema_context

# Model imports for ModelChoiceFields
from apps.schools.models import SchoolClass
from apps.students.models import Student
from apps.payments.models import PaymentMethod
from apps.fees.models import Term, AcademicYear
from apps.finance.models import BudgetItem # Assuming BudgetItem model exists as before
from apps.accounting.models import Account as ChartOfAccount # For potential account filtering


# --- Base Form for common styling or methods ---
class BaseReportFilterForm(forms.Form):
    def __init__(self, *args, **kwargs):
        # Pop custom kwargs BEFORE calling the parent's __init__
        self.tenant = kwargs.pop('tenant', None)
        self.request = kwargs.pop('request', None) # <<< ADD THIS LINE TO POP 'request'
        
        super().__init__(*args, **kwargs) # Now kwargs does not contain 'tenant' or 'request'
        
        # Apply common styling to all fields
        for field_name, field in self.fields.items():
            current_class = field.widget.attrs.get('class', '') # Get existing classes
            new_classes = []

            if isinstance(field.widget, (forms.Select, forms.SelectMultiple)):
                new_classes.append('form-select form-select-sm')
            elif isinstance(field.widget, forms.DateInput):
                new_classes.append('form-control form-control-sm')
                # Ensure type='date' for HTML5 date picker if not already set by widget
                if field.widget.input_type == 'date' and 'type' not in field.widget.attrs:
                    field.widget.attrs['type'] = 'date'
            elif isinstance(field.widget, (forms.CheckboxInput, forms.RadioSelect)):
                # Checkboxes and radios might not need form-control, 
                # but might need form-check-input (handled by Bootstrap wrappers usually)
                pass # Or add 'form-check-input' if not wrapped
            else: # Default for TextInput, NumberInput, Textarea, etc.
                new_classes.append('form-control form-control-sm')
            
            # Combine new classes with existing ones, avoiding duplicates
            final_classes = current_class.split()
            for nc in new_classes:
                if nc not in final_classes: # Add if not already present
                    final_classes.append(nc)
            field.widget.attrs['class'] = ' '.join(final_classes).strip()
            
# --- General Purpose Forms ---

class DateRangeForm(BaseReportFilterForm):
    """
    For reports primarily filtered by a start and end date.
    Used by: CollectionReportView (can be extended for others).
    """
    start_date = forms.DateField(
        widget=forms.DateInput(attrs={'type': 'date'}),
        required=False,
        label="Start Date"
    )
    end_date = forms.DateField(
        widget=forms.DateInput(attrs={'type': 'date'}),
        required=False,
        label="End Date"
    )

class ReportPeriodForm(BaseReportFilterForm):
    """
    For reports that are "as at" a specific date.
    Used by: TrialBalanceView, BalanceSheetView.
    """
    report_date = forms.DateField(
        widget=forms.DateInput(attrs={'type': 'date'}),
        required=True, # Typically, an "as at" date is required
        label="Report As At Date",
        initial=timezone.now().date  # Sensible default
    )

# --- Specific Report Filter Forms ---

class DateRangeClassTermForm(BaseReportFilterForm):
    """
    For reports filterable by date range, class, and/or term.
    Used by: OutstandingFeesReportView.
    """
    start_date = forms.DateField(widget=forms.DateInput(attrs={'type': 'date'}), required=False, label="Start Date")
    end_date = forms.DateField(widget=forms.DateInput(attrs={'type': 'date'}), required=False, label="End Date")
    school_class = forms.ModelChoiceField(
        queryset=SchoolClass.objects.none(), # Queryset populated in __init__
        required=False,
        label="Filter by Class",
        empty_label="-- All Classes --"
    )
    term = forms.ModelChoiceField(
        queryset=Term.objects.none(), # Queryset populated in __init__
        required=False,
        label="Filter by Term",
        empty_label="-- All Terms --"
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Dynamically populate querysets based on the current tenant if applicable
        # This assumes 'tenant' might be passed during form instantiation in the view
        # Or, these querysets could be filtered in the view before passing to template
        self.fields['school_class'].queryset = SchoolClass.objects.all().order_by('name') # Adjust if tenant-specific
        self.fields['term'].queryset = Term.objects.select_related('academic_year').all().order_by('-academic_year__start_date', 'name') # Adjust if tenant-specific


class DateRangeAccountForm(BaseReportFilterForm):
    """
    Placeholder for reports needing date range and account filtering.
    Could be used by a detailed General Ledger report (not yet built).
    For Trial Balance, ReportPeriodForm is simpler.
    """
    start_date = forms.DateField(widget=forms.DateInput(attrs={'type': 'date'}), required=False, label="Start Date")
    end_date = forms.DateField(widget=forms.DateInput(attrs={'type': 'date'}), required=False, label="End Date")
    account = forms.ModelChoiceField(
        queryset=ChartOfAccount.objects.none(), # Populate in __init__
        required=False,
        label="Filter by Account",
        empty_label="-- All Accounts --"
        # widget=forms.Select(attrs={'class': 'form-select select2-field'}) # If using select2
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['account'].queryset = ChartOfAccount.objects.filter(is_active=True).order_by('code') # Adjust if tenant-specific


class IncomeExpenseReportForm(BaseReportFilterForm):
    """
    For Income/Expense (P&L) and Cash Flow Statement reports requiring a period.
    Used by: IncomeExpenseReportView, CashFlowStatementView.
    """
    start_date = forms.DateField(
        widget=forms.DateInput(attrs={'type': 'date'}),
        required=True, # P&L is for a period
        label="Period Start Date"
    )
    end_date = forms.DateField(
        widget=forms.DateInput(attrs={'type': 'date'}),
        required=True, # P&L is for a period
        label="Period End Date"
    )
    # Future enhancements:
    # comparison_period_start_date = forms.DateField(required=False, label="Comparison Start Date")
    # comparison_period_end_date = forms.DateField(required=False, label="Comparison End Date")
    # display_format = forms.ChoiceField(choices=[('monthly', 'Monthly'), ('quarterly', 'Quarterly'), ('annual', 'Annual')], required=False)


class BudgetVarianceReportForm(BaseReportFilterForm):
    """
    For the Budget Variance Report.
    Used by: BudgetVarianceReportView.
    """
    academic_year = forms.ModelChoiceField(
        queryset=AcademicYear.objects.none(), # Populate in __init__
        required=True,
        label="Select Academic Year",
        widget=forms.Select(attrs={'class': 'form-select form-select-sm'}) # Explicitly set class
    )
    term = forms.ModelChoiceField(
        queryset=Term.objects.none(), # Queryset populated in __init__ based on selected AY
        required=False, # Optional, for more granular variance
        label="Filter by Term (Optional)",
        empty_label="-- Whole Academic Year --"
    )
    # Optional: Filter by specific budget items or categories
    # budget_items = forms.ModelMultipleChoiceField(
    #     queryset=BudgetItem.objects.none(), # Populate in __init__
    #     required=False,
    #     label="Filter by Budget Items",
    #     widget=forms.SelectMultiple(attrs={'class': 'form-select form-select-sm', 'size': '5'})
    # )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['academic_year'].queryset = AcademicYear.objects.all().order_by('-start_date')
        # self.fields['budget_items'].queryset = BudgetItem.objects.all().order_by('name')

        # Dynamically filter terms based on selected academic year (if academic_year is in data)
        # This requires JavaScript on the front-end for a good UX, or handling on form submission.
        # For simplicity here, we'll load all terms. A better approach uses JS or a separate step.
        self.fields['term'].queryset = Term.objects.select_related('academic_year').all().order_by('-academic_year__start_date', 'name')

    def clean(self):
        cleaned_data = super().clean()
        academic_year = cleaned_data.get('academic_year')
        term = cleaned_data.get('term')

        if academic_year and term:
            if term.academic_year != academic_year:
                self.add_error('term', "Selected term does not belong to the selected academic year.")
        return cleaned_data
    
    
    
# D:\school_fees_saas_V2\apps\reporting\forms.py
from django import forms
from django.utils import timezone

class BalanceSheetFilterForm(forms.Form):
    as_at_date = forms.DateField(
        label="As at Date",
        widget=forms.DateInput(
            attrs={
                'type': 'date',
                'class': 'form-control form-control-sm', # Added form-control-sm for smaller size
                'placeholder': 'YYYY-MM-DD'
            }
        ),
        initial=timezone.now().date, # Default to today's date
        required=True # Usually, a balance sheet needs a specific date
    )
    # You can add more filters later if needed, e.g., comparison periods, branches, etc.

class IncomeStatementFilterForm(forms.Form):
    start_date = forms.DateField(
        label="Start Date",
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control form-control-sm'}),
        required=True
    )
    end_date = forms.DateField(
        label="End Date",
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control form-control-sm'}),
        initial=timezone.now().date,
        required=True
    )
    # Add more filters like 'comparison_period' if needed

class TrialBalanceFilterForm(forms.Form):
    as_at_date = forms.DateField(
        label="As at Date",
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control form-control-sm'}),
        initial=timezone.now().date,
        required=True
    )

class AgedReceivablesFilterForm(forms.Form):
    as_at_date = forms.DateField(
        label="As at Date",
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control form-control-sm'}),
        initial=timezone.now().date,
        required=True
    )
    # Potentially filter by customer, aging buckets, etc.

class AgedPayablesFilterForm(forms.Form):
    as_at_date = forms.DateField(
        label="As at Date",
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control form-control-sm'}),
        initial=timezone.now().date,
        required=True
    )
    # Potentially filter by vendor, aging buckets, etc.

class CashFlowFilterForm(forms.Form): # If you have a CashFlowView
    start_date = forms.DateField(
        label="Start Date",
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control form-control-sm'}),
        required=True
    )
    end_date = forms.DateField(
        label="End Date",
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control form-control-sm'}),
        initial=timezone.now().date,
        required=True
    )



# D:\school_fees_saas_v2\apps\reporting\forms.py
from django import forms
from apps.payments.models import PaymentMethod # Assuming PaymentMethod is in payments app
from apps.students.models import Student, SchoolClass # Assuming SchoolClass is in students or schools app
from django.utils import timezone
import datetime

class BaseReportFilterForm(forms.Form):
    start_date = forms.DateField(
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control form-control-sm'}),
        required=False,
        label="Start Date"
    )
    end_date = forms.DateField(
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control form-control-sm'}),
        required=False,
        label="End Date"
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Set default date range (e.g., this month) if not provided
        if not self.initial.get('start_date') and not self.initial.get('end_date'):
            today = timezone.now().date()
            first_day_of_month = today.replace(day=1)
            # self.fields['start_date'].initial = first_day_of_month # Optional: default to start of month
            # self.fields['end_date'].initial = today # Optional: default to today

# apps/reporting/forms.py
from django import forms
from django.utils.translation import gettext_lazy as _
from apps.payments.models import PaymentMethod
from apps.students.models import Student, SchoolClass # Assuming SchoolClass is here or accessible
from django.utils import timezone

class PaymentSummaryFilterForm(forms.Form):
    start_date = forms.DateField(
        label=_("From Date"), 
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control form-control-sm'}),
        required=False
    )
    end_date = forms.DateField(
        label=_("To Date"), 
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control form-control-sm'}),
        required=False
    )
    payment_method = forms.ModelChoiceField(
        queryset=PaymentMethod.objects.all(), # Consider filtering by tenant if methods are tenant-specific
        label=_("Payment Method"),
        required=False,
        empty_label=_("All Methods"),
        widget=forms.Select(attrs={'class': 'form-select form-select-sm'})
    )
    student = forms.ModelChoiceField( # This will be a large dropdown, consider autocomplete
        queryset=Student.objects.none(), # Populate dynamically in __init__
        label=_("Student"),
        required=False,
        empty_label=_("All Students"),
        widget=forms.Select(attrs={'class': 'form-select form-select-sm select2-student-ajax'}) # For Select2 AJAX
    )
    school_class = forms.ModelChoiceField(
        queryset=SchoolClass.objects.none(), # Populate dynamically
        label=_("Class"),
        required=False,
        empty_label=_("All Classes"),
        widget=forms.Select(attrs={'class': 'form-select form-select-sm'})
    )
    # Add other filters like section, payment status if needed

    def __init__(self, *args, **kwargs):
        self.request = kwargs.pop('request', None) # To get tenant for filtering choices
        self.tenant = kwargs.pop('tenant', None) # Handle tenant parameter
        super().__init__(*args, **kwargs)
        
        # Dynamically populate queryset for student and class based on tenant if request is available
        if self.request and hasattr(self.request, 'tenant') and self.request.tenant:
            # This assumes Student and SchoolClass are tenant-scoped or you filter them by tenant
            with schema_context(self.request.tenant.schema_name): # If models are in tenant schema
                self.fields['student'].queryset = Student.objects.all().order_by('admission_number')
                self.fields['school_class'].queryset = SchoolClass.objects.all().order_by('name')
        else: # Fallback if no tenant (e.g., if form is instantiated outside tenant context)
            self.fields['student'].queryset = Student.objects.all().order_by('admission_number') # Or some default
            self.fields['school_class'].queryset = SchoolClass.objects.all().order_by('name')


        # Set default date range if not provided in GET (e.g., current month)
        if not self.is_bound: # Only set initial if form is not bound (i.e., not from GET)
            today = timezone.now().date()
            self.fields['start_date'].initial = today.replace(day=1)
            self.fields['end_date'].initial = today

class StudentLedgerFilterForm(forms.Form):
    student = forms.ModelChoiceField(
        queryset=Student.objects.all(), # This will be filtered by tenant in the view's get_form_kwargs
        required=True,
        label="Select Student",
        widget=forms.Select(attrs={'class': 'form-select form-select-lg'}) # Larger select for student
    )
    start_date = forms.DateField(
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control form-control-sm'}),
        required=False,
        label="Start Date (Optional)"
    )
    end_date = forms.DateField(
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control form-control-sm'}),
        required=False,
        label="End Date (Optional)"
    )

    def __init__(self, *args, **kwargs):
        tenant = kwargs.pop('tenant', None)
        super().__init__(*args, **kwargs)
        if tenant:
            # Assuming Student model has a direct or indirect link to the tenant
            # For django-tenants, queries are automatically scoped, but for choices it's good to filter
            self.fields['student'].queryset = Student.objects.all().order_by('last_name', 'first_name', 'middle_name')




# D:\school_fees_saas_v2\apps\reporting\forms.py

from django import forms
from django.utils import timezone
from django.utils.translation import gettext_lazy as _ # Import for translations
from apps.payments.models import PaymentMethod # Assuming this model exists
from apps.schools.models import SchoolClass     # Assuming this model exists
# Add any other models needed for choices
class CollectionReportFilterForm(forms.Form):
    start_date = forms.DateField(
        label=_("Start Date"),
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control form-control-sm'}), 
        required=False
    )
    end_date = forms.DateField(
        label=_("End Date"),
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control form-control-sm'}), 
        required=False
    )
    student_query = forms.CharField(
        label=_("Student/Parent Search"),
        widget=forms.TextInput(attrs={'class': 'form-control form-control-sm', 'placeholder': 'Student Name/Adm/Email or Parent Name/Email'}), 
        required=False
    )
    payment_method = forms.ModelChoiceField(
        queryset=PaymentMethod.objects.filter(is_active=True).order_by('name'), 
        required=False, 
        widget=forms.Select(attrs={'class': 'form-select form-select-sm'}),
        empty_label=_("All Payment Methods"),
        label=_("Payment Method")
    )
    class_obj = forms.ModelChoiceField( 
        queryset=SchoolClass.objects.all().order_by('name'), # Scope this in __init__ if SchoolClass is tenant-specific
        required=False,
        widget=forms.Select(attrs={'class': 'form-select form-select-sm'}),
        label=_("Class"),
        empty_label=_("All Classes")
    )
    # Add more filters as needed (e.g., section, specific fee head, staff who recorded payment)

    def __init__(self, *args, **kwargs):
        # Pop tenant or request if they are passed and needed for dynamic querysets
        self.tenant = kwargs.pop('tenant', None) 
        self.request = kwargs.pop('request', None)
        super().__init__(*args, **kwargs)

        # Example: Scope SchoolClass choices if it's tenant-specific and SchoolClass has a tenant FK
        # if self.tenant and hasattr(SchoolClass, 'tenant_id_field_name'): # Replace with actual FK field name
        #    self.fields['class_obj'].queryset = SchoolClass.objects.filter(tenant_id_field_name=self.tenant)
        # If SchoolClass is implicitly schema-scoped (like models in TENANT_APPS),
        # Django handles the scoping automatically when the form is instantiated within a tenant context.
        
        # If you want to set initial default dates here:
        # if not self.is_bound: # Only set initial if form is not bound to GET data
        #     self.initial['start_date'] = timezone.now().replace(day=1).date()
        #     self.initial['end_date'] = timezone.now().date()

    def clean(self):
        cleaned_data = super().clean()
        start_date = cleaned_data.get("start_date")
        end_date = cleaned_data.get("end_date")

        if start_date and end_date and end_date < start_date:
            self.add_error('end_date', _("End date cannot be before start date."))
        return cleaned_data


# D:\school_fees_saas_v2\apps\reporting\forms.py

from django import forms
from django.utils.translation import gettext_lazy as _
# Import models needed for choices, e.g., Budget (if you filter by a specific budget)
# from apps.finance.models import Budget 
from apps.schools.models import AcademicYear # Often budgets are per academic year

class BudgetReportFilterForm(forms.Form):
    # Example fields - adjust to your needs
    academic_year = forms.ModelChoiceField(
        queryset=AcademicYear.objects.all().order_by('-start_date'), # Scope in __init__ if needed
        required=True, # Usually a budget variance report needs a year
        widget=forms.Select(attrs={'class': 'form-select form-select-sm'}),
        label=_("Academic Year / Budget Period")
    )
    # You might add filters for specific departments, projects, etc.
    # department = forms.ModelChoiceField(queryset=Department.objects.all(), required=False, ...)

    def __init__(self, *args, **kwargs):
        tenant = kwargs.pop('tenant', None)
        request = kwargs.pop('request', None)
        super().__init__(*args, **kwargs)
        
        # Example: Scope AcademicYear choices if it has a tenant FK or is schema-scoped
        # if tenant and hasattr(AcademicYear, 'tenant_fk'):
        #     self.fields['academic_year'].queryset = AcademicYear.objects.filter(tenant_fk=tenant).order_by('-start_date')
        # Or if your AcademicYear model is inherently schema-scoped, this is fine.
        
        # Set initial value for academic_year to the current one if possible
        if not self.is_bound and 'academic_year' not in self.initial:
            current_ay = AcademicYear.objects.filter(is_current=True).first()
            if not current_ay: # Fallback to latest if no 'current'
                current_ay = AcademicYear.objects.order_by('-start_date').first()
            if current_ay:
                self.initial['academic_year'] = current_ay.pk


# D:\school_fees_saas_v2\apps\reporting\forms.py
from django import forms
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from apps.finance.models import ExpenseCategory # Assuming you have this
from apps.payments.models import PaymentMethod  # Assuming you have this

class ExpenseReportFilterForm(forms.Form):
    start_date = forms.DateField(
        label=_("Start Date"),
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control form-control-sm'}),
        required=False
    )
    end_date = forms.DateField(
        label=_("End Date"),
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control form-control-sm'}),
        required=False
    )
    category = forms.ModelChoiceField(
        queryset=ExpenseCategory.objects.all(), # Scope in __init__ if tenant-specific
        required=False,
        widget=forms.Select(attrs={'class': 'form-select form-select-sm'}),
        empty_label=_("All Categories"),
        label=_("Category")
    )
    payment_method = forms.ModelChoiceField(
        queryset=PaymentMethod.objects.filter(is_active=True), # Scope in __init__
        required=False,
        widget=forms.Select(attrs={'class': 'form-select form-select-sm'}),
        empty_label=_("All Payment Methods"),
        label=_("Payment Method")
    )
    description_contains = forms.CharField(
        label=_("Description Contains"),
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control form-control-sm', 'placeholder': 'e.g., stationary, repairs'})
    )

    def __init__(self, *args, **kwargs):
        self.tenant = kwargs.pop('tenant', None)
        self.request = kwargs.pop('request', None) # May not be needed here
        super().__init__(*args, **kwargs)

        # Example: Scope ExpenseCategory queryset if it's tenant-specific
        # (e.g., if ExpenseCategory has a ForeignKey to School or is schema-scoped)
        # if self.tenant and hasattr(ExpenseCategory, 'tenant_fk_field'):
        #     self.fields['category'].queryset = ExpenseCategory.objects.filter(tenant_fk_field=self.tenant).order_by('name')
        # elif self.tenant: # If schema-scoped (no direct FK, but objects are in tenant schema)
        # This queryset runs in the current tenant's schema context anyway.
        self.fields['category'].queryset = ExpenseCategory.objects.all().order_by('name')
        self.fields['payment_method'].queryset = PaymentMethod.objects.filter(is_active=True).order_by('name')


    def clean(self):
        cleaned_data = super().clean()
        start_date = cleaned_data.get("start_date")
        end_date = cleaned_data.get("end_date")

        if start_date and end_date and end_date < start_date:
            self.add_error('end_date', _("End date cannot be before start date."))
        return cleaned_data



# your_project/apps/reporting/forms.py

from django import forms
from django.utils.translation import gettext_lazy as _

# Assuming your SchoolClass model is in the 'schools' app
# Adjust the import path if your model is located elsewhere
from apps.schools.models import SchoolClass

class OutstandingFeesFilterForm(forms.Form):
    """
    A form for filtering the Outstanding Fees Report.
    """
    school_class = forms.ModelChoiceField(
        label=_("Filter by Class"),
        queryset=SchoolClass.objects.filter(is_active=True).order_by('name'),
        required=False,
        empty_label=_("--- All Classes ---"),
        widget=forms.Select(attrs={'class': 'form-select'})
    )

    min_due_amount = forms.DecimalField(
        label=_("Minimum Amount Due"),
        required=False,
        min_value=0,
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'placeholder': 'e.g., 50.00'
        })
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # You can add more dynamic adjustments here if needed
        # For example, dynamically setting the queryset based on the tenant/user
    
    

# your_project/apps/reporting/forms.py

from django import forms
from django.utils.translation import gettext_lazy as _

# Adjust these import paths to match your project structure
from apps.schools.models import SchoolClass
from apps.finance.models import PaymentMethod

class CollectionReportFilterForm(forms.Form):
    """
    A form for filtering the Fee Collection Report.
    """
    start_date = forms.DateField(
        label=_("Start Date"),
        required=False,
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control'})
    )
    
    end_date = forms.DateField(
        label=_("End Date"),
        required=False,
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control'})
    )
    
    student_query = forms.CharField(
        label=_("Student Name or Adm. No"),
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Search student...'
        })
    )
    
    school_class = forms.ModelChoiceField(
        label=_("Filter by Class"),
        queryset=SchoolClass.objects.filter(is_active=True).order_by('name'),
        required=False,
        empty_label=_("--- All Classes ---"),
        widget=forms.Select(attrs={'class': 'form-select'})
    )
   
    payment_method = forms.ModelChoiceField(
        label=_("Payment Method"),
        queryset=PaymentMethod.objects.filter(is_active=True).order_by('name'),
        required=False,
        empty_label=_("--- All Methods ---"),
        widget=forms.Select(attrs={'class': 'form-select'})
    )

    def clean(self):
        cleaned_data = super().clean()
        start_date = cleaned_data.get('start_date')
        end_date = cleaned_data.get('end_date')

        if start_date and end_date and start_date > end_date:
            raise forms.ValidationError(_("The start date cannot be after the end date."))
            
        return cleaned_data



# your_project/apps/reporting/forms.py

from django import forms
from django.utils.translation import gettext_lazy as _

# ... other form classes ...

class FeeProjectionFilterForm(forms.Form):
    """
    A placeholder form for the Fee Projection Report.
    This report currently does not use any filters, but this form
    can be extended in the future if needed (e.g., to select an
    academic year).
    """
    pass



# your_project/apps/reporting/forms.py

from django import forms
from django.utils.translation import gettext_lazy as _
from django.utils import timezone

# ... (other form imports) ...
from apps.finance.models import PaymentMethod
from apps.schools.models import SchoolClass

# ... (other form classes) ...

class PaymentReportFilter(forms.Form):
    """
    A form for filtering the Payment Summary Report.
    """
    start_date = forms.DateField(
        label=_("Start Date"),
        required=False,
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control'})
    )
    
    end_date = forms.DateField(
        label=_("End Date"),
        required=False,
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control'})
    )

    payment_method = forms.ModelChoiceField(
        label=_("Payment Method"),
        queryset=PaymentMethod.objects.filter(is_active=True).order_by('name'),
        required=False,
        empty_label=_("--- All Methods ---"),
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    
    school_class = forms.ModelChoiceField(
        label=_("Class"),
        queryset=SchoolClass.objects.filter(is_active=True).order_by('name'),
        required=False,
        empty_label=_("--- All Classes ---"),
        widget=forms.Select(attrs={'class': 'form-select'})
    )

    def clean(self):
        cleaned_data = super().clean()
        start_date = cleaned_data.get('start_date')
        end_date = cleaned_data.get('end_date')

        if start_date and end_date and start_date > end_date:
            raise forms.ValidationError(_("The start date cannot be after the end date."))
            
        return cleaned_data


# # your_project/apps/reporting/forms.py

# from django import forms
# from django.utils.translation import gettext_lazy as _
# from django.utils import timezone

# # Adjust these import paths to match your project structure
# from apps.finance.models import ExpenseCategory, PaymentMethod

# # ... other form classes ...

# class ExpenseReportFilterForm(forms.Form):
#     """
#     A form for filtering the Expense Report.
#     """
#     start_date = forms.DateField(
#         label=_("Start Date"),
#         required=False,
#         widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control'})
#     )
    
#     end_date = forms.DateField(
#         label=_("End Date"),
#         required=False,
#         widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control'})
#     )

#     category = forms.ModelChoiceField(
#         label=_("Category"),
#         queryset=ExpenseCategory.objects.filter(is_active=True).order_by('name'),
#         required=False,
#         empty_label=_("--- All Categories ---"),
#         widget=forms.Select(attrs={'class': 'form-select'})
#     )
    
#     payment_method = forms.ModelChoiceField(
#         label=_("Payment Method"),
#         queryset=PaymentMethod.objects.filter(is_active=True).order_by('name'),
#         required=False,
#         empty_label=_("--- All Methods ---"),
#         widget=forms.Select(attrs={'class': 'form-select'})
#     )
    
#     description_contains = forms.CharField(
#         label=_("Description Contains"),
#         required=False,
#         widget=forms.TextInput(attrs={
#             'class': 'form-control',
#             'placeholder': 'e.g., "stationery" or "repair"'
#         })
#     )

#     def clean(self):
#         cleaned_data = super().clean()
#         start_date = cleaned_data.get('start_date')
#         end_date = cleaned_data.get('end_date')

#         if start_date and end_date and start_date > end_date:
#             raise forms.ValidationError(_("The start date cannot be after the end date."))
            
#         return cleaned_data




















