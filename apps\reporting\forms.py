# apps/reporting/forms.py
from django import forms
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django_tenants.utils import schema_context

# Model imports for ModelChoiceFields
from apps.schools.models import SchoolClass
from apps.students.models import Student
from apps.payments.models import PaymentMethod
from apps.fees.models import Term, AcademicYear, FeeHead
from apps.finance.models import BudgetItem # Assuming BudgetItem model exists as before
from apps.accounting.models import Account as ChartOfAccount, AccountType, JournalEntry # For potential account filtering


# --- Base Form for common styling or methods ---
class BaseReportFilterForm(forms.Form):
    def __init__(self, *args, **kwargs):
        # Pop custom kwargs BEFORE calling the parent's __init__
        self.tenant = kwargs.pop('tenant', None)
        self.request = kwargs.pop('request', None) # <<< ADD THIS LINE TO POP 'request'
        
        super().__init__(*args, **kwargs) # Now kwargs does not contain 'tenant' or 'request'
        
        # Apply common styling to all fields
        for field_name, field in self.fields.items():
            current_class = field.widget.attrs.get('class', '') # Get existing classes
            new_classes = []

            if isinstance(field.widget, (forms.Select, forms.SelectMultiple)):
                new_classes.append('form-select form-select-sm')
            elif isinstance(field.widget, forms.DateInput):
                new_classes.append('form-control form-control-sm')
                # Ensure type='date' for HTML5 date picker if not already set by widget
                if field.widget.input_type == 'date' and 'type' not in field.widget.attrs:
                    field.widget.attrs['type'] = 'date'
            elif isinstance(field.widget, (forms.CheckboxInput, forms.RadioSelect)):
                # Checkboxes and radios might not need form-control, 
                # but might need form-check-input (handled by Bootstrap wrappers usually)
                pass # Or add 'form-check-input' if not wrapped
            else: # Default for TextInput, NumberInput, Textarea, etc.
                new_classes.append('form-control form-control-sm')
            
            # Combine new classes with existing ones, avoiding duplicates
            final_classes = current_class.split()
            for nc in new_classes:
                if nc not in final_classes: # Add if not already present
                    final_classes.append(nc)
            field.widget.attrs['class'] = ' '.join(final_classes).strip()
            
# --- General Purpose Forms ---

class DateRangeForm(BaseReportFilterForm):
    """
    For reports primarily filtered by a start and end date.
    Used by: CollectionReportView (can be extended for others).
    """
    start_date = forms.DateField(
        widget=forms.DateInput(attrs={'type': 'date'}),
        required=False,
        label="Start Date"
    )
    end_date = forms.DateField(
        widget=forms.DateInput(attrs={'type': 'date'}),
        required=False,
        label="End Date"
    )

class ReportPeriodForm(BaseReportFilterForm):
    """
    For reports that are "as at" a specific date.
    Used by: TrialBalanceView, BalanceSheetView.
    """
    report_date = forms.DateField(
        widget=forms.DateInput(attrs={'type': 'date'}),
        required=True, # Typically, an "as at" date is required
        label="Report As At Date",
        initial=timezone.now().date  # Sensible default
    )

# --- Specific Report Filter Forms ---

class DateRangeClassTermForm(BaseReportFilterForm):
    """
    For reports filterable by date range, class, and/or term.
    Used by: OutstandingFeesReportView.
    """
    start_date = forms.DateField(widget=forms.DateInput(attrs={'type': 'date'}), required=False, label="Start Date")
    end_date = forms.DateField(widget=forms.DateInput(attrs={'type': 'date'}), required=False, label="End Date")
    school_class = forms.ModelChoiceField(
        queryset=SchoolClass.objects.none(), # Queryset populated in __init__
        required=False,
        label="Filter by Class",
        empty_label="-- All Classes --"
    )
    term = forms.ModelChoiceField(
        queryset=Term.objects.none(), # Queryset populated in __init__
        required=False,
        label="Filter by Term",
        empty_label="-- All Terms --"
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Dynamically populate querysets based on the current tenant if applicable
        # This assumes 'tenant' might be passed during form instantiation in the view
        # Or, these querysets could be filtered in the view before passing to template
        self.fields['school_class'].queryset = SchoolClass.objects.all().order_by('name') # Adjust if tenant-specific
        self.fields['term'].queryset = Term.objects.select_related('academic_year').all().order_by('-academic_year__start_date', 'name') # Adjust if tenant-specific


class DateRangeAccountForm(BaseReportFilterForm):
    """
    Placeholder for reports needing date range and account filtering.
    Could be used by a detailed General Ledger report (not yet built).
    For Trial Balance, ReportPeriodForm is simpler.
    """
    start_date = forms.DateField(widget=forms.DateInput(attrs={'type': 'date'}), required=False, label="Start Date")
    end_date = forms.DateField(widget=forms.DateInput(attrs={'type': 'date'}), required=False, label="End Date")
    account = forms.ModelChoiceField(
        queryset=ChartOfAccount.objects.none(), # Populate in __init__
        required=False,
        label="Filter by Account",
        empty_label="-- All Accounts --"
        # widget=forms.Select(attrs={'class': 'form-select select2-field'}) # If using select2
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['account'].queryset = ChartOfAccount.objects.filter(is_active=True).order_by('code') # Adjust if tenant-specific


class IncomeExpenseReportForm(BaseReportFilterForm):
    """
    For Income/Expense (P&L) and Cash Flow Statement reports requiring a period.
    Used by: IncomeExpenseReportView, CashFlowStatementView.
    """
    start_date = forms.DateField(
        widget=forms.DateInput(attrs={'type': 'date'}),
        required=True, # P&L is for a period
        label="Period Start Date"
    )
    end_date = forms.DateField(
        widget=forms.DateInput(attrs={'type': 'date'}),
        required=True, # P&L is for a period
        label="Period End Date"
    )
    # Future enhancements:
    # comparison_period_start_date = forms.DateField(required=False, label="Comparison Start Date")
    # comparison_period_end_date = forms.DateField(required=False, label="Comparison End Date")
    # display_format = forms.ChoiceField(choices=[('monthly', 'Monthly'), ('quarterly', 'Quarterly'), ('annual', 'Annual')], required=False)


class BudgetVarianceReportForm(BaseReportFilterForm):
    """
    For the Budget Variance Report.
    Used by: BudgetVarianceReportView.
    """
    academic_year = forms.ModelChoiceField(
        queryset=AcademicYear.objects.none(), # Populate in __init__
        required=True,
        label="Select Academic Year",
        widget=forms.Select(attrs={'class': 'form-select form-select-sm'}) # Explicitly set class
    )
    term = forms.ModelChoiceField(
        queryset=Term.objects.none(), # Queryset populated in __init__ based on selected AY
        required=False, # Optional, for more granular variance
        label="Filter by Term (Optional)",
        empty_label="-- Whole Academic Year --"
    )
    # Optional: Filter by specific budget items or categories
    # budget_items = forms.ModelMultipleChoiceField(
    #     queryset=BudgetItem.objects.none(), # Populate in __init__
    #     required=False,
    #     label="Filter by Budget Items",
    #     widget=forms.SelectMultiple(attrs={'class': 'form-select form-select-sm', 'size': '5'})
    # )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['academic_year'].queryset = AcademicYear.objects.all().order_by('-start_date')
        # self.fields['budget_items'].queryset = BudgetItem.objects.all().order_by('name')

        # Dynamically filter terms based on selected academic year (if academic_year is in data)
        # This requires JavaScript on the front-end for a good UX, or handling on form submission.
        # For simplicity here, we'll load all terms. A better approach uses JS or a separate step.
        self.fields['term'].queryset = Term.objects.select_related('academic_year').all().order_by('-academic_year__start_date', 'name')

    def clean(self):
        cleaned_data = super().clean()
        academic_year = cleaned_data.get('academic_year')
        term = cleaned_data.get('term')

        if academic_year and term:
            if term.academic_year != academic_year:
                self.add_error('term', "Selected term does not belong to the selected academic year.")
        return cleaned_data
    
    
    
# D:\school_fees_saas_V2\apps\reporting\forms.py
from django import forms
from django.utils import timezone

class BalanceSheetFilterForm(forms.Form):
    as_at_date = forms.DateField(
        label="As at Date",
        widget=forms.DateInput(
            attrs={
                'type': 'date',
                'class': 'form-control form-control-sm', # Added form-control-sm for smaller size
                'placeholder': 'YYYY-MM-DD'
            }
        ),
        initial=timezone.now().date, # Default to today's date
        required=True # Usually, a balance sheet needs a specific date
    )
    # You can add more filters later if needed, e.g., comparison periods, branches, etc.

class IncomeStatementFilterForm(forms.Form):
    start_date = forms.DateField(
        label="Start Date",
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control form-control-sm'}),
        required=True
    )
    end_date = forms.DateField(
        label="End Date",
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control form-control-sm'}),
        initial=timezone.now().date,
        required=True
    )
    # Add more filters like 'comparison_period' if needed

class TrialBalanceFilterForm(forms.Form):
    as_at_date = forms.DateField(
        label="As at Date",
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control form-control-sm'}),
        initial=timezone.now().date,
        required=True
    )

class AgedReceivablesFilterForm(forms.Form):
    as_at_date = forms.DateField(
        label="As at Date",
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control form-control-sm'}),
        initial=timezone.now().date,
        required=True
    )
    # Potentially filter by customer, aging buckets, etc.

class AgedPayablesFilterForm(forms.Form):
    as_at_date = forms.DateField(
        label="As at Date",
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control form-control-sm'}),
        initial=timezone.now().date,
        required=True
    )
    # Potentially filter by vendor, aging buckets, etc.

class CashFlowFilterForm(forms.Form): # If you have a CashFlowView
    start_date = forms.DateField(
        label="Start Date",
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control form-control-sm'}),
        required=True
    )
    end_date = forms.DateField(
        label="End Date",
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control form-control-sm'}),
        initial=timezone.now().date,
        required=True
    )



# D:\school_fees_saas_v2\apps\reporting\forms.py
from django import forms
from apps.payments.models import PaymentMethod # Assuming PaymentMethod is in payments app
from apps.students.models import Student, SchoolClass # Assuming SchoolClass is in students or schools app
from django.utils import timezone
import datetime

class BaseReportFilterForm(forms.Form):
    start_date = forms.DateField(
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control form-control-sm'}),
        required=False,
        label="Start Date"
    )
    end_date = forms.DateField(
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control form-control-sm'}),
        required=False,
        label="End Date"
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Set default date range (e.g., this month) if not provided
        if not self.initial.get('start_date') and not self.initial.get('end_date'):
            today = timezone.now().date()
            first_day_of_month = today.replace(day=1)
            # self.fields['start_date'].initial = first_day_of_month # Optional: default to start of month
            # self.fields['end_date'].initial = today # Optional: default to today

# apps/reporting/forms.py
from django import forms
from django.utils.translation import gettext_lazy as _
from apps.payments.models import PaymentMethod
from apps.students.models import Student, SchoolClass # Assuming SchoolClass is here or accessible
from django.utils import timezone

class PaymentSummaryFilterForm(forms.Form):
    start_date = forms.DateField(
        label=_("From Date"), 
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control form-control-sm'}),
        required=False
    )
    end_date = forms.DateField(
        label=_("To Date"), 
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control form-control-sm'}),
        required=False
    )
    payment_method = forms.ModelChoiceField(
        queryset=PaymentMethod.objects.all(), # Consider filtering by tenant if methods are tenant-specific
        label=_("Payment Method"),
        required=False,
        empty_label=_("All Methods"),
        widget=forms.Select(attrs={'class': 'form-select form-select-sm'})
    )
    student = forms.ModelChoiceField( # This will be a large dropdown, consider autocomplete
        queryset=Student.objects.none(), # Populate dynamically in __init__
        label=_("Student"),
        required=False,
        empty_label=_("All Students"),
        widget=forms.Select(attrs={'class': 'form-select form-select-sm select2-student-ajax'}) # For Select2 AJAX
    )
    school_class = forms.ModelChoiceField(
        queryset=SchoolClass.objects.none(), # Populate dynamically
        label=_("Class"),
        required=False,
        empty_label=_("All Classes"),
        widget=forms.Select(attrs={'class': 'form-select form-select-sm'})
    )
    # Add other filters like section, payment status if needed

    def __init__(self, *args, **kwargs):
        self.request = kwargs.pop('request', None) # To get tenant for filtering choices
        self.tenant = kwargs.pop('tenant', None) # Handle tenant parameter
        super().__init__(*args, **kwargs)
        
        # Dynamically populate queryset for student and class based on tenant if request is available
        if self.request and hasattr(self.request, 'tenant') and self.request.tenant:
            # This assumes Student and SchoolClass are tenant-scoped or you filter them by tenant
            with schema_context(self.request.tenant.schema_name): # If models are in tenant schema
                self.fields['student'].queryset = Student.objects.all().order_by('admission_number')
                self.fields['school_class'].queryset = SchoolClass.objects.all().order_by('name')
        else: # Fallback if no tenant (e.g., if form is instantiated outside tenant context)
            self.fields['student'].queryset = Student.objects.all().order_by('admission_number') # Or some default
            self.fields['school_class'].queryset = SchoolClass.objects.all().order_by('name')


        # Set default date range if not provided in GET (e.g., current month)
        if not self.is_bound: # Only set initial if form is not bound (i.e., not from GET)
            today = timezone.now().date()
            self.fields['start_date'].initial = today.replace(day=1)
            self.fields['end_date'].initial = today

class StudentLedgerFilterForm(forms.Form):
    student = forms.ModelChoiceField(
        queryset=Student.objects.all(), # This will be filtered by tenant in the view's get_form_kwargs
        required=True,
        label="Select Student",
        widget=forms.Select(attrs={'class': 'form-select form-select-lg'}) # Larger select for student
    )
    start_date = forms.DateField(
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control form-control-sm'}),
        required=False,
        label="Start Date (Optional)"
    )
    end_date = forms.DateField(
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control form-control-sm'}),
        required=False,
        label="End Date (Optional)"
    )

    def __init__(self, *args, **kwargs):
        tenant = kwargs.pop('tenant', None)
        super().__init__(*args, **kwargs)
        if tenant:
            # Assuming Student model has a direct or indirect link to the tenant
            # For django-tenants, queries are automatically scoped, but for choices it's good to filter
            self.fields['student'].queryset = Student.objects.all().order_by('last_name', 'first_name', 'middle_name')




# D:\school_fees_saas_v2\apps\reporting\forms.py

from django import forms
from django.utils import timezone
from django.utils.translation import gettext_lazy as _ # Import for translations
from apps.payments.models import PaymentMethod # Assuming this model exists
from apps.schools.models import SchoolClass     # Assuming this model exists
# Add any other models needed for choices
class CollectionReportFilterForm(forms.Form):
    start_date = forms.DateField(
        label=_("Start Date"),
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control form-control-sm'}), 
        required=False
    )
    end_date = forms.DateField(
        label=_("End Date"),
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control form-control-sm'}), 
        required=False
    )
    student_query = forms.CharField(
        label=_("Student/Parent Search"),
        widget=forms.TextInput(attrs={'class': 'form-control form-control-sm', 'placeholder': 'Student Name/Adm/Email or Parent Name/Email'}), 
        required=False
    )
    payment_method = forms.ModelChoiceField(
        queryset=PaymentMethod.objects.filter(is_active=True).order_by('name'), 
        required=False, 
        widget=forms.Select(attrs={'class': 'form-select form-select-sm'}),
        empty_label=_("All Payment Methods"),
        label=_("Payment Method")
    )
    class_obj = forms.ModelChoiceField( 
        queryset=SchoolClass.objects.all().order_by('name'), # Scope this in __init__ if SchoolClass is tenant-specific
        required=False,
        widget=forms.Select(attrs={'class': 'form-select form-select-sm'}),
        label=_("Class"),
        empty_label=_("All Classes")
    )
    # Add more filters as needed (e.g., section, specific fee head, staff who recorded payment)

    def __init__(self, *args, **kwargs):
        # Pop tenant or request if they are passed and needed for dynamic querysets
        self.tenant = kwargs.pop('tenant', None) 
        self.request = kwargs.pop('request', None)
        super().__init__(*args, **kwargs)

        # Example: Scope SchoolClass choices if it's tenant-specific and SchoolClass has a tenant FK
        # if self.tenant and hasattr(SchoolClass, 'tenant_id_field_name'): # Replace with actual FK field name
        #    self.fields['class_obj'].queryset = SchoolClass.objects.filter(tenant_id_field_name=self.tenant)
        # If SchoolClass is implicitly schema-scoped (like models in TENANT_APPS),
        # Django handles the scoping automatically when the form is instantiated within a tenant context.
        
        # If you want to set initial default dates here:
        # if not self.is_bound: # Only set initial if form is not bound to GET data
        #     self.initial['start_date'] = timezone.now().replace(day=1).date()
        #     self.initial['end_date'] = timezone.now().date()

    def clean(self):
        cleaned_data = super().clean()
        start_date = cleaned_data.get("start_date")
        end_date = cleaned_data.get("end_date")

        if start_date and end_date and end_date < start_date:
            self.add_error('end_date', _("End date cannot be before start date."))
        return cleaned_data


# D:\school_fees_saas_v2\apps\reporting\forms.py

from django import forms
from django.utils.translation import gettext_lazy as _
# Import models needed for choices, e.g., Budget (if you filter by a specific budget)
# from apps.finance.models import Budget 
from apps.schools.models import AcademicYear # Often budgets are per academic year

class BudgetReportFilterForm(forms.Form):
    # Example fields - adjust to your needs
    academic_year = forms.ModelChoiceField(
        queryset=AcademicYear.objects.all().order_by('-start_date'), # Scope in __init__ if needed
        required=True, # Usually a budget variance report needs a year
        widget=forms.Select(attrs={'class': 'form-select form-select-sm'}),
        label=_("Academic Year / Budget Period")
    )
    # You might add filters for specific departments, projects, etc.
    # department = forms.ModelChoiceField(queryset=Department.objects.all(), required=False, ...)

    def __init__(self, *args, **kwargs):
        tenant = kwargs.pop('tenant', None)
        request = kwargs.pop('request', None)
        super().__init__(*args, **kwargs)
        
        # Example: Scope AcademicYear choices if it has a tenant FK or is schema-scoped
        # if tenant and hasattr(AcademicYear, 'tenant_fk'):
        #     self.fields['academic_year'].queryset = AcademicYear.objects.filter(tenant_fk=tenant).order_by('-start_date')
        # Or if your AcademicYear model is inherently schema-scoped, this is fine.
        
        # Set initial value for academic_year to the current one if possible
        if not self.is_bound and 'academic_year' not in self.initial:
            current_ay = AcademicYear.objects.filter(is_current=True).first()
            if not current_ay: # Fallback to latest if no 'current'
                current_ay = AcademicYear.objects.order_by('-start_date').first()
            if current_ay:
                self.initial['academic_year'] = current_ay.pk


# D:\school_fees_saas_v2\apps\reporting\forms.py
from django import forms
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from apps.finance.models import ExpenseCategory # Assuming you have this
from apps.payments.models import PaymentMethod  # Assuming you have this

class ExpenseReportFilterForm(forms.Form):
    start_date = forms.DateField(
        label=_("Start Date"),
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control form-control-sm'}),
        required=False
    )
    end_date = forms.DateField(
        label=_("End Date"),
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control form-control-sm'}),
        required=False
    )
    category = forms.ModelChoiceField(
        queryset=ExpenseCategory.objects.all(), # Scope in __init__ if tenant-specific
        required=False,
        widget=forms.Select(attrs={'class': 'form-select form-select-sm'}),
        empty_label=_("All Categories"),
        label=_("Category")
    )
    payment_method = forms.ModelChoiceField(
        queryset=PaymentMethod.objects.filter(is_active=True), # Scope in __init__
        required=False,
        widget=forms.Select(attrs={'class': 'form-select form-select-sm'}),
        empty_label=_("All Payment Methods"),
        label=_("Payment Method")
    )
    description_contains = forms.CharField(
        label=_("Description Contains"),
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control form-control-sm', 'placeholder': 'e.g., stationary, repairs'})
    )

    def __init__(self, *args, **kwargs):
        self.tenant = kwargs.pop('tenant', None)
        self.request = kwargs.pop('request', None) # May not be needed here
        super().__init__(*args, **kwargs)

        # Example: Scope ExpenseCategory queryset if it's tenant-specific
        # (e.g., if ExpenseCategory has a ForeignKey to School or is schema-scoped)
        # if self.tenant and hasattr(ExpenseCategory, 'tenant_fk_field'):
        #     self.fields['category'].queryset = ExpenseCategory.objects.filter(tenant_fk_field=self.tenant).order_by('name')
        # elif self.tenant: # If schema-scoped (no direct FK, but objects are in tenant schema)
        # This queryset runs in the current tenant's schema context anyway.
        self.fields['category'].queryset = ExpenseCategory.objects.all().order_by('name')
        self.fields['payment_method'].queryset = PaymentMethod.objects.filter(is_active=True).order_by('name')


    def clean(self):
        cleaned_data = super().clean()
        start_date = cleaned_data.get("start_date")
        end_date = cleaned_data.get("end_date")

        if start_date and end_date and end_date < start_date:
            self.add_error('end_date', _("End date cannot be before start date."))
        return cleaned_data



# your_project/apps/reporting/forms.py

from django import forms
from django.utils.translation import gettext_lazy as _

# Assuming your SchoolClass model is in the 'schools' app
# Adjust the import path if your model is located elsewhere
from apps.schools.models import SchoolClass

class OutstandingFeesFilterForm(forms.Form):
    """
    A form for filtering the Outstanding Fees Report.
    """
    school_class = forms.ModelChoiceField(
        label=_("Filter by Class"),
        queryset=SchoolClass.objects.filter(is_active=True).order_by('name'),
        required=False,
        empty_label=_("--- All Classes ---"),
        widget=forms.Select(attrs={'class': 'form-select'})
    )

    min_due_amount = forms.DecimalField(
        label=_("Minimum Amount Due"),
        required=False,
        min_value=0,
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'placeholder': 'e.g., 50.00'
        })
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # You can add more dynamic adjustments here if needed
        # For example, dynamically setting the queryset based on the tenant/user
    
    

# your_project/apps/reporting/forms.py

from django import forms
from django.utils.translation import gettext_lazy as _

# Adjust these import paths to match your project structure
from apps.schools.models import SchoolClass
from apps.finance.models import PaymentMethod

class CollectionReportFilterForm(forms.Form):
    """
    A form for filtering the Fee Collection Report.
    """
    start_date = forms.DateField(
        label=_("Start Date"),
        required=False,
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control'})
    )
    
    end_date = forms.DateField(
        label=_("End Date"),
        required=False,
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control'})
    )
    
    student_query = forms.CharField(
        label=_("Student Name or Adm. No"),
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Search student...'
        })
    )
    
    school_class = forms.ModelChoiceField(
        label=_("Filter by Class"),
        queryset=SchoolClass.objects.filter(is_active=True).order_by('name'),
        required=False,
        empty_label=_("--- All Classes ---"),
        widget=forms.Select(attrs={'class': 'form-select'})
    )
   
    payment_method = forms.ModelChoiceField(
        label=_("Payment Method"),
        queryset=PaymentMethod.objects.filter(is_active=True).order_by('name'),
        required=False,
        empty_label=_("--- All Methods ---"),
        widget=forms.Select(attrs={'class': 'form-select'})
    )

    def clean(self):
        cleaned_data = super().clean()
        start_date = cleaned_data.get('start_date')
        end_date = cleaned_data.get('end_date')

        if start_date and end_date and start_date > end_date:
            raise forms.ValidationError(_("The start date cannot be after the end date."))
            
        return cleaned_data



# your_project/apps/reporting/forms.py

from django import forms
from django.utils.translation import gettext_lazy as _

# ... other form classes ...

class FeeProjectionFilterForm(forms.Form):
    """
    A placeholder form for the Fee Projection Report.
    This report currently does not use any filters, but this form
    can be extended in the future if needed (e.g., to select an
    academic year).
    """
    pass



# your_project/apps/reporting/forms.py

from django import forms
from django.utils.translation import gettext_lazy as _
from django.utils import timezone

# ... (other form imports) ...
from apps.finance.models import PaymentMethod
from apps.schools.models import SchoolClass

# ... (other form classes) ...

class PaymentReportFilter(forms.Form):
    """
    A form for filtering the Payment Summary Report.
    """
    start_date = forms.DateField(
        label=_("Start Date"),
        required=False,
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control'})
    )
    
    end_date = forms.DateField(
        label=_("End Date"),
        required=False,
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control'})
    )

    payment_method = forms.ModelChoiceField(
        label=_("Payment Method"),
        queryset=PaymentMethod.objects.filter(is_active=True).order_by('name'),
        required=False,
        empty_label=_("--- All Methods ---"),
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    
    school_class = forms.ModelChoiceField(
        label=_("Class"),
        queryset=SchoolClass.objects.filter(is_active=True).order_by('name'),
        required=False,
        empty_label=_("--- All Classes ---"),
        widget=forms.Select(attrs={'class': 'form-select'})
    )

    def clean(self):
        cleaned_data = super().clean()
        start_date = cleaned_data.get('start_date')
        end_date = cleaned_data.get('end_date')

        if start_date and end_date and start_date > end_date:
            raise forms.ValidationError(_("The start date cannot be after the end date."))
            
        return cleaned_data



# ========================================
# GENERAL LEDGER REPORT FILTER FORM
# ========================================
class GeneralLedgerFilterForm(BaseReportFilterForm):
    """
    Filter form for General Ledger Report.
    Allows filtering by account, date range, and entry status.
    """
    account = forms.ModelChoiceField(
        queryset=ChartOfAccount.objects.none(),  # Will be populated in __init__
        required=False,
        empty_label=_("All Accounts"),
        label=_("Account"),
        help_text=_("Select specific account or leave blank for all accounts")
    )

    start_date = forms.DateField(
        required=False,
        label=_("Start Date"),
        widget=forms.DateInput(attrs={'type': 'date'}),
        help_text=_("Leave blank for beginning of current financial year")
    )

    end_date = forms.DateField(
        required=False,
        label=_("End Date"),
        widget=forms.DateInput(attrs={'type': 'date'}),
        help_text=_("Leave blank for today's date")
    )

    entry_status = forms.ChoiceField(
        choices=[
            ('', _('All Entries')),
            ('POSTED', _('Posted Only')),
            ('DRAFT', _('Draft Only')),
        ],
        required=False,
        label=_("Entry Status"),
        initial='POSTED',
        help_text=_("Filter by journal entry status")
    )

    account_type = forms.ModelChoiceField(
        queryset=AccountType.objects.none(),  # Will be populated in __init__
        required=False,
        empty_label=_("All Account Types"),
        label=_("Account Type"),
        help_text=_("Filter accounts by type")
    )

    include_zero_balance = forms.BooleanField(
        required=False,
        initial=True,
        label=_("Include Zero Balance Accounts"),
        help_text=_("Show accounts with zero balance in the period")
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Populate account choices
        self.fields['account'].queryset = ChartOfAccount.objects.filter(
            is_active=True
        ).select_related('account_type').order_by('account_type__name', 'name')

        # Populate account type choices
        self.fields['account_type'].queryset = AccountType.objects.all().order_by('classification', 'name')

        # Set default dates
        if not self.data:
            today = timezone.now().date()
            # Default to current month
            start_of_month = today.replace(day=1)
            self.fields['start_date'].initial = start_of_month
            self.fields['end_date'].initial = today


# ========================================
# ACCOUNTS RECEIVABLE AGING FILTER FORM
# ========================================
class AccountsReceivableAgingFilterForm(BaseReportFilterForm):
    """
    Filter form for Accounts Receivable Aging Report.
    """
    as_of_date = forms.DateField(
        required=False,
        label=_("As of Date"),
        widget=forms.DateInput(attrs={'type': 'date'}),
        help_text=_("Date for aging calculation (default: today)")
    )

    class_filter = forms.ModelChoiceField(
        queryset=SchoolClass.objects.none(),
        required=False,
        empty_label=_("All Classes"),
        label=_("Class"),
        help_text=_("Filter by student class")
    )

    academic_year = forms.ModelChoiceField(
        queryset=AcademicYear.objects.none(),
        required=False,
        empty_label=_("All Academic Years"),
        label=_("Academic Year"),
        help_text=_("Filter by academic year")
    )

    include_zero_balance = forms.BooleanField(
        required=False,
        initial=False,
        label=_("Include Zero Balance Students"),
        help_text=_("Show students with no outstanding balance")
    )

    aging_periods = forms.ChoiceField(
        choices=[
            ('30_60_90', _('30-60-90 Days')),
            ('30_60_90_120', _('30-60-90-120 Days')),
            ('custom', _('Custom Periods')),
        ],
        required=False,
        initial='30_60_90',
        label=_("Aging Periods"),
        help_text=_("Select aging period breakdown")
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Populate class choices
        self.fields['class_filter'].queryset = SchoolClass.objects.all().order_by('name')

        # Populate academic year choices
        self.fields['academic_year'].queryset = AcademicYear.objects.all().order_by('-start_date')

        # Set default date
        if not self.data:
            self.fields['as_of_date'].initial = timezone.now().date()


# ========================================
# ACCOUNTS PAYABLE FILTER FORM
# ========================================
class AccountsPayableFilterForm(BaseReportFilterForm):
    """
    Filter form for Accounts Payable Report.
    """
    as_of_date = forms.DateField(
        required=False,
        label=_("As of Date"),
        widget=forms.DateInput(attrs={'type': 'date'}),
        help_text=_("Date for payable calculation (default: today)")
    )

    vendor = forms.CharField(
        required=False,
        label=_("Vendor"),
        max_length=100,
        help_text=_("Filter by vendor name (partial match)")
    )

    due_date_from = forms.DateField(
        required=False,
        label=_("Due Date From"),
        widget=forms.DateInput(attrs={'type': 'date'}),
        help_text=_("Show payables due from this date")
    )

    due_date_to = forms.DateField(
        required=False,
        label=_("Due Date To"),
        widget=forms.DateInput(attrs={'type': 'date'}),
        help_text=_("Show payables due until this date")
    )

    overdue_only = forms.BooleanField(
        required=False,
        initial=False,
        label=_("Overdue Only"),
        help_text=_("Show only overdue payables")
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Set default date
        if not self.data:
            self.fields['as_of_date'].initial = timezone.now().date()


# ========================================
# BANK RECONCILIATION FILTER FORM
# ========================================
class BankReconciliationFilterForm(BaseReportFilterForm):
    """
    Filter form for Bank Reconciliation Report.
    """
    bank_account = forms.ModelChoiceField(
        queryset=ChartOfAccount.objects.none(),  # Will be populated in __init__
        required=True,
        label=_("Bank Account"),
        help_text=_("Select the bank account to reconcile")
    )

    statement_date = forms.DateField(
        required=True,
        label=_("Bank Statement Date"),
        widget=forms.DateInput(attrs={'type': 'date'}),
        help_text=_("Date of the bank statement")
    )

    statement_balance = forms.DecimalField(
        required=True,
        label=_("Bank Statement Balance"),
        max_digits=12,
        decimal_places=2,
        help_text=_("Ending balance from bank statement")
    )

    reconciliation_date = forms.DateField(
        required=False,
        label=_("Reconciliation Date"),
        widget=forms.DateInput(attrs={'type': 'date'}),
        help_text=_("Date of reconciliation (default: today)")
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Populate bank account choices (cash equivalent accounts)
        self.fields['bank_account'].queryset = ChartOfAccount.objects.filter(
            is_cash_equivalent=True,
            is_active=True
        ).order_by('name')

        # Set default dates
        if not self.data:
            today = timezone.now().date()
            self.fields['statement_date'].initial = today
            self.fields['reconciliation_date'].initial = today


# ========================================
# BUDGET VS ACTUAL FILTER FORM
# ========================================
class BudgetVsActualFilterForm(BaseReportFilterForm):
    """
    Filter form for Budget vs Actual Report.
    """
    academic_year = forms.ModelChoiceField(
        queryset=AcademicYear.objects.none(),
        required=False,
        empty_label=_("All Academic Years"),
        label=_("Academic Year"),
        help_text=_("Select academic year for budget comparison")
    )

    term = forms.ModelChoiceField(
        queryset=Term.objects.none(),
        required=False,
        empty_label=_("All Terms"),
        label=_("Term"),
        help_text=_("Select specific term or leave blank for full year")
    )

    budget_item_type = forms.ChoiceField(
        choices=[
            ('', _('All Types')),
            ('INCOME', _('Income Only')),
            ('EXPENSE', _('Expense Only')),
        ],
        required=False,
        label=_("Budget Item Type"),
        help_text=_("Filter by income or expense items")
    )

    variance_threshold = forms.DecimalField(
        required=False,
        label=_("Variance Threshold (%)"),
        max_digits=5,
        decimal_places=2,
        initial=10,
        help_text=_("Show only items with variance greater than this percentage")
    )

    show_zero_budget = forms.BooleanField(
        required=False,
        initial=True,
        label=_("Include Zero Budget Items"),
        help_text=_("Show budget items with zero budgeted amounts")
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Populate academic year choices
        self.fields['academic_year'].queryset = AcademicYear.objects.all().order_by('-start_date')

        # Populate term choices
        self.fields['term'].queryset = Term.objects.all().order_by('academic_year__start_date', 'start_date')

        # Set default to current academic year
        if not self.data:
            try:
                current_year = AcademicYear.objects.filter(
                    start_date__lte=timezone.now().date(),
                    end_date__gte=timezone.now().date()
                ).first()
                if current_year:
                    self.fields['academic_year'].initial = current_year
            except:
                pass


# ========================================
# FEE COLLECTION ANALYSIS FILTER FORM
# ========================================
class FeeCollectionAnalysisFilterForm(BaseReportFilterForm):
    """
    Filter form for Fee Collection Analysis Report.
    """
    start_date = forms.DateField(
        required=False,
        label=_("Start Date"),
        widget=forms.DateInput(attrs={'type': 'date'}),
        help_text=_("Analysis period start date")
    )

    end_date = forms.DateField(
        required=False,
        label=_("End Date"),
        widget=forms.DateInput(attrs={'type': 'date'}),
        help_text=_("Analysis period end date")
    )

    academic_year = forms.ModelChoiceField(
        queryset=AcademicYear.objects.none(),
        required=False,
        empty_label=_("All Academic Years"),
        label=_("Academic Year"),
        help_text=_("Filter by academic year")
    )

    payment_method = forms.ModelChoiceField(
        queryset=PaymentMethod.objects.none(),
        required=False,
        empty_label=_("All Payment Methods"),
        label=_("Payment Method"),
        help_text=_("Analyze specific payment method")
    )

    class_filter = forms.ModelChoiceField(
        queryset=SchoolClass.objects.none(),
        required=False,
        empty_label=_("All Classes"),
        label=_("Class"),
        help_text=_("Filter by student class")
    )

    analysis_type = forms.ChoiceField(
        choices=[
            ('summary', _('Summary Analysis')),
            ('detailed', _('Detailed Analysis')),
            ('trends', _('Trend Analysis')),
        ],
        required=False,
        initial='summary',
        label=_("Analysis Type"),
        help_text=_("Type of analysis to perform")
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Populate choices
        self.fields['academic_year'].queryset = AcademicYear.objects.all().order_by('-start_date')
        self.fields['payment_method'].queryset = PaymentMethod.objects.filter(is_active=True).order_by('name')
        self.fields['class_filter'].queryset = SchoolClass.objects.all().order_by('name')

        # Set default dates
        if not self.data:
            today = timezone.now().date()
            start_of_month = today.replace(day=1)
            self.fields['start_date'].initial = start_of_month
            self.fields['end_date'].initial = today


# ========================================
# STUDENT ACCOUNT STATEMENT FILTER FORM
# ========================================
class StudentAccountStatementFilterForm(BaseReportFilterForm):
    """
    Filter form for Student Account Statement Report.
    """
    student = forms.ModelChoiceField(
        queryset=Student.objects.none(),
        required=True,
        label=_("Student"),
        help_text=_("Select student for account statement")
    )

    start_date = forms.DateField(
        required=False,
        label=_("Start Date"),
        widget=forms.DateInput(attrs={'type': 'date'}),
        help_text=_("Statement period start date")
    )

    end_date = forms.DateField(
        required=False,
        label=_("End Date"),
        widget=forms.DateInput(attrs={'type': 'date'}),
        help_text=_("Statement period end date")
    )

    academic_year = forms.ModelChoiceField(
        queryset=AcademicYear.objects.none(),
        required=False,
        empty_label=_("All Academic Years"),
        label=_("Academic Year"),
        help_text=_("Filter by academic year")
    )

    include_paid = forms.BooleanField(
        required=False,
        initial=True,
        label=_("Include Paid Items"),
        help_text=_("Show paid invoices and payments")
    )

    include_pending = forms.BooleanField(
        required=False,
        initial=True,
        label=_("Include Pending Items"),
        help_text=_("Show unpaid/pending invoices")
    )

    statement_type = forms.ChoiceField(
        choices=[
            ('detailed', _('Detailed Statement')),
            ('summary', _('Summary Statement')),
            ('outstanding_only', _('Outstanding Only')),
        ],
        required=False,
        initial='detailed',
        label=_("Statement Type"),
        help_text=_("Type of statement to generate")
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Populate student choices
        self.fields['student'].queryset = Student.objects.select_related('current_class').order_by('first_name', 'last_name')

        # Populate academic year choices
        self.fields['academic_year'].queryset = AcademicYear.objects.all().order_by('-start_date')

        # Set default dates
        if not self.data:
            today = timezone.now().date()
            # Default to current academic year start or 6 months ago
            try:
                current_year = AcademicYear.objects.filter(
                    start_date__lte=today,
                    end_date__gte=today
                ).first()
                if current_year:
                    self.fields['start_date'].initial = current_year.start_date
                    self.fields['academic_year'].initial = current_year
                else:
                    # Fallback to 6 months ago
                    start_date = today.replace(month=today.month-6 if today.month > 6 else today.month+6,
                                             year=today.year if today.month > 6 else today.year-1)
                    self.fields['start_date'].initial = start_date
            except:
                # Fallback to 6 months ago
                start_date = today.replace(day=1)
                if start_date.month > 6:
                    start_date = start_date.replace(month=start_date.month-6)
                else:
                    start_date = start_date.replace(month=start_date.month+6, year=start_date.year-1)
                self.fields['start_date'].initial = start_date

            self.fields['end_date'].initial = today


# ========================================
# CLASS-WISE FEE COLLECTION FILTER FORM
# ========================================
class ClasswiseFeeCollectionFilterForm(BaseReportFilterForm):
    """
    Filter form for Class-wise Fee Collection Report.
    """
    academic_year = forms.ModelChoiceField(
        queryset=AcademicYear.objects.none(),
        required=False,
        empty_label=_("All Academic Years"),
        label=_("Academic Year"),
        help_text=_("Filter by academic year")
    )

    term = forms.ModelChoiceField(
        queryset=Term.objects.none(),
        required=False,
        empty_label=_("All Terms"),
        label=_("Term"),
        help_text=_("Filter by specific term")
    )

    class_filter = forms.ModelChoiceField(
        queryset=SchoolClass.objects.none(),
        required=False,
        empty_label=_("All Classes"),
        label=_("Class"),
        help_text=_("Filter by specific class")
    )

    fee_type = forms.ModelChoiceField(
        queryset=FeeType.objects.none(),
        required=False,
        empty_label=_("All Fee Types"),
        label=_("Fee Type"),
        help_text=_("Filter by fee type")
    )

    collection_status = forms.ChoiceField(
        choices=[
            ('', _('All Statuses')),
            ('paid', _('Fully Paid')),
            ('partial', _('Partially Paid')),
            ('unpaid', _('Unpaid')),
            ('overdue', _('Overdue')),
        ],
        required=False,
        label=_("Collection Status"),
        help_text=_("Filter by payment status")
    )

    as_of_date = forms.DateField(
        required=False,
        label=_("As of Date"),
        widget=forms.DateInput(attrs={'type': 'date'}),
        help_text=_("Report as of specific date")
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Populate choices
        self.fields['academic_year'].queryset = AcademicYear.objects.all().order_by('-start_date')
        self.fields['term'].queryset = Term.objects.all().order_by('academic_year__start_date', 'start_date')
        self.fields['class_filter'].queryset = SchoolClass.objects.all().order_by('name')
        self.fields['fee_type'].queryset = FeeType.objects.filter(is_active=True).order_by('name')

        # Set defaults
        if not self.data:
            today = timezone.now().date()
            self.fields['as_of_date'].initial = today

            # Set current academic year
            try:
                current_year = AcademicYear.objects.filter(
                    start_date__lte=today,
                    end_date__gte=today
                ).first()
                if current_year:
                    self.fields['academic_year'].initial = current_year
            except:
                pass


# ========================================
# FEE DEFAULTERS FILTER FORM
# ========================================
class FeeDefaultersFilterForm(BaseReportFilterForm):
    """
    Filter form for Fee Defaulters Report.
    """
    academic_year = forms.ModelChoiceField(
        queryset=AcademicYear.objects.none(),
        required=False,
        empty_label=_("All Academic Years"),
        label=_("Academic Year"),
        help_text=_("Filter by academic year")
    )

    class_filter = forms.ModelChoiceField(
        queryset=SchoolClass.objects.none(),
        required=False,
        empty_label=_("All Classes"),
        label=_("Class"),
        help_text=_("Filter by student class")
    )

    overdue_days = forms.ChoiceField(
        choices=[
            ('', _('All Overdue')),
            ('30', _('30+ Days Overdue')),
            ('60', _('60+ Days Overdue')),
            ('90', _('90+ Days Overdue')),
            ('120', _('120+ Days Overdue')),
        ],
        required=False,
        label=_("Overdue Period"),
        help_text=_("Filter by number of days overdue")
    )

    minimum_amount = forms.DecimalField(
        required=False,
        label=_("Minimum Outstanding Amount"),
        max_digits=10,
        decimal_places=2,
        help_text=_("Show only defaulters with outstanding amount above this value")
    )

    defaulter_status = forms.ChoiceField(
        choices=[
            ('', _('All Defaulters')),
            ('active', _('Active Students Only')),
            ('inactive', _('Inactive Students Only')),
            ('critical', _('Critical Cases (90+ days)')),
        ],
        required=False,
        label=_("Defaulter Status"),
        help_text=_("Filter by defaulter status")
    )

    as_of_date = forms.DateField(
        required=False,
        label=_("As of Date"),
        widget=forms.DateInput(attrs={'type': 'date'}),
        help_text=_("Calculate overdue amounts as of this date")
    )

    include_contact_info = forms.BooleanField(
        required=False,
        initial=True,
        label=_("Include Contact Information"),
        help_text=_("Show parent/guardian contact details")
    )

    sort_by = forms.ChoiceField(
        choices=[
            ('amount_desc', _('Outstanding Amount (High to Low)')),
            ('amount_asc', _('Outstanding Amount (Low to High)')),
            ('days_desc', _('Days Overdue (Most to Least)')),
            ('days_asc', _('Days Overdue (Least to Most)')),
            ('student_name', _('Student Name (A-Z)')),
            ('class_name', _('Class Name')),
        ],
        required=False,
        initial='amount_desc',
        label=_("Sort By"),
        help_text=_("Sort defaulters by selected criteria")
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Populate choices
        self.fields['academic_year'].queryset = AcademicYear.objects.all().order_by('-start_date')
        self.fields['class_filter'].queryset = SchoolClass.objects.all().order_by('name')

        # Set defaults
        if not self.data:
            today = timezone.now().date()
            self.fields['as_of_date'].initial = today

            # Set current academic year
            try:
                current_year = AcademicYear.objects.filter(
                    start_date__lte=today,
                    end_date__gte=today
                ).first()
                if current_year:
                    self.fields['academic_year'].initial = current_year
            except:
                pass


# ========================================
# CASH FLOW FORECASTING FILTER FORM
# ========================================
class CashFlowForecastingFilterForm(BaseReportFilterForm):
    """
    Filter form for Cash Flow Forecasting Report.
    """
    forecast_period = forms.ChoiceField(
        choices=[
            ('3_months', _('3 Months')),
            ('6_months', _('6 Months')),
            ('12_months', _('12 Months')),
            ('24_months', _('24 Months')),
        ],
        required=False,
        initial='12_months',
        label=_("Forecast Period"),
        help_text=_("Period for cash flow forecasting")
    )

    historical_period = forms.ChoiceField(
        choices=[
            ('6_months', _('6 Months Historical Data')),
            ('12_months', _('12 Months Historical Data')),
            ('24_months', _('24 Months Historical Data')),
            ('36_months', _('36 Months Historical Data')),
        ],
        required=False,
        initial='12_months',
        label=_("Historical Data Period"),
        help_text=_("Historical period to base forecasts on")
    )

    forecast_method = forms.ChoiceField(
        choices=[
            ('trend_analysis', _('Trend Analysis')),
            ('seasonal_adjustment', _('Seasonal Adjustment')),
            ('enrollment_based', _('Enrollment-Based')),
            ('conservative', _('Conservative Estimate')),
        ],
        required=False,
        initial='trend_analysis',
        label=_("Forecasting Method"),
        help_text=_("Method to use for cash flow forecasting")
    )

    include_receivables = forms.BooleanField(
        required=False,
        initial=True,
        label=_("Include Outstanding Receivables"),
        help_text=_("Include expected collections from outstanding invoices")
    )

    include_seasonal = forms.BooleanField(
        required=False,
        initial=True,
        label=_("Apply Seasonal Adjustments"),
        help_text=_("Apply seasonal patterns to forecasts")
    )

    confidence_level = forms.ChoiceField(
        choices=[
            ('conservative', _('Conservative (80%)')),
            ('moderate', _('Moderate (90%)')),
            ('optimistic', _('Optimistic (95%)')),
        ],
        required=False,
        initial='moderate',
        label=_("Confidence Level"),
        help_text=_("Confidence level for forecasting")
    )


# ========================================
# PROFITABILITY ANALYSIS FILTER FORM
# ========================================
class ProfitabilityAnalysisFilterForm(BaseReportFilterForm):
    """
    Filter form for Profitability Analysis Report.
    """
    academic_year = forms.ModelChoiceField(
        queryset=AcademicYear.objects.none(),
        required=False,
        empty_label=_("All Academic Years"),
        label=_("Academic Year"),
        help_text=_("Filter by academic year")
    )

    analysis_type = forms.ChoiceField(
        choices=[
            ('class', _('By Class')),
            ('fee_type', _('By Fee Type')),
            ('program', _('By Program')),
            ('monthly', _('Monthly Analysis')),
            ('comparative', _('Comparative Analysis')),
        ],
        required=False,
        initial='class',
        label=_("Analysis Type"),
        help_text=_("Type of profitability analysis")
    )

    cost_allocation_method = forms.ChoiceField(
        choices=[
            ('direct', _('Direct Costs Only')),
            ('proportional', _('Proportional Allocation')),
            ('activity_based', _('Activity-Based Costing')),
        ],
        required=False,
        initial='proportional',
        label=_("Cost Allocation Method"),
        help_text=_("Method for allocating costs")
    )

    include_indirect_costs = forms.BooleanField(
        required=False,
        initial=True,
        label=_("Include Indirect Costs"),
        help_text=_("Include administrative and overhead costs")
    )

    profitability_threshold = forms.DecimalField(
        required=False,
        label=_("Profitability Threshold (%)"),
        max_digits=5,
        decimal_places=2,
        initial=15,
        help_text=_("Minimum profitability percentage for highlighting")
    )


# ========================================
# FINANCIAL RATIO ANALYSIS FILTER FORM
# ========================================
class FinancialRatioAnalysisFilterForm(BaseReportFilterForm):
    """
    Filter form for Financial Ratio Analysis Report.
    """
    analysis_period = forms.ChoiceField(
        choices=[
            ('current_year', _('Current Academic Year')),
            ('last_year', _('Previous Academic Year')),
            ('quarterly', _('Quarterly Analysis')),
            ('comparative', _('Multi-Year Comparison')),
        ],
        required=False,
        initial='current_year',
        label=_("Analysis Period"),
        help_text=_("Period for ratio analysis")
    )

    ratio_categories = forms.MultipleChoiceField(
        choices=[
            ('liquidity', _('Liquidity Ratios')),
            ('profitability', _('Profitability Ratios')),
            ('efficiency', _('Efficiency Ratios')),
            ('leverage', _('Leverage Ratios')),
            ('growth', _('Growth Ratios')),
        ],
        required=False,
        initial=['liquidity', 'profitability', 'efficiency'],
        widget=forms.CheckboxSelectMultiple,
        label=_("Ratio Categories"),
        help_text=_("Select ratio categories to analyze")
    )

    benchmark_comparison = forms.BooleanField(
        required=False,
        initial=True,
        label=_("Include Benchmark Comparison"),
        help_text=_("Compare ratios against industry benchmarks")
    )

    trend_analysis = forms.BooleanField(
        required=False,
        initial=True,
        label=_("Include Trend Analysis"),
        help_text=_("Show ratio trends over time")
    )

    risk_assessment = forms.BooleanField(
        required=False,
        initial=True,
        label=_("Include Risk Assessment"),
        help_text=_("Provide risk level assessment based on ratios")
    )


# ========================================
# REVENUE RECOGNITION FILTER FORM
# ========================================
class RevenueRecognitionFilterForm(BaseReportFilterForm):
    """
    Filter form for Revenue Recognition Report.
    """
    academic_year = forms.ModelChoiceField(
        queryset=AcademicYear.objects.none(),
        required=False,
        empty_label=_("All Academic Years"),
        label=_("Academic Year"),
        help_text=_("Filter by academic year")
    )

    recognition_method = forms.ChoiceField(
        choices=[
            ('accrual', _('Accrual Basis')),
            ('cash', _('Cash Basis')),
            ('deferred', _('Deferred Revenue')),
            ('earned', _('Earned Revenue')),
        ],
        required=False,
        initial='accrual',
        label=_("Recognition Method"),
        help_text=_("Revenue recognition method")
    )

    revenue_type = forms.ChoiceField(
        choices=[
            ('', _('All Revenue Types')),
            ('tuition', _('Tuition Fees')),
            ('registration', _('Registration Fees')),
            ('examination', _('Examination Fees')),
            ('other', _('Other Fees')),
        ],
        required=False,
        label=_("Revenue Type"),
        help_text=_("Filter by revenue type")
    )

    recognition_status = forms.ChoiceField(
        choices=[
            ('', _('All Statuses')),
            ('recognized', _('Fully Recognized')),
            ('deferred', _('Deferred')),
            ('partial', _('Partially Recognized')),
        ],
        required=False,
        label=_("Recognition Status"),
        help_text=_("Filter by recognition status")
    )

    include_adjustments = forms.BooleanField(
        required=False,
        initial=True,
        label=_("Include Adjustments"),
        help_text=_("Include revenue adjustments and corrections")
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Populate academic year choices
        self.fields['academic_year'].queryset = AcademicYear.objects.all().order_by('-start_date')

        # Set current academic year as default
        if not self.data:
            today = timezone.now().date()
            try:
                current_year = AcademicYear.objects.filter(
                    start_date__lte=today,
                    end_date__gte=today
                ).first()
                if current_year:
                    self.fields['academic_year'].initial = current_year
            except:
                pass


# ========================================
# JOURNAL ENTRY REGISTER FILTER FORM
# ========================================
class JournalEntryRegisterFilterForm(BaseReportFilterForm):
    """
    Filter form for Journal Entry Register Report.
    """
    start_date = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
        label=_("Start Date"),
        help_text=_("Filter entries from this date")
    )

    end_date = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
        label=_("End Date"),
        help_text=_("Filter entries up to this date")
    )

    entry_status = forms.ChoiceField(
        choices=[
            ('', _('All Statuses')),
            ('DRAFT', _('Draft')),
            ('POSTED', _('Posted')),
            ('REVERSED', _('Reversed')),
        ],
        required=False,
        label=_("Entry Status"),
        help_text=_("Filter by journal entry status")
    )

    entry_type = forms.ChoiceField(
        choices=[
            ('', _('All Types')),
            ('MANUAL', _('Manual Entry')),
            ('AUTOMATIC', _('Automatic Entry')),
            ('ADJUSTMENT', _('Adjustment Entry')),
            ('CLOSING', _('Closing Entry')),
        ],
        required=False,
        label=_("Entry Type"),
        help_text=_("Filter by entry type")
    )

    include_reversals = forms.BooleanField(
        required=False,
        initial=True,
        label=_("Include Reversals"),
        help_text=_("Include reversed entries in the report")
    )

    show_details = forms.BooleanField(
        required=False,
        initial=True,
        label=_("Show Line Details"),
        help_text=_("Show individual journal line details")
    )


# ========================================
# PERIOD CLOSING FILTER FORM
# ========================================
class PeriodClosingFilterForm(BaseReportFilterForm):
    """
    Filter form for Period Closing Report.
    """
    closing_period = forms.ChoiceField(
        choices=[
            ('monthly', _('Monthly Closing')),
            ('quarterly', _('Quarterly Closing')),
            ('yearly', _('Year-end Closing')),
        ],
        required=False,
        initial='monthly',
        label=_("Closing Period"),
        help_text=_("Type of period closing")
    )

    period_year = forms.IntegerField(
        required=False,
        label=_("Year"),
        help_text=_("Year for period closing"),
        widget=forms.NumberInput(attrs={'min': 2020, 'max': 2030})
    )

    period_month = forms.ChoiceField(
        choices=[
            ('', _('All Months')),
            ('1', _('January')), ('2', _('February')), ('3', _('March')),
            ('4', _('April')), ('5', _('May')), ('6', _('June')),
            ('7', _('July')), ('8', _('August')), ('9', _('September')),
            ('10', _('October')), ('11', _('November')), ('12', _('December')),
        ],
        required=False,
        label=_("Month"),
        help_text=_("Month for period closing")
    )

    validation_level = forms.ChoiceField(
        choices=[
            ('basic', _('Basic Validation')),
            ('standard', _('Standard Validation')),
            ('comprehensive', _('Comprehensive Validation')),
        ],
        required=False,
        initial='standard',
        label=_("Validation Level"),
        help_text=_("Level of validation checks to perform")
    )

    include_reconciliation = forms.BooleanField(
        required=False,
        initial=True,
        label=_("Include Reconciliation"),
        help_text=_("Include account reconciliation in closing")
    )


# ========================================
# AUDIT TRAIL FILTER FORM
# ========================================
class AuditTrailFilterForm(BaseReportFilterForm):
    """
    Filter form for Audit Trail Report.
    """
    start_date = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
        label=_("Start Date"),
        help_text=_("Filter audit trail from this date")
    )

    end_date = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
        label=_("End Date"),
        help_text=_("Filter audit trail up to this date")
    )

    transaction_type = forms.ChoiceField(
        choices=[
            ('', _('All Transaction Types')),
            ('invoice', _('Invoice Transactions')),
            ('payment', _('Payment Transactions')),
            ('journal', _('Journal Entries')),
            ('adjustment', _('Adjustments')),
        ],
        required=False,
        label=_("Transaction Type"),
        help_text=_("Filter by transaction type")
    )

    user_filter = forms.CharField(
        required=False,
        max_length=100,
        label=_("User Filter"),
        help_text=_("Filter by username or user ID")
    )

    action_type = forms.ChoiceField(
        choices=[
            ('', _('All Actions')),
            ('CREATE', _('Create')),
            ('UPDATE', _('Update')),
            ('DELETE', _('Delete')),
            ('POST', _('Post')),
            ('REVERSE', _('Reverse')),
        ],
        required=False,
        label=_("Action Type"),
        help_text=_("Filter by action performed")
    )

    include_system_actions = forms.BooleanField(
        required=False,
        initial=False,
        label=_("Include System Actions"),
        help_text=_("Include automated system actions in audit trail")
    )

    show_details = forms.BooleanField(
        required=False,
        initial=True,
        label=_("Show Change Details"),
        help_text=_("Show detailed field changes in audit trail")
    )


# ========================================
# TAX COMPLIANCE FILTER FORM
# ========================================
class TaxComplianceFilterForm(BaseReportFilterForm):
    """
    Filter form for Tax Compliance Report.
    """
    tax_year = forms.IntegerField(
        required=False,
        label=_("Tax Year"),
        help_text=_("Year for tax reporting"),
        widget=forms.NumberInput(attrs={'min': 2020, 'max': 2030})
    )

    tax_period = forms.ChoiceField(
        choices=[
            ('annual', _('Annual')),
            ('quarterly', _('Quarterly')),
            ('monthly', _('Monthly')),
        ],
        required=False,
        initial='annual',
        label=_("Tax Period"),
        help_text=_("Tax reporting period")
    )

    tax_type = forms.ChoiceField(
        choices=[
            ('', _('All Tax Types')),
            ('income', _('Income Tax')),
            ('vat', _('VAT/Sales Tax')),
            ('payroll', _('Payroll Tax')),
            ('property', _('Property Tax')),
        ],
        required=False,
        label=_("Tax Type"),
        help_text=_("Type of tax for reporting")
    )

    include_exemptions = forms.BooleanField(
        required=False,
        initial=True,
        label=_("Include Tax Exemptions"),
        help_text=_("Include tax-exempt transactions in report")
    )

    compliance_level = forms.ChoiceField(
        choices=[
            ('basic', _('Basic Compliance')),
            ('standard', _('Standard Compliance')),
            ('comprehensive', _('Comprehensive Compliance')),
        ],
        required=False,
        initial='standard',
        label=_("Compliance Level"),
        help_text=_("Level of tax compliance reporting")
    )


# ========================================
# PAYMENT METHOD ANALYSIS FILTER FORM
# ========================================
class PaymentMethodAnalysisFilterForm(BaseReportFilterForm):
    """
    Filter form for Payment Method Analysis Report.
    """
    start_date = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
        label=_("Start Date"),
        help_text=_("Filter payments from this date")
    )

    end_date = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
        label=_("End Date"),
        help_text=_("Filter payments up to this date")
    )

    payment_method = forms.ChoiceField(
        choices=[
            ('', _('All Payment Methods')),
            ('CASH', _('Cash')),
            ('BANK_TRANSFER', _('Bank Transfer')),
            ('CREDIT_CARD', _('Credit Card')),
            ('DEBIT_CARD', _('Debit Card')),
            ('MOBILE_MONEY', _('Mobile Money')),
            ('CHECK', _('Check')),
            ('ONLINE', _('Online Payment')),
        ],
        required=False,
        label=_("Payment Method"),
        help_text=_("Filter by payment method")
    )

    analysis_type = forms.ChoiceField(
        choices=[
            ('summary', _('Summary Analysis')),
            ('trends', _('Trend Analysis')),
            ('efficiency', _('Efficiency Analysis')),
            ('comparative', _('Comparative Analysis')),
        ],
        required=False,
        initial='summary',
        label=_("Analysis Type"),
        help_text=_("Type of analysis to perform")
    )

    include_failed = forms.BooleanField(
        required=False,
        initial=False,
        label=_("Include Failed Payments"),
        help_text=_("Include failed/cancelled payments in analysis")
    )

    group_by_period = forms.ChoiceField(
        choices=[
            ('daily', _('Daily')),
            ('weekly', _('Weekly')),
            ('monthly', _('Monthly')),
            ('quarterly', _('Quarterly')),
        ],
        required=False,
        initial='monthly',
        label=_("Group by Period"),
        help_text=_("Time period for trend analysis")
    )


# ========================================
# REFUND ANALYSIS FILTER FORM
# ========================================
class RefundAnalysisFilterForm(BaseReportFilterForm):
    """
    Filter form for Refund Analysis Report.
    """
    start_date = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
        label=_("Start Date"),
        help_text=_("Filter refunds from this date")
    )

    end_date = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
        label=_("End Date"),
        help_text=_("Filter refunds up to this date")
    )

    refund_reason = forms.ChoiceField(
        choices=[
            ('', _('All Reasons')),
            ('OVERPAYMENT', _('Overpayment')),
            ('WITHDRAWAL', _('Student Withdrawal')),
            ('DUPLICATE', _('Duplicate Payment')),
            ('ERROR', _('Payment Error')),
            ('POLICY', _('Policy Change')),
            ('OTHER', _('Other')),
        ],
        required=False,
        label=_("Refund Reason"),
        help_text=_("Filter by refund reason")
    )

    refund_status = forms.ChoiceField(
        choices=[
            ('', _('All Statuses')),
            ('PENDING', _('Pending')),
            ('APPROVED', _('Approved')),
            ('PROCESSED', _('Processed')),
            ('REJECTED', _('Rejected')),
        ],
        required=False,
        label=_("Refund Status"),
        help_text=_("Filter by refund status")
    )

    analysis_focus = forms.ChoiceField(
        choices=[
            ('overview', _('Overview Analysis')),
            ('reasons', _('Reason Analysis')),
            ('trends', _('Trend Analysis')),
            ('impact', _('Financial Impact')),
        ],
        required=False,
        initial='overview',
        label=_("Analysis Focus"),
        help_text=_("Focus area for refund analysis")
    )


# ========================================
# COLLECTION PERFORMANCE FILTER FORM
# ========================================
class CollectionPerformanceFilterForm(BaseReportFilterForm):
    """
    Filter form for Collection Performance Report.
    """
    start_date = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
        label=_("Start Date"),
        help_text=_("Filter collection activities from this date")
    )

    end_date = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
        label=_("End Date"),
        help_text=_("Filter collection activities up to this date")
    )

    collection_status = forms.ChoiceField(
        choices=[
            ('', _('All Statuses')),
            ('PENDING', _('Pending Collection')),
            ('IN_PROGRESS', _('In Progress')),
            ('COLLECTED', _('Successfully Collected')),
            ('FAILED', _('Collection Failed')),
            ('WRITTEN_OFF', _('Written Off')),
        ],
        required=False,
        label=_("Collection Status"),
        help_text=_("Filter by collection status")
    )

    overdue_period = forms.ChoiceField(
        choices=[
            ('', _('All Periods')),
            ('0-30', _('0-30 Days Overdue')),
            ('31-60', _('31-60 Days Overdue')),
            ('61-90', _('61-90 Days Overdue')),
            ('90+', _('90+ Days Overdue')),
        ],
        required=False,
        label=_("Overdue Period"),
        help_text=_("Filter by overdue period")
    )

    performance_metric = forms.ChoiceField(
        choices=[
            ('efficiency', _('Collection Efficiency')),
            ('success_rate', _('Success Rate Analysis')),
            ('aging', _('Aging Analysis')),
            ('trends', _('Trend Analysis')),
        ],
        required=False,
        initial='efficiency',
        label=_("Performance Metric"),
        help_text=_("Focus metric for analysis")
    )


# ========================================
# FEE STRUCTURE ANALYSIS FILTER FORM
# ========================================
class FeeStructureAnalysisFilterForm(BaseReportFilterForm):
    """
    Filter form for Fee Structure Analysis Report.
    """
    academic_year = forms.CharField(
        required=False,
        max_length=20,
        label=_("Academic Year"),
        help_text=_("Filter by academic year (e.g., 2023-2024)")
    )

    fee_category = forms.ChoiceField(
        choices=[
            ('', _('All Categories')),
            ('TUITION', _('Tuition Fees')),
            ('LIBRARY', _('Library Fees')),
            ('LABORATORY', _('Laboratory Fees')),
            ('SPORTS', _('Sports Fees')),
            ('TRANSPORT', _('Transport Fees')),
            ('EXAMINATION', _('Examination Fees')),
            ('OTHER', _('Other Fees')),
        ],
        required=False,
        label=_("Fee Category"),
        help_text=_("Filter by fee category")
    )

    analysis_focus = forms.ChoiceField(
        choices=[
            ('pricing', _('Pricing Analysis')),
            ('revenue', _('Revenue Analysis')),
            ('trends', _('Trend Analysis')),
            ('optimization', _('Optimization Analysis')),
        ],
        required=False,
        initial='pricing',
        label=_("Analysis Focus"),
        help_text=_("Focus area for fee structure analysis")
    )

    comparison_period = forms.ChoiceField(
        choices=[
            ('year_over_year', _('Year over Year')),
            ('semester', _('Semester Comparison')),
            ('monthly', _('Monthly Comparison')),
        ],
        required=False,
        initial='year_over_year',
        label=_("Comparison Period"),
        help_text=_("Time period for comparison analysis")
    )


# ========================================
# STUDENT ENROLLMENT IMPACT FILTER FORM
# ========================================
class StudentEnrollmentImpactFilterForm(BaseReportFilterForm):
    """
    Filter form for Student Enrollment Impact Report.
    """
    start_date = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
        label=_("Start Date"),
        help_text=_("Filter enrollment data from this date")
    )

    end_date = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
        label=_("End Date"),
        help_text=_("Filter enrollment data up to this date")
    )

    enrollment_type = forms.ChoiceField(
        choices=[
            ('', _('All Enrollment Types')),
            ('NEW', _('New Enrollments')),
            ('RETURNING', _('Returning Students')),
            ('TRANSFER', _('Transfer Students')),
            ('WITHDRAWAL', _('Withdrawals')),
        ],
        required=False,
        label=_("Enrollment Type"),
        help_text=_("Filter by enrollment type")
    )

    impact_analysis = forms.ChoiceField(
        choices=[
            ('financial', _('Financial Impact')),
            ('trends', _('Enrollment Trends')),
            ('correlation', _('Fee Collection Correlation')),
            ('forecasting', _('Enrollment Forecasting')),
        ],
        required=False,
        initial='financial',
        label=_("Impact Analysis"),
        help_text=_("Type of impact analysis to perform")
    )

    group_by_period = forms.ChoiceField(
        choices=[
            ('monthly', _('Monthly')),
            ('quarterly', _('Quarterly')),
            ('semester', _('Semester')),
            ('annual', _('Annual')),
        ],
        required=False,
        initial='monthly',
        label=_("Group by Period"),
        help_text=_("Time period for grouping analysis")
    )


# ========================================
# ACCOUNTS RECEIVABLE AGING FILTER FORM
# ========================================
class AccountsReceivableAgingFilterForm(BaseReportFilterForm):
    """
    Filter form for Accounts Receivable Aging Report.
    """
    as_of_date = forms.DateField(
        required=False,
        label=_("As of Date"),
        widget=forms.DateInput(attrs={'type': 'date'}),
        help_text=_("Date for aging calculation (default: today)")
    )

    class_filter = forms.ModelChoiceField(
        queryset=SchoolClass.objects.none(),
        required=False,
        empty_label=_("All Classes"),
        label=_("Class"),
        help_text=_("Filter by student class")
    )

    academic_year = forms.ModelChoiceField(
        queryset=AcademicYear.objects.none(),
        required=False,
        empty_label=_("All Academic Years"),
        label=_("Academic Year"),
        help_text=_("Filter by academic year")
    )

    include_zero_balance = forms.BooleanField(
        required=False,
        initial=False,
        label=_("Include Zero Balance Students"),
        help_text=_("Show students with no outstanding balance")
    )

    aging_periods = forms.ChoiceField(
        choices=[
            ('30_60_90', _('30-60-90 Days')),
            ('30_60_90_120', _('30-60-90-120 Days')),
            ('custom', _('Custom Periods')),
        ],
        required=False,
        initial='30_60_90',
        label=_("Aging Periods"),
        help_text=_("Select aging period breakdown")
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Populate class choices
        self.fields['class_filter'].queryset = SchoolClass.objects.all().order_by('name')

        # Populate academic year choices
        self.fields['academic_year'].queryset = AcademicYear.objects.all().order_by('-start_date')

        # Set default date
        if not self.data:
            self.fields['as_of_date'].initial = timezone.now().date()


# ========================================
# ACCOUNTS PAYABLE FILTER FORM
# ========================================
class AccountsPayableFilterForm(BaseReportFilterForm):
    """
    Filter form for Accounts Payable Report.
    """
    as_of_date = forms.DateField(
        required=False,
        label=_("As of Date"),
        widget=forms.DateInput(attrs={'type': 'date'}),
        help_text=_("Date for payable calculation (default: today)")
    )

    vendor = forms.CharField(
        required=False,
        label=_("Vendor"),
        max_length=100,
        help_text=_("Filter by vendor name (partial match)")
    )

    due_date_from = forms.DateField(
        required=False,
        label=_("Due Date From"),
        widget=forms.DateInput(attrs={'type': 'date'}),
        help_text=_("Show payables due from this date")
    )

    due_date_to = forms.DateField(
        required=False,
        label=_("Due Date To"),
        widget=forms.DateInput(attrs={'type': 'date'}),
        help_text=_("Show payables due until this date")
    )

    overdue_only = forms.BooleanField(
        required=False,
        initial=False,
        label=_("Overdue Only"),
        help_text=_("Show only overdue payables")
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Set default date
        if not self.data:
            self.fields['as_of_date'].initial = timezone.now().date()




















