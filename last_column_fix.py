#!/usr/bin/env python
"""
Last Column Fix
Adds the final missing columns for complete report functionality

Usage: python last_column_fix.py
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from django.db import connection
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def fix_last_missing_columns():
    """Fix the last remaining missing columns"""
    schema_name = 'mandiva'
    
    logger.info(f"=== LAST COLUMN FIX FOR {schema_name} ===")
    
    try:
        with connection.cursor() as cursor:
            # Set search path to mandiva schema
            cursor.execute(f'SET search_path TO "{schema_name}"')
            
            # 1. Fix accounting_account table - add parent_id
            logger.info("1. Adding accounting_account.parent_id...")
            cursor.execute("ALTER TABLE accounting_account ADD COLUMN IF NOT EXISTS parent_id BIGINT")
            
            # Set up parent-child relationships for accounts
            cursor.execute("""
                UPDATE accounting_account 
                SET parent_id = CASE 
                    WHEN code = '1020' THEN (SELECT id FROM accounting_account WHERE code = '1010' LIMIT 1)
                    WHEN code = '1200' THEN (SELECT id FROM accounting_account WHERE code = '1010' LIMIT 1)
                    ELSE NULL
                END
                WHERE parent_id IS NULL
            """)
            
            # 2. Fix students_student table - add middle_name
            logger.info("2. Adding students_student.middle_name...")
            cursor.execute("ALTER TABLE students_student ADD COLUMN IF NOT EXISTS middle_name VARCHAR(150)")
            
            # Set default middle names for existing students
            cursor.execute("UPDATE students_student SET middle_name = '' WHERE middle_name IS NULL")
            
            # 3. Add other commonly needed student fields
            logger.info("3. Adding additional student fields...")
            cursor.execute("ALTER TABLE students_student ADD COLUMN IF NOT EXISTS last_name VARCHAR(150)")
            cursor.execute("ALTER TABLE students_student ADD COLUMN IF NOT EXISTS date_of_birth DATE")
            cursor.execute("ALTER TABLE students_student ADD COLUMN IF NOT EXISTS gender VARCHAR(10)")
            cursor.execute("ALTER TABLE students_student ADD COLUMN IF NOT EXISTS phone VARCHAR(20)")
            cursor.execute("ALTER TABLE students_student ADD COLUMN IF NOT EXISTS email VARCHAR(254)")
            cursor.execute("ALTER TABLE students_student ADD COLUMN IF NOT EXISTS address TEXT")
            cursor.execute("ALTER TABLE students_student ADD COLUMN IF NOT EXISTS guardian_name VARCHAR(200)")
            cursor.execute("ALTER TABLE students_student ADD COLUMN IF NOT EXISTS guardian_phone VARCHAR(20)")
            cursor.execute("ALTER TABLE students_student ADD COLUMN IF NOT EXISTS guardian_email VARCHAR(254)")
            cursor.execute("ALTER TABLE students_student ADD COLUMN IF NOT EXISTS enrollment_date DATE")
            cursor.execute("ALTER TABLE students_student ADD COLUMN IF NOT EXISTS status VARCHAR(20) DEFAULT 'ACTIVE'")
            
            # Update existing students with default values
            cursor.execute("""
                UPDATE students_student 
                SET 
                    last_name = COALESCE(last_name, 'Student'),
                    date_of_birth = COALESCE(date_of_birth, '2010-01-01'),
                    gender = COALESCE(gender, 'M'),
                    phone = COALESCE(phone, ''),
                    email = COALESCE(email, ''),
                    address = COALESCE(address, ''),
                    guardian_name = COALESCE(guardian_name, 'Guardian'),
                    guardian_phone = COALESCE(guardian_phone, ''),
                    guardian_email = COALESCE(guardian_email, ''),
                    enrollment_date = COALESCE(enrollment_date, CURRENT_DATE),
                    status = COALESCE(status, 'ACTIVE')
                WHERE id IS NOT NULL
            """)
            
            # 4. Add additional accounting fields for comprehensive reporting
            logger.info("4. Adding additional accounting fields...")
            cursor.execute("ALTER TABLE accounting_account ADD COLUMN IF NOT EXISTS account_level INTEGER DEFAULT 1")
            cursor.execute("ALTER TABLE accounting_account ADD COLUMN IF NOT EXISTS is_header BOOLEAN DEFAULT FALSE")
            cursor.execute("ALTER TABLE accounting_account ADD COLUMN IF NOT EXISTS sort_order INTEGER DEFAULT 0")
            
            # Update account levels and hierarchy
            cursor.execute("""
                UPDATE accounting_account 
                SET 
                    account_level = CASE 
                        WHEN parent_id IS NULL THEN 1
                        ELSE 2
                    END,
                    is_header = CASE 
                        WHEN parent_id IS NULL THEN TRUE
                        ELSE FALSE
                    END,
                    sort_order = CASE 
                        WHEN account_type = 'ASSET' THEN 1000
                        WHEN account_type = 'LIABILITY' THEN 2000
                        WHEN account_type = 'EQUITY' THEN 3000
                        WHEN account_type = 'INCOME' THEN 4000
                        WHEN account_type = 'EXPENSE' THEN 5000
                        ELSE 9000
                    END + id
                WHERE account_level IS NULL OR sort_order = 0
            """)
            
            # 5. Create sample students if none exist
            logger.info("5. Creating sample students if needed...")
            cursor.execute("SELECT COUNT(*) FROM students_student")
            student_count = cursor.fetchone()[0]
            
            if student_count == 0:
                logger.info("No students found, creating sample students...")
                cursor.execute("""
                    INSERT INTO students_student (
                        first_name, middle_name, last_name, admission_number, 
                        date_of_birth, gender, class_id, current_class_id, 
                        section_id, current_section_id, guardian_name, 
                        enrollment_date, status
                    ) VALUES 
                    ('John', 'Michael', 'Doe', 'ADM000001', '2010-05-15', 'M', 1, 1, 1, 1, 'Mr. John Doe Sr.', CURRENT_DATE, 'ACTIVE'),
                    ('Jane', 'Elizabeth', 'Smith', 'ADM000002', '2010-08-22', 'F', 1, 1, 1, 1, 'Mrs. Mary Smith', CURRENT_DATE, 'ACTIVE'),
                    ('Michael', 'James', 'Johnson', 'ADM000003', '2009-12-10', 'M', 2, 2, 2, 2, 'Mr. Robert Johnson', CURRENT_DATE, 'ACTIVE'),
                    ('Sarah', 'Anne', 'Williams', 'ADM000004', '2009-03-18', 'F', 2, 2, 2, 2, 'Mrs. Linda Williams', CURRENT_DATE, 'ACTIVE'),
                    ('David', 'Christopher', 'Brown', 'ADM000005', '2008-11-05', 'M', 3, 3, 3, 3, 'Mr. David Brown Sr.', CURRENT_DATE, 'ACTIVE')
                """)
                logger.info("✅ Created 5 sample students")
            else:
                logger.info(f"✅ Found {student_count} existing students")
            
            # 6. Create performance indexes for new columns
            logger.info("6. Creating performance indexes...")
            indexes = [
                "CREATE INDEX IF NOT EXISTS accounting_account_parent_id_idx ON accounting_account (parent_id);",
                "CREATE INDEX IF NOT EXISTS students_student_middle_name_idx ON students_student (middle_name);",
                "CREATE INDEX IF NOT EXISTS students_student_last_name_idx ON students_student (last_name);",
                "CREATE INDEX IF NOT EXISTS students_student_status_idx ON students_student (status);",
                "CREATE INDEX IF NOT EXISTS students_student_enrollment_date_idx ON students_student (enrollment_date);",
                "CREATE INDEX IF NOT EXISTS accounting_account_sort_order_idx ON accounting_account (sort_order);",
            ]
            
            for index_sql in indexes:
                try:
                    cursor.execute(index_sql)
                except Exception as e:
                    logger.warning(f"Index creation issue: {e}")
            
            logger.info("✅ All last missing columns added successfully!")
            
    except Exception as e:
        logger.error(f"Failed to add last missing columns: {e}")
        raise

def test_final_columns():
    """Test the final columns that were causing issues"""
    schema_name = 'mandiva'
    
    logger.info("=== TESTING FINAL COLUMNS ===")
    
    test_queries = [
        ("accounting_account.parent_id", "SELECT parent_id FROM accounting_account LIMIT 1;"),
        ("students_student.middle_name", "SELECT middle_name FROM students_student LIMIT 1;"),
        ("students_student.last_name", "SELECT last_name FROM students_student LIMIT 1;"),
        ("students_student.guardian_name", "SELECT guardian_name FROM students_student LIMIT 1;"),
        ("accounting_account.account_level", "SELECT account_level FROM accounting_account LIMIT 1;"),
        ("students count", "SELECT COUNT(*) FROM students_student;"),
    ]
    
    try:
        with connection.cursor() as cursor:
            cursor.execute(f'SET search_path TO "{schema_name}"')
            
            for test_name, query in test_queries:
                try:
                    cursor.execute(query)
                    result = cursor.fetchone()
                    logger.info(f"✅ {test_name}: query successful (result: {result})")
                except Exception as e:
                    logger.error(f"❌ {test_name}: query failed: {e}")
                    
    except Exception as e:
        logger.error(f"Final column testing failed: {e}")

def main():
    """Main function"""
    logger.info("=== LAST COLUMN FIX ===")
    
    try:
        # Fix last missing columns
        fix_last_missing_columns()
        
        # Test final columns
        test_final_columns()
        
        logger.info("\n🎉 ALL MISSING COLUMNS FINALLY FIXED!")
        logger.info("All reports should now work without any column errors.")
        logger.info("\nFinal fixes applied:")
        logger.info("- accounting_account.parent_id (for Balance Sheet, General Ledger, Bank Reconciliation)")
        logger.info("- students_student.middle_name (for all student reports)")
        logger.info("- students_student.last_name (for complete student names)")
        logger.info("- Complete student profile fields")
        logger.info("- Account hierarchy and sorting")
        logger.info("- Sample student data")
        logger.info("\nNow test these reports:")
        logger.info("- Balance Sheet Report")
        logger.info("- General Ledger Report")
        logger.info("- Bank Reconciliation Report")
        logger.info("- Student Account Statement")
        logger.info("- Student Ledger Report")
        logger.info("- Outstanding Fees Report")
        logger.info("- Payment Summary Report")
        
        return True
        
    except Exception as e:
        logger.error(f"Last column fix failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
