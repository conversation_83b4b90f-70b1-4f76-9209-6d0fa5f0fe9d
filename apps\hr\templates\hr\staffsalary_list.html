{% extends "tenant_base.html" %}
{% load static i18n humanize %}

{% block tenant_page_title %}{{ view_title }}{% endblock %}

{% block tenant_specific_content %}
<div class="container-fluid mt-4">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h1 class="h3 mb-0">{{ view_title }}</h1>
        {% if unassigned_staff_count > 0 and perms.hr.add_staffsalary %}
        <div>
            <a href="{% url 'hr:staffsalary_create' %}" class="btn btn-primary">
                <i class="bi bi-plus-circle-fill me-1"></i> Assign Salary to Staff
            </a>
        </div>
        {% endif %}
    </div>
    {% include "partials/_messages.html" %}
    <div class="card shadow-sm">
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>Staff Member</th>
                            <th>Salary Grade</th>
                            <th class="text-end">Basic Salary</th>
                            <th>Effective From</th>
                            <th class="text-center">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for record in salary_records %}
                        <tr>
                            <td>{{ record.staff_member.get_full_name }}</td>
                            <td>{{ record.grade.name|default:"-" }}</td>
                            <td class="text-end">{{ school_profile.currency_symbol|default:'$' }}{{ record.basic_salary|intcomma }}</td>
                            <td>{{ record.effective_from|date:"d M, Y" }}</td>
                            <td class="text-center">
                                <a href="{% url 'hr:staffsalary_update' pk=record.pk %}" class="btn btn-sm btn-outline-primary">
                                    <i class="bi bi-pencil-square"></i> Edit
                                </a>
                            </td>
                        </tr>
                        {% empty %}
                        <tr><td colspan="5" class="text-center p-4">No staff salary records found.</td></tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}




