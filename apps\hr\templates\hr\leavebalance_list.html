{# D:\school_fees_saas_v2\templates\hr\leave_balance_list.html #}
{% extends "tenant_base.html" %}
{% load humanize core_tags %}

{% block title %}{{ view_title|default:"Staff Leave Balances" }}{% endblock %}

{% block tenant_specific_content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h1>{{ view_title|default:"All Staff Leave Balances" }}</h1>
        {# Add button to manually adjust/create balance if needed later #}
        {# {% if perms.hr.add_leavebalance %} #}
        {# <a href="#" class="btn btn-primary btn-sm">Adjust Balance</a> #}
        {# {% endif %} #}
    </div>

    {% include "partials/_messages.html" %}

    {% if leave_balances %}
        <div class="card shadow-sm">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover table-sm">
                        <thead class="table-light">
                            <tr>
                                <th>Employee</th>
                                <th>Leave Type</th>
                                <th class="text-end">Accrued</th>
                                <th class="text-end">Taken</th>
                                <th class="text-end">Available</th>
                                <th>Last Updated</th>
                                {# <th>Actions</th> #}
                            </tr>
                        </thead>
                        <tbody>
                            {% for balance in leave_balances %}
                            <tr>
                                <td>{{ balance.employee.user.get_full_name|default:balance.employee.user.email }}</td>
                                <td>{{ balance.leave_type.name }}</td>
                                <td class="text-end">{{ balance.days_accrued|floatformat:1|default_if_none:"0" }}</td>
                                <td class="text-end">{{ balance.days_taken|floatformat:1|default_if_none:"0" }}</td>
                                <td class="text-end fw-bold">{{ balance.days_available|floatformat:1|default_if_none:"0" }}</td>
                                <td>{{ balance.updated_at|date:"d M Y" }}</td>
                                {# <td><a href="#" class="btn btn-sm btn-secondary">Edit</a></td> #}
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% include "partials/_pagination.html" %}
            </div>
        </div>
    {% else %}
        <div class="alert alert-info">No leave balances found.</div>
    {% endif %}

    <div class="mt-3">
        <a href="{% url 'schools:dashboard' %}" class="btn btn-outline-secondary">Back to Dashboard</a>
    </div>
</div>
{% endblock %}









{% comment %} {# D:\school_fees_saas_v2\apps\hr\templates\hr\leave_balance_list.html #}
{% extends "tenant_base.html" %}
{% load core_tags humanize %}

{% block title %}{{ view_title|default:"Staff Leave Balances" }}{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h1>{{ view_title|default:"Staff Leave Balances" }}</h1>
        {# Add link to manually adjust/create balances if that view is built #}
        {# <a href="#" class="btn btn-primary">Adjust Balance</a> #}
    </div>

    {% include "partials/_messages.html" %}

    {% if balances %}
        <div class="card shadow-sm">
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-striped table-hover table-sm mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>Employee</th>
                                <th>Leave Type</th>
                                <th class="text-end">Accrued</th>
                                <th class="text-end">Taken</th>
                                <th class="text-end">Remaining</th>
                                <th>Period/Year Info</th>
                                {# <th>Actions</th> #}
                            </tr>
                        </thead>
                        <tbody>
                            {% for balance in balances %}
                            <tr>
                                <td>{{ balance.employee.user.full_name|default:balance.employee.user.email }}</td>
                                <td>{{ balance.leave_type.name }}</td>
                                <td class="text-end">{{ balance.days_accrued|floatformat:1|default:"0.0" }}</td>
                                <td class="text-end">{{ balance.days_taken|floatformat:1|default:"0.0" }}</td>
                                <td class="text-end"><strong>{{ balance.days_remaining|floatformat:1|default:"0.0" }}</strong></td>
                                <td>{{ balance.year_or_period_info|default:"-" }}</td>
                                {# <td class="actions-column">
                                    <a href="#" class="btn btn-sm btn-outline-primary" title="Edit Balance">Edit</a>
                                </td> #}
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    {% else %}
        <div class="alert alert-info">No leave balances found.</div>
    {% endif %}

    {% include "partials/_pagination.html" %}

    <div class="footer-actions mt-3">
        <a href="{% url 'schools:dashboard' %}" class="btn btn-secondary">
            <i class="bi bi-arrow-left-circle me-1"></i> Back to Dashboard
        </a>
    </div>
</div>
{% endblock %} {% endcomment %}

