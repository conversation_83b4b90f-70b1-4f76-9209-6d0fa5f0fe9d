{# D:\school_fees_saas_v2\apps\tenants\templates\tenants\confirm_and_pay.html #}
{% extends "public_base.html" %}
{% load static i18n humanize %}

{% block public_page_title %}Confirm Subscription{% endblock %}

{% block content %}
<div class="container my-5">
    <div class="row justify-content-center">
        <div class="col-lg-7">
            <div class="card shadow-sm">
                <div class="card-header text-center">
                    <h2 class="h3">{% trans "Confirm Your Plan" %}</h2>
                </div>
                <div class="card-body p-4 p-md-5">
                    <p class="lead">{% trans "You're almost there! Please confirm your details before proceeding to payment." %}</p>
                    
                    <div class="alert alert-light border">
                        <h5 class="alert-heading">{{ school.name }}</h5>
                        <hr>
                        <p class="mb-1"><strong>{% trans "Plan" %}:</strong> {{ plan.name }}</p>
                        <p class="mb-1"><strong>{% trans "Price" %}:</strong> ${{ plan.price_monthly|floatformat:2|intcomma }} / {% trans "month" %}</p>
                        <p class="mb-0"><strong>{% trans "Student Limit" %}:</strong> {{ plan.max_students }}</p>
                    </div>

                    <p class="text-muted small">{% trans "You will be redirected to our secure payment partner to complete your subscription. Your school account will be activated immediately upon successful payment." %}</p>

                    {# This form POSTs to the view that initiates the payment with the gateway #}
                    <form method="post" action="{% url 'subscriptions:initiate_subscription_payment' subscription.pk %}">
                        {% csrf_token %}
                        <div class="d-grid">
                            <button type="submit" class="btn btn-success btn-lg">
                                <i class="bi bi-shield-check-fill me-2"></i>{% trans "Proceed to Secure Payment" %}
                            </button>
                        </div>
                    </form>
                    
                </div>
            </div>
        </div>
    </div>
</div>
</div>
{% endblock content %}


