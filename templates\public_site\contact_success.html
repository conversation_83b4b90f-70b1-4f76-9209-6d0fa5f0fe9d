{# templates/public_site/contact_success.html #}
{% extends "public_base.html" %}
{% load static %}

{% block title %}{{ view_title }} - School Fees Management Platform{% endblock %}

{% block extra_public_css %}
<style>
    .success-hero {
        background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
        color: white;
        padding: 5rem 0;
        text-align: center;
        position: relative;
        overflow: hidden;
    }

    .success-hero::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        opacity: 0.3;
    }

    .success-icon {
        width: 100px;
        height: 100px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 2rem;
        font-size: 3rem;
        position: relative;
        z-index: 1;
        backdrop-filter: blur(10px);
    }

    .success-hero h1 {
        font-size: 3rem;
        font-weight: 700;
        margin-bottom: 1rem;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        position: relative;
        z-index: 1;
    }

    .success-hero p {
        font-size: 1.2rem;
        opacity: 0.9;
        margin-bottom: 2rem;
        position: relative;
        z-index: 1;
        max-width: 600px;
        margin-left: auto;
        margin-right: auto;
    }

    .success-content {
        padding: 4rem 0;
        background: #f8fafc;
    }

    .success-card {
        background: white;
        border-radius: 20px;
        padding: 3rem;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        text-align: center;
        max-width: 600px;
        margin: 0 auto;
    }

    .success-card h2 {
        color: #2d3748;
        margin-bottom: 1.5rem;
    }

    .success-card p {
        color: #718096;
        line-height: 1.7;
        margin-bottom: 2rem;
    }

    .next-steps {
        background: #f7fafc;
        border-radius: 12px;
        padding: 2rem;
        margin: 2rem 0;
        text-align: left;
    }

    .next-steps h3 {
        color: #2d3748;
        margin-bottom: 1rem;
        text-align: center;
    }

    .next-steps ul {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .next-steps li {
        display: flex;
        align-items: center;
        margin-bottom: 1rem;
        color: #4a5568;
    }

    .next-steps li::before {
        content: '✓';
        color: #48bb78;
        font-weight: bold;
        margin-right: 1rem;
        font-size: 1.2rem;
    }

    .btn-primary-gradient {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        color: white;
        padding: 0.875rem 2rem;
        border-radius: 10px;
        font-weight: 600;
        text-decoration: none;
        display: inline-block;
        transition: all 0.3s ease;
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        margin: 0 0.5rem;
    }

    .btn-primary-gradient:hover {
        background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        color: white;
        text-decoration: none;
    }

    .btn-outline-gradient {
        background: transparent;
        border: 2px solid #667eea;
        color: #667eea;
        padding: 0.875rem 2rem;
        border-radius: 10px;
        font-weight: 600;
        text-decoration: none;
        display: inline-block;
        transition: all 0.3s ease;
        margin: 0 0.5rem;
    }

    .btn-outline-gradient:hover {
        background: #667eea;
        color: white;
        text-decoration: none;
        transform: translateY(-2px);
    }

    @media (max-width: 768px) {
        .success-hero h1 {
            font-size: 2.5rem;
        }

        .success-hero {
            padding: 3rem 0;
        }

        .success-content {
            padding: 3rem 0;
        }

        .success-card {
            padding: 2rem;
        }

        .btn-primary-gradient,
        .btn-outline-gradient {
            display: block;
            margin: 0.5rem 0;
            text-align: center;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Success Hero Section -->
<section class="success-hero">
    <div class="container">
        <div class="success-icon">
            <i class="bi bi-check-circle"></i>
        </div>
        <h1>Message Sent Successfully!</h1>
        <p>Thank you for reaching out to us. We've received your message and will respond within 24-48 hours.</p>
    </div>
</section>

<!-- Success Content -->
<section class="success-content">
    <div class="container">
        <div class="success-card">
            <h2>What Happens Next?</h2>
            <p>Your message has been delivered to our team and we're excited to help you with your school's fee management needs.</p>

            <div class="next-steps">
                <h3>Next Steps</h3>
                <ul>
                    <li>Our team will review your inquiry within 2 hours</li>
                    <li>You'll receive a personalized response within 24-48 hours</li>
                    <li>For urgent matters, we'll prioritize your request</li>
                    <li>Demo requests will be scheduled within 1 business day</li>
                    <li>You'll receive a confirmation email shortly</li>
                </ul>
            </div>

            <p>In the meantime, feel free to explore our platform features or check out what other schools are saying about us.</p>

            <div>
                <a href="{% url 'public_site:home' %}" class="btn-primary-gradient">
                    <i class="bi bi-house me-2"></i>
                    Return Home
                </a>
                <a href="{% url 'public_site:features' %}" class="btn-outline-gradient">
                    <i class="bi bi-star me-2"></i>
                    Explore Features
                </a>
            </div>
        </div>
    </div>
</section>
{% endblock %}

