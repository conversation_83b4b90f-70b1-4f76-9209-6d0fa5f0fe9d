# D:\school_fees_saas_v2\apps\public_site\views.py

from django.shortcuts import render, redirect, get_object_or_404
from django.views.generic import TemplateView, ListView, FormView
from django.urls import reverse
from django.db.models import Q
from django.contrib import messages
from django.conf import settings # For TENANT_BASE_DOMAIN

from .forms import SchoolFinderForm
from apps.tenants.models import School # Import the School model

from django.urls import reverse_lazy
from django.contrib import messages
from django.core.mail import send_mail
from django.conf import settings
import logging # For logging contact form errors

from django.contrib.messages.views import SuccessMessageMixin

from apps.subscriptions.models import SubscriptionPlan, Feature
from .forms import ContactForm, TestimonialSubmissionForm
from .models import Testimonial, ContactInquiry # If you created ContactInquiry

logger = logging.getLogger(__name__) # Setup logger


def home_view(request):
    finder_form = SchoolFinderForm() # For display on homepage
    recent_schools = School.objects.filter(is_active=True).order_by('-created_on')[:5]
    context = {
        'finder_form': finder_form,
        'recent_schools': recent_schools,
        'view_title': "Welcome to School Fees Platform"
    }
    return render(request, 'public_site/home.html', context)


def find_school_view(request):
    query = request.GET.get('query', '').strip()
    schools_found = None
    exact_match = None
    view_title = "Find Your School"
    finder_form = SchoolFinderForm(request.GET or None) # Populate with submitted data or empty

    if query: # Only search if query is provided
        view_title = f"Search Results for '{query}'"
        try:
            # Try exact slug match first
            exact_match = School.objects.get(slug__iexact=query, is_active=True)
        except School.DoesNotExist:
            pass # Continue to broader search
        except School.MultipleObjectsReturned:
            messages.error(request, "Data error: Multiple schools match this identifier. Please contact support.")
            # Fall through to broader search which might list them, or redirect home
            schools_found = School.objects.filter(slug__iexact=query, is_active=True).order_by('name')


        if exact_match:
            return redirect('public_site:school_profile', school_slug=exact_match.slug)
        else:
            # Broader search by name or domain part if no exact slug match
            schools_found = School.objects.filter(
                Q(name__icontains=query) |
                Q(domains__domain__istartswith=query + '.') # Matches "subdomain." part
            ).filter(is_active=True).distinct().order_by('name')

            if schools_found.count() == 1:
                return redirect('public_site:school_profile', school_slug=schools_found.first().slug)
            elif not schools_found:
                messages.error(request, f"No active school found matching '{query}'. You can try a different name or register a new school.")
                # Redirect back to home with query to prefill form
                return redirect(reverse('public_site:home') + f'?query={query}')
            # If multiple results, we'll display them (handled below)
    else:
        # No query submitted, perhaps direct access to /find-school/
        # Just show the form again (or redirect to home)
        # For this example, we'll show the home template which includes the finder
        pass # Let it fall through to render home with empty/prefilled form

    # If query was empty, or multiple results and we want to display them on home
    # Or if no results and we want to show home with the error message
    recent_schools = School.objects.filter(is_active=True).order_by('-created_on')[:5]
    context = {
        'finder_form': finder_form, # Pass the form with query data or empty
        'recent_schools': recent_schools,
        'schools_found_list': schools_found, # Pass the list of found schools
        'query': query, # Pass the original query back
        'view_title': view_title if query else "Find Your School" # Adjust title
    }
    # Render a specific search results page or reuse home page
    # If reusing home, home.html needs to be able to display 'schools_found_list'
    return render(request, 'public_site/home.html', context) # Or 'public_site/school_search_results.html'



def school_public_profile_view(request, school_slug):
    school = get_object_or_404(School, slug=school_slug, is_active=True)
    
    primary_domain_obj = school.domains.filter(is_primary=True).first()
    if not primary_domain_obj:
        primary_domain_obj = school.domains.first() # Fallback

    tenant_action_base_url = None # Initialize
    
    if primary_domain_obj:
        scheme = request.scheme # http or https
        domain_hostname_from_db = primary_domain_obj.domain # e.g., 'zenith.myapp.test' or 'www.customschool.com'
        
        constructed_domain = domain_hostname_from_db # Start with the domain from DB

        if settings.DEBUG and hasattr(settings, 'TENANT_BASE_DOMAIN') and ':' in settings.TENANT_BASE_DOMAIN:
            # Extract base hostname and port from settings for comparison
            base_hostname_from_settings = settings.TENANT_BASE_DOMAIN.split(':')[0] # e.g., 'myapp.test'
            port_from_settings = settings.TENANT_BASE_DOMAIN.split(':')[-1]          # e.g., '8000'

            # Only add the dev server port if the domain from DB is either:
            # 1. Exactly the same as the base hostname from settings (e.g., myapp.test for a public site on tenant domain)
            # OR
            # 2. A subdomain of the base hostname from settings (e.g., zenith.myapp.test is a subdomain of myapp.test)
            # AND
            # 3. The domain from DB doesn't already include a port.
            if (domain_hostname_from_db == base_hostname_from_settings or \
                domain_hostname_from_db.endswith(f".{base_hostname_from_settings}")):
                
                if ':' not in domain_hostname_from_db: # Check if port is not already in domain_hostname_from_db
                    constructed_domain = f"{domain_hostname_from_db}:{port_from_settings}"
            # If it's a custom domain not matching the base, we assume it's correctly configured
            # and don't append the dev port.
        
        tenant_action_base_url = f"{scheme}://{constructed_domain}"

    context = {
        'school': school,
        'tenant_action_base_url': tenant_action_base_url, # e.g., http://zenith.myapp.test:8000 or http://www.customschool.com
        'view_title': f"{school.name} - School Portal",
    }
    return render(request, 'public_site/school_profile.html', context)

class HomePageView(TemplateView):
    template_name = "public_site/home.html"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # You can set a specific title, or let the template default work
        # context['view_title'] = "Welcome to Our Amazing Platform!" 
        try:
            context['testimonials'] = Testimonial.objects.filter(is_approved=True).order_by('?')[:3]
        except: # Catch errors if Testimonial model or app isn't ready
            context['testimonials'] = None
        return context
# class HomePageView(TemplateView):
#     template_name = "public_site/home.html" # You already have this view as 'home'

#     def get_context_data(self, **kwargs):
#         context = super().get_context_data(**kwargs)
#         context['view_title'] = "Welcome"
#         # Fetch a few approved testimonials for the homepage
#         context['testimonials'] = Testimonial.objects.filter(is_approved=True).order_by('?')[:3] # Random 3
#         return context

class FeaturesPageView(TemplateView):
    template_name = "public_site/features.html"
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = "Our Features"
        # You could fetch Feature model instances here if you want to list them dynamically
        # context['all_features'] = Feature.objects.all().order_by('name')
        return context



# D:\school_fees_saas_v2\apps\public_site\views.py
from django.views.generic import TemplateView
from apps.subscriptions.models import SubscriptionPlan

class PricingPageView(TemplateView):
    template_name = "public_site/pricing.html"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Fetch active, public plans and prefetch their related features for efficiency
        plans_queryset = SubscriptionPlan.objects.filter(
            is_active=True, 
            is_public=True
        ).prefetch_related('features').order_by('price_monthly')
        
        context['plans'] = plans_queryset
        context['view_title'] = "Our Plans & Pricing"
        return context
    
    
# class PricingPageView(ListView): # Use ListView to display SubscriptionPlans
#     model = SubscriptionPlan
#     template_name = "public_site/pricing.html"
#     context_object_name = 'plans'

#     def get_queryset(self):
#         # Show only active and public plans, order them
#         return SubscriptionPlan.objects.filter(is_active=True, is_public=True).prefetch_related('features').order_by('price_monthly')

#     def get_context_data(self, **kwargs):
#         context = super().get_context_data(**kwargs)
#         context['view_title'] = "Pricing Plans"
#         return context

class AboutPageView(TemplateView):
    template_name = "public_site/about.html"
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = "About Us"
        return context

class ContactPageView(FormView):
    template_name = "public_site/contact.html"
    form_class = ContactForm
    success_url = reverse_lazy('public_site:contact_success')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = "Contact Us"
        return context

    def form_valid(self, form):
        # Save the form to create ContactInquiry instance
        contact_inquiry = form.save(commit=False)

        # Add metadata
        contact_inquiry.ip_address = self.get_client_ip()
        contact_inquiry.user_agent = self.request.META.get('HTTP_USER_AGENT', '')

        # Set priority based on inquiry type
        if contact_inquiry.inquiry_type in ['support', 'urgent']:
            contact_inquiry.priority = 'high'
        elif contact_inquiry.inquiry_type in ['sales', 'demo']:
            contact_inquiry.priority = 'medium'
        else:
            contact_inquiry.priority = 'low'

        contact_inquiry.save()

        # Send notification email to admin
        try:
            admin_subject = f"New {contact_inquiry.get_inquiry_type_display()}: {contact_inquiry.subject}"
            admin_message = f"""
New contact inquiry received:

Name: {contact_inquiry.name}
Email: {contact_inquiry.email}
Phone: {contact_inquiry.phone or 'Not provided'}
Organization: {contact_inquiry.organization or 'Not provided'}
Inquiry Type: {contact_inquiry.get_inquiry_type_display()}
Priority: {contact_inquiry.get_priority_display()}

Subject: {contact_inquiry.subject}

Message:
{contact_inquiry.message}

Submitted at: {contact_inquiry.submitted_at}
IP Address: {contact_inquiry.ip_address}

View in admin: {self.request.build_absolute_uri('/admin/public_site/contactinquiry/{}/'.format(contact_inquiry.id))}
"""

            send_mail(
                admin_subject,
                admin_message,
                settings.DEFAULT_FROM_EMAIL,
                [settings.DEFAULT_FROM_EMAIL],
                fail_silently=False,
            )

            # Send confirmation email to user
            user_subject = f"Thank you for contacting us - {contact_inquiry.subject}"
            user_message = f"""
Dear {contact_inquiry.name},

Thank you for contacting us! We have received your inquiry and will respond within 24-48 hours.

Your inquiry details:
Subject: {contact_inquiry.subject}
Inquiry Type: {contact_inquiry.get_inquiry_type_display()}
Submitted: {contact_inquiry.submitted_at.strftime('%B %d, %Y at %I:%M %p')}

We appreciate your interest in our school fees management platform.

Best regards,
The School Fees Platform Team
"""

            send_mail(
                user_subject,
                user_message,
                settings.DEFAULT_FROM_EMAIL,
                [contact_inquiry.email],
                fail_silently=True,  # Don't fail if user email fails
            )

            messages.success(
                self.request,
                f"Thank you {contact_inquiry.name}! Your message has been sent successfully. "
                f"We'll respond to your {contact_inquiry.get_inquiry_type_display().lower()} within 24-48 hours."
            )

        except Exception as e:
            messages.warning(
                self.request,
                "Your message was saved but there was an issue sending notifications. "
                "We'll still respond to your inquiry soon."
            )
            logger.error(f"Contact form email sending failed: {e}", exc_info=True)

        return super().form_valid(form)

    def get_client_ip(self):
        """Get the client's IP address"""
        x_forwarded_for = self.request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = self.request.META.get('REMOTE_ADDR')
        return ip

class ContactSuccessView(TemplateView):
    template_name = "public_site/contact_success.html"
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = "Message Sent"
        return context

class TermsPageView(TemplateView):
    template_name = "public_site/terms.html"
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = "Terms of Service"
        return context

class PrivacyPageView(TemplateView):
    template_name = "public_site/privacy.html"
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = "Privacy Policy"
        return context

class PublicMessagesView(ListView):
    """Display approved contact inquiries as public messages/testimonials"""
    model = ContactInquiry
    template_name = "public_site/public_messages.html"
    context_object_name = 'messages'
    paginate_by = 12

    def get_queryset(self):
        return ContactInquiry.objects.filter(
            is_approved_for_broadcast=True
        ).select_related().order_by('-is_featured', '-submitted_at')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = "Community Messages"

        # Get featured messages separately
        context['featured_messages'] = ContactInquiry.objects.filter(
            is_approved_for_broadcast=True,
            is_featured=True
        ).order_by('-submitted_at')[:3]

        # Get statistics
        context['total_messages'] = ContactInquiry.objects.filter(
            is_approved_for_broadcast=True
        ).count()

        context['inquiry_types'] = ContactInquiry.objects.filter(
            is_approved_for_broadcast=True
        ).values_list('inquiry_type', flat=True).distinct()

        return context

# --- Testimonials / Reviews ---
class TestimonialListView(ListView):
    model = Testimonial
    template_name = "public_site/testimonials_list.html"
    context_object_name = 'testimonials'
    paginate_by = 12

    def get_queryset(self):
        # Get filter parameters
        category = self.request.GET.get('category')
        school_type = self.request.GET.get('school_type')
        rating = self.request.GET.get('rating')

        queryset = Testimonial.objects.filter(is_approved=True)

        # Apply filters
        if category and category != 'all':
            queryset = queryset.filter(review_category=category)
        if school_type and school_type != 'all':
            queryset = queryset.filter(school_type=school_type)
        if rating and rating != 'all':
            queryset = queryset.filter(overall_rating__gte=int(rating))

        return queryset.order_by('-is_featured', '-submitted_on')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = "Customer Reviews & Testimonials"

        # Get featured testimonials
        context['featured_testimonials'] = Testimonial.objects.filter(
            is_approved=True, is_featured=True
        ).order_by('-submitted_on')[:3]

        # Get statistics
        all_approved = Testimonial.objects.filter(is_approved=True)
        context['total_reviews'] = all_approved.count()
        context['average_rating'] = all_approved.aggregate(
            avg_rating=models.Avg('overall_rating')
        )['avg_rating'] or 0

        # Rating distribution
        context['rating_distribution'] = {}
        for i in range(1, 6):
            context['rating_distribution'][i] = all_approved.filter(overall_rating=i).count()

        # Filter options
        context['categories'] = Testimonial.REVIEW_CATEGORIES
        context['school_types'] = Testimonial.SCHOOL_TYPES
        context['current_filters'] = {
            'category': self.request.GET.get('category', 'all'),
            'school_type': self.request.GET.get('school_type', 'all'),
            'rating': self.request.GET.get('rating', 'all'),
        }

        return context

class AddTestimonialView(SuccessMessageMixin, FormView):
    template_name = "public_site/testimonial_form.html"
    form_class = TestimonialSubmissionForm
    success_url = reverse_lazy('public_site:testimonial_thank_you')
    success_message = "Thank you for your review! It will be reviewed and published shortly."

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = "Share Your Experience"
        return context

    def form_valid(self, form):
        # Save the testimonial with metadata
        testimonial = form.save(commit=False)
        testimonial.ip_address = self.get_client_ip()
        testimonial.user_agent = self.request.META.get('HTTP_USER_AGENT', '')
        testimonial.save()

        # Send notification email to admin
        try:
            admin_subject = f"New Review Submitted: {testimonial.title}"
            admin_message = f"""
New review submitted:

Reviewer: {testimonial.author_name}
Title: {testimonial.author_title}
School: {testimonial.school_name}
School Type: {testimonial.get_school_type_display()}
Location: {testimonial.location or 'Not provided'}

Review Category: {testimonial.get_review_category_display()}
Review Title: {testimonial.title}
Overall Rating: {testimonial.overall_rating}/5 stars

Review Content:
{testimonial.quote}

Usage Duration: {testimonial.usage_duration or 'Not provided'}
Would Recommend: {'Yes' if testimonial.would_recommend else 'No'}

Submitted: {testimonial.submitted_on}
IP Address: {testimonial.ip_address}

View in admin: {self.request.build_absolute_uri('/admin/public_site/testimonial/{}/'.format(testimonial.id))}
"""

            send_mail(
                admin_subject,
                admin_message,
                settings.DEFAULT_FROM_EMAIL,
                [settings.DEFAULT_FROM_EMAIL],
                fail_silently=False,
            )

            # Send confirmation email to reviewer
            if testimonial.email:
                user_subject = f"Thank you for your review - {testimonial.title}"
                user_message = f"""
Dear {testimonial.author_name},

Thank you for taking the time to share your experience with our school fees management platform!

Your review details:
Title: {testimonial.title}
Category: {testimonial.get_review_category_display()}
Overall Rating: {testimonial.overall_rating}/5 stars
Submitted: {testimonial.submitted_on.strftime('%B %d, %Y at %I:%M %p')}

Your review will be reviewed by our team and published on our website within 24-48 hours. We appreciate your feedback and are thrilled to hear about your positive experience.

If you have any questions or need assistance, please don't hesitate to contact us.

Best regards,
The School Fees Platform Team
"""

                send_mail(
                    user_subject,
                    user_message,
                    settings.DEFAULT_FROM_EMAIL,
                    [testimonial.email],
                    fail_silently=True,
                )

        except Exception as e:
            logger.error(f"Testimonial email sending failed: {e}", exc_info=True)

        return super().form_valid(form)

    def get_client_ip(self):
        """Get the client's IP address"""
        x_forwarded_for = self.request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = self.request.META.get('REMOTE_ADDR')
        return ip

class TestimonialThankYouView(TemplateView):
    template_name = "public_site/testimonial_thank_you.html"
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = "Testimonial Received"
        return context
    
    
    
# D:\school_fees_saas_v2\apps\tenants\views.py (or public_site/views.py)

# import stripe  # Commented out - not needed for contact form
import logging
from django.conf import settings
from django.shortcuts import render, redirect
from django.views import View
from django.db import transaction
from django.contrib import messages
from django.contrib.auth import login
from django.utils import timezone
from django.urls import reverse

# Your project's models and forms
from apps.subscriptions.models import SubscriptionPlan, Subscription
from apps.tenants.models import School, Domain # Your tenant and domain models
# from .forms import TenantRegistrationForm # The form for this view

logger = logging.getLogger(__name__)

# Set Stripe API Key on startup (this is fine for development)
# stripe.api_key = settings.STRIPE_SECRET_KEY

class RegistrationView(View):
    template_name = 'public_site/registration.html' # Use a clear path

    def get(self, request, *args, **kwargs):
        # The plan ID should be passed as a query parameter from the pricing page
        plan_id = request.GET.get('plan')
        if not plan_id:
            messages.error(request, "Please select a pricing plan first.")
            return redirect('public_site:pricing') # Redirect to your pricing page

        try:
            plan = SubscriptionPlan.objects.get(pk=plan_id, is_active=True, is_public=True)
        except SubscriptionPlan.DoesNotExist:
            messages.error(request, "The selected plan is not valid. Please choose a plan to continue.")
            return redirect('public_site:pricing')

        # Pass initial data to the form if needed
        form = TenantRegistrationForm(initial={'plan_id': plan.pk})
        context = {
            'form': form,
            'plan': plan,
            'stripe_publishable_key': settings.STRIPE_PUBLISHABLE_KEY
        }
        return render(request, self.template_name, context)

    @transaction.atomic
    def post(self, request, *args, **kwargs):
        form = TenantRegistrationForm(request.POST)
        plan_id = request.POST.get('plan_id')
        payment_method_id = request.POST.get('payment_method_id') # This is sent from your Stripe.js frontend

        try:
            plan = SubscriptionPlan.objects.get(pk=plan_id, is_active=True, is_public=True)
        except SubscriptionPlan.DoesNotExist:
            messages.error(request, "Invalid plan selected. Please try again.")
            return redirect('public_site:pricing')

        if form.is_valid():
            if not payment_method_id:
                messages.error(request, "A payment processing error occurred. Your card was not charged. Please try again.")
                return self.render_form_with_error(form, plan)

            try:
                # --- 1. Create User, Tenant, and Subscription (locally) FIRST ---
                # This gives us objects to work with and link to Stripe.
                # The transaction will roll everything back if Stripe fails.
                
                # Create the User (SchoolOwner)
                owner = form.save(commit=False) # Assumes your form is a ModelForm for the owner
                owner.set_password(form.cleaned_data['password'])
                owner.is_school_owner = True
                owner.save()

                # Create the Tenant (School)
                school = School(
                    schema_name=form.cleaned_data['schema_name'],
                    name=form.cleaned_data['school_name'],
                    owner=owner,
                    is_active=False # IMPORTANT: Not active until payment is confirmed
                )
                school.save() # This triggers schema creation

                # Create the Domain
                base_domain = settings.TENANT_BASE_DOMAIN.split(':')[0]
                tenant_domain = f"{school.schema_name}.{base_domain}"
                Domain.objects.create(domain=tenant_domain, tenant=school, is_primary=True)
                
                # --- 2. Create Stripe Customer and Subscription ---
                customer = stripe.Customer.create(
                    email=owner.email,
                    name=owner.get_full_name(),
                    payment_method=payment_method_id,
                    invoice_settings={'default_payment_method': payment_method_id},
                    metadata={'school_schema': school.schema_name}
                )

                stripe_subscription = stripe.Subscription.create(
                    customer=customer.id,
                    items=[{'price': plan.stripe_price_id}], # Assumes plan has stripe_price_id
                    expand=['latest_invoice.payment_intent'],
                    metadata={'school_schema': school.schema_name}
                )

                # --- 3. Create our local Subscription record ---
                # This links our local objects to the Stripe objects
                subscription = Subscription.objects.create(
                    school=school,
                    plan=plan,
                    stripe_subscription_id=stripe_subscription.id,
                    stripe_customer_id=customer.id,
                    status=stripe_subscription.status, # e.g., 'active' or 'trialing'
                    current_period_end=timezone.datetime.fromtimestamp(stripe_subscription.current_period_end)
                )
                
                # --- 4. Activate the School ---
                # We only do this if the Stripe subscription status is active/trialing
                if subscription.is_usable:
                    school.is_active = True
                    school.save(update_fields=['is_active'])
                    messages.success(request, "Payment successful! Your school account is now active.")
                else:
                    # This case is rare but important (e.g., payment requires 3D secure, fails after creation)
                    messages.warning(request, "Your subscription is pending completion. Please check your email for further instructions.")
                    # The transaction will still commit, but the school remains inactive.
                    # A webhook would handle the final activation.

                # 5. Log the user in and redirect to their new tenant dashboard
                login(request, owner, backend='django.contrib.auth.backends.ModelBackend')
                
                # Build the tenant URL correctly
                tenant_url = school.get_absolute_url() # Use the method we created on the School model
                return redirect(tenant_url)

            except stripe.error.StripeError as e:
                # If Stripe fails, the transaction.atomic block rolls back everything.
                logger.error(f"Stripe error during registration for {form.cleaned_data['email']}: {e}")
                messages.error(request, f"Your payment could not be processed: {e.error.message}")
                return self.render_form_with_error(form, plan)
            except Exception as e:
                logger.error(f"Generic error during registration for {form.cleaned_data['email']}: {e}", exc_info=True)
                messages.error(request, "An unexpected error occurred. Please try again or contact support.")
                return self.render_form_with_error(form, plan)

        else: # Form is invalid
            return self.render_form_with_error(form, plan)

    def render_form_with_error(self, form, plan):
        """Helper to re-render the form with context on error."""
        context = {
            'form': form,
            'plan': plan,
            'stripe_publishable_key': settings.STRIPE_PUBLISHABLE_KEY
        }
        return render(self.request, self.template_name, context)
    
    


# apps/public_site/views.py

from django.views.generic import ListView
from .models import Testimonial # This import is likely already there for the ListView

# --- ADD THIS LINE ---
# Import the specific model you need from its app
from apps.subscriptions.models import Plan 
# Or whatever model you are actually trying to access.

# ... other imports ...

class TestimonialListView(ListView):
    model = Testimonial
    template_name = 'public/reviews.html' # Or whatever your template is called
    context_object_name = 'testimonials'
    paginate_by = 6

    def get_context_data(self, **kwargs):
        # First, get the base context from the super class
        context = super().get_context_data(**kwargs)
        
        # Now, add your extra context using the directly imported model
        # This is likely the area around line 417 that caused the error.
        
        # --- THIS IS THE FIX ---
        # Before (The Error):
        # context['plans'] = models.Plan.objects.filter(is_active=True)

        # After (Corrected):
        context['plans'] = Plan.objects.filter(is_active=True).order_by('price')
        
        return context