# D:\school_fees_saas_v2\apps\public_site\views.py
from django.shortcuts import render, redirect, get_object_or_404
from django.views.generic import TemplateView, ListView, FormView
from django.urls import reverse
from django.db.models import Q
from django.contrib import messages
from django.conf import settings # For TENANT_BASE_DOMAIN

from .forms import SchoolFinderForm
from apps.tenants.models import School # Import the School model

from django.urls import reverse_lazy
from django.contrib import messages
from django.core.mail import send_mail
from django.conf import settings
import logging # For logging contact form errors

from django.contrib.messages.views import SuccessMessageMixin

from apps.subscriptions.models import SubscriptionPlan, Feature
from .forms import ContactForm, TestimonialSubmissionForm
from .models import Testimonial, ContactInquiry # If you created ContactInquiry

logger = logging.getLogger(__name__) # Setup logger


def home_view(request):
    finder_form = SchoolFinderForm() # For display on homepage
    recent_schools = School.objects.filter(is_active=True).order_by('-created_on')[:5]
    context = {
        'finder_form': finder_form,
        'recent_schools': recent_schools,
        'view_title': "Welcome to School Fees Platform"
    }
    return render(request, 'public_site/home.html', context)


def find_school_view(request):
    query = request.GET.get('query', '').strip()
    schools_found = None
    exact_match = None
    view_title = "Find Your School"
    finder_form = SchoolFinderForm(request.GET or None) # Populate with submitted data or empty

    if query: # Only search if query is provided
        view_title = f"Search Results for '{query}'"
        try:
            # Try exact slug match first
            exact_match = School.objects.get(slug__iexact=query, is_active=True)
        except School.DoesNotExist:
            pass # Continue to broader search
        except School.MultipleObjectsReturned:
            messages.error(request, "Data error: Multiple schools match this identifier. Please contact support.")
            # Fall through to broader search which might list them, or redirect home
            schools_found = School.objects.filter(slug__iexact=query, is_active=True).order_by('name')


        if exact_match:
            return redirect('public_site:school_profile', school_slug=exact_match.slug)
        else:
            # Broader search by name or domain part if no exact slug match
            schools_found = School.objects.filter(
                Q(name__icontains=query) |
                Q(domains__domain__istartswith=query + '.') # Matches "subdomain." part
            ).filter(is_active=True).distinct().order_by('name')

            if schools_found.count() == 1:
                return redirect('public_site:school_profile', school_slug=schools_found.first().slug)
            elif not schools_found:
                messages.error(request, f"No active school found matching '{query}'. You can try a different name or register a new school.")
                # Redirect back to home with query to prefill form
                return redirect(reverse('public_site:home') + f'?query={query}')
            # If multiple results, we'll display them (handled below)
    else:
        # No query submitted, perhaps direct access to /find-school/
        # Just show the form again (or redirect to home)
        # For this example, we'll show the home template which includes the finder
        pass # Let it fall through to render home with empty/prefilled form

    # If query was empty, or multiple results and we want to display them on home
    # Or if no results and we want to show home with the error message
    recent_schools = School.objects.filter(is_active=True).order_by('-created_on')[:5]
    context = {
        'finder_form': finder_form, # Pass the form with query data or empty
        'recent_schools': recent_schools,
        'schools_found_list': schools_found, # Pass the list of found schools
        'query': query, # Pass the original query back
        'view_title': view_title if query else "Find Your School" # Adjust title
    }
    # Render a specific search results page or reuse home page
    # If reusing home, home.html needs to be able to display 'schools_found_list'
    return render(request, 'public_site/home.html', context) # Or 'public_site/school_search_results.html'



def school_public_profile_view(request, school_slug):
    school = get_object_or_404(School, slug=school_slug, is_active=True)
    
    primary_domain_obj = school.domains.filter(is_primary=True).first()
    if not primary_domain_obj:
        primary_domain_obj = school.domains.first() # Fallback

    tenant_action_base_url = None # Initialize
    
    if primary_domain_obj:
        scheme = request.scheme # http or https
        domain_hostname_from_db = primary_domain_obj.domain # e.g., 'zenith.myapp.test' or 'www.customschool.com'
        
        constructed_domain = domain_hostname_from_db # Start with the domain from DB

        if settings.DEBUG and hasattr(settings, 'TENANT_BASE_DOMAIN') and ':' in settings.TENANT_BASE_DOMAIN:
            # Extract base hostname and port from settings for comparison
            base_hostname_from_settings = settings.TENANT_BASE_DOMAIN.split(':')[0] # e.g., 'myapp.test'
            port_from_settings = settings.TENANT_BASE_DOMAIN.split(':')[-1]          # e.g., '8000'

            # Only add the dev server port if the domain from DB is either:
            # 1. Exactly the same as the base hostname from settings (e.g., myapp.test for a public site on tenant domain)
            # OR
            # 2. A subdomain of the base hostname from settings (e.g., zenith.myapp.test is a subdomain of myapp.test)
            # AND
            # 3. The domain from DB doesn't already include a port.
            if (domain_hostname_from_db == base_hostname_from_settings or \
                domain_hostname_from_db.endswith(f".{base_hostname_from_settings}")):
                
                if ':' not in domain_hostname_from_db: # Check if port is not already in domain_hostname_from_db
                    constructed_domain = f"{domain_hostname_from_db}:{port_from_settings}"
            # If it's a custom domain not matching the base, we assume it's correctly configured
            # and don't append the dev port.
        
        tenant_action_base_url = f"{scheme}://{constructed_domain}"

    context = {
        'school': school,
        'tenant_action_base_url': tenant_action_base_url, # e.g., http://zenith.myapp.test:8000 or http://www.customschool.com
        'view_title': f"{school.name} - School Portal",
    }
    return render(request, 'public_site/school_profile.html', context)

class HomePageView(TemplateView):
    template_name = "public_site/home.html"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # You can set a specific title, or let the template default work
        # context['view_title'] = "Welcome to Our Amazing Platform!" 
        try:
            context['testimonials'] = Testimonial.objects.filter(is_approved=True).order_by('?')[:3]
        except: # Catch errors if Testimonial model or app isn't ready
            context['testimonials'] = None
        return context
# class HomePageView(TemplateView):
#     template_name = "public_site/home.html" # You already have this view as 'home'

#     def get_context_data(self, **kwargs):
#         context = super().get_context_data(**kwargs)
#         context['view_title'] = "Welcome"
#         # Fetch a few approved testimonials for the homepage
#         context['testimonials'] = Testimonial.objects.filter(is_approved=True).order_by('?')[:3] # Random 3
#         return context

class FeaturesPageView(TemplateView):
    template_name = "public_site/features.html"
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = "Our Features"
        # You could fetch Feature model instances here if you want to list them dynamically
        # context['all_features'] = Feature.objects.all().order_by('name')
        return context



# D:\school_fees_saas_v2\apps\public_site\views.py
from django.views.generic import TemplateView
from apps.subscriptions.models import SubscriptionPlan

class PricingPageView(TemplateView):
    template_name = "public_site/pricing.html"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Fetch active, public plans and prefetch their related features for efficiency
        plans_queryset = SubscriptionPlan.objects.filter(
            is_active=True, 
            is_public=True
        ).prefetch_related('features').order_by('price_monthly')
        
        context['plans'] = plans_queryset
        context['view_title'] = "Our Plans & Pricing"
        return context
    
    
# class PricingPageView(ListView): # Use ListView to display SubscriptionPlans
#     model = SubscriptionPlan
#     template_name = "public_site/pricing.html"
#     context_object_name = 'plans'

#     def get_queryset(self):
#         # Show only active and public plans, order them
#         return SubscriptionPlan.objects.filter(is_active=True, is_public=True).prefetch_related('features').order_by('price_monthly')

#     def get_context_data(self, **kwargs):
#         context = super().get_context_data(**kwargs)
#         context['view_title'] = "Pricing Plans"
#         return context

class AboutPageView(TemplateView):
    template_name = "public_site/about.html"
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = "About Us"
        return context

class ContactPageView(FormView):
    template_name = "public_site/contact.html"
    form_class = ContactForm
    success_url = reverse_lazy('public_site:contact_success') # Redirect to a success page

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = "Contact Us"
        return context

    def form_valid(self, form):
        name = form.cleaned_data['name']
        from_email = form.cleaned_data['email']
        subject = form.cleaned_data['subject']
        message_content = form.cleaned_data['message']

        # Option 1: Save to ContactInquiry model
        # ContactInquiry.objects.create(...)

        # Option 2: Send an email
        try:
            send_mail(
                f"Contact Form: {subject} from {name}",
                f"From: {name} <{from_email}>\n\nMessage:\n{message_content}",
                from_email, # Reply-to
                [settings.DEFAULT_FROM_EMAIL], # Send to your admin email
                fail_silently=False,
            )
            messages.success(self.request, "Your message has been sent successfully! We'll get back to you soon.")
        except Exception as e:
            messages.error(self.request, "Sorry, there was an error sending your message. Please try again later.")
            logger.error(f"Contact form email sending failed: {e}", exc_info=True)

        return super().form_valid(form)

class ContactSuccessView(TemplateView):
    template_name = "public_site/contact_success.html"
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = "Message Sent"
        return context

class TermsPageView(TemplateView):
    template_name = "public_site/terms.html"
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = "Terms of Service"
        return context

class PrivacyPageView(TemplateView):
    template_name = "public_site/privacy.html"
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = "Privacy Policy"
        return context

# --- Testimonials / Reviews ---
class TestimonialListView(ListView):
    model = Testimonial
    template_name = "public_site/testimonials_list.html"
    context_object_name = 'testimonials'
    paginate_by = 10

    def get_queryset(self):
        return Testimonial.objects.filter(is_approved=True).order_by('-submitted_on')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = "What Our Users Say"
        return context

class AddTestimonialView(SuccessMessageMixin, FormView):
    template_name = "public_site/testimonial_form.html"
    form_class = TestimonialSubmissionForm
    success_url = reverse_lazy('public_site:testimonial_thank_you') # Redirect to a thank you page
    success_message = "Thank you for your testimonial! It will be reviewed shortly."

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = "Share Your Experience"
        return context

    def form_valid(self, form):
        # Form is a ModelForm, so form.save() creates the Testimonial
        # It will be created with is_approved=False by default (from model)
        form.save()
        # Optionally, send an email notification to admin about new testimonial
        return super().form_valid(form)

class TestimonialThankYouView(TemplateView):
    template_name = "public_site/testimonial_thank_you.html"
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = "Testimonial Received"
        return context
    
    
    
# D:\school_fees_saas_v2\apps\tenants\views.py (or public_site/views.py)

import stripe
import logging
from django.conf import settings
from django.shortcuts import render, redirect
from django.views import View
from django.db import transaction
from django.contrib import messages
from django.contrib.auth import login
from django.utils import timezone
from django.urls import reverse

# Your project's models and forms
from apps.subscriptions.models import SubscriptionPlan, Subscription
from apps.tenants.models import School, Domain # Your tenant and domain models
# from .forms import TenantRegistrationForm # The form for this view

logger = logging.getLogger(__name__)

# Set Stripe API Key on startup (this is fine for development)
# stripe.api_key = settings.STRIPE_SECRET_KEY

class RegistrationView(View):
    template_name = 'public_site/registration.html' # Use a clear path

    def get(self, request, *args, **kwargs):
        # The plan ID should be passed as a query parameter from the pricing page
        plan_id = request.GET.get('plan')
        if not plan_id:
            messages.error(request, "Please select a pricing plan first.")
            return redirect('public_site:pricing') # Redirect to your pricing page

        try:
            plan = SubscriptionPlan.objects.get(pk=plan_id, is_active=True, is_public=True)
        except SubscriptionPlan.DoesNotExist:
            messages.error(request, "The selected plan is not valid. Please choose a plan to continue.")
            return redirect('public_site:pricing')

        # Pass initial data to the form if needed
        form = TenantRegistrationForm(initial={'plan_id': plan.pk})
        context = {
            'form': form,
            'plan': plan,
            'stripe_publishable_key': settings.STRIPE_PUBLISHABLE_KEY
        }
        return render(request, self.template_name, context)

    @transaction.atomic
    def post(self, request, *args, **kwargs):
        form = TenantRegistrationForm(request.POST)
        plan_id = request.POST.get('plan_id')
        payment_method_id = request.POST.get('payment_method_id') # This is sent from your Stripe.js frontend

        try:
            plan = SubscriptionPlan.objects.get(pk=plan_id, is_active=True, is_public=True)
        except SubscriptionPlan.DoesNotExist:
            messages.error(request, "Invalid plan selected. Please try again.")
            return redirect('public_site:pricing')

        if form.is_valid():
            if not payment_method_id:
                messages.error(request, "A payment processing error occurred. Your card was not charged. Please try again.")
                return self.render_form_with_error(form, plan)

            try:
                # --- 1. Create User, Tenant, and Subscription (locally) FIRST ---
                # This gives us objects to work with and link to Stripe.
                # The transaction will roll everything back if Stripe fails.
                
                # Create the User (SchoolOwner)
                owner = form.save(commit=False) # Assumes your form is a ModelForm for the owner
                owner.set_password(form.cleaned_data['password'])
                owner.is_school_owner = True
                owner.save()

                # Create the Tenant (School)
                school = School(
                    schema_name=form.cleaned_data['schema_name'],
                    name=form.cleaned_data['school_name'],
                    owner=owner,
                    is_active=False # IMPORTANT: Not active until payment is confirmed
                )
                school.save() # This triggers schema creation

                # Create the Domain
                base_domain = settings.TENANT_BASE_DOMAIN.split(':')[0]
                tenant_domain = f"{school.schema_name}.{base_domain}"
                Domain.objects.create(domain=tenant_domain, tenant=school, is_primary=True)
                
                # --- 2. Create Stripe Customer and Subscription ---
                customer = stripe.Customer.create(
                    email=owner.email,
                    name=owner.get_full_name(),
                    payment_method=payment_method_id,
                    invoice_settings={'default_payment_method': payment_method_id},
                    metadata={'school_schema': school.schema_name}
                )

                stripe_subscription = stripe.Subscription.create(
                    customer=customer.id,
                    items=[{'price': plan.stripe_price_id}], # Assumes plan has stripe_price_id
                    expand=['latest_invoice.payment_intent'],
                    metadata={'school_schema': school.schema_name}
                )

                # --- 3. Create our local Subscription record ---
                # This links our local objects to the Stripe objects
                subscription = Subscription.objects.create(
                    school=school,
                    plan=plan,
                    stripe_subscription_id=stripe_subscription.id,
                    stripe_customer_id=customer.id,
                    status=stripe_subscription.status, # e.g., 'active' or 'trialing'
                    current_period_end=timezone.datetime.fromtimestamp(stripe_subscription.current_period_end)
                )
                
                # --- 4. Activate the School ---
                # We only do this if the Stripe subscription status is active/trialing
                if subscription.is_usable:
                    school.is_active = True
                    school.save(update_fields=['is_active'])
                    messages.success(request, "Payment successful! Your school account is now active.")
                else:
                    # This case is rare but important (e.g., payment requires 3D secure, fails after creation)
                    messages.warning(request, "Your subscription is pending completion. Please check your email for further instructions.")
                    # The transaction will still commit, but the school remains inactive.
                    # A webhook would handle the final activation.

                # 5. Log the user in and redirect to their new tenant dashboard
                login(request, owner, backend='django.contrib.auth.backends.ModelBackend')
                
                # Build the tenant URL correctly
                tenant_url = school.get_absolute_url() # Use the method we created on the School model
                return redirect(tenant_url)

            except stripe.error.StripeError as e:
                # If Stripe fails, the transaction.atomic block rolls back everything.
                logger.error(f"Stripe error during registration for {form.cleaned_data['email']}: {e}")
                messages.error(request, f"Your payment could not be processed: {e.error.message}")
                return self.render_form_with_error(form, plan)
            except Exception as e:
                logger.error(f"Generic error during registration for {form.cleaned_data['email']}: {e}", exc_info=True)
                messages.error(request, "An unexpected error occurred. Please try again or contact support.")
                return self.render_form_with_error(form, plan)

        else: # Form is invalid
            return self.render_form_with_error(form, plan)

    def render_form_with_error(self, form, plan):
        """Helper to re-render the form with context on error."""
        context = {
            'form': form,
            'plan': plan,
            'stripe_publishable_key': settings.STRIPE_PUBLISHABLE_KEY
        }
        return render(self.request, self.template_name, context)
    
    
    