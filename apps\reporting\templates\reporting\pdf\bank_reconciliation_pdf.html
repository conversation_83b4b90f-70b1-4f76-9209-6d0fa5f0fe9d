<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>{{ report_title }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            margin: 0;
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
        }
        .school-name {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .report-title {
            font-size: 16px;
            font-weight: bold;
            margin: 10px 0;
        }
        .report-date {
            font-size: 12px;
            color: #666;
        }
        .summary-section {
            margin: 20px 0;
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
        }
        .account-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
        }
        .account-details, .balance-details {
            width: 48%;
        }
        .calculation-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background-color: #f8f9fa;
        }
        .calculation-table th,
        .calculation-table td {
            border: 1px solid #ddd;
            padding: 10px;
            text-align: left;
        }
        .calculation-table th {
            background-color: #e9ecef;
            font-weight: bold;
        }
        .text-right {
            text-align: right;
        }
        .text-center {
            text-align: center;
        }
        .reconciled-balance {
            background-color: #d4edda;
            font-weight: bold;
        }
        .difference-row {
            background-color: #fff3cd;
            font-weight: bold;
        }
        .difference-zero {
            background-color: #d4edda;
            font-weight: bold;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
            font-size: 11px;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 10px;
            color: #666;
            border-top: 1px solid #ddd;
            padding-top: 10px;
        }
        .page-break {
            page-break-before: always;
        }
        .totals-row {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .deposit-amount {
            color: #28a745;
            font-weight: bold;
        }
        .payment-amount {
            color: #dc3545;
            font-weight: bold;
        }
        .outstanding-item {
            background-color: #fff3cd;
        }
        .cleared-item {
            background-color: #d4edda;
        }
        .section-header {
            background-color: #e9ecef;
            padding: 10px;
            margin: 20px 0 10px 0;
            border-left: 4px solid #007bff;
            font-weight: bold;
        }
        .status-badge {
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 10px;
            font-weight: bold;
        }
        .status-cleared {
            background-color: #28a745;
            color: white;
        }
        .status-outstanding {
            background-color: #ffc107;
            color: black;
        }
        .entry-badge {
            background-color: #6c757d;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 10px;
        }
    </style>
</head>
<body>
    <!-- Header Section -->
    <div class="header">
        {% if school_profile %}
        <div class="school-name">{{ school_profile.school_name }}</div>
        {% if school_profile.address %}
        <div>{{ school_profile.address }}</div>
        {% endif %}
        {% if school_profile.phone %}
        <div>Phone: {{ school_profile.phone }}</div>
        {% endif %}
        {% if school_profile.email %}
        <div>Email: {{ school_profile.email }}</div>
        {% endif %}
        {% else %}
        <div class="school-name">{{ tenant.name }}</div>
        {% endif %}
        
        <div class="report-title">{{ report_title }}</div>
        <div class="report-date">
            Statement Date: {{ statement_date|date:"M d, Y" }} | Reconciliation Date: {{ reconciliation_date|date:"M d, Y" }}
        </div>
    </div>

    <!-- Account Information -->
    <div class="summary-section">
        <h3>Account Information</h3>
        <div class="account-info">
            <div class="account-details">
                <p><strong>Bank Account:</strong> {{ bank_account.name }}</p>
                <p><strong>Account Code:</strong> {{ bank_account.code|default:"N/A" }}</p>
                <p><strong>Account Type:</strong> {{ bank_account.account_type.name }}</p>
            </div>
            <div class="balance-details">
                <p><strong>Bank Statement Balance:</strong> {{ statement_balance|floatformat:2 }}</p>
                <p><strong>Book Balance:</strong> {{ book_balance|floatformat:2 }}</p>
                <p><strong>Reconciled Balance:</strong> {{ reconciled_balance|floatformat:2 }}</p>
                <p><strong>Difference:</strong> 
                    <span style="{% if difference == 0 %}color: #28a745;{% else %}color: #dc3545;{% endif %}">
                        {{ difference|floatformat:2 }}
                    </span>
                </p>
            </div>
        </div>
    </div>

    <!-- Reconciliation Calculation -->
    <h3>Reconciliation Calculation</h3>
    <table class="calculation-table">
        <tbody>
            <tr>
                <td><strong>Bank Statement Balance</strong></td>
                <td class="text-right">{{ statement_balance|floatformat:2 }}</td>
            </tr>
            <tr>
                <td><em>Add: Outstanding Deposits</em></td>
                <td class="text-right deposit-amount">+ {{ outstanding_deposits_total|floatformat:2 }}</td>
            </tr>
            <tr>
                <td><em>Less: Outstanding Checks/Payments</em></td>
                <td class="text-right payment-amount">- {{ outstanding_checks_total|floatformat:2 }}</td>
            </tr>
            <tr class="reconciled-balance">
                <td><strong>Reconciled Balance</strong></td>
                <td class="text-right"><strong>{{ reconciled_balance|floatformat:2 }}</strong></td>
            </tr>
            <tr>
                <td><strong>Book Balance</strong></td>
                <td class="text-right">{{ book_balance|floatformat:2 }}</td>
            </tr>
            <tr class="{% if difference == 0 %}difference-zero{% else %}difference-row{% endif %}">
                <td><strong>Difference</strong></td>
                <td class="text-right"><strong>{{ difference|floatformat:2 }}</strong></td>
            </tr>
        </tbody>
    </table>

    {% if difference != 0 %}
    <div style="background-color: #fff3cd; padding: 10px; border-left: 4px solid #ffc107; margin: 15px 0;">
        <strong>Reconciliation Difference:</strong> {{ difference|floatformat:2 }}
        {% if difference > 0 %}
        (Book balance is higher than reconciled balance)
        {% else %}
        (Book balance is lower than reconciled balance)
        {% endif %}
    </div>
    {% else %}
    <div style="background-color: #d4edda; padding: 10px; border-left: 4px solid #28a745; margin: 15px 0;">
        <strong>Reconciliation Complete:</strong> Book balance matches reconciled balance.
    </div>
    {% endif %}

    <!-- Outstanding Deposits -->
    {% if outstanding_deposits %}
    <div class="section-header">Outstanding Deposits ({{ outstanding_deposits|length }} items)</div>
    <table>
        <thead>
            <tr>
                <th style="width: 15%;">Date</th>
                <th style="width: 15%;">Entry #</th>
                <th style="width: 50%;">Description</th>
                <th style="width: 20%;" class="text-right">Amount</th>
            </tr>
        </thead>
        <tbody>
            {% for item in outstanding_deposits %}
            <tr class="outstanding-item">
                <td>{{ item.date|date:"M d, Y" }}</td>
                <td><span class="entry-badge">{{ item.entry_number }}</span></td>
                <td>{{ item.description|truncatechars:40 }}</td>
                <td class="text-right deposit-amount">{{ item.amount|floatformat:2 }}</td>
            </tr>
            {% endfor %}
        </tbody>
        <tfoot>
            <tr class="totals-row">
                <td colspan="3" class="text-right"><strong>Total Outstanding Deposits:</strong></td>
                <td class="text-right deposit-amount"><strong>{{ outstanding_deposits_total|floatformat:2 }}</strong></td>
            </tr>
        </tfoot>
    </table>
    {% endif %}

    <!-- Outstanding Checks/Payments -->
    {% if outstanding_checks %}
    <div class="section-header">Outstanding Checks/Payments ({{ outstanding_checks|length }} items)</div>
    <table>
        <thead>
            <tr>
                <th style="width: 15%;">Date</th>
                <th style="width: 15%;">Entry #</th>
                <th style="width: 50%;">Description</th>
                <th style="width: 20%;" class="text-right">Amount</th>
            </tr>
        </thead>
        <tbody>
            {% for item in outstanding_checks %}
            <tr class="outstanding-item">
                <td>{{ item.date|date:"M d, Y" }}</td>
                <td><span class="entry-badge">{{ item.entry_number }}</span></td>
                <td>{{ item.description|truncatechars:40 }}</td>
                <td class="text-right payment-amount">{{ item.amount|floatformat:2 }}</td>
            </tr>
            {% endfor %}
        </tbody>
        <tfoot>
            <tr class="totals-row">
                <td colspan="3" class="text-right"><strong>Total Outstanding Checks:</strong></td>
                <td class="text-right payment-amount"><strong>{{ outstanding_checks_total|floatformat:2 }}</strong></td>
            </tr>
        </tfoot>
    </table>
    {% endif %}

    <!-- All Bank Transactions -->
    {% if reconciliation_items %}
    <div class="page-break"></div>
    <div class="section-header">All Bank Transactions ({{ reconciliation_items|length }} transactions)</div>
    <table>
        <thead>
            <tr>
                <th style="width: 12%;">Date</th>
                <th style="width: 12%;">Entry #</th>
                <th style="width: 12%;">Type</th>
                <th style="width: 40%;">Description</th>
                <th style="width: 15%;" class="text-right">Amount</th>
                <th style="width: 9%;" class="text-center">Status</th>
            </tr>
        </thead>
        <tbody>
            {% for item in reconciliation_items %}
            <tr class="{% if item.is_cleared %}cleared-item{% else %}outstanding-item{% endif %}">
                <td>{{ item.date|date:"M d, Y" }}</td>
                <td><span class="entry-badge">{{ item.entry_number }}</span></td>
                <td>{{ item.type }}</td>
                <td>{{ item.description|truncatechars:35 }}</td>
                <td class="text-right">
                    <span class="{% if item.type == 'Deposit' %}deposit-amount{% else %}payment-amount{% endif %}">
                        {{ item.amount|floatformat:2 }}
                    </span>
                </td>
                <td class="text-center">
                    {% if item.is_cleared %}
                    <span class="status-badge status-cleared">Cleared</span>
                    {% else %}
                    <span class="status-badge status-outstanding">Outstanding</span>
                    {% endif %}
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    {% endif %}

    <!-- Footer -->
    <div class="footer">
        <p>Generated on {{ "now"|date:"F d, Y \a\\t g:i A" }} | {{ tenant.name }} | Bank Reconciliation Report</p>
        <p><em>Note: Outstanding items are transactions recorded after the statement date. Cleared items are transactions on or before the statement date.</em></p>
    </div>
</body>
</html>
