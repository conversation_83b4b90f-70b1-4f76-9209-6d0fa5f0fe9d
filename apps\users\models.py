# D:\school_fees_saas_v2\apps\users\models.py
from django.contrib.auth.models import AbstractBaseUser, BaseUserManager, PermissionsMixin
from django.db import models
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

class CustomUserManager(BaseUserManager):
    """
    Custom user manager where email is the unique identifiers
    for authentication instead of usernames.
    """
    def create_user(self, email, password=None, **extra_fields):
        """
        Create and save a User with the given email and password.
        """
        if not email:
            raise ValueError(_('The Email must be set'))
        email = self.normalize_email(email)
        user = self.model(email=email, **extra_fields)
        user.set_password(password)
        user.save(using=self._db)
        return user

    def create_superuser(self, email, password=None, **extra_fields):
        """
        Create and save a SuperUser with the given email and password.
        """
        extra_fields.setdefault('is_staff', True)
        extra_fields.setdefault('is_superuser', True)
        extra_fields.setdefault('is_active', True)

        if extra_fields.get('is_staff') is not True:
            raise ValueError(_('Superuser must have is_staff=True.'))
        if extra_fields.get('is_superuser') is not True:
            raise ValueError(_('Superuser must have is_superuser=True.'))
        
        # If you add other REQUIRED_FIELDS to the User model (e.g., first_name),
        # ensure they are provided or handled here for createsuperuser.
        # For example:
        # if not extra_fields.get('first_name'):
        #     raise ValueError(_('Superuser must have a first name.'))

        return self.create_user(email, password, **extra_fields)


# D:\school_fees_saas_v2\apps\users\models.py

from django.db import models
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django.contrib.auth.models import AbstractBaseUser, PermissionsMixin
from .managers import CustomUserManager # Assuming your manager is here

class User(AbstractBaseUser, PermissionsMixin):
    """
    Custom User model for platform administrators and school owners.
    Login is with email. This model resides in the 'public' schema.
    """
    email = models.EmailField(
        _('email address'), 
        unique=True,
        help_text=_("Required. Used for login and important communications.")
    )
    first_name = models.CharField(_('first name'), max_length=150, blank=True)
    last_name = models.CharField(_('last name'), max_length=150, blank=True)
    
    is_staff = models.BooleanField(
        _('platform staff status'),
        default=False, 
        help_text=_('Designates whether the user can log into the main platform Django admin site.'),
    )
    is_active = models.BooleanField(
        _('active'),
        default=True,
        help_text=_('Designates whether this user should be treated as active. Unselect this instead of deleting accounts.'),
    )
    date_joined = models.DateTimeField(_('date joined'), default=timezone.now)

    # --- ADDED TO FIX REVERSE ACCESSOR CLASH (E304) ---
    groups = models.ManyToManyField(
        'auth.Group',
        verbose_name=_('groups'),
        blank=True,
        help_text=_(
            'The groups this user belongs to. A user will get all permissions '
            'granted to each of their groups.'
        ),
        related_name="public_user_set",  # Unique related_name for the public user
        related_query_name="public_user",
    )
    user_permissions = models.ManyToManyField(
        'auth.Permission',
        verbose_name=_('user permissions'),
        blank=True,
        help_text=_('Specific permissions for this user.'),
        related_name="public_user_permissions_set", # Unique related_name
        related_query_name="public_user",
    )
    # --- END OF ADDED FIELDS ---

    objects = CustomUserManager()

    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = ['first_name', 'last_name']

    class Meta:
        verbose_name = _('Platform User')
        verbose_name_plural = _('Platform Users')
        ordering = ['email']

    def __str__(self):
        return self.email

    def get_full_name(self):
        full_name = '%s %s' % (self.first_name, self.last_name)
        return full_name.strip()

    def get_short_name(self):
        return self.first_name
    
    
# class User(AbstractBaseUser, PermissionsMixin):
#     """
#     Custom User model for platform administrators and school owners.
#     Login is with email.
#     This model resides in the 'public' schema.
#     """
#     email = models.EmailField(
#         _('email address'), 
#         unique=True, # Email must be unique
#         help_text=_("Required. Used for login, password reset, and important communications.")
#     )
#     first_name = models.CharField(_('first name'), max_length=150, blank=True)
#     last_name = models.CharField(_('last name'), max_length=150, blank=True)
    
#     # Designates whether this user can log into the MAIN Django admin site (public schema).
#     # This is different from StaffUser.is_staff which is for tenant admin access.
#     is_staff = models.BooleanField(
#         _('platform staff status'),
#         default=False, 
#         help_text=_('Designates whether the user can log into the main platform Django admin site.'),
#     )
#     # Designates whether this user should be treated as active. 
#     # Unselect this instead of deleting accounts.
#     is_active = models.BooleanField(
#         _('active'),
#         default=True,
#         help_text=_(
#             'Designates whether this user should be treated as active. '
#             'Unselect this instead of deleting accounts.'
#         ),
#     )
#     date_joined = models.DateTimeField(_('date joined'), default=timezone.now)

#     # The 'groups' and 'user_permissions' fields are inherited from PermissionsMixin.
#     # These groups and permissions would be for platform-level roles/permissions,
#     # distinct from tenant-level roles for StaffUser.

#     objects = CustomUserManager() # Use the custom manager

#     USERNAME_FIELD = 'email' # Use email as the unique identifier for login
#     REQUIRED_FIELDS = ['first_name', 'last_name'] # Fields prompted for when using createsuperuser (besides email & password)
#                                                 # Remove 'username' if you don't have that field.

#     class Meta:
#         verbose_name = _('Platform User')
#         verbose_name_plural = _('Platform Users')
#         ordering = ['email']

#     def __str__(self):
#         return self.email

#     def get_full_name(self):
#         """
#         Return the first_name plus the last_name, with a space in between.
#         """
#         full_name = '%s %s' % (self.first_name, self.last_name)
#         return full_name.strip()

#     def get_short_name(self):
#         """Return the short name for the user (first_name)."""
#         return self.first_name


    
    
    