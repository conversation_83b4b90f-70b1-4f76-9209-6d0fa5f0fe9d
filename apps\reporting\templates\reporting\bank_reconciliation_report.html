{% extends "tenant_base.html" %}
{% load humanize static core_tags %}

{% block title %}{{ view_title|default:"Bank Reconciliation Report" }}{% endblock %}

{% block content %}
<div class="container-fluid mt-3">
    {% with report_icon_class="bi bi-bank" %}
        {% include "partials/_report_header.html" %}
    {% endwith %}

    {% include "partials/_messages.html" %}
    {% include "partials/_report_filter_form.html" with filter_form=filter_form report_code=report_code specific_filter_title="Bank Reconciliation Filters" %}

    {% if bank_account and statement_date %}
    <!-- Report Summary Card -->
    <div class="card mb-4">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="bi bi-calculator me-2"></i>Reconciliation Summary</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h6 class="text-muted">Account Information</h6>
                    <p><strong>Bank Account:</strong> {{ bank_account.name }}</p>
                    <p><strong>Account Code:</strong> {{ bank_account.code|default:"N/A" }}</p>
                    <p><strong>Statement Date:</strong> {{ statement_date|date:"M d, Y" }}</p>
                    <p><strong>Reconciliation Date:</strong> {{ reconciliation_date|date:"M d, Y" }}</p>
                </div>
                <div class="col-md-6">
                    <h6 class="text-muted">Balance Information</h6>
                    <div class="row">
                        <div class="col-6">
                            <p><strong>Bank Statement Balance:</strong><br>
                            <span class="text-primary fs-5">{{ statement_balance|currency }}</span></p>
                        </div>
                        <div class="col-6">
                            <p><strong>Book Balance:</strong><br>
                            <span class="text-info fs-5">{{ book_balance|currency }}</span></p>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-6">
                            <p><strong>Reconciled Balance:</strong><br>
                            <span class="text-success fs-5">{{ reconciled_balance|currency }}</span></p>
                        </div>
                        <div class="col-6">
                            <p><strong>Difference:</strong><br>
                            <span class="{% if difference == 0 %}text-success{% else %}text-danger{% endif %} fs-5">{{ difference|currency }}</span></p>
                        </div>
                    </div>
                </div>
            </div>
            
            {% if difference != 0 %}
            <div class="alert alert-warning mt-3">
                <i class="bi bi-exclamation-triangle me-2"></i>
                <strong>Reconciliation Difference:</strong> {{ difference|currency }}
                {% if difference > 0 %}
                (Book balance is higher than reconciled balance)
                {% else %}
                (Book balance is lower than reconciled balance)
                {% endif %}
            </div>
            {% else %}
            <div class="alert alert-success mt-3">
                <i class="bi bi-check-circle me-2"></i>
                <strong>Reconciliation Complete:</strong> Book balance matches reconciled balance.
            </div>
            {% endif %}
        </div>
    </div>

    <!-- Reconciliation Calculation -->
    <div class="card mb-4">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="bi bi-calculator-fill me-2"></i>Reconciliation Calculation</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-borderless">
                    <tbody>
                        <tr>
                            <td><strong>Bank Statement Balance</strong></td>
                            <td class="text-end">{{ statement_balance|currency }}</td>
                        </tr>
                        <tr class="table-light">
                            <td><em>Add: Outstanding Deposits</em></td>
                            <td class="text-end text-success">+ {{ outstanding_deposits_total|currency }}</td>
                        </tr>
                        <tr class="table-light">
                            <td><em>Less: Outstanding Checks/Payments</em></td>
                            <td class="text-end text-danger">- {{ outstanding_checks_total|currency }}</td>
                        </tr>
                        <tr class="table-primary">
                            <td><strong>Reconciled Balance</strong></td>
                            <td class="text-end"><strong>{{ reconciled_balance|currency }}</strong></td>
                        </tr>
                        <tr>
                            <td><strong>Book Balance</strong></td>
                            <td class="text-end">{{ book_balance|currency }}</td>
                        </tr>
                        <tr class="{% if difference == 0 %}table-success{% else %}table-warning{% endif %}">
                            <td><strong>Difference</strong></td>
                            <td class="text-end"><strong>{{ difference|currency }}</strong></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Outstanding Deposits -->
    {% if outstanding_deposits %}
    <div class="card mb-4">
        <div class="card-header bg-light d-flex justify-content-between align-items-center">
            <h5 class="mb-0"><i class="bi bi-arrow-down-circle text-success me-2"></i>Outstanding Deposits</h5>
            <span class="badge bg-success">{{ outstanding_deposits|length }} items</span>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-striped table-hover mb-0">
                    <thead class="table-dark">
                        <tr>
                            <th>Date</th>
                            <th>Entry #</th>
                            <th>Description</th>
                            <th class="text-end">Amount</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for item in outstanding_deposits %}
                        <tr>
                            <td>{{ item.date|date:"M d, Y" }}</td>
                            <td><span class="badge bg-secondary">{{ item.entry_number }}</span></td>
                            <td>{{ item.description|truncatechars:50 }}</td>
                            <td class="text-end"><span class="text-success fw-bold">{{ item.amount|currency }}</span></td>
                        </tr>
                        {% endfor %}
                    </tbody>
                    <tfoot class="table-light">
                        <tr>
                            <th colspan="3" class="text-end">Total Outstanding Deposits:</th>
                            <th class="text-end text-success">{{ outstanding_deposits_total|currency }}</th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Outstanding Checks/Payments -->
    {% if outstanding_checks %}
    <div class="card mb-4">
        <div class="card-header bg-light d-flex justify-content-between align-items-center">
            <h5 class="mb-0"><i class="bi bi-arrow-up-circle text-danger me-2"></i>Outstanding Checks/Payments</h5>
            <span class="badge bg-danger">{{ outstanding_checks|length }} items</span>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-striped table-hover mb-0">
                    <thead class="table-dark">
                        <tr>
                            <th>Date</th>
                            <th>Entry #</th>
                            <th>Description</th>
                            <th class="text-end">Amount</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for item in outstanding_checks %}
                        <tr>
                            <td>{{ item.date|date:"M d, Y" }}</td>
                            <td><span class="badge bg-secondary">{{ item.entry_number }}</span></td>
                            <td>{{ item.description|truncatechars:50 }}</td>
                            <td class="text-end"><span class="text-danger fw-bold">{{ item.amount|currency }}</span></td>
                        </tr>
                        {% endfor %}
                    </tbody>
                    <tfoot class="table-light">
                        <tr>
                            <th colspan="3" class="text-end">Total Outstanding Checks:</th>
                            <th class="text-end text-danger">{{ outstanding_checks_total|currency }}</th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- All Reconciliation Items -->
    {% if reconciliation_items %}
    <div class="card">
        <div class="card-header bg-light d-flex justify-content-between align-items-center">
            <h5 class="mb-0"><i class="bi bi-list-check me-2"></i>All Bank Transactions</h5>
            <span class="badge bg-primary">{{ reconciliation_items|length }} transactions</span>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-striped table-hover mb-0">
                    <thead class="table-dark">
                        <tr>
                            <th>Date</th>
                            <th>Entry #</th>
                            <th>Type</th>
                            <th>Description</th>
                            <th class="text-end">Amount</th>
                            <th class="text-center">Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for item in reconciliation_items %}
                        <tr class="{% if not item.is_cleared %}table-warning{% endif %}">
                            <td>{{ item.date|date:"M d, Y" }}</td>
                            <td><span class="badge bg-secondary">{{ item.entry_number }}</span></td>
                            <td>
                                {% if item.type == 'Deposit' %}
                                <span class="badge bg-success">{{ item.type }}</span>
                                {% else %}
                                <span class="badge bg-danger">{{ item.type }}</span>
                                {% endif %}
                            </td>
                            <td>{{ item.description|truncatechars:50 }}</td>
                            <td class="text-end">
                                <span class="{% if item.type == 'Deposit' %}text-success{% else %}text-danger{% endif %} fw-bold">
                                    {{ item.amount|currency }}
                                </span>
                            </td>
                            <td class="text-center">
                                {% if item.is_cleared %}
                                <span class="badge bg-success">Cleared</span>
                                {% else %}
                                <span class="badge bg-warning">Outstanding</span>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    {% endif %}

    {% else %}
    <!-- No Data Message -->
    <div class="card">
        <div class="card-body text-center py-5">
            <i class="bi bi-bank display-1 text-muted mb-3"></i>
            <h4 class="text-muted">Bank Reconciliation Setup Required</h4>
            <p class="text-muted">Please select a bank account, statement date, and enter the statement balance to begin reconciliation.</p>
        </div>
    </div>
    {% endif %}

</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Highlight outstanding items
    $('.table-warning').hover(
        function() {
            $(this).addClass('table-info');
        },
        function() {
            $(this).removeClass('table-info');
        }
    );
});
</script>
{% endblock %}
