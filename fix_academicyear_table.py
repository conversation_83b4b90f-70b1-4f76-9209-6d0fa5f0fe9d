#!/usr/bin/env python
"""
Script to fix the schools_academicyear table in existing tenant schemas
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from django.db import connection
from django_tenants.utils import schema_context
from apps.tenants.models import School

def fix_academicyear_tables():
    """Fix the schools_academicyear table in all tenant schemas"""
    
    # Get all schools
    schools = School.objects.all()
    print(f"Found {schools.count()} schools to fix:")
    for school in schools:
        print(f"  {school.schema_name}: {school.name}")
    
    print("\nStarting table fixes...")
    
    # Fix each schema
    for school in schools:
        schema_name = school.schema_name
        school_name = school.name
        
        print(f"\nFixing schema '{schema_name}' ({school_name})...")
        
        try:
            with schema_context(schema_name):
                with connection.cursor() as cursor:
                    # Check if is_active column exists
                    cursor.execute("""
                        SELECT column_name 
                        FROM information_schema.columns 
                        WHERE table_name = 'schools_academicyear' 
                        AND column_name = 'is_active'
                    """)
                    has_is_active = cursor.fetchone() is not None
                    
                    if not has_is_active:
                        # Add the is_active column
                        cursor.execute("""
                            ALTER TABLE schools_academicyear 
                            ADD COLUMN is_active BOOLEAN NOT NULL DEFAULT FALSE
                        """)
                        print(f"  ✓ Added is_active column")
                    else:
                        print(f"  ✓ is_active column already exists")
                    
                    # Check if unique constraint exists on name
                    cursor.execute("""
                        SELECT constraint_name 
                        FROM information_schema.table_constraints 
                        WHERE table_name = 'schools_academicyear' 
                        AND constraint_type = 'UNIQUE'
                        AND constraint_name LIKE '%name%'
                    """)
                    has_unique_constraint = cursor.fetchone() is not None
                    
                    if not has_unique_constraint:
                        # Add unique constraint on name
                        cursor.execute("""
                            ALTER TABLE schools_academicyear 
                            ADD CONSTRAINT schools_academicyear_name_unique UNIQUE (name)
                        """)
                        print(f"  ✓ Added unique constraint on name")
                    else:
                        print(f"  ✓ Unique constraint on name already exists")
                        
        except Exception as e:
            print(f"  ✗ Error fixing schema '{schema_name}': {e}")
    
    print("\nTable fixes completed!")

if __name__ == "__main__":
    fix_academicyear_tables()
