{% extends "tenant_base.html" %}
{% load static i18n widget_tweaks %}

{% block tenant_page_title %}{{ view_title|default:_("Review Leave Request") }}{% endblock %}

{% block tenant_specific_content %}
<div class="container mt-4">

    {# Page Header & Breadcrumb #}
    <div class="pagetitle mb-3">
        <h1>{{ view_title }}</h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'schools:dashboard' %}">{% trans "Dashboard" %}</a></li>
                <li class="breadcrumb-item"><a href="{% url 'hr:hr_dashboard' %}">{% trans "HR" %}</a></li>
                <li class="breadcrumb-item"><a href="{% url 'hr:admin_leaverequest_list' %}">{% trans "Administer Leave Requests" %}</a></li>
                <li class="breadcrumb-item active" aria-current="page">{% trans "Review" %}</li>
            </ol>
        </nav>
    </div>

    {% include "partials/_messages.html" %}

    {# Card to show the read-only details of the request #}
    <div class="card shadow-sm mb-4">
        <div class="card-header">
            <h5 class="mb-0"><i class="bi bi-info-circle-fill me-2"></i>{% trans "Request Details" %}</h5>
        </div>
        <div class="card-body">
            <div class="row pt-3">
                <div class="col-md-6">
                    <p><strong>{% trans "Staff Member" %}:</strong> {{ object.employee.user.get_full_name }}</p>
                    <p><strong>{% trans "Leave Type" %}:</strong> {{ object.leave_type.name }}</p>
                    <p><strong>{% trans "Dates Requested" %}:</strong> {{ object.start_date|date:"D, d M Y" }} - {{ object.end_date|date:"D, d M Y" }}</p>
                </div>
                <div class="col-md-6">
                    {# --- CORRECTED DURATION DISPLAY --- #}
                    <p><strong>{% trans "Total Days Requested" %}:</strong> {{ object.duration }} {% trans "day" %}{{ object.duration|pluralize }}</p>
                    <p><strong>{% trans "Date Submitted" %}:</strong> {{ object.created_at|date:"d M, Y" }}</p>
                    <p><strong>{% trans "Current Status" %}:</strong> <span class="badge bg-{{ object.get_status_badge_class }}">{{ object.get_status_display }}</span></p>
                </div>
                <div class="col-12 mt-2">
                    <p><strong>{% trans "Reason Given by Staff" %}:</strong></p>
                    <blockquote class="blockquote bg-light p-3 rounded">
                        <p class="mb-0 fst-italic">{{ object.reason|default:"No reason provided." }}</p>
                    </blockquote>
                </div>
            </div>
        </div>
    </div>
    
    {# Card for the administrator's action form #}
    <div class="card shadow-sm">
        <div class="card-header">
            <h5 class="mb-0"><i class="bi bi-check2-square me-2"></i>{% trans "Administrator Action" %}</h5>
        </div>
        <div class="card-body p-4">
            <form method="post">
                {% csrf_token %}
                
                <div class="mb-3">
                    <label for="{{ form.status.id_for_label }}" class="form-label required">{{ form.status.label }}</label>
                    {% render_field form.status class="form-select" %}
                    <div class="form-text">{{ form.status.help_text }}</div>
                </div>

                <div class="mb-3">
                    <label for="{{ form.status_reason.id_for_label }}" class="form-label">{{ form.status_reason.label }}</label>
                    {% render_field form.status_reason class="form-control" rows="3" placeholder="Optional: Provide a reason for approval or rejection..." %}
                    <div class="form-text">{{ form.status_reason.help_text }}</div>
                </div>

                <div class="d-flex justify-content-end gap-2 mt-4">
                    <a href="{% url 'hr:admin_leaverequest_list' %}" class="btn btn-secondary">
                        <i class="bi bi-x-circle me-1"></i>{% trans "Cancel" %}
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-save-fill me-2"></i>{% trans "Update Request Status" %}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock tenant_specific_content %}



