<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>{{ report_title }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            margin: 0;
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
        }
        .school-name {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .report-title {
            font-size: 16px;
            font-weight: bold;
            margin: 10px 0;
        }
        .report-period {
            font-size: 12px;
            color: #666;
        }
        .summary-section {
            margin: 20px 0;
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
        }
        .summary-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }
        .summary-label {
            font-weight: bold;
        }
        .filter-info {
            background-color: #e3f2fd;
            padding: 10px;
            margin: 15px 0;
            border-left: 4px solid #2196f3;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .text-right {
            text-align: right;
        }
        .text-center {
            text-align: center;
        }
        .debit-amount {
            color: #28a745;
            font-weight: bold;
        }
        .credit-amount {
            color: #dc3545;
            font-weight: bold;
        }
        .status-posted {
            background-color: #28a745;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 10px;
        }
        .status-draft {
            background-color: #ffc107;
            color: black;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 10px;
        }
        .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 10px;
            color: #666;
            border-top: 1px solid #ddd;
            padding-top: 10px;
        }
        .page-break {
            page-break-before: always;
        }
        .totals-row {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .account-code {
            font-size: 10px;
            color: #666;
        }
        .entry-type {
            font-size: 10px;
            color: #666;
            font-style: italic;
        }
    </style>
</head>
<body>
    <!-- Header Section -->
    <div class="header">
        {% if school_profile %}
        <div class="school-name">{{ school_profile.school_name }}</div>
        {% if school_profile.address %}
        <div>{{ school_profile.address }}</div>
        {% endif %}
        {% if school_profile.phone %}
        <div>Phone: {{ school_profile.phone }}</div>
        {% endif %}
        {% if school_profile.email %}
        <div>Email: {{ school_profile.email }}</div>
        {% endif %}
        {% else %}
        <div class="school-name">{{ tenant.name }}</div>
        {% endif %}
        
        <div class="report-title">{{ report_title }}</div>
        <div class="report-period">
            Period: {{ start_date|date:"M d, Y" }} to {{ end_date|date:"M d, Y" }}
        </div>
    </div>

    <!-- Summary Section -->
    <div class="summary-section">
        <h3>Report Summary</h3>
        <div class="summary-row">
            <span class="summary-label">Total Debits:</span>
            <span class="debit-amount">{{ total_debits|floatformat:2 }}</span>
        </div>
        <div class="summary-row">
            <span class="summary-label">Total Credits:</span>
            <span class="credit-amount">{{ total_credits|floatformat:2 }}</span>
        </div>
        <div class="summary-row">
            <span class="summary-label">Difference:</span>
            <span>{{ total_debits|subtract:total_credits|floatformat:2 }}</span>
        </div>
    </div>

    <!-- Filter Information -->
    {% if selected_account %}
    <div class="filter-info">
        <strong>Account Filter:</strong> {{ selected_account.name }} ({{ selected_account.account_type.name }})
    </div>
    {% elif selected_account_type %}
    <div class="filter-info">
        <strong>Account Type Filter:</strong> {{ selected_account_type.name }}
    </div>
    {% endif %}

    <!-- Journal Entries Table -->
    {% if journal_lines %}
    <table>
        <thead>
            <tr>
                <th>Date</th>
                <th>Entry #</th>
                <th>Account</th>
                <th>Account Type</th>
                <th>Description</th>
                <th class="text-right">Debit</th>
                <th class="text-right">Credit</th>
                <th class="text-center">Status</th>
            </tr>
        </thead>
        <tbody>
            {% for line in journal_lines %}
            <tr>
                <td>{{ line.journal_entry.date|date:"M d, Y" }}</td>
                <td>{{ line.journal_entry.entry_number|default:line.journal_entry.id }}</td>
                <td>
                    {{ line.account.name }}
                    {% if line.account.code %}
                    <br><span class="account-code">{{ line.account.code }}</span>
                    {% endif %}
                </td>
                <td>{{ line.account.account_type.name }}</td>
                <td>
                    {{ line.description|default:line.journal_entry.narration|truncatechars:40 }}
                    {% if line.journal_entry.entry_type != 'MANUAL' %}
                    <br><span class="entry-type">{{ line.journal_entry.get_entry_type_display }}</span>
                    {% endif %}
                </td>
                <td class="text-right">
                    {% if line.debit_amount > 0 %}
                    <span class="debit-amount">{{ line.debit_amount|floatformat:2 }}</span>
                    {% else %}
                    -
                    {% endif %}
                </td>
                <td class="text-right">
                    {% if line.credit_amount > 0 %}
                    <span class="credit-amount">{{ line.credit_amount|floatformat:2 }}</span>
                    {% else %}
                    -
                    {% endif %}
                </td>
                <td class="text-center">
                    {% if line.journal_entry.status == 'POSTED' %}
                    <span class="status-posted">Posted</span>
                    {% elif line.journal_entry.status == 'DRAFT' %}
                    <span class="status-draft">Draft</span>
                    {% else %}
                    {{ line.journal_entry.get_status_display }}
                    {% endif %}
                </td>
            </tr>
            {% endfor %}
        </tbody>
        <tfoot>
            <tr class="totals-row">
                <td colspan="5" class="text-right"><strong>Totals:</strong></td>
                <td class="text-right debit-amount"><strong>{{ total_debits|floatformat:2 }}</strong></td>
                <td class="text-right credit-amount"><strong>{{ total_credits|floatformat:2 }}</strong></td>
                <td></td>
            </tr>
        </tfoot>
    </table>
    {% endif %}

    <!-- Accounts Summary (when no specific account is selected) -->
    {% if accounts_summary and not selected_account %}
    <div class="page-break"></div>
    <h3>Accounts Summary</h3>
    <table>
        <thead>
            <tr>
                <th>Account</th>
                <th>Account Type</th>
                <th class="text-center">Entries</th>
                <th class="text-right">Total Debits</th>
                <th class="text-right">Total Credits</th>
                <th class="text-right">Net Balance</th>
            </tr>
        </thead>
        <tbody>
            {% for summary in accounts_summary %}
            <tr>
                <td>
                    {{ summary.account.name }}
                    {% if summary.account.code %}
                    <br><span class="account-code">{{ summary.account.code }}</span>
                    {% endif %}
                </td>
                <td>{{ summary.account.account_type.name }}</td>
                <td class="text-center">{{ summary.line_count }}</td>
                <td class="text-right">
                    {% if summary.debits > 0 %}
                    <span class="debit-amount">{{ summary.debits|floatformat:2 }}</span>
                    {% else %}
                    -
                    {% endif %}
                </td>
                <td class="text-right">
                    {% if summary.credits > 0 %}
                    <span class="credit-amount">{{ summary.credits|floatformat:2 }}</span>
                    {% else %}
                    -
                    {% endif %}
                </td>
                <td class="text-right">
                    {% if summary.net_balance > 0 %}
                    <span class="debit-amount">{{ summary.net_balance|floatformat:2 }}</span>
                    {% elif summary.net_balance < 0 %}
                    <span class="credit-amount">{{ summary.net_balance|floatformat:2 }}</span>
                    {% else %}
                    {{ summary.net_balance|floatformat:2 }}
                    {% endif %}
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    {% endif %}

    <!-- Footer -->
    <div class="footer">
        <p>Generated on {{ "now"|date:"F d, Y \a\\t g:i A" }} | {{ tenant.name }} | General Ledger Report</p>
    </div>
</body>
</html>
