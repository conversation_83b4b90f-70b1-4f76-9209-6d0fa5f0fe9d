#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to create a trial subscription plan for the SaaS application.
This creates a 7-day trial plan with 10 student limit.
"""

import os
import sys
import django
from decimal import Decimal

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from apps.subscriptions.models import SubscriptionPlan, Feature

def create_trial_plan():
    """Create a trial subscription plan with 7-day trial and 10 student limit."""
    
    print("Creating trial subscription plan...")
    
    # Create or update the trial plan
    trial_plan, created = SubscriptionPlan.objects.get_or_create(
        name='Trial Plan',
        defaults={
            'slug': 'trial-plan',
            'description': 'Free 7-day trial with up to 10 students. Perfect for testing our platform.',
            'price_monthly': Decimal('0.00'),
            'price_annually': Decimal('0.00'),
            'max_students': 10,  # Trial limit: 10 students
            'max_staff': 5,      # Reasonable staff limit for trial
            'trial_period_days': 7,  # 7-day trial
            'is_active': True,
            'is_public': True,
            'display_order': 0,  # Show first in pricing
        }
    )
    
    if created:
        print(f"✅ Created new trial plan: {trial_plan.name}")
    else:
        print(f"✅ Trial plan already exists: {trial_plan.name}")
        # Update key fields to ensure they're correct
        trial_plan.max_students = 10
        trial_plan.trial_period_days = 7
        trial_plan.price_monthly = Decimal('0.00')
        trial_plan.price_annually = Decimal('0.00')
        trial_plan.is_active = True
        trial_plan.is_public = True
        trial_plan.display_order = 0
        trial_plan.save()
        print(f"✅ Updated trial plan settings")
    
    # Create basic features for trial (optional)
    basic_features = [
        {
            'code': 'student_management',
            'name': 'Student Management',
            'description': 'Add and manage up to 10 students'
        },
        {
            'code': 'fee_management',
            'name': 'Fee Management',
            'description': 'Create invoices and track payments'
        },
        {
            'code': 'basic_reporting',
            'name': 'Basic Reporting',
            'description': 'Access to essential reports'
        },
        {
            'code': 'parent_portal',
            'name': 'Parent Portal',
            'description': 'Parent access to student information'
        }
    ]
    
    for feature_data in basic_features:
        feature, created = Feature.objects.get_or_create(
            code=feature_data['code'],
            defaults={
                'name': feature_data['name'],
                'description': feature_data['description']
            }
        )
        
        # Add feature to trial plan
        trial_plan.features.add(feature)
        
        if created:
            print(f"✅ Created feature: {feature.name}")
        else:
            print(f"✅ Feature already exists: {feature.name}")
    
    # Create paid plans for upgrade options
    paid_plans = [
        {
            'name': 'Starter Plan',
            'slug': 'starter-plan',
            'description': 'Perfect for small schools with up to 50 students.',
            'price_monthly': Decimal('29.99'),
            'price_annually': Decimal('299.99'),
            'max_students': 50,
            'max_staff': 10,
            'trial_period_days': 0,  # No trial for paid plans
            'display_order': 1
        },
        {
            'name': 'Professional Plan',
            'slug': 'professional-plan',
            'description': 'Ideal for growing schools with up to 200 students.',
            'price_monthly': Decimal('79.99'),
            'price_annually': Decimal('799.99'),
            'max_students': 200,
            'max_staff': 25,
            'trial_period_days': 0,
            'display_order': 2
        },
        {
            'name': 'Enterprise Plan',
            'slug': 'enterprise-plan',
            'description': 'For large schools with unlimited students and advanced features.',
            'price_monthly': Decimal('199.99'),
            'price_annually': Decimal('1999.99'),
            'max_students': 9999,  # Effectively unlimited
            'max_staff': None,     # Unlimited staff
            'trial_period_days': 0,
            'display_order': 3
        }
    ]
    
    for plan_data in paid_plans:
        plan, created = SubscriptionPlan.objects.get_or_create(
            name=plan_data['name'],
            defaults={
                'slug': plan_data['slug'],
                'description': plan_data['description'],
                'price_monthly': plan_data['price_monthly'],
                'price_annually': plan_data['price_annually'],
                'max_students': plan_data['max_students'],
                'max_staff': plan_data['max_staff'],
                'trial_period_days': plan_data['trial_period_days'],
                'is_active': True,
                'is_public': True,
                'display_order': plan_data['display_order']
            }
        )
        
        # Add all features to paid plans
        for feature_data in basic_features:
            feature = Feature.objects.get(code=feature_data['code'])
            plan.features.add(feature)
        
        if created:
            print(f"✅ Created paid plan: {plan.name}")
        else:
            print(f"✅ Paid plan already exists: {plan.name}")
    
    print("\n🎉 Trial plan setup completed!")
    print(f"📊 Trial Plan Details:")
    print(f"   - Name: {trial_plan.name}")
    print(f"   - Trial Period: {trial_plan.trial_period_days} days")
    print(f"   - Student Limit: {trial_plan.max_students} students")
    print(f"   - Price: Free (${trial_plan.price_monthly}/month)")
    print(f"   - Features: {trial_plan.features.count()} features included")
    
    print(f"\n📈 Available Plans:")
    for plan in SubscriptionPlan.objects.filter(is_active=True).order_by('display_order'):
        print(f"   - {plan.name}: ${plan.price_monthly}/month, {plan.max_students} students, {plan.trial_period_days} day trial")

if __name__ == '__main__':
    create_trial_plan()
