{# D:\school_fees_saas_v2\templates\portal_admin\group_detail.html #}
{% extends "tenant_base.html" %}

{% block tenant_page_title %}{{ view_title }}{% endblock %}

{% block tenant_specific_content %}
<div class="container mt-4">
    <div class="card shadow-sm">
        <div class="card-header bg-success text-white">
            <div class="d-flex justify-content-between align-items-center">
                <h4 class="mb-0"><i class="bi bi-shield-lock-fill me-2"></i>{{ view_title }}</h4>
                <div>
                    {% if perms.auth.change_group %}
                        <a href="{% url 'portal_admin:group_update' pk=group_obj.pk %}" class="btn btn-light btn-sm me-2">
                            <i class="bi bi-pencil-square me-1"></i> Edit Role Name
                        </a>
                    {% endif %}
                    {% if perms.portal_admin.manage_role_permissions %} {# Assuming a perm for this #}
                        <a href="{% url 'portal_admin:assign_permissions_to_group' group_pk=group_obj.pk %}" class="btn btn-info btn-sm">
                            <i class="bi bi-key-fill me-1"></i> Manage Permissions
                        </a>
                    {% endif %}
                </div>
            </div>
        </div>
        <div class="card-body p-4">
            <dl class="row">
                <dt class="col-sm-3">Role Name:</dt>
                <dd class="col-sm-9">{{ group_obj.name }}</dd>

                <dt class="col-sm-3">System ID (PK):</dt>
                <dd class="col-sm-9">{{ group_obj.pk }}</dd>
            </dl>

            <hr>
            
            <h5>Permissions ({{ group_obj.permissions.count }}):</h5>
            {% if group_obj.permissions.all %}
                <ul class="list-group list-group-flush mb-3">
                    {% for perm in group_obj.permissions.all|dictsort:"content_type.app_label"|dictsort:"name" %}
                        <li class="list-group-item list-group-item-light py-1 px-2 small">
                            <span class="text-muted small">{{ perm.content_type.app_label }}.{{ perm.codename }}</span> - {{ perm.name }}
                        </li>
                    {% endfor %}
                </ul>
            {% else %}
                <p class="text-muted">No permissions assigned to this role.</p>
            {% endif %}

            <hr>

            {# START: SECTION TO DISPLAY STAFF MEMBERS IN THIS ROLE #}
            <h5>Staff Members in this Role ({{ staff_in_group.count }}):</h5>
            {% if staff_in_group %}
                <table class="table table-sm table-hover table-striped">
                    <thead class="table-light">
                        <tr>
                            <th>#</th>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Employee ID</th>
                            <th>Designation</th>
                            <th>Status</th>
                            {% if perms.portal_admin.manage_staff_roles %} {# Or a specific "remove from role" perm #}
                            <th>Actions</th>
                            {% endif %}
                        </tr>
                    </thead>
                    <tbody>
                        {% for staff_member in staff_in_group %}
                            <tr>
                                <td>{{ forloop.counter }}</td>
                                <td>{{ staff_member.get_full_name|default:"N/A" }}</td>
                                <td>{{ staff_member.email }}</td>
                                <td>
                                    {# Access hr_profile data if available #}
                                    {% if staff_member.hr_profile %}
                                        {{ staff_member.hr_profile.employee_id|default:"-" }}
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                                <td>
                                    {% if staff_member.hr_profile %}
                                        {{ staff_member.hr_profile.designation|default:"-" }}
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                                <td>
                                    {% if staff_member.is_active %}
                                        <span class="badge bg-success status-badge">Active</span>
                                    {% else %}
                                        <span class="badge bg-danger status-badge">Inactive</span>
                                    {% endif %}
                                </td>
                                {% if perms.portal_admin.manage_staff_roles %}
                                <td>
                                    {# Link to a view to remove this specific user from this specific role #}
                                    {# This would require another view/form, or an AJAX call #}
                                    {# For now, could link back to the main assign page with user preselected #}
                                     <a href="{% url 'portal_admin:assign_staff_roles' %}?staff_member_id={{ staff_member.pk }}" 
                                       class="btn btn-outline-secondary btn-sm" title="Edit {{staff_member.get_full_name}}'s Roles">
                                        <i class="bi bi-pencil-square"></i> Edit Roles
                                    </a>
                                </td>
                                {% endif %}
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            {% else %}
                <p class="alert alert-info">No staff members currently assigned to this role.</p>
            {% endif %}
            {# END: SECTION TO DISPLAY STAFF MEMBERS IN THIS ROLE #}

            {% if perms.portal_admin.manage_staff_roles %}
            <div class="mt-4">
                <a href="{% url 'portal_admin:assign_staff_roles' %}" class="btn btn-primary">
                    <i class="bi bi-people-fill me-1"></i> Assign More Staff to Roles
                </a>
            </div>
            {% endif %}

        </div>
        <div class="card-footer text-muted small">
            Last updated: {% now "D d M Y, H:i" %}
        </div>
    </div>
</div>
{% endblock tenant_specific_content %}






{% comment %} {# templates/portal_admin/group_detail.html #}
{% extends "tenant_base.html" %}

{% block title %}{{ view_title }}{% endblock %}

{% block tenant_specific_content %}
<div class="container mt-4">
    <div class="card">
        <div class="card-header bg-light d-flex justify-content-between align-items-center">
            <h4 class="mb-0">{{ view_title }}</h4>
            <div>
                <a href="{% url 'portal_admin:assign_permissions_to_group' group_pk=group_obj.pk %}" class="btn btn-sm btn-info">
                    <i class="bi bi-shield-lock-fill me-1"></i> Edit Permissions
                </a>
                <a href="{% url 'portal_admin:group_update' pk=group_obj.pk %}" class="btn btn-sm btn-warning">
                    <i class="bi bi-pencil-fill me-1"></i> Edit Role
                </a>
            </div>
        </div>
        <div class="card-body">
            <h5 class="card-title mt-3">Role Name: {{ group_obj.name }}</h5>
            
            <hr>
            <h6>Permissions ({{ group_obj.permissions.count }}):</h6>
            {% if group_obj.permissions.all %}
                <ul class="list-unstyled">
                {% for perm in group_obj.permissions.all|dictsort:"content_type.app_label"|dictsort:"name" %}
                    <li>
                        <span class="badge bg-secondary me-1">{{ perm.content_type.app_label }}</span>
                        {{ perm.name }} 
                        <small class="text-muted">({{ perm.codename }})</small>
                    </li>
                {% endfor %}
                </ul>
            {% else %}
                <p class="text-muted">No permissions assigned to this role.</p>
            {% endif %}

            <hr>
            <h6>Staff Members in this Role ({{ staff_in_group.count }}):</h6>
            {% if staff_in_group %}
                <ul>
                {% for staff_member in staff_in_group %}
                    <li>{{ staff_member.full_name|default:staff_member.email }}</li>
                {% endfor %}
                </ul>
            {% else %}
                <p class="text-muted">No staff members assigned to this role.</p>
            {% endif %}

            {# Add section for ParentUsers if you manage them in groups too #}
            {# {% if parents_in_group %} ... {% endif %} #}

        </div>
        <div class="card-footer">
            <a href="{% url 'portal_admin:group_list' %}" class="btn btn-outline-secondary">Back to Roles List</a>
        </div>
    </div>
</div>
{% endblock %}
 {% endcomment %}

