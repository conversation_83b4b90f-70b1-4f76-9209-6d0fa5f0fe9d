# apps/tenants/signals.py
from django.db.models.signals import post_save
from django.dispatch import receiver
from django_tenants.signals import post_schema_sync
from django_tenants.utils import schema_context, get_tenant_model, get_public_schema_name
from django.db import connection
from django.contrib.auth.models import Group
# from django.conf import settings # Not directly used in this snippet
import logging

# Use ONE logger instance for this entire file, configured in settings.LOGGING
signal_logger = logging.getLogger('project.tenant_signals') # Explicit name

Tenant = get_tenant_model()

@receiver(post_schema_sync, sender=Tenant)
def create_owner_as_staff_user_on_tenant_creation(sender, instance, **kwargs):
    """
    After a new tenant schema is created and synced (post_schema_sync),
    create or update a StaffUser record for the tenant's owner within that tenant's schema.
    This StaffUser will act as the super-administrator for the tenant.
    """
    signal_logger.critical("!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!")
    signal_logger.critical("OWNER-STAFF SIGNAL: TOP OF FUNCTION REACHED!")
    signal_logger.critical(f"OWNER-STAFF SIGNAL: Tenant instance: {instance.schema_name if instance else 'No Instance'}")
    signal_logger.critical("!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!")

    tenant = instance

    if tenant.schema_name == get_public_schema_name():
        signal_logger.debug(f"TENANT_SIGNAL (OwnerStaff): Skipping owner-as-staff creation for public schema '{tenant.schema_name}'.") # Changed logger
        return

    # First, ensure tenant has proper structure using template
    apply_tenant_template(tenant)

    if not hasattr(tenant, 'owner') or not tenant.owner:
        signal_logger.error(f"TENANT_SIGNAL (OwnerStaff): Tenant '{tenant.schema_name}' (PK: {tenant.pk}) does not have an owner assigned. Cannot create owner-as-staff user.") # Changed logger
        return

    owner = tenant.owner
    signal_logger.info(f"TENANT_SIGNAL (OwnerStaff): Processing owner-as-staff creation for tenant '{tenant.schema_name}' (PK: {tenant.pk}), Owner: {owner.email} (PK: {owner.pk}).") # Changed logger

    # Use the helper function to create StaffUser
    staff_user, created = create_staff_user_for_owner(tenant)

    if staff_user:
        signal_logger.info(f"TENANT_SIGNAL (OwnerStaff): StaffUser for {owner.email} - Created: {created}")
        signal_logger.info(f"TENANT_SIGNAL (OwnerStaff): <<< EXITED Owner-Staff Signal successfully for {tenant.schema_name}")
    else:
        signal_logger.error(f"TENANT_SIGNAL (OwnerStaff): Failed to create StaffUser for {owner.email} in tenant '{tenant.schema_name}'")
        signal_logger.info(f"TENANT_SIGNAL (OwnerStaff): <<< EXITED Owner-Staff Signal WITH ERROR for {tenant.schema_name}")


@receiver(post_save, sender=Tenant)
def create_owner_staff_user_on_school_save(sender, instance, created, **kwargs):
    """
    Backup signal: Create StaffUser for owner when School is saved (created=True).
    This runs after the School instance is saved but before schema sync.
    We'll use a delayed task to create the StaffUser after the schema is ready.
    """
    if not created:
        return  # Only run for new tenants

    tenant = instance
    signal_logger.info(f"POST_SAVE_SIGNAL: School '{tenant.schema_name}' created, will schedule StaffUser creation")

    if tenant.schema_name == get_public_schema_name():
        signal_logger.debug(f"POST_SAVE_SIGNAL: Skipping for public schema '{tenant.schema_name}'")
        return

    if not hasattr(tenant, 'owner') or not tenant.owner:
        signal_logger.error(f"POST_SAVE_SIGNAL: Tenant '{tenant.schema_name}' has no owner. Cannot create StaffUser.")
        return

    # Schedule a delayed task to create StaffUser after schema is ready
    from django.db import transaction

    def create_staff_user_after_commit():
        """Create StaffUser after the transaction commits and schema is ready."""
        import time
        time.sleep(2)  # Give time for schema creation to complete

        try:
            # Check if schema exists and is ready
            with schema_context(tenant.schema_name):
                staff_user, created = create_staff_user_for_owner(tenant)
                if staff_user:
                    signal_logger.info(f"POST_SAVE_DELAYED: Successfully created StaffUser for {tenant.owner.email} in {tenant.schema_name}")
                else:
                    signal_logger.error(f"POST_SAVE_DELAYED: Failed to create StaffUser for {tenant.owner.email} in {tenant.schema_name}")
        except Exception as e:
            signal_logger.error(f"POST_SAVE_DELAYED: Error creating StaffUser for {tenant.schema_name}: {e}", exc_info=True)

    # Schedule the function to run after the current transaction commits
    transaction.on_commit(create_staff_user_after_commit)
    signal_logger.info(f"POST_SAVE_SIGNAL: Scheduled delayed StaffUser creation for '{tenant.schema_name}' with owner '{tenant.owner.email}'")


def apply_tenant_template(tenant):
    """
    Apply proper tenant template to ensure consistent structure
    """
    signal_logger.info(f"TEMPLATE: Applying proper tenant template to {tenant.schema_name}")

    try:
        from django.core.management import call_command

        # 1. First run migrations to create all tables
        signal_logger.info(f"TEMPLATE: Running migrations for {tenant.schema_name}")
        with schema_context(tenant.schema_name):
            call_command('migrate', verbosity=0, interactive=False)

        # 2. Then apply essential data
        signal_logger.info(f"TEMPLATE: Applying essential data to {tenant.schema_name}")
        apply_essential_tenant_data(tenant.schema_name)

        signal_logger.info(f"TEMPLATE: Proper template applied successfully to {tenant.schema_name}")

    except Exception as e:
        signal_logger.error(f"TEMPLATE: Failed to apply proper template to {tenant.schema_name}: {e}")

def apply_essential_tenant_data(schema_name):
    """Apply essential data to tenant schema"""

    try:
        with schema_context(schema_name):
            with connection.cursor() as cursor:
                # Create academic year
                cursor.execute("SELECT COUNT(*) FROM schools_academicyear")
                if cursor.fetchone()[0] == 0:
                    cursor.execute("""
                        INSERT INTO schools_academicyear (name, start_date, end_date, is_active, is_current, created_at, updated_at)
                        VALUES ('2024-2025', '2024-09-01', '2025-08-31', TRUE, TRUE, NOW(), NOW())
                    """)
                    signal_logger.info(f"TEMPLATE: Created academic year for {schema_name}")

                # Create academic settings
                cursor.execute("SELECT COUNT(*) FROM schools_academicsetting")
                if cursor.fetchone()[0] == 0:
                    cursor.execute("""
                        INSERT INTO schools_academicsetting (academic_year_id, created_at, updated_at)
                        VALUES (1, NOW(), NOW())
                    """)
                    signal_logger.info(f"TEMPLATE: Created academic settings for {schema_name}")

                # Create sequences
                for seq_table, prefix in [('schools_invoicesequence', 'INV'), ('schools_receiptsequence', 'RCP')]:
                    cursor.execute(f"SELECT COUNT(*) FROM {seq_table}")
                    if cursor.fetchone()[0] == 0:
                        cursor.execute(f"""
                            INSERT INTO {seq_table} (prefix, current_number, created_at, updated_at)
                            VALUES ('{prefix}', 1, NOW(), NOW())
                        """)
                        signal_logger.info(f"TEMPLATE: Created {seq_table} for {schema_name}")

                # Create essential reference data
                create_essential_reference_data(cursor, schema_name)

    except Exception as e:
        signal_logger.error(f"TEMPLATE: Failed to apply essential data to {schema_name}: {e}")

def create_essential_reference_data(cursor, schema_name):
    """Create essential reference data"""

    try:
        # Leave types
        cursor.execute("SELECT COUNT(*) FROM hr_leavetype")
        if cursor.fetchone()[0] == 0:
            cursor.execute("""
                INSERT INTO hr_leavetype (name, description, max_annual_days, is_paid, requires_approval, is_active, accrual_frequency, accrual_rate, prorate_accrual, created_at, updated_at) VALUES
                ('Annual Leave', 'Annual vacation leave', 21, TRUE, TRUE, TRUE, 'YEARLY', 1.75, FALSE, NOW(), NOW()),
                ('Sick Leave', 'Medical leave', 10, TRUE, FALSE, TRUE, 'YEARLY', 0.83, FALSE, NOW(), NOW()),
                ('Maternity Leave', 'Maternity leave', 90, TRUE, TRUE, TRUE, 'ONCE', 90.0, FALSE, NOW(), NOW()),
                ('Emergency Leave', 'Emergency leave', 3, FALSE, TRUE, TRUE, 'YEARLY', 0.25, FALSE, NOW(), NOW())
            """)
            signal_logger.info(f"TEMPLATE: Created leave types for {schema_name}")

        # Payment methods
        cursor.execute("SELECT COUNT(*) FROM payments_paymentmethod")
        if cursor.fetchone()[0] == 0:
            cursor.execute("""
                INSERT INTO payments_paymentmethod (name, description, type, is_active, created_at, updated_at) VALUES
                ('Cash', 'Cash payment', 'CASH', TRUE, NOW(), NOW()),
                ('Bank Transfer', 'Bank transfer payment', 'BANK_TRANSFER', TRUE, NOW(), NOW()),
                ('Mobile Money', 'Mobile money payment', 'MOBILE_MONEY', TRUE, NOW(), NOW()),
                ('Cheque', 'Cheque payment', 'CHEQUE', TRUE, NOW(), NOW())
            """)
            signal_logger.info(f"TEMPLATE: Created payment methods for {schema_name}")

        # Concession types
        cursor.execute("SELECT COUNT(*) FROM fees_concessiontype")
        if cursor.fetchone()[0] == 0:
            cursor.execute("""
                INSERT INTO fees_concessiontype (name, description, type, value, is_active, created_at, updated_at) VALUES
                ('Sibling Discount', 'Discount for siblings', 'PERCENTAGE', 10.00, TRUE, NOW(), NOW()),
                ('Staff Child Discount', 'Discount for staff children', 'PERCENTAGE', 50.00, TRUE, NOW(), NOW()),
                ('Merit Scholarship', 'Merit-based scholarship', 'PERCENTAGE', 25.00, TRUE, NOW(), NOW())
            """)
            signal_logger.info(f"TEMPLATE: Created concession types for {schema_name}")

        # Event categories
        cursor.execute("SELECT COUNT(*) FROM school_calendar_eventcategory")
        if cursor.fetchone()[0] == 0:
            cursor.execute("""
                INSERT INTO school_calendar_eventcategory (name, color, icon, description, is_active, created_at, updated_at) VALUES
                ('Academic', '#007bff', 'bi-book', 'Academic events', TRUE, NOW(), NOW()),
                ('Sports', '#28a745', 'bi-trophy', 'Sports events', TRUE, NOW(), NOW()),
                ('Cultural', '#ffc107', 'bi-music-note', 'Cultural events', TRUE, NOW(), NOW()),
                ('Meeting', '#6c757d', 'bi-people', 'Meetings', TRUE, NOW(), NOW()),
                ('Holiday', '#dc3545', 'bi-calendar-x', 'Holidays', TRUE, NOW(), NOW())
            """)
            signal_logger.info(f"TEMPLATE: Created event categories for {schema_name}")

    except Exception as e:
        signal_logger.warning(f"TEMPLATE: Error creating reference data for {schema_name}: {e}")



def create_staff_user_for_owner(tenant):
    """
    Helper function to create StaffUser for tenant owner.
    Can be called from signals or management commands.
    Uses only core fields that exist in all StaffUser schemas.
    """
    if not tenant.owner:
        signal_logger.error(f"HELPER: Tenant '{tenant.schema_name}' has no owner. Cannot create StaffUser.")
        return None, False

    owner = tenant.owner

    with schema_context(tenant.schema_name):
        try:
            from apps.schools.models import StaffUser

            staff_email = owner.email.lower()
            owner_first_name = getattr(owner, 'first_name', '') or 'School'
            owner_last_name = getattr(owner, 'last_name', '') or 'Admin'

            # Use only core fields that exist in all StaffUser schemas
            # Based on the migration file, these fields should always exist:
            core_defaults = {
                'first_name': owner_first_name,
                'last_name': owner_last_name,
                'is_staff': True,
                'is_active': True,
                'is_superuser': True,
            }

            # Add fields that exist in the migration but may not be in all schemas
            # Check each field individually to avoid errors
            try:
                # Check if we can access the field by trying to get the field from the model
                StaffUser._meta.get_field('middle_name')
                core_defaults['middle_name'] = ''
            except:
                pass  # Field doesn't exist, skip it

            try:
                StaffUser._meta.get_field('employee_id')
                core_defaults['employee_id'] = f'OWNER-{owner.pk}'
            except:
                pass

            try:
                StaffUser._meta.get_field('designation')
                core_defaults['designation'] = 'School Owner/Director'
            except:
                pass

            try:
                StaffUser._meta.get_field('is_owner_profile')
                core_defaults['is_owner_profile'] = True
            except:
                pass

            # Use raw SQL to check if user exists and create if needed
            # This bypasses Django ORM field issues
            from django.db import connection

            with connection.cursor() as cursor:
                # Check if user exists using raw SQL
                cursor.execute(
                    "SELECT id FROM schools_staffuser WHERE email = %s LIMIT 1",
                    [staff_email]
                )
                existing_user = cursor.fetchone()

                if existing_user:
                    # User exists, get the StaffUser object by ID
                    try:
                        staff_user = StaffUser.objects.get(id=existing_user[0])
                        created = False
                        signal_logger.info(f"HELPER: StaffUser '{staff_email}' already exists in tenant '{tenant.schema_name}'")
                    except Exception as get_error:
                        signal_logger.error(f"HELPER: Error getting existing StaffUser by ID {existing_user[0]}: {get_error}")
                        return None, False
                else:
                    # User doesn't exist, create using raw SQL with only core fields
                    try:
                        # Get available columns from the table
                        cursor.execute("""
                            SELECT column_name
                            FROM information_schema.columns
                            WHERE table_name = 'schools_staffuser'
                            AND table_schema = %s
                        """, [tenant.schema_name])

                        available_columns = [row[0] for row in cursor.fetchall()]

                        # Build INSERT statement with only available columns
                        insert_fields = ['email', 'first_name', 'last_name', 'is_staff', 'is_active', 'is_superuser']
                        insert_values = [staff_email, core_defaults['first_name'], core_defaults['last_name'], True, True, True]

                        # Add optional fields that exist in the database
                        for field_name, value in core_defaults.items():
                            if field_name not in insert_fields and field_name in available_columns:
                                insert_fields.append(field_name)
                                insert_values.append(value)

                        # Handle fields that might have NOT NULL constraints with defaults
                        not_null_defaults = {
                            'department': '',
                            'phone_number_primary': '',
                            'phone_number_alternate': '',
                            'address_line1': '',
                            'address_line2': '',
                            'city': '',
                            'state_province': '',
                            'postal_code': '',
                            'country': '',
                            'gender': '',
                            'marital_status': '',
                            'employment_type': '',
                            'notes': '',
                            'photo': ''
                        }

                        for field_name, default_value in not_null_defaults.items():
                            if field_name in available_columns and field_name not in insert_fields:
                                insert_fields.append(field_name)
                                insert_values.append(default_value)

                        # Add password field (required) - use proper unusable password format
                        if 'password' in available_columns:
                            insert_fields.append('password')
                            insert_values.append('!unusable_password!')  # Proper unusable password format

                        # Add date_joined if it exists
                        if 'date_joined' in available_columns:
                            insert_fields.append('date_joined')
                            insert_values.append('NOW()')
                            # Remove NOW() from values and handle it in SQL
                            insert_values[-1] = None  # Will be replaced with NOW()

                        # Build the SQL
                        placeholders = ['%s'] * len(insert_values)
                        if 'date_joined' in insert_fields:
                            # Replace the last placeholder with NOW() for date_joined
                            placeholders[insert_fields.index('date_joined')] = 'NOW()'
                            insert_values = [v for v in insert_values if v is not None]

                        sql = f"""
                            INSERT INTO schools_staffuser ({', '.join(insert_fields)})
                            VALUES ({', '.join(placeholders)})
                            RETURNING id
                        """

                        cursor.execute(sql, insert_values)
                        new_user_id = cursor.fetchone()[0]

                        # Get the created StaffUser object
                        staff_user = StaffUser.objects.get(id=new_user_id)
                        created = True
                        signal_logger.info(f"HELPER: Created StaffUser '{staff_email}' for tenant '{tenant.schema_name}' using raw SQL")

                    except Exception as create_error:
                        signal_logger.error(f"HELPER: Error creating StaffUser with raw SQL: {create_error}")
                        return None, False

            if created:
                staff_user.set_unusable_password()
                staff_user.save()
                signal_logger.info(f"HELPER: Created StaffUser '{staff_email}' for tenant '{tenant.schema_name}'")

                # Create admin group and assign user
                admin_group_name = "School Administrators"
                admin_group, group_created = Group.objects.get_or_create(name=admin_group_name)
                staff_user.groups.add(admin_group)

                if group_created:
                    signal_logger.info(f"HELPER: Created admin group '{admin_group_name}' in tenant '{tenant.schema_name}'")
            else:
                signal_logger.info(f"HELPER: StaffUser '{staff_email}' already exists in tenant '{tenant.schema_name}'")

            return staff_user, created

        except Exception as e:
            signal_logger.error(f"HELPER: Error creating StaffUser for tenant '{tenant.schema_name}': {e}", exc_info=True)
            return None, False


@receiver(post_schema_sync, sender=Tenant)
def create_starter_coa_for_tenant(sender, instance, **kwargs):
    tenant = instance
    signal_logger.critical("!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!") # For CoA signal entry
    signal_logger.critical("COA_SIGNAL: TOP OF FUNCTION REACHED!")
    signal_logger.critical(f"COA_SIGNAL: Tenant instance: {instance.schema_name if instance else 'No Instance'}")
    signal_logger.critical("!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!")
    
    signal_logger.info(f"COA_SIGNAL: --- Entered create_starter_coa_for_tenant for tenant '{tenant.schema_name}' (PK: {tenant.pk}) ---") # Changed logger

    if tenant.schema_name == get_public_schema_name():
        signal_logger.info(f"COA_SIGNAL: Skipping CoA creation for public schema '{tenant.schema_name}'.") # Changed logger
        return

    if not hasattr(tenant, 'owner') or not tenant.owner:
        signal_logger.warning(f"COA_SIGNAL: Tenant '{tenant.schema_name}' has no owner. Proceeding with CoA creation.") # Changed logger
    
    signal_logger.info(f"COA_SIGNAL: Preparing to create starter Chart of Accounts for tenant '{tenant.schema_name}'.") # Changed logger

    account_type_map = _fetch_account_types_from_public_schema() # Uses signal_logger internally now
    if not account_type_map:
        return

    _create_accounts_in_tenant_schema(tenant, account_type_map) # Uses signal_logger internally now
    
    signal_logger.info(f"COA_SIGNAL: --- Exiting create_starter_coa_for_tenant for tenant '{tenant.schema_name}' ---") # Changed logger


def _fetch_account_types_from_public_schema():
    account_type_map = {}
    public_schema_name = get_public_schema_name()
    signal_logger.info(f"COA_SIGNAL: Attempting to fetch AccountTypes from public schema ('{public_schema_name}')...") # Changed logger
    try:
        from apps.accounting.models import AccountType
        with schema_context(public_schema_name):
            all_account_types = AccountType.objects.all()
            if not all_account_types.exists():
                signal_logger.error("COA_SIGNAL: CRITICAL - No AccountTypes found in public schema. Please seed AccountTypes via data migration.") # Changed logger
            else:
                account_type_map = {at.name.lower(): at for at in all_account_types}
                signal_logger.info(f"COA_SIGNAL: Fetched {len(account_type_map)} AccountTypes from '{public_schema_name}'. Keys: {list(account_type_map.keys())}") # Changed logger
    except ImportError:
        signal_logger.error("COA_SIGNAL: CRITICAL - Could not import AccountType model. Ensure 'apps.accounting' is correctly set up.", exc_info=True) # Changed logger
    except Exception as e:
        signal_logger.error(f"COA_SIGNAL: CRITICAL - Error fetching AccountTypes from public schema: {e}", exc_info=True) # Changed logger
    return account_type_map

def _verify_schema_context(tenant):
    current_schema = getattr(connection, 'schema_name', None)
    if current_schema != tenant.schema_name:
        signal_logger.error(f"COA_SIGNAL: Schema context mismatch! Expected '{tenant.schema_name}' but connection is on '{current_schema}'. Attempting to reset.") # Changed logger
        try:
            connection.set_tenant(tenant)
            current_schema = getattr(connection, 'schema_name', None)
            signal_logger.info(f"COA_SIGNAL: Schema context after reset: {current_schema}") # Changed logger
            if current_schema != tenant.schema_name:
                signal_logger.error(f"COA_SIGNAL: CRITICAL - Failed to correctly set schema context to '{tenant.schema_name}'. Aborting CoA creation.") # Changed logger
                return False
        except Exception as e:
            signal_logger.error(f"COA_SIGNAL: Error setting tenant context: {e}", exc_info=True) # Changed logger
            return False
    return True

def _validate_template_data(template_data): # This should be defined BEFORE _create_accounts_in_tenant_schema
    required_fields = ['code', 'name', 'account_type_name']
    for acc_data in template_data:
        missing_fields = [field for field in required_fields if not acc_data.get(field)]
        if missing_fields:
            signal_logger.error(f"COA_SIGNAL: Template validation failed. Missing fields {missing_fields} in: {acc_data}") # Changed logger
            return False
    return True

def _create_accounts_in_tenant_schema(tenant, account_type_map):
    with schema_context(tenant.schema_name):
        signal_logger.info(f"COA_SIGNAL: Switched to schema context: '{tenant.schema_name}' for Account creation.") # Changed logger
        if not _verify_schema_context(tenant): return
        try:
            from apps.accounting.models import Account
            from apps.accounting.coa_template import STANDARD_CHART_OF_ACCOUNTS
            signal_logger.info(f"COA_SIGNAL: Successfully imported Account model and STANDARD_CHART_OF_ACCOUNTS (Item count: {len(STANDARD_CHART_OF_ACCOUNTS)}).") # Changed logger
            if not STANDARD_CHART_OF_ACCOUNTS:
                signal_logger.error("COA_SIGNAL: CRITICAL - STANDARD_CHART_OF_ACCOUNTS is empty! Cannot create CoA.") # Changed logger
                return
            if not _validate_template_data(STANDARD_CHART_OF_ACCOUNTS): # This function needs to be defined before this call
                return
            created_accounts_by_code = {}
            _create_top_level_accounts(STANDARD_CHART_OF_ACCOUNTS, account_type_map, created_accounts_by_code, tenant)
            _create_child_accounts(STANDARD_CHART_OF_ACCOUNTS, account_type_map, created_accounts_by_code, tenant)
            _rebuild_mptt_tree(Account, tenant.schema_name)
        except ImportError as e:
            signal_logger.error(f"COA_SIGNAL: CRITICAL - Model or CoA Template import error in tenant '{tenant.schema_name}': {e}", exc_info=True) # Changed logger
        except Exception as e:
            signal_logger.error(f"COA_SIGNAL: General error creating starter CoA for tenant '{tenant.schema_name}': {e}", exc_info=True) # Changed logger

def _create_top_level_accounts(template_data, account_type_map, created_accounts_by_code, tenant_instance):
    from apps.accounting.models import Account
    signal_logger.info(f"COA_SIGNAL: Starting Pass 1 - Creating top-level accounts for tenant '{tenant_instance.schema_name}'.") # Changed logger
    for acc_data in template_data:
        if acc_data.get("parent_code") is None:
            account = _create_single_account(acc_data, account_type_map, None, "L1", tenant_instance)
            if account: created_accounts_by_code[account.code] = account

def _create_child_accounts(template_data, account_type_map, created_accounts_by_code, tenant_instance):
    accounts_to_create = [data for data in template_data if data.get("parent_code") is not None]
    max_passes = 10; current_pass = 0
    signal_logger.info(f"COA_SIGNAL: Starting child account creation for tenant '{tenant_instance.schema_name}'. Max passes: {max_passes}. Items: {len(accounts_to_create)}") # Changed logger
    while accounts_to_create and current_pass < max_passes:
        current_pass += 1; processed_count = 0; remaining_accounts = []
        signal_logger.info(f"  COA_SIGNAL_CHILD: Pass {current_pass} for tenant '{tenant_instance.schema_name}'. Items remaining: {len(accounts_to_create)}") # Changed logger
        for acc_data in accounts_to_create:
            parent_code = acc_data.get("parent_code")
            if parent_code in created_accounts_by_code:
                parent_account = created_accounts_by_code[parent_code]
                account = _create_single_account(acc_data, account_type_map, parent_account, "CHILD", tenant_instance)
                if account: created_accounts_by_code[account.code] = account; processed_count += 1
                else: remaining_accounts.append(acc_data)
            else: remaining_accounts.append(acc_data)
        accounts_to_create = remaining_accounts
        if processed_count == 0 and accounts_to_create:
            remaining_codes = [item.get('code') for item in accounts_to_create]
            signal_logger.error(f"COA_SIGNAL: STUCK in child account creation. No accounts processed in pass {current_pass}. Remaining codes: {remaining_codes}. Check coa_template.py.") # Changed logger
            break
    if not accounts_to_create: signal_logger.info(f"COA_SIGNAL: Successfully processed all starter Chart of Accounts for tenant '{tenant_instance.schema_name}'.") # Changed logger
    else: signal_logger.warning(f"COA_SIGNAL: Some accounts could not be created for tenant '{tenant_instance.schema_name}'. Unresolved items: {len(accounts_to_create)}") # Changed logger

def _create_single_account(acc_data, account_type_map, parent_account, log_prefix, tenant_instance):
    from apps.accounting.models import Account
    code = acc_data.get("code"); name = acc_data.get("name"); account_type_name = acc_data.get("account_type_name")
    if not all([code, name, account_type_name]):
        signal_logger.warning(f"  COA_SIGNAL_{log_prefix}: Invalid data (missing required fields) for tenant '{tenant_instance.schema_name}': {acc_data}. Skipping.") # Changed logger
        return None
    account_type_instance = account_type_map.get(account_type_name.lower())
    if not account_type_instance:
        signal_logger.warning(f"  COA_SIGNAL_{log_prefix}: AccountType '{account_type_name}' not found for account {code} ('{name}') for tenant '{tenant_instance.schema_name}'. Skipping.") # Changed logger
        return None
    try:
        account, created = Account.objects.get_or_create(
            tenant=tenant_instance, code=code,
            defaults={'name': name, 'account_type': account_type_instance, 'parent_account': parent_account,
                    'is_control_account': acc_data.get("is_control_account", False),
                    'description': acc_data.get("description", ""), 'is_active': True,})
        if created:
            parent_info = f" (Parent: {parent_account.code})" if parent_account else ""
            signal_logger.info(f"  COA_SIGNAL_{log_prefix}: CREATED: {account.code} - {account.name} for Tenant '{tenant_instance.schema_name}'{parent_info}") # Changed logger
        return account
    except Exception as e:
        signal_logger.error(f"  COA_SIGNAL_{log_prefix}: ERROR creating/finding {code} ('{name}') for Tenant '{tenant_instance.schema_name}': {e}", exc_info=True) # Changed logger
        return None

def _rebuild_mptt_tree(Account, schema_name):
    if Account.objects.exists():
        try:
            signal_logger.info(f"COA_SIGNAL: Attempting to rebuild MPTT tree for Account model in tenant '{schema_name}'.") # Changed logger
            Account.objects.rebuild()
            signal_logger.info(f"COA_SIGNAL: MPTT tree rebuild completed for tenant '{schema_name}'.") # Changed logger
        except Exception as e:
            signal_logger.error(f"COA_SIGNAL: Error during MPTT tree rebuild for tenant '{schema_name}': {e}", exc_info=True) # Changed logger


