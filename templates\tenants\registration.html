{# D:\school_fees_saas_v2\templates\tenants\registration.html #}
{% extends "public_base.html" %}
{% load static i18n widget_tweaks tenant_extras %}

{% block title %}{% trans "Register Your School" %}{% endblock %}

{% block extra_public_css %}
<style>
    /* Premium Registration Form Styling */
    .premium-registration-container {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 2rem 0;
    }

    .premium-registration-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 24px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .premium-registration-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
    }

    .premium-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 3rem 2rem;
        text-align: center;
        position: relative;
        overflow: hidden;
    }

    .premium-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        opacity: 0.3;
    }

    .premium-header h1 {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        position: relative;
        z-index: 1;
    }

    .premium-header p {
        font-size: 1.1rem;
        opacity: 0.9;
        margin-bottom: 0;
        position: relative;
        z-index: 1;
    }

    .premium-form-container {
        padding: 3rem;
    }

    .premium-section {
        margin-bottom: 2.5rem;
    }

    .premium-section-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: #2d3748;
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .premium-section-title i {
        color: #667eea;
        font-size: 1.3rem;
    }

    .premium-form-group {
        margin-bottom: 1.5rem;
    }

    .premium-label {
        font-size: 0.95rem;
        font-weight: 600;
        color: #4a5568;
        margin-bottom: 0.5rem;
        display: block;
    }

    .premium-input {
        border: 2px solid #e2e8f0;
        border-radius: 12px;
        padding: 0.875rem 1rem;
        font-size: 1rem;
        transition: all 0.3s ease;
        background: #ffffff;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
        width: 100%;
    }

    .premium-input:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        outline: none;
        transform: translateY(-1px);
    }

    .premium-help-text {
        font-size: 0.85rem;
        color: #718096;
        margin-top: 0.5rem;
        font-style: italic;
    }

    .premium-plan-card {
        border: 2px solid #e2e8f0;
        border-radius: 16px;
        padding: 1.5rem;
        margin-bottom: 1rem;
        transition: all 0.3s ease;
        background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
    }

    .premium-plan-card:hover {
        border-color: #667eea;
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.1);
        transform: translateY(-2px);
    }

    .premium-plan-card input[type="radio"]:checked + label .premium-plan-card {
        border-color: #667eea;
        background: linear-gradient(145deg, #f7fafc 0%, #edf2f7 100%);
    }

    .premium-checkbox-container {
        display: flex;
        align-items: flex-start;
        gap: 0.75rem;
        padding: 1rem;
        border: 1px solid #e2e8f0;
        border-radius: 12px;
        background: #f8fafc;
        transition: all 0.3s ease;
    }

    .premium-checkbox-container:hover {
        background: #f1f5f9;
        border-color: #cbd5e0;
    }

    .premium-checkbox {
        width: 18px;
        height: 18px;
        accent-color: #667eea;
        cursor: pointer;
        margin: 0;
    }

    .premium-checkbox-label {
        font-size: 0.9rem;
        color: #4a5568;
        cursor: pointer;
        line-height: 1.5;
        margin: 0;
    }

    .premium-checkbox-label a {
        color: #667eea;
        text-decoration: none;
        font-weight: 500;
    }

    .premium-checkbox-label a:hover {
        color: #5a67d8;
        text-decoration: underline;
    }

    .premium-btn {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        border-radius: 12px;
        padding: 1rem 2rem;
        font-size: 1.1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        width: 100%;
    }

    .premium-btn:hover {
        background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        color: white;
    }

    .premium-btn:active {
        transform: translateY(0);
    }

    .premium-error {
        color: #e53e3e;
        font-size: 0.85rem;
        margin-top: 0.5rem;
        font-weight: 500;
    }

    .premium-alert {
        background: linear-gradient(135deg, #fed7d7 0%, #feb2b2 100%);
        border: 1px solid #fc8181;
        border-radius: 12px;
        padding: 1rem;
        margin-bottom: 1.5rem;
        color: #742a2a;
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .premium-fade-in {
        animation: fadeInUp 0.6s ease-out;
    }

    @media (max-width: 768px) {
        .premium-registration-container {
            padding: 1rem 0;
        }

        .premium-form-container {
            padding: 2rem 1.5rem;
        }

        .premium-header h1 {
            font-size: 2rem;
        }

        .premium-header {
            padding: 2rem 1.5rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="premium-registration-container">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10 col-xl-8">
                <div class="premium-registration-card premium-fade-in">
                    <div class="premium-header">
                        <h1><i class="bi bi-mortarboard-fill me-3"></i>Register Your School</h1>
                        <p>Join thousands of schools managing fees effortlessly</p>
                    </div>
                    <div class="premium-form-container">
                        {% if messages %}
                            {% for message in messages %}
                                <div class="premium-alert">
                                    <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                    {{ message }}
                                </div>
                            {% endfor %}
                        {% endif %}
                        {% if form.non_field_errors %}
                            <div class="premium-alert">
                                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                {{ form.non_field_errors|striptags }}
                            </div>
                        {% endif %}

                        <form method="post" novalidate>
                            {% csrf_token %}

                            {# School Information Section #}
                            <div class="premium-section">
                                <h3 class="premium-section-title">
                                    <i class="bi bi-building"></i>
                                    {% trans "School Information" %}
                                </h3>

                                <div class="premium-form-group">
                                    <label for="{{ form.school_name.id_for_label }}" class="premium-label">{{ form.school_name.label }}</label>
                                    <input type="text" name="{{ form.school_name.name }}" id="{{ form.school_name.id_for_label }}"
                                           class="premium-input" placeholder="{{ form.school_name.field.widget.attrs.placeholder }}"
                                           value="{{ form.school_name.value|default:'' }}" required>
                                    {% if form.school_name.errors %}
                                        <div class="premium-error">{{ form.school_name.errors.0 }}</div>
                                    {% endif %}
                                </div>

                                <div class="premium-form-group">
                                    <label for="{{ form.subdomain.id_for_label }}" class="premium-label">{{ form.subdomain.label }}</label>
                                    <input type="text" name="{{ form.subdomain.name }}" id="{{ form.subdomain.id_for_label }}"
                                           class="premium-input" placeholder="{{ form.subdomain.field.widget.attrs.placeholder }}"
                                           value="{{ form.subdomain.value|default:'' }}" required>
                                    {% if form.subdomain.help_text %}
                                        <div class="premium-help-text">{{ form.subdomain.help_text }}</div>
                                    {% endif %}
                                    {% if form.subdomain.errors %}
                                        <div class="premium-error">{{ form.subdomain.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>

                            {# Administrator Account Section #}
                            <div class="premium-section">
                                <h3 class="premium-section-title">
                                    <i class="bi bi-person-badge"></i>
                                    {% trans "Administrator Account" %}
                                </h3>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="premium-form-group">
                                            <label for="{{ form.admin_first_name.id_for_label }}" class="premium-label">{{ form.admin_first_name.label }}</label>
                                            <input type="text" name="{{ form.admin_first_name.name }}" id="{{ form.admin_first_name.id_for_label }}"
                                                   class="premium-input" value="{{ form.admin_first_name.value|default:'' }}" required>
                                            {% if form.admin_first_name.errors %}
                                                <div class="premium-error">{{ form.admin_first_name.errors.0 }}</div>
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="premium-form-group">
                                            <label for="{{ form.admin_last_name.id_for_label }}" class="premium-label">{{ form.admin_last_name.label }}</label>
                                            <input type="text" name="{{ form.admin_last_name.name }}" id="{{ form.admin_last_name.id_for_label }}"
                                                   class="premium-input" value="{{ form.admin_last_name.value|default:'' }}" required>
                                            {% if form.admin_last_name.errors %}
                                                <div class="premium-error">{{ form.admin_last_name.errors.0 }}</div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>

                                <div class="premium-form-group">
                                    <label for="{{ form.admin_email.id_for_label }}" class="premium-label">{{ form.admin_email.label }}</label>
                                    <input type="email" name="{{ form.admin_email.name }}" id="{{ form.admin_email.id_for_label }}"
                                           class="premium-input" value="{{ form.admin_email.value|default:'' }}" required autocomplete="email">
                                    {% if form.admin_email.errors %}
                                        <div class="premium-error">{{ form.admin_email.errors.0 }}</div>
                                    {% endif %}
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="premium-form-group">
                                            <label for="{{ form.admin_password1.id_for_label }}" class="premium-label">{{ form.admin_password1.label }}</label>
                                            <input type="password" name="{{ form.admin_password1.name }}" id="{{ form.admin_password1.id_for_label }}"
                                                   class="premium-input" required autocomplete="new-password">
                                            {% if form.admin_password1.errors %}
                                                <div class="premium-error">{{ form.admin_password1.errors.0 }}</div>
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="premium-form-group">
                                            <label for="{{ form.admin_password2.id_for_label }}" class="premium-label">{{ form.admin_password2.label }}</label>
                                            <input type="password" name="{{ form.admin_password2.name }}" id="{{ form.admin_password2.id_for_label }}"
                                                   class="premium-input" required>
                                            {% if form.admin_password2.errors %}
                                                <div class="premium-error">{{ form.admin_password2.errors.0 }}</div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            {# Plan Selection Section #}
                            <div class="premium-section">
                                <h3 class="premium-section-title">
                                    <i class="bi bi-star"></i>
                                    {% trans "Choose Your Plan" %}
                                </h3>

                                {% if form.plan.errors %}
                                    <div class="premium-error">{{ form.plan.errors.0 }}</div>
                                {% endif %}

                                {% for radio in form.plan %}
                                <div class="premium-plan-card">
                                    {{ radio.tag }}
                                    <label for="{{ radio.id_for_label }}" class="form-check-label w-100" style="cursor: pointer;">
                                        <div class="d-flex justify-content-between align-items-start">
                                            <div>
                                                <h5 class="mb-2" style="color: #2d3748;">{{ radio.choice_label }}</h5>
                                                {% with plan_instance=radio.choice_value.instance %}
                                                    <p class="text-muted mb-2">{{ plan_instance.description|truncatewords:15 }}</p>
                                                    {% if plan_instance.trial_period_days > 0 %}
                                                        <div class="badge bg-success mb-2">
                                                            {% blocktrans with days=plan_instance.trial_period_days %}{{ days }}-day free trial{% endblocktrans %}
                                                        </div>
                                                    {% endif %}
                                                    <ul class="list-unstyled small">
                                                        {% for feature_item in plan_instance.features.all|slice:":3" %}
                                                            <li class="mb-1"><i class="bi bi-check-circle text-success me-2"></i>{{ feature_item.name }}</li>
                                                        {% empty %}
                                                            <li class="mb-1"><i class="bi bi-check-circle text-success me-2"></i>{% trans "Core Features" %}</li>
                                                        {% endfor %}
                                                        {% if plan_instance.features.all.count > 3 %}
                                                            <li class="text-muted"><em>+ {% trans "more features" %}...</em></li>
                                                        {% endif %}
                                                    </ul>
                                                {% endwith %}
                                            </div>
                                            <div class="text-end">
                                                {% with plan_instance=radio.choice_value.instance %}
                                                    {% if plan_instance.price > 0 %}
                                                        <div class="h4 mb-0" style="color: #667eea;">${{ plan_instance.price|floatformat:0 }}</div>
                                                        <small class="text-muted">per month</small>
                                                    {% else %}
                                                        <div class="h4 mb-0" style="color: #48bb78;">{% trans "Free" %}</div>
                                                        <small class="text-muted">forever</small>
                                                    {% endif %}
                                                {% endwith %}
                                            </div>
                                        </div>
                                    </label>
                                </div>
                                {% endfor %}
                            </div>

                            {# Billing Cycle Section #}
                            <div class="premium-section">
                                <h3 class="premium-section-title">
                                    <i class="bi bi-calendar-month"></i>
                                    {% trans "Billing Cycle" %}
                                </h3>

                                {% if form.billing_cycle.errors %}
                                    <div class="premium-error">{{ form.billing_cycle.errors.0 }}</div>
                                {% endif %}

                                {% for radio_cycle in form.billing_cycle %}
                                <div class="premium-plan-card" style="padding: 1rem;">
                                    {{ radio_cycle.tag }}
                                    <label for="{{ radio_cycle.id_for_label }}" class="form-check-label w-100" style="cursor: pointer;">
                                        <strong>{{ radio_cycle.choice_label }}</strong>
                                        {% if radio_cycle.choice_value == 'yearly' %}
                                            <span class="badge bg-success ms-2">Save 20%</span>
                                        {% endif %}
                                    </label>
                                </div>
                                {% endfor %}
                            </div>

                            {# Terms Agreement Section #}
                            <div class="premium-section">
                                <div class="premium-checkbox-container">
                                    <input type="checkbox" name="{{ form.agree_to_terms.name }}" id="{{ form.agree_to_terms.id_for_label }}"
                                           class="premium-checkbox" required>
                                    <label for="{{ form.agree_to_terms.id_for_label }}" class="premium-checkbox-label">
                                        I agree to the <a href="{% url 'public_site:terms' %}" target="_blank">Terms of Service</a>
                                        and <a href="{% url 'public_site:privacy' %}" target="_blank">Privacy Policy</a>.
                                    </label>
                                </div>
                                {% if form.agree_to_terms.errors %}
                                    <div class="premium-error">{{ form.agree_to_terms.errors.0 }}</div>
                                {% endif %}
                            </div>

                            {# Submit Button #}
                            <div class="text-center">
                                <button type="submit" class="premium-btn">
                                    <i class="bi bi-rocket-takeoff me-2"></i>
                                    {% trans "Launch My School Portal" %}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock content %}
