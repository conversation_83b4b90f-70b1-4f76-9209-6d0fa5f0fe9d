{# D:\school_fees_saas_v2\apps\hr\templates\hr\hr_dashboard.html #}
{% extends "tenant_base.html" %}
{% load static i18n %}

{% block tenant_page_title %}{{ view_title }}{% endblock %}

{% block tenant_specific_content %}
<div class="container-fluid mt-4">

    <div class="pagetitle mb-3">
        <h1>{{ view_title }}</h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'schools:dashboard' %}">{% trans "Dashboard" %}</a></li>
                <li class="breadcrumb-item active" aria-current="page">{% trans "HR" %}</li>
            </ol>
        </nav>
    </div>

    {% include "partials/_messages.html" %}

    <div class="row">
        {# Card for Staff Management #}
        <div class="col-md-6 col-lg-4 mb-4">
            <div class="card h-100">
                <div class="card-body text-center p-4">
                    <i class="bi bi-people-fill display-4 text-primary mb-3"></i>
                    <h5 class="card-title">{% trans "Staff Management" %}</h5>
                    <p class="card-text text-muted">{% trans "Add, view, and manage all staff user accounts and their roles." %}</p>
                    <a href="{% url 'schools:staff_list' %}" class="btn btn-primary mt-3">{% trans "Go to Staff List" %}</a>
                </div>
            </div>
        </div>

        {# Card for Leave Management #}
        <div class="col-md-6 col-lg-4 mb-4">
            <div class="card h-100">
                <div class="card-body text-center p-4">
                    <i class="bi bi-calendar2-week-fill display-4 text-info mb-3"></i>
                    <h5 class="card-title">{% trans "Leave Management" %}</h5>
                    <p class="card-text text-muted">{% trans "Administer and review staff leave requests and manage leave types." %}</p>
                    <a href="{% url 'hr:admin_leaverequest_list' %}" class="btn btn-info">{% trans "Review Requests" %}</a>
                </div>
            </div>
        </div>

        {# Card for Payroll Management #}
        <div class="col-md-6 col-lg-4 mb-4">
            <div class="card h-100">
                <div class="card-body text-center p-4">
                    <i class="bi bi-calculator-fill display-4 text-success mb-3"></i>
                    <h5 class="card-title">{% trans "Payroll" %}</h5>
                    <p class="card-text text-muted">{% trans "Process monthly payroll, generate payslips, and view payment history." %}</p>
                    <a href="{% url 'hr:payroll_run_list' %}" class="btn btn-success">{% trans "Manage Payroll" %}</a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock tenant_specific_content %}



