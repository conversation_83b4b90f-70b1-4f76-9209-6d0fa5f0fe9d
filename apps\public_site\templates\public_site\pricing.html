{% extends "public_base.html" %}
{% load static i18n humanize %}

{% block public_page_title %}{{ view_title|default:"Pricing Plans" }}{% endblock %}

{% block extra_public_css %}
    {{ block.super }}
    <style>
        .card.pricing {
            border: 1px solid #dee2e6;
            transition: all 0.2s;
        }
        .card.pricing:hover {
            margin-top: -.25rem;
            margin-bottom: .25rem;
            box-shadow: 0 0.5rem 1rem 0 rgba(0, 0, 0, 0.1);
        }
        .card.pricing .card-header {
            background-color: #f8f9fa;
        }
        .card-price {
            font-size: 3rem;
            margin: 0;
        }
        .card-price .period {
            font-size: 0.8rem;
            color: #6c757d;
        }
        .card-body ul li {
            margin-bottom: 0.5rem;
        }
    </style>
{% endblock %}


{% block content %}
<div class="container py-5">
    <div class="text-center mb-5">
        <h1 class="display-4 fw-bold">{{ view_title|default:"Our Pricing Plans" }}</h1>
        <p class="lead text-muted">{% trans "Choose the plan that's right for your institution. No hidden fees." %}</p>
    </div>

    <div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4 justify-content-center">
        {% for plan in plans %}
        <div class="col">
            <div class="card pricing h-100 shadow-sm">
                <div class="card-header py-3">
                    <h4 class="my-0 fw-normal">{{ plan.name }}</h4>
                </div>
                <div class="card-body d-flex flex-column">
                    <h1 class="card-price text-center">
                        {{ school_profile.currency_symbol|default:'$' }}{{ plan.price_monthly|floatformat:0|intcomma }}<span class="period">/month</span>
                    </h1>
                    <hr>
                    <ul class="list-unstyled mt-3 mb-4">
                        {# This is a key feature based on your new model #}
                        <li class="mb-2">
                            <i class="bi bi-people-fill text-success me-2"></i>
                            <strong>Up to {{ plan.max_students|intcomma }}</strong> students
                        </li>
                        
                        {# Now we loop through the prefetched features #}
                        {% for feature in plan.features.all %}
                            <li class="mb-2">
                                <i class="bi bi-check-lg text-success me-2"></i>{{ feature.name }}
                            </li>
                        {% endfor %}
                    </ul>
                    <div class="d-grid mt-auto">
                        {# This link correctly passes the plan's ID to the registration page #}
                        <a href="{% url 'tenants:register_school' %}?plan={{ plan.pk }}" class="btn btn-primary btn-lg text-uppercase">
                            {% trans "Get Started" %}
                        </a>
                    </div>
                </div>
            </div>
        </div>
        {% empty %}
            <div class="col-12">
                <div class="alert alert-warning text-center">
                    {% trans "No subscription plans are currently available. Please check back later or contact support." %}
                </div>
            </div>
        {% endfor %}
    </div>
</div>
{% endblock content %}


