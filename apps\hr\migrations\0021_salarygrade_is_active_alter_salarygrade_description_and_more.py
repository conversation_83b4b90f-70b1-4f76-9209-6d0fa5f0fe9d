# Generated by Django 5.1.9 on 2025-07-09 20:03

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('hr', '0020_alter_staffsalarycomponent_unique_together_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='salarygrade',
            name='is_active',
            field=models.BooleanField(default=True),
        ),
        migrations.AlterField(
            model_name='salarygrade',
            name='description',
            field=models.TextField(blank=True, default=''),
        ),
        migrations.AlterField(
            model_name='salarygrade',
            name='name',
            field=models.CharField(max_length=100, verbose_name='Grade Name'),
        ),
        # Skip AlterUniqueTogether for leavebalance - constraint already handled in previous migrations
        migrations.AlterUniqueTogether(
            name='salarygrade',
            unique_together={('name',)},
        ),
        migrations.CreateModel(
            name='PayslipLineItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('type', models.CharField(choices=[('EARNING', 'Earning'), ('DEDUCTION', 'Deduction')], max_length=10)),
                ('amount', models.DecimalField(decimal_places=2, max_digits=12)),
                ('payslip', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='line_items', to='hr.payslip')),
                ('source_component', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='hr.salarycomponent')),
            ],
            options={
                'ordering': ['type', 'name'],
            },
        ),
    ]
