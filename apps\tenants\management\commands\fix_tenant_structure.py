"""
Fix Tenant Structure
Comprehensive command to fix any tenant structure issues

Usage: 
python manage.py fix_tenant_structure --all
python manage.py fix_tenant_structure --tenant=mandiva
python manage.py fix_tenant_structure --check-only
"""

from django.core.management.base import BaseCommand
from django.db import connection
from django.core.management import call_command
from apps.tenants.models import School
from django_tenants.utils import schema_context
import logging

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Fix tenant structure issues comprehensively'

    def add_arguments(self, parser):
        parser.add_argument(
            '--all',
            action='store_true',
            help='Fix all tenant schemas',
        )
        parser.add_argument(
            '--tenant',
            type=str,
            help='Fix specific tenant schema',
        )
        parser.add_argument(
            '--check-only',
            action='store_true',
            help='Only check for issues, do not fix',
        )

    def handle(self, *args, **options):
        if options['all']:
            self.fix_all_tenants(options['check_only'])
        elif options['tenant']:
            self.fix_specific_tenant(options['tenant'], options['check_only'])
        elif options['check_only']:
            self.check_all_tenants()
        else:
            self.stdout.write("Use --all, --tenant=<name>, or --check-only")

    def fix_all_tenants(self, check_only=False):
        """Fix all tenant schemas"""
        
        self.stdout.write("=== FIXING ALL TENANT STRUCTURES ===")
        
        try:
            # Get all tenants except public
            tenants = School.objects.exclude(schema_name='public')
            
            for tenant in tenants:
                self.stdout.write(f"\n--- Processing {tenant.schema_name} ---")
                self.fix_tenant_structure(tenant, check_only)
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"Failed to process tenants: {e}")
            )

    def fix_specific_tenant(self, tenant_name, check_only=False):
        """Fix specific tenant schema"""
        
        self.stdout.write(f"=== FIXING {tenant_name.upper()} STRUCTURE ===")
        
        try:
            tenant = School.objects.get(schema_name=tenant_name)
            self.fix_tenant_structure(tenant, check_only)
            
        except School.DoesNotExist:
            self.stdout.write(
                self.style.ERROR(f"Tenant '{tenant_name}' not found")
            )
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"Failed to fix {tenant_name}: {e}")
            )

    def check_all_tenants(self):
        """Check all tenants for issues"""
        
        self.stdout.write("=== CHECKING ALL TENANT STRUCTURES ===")
        
        try:
            tenants = School.objects.exclude(schema_name='public')
            
            for tenant in tenants:
                self.stdout.write(f"\n--- Checking {tenant.schema_name} ---")
                issues = self.check_tenant_issues(tenant)
                
                if issues:
                    self.stdout.write(
                        self.style.WARNING(f"Issues found in {tenant.schema_name}:")
                    )
                    for issue in issues:
                        self.stdout.write(f"  - {issue}")
                else:
                    self.stdout.write(
                        self.style.SUCCESS(f"✅ {tenant.schema_name} looks good")
                    )
                    
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"Failed to check tenants: {e}")
            )

    def fix_tenant_structure(self, tenant, check_only=False):
        """Fix structure for a specific tenant"""
        
        try:
            with schema_context(tenant.schema_name):
                # Check for issues first
                issues = self.check_tenant_issues(tenant)
                
                if not issues:
                    self.stdout.write(
                        self.style.SUCCESS(f"✅ {tenant.schema_name} has no issues")
                    )
                    return
                
                self.stdout.write(f"Found {len(issues)} issues in {tenant.schema_name}")
                
                if check_only:
                    for issue in issues:
                        self.stdout.write(f"  - {issue}")
                    return
                
                # Fix issues
                self.stdout.write(f"Fixing issues in {tenant.schema_name}...")
                
                # 1. Run migrations to ensure all tables exist
                self.stdout.write("  Running migrations...")
                call_command('migrate', verbosity=0, interactive=False)
                
                # 2. Fix missing columns
                self.fix_missing_columns(tenant)
                
                # 3. Create essential data
                self.create_essential_data(tenant)
                
                # 4. Verify fixes
                remaining_issues = self.check_tenant_issues(tenant)
                
                if remaining_issues:
                    self.stdout.write(
                        self.style.WARNING(f"⚠️  {len(remaining_issues)} issues remain in {tenant.schema_name}")
                    )
                    for issue in remaining_issues:
                        self.stdout.write(f"  - {issue}")
                else:
                    self.stdout.write(
                        self.style.SUCCESS(f"✅ All issues fixed in {tenant.schema_name}")
                    )
                    
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"Failed to fix {tenant.schema_name}: {e}")
            )

    def check_tenant_issues(self, tenant):
        """Check for common tenant issues"""
        
        issues = []
        
        try:
            with schema_context(tenant.schema_name):
                with connection.cursor() as cursor:
                    
                    # Check for missing essential tables
                    essential_tables = [
                        'schools_academicyear', 'schools_term', 'schools_schoolclass', 'schools_section',
                        'fees_feehead', 'fees_feestructure', 'fees_feestructure_applicable_classes',
                        'fees_invoice', 'fees_invoicedetail', 'payments_payment', 'payments_paymentallocation',
                        'hr_employeeprofile', 'hr_leavebalance', 'hr_leaverequest', 'hr_leavetype',
                        'payments_paymentmethod', 'fees_concessiontype', 'school_calendar_eventcategory'
                    ]
                    
                    for table in essential_tables:
                        cursor.execute(f"""
                            SELECT EXISTS (
                                SELECT FROM information_schema.tables 
                                WHERE table_schema = '{tenant.schema_name}' 
                                AND table_name = '{table}'
                            )
                        """)
                        
                        if not cursor.fetchone()[0]:
                            issues.append(f"Missing table: {table}")
                    
                    # Check for missing essential data
                    data_checks = [
                        ('schools_academicyear', 'Academic years'),
                        ('hr_leavetype', 'Leave types'),
                        ('payments_paymentmethod', 'Payment methods'),
                        ('fees_concessiontype', 'Concession types'),
                        ('school_calendar_eventcategory', 'Event categories')
                    ]
                    
                    for table, description in data_checks:
                        try:
                            cursor.execute(f"SELECT COUNT(*) FROM {table}")
                            count = cursor.fetchone()[0]
                            if count == 0:
                                issues.append(f"No data in {description} ({table})")
                        except:
                            # Table doesn't exist, already caught above
                            pass
                    
                    # Check for missing columns in key tables
                    column_checks = [
                        ('schools_staffuser', 'employee_id'),
                        ('schools_staffuser', 'designation'),
                        ('students_student', 'roll_number'),
                        ('fees_invoice', 'invoice_number'),
                        ('payments_payment', 'payment_reference')
                    ]
                    
                    for table, column in column_checks:
                        try:
                            cursor.execute(f"""
                                SELECT EXISTS (
                                    SELECT FROM information_schema.columns 
                                    WHERE table_schema = '{tenant.schema_name}' 
                                    AND table_name = '{table}'
                                    AND column_name = '{column}'
                                )
                            """)
                            
                            if not cursor.fetchone()[0]:
                                issues.append(f"Missing column: {table}.{column}")
                        except:
                            # Table doesn't exist
                            pass
                    
        except Exception as e:
            issues.append(f"Error checking tenant: {e}")
        
        return issues

    def fix_missing_columns(self, tenant):
        """Fix missing columns by comparing with alpha"""
        
        self.stdout.write(f"  Checking columns in {tenant.schema_name}...")
        
        try:
            # This would need to be implemented based on your specific column requirements
            # For now, running migrations should handle most column issues
            pass
            
        except Exception as e:
            self.stdout.write(f"    Error fixing columns: {e}")

    def create_essential_data(self, tenant):
        """Create essential system data"""
        
        self.stdout.write(f"  Creating essential data in {tenant.schema_name}...")
        
        try:
            with schema_context(tenant.schema_name):
                with connection.cursor() as cursor:
                    
                    # Create academic year if missing
                    cursor.execute("SELECT COUNT(*) FROM schools_academicyear")
                    if cursor.fetchone()[0] == 0:
                        cursor.execute("""
                            INSERT INTO schools_academicyear (name, start_date, end_date, is_active, is_current, created_at, updated_at) 
                            VALUES ('Academic Year', CURRENT_DATE, CURRENT_DATE + INTERVAL '1 year', TRUE, TRUE, NOW(), NOW())
                        """)
                        self.stdout.write("    ✅ Created academic year")
                    
                    # Create essential reference data
                    essential_data_sql = """
                    INSERT INTO fees_concessiontype (name, description, is_percentage, default_value, is_active, created_at, updated_at) VALUES
                    ('Sibling Discount', 'Discount for siblings', TRUE, 10.00, TRUE, NOW(), NOW()),
                    ('Staff Child Discount', 'Discount for staff children', TRUE, 50.00, TRUE, NOW(), NOW()),
                    ('Merit Scholarship', 'Merit-based scholarship', TRUE, 25.00, TRUE, NOW(), NOW())
                    ON CONFLICT DO NOTHING;

                    INSERT INTO hr_leavetype (name, description, max_days_per_year, is_paid, requires_approval, is_active, created_at, updated_at) VALUES
                    ('Annual Leave', 'Annual vacation leave', 21, TRUE, TRUE, TRUE, NOW(), NOW()),
                    ('Sick Leave', 'Medical leave', 10, TRUE, FALSE, TRUE, NOW(), NOW()),
                    ('Maternity Leave', 'Maternity leave', 90, TRUE, TRUE, TRUE, NOW(), NOW()),
                    ('Emergency Leave', 'Emergency leave', 3, FALSE, TRUE, TRUE, NOW(), NOW())
                    ON CONFLICT DO NOTHING;

                    INSERT INTO payments_paymentmethod (name, description, is_active, created_at, updated_at) VALUES
                    ('Cash', 'Cash payment', TRUE, NOW(), NOW()),
                    ('Bank Transfer', 'Bank transfer payment', TRUE, NOW(), NOW()),
                    ('Mobile Money', 'Mobile money payment', TRUE, NOW(), NOW()),
                    ('Cheque', 'Cheque payment', TRUE, NOW(), NOW())
                    ON CONFLICT DO NOTHING;

                    INSERT INTO school_calendar_eventcategory (name, color, icon, description, is_active, created_at, updated_at) VALUES
                    ('Academic', '#007bff', 'bi-book', 'Academic events', TRUE, NOW(), NOW()),
                    ('Sports', '#28a745', 'bi-trophy', 'Sports events', TRUE, NOW(), NOW()),
                    ('Cultural', '#ffc107', 'bi-music-note', 'Cultural events', TRUE, NOW(), NOW()),
                    ('Meeting', '#6c757d', 'bi-people', 'Meetings', TRUE, NOW(), NOW()),
                    ('Holiday', '#dc3545', 'bi-calendar-x', 'Holidays', TRUE, NOW(), NOW())
                    ON CONFLICT DO NOTHING;
                    """
                    
                    # Execute each statement
                    statements = [stmt.strip() for stmt in essential_data_sql.split(';') if stmt.strip()]
                    for statement in statements:
                        try:
                            cursor.execute(statement)
                        except Exception as e:
                            # Ignore conflicts and missing tables
                            pass
                    
                    self.stdout.write("    ✅ Created essential reference data")
                    
        except Exception as e:
            self.stdout.write(f"    Error creating essential data: {e}")
