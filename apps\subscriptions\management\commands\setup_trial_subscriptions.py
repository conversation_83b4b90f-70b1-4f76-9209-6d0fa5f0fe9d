# apps/subscriptions/management/commands/setup_trial_subscriptions.py

from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import timedelta
from django_tenants.utils import get_public_schema_name

from apps.tenants.models import School
from apps.subscriptions.models import SubscriptionPlan, Subscription


class Command(BaseCommand):
    help = 'Setup trial subscriptions for tenants that do not have subscriptions'

    def add_arguments(self, parser):
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force update existing subscriptions to trial if they are not active',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be done without making changes',
        )

    def handle(self, *args, **options):
        force = options['force']
        dry_run = options['dry_run']
        
        if dry_run:
            self.stdout.write(self.style.WARNING('DRY RUN MODE - No changes will be made'))
        
        # Get the trial plan
        try:
            trial_plan = SubscriptionPlan.objects.get(name='Trial Plan')
        except SubscriptionPlan.DoesNotExist:
            self.stdout.write(
                self.style.ERROR('Trial Plan not found. Please run create_trial_plan.py first.')
            )
            return
        
        # Get all tenants (excluding public schema)
        tenants = School.objects.exclude(schema_name=get_public_schema_name())
        
        created_count = 0
        updated_count = 0
        skipped_count = 0
        
        for tenant in tenants:
            try:
                subscription = tenant.subscription
                
                # Check if we should update existing subscription
                if force and subscription.status in ['PENDING', 'CANCELLED', 'PAST_DUE']:
                    if not dry_run:
                        subscription.plan = trial_plan
                        subscription.status = Subscription.Status.TRIALING
                        subscription.trial_start_date = timezone.now()
                        subscription.trial_end_date = timezone.now() + timedelta(days=7)
                        subscription.save()
                    
                    updated_count += 1
                    self.stdout.write(
                        self.style.SUCCESS(
                            f'{"[DRY RUN] " if dry_run else ""}Updated subscription for tenant: {tenant.name}'
                        )
                    )
                else:
                    skipped_count += 1
                    self.stdout.write(
                        self.style.WARNING(
                            f'Skipped tenant {tenant.name} - already has subscription: {subscription.status}'
                        )
                    )
                    
            except Subscription.DoesNotExist:
                # Create new trial subscription
                if not dry_run:
                    subscription = Subscription.objects.create(
                        tenant=tenant,
                        plan=trial_plan,
                        status=Subscription.Status.TRIALING,
                        trial_start_date=timezone.now(),
                        trial_end_date=timezone.now() + timedelta(days=7),
                        billing_cycle='monthly',
                        next_billing_date=timezone.now() + timedelta(days=7),
                    )
                
                created_count += 1
                self.stdout.write(
                    self.style.SUCCESS(
                        f'{"[DRY RUN] " if dry_run else ""}Created trial subscription for tenant: {tenant.name}'
                    )
                )
        
        # Summary
        self.stdout.write('\n' + '='*50)
        self.stdout.write(self.style.SUCCESS('SUMMARY:'))
        self.stdout.write(f'Total tenants processed: {tenants.count()}')
        self.stdout.write(f'Trial subscriptions created: {created_count}')
        self.stdout.write(f'Subscriptions updated: {updated_count}')
        self.stdout.write(f'Tenants skipped: {skipped_count}')
        
        if dry_run:
            self.stdout.write(self.style.WARNING('\nThis was a dry run. Use --force to make actual changes.'))
        else:
            self.stdout.write(self.style.SUCCESS('\nTrial subscription setup completed!'))
            
        # Show trial plan details
        self.stdout.write('\n' + '='*50)
        self.stdout.write(self.style.SUCCESS('TRIAL PLAN DETAILS:'))
        self.stdout.write(f'Plan Name: {trial_plan.name}')
        self.stdout.write(f'Trial Period: {trial_plan.trial_period_days} days')
        self.stdout.write(f'Student Limit: {trial_plan.max_students}')
        self.stdout.write(f'Staff Limit: {trial_plan.max_staff or "Unlimited"}')
        self.stdout.write(f'Monthly Price: ${trial_plan.price_monthly}')
        self.stdout.write(f'Annual Price: ${trial_plan.price_annually}')
