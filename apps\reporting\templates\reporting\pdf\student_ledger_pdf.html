{% extends "./_pdf_base.html" %}
{% load humanize %}

{% block pdf_title %}Student Ledger - {{ student.full_name }} - {{ school_profile.name|default:"School" }}{% endblock %}

{% block report_period_header %}
    <p class="mb-0"><strong>Student:</strong> {{ student.full_name }} ({{ student.admission_number }})</p>
    {% if student.current_class %}
        <p class="mb-0"><strong>Class:</strong> {{ student.current_class.name }}{% if student.current_section %} - {{ student.current_section.name }}{% endif %}</p>
    {% endif %}
    {% if report_period_start and report_period_end %}
        <p class="mb-0"><strong>Period:</strong> {{ report_period_start|date:"d M Y" }} to {{ report_period_end|date:"d M Y" }}</p>
    {% elif report_period_start %}
        <p class="mb-0"><strong>Period:</strong> From {{ report_period_start|date:"d M Y" }}</p>
    {% elif report_period_end %}
        <p class="mb-0"><strong>Period:</strong> Up to {{ report_period_end|date:"d M Y" }}</p>
    {% else %}
        <p class="mb-0"><strong>Period:</strong> All Time</p>
    {% endif %}
{% endblock %}

{% block pdf_main_content %}
    <style>
        .ledger-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 5mm;
            font-size: 9pt;
        }
        .ledger-table th,
        .ledger-table td {
            border: 0.5px solid #666;
            padding: 2mm;
            text-align: left;
        }
        .ledger-table th {
            background-color: #f5f5f5;
            font-weight: bold;
            text-align: center;
        }
        .ledger-table .text-right {
            text-align: right;
        }
        .ledger-table .text-center {
            text-align: center;
        }
        .balance-row {
            background-color: #f9f9f9;
            font-weight: bold;
        }
        .opening-balance-row {
            background-color: #e3f2fd;
        }
        .closing-balance-row {
            background-color: #e8f5e8;
            font-weight: bold;
        }
        .summary-section {
            margin-top: 10mm;
            padding: 3mm;
            border: 1px solid #ccc;
            background-color: #f9f9f9;
        }
        .summary-title {
            font-weight: bold;
            margin-bottom: 2mm;
            font-size: 10pt;
        }
        .summary-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 1mm;
        }
        .transaction-type-badge {
            font-size: 7pt;
            padding: 1px 3px;
            border-radius: 2px;
            color: white;
        }
        .badge-invoice { background-color: #007bff; }
        .badge-payment { background-color: #28a745; }
        .badge-concession { background-color: #ffc107; color: #000; }
        .badge-adjustment { background-color: #6c757d; }
    </style>

    <!-- Ledger Transactions Table -->
    <table class="ledger-table">
        <thead>
            <tr>
                <th style="width: 12%;">Date</th>
                <th style="width: 40%;">Description</th>
                <th style="width: 12%;">Reference</th>
                <th style="width: 12%;">Debit</th>
                <th style="width: 12%;">Credit</th>
                <th style="width: 12%;">Balance</th>
            </tr>
        </thead>
        <tbody>
            <!-- Opening Balance Row -->
            {% if opening_balance is not None %}
            <tr class="opening-balance-row">
                <td class="text-center">{{ report_period_start|date:"Y-m-d"|default:"Beginning" }}</td>
                <td><strong>Opening Balance</strong></td>
                <td class="text-center">-</td>
                <td class="text-right">-</td>
                <td class="text-right">-</td>
                <td class="text-right"><strong>{{ opening_balance|floatformat:2|intcomma }}</strong></td>
            </tr>
            {% endif %}

            <!-- Transaction Rows -->
            {% for tx in transactions %}
            <tr>
                <td class="text-center">{{ tx.date|date:"Y-m-d" }}</td>
                <td>
                    {{ tx.description }}
                    {% if tx.type %}
                        <span class="transaction-type-badge badge-{{ tx.type|lower }}">{{ tx.type }}</span>
                    {% endif %}
                </td>
                <td class="text-center">
                    {% if tx.reference_number %}{{ tx.reference_number }}{% else %}-{% endif %}
                </td>
                <td class="text-right">
                    {% if tx.debit and tx.debit != 0 %}{{ tx.debit|floatformat:2|intcomma }}{% else %}-{% endif %}
                </td>
                <td class="text-right">
                    {% if tx.credit and tx.credit != 0 %}{{ tx.credit|floatformat:2|intcomma }}{% else %}-{% endif %}
                </td>
                <td class="text-right"><strong>{{ tx.balance|floatformat:2|intcomma }}</strong></td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="6" class="text-center" style="padding: 10mm; font-style: italic; color: #666;">
                    No transactions found for this period.
                </td>
            </tr>
            {% endfor %}

            <!-- Closing Balance Row -->
            <tr class="closing-balance-row">
                <td class="text-center">{{ report_period_end|date:"Y-m-d"|default:"Today" }}</td>
                <td><strong>Closing Balance</strong></td>
                <td class="text-center">-</td>
                <td class="text-right">-</td>
                <td class="text-right">-</td>
                <td class="text-right"><strong>{{ closing_balance|floatformat:2|intcomma }}</strong></td>
            </tr>
        </tbody>
    </table>

    <!-- Summary Section -->
    <div class="summary-section">
        <div class="summary-title">Ledger Summary</div>
        <div class="summary-row">
            <span>Total Debits:</span>
            <span><strong>{{ total_debits|floatformat:2|intcomma|default:"0.00" }}</strong></span>
        </div>
        <div class="summary-row">
            <span>Total Credits:</span>
            <span><strong>{{ total_credits|floatformat:2|intcomma|default:"0.00" }}</strong></span>
        </div>
        <div class="summary-row">
            <span>Net Balance:</span>
            <span><strong>{{ closing_balance|floatformat:2|intcomma|default:"0.00" }}</strong></span>
        </div>
    </div>

    <!-- Footer Note -->
    <p style="margin-top: 10mm; font-size: 8pt; color: #666; text-align: center;">
        This ledger shows all financial transactions for the selected student and period.
        <br>
        Generated on {% now "F d, Y \a\t H:i" %} by {{ school_profile.name|default:"School Management System" }}
    </p>
{% endblock %}
