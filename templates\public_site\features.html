{# templates/public_site/features.html #}
{% extends "public_base.html" %}
{% load static %}

{% block title %}Features - School Fees Management Platform{% endblock %}

{% block extra_public_css %}
<style>
    .features-hero {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 5rem 0;
        text-align: center;
        position: relative;
        overflow: hidden;
    }

    .features-hero::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        opacity: 0.3;
    }

    .features-hero h1 {
        font-size: 3.5rem;
        font-weight: 700;
        margin-bottom: 1rem;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        position: relative;
        z-index: 1;
    }

    .features-hero p {
        font-size: 1.3rem;
        opacity: 0.9;
        margin-bottom: 0;
        position: relative;
        z-index: 1;
        max-width: 600px;
        margin: 0 auto;
    }

    .section-container {
        padding: 4rem 0;
    }

    .section-title {
        text-align: center;
        margin-bottom: 3rem;
    }

    .section-title h2 {
        font-size: 2.5rem;
        color: #2d3748;
        margin-bottom: 1rem;
    }

    .section-title p {
        font-size: 1.1rem;
        color: #718096;
        max-width: 600px;
        margin: 0 auto;
    }

    .feature-category {
        margin-bottom: 4rem;
    }

    .category-header {
        text-align: center;
        margin-bottom: 3rem;
        padding: 2rem;
        background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
        border-radius: 16px;
    }

    .category-header .icon {
        width: 80px;
        height: 80px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1.5rem;
        color: white;
        font-size: 2rem;
    }

    .category-header h3 {
        color: #2d3748;
        font-size: 2rem;
        margin-bottom: 0.75rem;
    }

    .category-header p {
        color: #718096;
        font-size: 1.1rem;
        margin-bottom: 0;
    }

    .features-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 2rem;
    }

    .feature-card {
        background: white;
        border: 1px solid #e2e8f0;
        border-radius: 16px;
        padding: 2rem;
        transition: all 0.3s ease;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    .feature-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        border-color: #667eea;
    }

    .feature-card .icon {
        width: 50px;
        height: 50px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 1.5rem;
        color: white;
        font-size: 1.2rem;
    }

    .feature-card h4 {
        color: #2d3748;
        font-size: 1.25rem;
        margin-bottom: 0.75rem;
    }

    .feature-card p {
        color: #718096;
        line-height: 1.6;
        margin-bottom: 1rem;
    }

    .feature-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .feature-list li {
        color: #4a5568;
        padding: 0.25rem 0;
        display: flex;
        align-items: center;
    }

    .feature-list li::before {
        content: '✓';
        color: #48bb78;
        font-weight: bold;
        margin-right: 0.75rem;
        font-size: 1.1rem;
    }

    .highlight-section {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 20px;
        padding: 3rem;
        text-align: center;
        margin: 4rem 0;
    }

    .highlight-section h3 {
        font-size: 2rem;
        margin-bottom: 1rem;
    }

    .highlight-section p {
        font-size: 1.1rem;
        opacity: 0.9;
        margin-bottom: 0;
    }

    .integration-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
        margin-top: 2rem;
    }

    .integration-card {
        background: white;
        border: 1px solid #e2e8f0;
        border-radius: 12px;
        padding: 1.5rem;
        text-align: center;
        transition: all 0.3s ease;
    }

    .integration-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
    }

    .integration-card .icon {
        width: 40px;
        height: 40px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1rem;
        color: white;
        font-size: 1rem;
    }

    .integration-card h5 {
        color: #2d3748;
        margin-bottom: 0.5rem;
    }

    .integration-card p {
        color: #718096;
        font-size: 0.9rem;
        margin-bottom: 0;
    }

    .cta-section {
        background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
        text-align: center;
        border-radius: 20px;
        padding: 3rem;
        margin-top: 4rem;
    }

    .cta-section h2 {
        color: #2d3748;
        margin-bottom: 1rem;
    }

    .cta-section p {
        color: #718096;
        font-size: 1.1rem;
        margin-bottom: 2rem;
    }

    .btn-primary-gradient {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        color: white;
        padding: 0.875rem 2rem;
        border-radius: 10px;
        font-weight: 600;
        text-decoration: none;
        display: inline-block;
        transition: all 0.3s ease;
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        margin: 0 0.5rem;
    }

    .btn-primary-gradient:hover {
        background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        color: white;
        text-decoration: none;
    }

    .btn-outline-gradient {
        background: transparent;
        border: 2px solid #667eea;
        color: #667eea;
        padding: 0.875rem 2rem;
        border-radius: 10px;
        font-weight: 600;
        text-decoration: none;
        display: inline-block;
        transition: all 0.3s ease;
        margin: 0 0.5rem;
    }

    .btn-outline-gradient:hover {
        background: #667eea;
        color: white;
        text-decoration: none;
        transform: translateY(-2px);
    }

    @media (max-width: 768px) {
        .features-hero h1 {
            font-size: 2.5rem;
        }

        .features-hero {
            padding: 3rem 0;
        }

        .section-container {
            padding: 3rem 0;
        }

        .features-grid {
            grid-template-columns: 1fr;
        }

        .feature-card {
            padding: 1.5rem;
        }

        .highlight-section {
            padding: 2rem;
        }

        .cta-section {
            padding: 2rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="features-hero">
    <div class="container">
        <h1>Powerful Features</h1>
        <p>Everything you need to manage school fees efficiently, transparently, and professionally</p>
    </div>
</section>

<!-- Core Fee Management Features -->
<section class="section-container">
    <div class="container">
        <div class="feature-category">
            <div class="category-header">
                <div class="icon">
                    <i class="bi bi-cash-coin"></i>
                </div>
                <h3>Fee Management</h3>
                <p>Comprehensive tools for managing all aspects of school fees, from structure creation to payment tracking</p>
            </div>

            <div class="features-grid">
                <div class="feature-card">
                    <div class="icon">
                        <i class="bi bi-diagram-3"></i>
                    </div>
                    <h4>Flexible Fee Structures</h4>
                    <p>Create unlimited fee structures with customizable fee heads, terms, and academic years.</p>
                    <ul class="feature-list">
                        <li>Multiple fee heads per structure</li>
                        <li>Term-based or annual fee collection</li>
                        <li>Class-specific fee variations</li>
                        <li>Academic year management</li>
                        <li>Fee structure templates</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <div class="icon">
                        <i class="bi bi-receipt"></i>
                    </div>
                    <h4>Automated Invoice Generation</h4>
                    <p>Generate professional invoices automatically with customizable templates and bulk processing.</p>
                    <ul class="feature-list">
                        <li>Bulk invoice generation</li>
                        <li>Customizable invoice templates</li>
                        <li>Automatic due date calculation</li>
                        <li>Invoice preview and approval</li>
                        <li>Email delivery to parents</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <div class="icon">
                        <i class="bi bi-credit-card"></i>
                    </div>
                    <h4>Payment Processing</h4>
                    <p>Accept payments through multiple channels with automatic reconciliation and tracking.</p>
                    <ul class="feature-list">
                        <li>Online payment gateway integration</li>
                        <li>Cash and check payment recording</li>
                        <li>Partial payment support</li>
                        <li>Payment plan management</li>
                        <li>Automatic receipt generation</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <div class="icon">
                        <i class="bi bi-percent"></i>
                    </div>
                    <h4>Discounts & Concessions</h4>
                    <p>Manage scholarships, sibling discounts, and special concessions with flexible rules.</p>
                    <ul class="feature-list">
                        <li>Percentage and fixed amount discounts</li>
                        <li>Sibling discount automation</li>
                        <li>Merit-based scholarships</li>
                        <li>Need-based financial aid</li>
                        <li>Approval workflow for concessions</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <div class="icon">
                        <i class="bi bi-clock-history"></i>
                    </div>
                    <h4>Late Fee Management</h4>
                    <p>Automatically calculate and apply late fees with customizable rules and grace periods.</p>
                    <ul class="feature-list">
                        <li>Configurable late fee rules</li>
                        <li>Grace period settings</li>
                        <li>Automatic late fee calculation</li>
                        <li>Late fee waiver options</li>
                        <li>Payment reminder notifications</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <div class="icon">
                        <i class="bi bi-file-earmark-pdf"></i>
                    </div>
                    <h4>Professional Receipts</h4>
                    <p>Generate branded receipts and financial documents with school logos and custom formatting.</p>
                    <ul class="feature-list">
                        <li>Branded receipt templates</li>
                        <li>PDF receipt generation</li>
                        <li>Email receipt delivery</li>
                        <li>Receipt numbering system</li>
                        <li>Duplicate receipt prevention</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Student & Parent Management -->
<section class="section-container" style="background: #f7fafc;">
    <div class="container">
        <div class="feature-category">
            <div class="category-header">
                <div class="icon">
                    <i class="bi bi-people"></i>
                </div>
                <h3>Student & Parent Management</h3>
                <p>Comprehensive student information system with parent portal and communication tools</p>
            </div>

            <div class="features-grid">
                <div class="feature-card">
                    <div class="icon">
                        <i class="bi bi-person-badge"></i>
                    </div>
                    <h4>Student Profiles</h4>
                    <p>Maintain detailed student records with academic and financial information.</p>
                    <ul class="feature-list">
                        <li>Complete student demographics</li>
                        <li>Academic history tracking</li>
                        <li>Fee allocation and history</li>
                        <li>Parent/guardian information</li>
                        <li>Emergency contact details</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <div class="icon">
                        <i class="bi bi-house-door"></i>
                    </div>
                    <h4>Parent Portal</h4>
                    <p>Dedicated portal for parents to view fees, make payments, and track academic progress.</p>
                    <ul class="feature-list">
                        <li>Fee statement viewing</li>
                        <li>Online payment processing</li>
                        <li>Payment history access</li>
                        <li>Receipt downloads</li>
                        <li>Account balance tracking</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <div class="icon">
                        <i class="bi bi-collection"></i>
                    </div>
                    <h4>Class Management</h4>
                    <p>Organize students into classes with flexible grouping and fee assignment options.</p>
                    <ul class="feature-list">
                        <li>Multi-level class structure</li>
                        <li>Bulk student enrollment</li>
                        <li>Class-wise fee assignment</li>
                        <li>Academic year transitions</li>
                        <li>Student promotion tracking</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <div class="icon">
                        <i class="bi bi-envelope"></i>
                    </div>
                    <h4>Communication Tools</h4>
                    <p>Keep parents informed with automated notifications and messaging systems.</p>
                    <ul class="feature-list">
                        <li>Email notifications</li>
                        <li>SMS alerts (optional)</li>
                        <li>Payment reminders</li>
                        <li>Fee due notifications</li>
                        <li>Custom message broadcasting</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Reporting & Analytics -->
<section class="section-container">
    <div class="container">
        <div class="feature-category">
            <div class="category-header">
                <div class="icon">
                    <i class="bi bi-graph-up"></i>
                </div>
                <h3>Reporting & Analytics</h3>
                <p>Comprehensive financial reporting and analytics to track performance and make informed decisions</p>
            </div>

            <div class="features-grid">
                <div class="feature-card">
                    <div class="icon">
                        <i class="bi bi-file-earmark-bar-graph"></i>
                    </div>
                    <h4>Financial Reports</h4>
                    <p>Generate detailed financial reports for accounting and compliance purposes.</p>
                    <ul class="feature-list">
                        <li>Income statements</li>
                        <li>Balance sheets</li>
                        <li>Cash flow statements</li>
                        <li>Trial balance reports</li>
                        <li>Budget vs actual analysis</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <div class="icon">
                        <i class="bi bi-exclamation-triangle"></i>
                    </div>
                    <h4>Outstanding Fees</h4>
                    <p>Track and manage outstanding fees with detailed aging reports and collection insights.</p>
                    <ul class="feature-list">
                        <li>Outstanding fees by student</li>
                        <li>Aging analysis reports</li>
                        <li>Collection efficiency metrics</li>
                        <li>Defaulter identification</li>
                        <li>Recovery action tracking</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <div class="icon">
                        <i class="bi bi-calendar-check"></i>
                    </div>
                    <h4>Collection Reports</h4>
                    <p>Monitor fee collection performance with detailed analytics and trends.</p>
                    <ul class="feature-list">
                        <li>Daily collection summaries</li>
                        <li>Monthly collection trends</li>
                        <li>Payment method analysis</li>
                        <li>Collection target tracking</li>
                        <li>Comparative period analysis</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <div class="icon">
                        <i class="bi bi-person-lines-fill"></i>
                    </div>
                    <h4>Student Ledgers</h4>
                    <p>Individual student account statements with complete transaction history.</p>
                    <ul class="feature-list">
                        <li>Complete payment history</li>
                        <li>Fee allocation details</li>
                        <li>Discount and concession tracking</li>
                        <li>Outstanding balance summary</li>
                        <li>Parent-friendly statements</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <div class="icon">
                        <i class="bi bi-download"></i>
                    </div>
                    <h4>Export Options</h4>
                    <p>Export reports in multiple formats for external analysis and record keeping.</p>
                    <ul class="feature-list">
                        <li>PDF report generation</li>
                        <li>Excel spreadsheet export</li>
                        <li>CSV data export</li>
                        <li>Scheduled report delivery</li>
                        <li>Custom report formatting</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <div class="icon">
                        <i class="bi bi-speedometer2"></i>
                    </div>
                    <h4>Dashboard Analytics</h4>
                    <p>Real-time dashboard with key performance indicators and visual analytics.</p>
                    <ul class="feature-list">
                        <li>Revenue tracking charts</li>
                        <li>Collection rate metrics</li>
                        <li>Outstanding fees visualization</li>
                        <li>Payment trend analysis</li>
                        <li>Custom KPI widgets</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Accounting & Finance -->
<section class="section-container" style="background: #f7fafc;">
    <div class="container">
        <div class="feature-category">
            <div class="category-header">
                <div class="icon">
                    <i class="bi bi-calculator"></i>
                </div>
                <h3>Accounting & Finance</h3>
                <p>Professional accounting features with chart of accounts and double-entry bookkeeping</p>
            </div>

            <div class="features-grid">
                <div class="feature-card">
                    <div class="icon">
                        <i class="bi bi-list-ul"></i>
                    </div>
                    <h4>Chart of Accounts</h4>
                    <p>Comprehensive chart of accounts with automated account creation and management.</p>
                    <ul class="feature-list">
                        <li>Standard accounting structure</li>
                        <li>Custom account creation</li>
                        <li>Account hierarchy management</li>
                        <li>Automated fee head linking</li>
                        <li>Account balance tracking</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <div class="icon">
                        <i class="bi bi-arrow-left-right"></i>
                    </div>
                    <h4>Double-Entry Bookkeeping</h4>
                    <p>Maintain accurate financial records with automated double-entry accounting.</p>
                    <ul class="feature-list">
                        <li>Automatic journal entries</li>
                        <li>Debit and credit balancing</li>
                        <li>Transaction audit trails</li>
                        <li>Account reconciliation</li>
                        <li>Financial integrity checks</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <div class="icon">
                        <i class="bi bi-bank"></i>
                    </div>
                    <h4>Bank Reconciliation</h4>
                    <p>Streamline bank reconciliation with automated matching and variance reporting.</p>
                    <ul class="feature-list">
                        <li>Bank statement import</li>
                        <li>Automatic transaction matching</li>
                        <li>Variance identification</li>
                        <li>Reconciliation reports</li>
                        <li>Outstanding items tracking</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <div class="icon">
                        <i class="bi bi-pie-chart"></i>
                    </div>
                    <h4>Budget Management</h4>
                    <p>Create and monitor budgets with variance analysis and forecasting tools.</p>
                    <ul class="feature-list">
                        <li>Annual budget creation</li>
                        <li>Department-wise budgeting</li>
                        <li>Budget vs actual tracking</li>
                        <li>Variance analysis reports</li>
                        <li>Budget revision management</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Security & Compliance -->
<section class="section-container">
    <div class="container">
        <div class="feature-category">
            <div class="category-header">
                <div class="icon">
                    <i class="bi bi-shield-check"></i>
                </div>
                <h3>Security & Compliance</h3>
                <p>Enterprise-grade security features to protect sensitive student and financial data</p>
            </div>

            <div class="features-grid">
                <div class="feature-card">
                    <div class="icon">
                        <i class="bi bi-lock"></i>
                    </div>
                    <h4>Data Encryption</h4>
                    <p>End-to-end encryption for all sensitive data with industry-standard security protocols.</p>
                    <ul class="feature-list">
                        <li>256-bit SSL encryption</li>
                        <li>Database encryption at rest</li>
                        <li>Secure data transmission</li>
                        <li>Payment data tokenization</li>
                        <li>Regular security audits</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <div class="icon">
                        <i class="bi bi-person-check"></i>
                    </div>
                    <h4>Access Control</h4>
                    <p>Role-based access control with granular permissions and user management.</p>
                    <ul class="feature-list">
                        <li>Role-based permissions</li>
                        <li>Multi-factor authentication</li>
                        <li>Session management</li>
                        <li>User activity logging</li>
                        <li>Password policy enforcement</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <div class="icon">
                        <i class="bi bi-clipboard-check"></i>
                    </div>
                    <h4>Compliance</h4>
                    <p>Meet educational and financial compliance requirements with built-in safeguards.</p>
                    <ul class="feature-list">
                        <li>FERPA compliance</li>
                        <li>GDPR data protection</li>
                        <li>SOC 2 certification</li>
                        <li>PCI DSS compliance</li>
                        <li>Regular compliance audits</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <div class="icon">
                        <i class="bi bi-cloud-upload"></i>
                    </div>
                    <h4>Data Backup</h4>
                    <p>Automated backup and disaster recovery to ensure data safety and business continuity.</p>
                    <ul class="feature-list">
                        <li>Automated daily backups</li>
                        <li>Multi-location data storage</li>
                        <li>Point-in-time recovery</li>
                        <li>Disaster recovery planning</li>
                        <li>99.9% uptime guarantee</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Highlight Section -->
<div class="container">
    <div class="highlight-section">
        <h3>All Features Included</h3>
        <p>Every plan includes access to all features - no hidden costs, no feature restrictions. Scale your usage as your school grows.</p>
    </div>
</div>

<!-- Integrations -->
<section class="section-container" style="background: #f7fafc;">
    <div class="container">
        <div class="section-title">
            <h2>Integrations & Compatibility</h2>
            <p>Seamlessly integrate with your existing systems and tools</p>
        </div>

        <div class="integration-grid">
            <div class="integration-card">
                <div class="icon">
                    <i class="bi bi-credit-card"></i>
                </div>
                <h5>Payment Gateways</h5>
                <p>Stripe, PayPal, Razorpay, and more</p>
            </div>

            <div class="integration-card">
                <div class="icon">
                    <i class="bi bi-bank"></i>
                </div>
                <h5>Banking</h5>
                <p>Direct bank integrations and reconciliation</p>
            </div>

            <div class="integration-card">
                <div class="icon">
                    <i class="bi bi-envelope"></i>
                </div>
                <h5>Email Services</h5>
                <p>SMTP, SendGrid, Mailgun integration</p>
            </div>

            <div class="integration-card">
                <div class="icon">
                    <i class="bi bi-phone"></i>
                </div>
                <h5>SMS Providers</h5>
                <p>Twilio, AWS SNS, local SMS gateways</p>
            </div>

            <div class="integration-card">
                <div class="icon">
                    <i class="bi bi-file-earmark-excel"></i>
                </div>
                <h5>Data Export</h5>
                <p>Excel, CSV, PDF, and API access</p>
            </div>

            <div class="integration-card">
                <div class="icon">
                    <i class="bi bi-cloud"></i>
                </div>
                <h5>Cloud Storage</h5>
                <p>AWS, Google Cloud, Azure integration</p>
            </div>

            <div class="integration-card">
                <div class="icon">
                    <i class="bi bi-gear"></i>
                </div>
                <h5>APIs</h5>
                <p>RESTful APIs for custom integrations</p>
            </div>

            <div class="integration-card">
                <div class="icon">
                    <i class="bi bi-mortarboard"></i>
                </div>
                <h5>Student Information Systems</h5>
                <p>SIS integration and data synchronization</p>
            </div>
        </div>
    </div>
</section>

<!-- Call to Action -->
<section class="section-container">
    <div class="container">
        <div class="cta-section">
            <h2>Ready to Experience These Features?</h2>
            <p>Start your free trial today and discover how our comprehensive platform can transform your school's fee management.</p>
            <div>
                <a href="{% url 'tenants:register_school' %}" class="btn-primary-gradient">
                    <i class="bi bi-rocket-takeoff me-2"></i>
                    Start Free Trial
                </a>
                <a href="{% url 'public_site:contact' %}" class="btn-outline-gradient">
                    <i class="bi bi-chat-dots me-2"></i>
                    Schedule Demo
                </a>
            </div>
        </div>
    </div>
</section>
{% endblock %}


