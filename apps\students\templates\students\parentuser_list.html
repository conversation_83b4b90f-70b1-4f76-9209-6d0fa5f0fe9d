{% extends "tenant_base.html" %}
{% load static core_tags humanize widget_tweaks %}

{% block title %}{{ view_title|default:"Manage Parent Accounts" }} - {{ request.tenant.name }}{% endblock title %}

{% block page_specific_css %}
    {{ block.super }}
    <style>
        .filter-form .form-label-sm {
            font-size: .875em;
            margin-bottom: .25rem;
            font-weight: 500;
        }
        .actions-column {
            min-width: 150px;
            white-space: nowrap;
        }
        .data-table th, .data-table td {
            vertical-align: middle;
        }
        .parent-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
            margin-right: 10px;
            flex-shrink: 0;
        }
        .parent-info {
            display: flex;
            align-items: center;
        }
        .filter-toggle {
            cursor: pointer;
        }
        .filter-card.collapsed {
            display: none;
        }
        @media (min-width: 768px) {
            .filter-card.collapsed {
                display: block !important;
            }
            #filterToggle {
                display: none;
            }
        }
    </style>
{% endblock page_specific_css %}

{% block tenant_specific_content %}
<div class="container-fluid mt-4">
    {% include "partials/_breadcrumb.html" with current_page_title=view_title|default:"Manage Parent Accounts" %}

    <div class="row">
        <div class="col-12">
            <!-- Header Section -->
            <div class="d-flex justify-content-between align-items-center mb-3 flex-wrap">
                <div>
                    <h1 class="mb-0 h2">{{ view_title|default:"Manage Parent Accounts" }}</h1>
                    <small class="text-muted">
                        {% if parents %}
                            {% if paginator %}
                                Showing {{ parents|length }} of {{ paginator.count }} parent accounts
                            {% else %}
                                Showing {{ parents|length }} parent accounts
                            {% endif %}
                            {% if request.GET.name or request.GET.email or request.GET.status %}
                                (filtered)
                            {% endif %}
                        {% else %}
                            No parent accounts found
                        {% endif %}
                    </small>
                </div>
                <div class="d-flex gap-2 flex-wrap mt-2 mt-md-0">
                    {% if perms.students.add_parentuser %}
                        <a href="{% url 'students:parentuser_create' %}" class="btn btn-primary">
                            <i class="bi bi-person-plus-fill me-1"></i>Add Parent
                        </a>
                    {% endif %}
                    <button type="button" class="btn btn-outline-secondary filter-toggle" id="filterToggle">
                        <i class="bi bi-funnel me-1"></i>Filters <i class="bi bi-chevron-down ms-1"></i>
                    </button>
                </div>
            </div>

            {% include "partials/_messages.html" %}

            <!-- Toggle Filter Section -->
            {% include "students/_parent_filter_toggle.html" %}


            <!-- Parent Accounts Table -->
            {% if parents %}
            <div class="card shadow-sm">
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover table-sm data-table mb-0">
                            <thead class="table-dark">
                                <tr>
                                    <th>{% trans "Parent" %}</th>
                                    <th>{% trans "Email" %}</th>
                                    <th>{% trans "Phone" %}</th>
                                    <th>{% trans "Linked Children" %}</th>
                                    <th>{% trans "Status" %}</th>
                                    <th class="text-end actions-column">{% trans "Actions" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for parent_obj in parents %}
                                <tr class="parent-row">
                                    <td>
                                        <div class="parent-info">
                                            <div class="parent-avatar bg-primary">
                                                {{ parent_obj.get_full_name|default:parent_obj.email|first|upper }}
                                            </div>
                                            <div>
                                                <div class="fw-medium">
                                                    {{ parent_obj.get_full_name|default:parent_obj.email }}
                                                </div>
                                                {% if parent_obj.get_full_name and parent_obj.email != parent_obj.get_full_name %}
                                                    <small class="text-muted">{{ parent_obj.email }}</small>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </td>
                                    <td>{{ parent_obj.email }}</td>
                                    <td>{{ parent_obj.phone_number|default:"N/A" }}</td>
                                    <td>
                                        {% for child in parent_obj.children.all|slice:":2" %}
                                            <span class="badge bg-light text-dark border me-1">{{ child.get_full_name|truncatechars:15 }}</span>
                                        {% empty %}
                                            <span class="text-muted small">{% trans "None" %}</span>
                                        {% endfor %}
                                        {% if parent_obj.children.all.count > 2 %}
                                            <span class="text-muted small"> +{{ parent_obj.children.all.count|add:"-2" }} more</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if parent_obj.is_active %}
                                            <span class="badge bg-success">{% trans "Active" %}</span>
                                        {% else %}
                                            <span class="badge bg-danger">{% trans "Inactive" %}</span>
                                        {% endif %}
                                    </td>
                                    <td class="text-end actions-column">
                                        <div class="btn-group btn-group-sm" role="group">
                                            {% if perms.students.change_parentuser %}
                                            <a href="{% url 'students:parentuser_update' parent_obj.pk %}"
                                               class="btn btn-outline-primary btn-sm"
                                               title="{% trans 'Edit Parent' %}">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                            {% endif %}
                                            {% if perms.students.delete_parentuser %}
                                            <a href="{% url 'students:parentuser_delete' parent_obj.pk %}"
                                               class="btn btn-outline-danger btn-sm"
                                               title="{% trans 'Delete Parent' %}">
                                                <i class="bi bi-trash"></i>
                                            </a>
                                            {% endif %}
                                        </div>
                                    </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="card shadow-sm">
                    <div class="card-body text-center py-5">
                        <i class="bi bi-people display-1 text-muted"></i>
                        <h4 class="mt-3">{% trans "No Parent Accounts Found" %}</h4>
                        <p class="text-muted">{% trans "There are no parent accounts in the system yet." %}</p>
                        {% if perms.students.add_parentuser %}
                        <a href="{% url 'students:parentuser_create' %}" class="btn btn-primary">
                            <i class="bi bi-person-plus-fill me-1"></i> {% trans "Add First Parent" %}
                        </a>
                        {% endif %}
                    </div>
                </div>
            {% endif %}

            <!-- Pagination -->
            {% if is_paginated %}
            <div class="d-flex justify-content-center mt-4">
                {% include "partials/_pagination.html" with page_obj=page_obj filter_params=filter_params %}
            </div>
            {% endif %}
        </div>
    </div>

    <!-- Import Modal -->
    {% include "students/_parent_import_modal.html" %}
</div>
{% endblock tenant_specific_content %}

{% block extra_tenant_js %}
{{ block.super }}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Filter toggle functionality
    const filterToggle = document.getElementById('filterToggle');
    const filterCard = document.querySelector('.premium-parent-filter-card');

    if (filterToggle && filterCard) {
        filterToggle.addEventListener('click', function() {
            const isCollapsed = filterCard.style.display === 'none';
            filterCard.style.display = isCollapsed ? 'block' : 'none';

            const icon = this.querySelector('.bi-chevron-down, .bi-chevron-up');
            if (icon) {
                icon.classList.toggle('bi-chevron-down');
                icon.classList.toggle('bi-chevron-up');
            }
        });
    }
});
</script>
{% endblock %}

