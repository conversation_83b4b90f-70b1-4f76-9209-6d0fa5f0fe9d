{% extends "tenant_base.html" %}
{% load humanize static core_tags reporting_filters %}

{% block title %}{{ view_title|default:"Accounts Payable Report" }}{% endblock %}

{% block content %}
<div class="container-fluid mt-3">
    {% with report_icon_class="bi bi-credit-card" %}
        {% include "partials/_report_header.html" %}
    {% endwith %}

    {% include "partials/_messages.html" %}
    {% include "partials/_report_filter_form.html" with filter_form=filter_form report_code=report_code specific_filter_title="Payable Report Filters" %}

    <!-- Report Summary Card -->
    <div class="card mb-4">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="bi bi-bar-chart me-2"></i>Payable Summary</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-3">
                    <div class="text-center">
                        <h6 class="text-muted">As of Date</h6>
                        <p class="mb-0">{{ as_of_date|date:"M d, Y" }}</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h6 class="text-muted">Total Payable</h6>
                        <p class="mb-0 fw-bold text-danger">{{ total_payable|currency }}</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h6 class="text-muted">Overdue Amount</h6>
                        <p class="mb-0 fw-bold text-warning">{{ overdue_total|currency }}</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h6 class="text-muted">Current Amount</h6>
                        <p class="mb-0 fw-bold text-success">{{ total_payable|subtract:overdue_total|currency }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Vendor Summary -->
    {% if vendor_summary %}
    <div class="card mb-4">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="bi bi-building me-2"></i>Vendor Summary</h5>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-striped table-hover mb-0">
                    <thead class="table-dark">
                        <tr>
                            <th>Vendor</th>
                            <th class="text-center">Invoices</th>
                            <th class="text-end">Total Amount</th>
                            <th class="text-end">Current</th>
                            <th class="text-end">Overdue</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for vendor in vendor_summary %}
                        <tr>
                            <td>
                                <strong>{{ vendor.vendor.name|default:"Unknown Vendor" }}</strong>
                                {% if vendor.vendor.contact_person %}
                                <br><small class="text-muted">Contact: {{ vendor.vendor.contact_person }}</small>
                                {% endif %}
                            </td>
                            <td class="text-center">
                                <span class="badge bg-info">{{ vendor.invoice_count }}</span>
                            </td>
                            <td class="text-end">
                                <strong class="text-danger">{{ vendor.total_amount|currency }}</strong>
                            </td>
                            <td class="text-end">
                                {% if vendor.current_amount > 0 %}
                                <span class="text-success">{{ vendor.current_amount|currency }}</span>
                                {% else %}
                                <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td class="text-end">
                                {% if vendor.overdue_amount > 0 %}
                                <span class="text-warning">{{ vendor.overdue_amount|currency }}</span>
                                {% else %}
                                <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                    <tfoot class="table-light">
                        <tr>
                            <th>Totals:</th>
                            <th class="text-center">{{ payable_data|length }}</th>
                            <th class="text-end text-danger">{{ total_payable|currency }}</th>
                            <th class="text-end text-success">{{ total_payable|subtract:overdue_total|currency }}</th>
                            <th class="text-end text-warning">{{ overdue_total|currency }}</th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Payable Details -->
    {% if payable_data %}
    <div class="card">
        <div class="card-header bg-light d-flex justify-content-between align-items-center">
            <h5 class="mb-0"><i class="bi bi-receipt me-2"></i>Payable Details</h5>
            <span class="badge bg-primary">{{ payable_data|length }} items</span>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-striped table-hover mb-0">
                    <thead class="table-dark">
                        <tr>
                            <th>Vendor</th>
                            <th>Expense Date</th>
                            <th>Due Date</th>
                            <th class="text-center">Days Overdue</th>
                            <th class="text-end">Amount</th>
                            <th>Category</th>
                            <th>Reference</th>
                            <th>Description</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for item in payable_data %}
                        <tr class="{% if item.is_overdue %}table-warning{% endif %}">
                            <td>
                                <strong>{{ item.vendor_name }}</strong>
                                {% if item.expense.vendor.email %}
                                <br><small class="text-muted">{{ item.expense.vendor.email }}</small>
                                {% endif %}
                            </td>
                            <td>{{ item.expense.expense_date|date:"M d, Y" }}</td>
                            <td>
                                {{ item.due_date|date:"M d, Y" }}
                                {% if item.is_overdue %}
                                <br><small class="text-danger">Overdue</small>
                                {% endif %}
                            </td>
                            <td class="text-center">
                                {% if item.is_overdue %}
                                <span class="badge bg-danger">{{ item.days_overdue }}</span>
                                {% else %}
                                <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td class="text-end">
                                <strong class="{% if item.is_overdue %}text-warning{% else %}text-danger{% endif %}">
                                    {{ item.amount|currency }}
                                </strong>
                            </td>
                            <td>
                                <span class="badge bg-light text-dark">{{ item.expense.category.name }}</span>
                            </td>
                            <td>
                                {% if item.expense.reference_number %}
                                <span class="badge bg-secondary">{{ item.expense.reference_number }}</span>
                                {% else %}
                                <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td>
                                {{ item.expense.description|truncatechars:50 }}
                                {% if item.expense.recorded_by %}
                                <br><small class="text-muted">By: {{ item.expense.recorded_by.get_full_name }}</small>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                    <tfoot class="table-light">
                        <tr>
                            <th colspan="4" class="text-end">Totals:</th>
                            <th class="text-end text-danger">{{ total_payable|currency }}</th>
                            <th colspan="3"></th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- No Data Message -->
    {% if not payable_data %}
    <div class="card">
        <div class="card-body text-center py-5">
            <i class="bi bi-credit-card-2-front display-1 text-muted mb-3"></i>
            <h4 class="text-muted">No Outstanding Payables Found</h4>
            <p class="text-muted">No unpaid expenses match your current filter criteria.</p>
            <div class="mt-3">
                <a href="{% url 'reporting:accounts_payable_report' %}" class="btn btn-outline-primary me-2">
                    <i class="bi bi-arrow-clockwise me-2"></i>Reset Filters
                </a>
                <a href="#" class="btn btn-outline-success">
                    <i class="bi bi-plus-circle me-2"></i>Add Expense
                </a>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Information Note -->
    <div class="alert alert-info mt-4">
        <i class="bi bi-info-circle me-2"></i>
        <strong>Note:</strong> This report shows expenses that have been recorded but not yet paid (no payment method assigned). 
        Due dates are calculated as expense date + 30 days. For more accurate payable tracking, consider implementing a dedicated vendor invoice system.
    </div>

</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Highlight overdue rows
    $('.table-warning').hover(
        function() {
            $(this).addClass('table-danger');
        },
        function() {
            $(this).removeClass('table-danger');
        }
    );
    
    // Add click handlers for vendor rows
    $('tbody tr').on('click', function() {
        $(this).toggleClass('table-active');
    });
});
</script>
{% endblock %}
