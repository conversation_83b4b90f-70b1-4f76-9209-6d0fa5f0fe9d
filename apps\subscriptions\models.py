# D:\school_fees_saas_v2\apps\subscriptions\models.py

from django.db import models
from django.utils import timezone
from django.utils.text import slugify
from django.utils.translation import gettext_lazy as _
from decimal import Decimal
from apps.tenants.models import School # Import School for ForeignKey

# --- Feature Model (Defines available features/add-ons) ---
class Feature(models.Model):
    code = models.CharField(max_length=50, unique=True, help_text=_("Short unique code for the feature (e.g., HR_MODULE, PARENT_PORTAL)."))
    name = models.CharField(max_length=150, help_text=_("User-friendly name of the feature."))
    description = models.TextField(blank=True, help_text=_("Optional description."))
    
    class Meta:
        ordering = ['name']
        verbose_name = _("Plan Feature")
        verbose_name_plural = _("Plan Features")

    def __str__(self):
        return self.name

# --- Subscription Plan Model (Defines the tiers) ---
class SubscriptionPlan(models.Model):
    name = models.CharField(max_length=100, unique=True)
    slug = models.SlugField(max_length=120, unique=True, blank=True, help_text=_("URL-friendly version of the name, auto-generated if blank."))
    description = models.TextField(blank=True)
    
    price_monthly = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'), help_text=_("Price per month if billed monthly."))
    price_annually = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'), help_text=_("Price per year if billed annually."))
    
    # Payment Gateway Price IDs
    pg_price_id_monthly = models.CharField(max_length=100, blank=True, null=True, help_text=_("Price ID from payment gateway for monthly billing."))
    pg_price_id_annually = models.CharField(max_length=100, blank=True, null=True, help_text=_("Price ID from payment gateway for annual billing."))
    
    # --- CORE OF THIS PRICING MODEL ---
    max_students = models.PositiveIntegerField(
        default=50, 
        help_text=_("The maximum number of active students allowed for this plan.")
    )
    max_staff = models.PositiveIntegerField(
        null=True, blank=True, 
        help_text=_("Maximum number of active staff users allowed. Leave blank for unlimited.")
    )
    
    # Kept for optional major feature add-ons
    features = models.ManyToManyField(Feature, blank=True, related_name='plans')
    
    is_active = models.BooleanField(default=True, help_text=_("Is this plan currently offered for new subscriptions?"))
    is_public = models.BooleanField(default=True, help_text=_("Is this plan visible on the public pricing page?"))
    display_order = models.PositiveIntegerField(default=0, help_text=_("Order for display on pricing page (0=first)."))
    
    trial_period_days = models.PositiveIntegerField(default=0, help_text=_("Number of trial days for this plan (0 for no trial)."))

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['display_order', 'price_monthly']
        verbose_name = _("Subscription Plan")
        verbose_name_plural = _("Subscription Plans")
    
    def __str__(self):
        return f"{self.name} (Up to {self.max_students} students)"

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.name)
        super().save(*args, **kwargs)

# # --- Subscription Model (Links a School to a Plan) ---
# class Subscription(models.Model):
#     class Status(models.TextChoices):
#         PENDING = 'PENDING', _('Pending Setup')
#         TRIALING = 'TRIALING', _('Trialing')
#         ACTIVE = 'ACTIVE', _('Active')
#         PAST_DUE = 'PAST_DUE', _('Past Due')
#         CANCELLED = 'CANCELLED', _('Cancelled')
#         ENDED = 'ENDED', _('Ended')
#         # Add other statuses as needed from your payment gateway (e.g., INCOMPLETE)
    
#     class BillingCycle(models.TextChoices):
#         MONTHLY = 'MONTHLY', _('Monthly')
#         ANNUALLY = 'ANNUALLY', _('Annually')

#     school = models.OneToOneField(
#         School, 
#         on_delete=models.CASCADE,
#         related_name='subscription' 
#     )
#     plan = models.ForeignKey(
#         SubscriptionPlan,
#         on_delete=models.PROTECT,
#         related_name='subscriptions'
#     )
#     status = models.CharField(max_length=25, choices=Status.choices, default=Status.PENDING, db_index=True)
#     billing_cycle = models.CharField(max_length=10, choices=BillingCycle.choices, default=BillingCycle.MONTHLY)
    
#     price_at_subscription = models.DecimalField(
#         max_digits=10, 
#         decimal_places=2, 
#         default=Decimal('0.00'),
#         help_text="The price of the plan at the moment this subscription was created or renewed."
#     )
    
#     # Payment Gateway Info
#     pg_subscription_id = models.CharField(max_length=100, blank=True, null=True, unique=True, db_index=True)
#     pg_customer_id = models.CharField(max_length=100, blank=True, null=True, db_index=True)
    
#     # Dates
#     trial_start_date = models.DateTimeField(null=True, blank=True) # NEW
#     trial_end_date = models.DateTimeField(null=True, blank=True)
#     current_period_start = models.DateTimeField(null=True, blank=True)
#     current_period_end = models.DateTimeField(null=True, blank=True, db_index=True)
    
#     # Cancellation Info
#     cancel_at_period_end = models.BooleanField(default=False)
#     cancelled_at = models.DateTimeField(null=True, blank=True)
#     ended_at = models.DateTimeField(null=True, blank=True)

#     notes = models.TextField(blank=True, help_text=_("Internal notes about this subscription."))

#     created_at = models.DateTimeField(auto_now_add=True)
#     updated_at = models.DateTimeField(auto_now=True)

#     class Meta:
#         ordering = ['school__name']
#         verbose_name = _("School Subscription")
#         verbose_name_plural = _("School Subscriptions")

#     def __str__(self):
#         return f"Subscription for {self.school.name} ({self.plan.name}) - Status: {self.get_status_display()}"

#     @property
#     def is_usable(self):
#         """Determines if the tenant should have access to the app's features."""
#         return self.status in [self.Status.ACTIVE, self.Status.TRIALING]

#     @property
#     def is_on_trial(self):
#         return self.status == self.Status.TRIALING and self.trial_end_date and self.trial_end_date >= timezone.now()

#     def has_feature(self, feature_code):
#         """Checks if this subscription's plan includes a specific optional feature."""
#         if not self.is_usable or not self.plan:
#             return False
#         return self.plan.features.filter(code__iexact=feature_code).exists()
    
# your_project/apps/subscriptions/models.py

import logging
from decimal import Decimal
from django.db import models
from django.conf import settings
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

# Import your actual Tenant and Plan models
from apps.tenants.models import School
from .models import SubscriptionPlan

logger = logging.getLogger(__name__)

class Subscription(models.Model):
    class Status(models.TextChoices):
        PENDING = 'PENDING', _('Pending Setup')
        TRIALING = 'TRIALING', _('Trialing')
        ACTIVE = 'ACTIVE', _('Active')
        PAST_DUE = 'PAST_DUE', _('Past Due')
        CANCELLED = 'CANCELLED', _('Cancelled')
        ENDED = 'ENDED', _('Ended')
    
    class BillingCycle(models.TextChoices):
        MONTHLY = 'MONTHLY', _('Monthly')
        ANNUALLY = 'ANNUALLY', _('Annually')

    # --- THIS IS THE CORRECT FIELD NAME AS PER YOUR MODEL DEFINITION ---
    school = models.OneToOneField(
        School, 
        on_delete=models.CASCADE,
        related_name='subscription' 
    )
    plan = models.ForeignKey(
        SubscriptionPlan,
        on_delete=models.PROTECT,
        related_name='subscriptions'
    )
    status = models.CharField(max_length=25, choices=Status.choices, default=Status.PENDING, db_index=True)
    billing_cycle = models.CharField(max_length=10, choices=BillingCycle.choices, default=BillingCycle.MONTHLY)
    
    price_at_subscription = models.DecimalField(
        max_digits=10, 
        decimal_places=2, 
        default=Decimal('0.00'),
        help_text=_("The price of the plan at the moment this subscription was created or renewed.")
    )
    
    pg_subscription_id = models.CharField(max_length=100, blank=True, null=True, unique=True, db_index=True)
    pg_customer_id = models.CharField(max_length=100, blank=True, null=True, db_index=True)
    
    trial_start_date = models.DateTimeField(null=True, blank=True)
    trial_end_date = models.DateTimeField(null=True, blank=True)
    current_period_start = models.DateTimeField(null=True, blank=True)
    current_period_end = models.DateTimeField(null=True, blank=True, db_index=True)
    
    cancel_at_period_end = models.BooleanField(default=False)
    cancelled_at = models.DateTimeField(null=True, blank=True)
    ended_at = models.DateTimeField(null=True, blank=True)

    notes = models.TextField(blank=True, help_text=_("Internal notes about this subscription."))

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['school__name']
        verbose_name = _("School Subscription")
        verbose_name_plural = _("School Subscriptions")

    def __str__(self):
        return f"Subscription for {self.school.name} ({self.plan.name}) - Status: {self.get_status_display()}"

    def save(self, *args, **kwargs):
        """
        Custom save method to handle logging of creation and status changes.
        This consolidates logic from the old signals.
        """
        is_new = self._state.adding
        original_status = None
        if not is_new:
            try:
                original_status = Subscription.objects.get(pk=self.pk).status
            except Subscription.DoesNotExist:
                pass # This is an update, so it should exist.

        super().save(*args, **kwargs) # Call the "real" save() method

        # Post-save logic for logging
        if is_new:
            logger.info(
                f"New subscription created for tenant {self.school.name}: "
                f"Plan='{self.plan.name}', Status='{self.get_status_display()}', "
                f"Trial End='{self.trial_end_date.strftime('%Y-%m-%d') if self.trial_end_date else 'N/A'}'"
            )
        elif original_status and original_status != self.status:
            logger.info(
                f"Subscription status changed for tenant {self.school.name}: "
                f"'{original_status}' -> '{self.get_status_display()}'"
            )

    @property
    def is_usable(self):
        return self.status in [self.Status.ACTIVE, self.Status.TRIALING]

    @property
    def is_on_trial(self):
        return self.status == self.Status.TRIALING and self.trial_end_date and self.trial_end_date >= timezone.now()
