{% extends "tenant_base.html" %}
{% load humanize static core_tags reporting_filters %}

{% block title %}{{ view_title|default:"Journal Entry Register Report" }}{% endblock %}

{% block content %}
<div class="container-fluid mt-3">
    {% with report_icon_class="bi bi-journal-text" %}
        {% include "partials/_report_header.html" %}
    {% endwith %}

    {% include "partials/_messages.html" %}
    {% include "partials/_report_filter_form.html" with filter_form=filter_form report_code=report_code specific_filter_title="Journal Entry Register Filters" %}

    <!-- Summary Card -->
    <div class="card mb-4">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="bi bi-graph-up me-2"></i>Journal Entry Summary</h5>
        </div>
        <div class="card-body">
            <div class="row mb-3">
                <div class="col-md-12">
                    <p>
                        <strong>Date Range:</strong> {{ summary.date_range }} | 
                        <strong>Total Entries:</strong> {{ summary.total_entries }} | 
                        <strong>Details:</strong> {% if show_details %}Shown{% else %}Hidden{% endif %}
                    </p>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-3">
                    <div class="text-center">
                        <h6 class="text-muted">Total Debits</h6>
                        <p class="mb-0 fw-bold text-primary fs-4">{{ summary.total_debits|currency }}</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h6 class="text-muted">Total Credits</h6>
                        <p class="mb-0 fw-bold text-success fs-4">{{ summary.total_credits|currency }}</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h6 class="text-muted">Balance Status</h6>
                        <p class="mb-0 fw-bold {% if summary.is_balanced %}text-success{% else %}text-danger{% endif %} fs-4">
                            {% if summary.is_balanced %}Balanced{% else %}Unbalanced{% endif %}
                        </p>
                        {% if not summary.is_balanced %}
                        <small class="text-danger">Difference: {{ summary.balance_difference|currency }}</small>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h6 class="text-muted">Average Entry</h6>
                        <p class="mb-0 fw-bold text-info">{{ summary.avg_entry_amount|currency }}</p>
                    </div>
                </div>
            </div>
            
            <!-- Balance Status Indicator -->
            <div class="mt-3">
                <h6>Journal Balance Status</h6>
                <div class="progress" style="height: 25px;">
                    <div class="progress-bar {% if summary.is_balanced %}bg-success{% else %}bg-danger{% endif %}" 
                         role="progressbar" style="width: 100%">
                        {% if summary.is_balanced %}
                        All Entries Balanced
                        {% else %}
                        {{ summary.unbalanced_count }} Unbalanced Entries
                        {% endif %}
                    </div>
                </div>
                <small class="text-muted">
                    {% if summary.is_balanced %}
                    All journal entries are properly balanced with equal debits and credits
                    {% else %}
                    {{ summary.unbalanced_count }} entries have unequal debits and credits - requires attention
                    {% endif %}
                </small>
            </div>
        </div>
    </div>

    <!-- Entry Status Breakdown -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="bi bi-pie-chart me-2"></i>Entry Status Distribution</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6">
                            <div class="text-center">
                                <h6 class="text-success">Posted</h6>
                                <p class="mb-0 fw-bold">{{ summary.posted_count }}</p>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center">
                                <h6 class="text-warning">Draft</h6>
                                <p class="mb-0 fw-bold">{{ summary.draft_count }}</p>
                            </div>
                        </div>
                    </div>
                    <hr>
                    <div class="row">
                        <div class="col-6">
                            <div class="text-center">
                                <h6 class="text-danger">Reversed</h6>
                                <p class="mb-0 fw-bold">{{ summary.reversed_count }}</p>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center">
                                <h6 class="text-danger">Unbalanced</h6>
                                <p class="mb-0 fw-bold">{{ summary.unbalanced_count }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="bi bi-shield-check me-2"></i>Audit Compliance</h5>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled">
                        <li><i class="bi bi-check-circle {% if summary.is_balanced %}text-success{% else %}text-danger{% endif %} me-2"></i>
                            Journal Balance: {% if summary.is_balanced %}Compliant{% else %}Non-compliant{% endif %}
                        </li>
                        <li><i class="bi bi-check-circle {% if summary.unbalanced_count == 0 %}text-success{% else %}text-warning{% endif %} me-2"></i>
                            Entry Balance: {% if summary.unbalanced_count == 0 %}All Balanced{% else %}{{ summary.unbalanced_count }} Issues{% endif %}
                        </li>
                        <li><i class="bi bi-check-circle text-success me-2"></i>
                            Audit Trail: Complete
                        </li>
                        <li><i class="bi bi-check-circle {% if summary.draft_count == 0 %}text-success{% else %}text-info{% endif %} me-2"></i>
                            Posted Entries: {{ summary.posted_count }}/{{ summary.total_entries }}
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Journal Entries -->
    {% if journal_entries %}
    <div class="card">
        <div class="card-header bg-light d-flex justify-content-between align-items-center">
            <h5 class="mb-0"><i class="bi bi-table me-2"></i>Journal Entry Register</h5>
            <span class="badge bg-primary">{{ journal_entries|length }} entries</span>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-striped table-hover mb-0">
                    <thead class="table-dark">
                        <tr>
                            <th>Entry #</th>
                            <th>Date</th>
                            <th>Description</th>
                            <th class="text-center">Status</th>
                            {% if show_details %}
                            <th>Account</th>
                            <th class="text-end">Debit</th>
                            <th class="text-end">Credit</th>
                            {% else %}
                            <th class="text-end">Total Debits</th>
                            <th class="text-end">Total Credits</th>
                            <th>Created By</th>
                            {% endif %}
                            <th class="text-center">Balance</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for entry in journal_entries %}
                        {% if show_details and entry.lines %}
                            {% for line in entry.lines %}
                            <tr class="{% if not entry.is_balanced %}table-warning{% elif entry.status == 'REVERSED' %}table-danger{% elif entry.status == 'DRAFT' %}table-light{% endif %}">
                                {% if forloop.first %}
                                <td rowspan="{{ entry.line_count }}">
                                    <strong>{{ entry.entry_number }}</strong>
                                    <br><small class="text-muted">{{ entry.entry_type }}</small>
                                </td>
                                <td rowspan="{{ entry.line_count }}">
                                    {{ entry.date }}
                                    <br><small class="text-muted">{{ entry.created_at|date:"M d, H:i" }}</small>
                                </td>
                                <td rowspan="{{ entry.line_count }}">
                                    {{ entry.description }}
                                    {% if entry.reference %}
                                    <br><small class="text-muted">Ref: {{ entry.reference }}</small>
                                    {% endif %}
                                </td>
                                <td rowspan="{{ entry.line_count }}" class="text-center">
                                    <span class="badge bg-{{ entry.status_badge }}">{{ entry.status }}</span>
                                </td>
                                {% endif %}
                                
                                <td>
                                    <strong>{{ line.account_code }}</strong> - {{ line.account_name }}
                                    {% if line.description %}
                                    <br><small class="text-muted">{{ line.description }}</small>
                                    {% endif %}
                                </td>
                                <td class="text-end">
                                    {% if line.debit_amount > 0 %}
                                    <span class="text-primary fw-bold">{{ line.debit_amount|currency }}</span>
                                    {% else %}
                                    <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td class="text-end">
                                    {% if line.credit_amount > 0 %}
                                    <span class="text-success fw-bold">{{ line.credit_amount|currency }}</span>
                                    {% else %}
                                    <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                
                                {% if forloop.first %}
                                <td rowspan="{{ entry.line_count }}" class="text-center">
                                    {% if entry.is_balanced %}
                                    <i class="bi bi-check-circle text-success" title="Balanced"></i>
                                    {% else %}
                                    <i class="bi bi-exclamation-triangle text-warning" title="Unbalanced"></i>
                                    {% endif %}
                                </td>
                                {% endif %}
                            </tr>
                            {% endfor %}
                        {% else %}
                        <tr class="{% if not entry.is_balanced %}table-warning{% elif entry.status == 'REVERSED' %}table-danger{% elif entry.status == 'DRAFT' %}table-light{% endif %}">
                            <td>
                                <strong>{{ entry.entry_number }}</strong>
                                <br><small class="text-muted">{{ entry.entry_type }}</small>
                            </td>
                            <td>
                                {{ entry.date }}
                                <br><small class="text-muted">{{ entry.created_at|date:"M d, H:i" }}</small>
                            </td>
                            <td>
                                {{ entry.description }}
                                {% if entry.reference %}
                                <br><small class="text-muted">Ref: {{ entry.reference }}</small>
                                {% endif %}
                            </td>
                            <td class="text-center">
                                <span class="badge bg-{{ entry.status_badge }}">{{ entry.status }}</span>
                            </td>
                            <td class="text-end">
                                <span class="text-primary fw-bold">{{ entry.total_debits|currency }}</span>
                            </td>
                            <td class="text-end">
                                <span class="text-success fw-bold">{{ entry.total_credits|currency }}</span>
                            </td>
                            <td>{{ entry.created_by }}</td>
                            <td class="text-center">
                                {% if entry.is_balanced %}
                                <i class="bi bi-check-circle text-success" title="Balanced"></i>
                                {% else %}
                                <i class="bi bi-exclamation-triangle text-warning" title="Unbalanced"></i>
                                {% endif %}
                            </td>
                        </tr>
                        {% endif %}
                        {% endfor %}
                    </tbody>
                    <tfoot class="table-light">
                        <tr>
                            <th colspan="{% if show_details %}5{% else %}4{% endif %}">Totals:</th>
                            <th class="text-end">{{ summary.total_debits|currency }}</th>
                            <th class="text-end">{{ summary.total_credits|currency }}</th>
                            <th class="text-center">
                                {% if summary.is_balanced %}
                                <i class="bi bi-check-circle text-success"></i>
                                {% else %}
                                <i class="bi bi-exclamation-triangle text-danger"></i>
                                {% endif %}
                            </th>
                            {% if not show_details %}<th></th>{% endif %}
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Audit Compliance Notes -->
    {% if journal_entries %}
    <div class="card mt-4">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="bi bi-shield-check me-2"></i>Audit Compliance & Notes</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h6>Compliance Status</h6>
                    <ul class="list-unstyled">
                        {% if summary.is_balanced %}
                        <li><i class="bi bi-check-circle text-success me-2"></i>Journal is balanced - debits equal credits</li>
                        {% else %}
                        <li><i class="bi bi-exclamation-triangle text-danger me-2"></i>Journal is unbalanced - requires correction</li>
                        {% endif %}
                        
                        {% if summary.unbalanced_count == 0 %}
                        <li><i class="bi bi-check-circle text-success me-2"></i>All entries are individually balanced</li>
                        {% else %}
                        <li><i class="bi bi-exclamation-triangle text-warning me-2"></i>{{ summary.unbalanced_count }} entries are unbalanced</li>
                        {% endif %}
                        
                        <li><i class="bi bi-check-circle text-success me-2"></i>Complete audit trail maintained</li>
                        <li><i class="bi bi-check-circle text-success me-2"></i>Entry numbering sequential</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6>Audit Notes</h6>
                    <ul class="list-unstyled">
                        <li><i class="bi bi-info-circle text-info me-2"></i>
                            {{ summary.posted_count }} entries posted, {{ summary.draft_count }} in draft
                        </li>
                        {% if summary.reversed_count > 0 %}
                        <li><i class="bi bi-arrow-counterclockwise text-warning me-2"></i>
                            {{ summary.reversed_count }} entries have been reversed
                        </li>
                        {% endif %}
                        <li><i class="bi bi-calendar text-primary me-2"></i>
                            Date range: {{ summary.date_range }}
                        </li>
                        <li><i class="bi bi-calculator text-info me-2"></i>
                            Average entry amount: {{ summary.avg_entry_amount|currency }}
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- No Data Message -->
    {% if not journal_entries %}
    <div class="card">
        <div class="card-body text-center py-5">
            <i class="bi bi-journal-text display-1 text-muted mb-3"></i>
            <h4 class="text-muted">No Journal Entries Found</h4>
            <p class="text-muted">No journal entries found for the selected criteria.</p>
            <div class="mt-3">
                <a href="{% url 'reporting:journal_entry_register_report' %}" class="btn btn-outline-primary me-2">
                    <i class="bi bi-arrow-clockwise me-2"></i>Reset Filters
                </a>
            </div>
        </div>
    </div>
    {% endif %}

</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Animate progress bars
    $('.progress-bar').each(function() {
        var $this = $(this);
        var width = $this.css('width');
        $this.css('width', '0%').animate({width: width}, 1500);
    });
    
    // Highlight unbalanced entries
    $('.table-warning').hover(
        function() {
            $(this).addClass('table-active');
        },
        function() {
            $(this).removeClass('table-active');
        }
    );
    
    // Add click handlers for entry rows
    $('tbody tr').on('click', function() {
        $(this).toggleClass('table-info');
    });
});
</script>
{% endblock %}

