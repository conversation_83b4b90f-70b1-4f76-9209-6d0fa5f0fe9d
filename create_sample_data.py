#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to create sample data for testing the reporting system.
Run this from the Django shell or as a management command.
"""

import os
import sys
import django
from decimal import Decimal
from datetime import date, timedelta
import random

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from django.utils import timezone
from apps.students.models import Student
from apps.fees.models import Invoice, InvoiceDetail, AcademicYear, Term, FeeHead
from apps.payments.models import Payment, PaymentMethod
from apps.schools.models import SchoolClass, SchoolProfile
from apps.accounting.models import Account, AccountType

def create_sample_data():
    print("Creating sample data for reporting...")
    
    # 1. Create Academic Year and Term if they don't exist
    academic_year, created = AcademicYear.objects.get_or_create(
        name='2024 Academic Year',
        defaults={
            'start_date': date(2024, 1, 1),
            'end_date': date(2024, 12, 31),
            'is_active': True,
            'is_current': True
        }
    )
    if created:
        print(f"Created Academic Year: {academic_year}")
    
    term, created = Term.objects.get_or_create(
        name='Term 1',
        academic_year=academic_year,
        defaults={
            'start_date': date(2024, 1, 1),
            'end_date': date(2024, 4, 30),
            'is_active': True
        }
    )
    if created:
        print(f"Created Term: {term}")
    
    # 2. Create School Classes if they don't exist
    classes = []
    for grade in ['Grade 1', 'Grade 2', 'Grade 3', 'Grade 4', 'Grade 5']:
        school_class, created = SchoolClass.objects.get_or_create(
            name=grade,
            defaults={'description': f'{grade} class'}
        )
        classes.append(school_class)
        if created:
            print(f"Created Class: {school_class}")
    
    # 3. Create Fee Heads if they don't exist
    fee_heads = []
    for fee_name in ['Tuition Fee', 'Library Fee', 'Sports Fee', 'Transport Fee']:
        fee_head, created = FeeHead.objects.get_or_create(
            name=fee_name,
            defaults={
                'description': f'{fee_name} for students',
                'is_active': True
            }
        )
        fee_heads.append(fee_head)
        if created:
            print(f"Created Fee Head: {fee_head}")
    
    # 4. Create Account Types and Accounts if they don't exist
    account_types = {}
    for type_name in ['Assets', 'Revenue', 'Liabilities']:
        acc_type, created = AccountType.objects.get_or_create(
            name=type_name,
            defaults={'description': f'{type_name} accounts'}
        )
        account_types[type_name] = acc_type
        if created:
            print(f"Created Account Type: {acc_type}")
    
    accounts = {}
    account_data = [
        ('Accounts Receivable', 'Assets'),
        ('Fee Income', 'Revenue'),
        ('Cash', 'Assets'),
        ('Bank Account', 'Assets'),
    ]
    
    for acc_name, acc_type_name in account_data:
        account, created = Account.objects.get_or_create(
            name=acc_name,
            defaults={
                'code': f'ACC{len(accounts)+1000}',
                'account_type': account_types[acc_type_name],
                'description': f'{acc_name} account',
                'is_active': True
            }
        )
        accounts[acc_name] = account
        if created:
            print(f"Created Account: {account}")
    
    # 5. Create Payment Methods if they don't exist
    payment_methods = []
    for method_name in ['Cash', 'Bank Transfer', 'Mobile Money']:
        payment_method, created = PaymentMethod.objects.get_or_create(
            name=method_name,
            defaults={
                'type': 'CASH' if method_name == 'Cash' else 'BANK_TRANSFER',
                'is_active': True
            }
        )
        payment_methods.append(payment_method)
        if created:
            print(f"Created Payment Method: {payment_method}")
    
    # 6. Create Students if they don't exist
    students = []
    student_names = [
        ('John', 'Doe'), ('Jane', 'Smith'), ('Michael', 'Johnson'), 
        ('Sarah', 'Williams'), ('David', 'Brown'), ('Emily', 'Davis'),
        ('Robert', 'Miller'), ('Lisa', 'Wilson'), ('James', 'Moore'),
        ('Mary', 'Taylor')
    ]
    
    for i, (first_name, last_name) in enumerate(student_names):
        student, created = Student.objects.get_or_create(
            admission_number=f'STU{2024}{i+1:03d}',
            defaults={
                'first_name': first_name,
                'last_name': last_name,
                'current_class': random.choice(classes),
                'date_of_admission': date(2024, 1, 15),
                'status': 'ACTIVE',
                'is_active': True
            }
        )
        students.append(student)
        if created:
            print(f"Created Student: {student}")
    
    # 7. Create Invoices
    invoices_created = 0
    for student in students:
        # Create 2-3 invoices per student
        for invoice_num in range(random.randint(2, 3)):
            issue_date = date(2024, random.randint(1, 12), random.randint(1, 28))
            due_date = issue_date + timedelta(days=30)
            
            invoice, created = Invoice.objects.get_or_create(
                student=student,
                academic_year=academic_year,
                term=term,
                issue_date=issue_date,
                defaults={
                    'due_date': due_date,
                    'subtotal_amount': Decimal('0.00'),
                    'total_concession_amount': Decimal('0.00'),
                    'amount_paid': Decimal('0.00'),
                    'status': random.choice(['SENT', 'PARTIALLY_PAID', 'PAID', 'OVERDUE']),
                    'notes_to_parent': f'Invoice for {student.get_full_name()}'
                }
            )
            
            if created:
                invoices_created += 1
                
                # Add invoice details
                total_amount = Decimal('0.00')
                for fee_head in random.sample(fee_heads, random.randint(2, 4)):
                    amount = Decimal(random.randint(50, 500))
                    InvoiceDetail.objects.create(
                        invoice=invoice,
                        fee_head=fee_head,
                        description=f'{fee_head.name} for {term.name}',
                        quantity=1,
                        unit_price=amount,
                        amount=amount,
                        line_type='FEE_ITEM'
                    )
                    total_amount += amount
                
                # Update invoice totals
                invoice.subtotal_amount = total_amount
                invoice.save()
    
    print(f"Created {invoices_created} invoices")
    
    # 8. Create Payments
    payments_created = 0
    invoices = Invoice.objects.all()
    
    for invoice in invoices:
        # 70% chance of having a payment
        if random.random() < 0.7:
            payment_amount = invoice.total_amount * Decimal(random.uniform(0.3, 1.0))
            
            payment, created = Payment.objects.get_or_create(
                student=invoice.student,
                academic_year=academic_year,
                defaults={
                    'amount': payment_amount,
                    'payment_date': invoice.issue_date + timedelta(days=random.randint(1, 45)),
                    'payment_method': random.choice(payment_methods),
                    'payment_type': 'FEE',
                    'status': 'COMPLETED',
                    'notes': f'Payment for invoice {invoice.invoice_number}'
                }
            )
            
            if created:
                payments_created += 1
                # Update invoice amount paid
                invoice.amount_paid += payment_amount
                invoice.save()
    
    print(f"Created {payments_created} payments")
    
    print("Sample data creation completed!")
    print(f"Total Students: {Student.objects.count()}")
    print(f"Total Invoices: {Invoice.objects.count()}")
    print(f"Total Payments: {Payment.objects.count()}")
    print(f"Total Accounts: {Account.objects.count()}")

if __name__ == '__main__':
    create_sample_data()
