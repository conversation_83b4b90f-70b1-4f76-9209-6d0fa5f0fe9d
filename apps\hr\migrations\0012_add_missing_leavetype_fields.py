# Generated by Django 5.1.9 on 2025-07-07 18:43

from django.db import migrations, models
from decimal import Decimal


def add_field_if_not_exists(apps, schema_editor):
    """Add fields only if they don't already exist"""
    from django.db import connection

    # Check if max_days_per_year_grant column exists
    with connection.cursor() as cursor:
        cursor.execute("""
            SELECT column_name
            FROM information_schema.columns
            WHERE table_name='hr_leavetype' AND column_name='max_days_per_year_grant'
        """)
        if not cursor.fetchone():
            # Column doesn't exist, safe to add
            cursor.execute("""
                ALTER TABLE hr_leavetype
                ADD COLUMN max_days_per_year_grant DECIMAL(5,2) NULL
            """)

        # Check and add other fields similarly
        cursor.execute("""
            SELECT column_name
            FROM information_schema.columns
            WHERE table_name='hr_leavetype' AND column_name='accrual_frequency'
        """)
        if not cursor.fetchone():
            cursor.execute("""
                ALTER TABLE hr_leavetype
                ADD COLUMN accrual_frequency VARCHAR(20) DEFAULT 'NONE' NOT NULL
            """)

        cursor.execute("""
            SELECT column_name
            FROM information_schema.columns
            WHERE table_name='hr_leavetype' AND column_name='accrual_rate'
        """)
        if not cursor.fetchone():
            cursor.execute("""
                ALTER TABLE hr_leavetype
                ADD COLUMN accrual_rate DECIMAL(5,2) DEFAULT 0.00 NOT NULL
            """)

        cursor.execute("""
            SELECT column_name
            FROM information_schema.columns
            WHERE table_name='hr_leavetype' AND column_name='max_accrual_balance'
        """)
        if not cursor.fetchone():
            cursor.execute("""
                ALTER TABLE hr_leavetype
                ADD COLUMN max_accrual_balance INTEGER NULL
            """)

        cursor.execute("""
            SELECT column_name
            FROM information_schema.columns
            WHERE table_name='hr_leavetype' AND column_name='prorate_accrual'
        """)
        if not cursor.fetchone():
            cursor.execute("""
                ALTER TABLE hr_leavetype
                ADD COLUMN prorate_accrual BOOLEAN DEFAULT TRUE NOT NULL
            """)


def reverse_add_fields(apps, schema_editor):
    """Remove the fields if needed"""
    from django.db import connection
    with connection.cursor() as cursor:
        cursor.execute("ALTER TABLE hr_leavetype DROP COLUMN IF EXISTS max_days_per_year_grant")
        cursor.execute("ALTER TABLE hr_leavetype DROP COLUMN IF EXISTS accrual_frequency")
        cursor.execute("ALTER TABLE hr_leavetype DROP COLUMN IF EXISTS accrual_rate")
        cursor.execute("ALTER TABLE hr_leavetype DROP COLUMN IF EXISTS max_accrual_balance")
        cursor.execute("ALTER TABLE hr_leavetype DROP COLUMN IF EXISTS prorate_accrual")


class Migration(migrations.Migration):

    dependencies = [
        ('hr', '0011_alter_payslip_adjustments'),
    ]

    operations = [
        migrations.RunPython(add_field_if_not_exists, reverse_add_fields),
    ]
