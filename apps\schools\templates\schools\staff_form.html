{% extends "tenant_base.html" %}
{% load static widget_tweaks i18n %}

{% block title %}
    {% if form.instance.pk %}
        {% blocktrans with staff_name=form.instance.get_full_name|default:form.instance.user.email %}Edit Staff: {{ staff_name }}{% endblocktrans %}
    {% else %}
        {% translate "Create New Staff Member" %}
    {% endif %}
{% endblock %}

{% block tenant_specific_content %}
<div class="container mt-4">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'schools:dashboard' %}">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="{% url 'schools:staff_list' %}">Staff</a></li>
            <li class="breadcrumb-item active" aria-current="page">
                {% if form.instance.pk %}Edit Staff{% else %}Add Staff{% endif %}
            </li>
        </ol>
    </nav>

    <div class="card shadow-sm">
        <div class="card-header bg-primary text-white">
            <h4 class="mb-0">
                <i class="bi bi-person-plus me-2"></i>
                {% if form.instance.pk %}
                    {% translate "Edit Staff Member" %}
                {% else %}
                    {% translate "Add New Staff Member" %}
                {% endif %}
            </h4>
        </div>
        <div class="card-body">
            {% include "partials/_messages.html" %}

            <form method="post" enctype="multipart/form-data" novalidate>
                {% csrf_token %}

                {% if form.non_field_errors %}
                    <div class="alert alert-danger">
                        {% for error in form.non_field_errors %}
                            {{ error }}{% if not forloop.last %}<br>{% endif %}
                        {% endfor %}
                    </div>
                {% endif %}

                <!-- Basic Information Section -->
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0"><i class="bi bi-person me-2"></i>Basic Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="{{ form.first_name.id_for_label }}" class="form-label">{{ form.first_name.label }}{% if form.first_name.field.required %}<span class="text-danger">*</span>{% endif %}</label>
                                {% render_field form.first_name class+="form-control" %}
                                {% if form.first_name.help_text %}
                                    <small class="form-text text-muted">{{ form.first_name.help_text|safe }}</small>
                                {% endif %}
                                {% for error in form.first_name.errors %}
                                    <div class="text-danger small">{{ error }}</div>
                                {% endfor %}
                            </div>

                            <div class="col-md-4 mb-3">
                                <label for="{{ form.middle_name.id_for_label }}" class="form-label">{{ form.middle_name.label }}</label>
                                {% render_field form.middle_name class+="form-control" %}
                                {% if form.middle_name.help_text %}
                                    <small class="form-text text-muted">{{ form.middle_name.help_text|safe }}</small>
                                {% endif %}
                                {% for error in form.middle_name.errors %}
                                    <div class="text-danger small">{{ error }}</div>
                                {% endfor %}
                            </div>

                            <div class="col-md-4 mb-3">
                                <label for="{{ form.last_name.id_for_label }}" class="form-label">{{ form.last_name.label }}{% if form.last_name.field.required %}<span class="text-danger">*</span>{% endif %}</label>
                                {% render_field form.last_name class+="form-control" %}
                                {% if form.last_name.help_text %}
                                    <small class="form-text text-muted">{{ form.last_name.help_text|safe }}</small>
                                {% endif %}
                                {% for error in form.last_name.errors %}
                                    <div class="text-danger small">{{ error }}</div>
                                {% endfor %}
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.email.id_for_label }}" class="form-label">{{ form.email.label }}{% if form.email.field.required %}<span class="text-danger">*</span>{% endif %}</label>
                                {% render_field form.email class+="form-control" %}
                                {% if form.email.help_text %}
                                    <small class="form-text text-muted">{{ form.email.help_text|safe }}</small>
                                {% endif %}
                                {% for error in form.email.errors %}
                                    <div class="text-danger small">{{ error }}</div>
                                {% endfor %}
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="{{ form.phone_number.id_for_label }}" class="form-label">{{ form.phone_number.label }}{% if form.phone_number.field.required %}<span class="text-danger">*</span>{% endif %}</label>
                                {% render_field form.phone_number class+="form-control" %}
                                {% if form.phone_number.help_text %}
                                    <small class="form-text text-muted">{{ form.phone_number.help_text|safe }}</small>
                                {% endif %}
                                {% for error in form.phone_number.errors %}
                                    <div class="text-danger small">{{ error }}</div>
                                {% endfor %}
                            </div>
                        </div>

                        {% if form.password1 %}
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.password1.id_for_label }}" class="form-label">{{ form.password1.label }}{% if form.password1.field.required %}<span class="text-danger">*</span>{% endif %}</label>
                                {% render_field form.password1 class+="form-control" %}
                                {% if form.password1.help_text %}
                                    <small class="form-text text-muted">{{ form.password1.help_text|safe }}</small>
                                {% endif %}
                                {% for error in form.password1.errors %}
                                    <div class="text-danger small">{{ error }}</div>
                                {% endfor %}
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="{{ form.password2.id_for_label }}" class="form-label">{{ form.password2.label }}{% if form.password2.field.required %}<span class="text-danger">*</span>{% endif %}</label>
                                {% render_field form.password2 class+="form-control" %}
                                {% if form.password2.help_text %}
                                    <small class="form-text text-muted">{{ form.password2.help_text|safe }}</small>
                                {% endif %}
                                {% for error in form.password2.errors %}
                                    <div class="text-danger small">{{ error }}</div>
                                {% endfor %}
                            </div>
                        </div>
                        {% endif %}

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="form-check">
                                    {% render_field form.is_active class+="form-check-input" %}
                                    <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                        {{ form.is_active.label }}
                                    </label>
                                    {% if form.is_active.help_text %}
                                        <small class="form-text text-muted d-block">{{ form.is_active.help_text|safe }}</small>
                                    {% endif %}
                                    {% for error in form.is_active.errors %}
                                        <div class="text-danger small">{{ error }}</div>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Employment Details Section -->
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0"><i class="bi bi-briefcase me-2"></i>Employment Details</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="{{ form.employee_id.id_for_label }}" class="form-label">{{ form.employee_id.label }}</label>
                                {% render_field form.employee_id class+="form-control" %}
                                {% if form.employee_id.help_text %}
                                    <small class="form-text text-muted">{{ form.employee_id.help_text|safe }}</small>
                                {% endif %}
                                {% for error in form.employee_id.errors %}
                                    <div class="text-danger small">{{ error }}</div>
                                {% endfor %}
                            </div>

                            <div class="col-md-4 mb-3">
                                <label for="{{ form.designation.id_for_label }}" class="form-label">{{ form.designation.label }}</label>
                                {% render_field form.designation class+="form-control" %}
                                {% if form.designation.help_text %}
                                    <small class="form-text text-muted">{{ form.designation.help_text|safe }}</small>
                                {% endif %}
                                {% for error in form.designation.errors %}
                                    <div class="text-danger small">{{ error }}</div>
                                {% endfor %}
                            </div>

                            <div class="col-md-4 mb-3">
                                <label for="{{ form.department.id_for_label }}" class="form-label">{{ form.department.label }}</label>
                                {% render_field form.department class+="form-control" %}
                                {% if form.department.help_text %}
                                    <small class="form-text text-muted">{{ form.department.help_text|safe }}</small>
                                {% endif %}
                                {% for error in form.department.errors %}
                                    <div class="text-danger small">{{ error }}</div>
                                {% endfor %}
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="{{ form.employment_type.id_for_label }}" class="form-label">{{ form.employment_type.label }}</label>
                                {% render_field form.employment_type class+="form-control" %}
                                {% if form.employment_type.help_text %}
                                    <small class="form-text text-muted">{{ form.employment_type.help_text|safe }}</small>
                                {% endif %}
                                {% for error in form.employment_type.errors %}
                                    <div class="text-danger small">{{ error }}</div>
                                {% endfor %}
                            </div>

                            <div class="col-md-4 mb-3">
                                <label for="{{ form.date_hired.id_for_label }}" class="form-label">{{ form.date_hired.label }}</label>
                                {% render_field form.date_hired class+="form-control" %}
                                {% if form.date_hired.help_text %}
                                    <small class="form-text text-muted">{{ form.date_hired.help_text|safe }}</small>
                                {% endif %}
                                {% for error in form.date_hired.errors %}
                                    <div class="text-danger small">{{ error }}</div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Personal Details Section -->
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0"><i class="bi bi-person-badge me-2"></i>Personal Details</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="{{ form.gender.id_for_label }}" class="form-label">{{ form.gender.label }}</label>
                                {% render_field form.gender class+="form-control" %}
                                {% if form.gender.help_text %}
                                    <small class="form-text text-muted">{{ form.gender.help_text|safe }}</small>
                                {% endif %}
                                {% for error in form.gender.errors %}
                                    <div class="text-danger small">{{ error }}</div>
                                {% endfor %}
                            </div>

                            <div class="col-md-4 mb-3">
                                <label for="{{ form.date_of_birth.id_for_label }}" class="form-label">{{ form.date_of_birth.label }}</label>
                                {% render_field form.date_of_birth class+="form-control" %}
                                {% if form.date_of_birth.help_text %}
                                    <small class="form-text text-muted">{{ form.date_of_birth.help_text|safe }}</small>
                                {% endif %}
                                {% for error in form.date_of_birth.errors %}
                                    <div class="text-danger small">{{ error }}</div>
                                {% endfor %}
                            </div>

                            <div class="col-md-4 mb-3">
                                <label for="{{ form.marital_status.id_for_label }}" class="form-label">{{ form.marital_status.label }}</label>
                                {% render_field form.marital_status class+="form-control" %}
                                {% if form.marital_status.help_text %}
                                    <small class="form-text text-muted">{{ form.marital_status.help_text|safe }}</small>
                                {% endif %}
                                {% for error in form.marital_status.errors %}
                                    <div class="text-danger small">{{ error }}</div>
                                {% endfor %}
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.photo.id_for_label }}" class="form-label">{{ form.photo.label }}</label>
                                {% render_field form.photo class+="form-control" %}
                                {% if form.photo.help_text %}
                                    <small class="form-text text-muted">{{ form.photo.help_text|safe }}</small>
                                {% endif %}
                                {% for error in form.photo.errors %}
                                    <div class="text-danger small">{{ error }}</div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Contact Information Section -->
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0"><i class="bi bi-telephone me-2"></i>Contact Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.phone_number_primary.id_for_label }}" class="form-label">{{ form.phone_number_primary.label }}</label>
                                {% render_field form.phone_number_primary class+="form-control" %}
                                {% if form.phone_number_primary.help_text %}
                                    <small class="form-text text-muted">{{ form.phone_number_primary.help_text|safe }}</small>
                                {% endif %}
                                {% for error in form.phone_number_primary.errors %}
                                    <div class="text-danger small">{{ error }}</div>
                                {% endfor %}
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="{{ form.phone_number_alternate.id_for_label }}" class="form-label">{{ form.phone_number_alternate.label }}</label>
                                {% render_field form.phone_number_alternate class+="form-control" %}
                                {% if form.phone_number_alternate.help_text %}
                                    <small class="form-text text-muted">{{ form.phone_number_alternate.help_text|safe }}</small>
                                {% endif %}
                                {% for error in form.phone_number_alternate.errors %}
                                    <div class="text-danger small">{{ error }}</div>
                                {% endfor %}
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.address_line1.id_for_label }}" class="form-label">{{ form.address_line1.label }}</label>
                                {% render_field form.address_line1 class+="form-control" %}
                                {% if form.address_line1.help_text %}
                                    <small class="form-text text-muted">{{ form.address_line1.help_text|safe }}</small>
                                {% endif %}
                                {% for error in form.address_line1.errors %}
                                    <div class="text-danger small">{{ error }}</div>
                                {% endfor %}
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="{{ form.address_line2.id_for_label }}" class="form-label">{{ form.address_line2.label }}</label>
                                {% render_field form.address_line2 class+="form-control" %}
                                {% if form.address_line2.help_text %}
                                    <small class="form-text text-muted">{{ form.address_line2.help_text|safe }}</small>
                                {% endif %}
                                {% for error in form.address_line2.errors %}
                                    <div class="text-danger small">{{ error }}</div>
                                {% endfor %}
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <label for="{{ form.city.id_for_label }}" class="form-label">{{ form.city.label }}</label>
                                {% render_field form.city class+="form-control" %}
                                {% if form.city.help_text %}
                                    <small class="form-text text-muted">{{ form.city.help_text|safe }}</small>
                                {% endif %}
                                {% for error in form.city.errors %}
                                    <div class="text-danger small">{{ error }}</div>
                                {% endfor %}
                            </div>

                            <div class="col-md-3 mb-3">
                                <label for="{{ form.state_province.id_for_label }}" class="form-label">{{ form.state_province.label }}</label>
                                {% render_field form.state_province class+="form-control" %}
                                {% if form.state_province.help_text %}
                                    <small class="form-text text-muted">{{ form.state_province.help_text|safe }}</small>
                                {% endif %}
                                {% for error in form.state_province.errors %}
                                    <div class="text-danger small">{{ error }}</div>
                                {% endfor %}
                            </div>

                            <div class="col-md-3 mb-3">
                                <label for="{{ form.postal_code.id_for_label }}" class="form-label">{{ form.postal_code.label }}</label>
                                {% render_field form.postal_code class+="form-control" %}
                                {% if form.postal_code.help_text %}
                                    <small class="form-text text-muted">{{ form.postal_code.help_text|safe }}</small>
                                {% endif %}
                                {% for error in form.postal_code.errors %}
                                    <div class="text-danger small">{{ error }}</div>
                                {% endfor %}
                            </div>

                            <div class="col-md-3 mb-3">
                                <label for="{{ form.country.id_for_label }}" class="form-label">{{ form.country.label }}</label>
                                {% render_field form.country class+="form-control" %}
                                {% if form.country.help_text %}
                                    <small class="form-text text-muted">{{ form.country.help_text|safe }}</small>
                                {% endif %}
                                {% for error in form.country.errors %}
                                    <div class="text-danger small">{{ error }}</div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Additional Information Section -->
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0"><i class="bi bi-journal-text me-2"></i>Additional Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-12 mb-3">
                                <label for="{{ form.notes.id_for_label }}" class="form-label">{{ form.notes.label }}</label>
                                {% render_field form.notes class+="form-control" %}
                                {% if form.notes.help_text %}
                                    <small class="form-text text-muted">{{ form.notes.help_text|safe }}</small>
                                {% endif %}
                                {% for error in form.notes.errors %}
                                    <div class="text-danger small">{{ error }}</div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>

                <div class="d-flex justify-content-end gap-3 mt-4">
                    <a href="{% url 'schools:staff_list' %}" class="btn btn-secondary">
                        <i class="bi bi-arrow-left me-2"></i>{% translate "Cancel" %}
                    </a>
                    <button type="submit" class="btn btn-success">
                        <i class="bi bi-check-circle me-2"></i>
                        {% if form.instance.pk %}
                            {% translate "Update Staff" %}
                        {% else %}
                            {% translate "Create Staff" %}
                        {% endif %}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
