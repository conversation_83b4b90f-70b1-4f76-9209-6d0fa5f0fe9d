{# templates/public_site/contact.html #}
{% extends "public_base.html" %}
{% load static %}

{% block title %}{{ view_title }} - School Fees Management Platform{% endblock %}

{% block extra_public_css %}
<style>
    .contact-hero {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 4rem 0;
        text-align: center;
        position: relative;
        overflow: hidden;
    }

    .contact-hero::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        opacity: 0.3;
    }

    .contact-hero h1 {
        font-size: 3rem;
        font-weight: 700;
        margin-bottom: 1rem;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        position: relative;
        z-index: 1;
    }

    .contact-hero p {
        font-size: 1.2rem;
        opacity: 0.9;
        margin-bottom: 0;
        position: relative;
        z-index: 1;
        max-width: 600px;
        margin: 0 auto;
    }

    .contact-container {
        padding: 4rem 0;
        background: #f8fafc;
    }

    .contact-form-card {
        background: white;
        border-radius: 20px;
        padding: 3rem;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        border: 1px solid #e2e8f0;
    }

    .contact-info-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 20px;
        padding: 3rem;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
    }

    .premium-form-group {
        position: relative;
        margin-bottom: 2rem;
    }

    .premium-input,
    .premium-select,
    .premium-textarea {
        width: 100%;
        padding: 1rem 1rem 1rem 3rem;
        border: 2px solid #e2e8f0;
        border-radius: 12px;
        font-size: 1rem;
        transition: all 0.3s ease;
        background: #ffffff;
        color: #2d3748;
    }

    .premium-input:focus,
    .premium-select:focus,
    .premium-textarea:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        transform: translateY(-2px);
    }

    .premium-textarea {
        resize: vertical;
        min-height: 120px;
    }

    .premium-label {
        position: absolute;
        left: 3rem;
        top: 1rem;
        color: #718096;
        font-size: 1rem;
        transition: all 0.3s ease;
        pointer-events: none;
        background: white;
        padding: 0 0.5rem;
    }

    .premium-input:focus + .premium-label,
    .premium-input:not(:placeholder-shown) + .premium-label,
    .premium-select:focus + .premium-label,
    .premium-select:not([value=""]) + .premium-label,
    .premium-textarea:focus + .premium-label,
    .premium-textarea:not(:placeholder-shown) + .premium-label {
        top: -0.5rem;
        left: 2.5rem;
        font-size: 0.875rem;
        color: #667eea;
        font-weight: 500;
    }

    .field-icon {
        position: absolute;
        left: 1rem;
        top: 1rem;
        color: #718096;
        font-size: 1.1rem;
        transition: all 0.3s ease;
        z-index: 1;
    }

    .premium-input:focus ~ .field-icon,
    .premium-select:focus ~ .field-icon,
    .premium-textarea:focus ~ .field-icon {
        color: #667eea;
    }

    .help-text {
        font-size: 0.875rem;
        color: #718096;
        margin-top: 0.5rem;
        margin-left: 3rem;
    }

    .error-text {
        font-size: 0.875rem;
        color: #e53e3e;
        margin-top: 0.5rem;
        margin-left: 3rem;
    }

    .premium-btn {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        color: white;
        padding: 1rem 3rem;
        border-radius: 12px;
        font-size: 1.1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        width: 100%;
    }

    .premium-btn:hover {
        background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
    }

    .premium-btn:active {
        transform: translateY(0);
    }

    .contact-info-item {
        display: flex;
        align-items: center;
        margin-bottom: 2rem;
        padding: 1rem;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 12px;
        backdrop-filter: blur(10px);
    }

    .contact-info-icon {
        width: 50px;
        height: 50px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1rem;
        font-size: 1.2rem;
    }

    .contact-info-content h4 {
        margin: 0 0 0.25rem 0;
        font-size: 1.1rem;
        font-weight: 600;
    }

    .contact-info-content p {
        margin: 0;
        opacity: 0.9;
        font-size: 0.95rem;
    }

    .form-section-title {
        text-align: center;
        margin-bottom: 2rem;
    }

    .form-section-title h2 {
        color: #2d3748;
        font-size: 2rem;
        margin-bottom: 0.5rem;
    }

    .form-section-title p {
        color: #718096;
        font-size: 1.1rem;
        margin: 0;
    }

    .success-message {
        background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 12px;
        margin-bottom: 2rem;
        display: flex;
        align-items: center;
    }

    .success-message i {
        margin-right: 0.75rem;
        font-size: 1.2rem;
    }

    .error-message {
        background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 12px;
        margin-bottom: 2rem;
        display: flex;
        align-items: center;
    }

    .error-message i {
        margin-right: 0.75rem;
        font-size: 1.2rem;
    }

    @media (max-width: 768px) {
        .contact-hero h1 {
            font-size: 2.5rem;
        }

        .contact-hero {
            padding: 3rem 0;
        }

        .contact-form-card,
        .contact-info-card {
            padding: 2rem;
            margin-bottom: 2rem;
        }

        .premium-input,
        .premium-select,
        .premium-textarea {
            padding: 0.875rem 0.875rem 0.875rem 2.5rem;
        }

        .premium-label {
            left: 2.5rem;
        }

        .field-icon {
            left: 0.75rem;
        }

        .help-text,
        .error-text {
            margin-left: 2.5rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="contact-hero">
    <div class="container">
        <h1>Get in Touch</h1>
        <p>We're here to help you transform your school's fee management. Reach out to us for support, demos, or any questions.</p>
    </div>
</section>

<!-- Contact Form Section -->
<section class="contact-container">
    <div class="container">
        <div class="row">
            <!-- Contact Form -->
            <div class="col-lg-8">
                <div class="contact-form-card">
                    <div class="form-section-title">
                        <h2>Send us a Message</h2>
                        <p>Fill out the form below and we'll get back to you within 24 hours</p>
                    </div>

                    <!-- Messages -->
                    {% if messages %}
                        {% for message in messages %}
                            {% if message.tags == 'success' %}
                                <div class="success-message">
                                    <i class="bi bi-check-circle"></i>
                                    {{ message }}
                                </div>
                            {% elif message.tags == 'error' %}
                                <div class="error-message">
                                    <i class="bi bi-exclamation-triangle"></i>
                                    {{ message }}
                                </div>
                            {% endif %}
                        {% endfor %}
                    {% endif %}

                    <form method="post" novalidate>
                        {% csrf_token %}

                        <div class="row">
                            <!-- Name Field -->
                            <div class="col-md-6">
                                <div class="premium-form-group">
                                    {{ form.name }}
                                    <label for="{{ form.name.id_for_label }}" class="premium-label">{{ form.name.label }}</label>
                                    <i class="bi bi-person field-icon"></i>
                                    {% if form.name.errors %}
                                        <div class="error-text">{{ form.name.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>

                            <!-- Email Field -->
                            <div class="col-md-6">
                                <div class="premium-form-group">
                                    {{ form.email }}
                                    <label for="{{ form.email.id_for_label }}" class="premium-label">{{ form.email.label }}</label>
                                    <i class="bi bi-envelope field-icon"></i>
                                    {% if form.email.errors %}
                                        <div class="error-text">{{ form.email.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- Phone Field -->
                            <div class="col-md-6">
                                <div class="premium-form-group">
                                    {{ form.phone }}
                                    <label for="{{ form.phone.id_for_label }}" class="premium-label">{{ form.phone.label }}</label>
                                    <i class="bi bi-telephone field-icon"></i>
                                    {% if form.phone.help_text %}
                                        <div class="help-text">{{ form.phone.help_text }}</div>
                                    {% endif %}
                                    {% if form.phone.errors %}
                                        <div class="error-text">{{ form.phone.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>

                            <!-- Organization Field -->
                            <div class="col-md-6">
                                <div class="premium-form-group">
                                    {{ form.organization }}
                                    <label for="{{ form.organization.id_for_label }}" class="premium-label">{{ form.organization.label }}</label>
                                    <i class="bi bi-building field-icon"></i>
                                    {% if form.organization.help_text %}
                                        <div class="help-text">{{ form.organization.help_text }}</div>
                                    {% endif %}
                                    {% if form.organization.errors %}
                                        <div class="error-text">{{ form.organization.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- Inquiry Type Field -->
                            <div class="col-md-6">
                                <div class="premium-form-group">
                                    {{ form.inquiry_type }}
                                    <label for="{{ form.inquiry_type.id_for_label }}" class="premium-label">{{ form.inquiry_type.label }}</label>
                                    <i class="bi bi-tag field-icon"></i>
                                    {% if form.inquiry_type.errors %}
                                        <div class="error-text">{{ form.inquiry_type.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>

                            <!-- Subject Field -->
                            <div class="col-md-6">
                                <div class="premium-form-group">
                                    {{ form.subject }}
                                    <label for="{{ form.subject.id_for_label }}" class="premium-label">{{ form.subject.label }}</label>
                                    <i class="bi bi-chat-dots field-icon"></i>
                                    {% if form.subject.errors %}
                                        <div class="error-text">{{ form.subject.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Message Field -->
                        <div class="premium-form-group">
                            {{ form.message }}
                            <label for="{{ form.message.id_for_label }}" class="premium-label">{{ form.message.label }}</label>
                            <i class="bi bi-chat-text field-icon"></i>
                            {% if form.message.help_text %}
                                <div class="help-text">{{ form.message.help_text }}</div>
                            {% endif %}
                            {% if form.message.errors %}
                                <div class="error-text">{{ form.message.errors.0 }}</div>
                            {% endif %}
                        </div>

                        <!-- Submit Button -->
                        <button type="submit" class="premium-btn">
                            <i class="bi bi-send me-2"></i>
                            Send Message
                        </button>
                    </form>
                </div>
            </div>

            <!-- Contact Information -->
            <div class="col-lg-4">
                <div class="contact-info-card">
                    <h3 style="margin-bottom: 2rem; text-align: center;">Contact Information</h3>

                    <div class="contact-info-item">
                        <div class="contact-info-icon">
                            <i class="bi bi-envelope"></i>
                        </div>
                        <div class="contact-info-content">
                            <h4>Email Us</h4>
                            <p><EMAIL></p>
                        </div>
                    </div>

                    <div class="contact-info-item">
                        <div class="contact-info-icon">
                            <i class="bi bi-telephone"></i>
                        </div>
                        <div class="contact-info-content">
                            <h4>Call Us</h4>
                            <p>+****************</p>
                        </div>
                    </div>

                    <div class="contact-info-item">
                        <div class="contact-info-icon">
                            <i class="bi bi-clock"></i>
                        </div>
                        <div class="contact-info-content">
                            <h4>Business Hours</h4>
                            <p>Mon-Fri: 9AM-6PM EST<br>Sat-Sun: 10AM-4PM EST</p>
                        </div>
                    </div>

                    <div class="contact-info-item">
                        <div class="contact-info-icon">
                            <i class="bi bi-headset"></i>
                        </div>
                        <div class="contact-info-content">
                            <h4>24/7 Support</h4>
                            <p>Emergency technical support available for premium customers</p>
                        </div>
                    </div>

                    <div class="contact-info-item">
                        <div class="contact-info-icon">
                            <i class="bi bi-calendar-check"></i>
                        </div>
                        <div class="contact-info-content">
                            <h4>Schedule Demo</h4>
                            <p>Book a personalized demo to see our platform in action</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}


