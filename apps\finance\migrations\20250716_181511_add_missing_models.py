# Generated migration for missing finance models
from django.db import migrations, models

class Migration(migrations.Migration):
    dependencies = [
        ('finance', '0001_initial'),  # Adjust based on your last migration
    ]

    operations = [
        migrations.CreateModel(
            name='Vendor',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.Char<PERSON>ield(max_length=255)),
                ('contact_person', models.Char<PERSON>ield(max_length=200, blank=True)),
                ('email', models.EmailField(max_length=254, blank=True)),
                ('phone', models.Char<PERSON>ield(max_length=20, blank=True)),
                ('phone_number', models.CharField(max_length=20, blank=True)),
                ('address', models.TextField(blank=True)),
                ('tax_id', models.Char<PERSON>ield(max_length=50, blank=True)),
                ('payment_terms', models.Char<PERSON>ield(max_length=100, blank=True)),
                ('is_active', models.<PERSON><PERSON>an<PERSON>ield(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='BudgetItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('description', models.TextField(blank=True)),
                ('budget_amount', models.DecimalField(decimal_places=2, default=0, max_digits=15)),
                ('actual_amount', models.DecimalField(decimal_places=2, default=0, max_digits=15)),
                ('variance_amount', models.DecimalField(decimal_places=2, default=0, max_digits=15)),
                ('budget_period_start', models.DateField(null=True, blank=True)),
                ('budget_period_end', models.DateField(null=True, blank=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
    ]
