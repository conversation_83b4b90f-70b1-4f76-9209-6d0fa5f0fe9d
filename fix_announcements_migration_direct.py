#!/usr/bin/env python
"""
Direct Fix for Announcements Migration Issue
This script directly creates the announcements table in all schemas

Usage: python fix_announcements_migration_direct.py
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from django.db import connection
from django_tenants.utils import schema_context
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_announcements_table_directly(schema_name='public'):
    """Create announcements table directly with <PERSON><PERSON>"""
    
    create_table_sql = """
    CREATE TABLE IF NOT EXISTS announcements_announcement (
        id BIGSERIAL PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        content TEXT NOT NULL,
        target_all_tenant_staff BOOLEAN NOT NULL DEFAULT FALSE,
        target_all_tenant_parents BOOLEAN NOT NULL DEFAULT FALSE,
        is_global BOOLEAN NOT NULL DEFAULT FALSE,
        target_global_audience_type VARCHAR(50),
        publish_date TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
        expiry_date TIMESTAMP WITH TIME ZONE,
        is_published BOOLEAN NOT NULL DEFAULT TRUE,
        is_sticky BOOLEAN NOT NULL DEFAULT FALSE,
        created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
        author_id BIGINT,
        tenant_id BIGINT
    );
    
    CREATE INDEX IF NOT EXISTS announcements_announcement_publish_date_idx 
        ON announcements_announcement (publish_date);
    CREATE INDEX IF NOT EXISTS announcements_announcement_expiry_date_idx 
        ON announcements_announcement (expiry_date);
    CREATE INDEX IF NOT EXISTS announcements_announcement_is_published_idx 
        ON announcements_announcement (is_published);
    CREATE INDEX IF NOT EXISTS announcements_announcement_is_sticky_idx 
        ON announcements_announcement (is_sticky);
    """
    
    with connection.cursor() as cursor:
        # Set schema
        cursor.execute(f'SET search_path TO "{schema_name}";')
        
        # Create table
        cursor.execute(create_table_sql)
        
        logger.info(f"✅ Created announcements_announcement table in schema: {schema_name}")

def mark_announcements_migrations_applied(schema_name='public'):
    """Mark announcements migrations as applied"""
    
    migrations_to_mark = [
        ('announcements', '0001_initial'),
        ('announcements', '0002_initial'),
    ]
    
    with connection.cursor() as cursor:
        # Set schema
        cursor.execute(f'SET search_path TO "{schema_name}";')
        
        # Check if django_migrations table exists
        cursor.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_name = 'django_migrations'
            );
        """)
        
        if cursor.fetchone()[0]:
            for app, migration in migrations_to_mark:
                # Check if migration is already marked
                cursor.execute("""
                    SELECT COUNT(*) FROM django_migrations 
                    WHERE app = %s AND name = %s
                """, [app, migration])
                
                if cursor.fetchone()[0] == 0:
                    # Mark migration as applied
                    cursor.execute("""
                        INSERT INTO django_migrations (app, name, applied) 
                        VALUES (%s, %s, NOW())
                    """, [app, migration])
                    
                    logger.info(f"✅ Marked {app}.{migration} as applied in {schema_name}")

def fix_all_schemas():
    """Fix announcements table in all schemas"""
    logger.info("=== FIXING ANNOUNCEMENTS TABLES IN ALL SCHEMAS ===")
    
    # Fix public schema first
    logger.info("Fixing public schema...")
    create_announcements_table_directly('public')
    mark_announcements_migrations_applied('public')
    
    # Fix existing tenant schemas
    try:
        from apps.tenants.models import School
        tenants = School.objects.all()
        
        for tenant in tenants:
            logger.info(f"Fixing tenant schema: {tenant.schema_name}")
            try:
                create_announcements_table_directly(tenant.schema_name)
                mark_announcements_migrations_applied(tenant.schema_name)
            except Exception as e:
                logger.error(f"Failed to fix {tenant.schema_name}: {e}")
                
    except Exception as e:
        logger.error(f"Failed to get tenants: {e}")

def test_announcements_table():
    """Test that announcements table exists and works"""
    logger.info("=== TESTING ANNOUNCEMENTS TABLE ===")
    
    try:
        # Test public schema
        with connection.cursor() as cursor:
            cursor.execute('SET search_path TO "public";')
            cursor.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_name = 'announcements_announcement'
                );
            """)
            
            if cursor.fetchone()[0]:
                logger.info("✅ Announcements table exists in public schema")
            else:
                logger.error("❌ Announcements table missing in public schema")
                return False
        
        # Test tenant schemas
        from apps.tenants.models import School
        tenants = School.objects.all()
        
        for tenant in tenants:
            with connection.cursor() as cursor:
                cursor.execute(f'SET search_path TO "{tenant.schema_name}";')
                cursor.execute("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_name = 'announcements_announcement'
                    );
                """)
                
                if cursor.fetchone()[0]:
                    logger.info(f"✅ Announcements table exists in {tenant.schema_name}")
                else:
                    logger.error(f"❌ Announcements table missing in {tenant.schema_name}")
                    return False
        
        return True
        
    except Exception as e:
        logger.error(f"Test failed: {e}")
        return False

def main():
    """Main function"""
    logger.info("=== DIRECT ANNOUNCEMENTS FIX ===")
    
    try:
        # Step 1: Fix all schemas
        fix_all_schemas()
        
        # Step 2: Test the fix
        if test_announcements_table():
            logger.info("🎉 ALL ANNOUNCEMENTS TABLES FIXED!")
            logger.info("You can now test tenant registration.")
            return True
        else:
            logger.error("💥 SOME TABLES STILL MISSING!")
            return False
            
    except Exception as e:
        logger.error(f"Fix failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
