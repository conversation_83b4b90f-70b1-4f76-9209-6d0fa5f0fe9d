{# D:\school_fees_saas_v2\apps\accounting\templates\accounting\account_form.html #}
{% extends "tenant_base.html" %}
{% load static i18n widget_tweaks %}

{% block tenant_page_title %}
    {% if object %}
        {% blocktrans with object_name=object.name %}Edit Account: {{ object_name }}{% endblocktrans %}
    {% else %}
        {% trans "Create New Account" %}
    {% endif %}
{% endblock tenant_page_title %}

{% block extra_tenant_css %}
    {{ block.super }}
    {# If you use Select2 for dropdowns, include its CSS here or in a base template #}
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />
    <style>
        /* Optional: Add a subtle transition for the bank details section */
        #bank-details-section {
            transition: opacity 0.3s ease-in-out, max-height 0.3s ease-in-out;
            overflow: hidden;
            max-height: 0;
            opacity: 0;
        }
        #bank-details-section.visible {
            max-height: 500px; /* Adjust if more space is needed */
            opacity: 1;
        }
    </style>
{% endblock %}

{% block tenant_specific_content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-lg-8 col-md-10">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    {% if object %}{% trans "Edit Account" %}{% else %}{% trans "Create New Account" %}{% endif %}
                </h1>
            </div>

            {% include "partials/_messages.html" %}

            <div class="card shadow-sm">
                <div class="card-body p-4">
                    <form method="post" novalidate>
                        {% csrf_token %}

                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {% for error in form.non_field_errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}

                        {# --- Main Account Fields --- #}
                        <div class="row">
                            <div class="col-md-8 mb-3">
                                <label for="{{ form.name.id_for_label }}" class="form-label required">{{ form.name.label }}</label>
                                {% render_field form.name class="form-control" %}
                                {% for error in form.name.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="{{ form.code.id_for_label }}" class="form-label required">{{ form.code.label }}</label>
                                {% render_field form.code class="form-control" %}
                                {% for error in form.code.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.account_type.id_for_label }}" class="form-label required">{{ form.account_type.label }}</label>
                                {% render_field form.account_type class="form-select select2-field" %}
                                {% for error in form.account_type.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.parent_account.id_for_label }}" class="form-label">{{ form.parent_account.label }}</label>
                                {% render_field form.parent_account class="form-select select2-field" %}
                                <div class="form-text">{{ form.parent_account.help_text|safe }}</div>
                                {% for error in form.parent_account.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="{{ form.description.id_for_label }}" class="form-label">{{ form.description.label }}</label>
                            {% render_field form.description class="form-control" rows="3" %}
                        </div>

                        <div class="form-check form-switch mb-3">
                            {% render_field form.is_active class="form-check-input" %}
                            <label for="{{ form.is_active.id_for_label }}" class="form-check-label">{{ form.is_active.label }}</label>
                        </div>
                        <div class="form-check form-switch mb-3">
                            {% render_field form.is_control_account class="form-check-input" %}
                            <label for="{{ form.is_control_account.id_for_label }}" class="form-check-label">{{ form.is_control_account.label }}</label>
                            <div class="form-text">{{ form.is_control_account.help_text|safe }}</div>
                        </div>

                        {# --- Hidden Bank Details Section --- #}
                        <div id="bank-details-section">
                            <hr class="my-4">
                            <h5 class="mb-3">{% trans "Bank Account Details" %}</h5>
                            <p class="text-muted small">{% trans "These fields are optional and only apply if this is a bank account." %}</p>
                            <div class="row">
                                <div class="col-md-6 mb-3"><label for="{{ form.bank_name.id_for_label }}" class="form-label">{{ form.bank_name.label }}</label>{% render_field form.bank_name class="form-control" %}</div>
                                <div class="col-md-6 mb-3"><label for="{{ form.account_holder_name.id_for_label }}" class="form-label">{{ form.account_holder_name.label }}</label>{% render_field form.account_holder_name class="form-control" %}</div>
                            </div>
                            <div class="row">
                                <div class="col-md-6 mb-3"><label for="{{ form.account_number.id_for_label }}" class="form-label">{{ form.account_number.label }}</label>{% render_field form.account_number class="form-control" %}</div>
                                <div class="col-md-6 mb-3"><label for="{{ form.branch_code.id_for_label }}" class="form-label">{{ form.branch_code.label }}</label>{% render_field form.branch_code class="form-control" %}</div>
                            </div>
                            <div class="mb-3"><label for="{{ form.swift_code.id_for_label }}" class="form-label">{{ form.swift_code.label }}</label>{% render_field form.swift_code class="form-control" %}</div>
                        </div>
                        {# --- END --- #}

                        <hr class="my-4">
                        <div class="d-flex justify-content-end gap-2">
                            <a href="{% url 'accounting:accounts_list' %}" class="btn btn-secondary"><i class="bi bi-x-circle me-1"></i> {% trans "Cancel" %}</a>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-circle-fill me-1"></i>
                                {% if object %}{% trans "Save Changes" %}{% else %}{% trans "Create Account" %}{% endif %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock tenant_specific_content %}

{% block page_specific_js %}
    {{ block.super }}
    {# Add script for Select2 and dynamic fields #}
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.5.0/dist/jquery.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize Select2 on dropdowns
        $('.select2-field').select2({
            theme: "bootstrap-5",
            width: '100%'
        });

        // --- DYNAMIC BANK DETAILS SCRIPT ---
        const accountTypeSelect = document.getElementById('{{ form.account_type.id_for_label }}');
        const bankDetailsSection = document.getElementById('bank-details-section');
        
        // Get the list of PKs from the form attribute we passed in the view's form __init__
        const bankAccountTypePKs = JSON.parse('{{ form.bank_account_type_pks_json|default:"[]"|escapejs }}');

        function toggleBankDetails() {
            if (!accountTypeSelect || !bankDetailsSection) return; // Exit if elements not found
            
            const selectedTypePK = parseInt(accountTypeSelect.value, 10);
            
            // Check if the selected PK is in our list of bank type PKs
            if (bankAccountTypePKs.includes(selectedTypePK)) {
                bankDetailsSection.classList.add('visible');
            } else {
                bankDetailsSection.classList.remove('visible');
            }
        }

        // Run the check when the page loads and when the dropdown changes
        toggleBankDetails();
        if (accountTypeSelect) {
            accountTypeSelect.addEventListener('change', toggleBankDetails);
        }
    });
    </script>
{% endblock %}




















{% comment %} {% extends "tenant_base.html" %}
{% load static i18n widget_tweaks %} {# widget_tweaks is optional but helpful #}

{% block title %}
    {% if form.instance.pk %}
        {% translate "Edit Chart of Account Entry" %} - {{ form.instance.name|default:"Account" }}
    {% else %}
        {% translate "Create New Chart of Account Entry" %}
    {% endif %}
{% endblock %}

{% block page_specific_css %}
    {{ block.super }}
    {# Add link to Select2 CSS if you're using it #}
    {# <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" /> #}
{% endblock %}


{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-lg-8 offset-lg-2">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        {% if form.instance.pk %}
                            <i class="bi bi-pencil-square me-2"></i>{% translate "Edit Chart of Account Entry" %}
                            <small class="text-white-75 ms-2">({{ form.instance.name|truncatechars:30 }})</small>
                        {% else %}
                            <i class="bi bi-journal-plus me-2"></i>{% translate "Create New Chart of Account Entry" %}
                        {% endif %}
                    </h4>
                </div>
                <div class="card-body p-4">
                    <form method="post" novalidate>
                        {% csrf_token %}


                        {# --- NEW: Hidden Bank Details Section --- #}
                        <div id="bank-details-section" style="display: none;">
                            <hr>
                            <h5>Bank Account Details</h5>
                            {{ form.bank_name.label_tag }}
                            {{ form.bank_name }}
                            
                            {{ form.account_holder_name.label_tag }}
                            {{ form.account_holder_name }}

                            {{ form.account_number.label_tag }}
                            {{ form.account_number }}
                            
                            {{ form.branch_code.label_tag }}
                            {{ form.branch_code }}

                            {{ form.swift_code.label_tag }}
                            {{ form.swift_code }}
                        </div>
                        {# --- END --- #}


                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {% for error in form.non_field_errors %}
                                    {{ error }}<br>
                                {% endfor %}
                            </div>
                        {% endif %}

                        {# Iterate over form fields for more control, or use {{ form.as_p }} for simplicity #}
                        {% for field in form %}
                            <div class="mb-3">
                                <label for="{{ field.id_for_label }}" class="form-label">
                                    {{ field.label }}{% if field.field.required %}<span class="text-danger">*</span>{% endif %}
                                </label>
                                
                                {% if field.name == 'is_active' or field.name == 'is_control_account' %}
                                    <div class="form-check">
                                        {% render_field field class+="form-check-input" %}
                                        <label class="form-check-label" for="{{ field.id_for_label }}">
                                            {{ field.help_text|safe }}
                                        </label>
                                    </div>
                                {% else %}
                                    {% render_field field class+="form-control" %}
                                {% endif %}

                                {% if field.help_text and field.name != 'is_active' and field.name != 'is_control_account' %}
                                    <small class="form-text text-muted">{{ field.help_text|safe }}</small>
                                {% endif %}
                                {% for error in field.errors %}
                                    <div class="invalid-feedback d-block"> 
                                        {{ error }}
                                    </div>
                                {% endfor %}
                            </div>
                        {% endfor %}

                        <hr>
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'accounting:accounts_list' %}" class="btn btn-outline-secondary">
                                <i class="bi bi-x-circle me-1"></i> {% translate "Cancel" %}
                            </a>
                            <button type="submit" class="btn btn-success">
                                <i class="bi bi-check-circle me-1"></i>
                                {% if form.instance.pk %}
                                    {% translate "Save Changes" %}
                                {% else %}
                                    {% translate "Create Account" %}
                                {% endif %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock content %}

{% block page_specific_js %}
    {{ block.super }}
    {# Add script for Select2 if you're using it #}
    {# <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script>
    // $(document).ready(function() {
    //     $('.select2-account-type').select2({ placeholder: "Select an account type", allowClear: true });
    //     $('.select2-parent-account').select2({ placeholder: "Select a parent account (optional)", allowClear: true });
    // });
    </script> #}
{% endblock %}

 {% endcomment %}
