# Python Standard Library Imports
import csv
from decimal import Decimal, InvalidOperation
from io import BytesIO
import logging

# Django Imports
from django.core.paginator import Paginator
from django.db.models import Sum, F, Q, OuterRef, Subquery, ExpressionWrapper, Dec<PERSON>l<PERSON>ield, Count
from django.db.models.functions import Coalesce
from django.http import HttpResponse
from django.shortcuts import redirect
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django.views.generic import TemplateView
from django.contrib import messages

# Third-Party Imports
try:
    import openpyxl
    from openpyxl.styles import Font, Alignment
    from openpyxl.utils import get_column_letter
except ImportError:
    openpyxl = None

# --- CORRECTED MODEL IMPORTS FROM SPECIFIC APPS ---
# Note: Adjust these paths if your app names are different.
from apps.accounting.models import Account, AccountType, JournalEntryItem
from apps.fees.models import Invoice, InvoiceStatus, FeeStructure, StudentConcession
from apps.payments.models import Payment
from apps.finance.models import PaymentMethod, Expense, ExpenseCategory, BudgetAmount, BudgetItem
from apps.schools.models import SchoolClass, AcademicYear, SchoolProfile
from apps.students.models import Student

# --- LOCAL APP IMPORTS ---
from apps.common.mixins import TenantPermissionRequiredMixin, BaseReportViewMixin
from .forms import (
    StudentLedgerFilterForm, BudgetReportFilterForm, DateRangeForm,
    BalanceSheetFilterForm, OutstandingFeesFilterForm, CollectionReportFilterForm,
    ReportPeriodForm, IncomeExpenseReportForm, ExpenseReportFilterForm,
    FeeProjectionFilterForm, GeneralLedgerFilterForm, AccountsReceivableAgingFilterForm,
    AccountsPayableFilterForm, BankReconciliationFilterForm, BudgetVsActualFilterForm,
    FeeCollectionAnalysisFilterForm, StudentAccountStatementFilterForm, ClasswiseFeeCollectionFilterForm,
    FeeDefaultersFilterForm, CashFlowForecastingFilterForm, ProfitabilityAnalysisFilterForm,
    FinancialRatioAnalysisFilterForm, RevenueRecognitionFilterForm
)
from .filters import PaymentReportFilter
from apps.common.utils import render_to_pdf, get_financial_year_start

logger = logging.getLogger(__name__)


# = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = 
#
#      OUTSTANDING FEES REPORT
#
# = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =


class OutstandingFeesReportView(TenantPermissionRequiredMixin, BaseReportViewMixin, TemplateView):
    """
    Handles the display of the HTML Outstanding Fees report page.
    """
    template_name = 'reporting/outstanding_fees_report.html'
    
    # Attributes for BaseReportViewMixin
    permission_required = 'reporting.view_outstanding_fees_report'
    report_title = _("Outstanding Fees Report")
    filter_form_class = OutstandingFeesFilterForm # Using a dedicated form now
    export_csv_url_name = 'reporting:outstanding_fees_export_csv'
    export_pdf_url_name = 'reporting:outstanding_fees_export_pdf'
    export_excel_url_name = 'reporting:outstanding_fees_export_excel'
    paginate_by = 25 # Define pagination size here

    def _get_outstanding_fees_data(self, request):
        """
        The core logic for fetching and processing all data for the Outstanding Fees report.
        This is used by the HTML view and all export views.
        """
        # 1. Initialize the data structure
        data = {
            'students_page': None, 'full_queryset': Student.objects.none(),
            'total_outstanding': Decimal('0.00'), 'filter_form': self.filter_form_class(request.GET or None)
        }
        
        # 2. Base queryset and filters
        base_queryset = Student.objects.filter(is_active=True).select_related(
            'current_class', 'current_section'
        ).prefetch_related('parents')

        if data['filter_form'].is_valid():
            class_filter = data['filter_form'].cleaned_data.get('school_class')
            if class_filter:
                base_queryset = base_queryset.filter(current_class=class_filter)
        
        # 3. Define outstanding statuses and perform annotations
        outstanding_statuses = [InvoiceStatus.SENT, InvoiceStatus.PARTIALLY_PAID, InvoiceStatus.OVERDUE]
        
        # Subquery to sum the calculated balance of outstanding invoices for each student
        student_outstanding_balance = Invoice.objects.filter(
            student_id=OuterRef('pk'), status__in=outstanding_statuses
        ).values('student_id').annotate(
            total_due=Sum(
                (F('subtotal_amount') - F('total_concession_amount')) - F('amount_paid')
            )
        ).values('total_due')

        annotated_queryset = base_queryset.annotate(
            outstanding_balance=Coalesce(
                Subquery(student_outstanding_balance, output_field=DecimalField()),
                Decimal('0.00')
            )
        )
        
        # 4. Filter based on calculated balance and form fields
        final_queryset = annotated_queryset.filter(outstanding_balance__gt=Decimal('0.01'))

        if data['filter_form'].is_valid():
            min_due_amount = data['filter_form'].cleaned_data.get('min_due_amount')
            if min_due_amount:
                final_queryset = final_queryset.filter(outstanding_balance__gte=min_due_amount)

        final_queryset = final_queryset.order_by('current_class__name', 'last_name', 'first_name').distinct()
        
        data['full_queryset'] = final_queryset

        # 5. Calculate grand total from the full (un-paginated) queryset
        aggregation = final_queryset.aggregate(total=Sum('outstanding_balance'))
        data['total_outstanding'] = aggregation.get('total') or Decimal('0.00')
        
        # 6. Paginate the results for the HTML view
        paginator = Paginator(final_queryset, self.paginate_by)
        page_number = request.GET.get('page', 1)
        data['students_page'] = paginator.get_page(page_number)
        
        return data

    def get_context_data(self, **kwargs):
        """Prepares context for the HTML page by calling the main data helper."""
        context = super().get_context_data(**kwargs)
        report_data = self._get_outstanding_fees_data(self.request)
        context.update(report_data)
        
        # Add additional context if needed
        context['school_profile'] = SchoolProfile.objects.first()
        
        return context


class OutstandingFeesExportCSVView(OutstandingFeesReportView):
    """Handles CSV export for the Outstanding Fees report."""
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        report_data = self._get_outstanding_fees_data(request)
        queryset = report_data.get('full_queryset')

        response = HttpResponse(content_type='text/csv')
        filename = f"Outstanding_Fees_{timezone.now().strftime('%Y%m%d')}.csv"
        response['Content-Disposition'] = f'attachment; filename="{filename}"'
        writer = csv.writer(response)
        
        headers = ['Admission No', 'Full Name', 'Class', 'Primary Parent', 'Parent Phone', 'Outstanding Balance']
        writer.writerow(headers)

        for student in queryset:
            parent = student.parents.first()
            writer.writerow([
                student.admission_number, student.get_full_name(),
                student.current_class.name if student.current_class else 'N/A',
                parent.get_full_name() if parent else 'N/A',
                parent.phone_number if parent else 'N/A',
                student.outstanding_balance
            ])
        return response


class OutstandingFeesExportExcelView(OutstandingFeesReportView):
    """Handles Excel export for the Outstanding Fees report."""
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        if not openpyxl:
            messages.error(request, "Excel export library not available.")
            return redirect('reporting:outstanding_fees_report')

        report_data = self._get_outstanding_fees_data(request)
        queryset = report_data.get('full_queryset')
        school_profile = SchoolProfile.objects.first()
        currency_symbol = school_profile.currency_symbol if school_profile else '$'
        currency_format = f'"{currency_symbol}"#,##0.00'

        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "Outstanding Fees"
        
        headers = ['Admission No', 'Full Name', 'Class', 'Primary Parent', 'Parent Phone', 'Outstanding Balance']
        ws.append(headers)
        header_font = Font(bold=True)
        for cell in ws[1]:
            cell.font = header_font

        for student in queryset:
            parent = student.parents.first()
            row_data = [
                student.admission_number, student.get_full_name(),
                student.current_class.name if student.current_class else 'N/A',
                parent.get_full_name() if parent else 'N/A',
                parent.phone_number if parent else 'N/A',
                student.outstanding_balance
            ]
            ws.append(row_data)
        
        # Formatting
        ws.column_dimensions[get_column_letter(1)].width = 15
        ws.column_dimensions[get_column_letter(2)].width = 25
        ws.column_dimensions[get_column_letter(3)].width = 20
        ws.column_dimensions[get_column_letter(4)].width = 25
        ws.column_dimensions[get_column_letter(5)].width = 20
        ws.column_dimensions[get_column_letter(6)].width = 20
        for cell in ws['F']:
            cell.number_format = currency_format

        response = HttpResponse(content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        filename = f"Outstanding_Fees_{timezone.now().strftime('%Y%m%d')}.xlsx"
        response['Content-Disposition'] = f'attachment; filename="{filename}"'
        wb.save(response)
        return response


class OutstandingFeesExportPDFView(OutstandingFeesReportView):
    """Handles PDF export for the Outstanding Fees report."""
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        report_data = self._get_outstanding_fees_data(request)
        
        pdf_context = report_data.copy()
        pdf_context.update({
            'report_title': self.report_title,
            'school_profile': SchoolProfile.objects.first(),
            'tenant': getattr(request, 'tenant', None),
            'current_datetime': timezone.now()
        })
        
        try:
            pdf = render_to_pdf('reporting/pdf/outstanding_fees_pdf.html', pdf_context)
            if pdf:
                response = HttpResponse(pdf, content_type='application/pdf')
                filename = f"Outstanding_Fees_{timezone.now().strftime('%Y%m%d')}.pdf"
                response['Content-Disposition'] = f'inline; filename="{filename}"'
                return response
        except Exception as e:
            logger.error(f"Error generating Outstanding Fees PDF: {e}", exc_info=True)
            messages.error(request, "An error occurred while generating the PDF report.")
            
        return redirect('reporting:outstanding_fees_report')
    


# = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = 
#
#      COLLECTION REPORT
#
# = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =


class CollectionReportView(TenantPermissionRequiredMixin, BaseReportViewMixin, TemplateView):
    """
    Handles the display of the HTML Fee Collection report page.
    """
    template_name = 'reporting/collection_report.html'
    
    # Attributes for BaseReportViewMixin
    permission_required = 'reporting.view_collection_report'
    report_title = _("Fee Collection Report")
    filter_form_class = CollectionReportFilterForm
    export_csv_url_name = 'reporting:collection_report_export_csv'
    export_pdf_url_name = 'reporting:collection_report_export_pdf'
    export_excel_url_name = 'reporting:collection_report_export_excel'
    paginate_by = 25 # Define pagination size here

    def _get_collection_data(self, request):
        """
        The core logic for fetching and processing all data for the Fee Collection report.
        """
        # 1. Initialize the data structure
        data = {
            'payments_page': None,
            'full_queryset': Payment.objects.none(),
            'total_collected': Decimal('0.00'),
            'transaction_count': 0,
            'collections_by_method': [],
            'start_date': None,
            'end_date': None,
            'filter_form': self.filter_form_class(request.GET or None)
        }
        
        # Set default date range if form is not submitted
        if not request.GET:
            today = timezone.now().date()
            start_date = today.replace(day=1) - timezone.timedelta(days=60) # Default to last ~2 months
            data['filter_form'] = self.filter_form_class(initial={'start_date': start_date, 'end_date': today})

        # 2. Validate form and apply filters
        base_queryset = Payment.objects.select_related(
            'student__current_class', 'payment_method', 'recorded_by', 'parent_payer'
        ).prefetch_related('allocations__invoice').order_by('-payment_date')

        filtered_queryset = base_queryset
        if data['filter_form'].is_valid():
            cd = data['filter_form'].cleaned_data
            data['start_date'] = cd.get('start_date')
            data['end_date'] = cd.get('end_date')

            if data['start_date']:
                filtered_queryset = filtered_queryset.filter(payment_date__date__gte=data['start_date'])
            if data['end_date']:
                filtered_queryset = filtered_queryset.filter(payment_date__date__lte=data['end_date'])
            if cd.get('school_class'):
                filtered_queryset = filtered_queryset.filter(student__current_class=cd['school_class'])
            if cd.get('payment_method'):
                filtered_queryset = filtered_queryset.filter(payment_method=cd['payment_method'])
            if cd.get('student_query'):
                sq = cd['student_query']
                filtered_queryset = filtered_queryset.filter(
                    Q(student__first_name__icontains=sq) |
                    Q(student__last_name__icontains=sq) |
                    Q(student__admission_number__iexact=sq)
                )
        
        data['full_queryset'] = filtered_queryset

        # 3. Calculate summary totals from the full (un-paginated) queryset
        if filtered_queryset.exists():
            summary = filtered_queryset.aggregate(
                total=Coalesce(Sum('amount'), Decimal('0.00')),
                count=Count('id')
            )
            data['total_collected'] = summary['total']
            data['transaction_count'] = summary['count']
            
            data['collections_by_method'] = list(
                filtered_queryset.values('payment_method__name')
                .annotate(total=Sum('amount'), count=Count('id'))
                .order_by('-total')
                .filter(payment_method__name__isnull=False)
            )

        # 4. Paginate the results for the HTML view
        paginator = Paginator(filtered_queryset, self.paginate_by)
        page_number = request.GET.get('page', 1)
        data['payments_page'] = paginator.get_page(page_number)
        
        return data

    def get_context_data(self, **kwargs):
        """Prepares context for the HTML page by calling the main data helper."""
        context = super().get_context_data(**kwargs)
        report_data = self._get_collection_data(self.request)
        context.update(report_data)
        
        # Dynamically set the report title based on the date range
        if context.get('start_date') and context.get('end_date'):
            start, end = context['start_date'], context['end_date']
            context['report_title'] = _(f"Fee Collection from {start.strftime('%d-%b-%Y')} to {end.strftime('%d-%b-%Y')}")
        
        context['school_profile'] = SchoolProfile.objects.first()
        return context


class CollectionReportExportCSVView(CollectionReportView):
    """Handles CSV export for the Collection Report."""
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        report_data = self._get_collection_data(request)
        queryset = report_data.get('full_queryset')

        response = HttpResponse(content_type='text/csv')
        filename = f"Collection_Report_{timezone.now().strftime('%Y%m%d')}.csv"
        response['Content-Disposition'] = f'attachment; filename="{filename}"'
        writer = csv.writer(response)
        
        headers = ['Date', 'Receipt No', 'Student Name', 'Class', 'Amount', 'Method', 'Recorded By']
        writer.writerow(headers)

        for payment in queryset:
            writer.writerow([
                payment.payment_date.strftime('%Y-%m-%d %H:%M'),
                payment.receipt_number,
                payment.student.get_full_name() if payment.student else 'N/A',
                payment.student.current_class.name if payment.student and payment.student.current_class else 'N/A',
                payment.amount,
                payment.payment_method.name if payment.payment_method else 'N/A',
                payment.recorded_by.get_full_name() if payment.recorded_by else 'System'
            ])
        return response


class CollectionReportExportExcelView(CollectionReportView):
    """Handles Excel export for the Collection Report."""
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        if not openpyxl:
            messages.error(request, "Excel export library not available.")
            return redirect('reporting:collection_report')

        report_data = self._get_collection_data(request)
        queryset = report_data.get('full_queryset')
        
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "Fee Collection"
        
        headers = ['Date', 'Receipt No', 'Student Name', 'Class', 'Amount', 'Method', 'Recorded By']
        ws.append(headers)
        header_font = Font(bold=True)
        for cell in ws[1]:
            cell.font = header_font
        
        currency_format = '#,##0.00'
        date_format = 'YYYY-MM-DD HH:MM'

        for payment in queryset:
            ws.append([
                payment.payment_date,
                payment.receipt_number,
                payment.student.get_full_name() if payment.student else 'N/A',
                payment.student.current_class.name if payment.student and payment.student.current_class else 'N/A',
                payment.amount,
                payment.payment_method.name if payment.payment_method else 'N/A',
                payment.recorded_by.get_full_name() if payment.recorded_by else 'System'
            ])
            ws.cell(row=ws.max_row, column=1).number_format = date_format
            ws.cell(row=ws.max_row, column=5).number_format = currency_format

        # Auto-fit columns
        for col in ws.columns:
            max_length = 0
            column_letter = get_column_letter(col[0].column)
            for cell in col:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = (max_length + 2)
            ws.column_dimensions[column_letter].width = adjusted_width

        response = HttpResponse(content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        filename = f"Collection_Report_{timezone.now().strftime('%Y%m%d')}.xlsx"
        response['Content-Disposition'] = f'attachment; filename="{filename}"'
        wb.save(response)
        return response


class CollectionReportExportPDFView(CollectionReportView):
    """Handles PDF export for the Collection Report."""
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        report_data = self._get_collection_data(request)
        
        start = report_data.get('start_date')
        end = report_data.get('end_date')

        pdf_context = report_data.copy()
        pdf_context['report_title'] = f"Fee Collection Report"
        if start and end:
            pdf_context['report_subtitle'] = f"From {start.strftime('%d-%b-%Y')} to {end.strftime('%d-%b-%Y')}"

        pdf_context.update({
            'school_profile': SchoolProfile.objects.first(),
            'tenant': getattr(request, 'tenant', None),
            'current_datetime': timezone.now()
        })
        
        try:
            pdf = render_to_pdf('reporting/pdf/collection_report_pdf.html', pdf_context)
            if pdf:
                response = HttpResponse(pdf, content_type='application/pdf')
                filename = f"Collection_Report_{timezone.now().strftime('%Y%m%d')}.pdf"
                response['Content-Disposition'] = f'inline; filename="{filename}"'
                return response
        except Exception as e:
            logger.error(f"Error generating Collection Report PDF: {e}", exc_info=True)
            messages.error(request, "An error occurred while generating the PDF report.")
            
        return redirect('reporting:collection_report')



# = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = 
#
#     TRIAL BALANCE REPORT
#
# = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =


class TrialBalanceReportView(TenantPermissionRequiredMixin, BaseReportViewMixin, TemplateView):
    """
    Handles the display of the HTML Trial Balance report page.
    """
    template_name = 'reporting/trial_balance_report.html'
    
    # Attributes for BaseReportViewMixin
    permission_required = 'reporting.view_trial_balance_report'
    report_title = _("Trial Balance")
    filter_form_class = ReportPeriodForm # A form with a single 'report_date' field
    export_csv_url_name = 'reporting:trial_balance_export_csv'
    export_pdf_url_name = 'reporting:trial_balance_export_pdf'
    export_excel_url_name = 'reporting:trial_balance_export_excel'

    def _get_trial_balance_data(self, request):
        """
        The core logic for fetching and processing all data for the Trial Balance report.
        """
        # 1. Initialize the data structure
        data = {
            'accounts_data': [], 'total_debits': Decimal('0.00'),
            'total_credits': Decimal('0.00'), 'balance_difference': Decimal('0.00'),
            'as_at_date': None, 'filter_form': self.filter_form_class(request.GET or None)
        }
        
        # 2. Validate form and get the 'as at' date
        # Use a default date if the form is unbound (initial load)
        if not data['filter_form'].is_bound:
            data['filter_form'] = self.filter_form_class(initial={'report_date': timezone.now().date()})
            data['filter_form'].is_valid() # Run validation on initial data

        if not data['filter_form'].is_valid():
            return data # Return with form errors if bound and invalid
        
        as_at_date = data['filter_form'].cleaned_data.get('report_date', timezone.now().date())
        data['as_at_date'] = as_at_date

        # 3. Fetch all active accounts and calculate their balances
        accounts = Account.objects.filter(is_active=True).select_related('account_type').order_by('code')

        for account in accounts:
            balance = account.get_balance(as_of_date=as_at_date)

            if balance == Decimal('0.00'):
                continue # Skip accounts with no balance

            debit_balance = Decimal('0.00')
            credit_balance = Decimal('0.00')

            # Determine if balance is debit or credit based on the account type's normal balance
            if account.account_type.normal_balance == AccountType.NormalBalanceChoices.DEBIT:
                debit_balance = balance
            else: # Normal balance is CREDIT
                # A positive balance for a credit-normal account is a credit.
                # A negative balance means it has flipped to a debit balance.
                credit_balance = -balance

            data['accounts_data'].append({
                'code': account.code,
                'name': account.name,
                'debit': debit_balance if debit_balance > 0 else Decimal('0.00'),
                'credit': credit_balance if credit_balance > 0 else Decimal('0.00'),
            })
            
            data['total_debits'] += debit_balance if debit_balance > 0 else Decimal('0.00')
            data['total_credits'] += credit_balance if credit_balance > 0 else Decimal('0.00')
            
        data['balance_difference'] = data['total_debits'] - data['total_credits']

        return data

    def get_context_data(self, **kwargs):
        """Prepares context for the HTML page by calling the main data helper."""
        context = super().get_context_data(**kwargs)
        report_data = self._get_trial_balance_data(self.request)
        context.update(report_data)
        
        if context.get('as_at_date'):
            context['report_title'] = _(f"Trial Balance as at {context['as_at_date'].strftime('%d %B %Y')}")
            
        context['school_profile'] = SchoolProfile.objects.first()
        return context


class TrialBalanceExportCSVView(TrialBalanceReportView):
    """Handles CSV export for the Trial Balance report."""
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        report_data = self._get_trial_balance_data(request)
        as_at_date = report_data.get('as_at_date')
        if not as_at_date:
            messages.error(request, "Please select a date to generate the CSV export.")
            return redirect('reporting:trial_balance_report')

        response = HttpResponse(content_type='text/csv')
        filename = f"Trial_Balance_{as_at_date.strftime('%Y%m%d')}.csv"
        response['Content-Disposition'] = f'attachment; filename="{filename}"'
        writer = csv.writer(response)
        
        writer.writerow([f"Trial Balance as at {as_at_date.strftime('%d %B %Y')}"])
        writer.writerow([])
        writer.writerow(['Account Code', 'Account Name', 'Debit', 'Credit'])

        for acc in report_data.get('accounts_data', []):
            writer.writerow([acc['code'], acc['name'], acc['debit'], acc['credit']])
        
        writer.writerow([])
        writer.writerow(['', 'TOTALS', report_data['total_debits'], report_data['total_credits']])
        
        return response


class TrialBalanceExportExcelView(TrialBalanceReportView):
    """Handles Excel export for the Trial Balance report."""
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        if not openpyxl:
            messages.error(request, "Excel export library not available.")
            return redirect('reporting:trial_balance_report')

        report_data = self._get_trial_balance_data(request)
        as_at_date = report_data.get('as_at_date')
        if not as_at_date:
            messages.error(request, "Please select a date to generate the Excel export.")
            return redirect('reporting:trial_balance_report')

        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "Trial Balance"
        
        # Styles
        title_font = Font(bold=True, size=14)
        header_font = Font(bold=True)
        currency_format = '#,##0.00'

        # Header
        ws.merge_cells('A1:D1')
        ws['A1'] = f"Trial Balance as at {as_at_date.strftime('%d %B %Y')}"
        ws['A1'].font = title_font
        ws['A1'].alignment = Alignment(horizontal='center')
        
        # Table Headers
        headers = ['Account Code', 'Account Name', 'Debit', 'Credit']
        ws.append(headers)
        for cell in ws[2]: cell.font = header_font

        # Data
        for acc in report_data.get('accounts_data', []):
            ws.append([acc['code'], acc['name'], acc['debit'], acc['credit']])
        
        # Totals
        ws.append(['', 'TOTALS', report_data['total_debits'], report_data['total_credits']])
        total_row = ws.max_row
        for cell in ws[total_row]: cell.font = header_font
        
        # Formatting
        ws.column_dimensions['A'].width = 15
        ws.column_dimensions['B'].width = 40
        ws.column_dimensions['C'].width = 18
        ws.column_dimensions['D'].width = 18
        for row in ws.iter_rows(min_row=3, max_col=4, min_col=3):
            for cell in row:
                cell.number_format = currency_format

        response = HttpResponse(content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        filename = f"Trial_Balance_{as_at_date.strftime('%Y%m%d')}.xlsx"
        response['Content-Disposition'] = f'attachment; filename="{filename}"'
        wb.save(response)
        return response


class TrialBalanceExportPDFView(TrialBalanceReportView):
    """Handles PDF export for the Trial Balance report."""
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        report_data = self._get_trial_balance_data(request)
        as_at_date = report_data.get('as_at_date')
        if not as_at_date:
            messages.error(request, "Please select a date to generate the PDF.")
            return redirect('reporting:trial_balance_report')
        
        pdf_context = report_data.copy()
        pdf_context.update({
            'report_title': f"Trial Balance as at {as_at_date.strftime('%d %B %Y')}",
            'school_profile': SchoolProfile.objects.first(),
            'tenant': getattr(request, 'tenant', None),
            'current_datetime': timezone.now()
        })
        
        try:
            pdf = render_to_pdf('reporting/pdf/trial_balance_pdf.html', pdf_context)
            if pdf:
                response = HttpResponse(pdf, content_type='application/pdf')
                filename = f"Trial_Balance_{as_at_date.strftime('%Y%m%d')}.pdf"
                response['Content-Disposition'] = f'inline; filename="{filename}"'
                return response
        except Exception as e:
            logger.error(f"Error generating Trial Balance PDF: {e}", exc_info=True)
            messages.error(request, "An error occurred while generating the PDF report.")
            
        return redirect('reporting:trial_balance_report')
    


# = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = 
#
#      INCOME / EXPENSE REPORTS (PROFIT AND LOSS)
#
# = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =



class IncomeExpenseReportView(TenantPermissionRequiredMixin, BaseReportViewMixin, TemplateView):
    """
    Handles the display of the HTML Income & Expense (Profit & Loss) report page.
    """
    template_name = 'reporting/income_expense_report.html'
    
    # Attributes for BaseReportViewMixin
    permission_required = 'reporting.view_income_expense_report'
    report_title = _("Income Statement (Profit & Loss)")
    filter_form_class = IncomeExpenseReportForm
    export_csv_url_name = 'reporting:income_expense_export_csv'
    export_pdf_url_name = 'reporting:income_expense_export_pdf'
    export_excel_url_name = 'reporting:income_expense_export_excel'

    def _get_balances_by_classification(self, classification, start_date, end_date):
        """Helper to get accounts and total balance for a given classification."""
        accounts = Account.objects.filter(
            account_type__classification=classification,
            is_active=True
        ).select_related('account_type').order_by('code')

        items_list = []
        total_balance = Decimal('0.00')

        for account in accounts:
            balance = account.get_balance(start_date=start_date, as_of_date=end_date)
            if balance != Decimal('0.00'):
                # For display, revenues are positive, expenses are positive.
                # get_balance returns credits as negative, debits as positive.
                # So we negate revenue balances.
                display_balance = -balance if classification == AccountType.ClassificationChoices.REVENUE else balance
                items_list.append({'name': account.name, 'amount': display_balance})
                total_balance += display_balance
                
        return items_list, total_balance

    def _get_income_expense_data(self, request):
        """
        The core logic for fetching and processing all data for the Income & Expense report.
        """
        # 1. Initialize data structure
        data = {
            'start_date': None, 'end_date': None,
            'revenues': [], 'total_revenue': Decimal('0.00'),
            'cost_of_sales': [], 'total_cogs': Decimal('0.00'),
            'operating_expenses': [], 'total_expenses': Decimal('0.00'),
            'gross_profit': Decimal('0.00'), 'net_profit': Decimal('0.00'),
            'filter_form': self.filter_form_class(request.GET or None)
        }

        # 2. Handle form and dates
        if not data['filter_form'].is_bound:
            today = timezone.localdate()
            first_day_current_month = today.replace(day=1)
            last_day_previous_month = first_day_current_month - timezone.timedelta(days=1)
            first_day_previous_month = last_day_previous_month.replace(day=1)
            data['filter_form'] = self.filter_form_class(initial={
                'start_date': first_day_previous_month, 'end_date': last_day_previous_month
            })
            data['filter_form'].is_valid()

        if not data['filter_form'].is_valid():
            return data

        start_date = data['filter_form'].cleaned_data.get('start_date')
        end_date = data['filter_form'].cleaned_data.get('end_date')
        data.update({'start_date': start_date, 'end_date': end_date})

        # 3. Fetch data for each P&L section
        data['revenues'], data['total_revenue'] = self._get_balances_by_classification(
            AccountType.ClassificationChoices.REVENUE, start_date, end_date
        )
        data['cost_of_sales'], data['total_cogs'] = self._get_balances_by_classification(
            AccountType.ClassificationChoices.COGS, start_date, end_date
        )
        data['operating_expenses'], data['total_expenses'] = self._get_balances_by_classification(
            AccountType.ClassificationChoices.EXPENSE, start_date, end_date
        )

        # 4. Calculate derived totals
        data['gross_profit'] = data['total_revenue'] - data['total_cogs']
        data['net_profit'] = data['gross_profit'] - data['total_expenses']

        return data

    def get_context_data(self, **kwargs):
        """Prepares context for the HTML page."""
        context = super().get_context_data(**kwargs)
        report_data = self._get_income_expense_data(self.request)
        context.update(report_data)
        
        if context.get('start_date') and context.get('end_date'):
            start, end = context['start_date'], context['end_date']
            context['report_title'] = _(f"Income Statement from {start.strftime('%d-%b-%Y')} to {end.strftime('%d-%b-%Y')}")
            
        context['school_profile'] = SchoolProfile.objects.first()
        return context


class IncomeExpenseReportExportCSVView(IncomeExpenseReportView):
    """Handles CSV export for the Income & Expense Report."""
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        report_data = self._get_income_expense_data(request)
        start, end = report_data.get('start_date'), report_data.get('end_date')
        if not all([start, end]):
            messages.error(request, "Please select a date range to generate the CSV export.")
            return redirect('reporting:income_expense_report')

        response = HttpResponse(content_type='text/csv')
        filename = f"Income_Statement_{start.strftime('%Y%m%d')}_{end.strftime('%Y%m%d')}.csv"
        response['Content-Disposition'] = f'attachment; filename="{filename}"'
        writer = csv.writer(response)

        def write_section(title, items, total, total_label):
            writer.writerow([title, ''])
            for item in items:
                writer.writerow([f"  {item['name']}", item['amount']])
            writer.writerow([total_label, total])
            writer.writerow([])
        
        write_section('REVENUE', report_data['revenues'], report_data['total_revenue'], 'Total Revenue')
        write_section('COST OF SALES', report_data['cost_of_sales'], report_data['total_cogs'], 'Total Cost of Sales')
        writer.writerow(['GROSS PROFIT', report_data['gross_profit']])
        writer.writerow([])
        write_section('OPERATING EXPENSES', report_data['operating_expenses'], report_data['total_expenses'], 'Total Operating Expenses')
        writer.writerow(['NET PROFIT / (LOSS)', report_data['net_profit']])
        
        return response


class IncomeExpenseReportExportExcelView(IncomeExpenseReportView):
    """Handles Excel export for the Income & Expense Report."""
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        if not openpyxl:
            messages.error(request, "Excel export library not available.")
            return redirect('reporting:income_expense_report')

        report_data = self._get_income_expense_data(request)
        start, end = report_data.get('start_date'), report_data.get('end_date')
        if not all([start, end]):
            messages.error(request, "Please select a date range to generate the Excel export.")
            return redirect('reporting:income_expense_report')

        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "Income Statement"
        
        # Styles
        title_font, header_font, bold_font = Font(bold=True, size=14), Font(bold=True, size=12), Font(bold=True)
        currency_format = '#,##0.00'

        # Helper
        row = 1
        def write_excel_section(title, items, total, total_label):
            nonlocal row
            ws.cell(row=row, column=1, value=title).font = header_font
            row += 1
            for item in items:
                ws.cell(row=row, column=1, value=item['name']).alignment = Alignment(indent=1)
                ws.cell(row=row, column=2, value=item['amount'])
                row += 1
            ws.cell(row=row, column=1, value=total_label).font = bold_font
            ws.cell(row=row, column=2, value=total).font = bold_font
            row += 2

        # Write Data
        write_excel_section("REVENUE", report_data['revenues'], report_data['total_revenue'], "Total Revenue")
        write_excel_section("COST OF SALES", report_data['cost_of_sales'], report_data['total_cogs'], "Total Cost of Sales")
        ws.cell(row=row, column=1, value="GROSS PROFIT").font = header_font
        ws.cell(row=row, column=2, value=report_data['gross_profit']).font = header_font
        row += 2
        write_excel_section("OPERATING EXPENSES", report_data['operating_expenses'], report_data['total_expenses'], "Total Operating Expenses")
        ws.cell(row=row, column=1, value="NET PROFIT / (LOSS)").font = header_font
        ws.cell(row=row, column=2, value=report_data['net_profit']).font = header_font

        # Formatting
        ws.column_dimensions['A'].width = 45
        ws.column_dimensions['B'].width = 20
        for cell in ws['B']: cell.number_format = currency_format

        response = HttpResponse(content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        filename = f"Income_Statement_{start.strftime('%Y%m%d')}_{end.strftime('%Y%m%d')}.xlsx"
        response['Content-Disposition'] = f'attachment; filename="{filename}"'
        wb.save(response)
        return response


class IncomeExpenseReportExportPDFView(IncomeExpenseReportView):
    """Handles PDF export for the Income & Expense Report."""
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        report_data = self._get_income_expense_data(request)
        start, end = report_data.get('start_date'), report_data.get('end_date')
        if not all([start, end]):
            messages.error(request, "Please select a date range to generate the PDF.")
            return redirect('reporting:income_expense_report')
        
        pdf_context = report_data.copy()
        pdf_context.update({
            'report_title': _(f"Income Statement from {start.strftime('%d-%b-%Y')} to {end.strftime('%d-%b-%Y')}"),
            'school_profile': SchoolProfile.objects.first(),
            'tenant': getattr(request, 'tenant', None),
            'current_datetime': timezone.now()
        })
        
        try:
            pdf = render_to_pdf('reporting/pdf/income_expense_report_pdf.html', pdf_context)
            if pdf:
                response = HttpResponse(pdf, content_type='application/pdf')
                filename = f"Income_Statement_{start.strftime('%Y%m%d')}.pdf"
                response['Content-Disposition'] = f'inline; filename="{filename}"'
                return response
        except Exception as e:
            logger.error(f"Error generating Income Statement PDF: {e}", exc_info=True)
            messages.error(request, "An error occurred while generating the PDF report.")
            
        return redirect('reporting:income_expense_report')



# = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = 
#
#      EXPENSE REPORT VIEW
#
# = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
# # D:\school_fees_saas_v2\apps\reporting\views.py


class ExpenseReportView(TenantPermissionRequiredMixin, BaseReportViewMixin, TemplateView):
    """
    Handles the display of the HTML Expense Report page.
    """
    template_name = 'reporting/expense_report.html'
    
    # Attributes for BaseReportViewMixin
    permission_required = 'reporting.view_expense_report'
    report_title = _("Expense Report")
    filter_form_class = ExpenseReportFilterForm
    export_csv_url_name = 'reporting:expense_report_export_csv'
    export_pdf_url_name = 'reporting:expense_report_export_pdf'
    export_excel_url_name = 'reporting:expense_report_export_excel'
    paginate_by = 30

    def _get_expense_data(self, request):
        """
        The core logic for fetching and processing all data for the Expense Report.
        """
        # 1. Initialize the data structure
        data = {
            'expenses_page': None, 'full_queryset': Expense.objects.none(),
            'total_expenses': Decimal('0.00'), 'expense_count': 0,
            'expenses_by_category': [], 'start_date': None, 'end_date': None,
            'filter_form': self.filter_form_class(request.GET or None)
        }
        
        # 2. Set default date range if form is not submitted
        if not request.GET:
            today = timezone.now().date()
            start_date = today.replace(day=1) # Default to current month start
            data['filter_form'] = self.filter_form_class(initial={'start_date': start_date, 'end_date': today})

        # 3. Validate form and apply filters
        base_queryset = Expense.objects.select_related(
            'category', 'payment_method', 'recorded_by'
        ).order_by('-expense_date')

        filtered_queryset = base_queryset
        if data['filter_form'].is_valid():
            cd = data['filter_form'].cleaned_data
            data['start_date'] = cd.get('start_date')
            data['end_date'] = cd.get('end_date')

            if data['start_date']:
                filtered_queryset = filtered_queryset.filter(expense_date__gte=data['start_date'])
            if data['end_date']:
                filtered_queryset = filtered_queryset.filter(expense_date__lte=data['end_date'])
            if cd.get('category'):
                filtered_queryset = filtered_queryset.filter(category=cd['category'])
            if cd.get('payment_method'):
                filtered_queryset = filtered_queryset.filter(payment_method=cd['payment_method'])
            if cd.get('description_contains'):
                filtered_queryset = filtered_queryset.filter(description__icontains=cd['description_contains'])
        
        data['full_queryset'] = filtered_queryset

        # 4. Calculate summary totals from the full (un-paginated) queryset
        if filtered_queryset.exists():
            summary = filtered_queryset.aggregate(
                total=Coalesce(Sum('amount'), Decimal('0.00')),
                count=Count('id')
            )
            data['total_expenses'] = summary['total']
            data['expense_count'] = summary['count']
            
            data['expenses_by_category'] = list(
                filtered_queryset.values('category__name')
                .annotate(total=Sum('amount'), count_items=Count('id'))
                .order_by('-total')
                .filter(category__name__isnull=False)
            )

        # 5. Paginate the results for the HTML view
        paginator = Paginator(filtered_queryset, self.paginate_by)
        page_number = request.GET.get('page', 1)
        data['expenses_page'] = paginator.get_page(page_number)
        
        return data

    def get_context_data(self, **kwargs):
        """Prepares context for the HTML page."""
        context = super().get_context_data(**kwargs)
        report_data = self._get_expense_data(self.request)
        context.update(report_data)
        
        if context.get('start_date') and context.get('end_date'):
            start, end = context['start_date'], context['end_date']
            context['report_title'] = _(f"Expense Report from {start.strftime('%d-%b-%Y')} to {end.strftime('%d-%b-%Y')}")
            
        context['school_profile'] = SchoolProfile.objects.first()
        return context


class ExpenseReportExportCSVView(ExpenseReportView):
    """Handles CSV export for the Expense Report."""
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        report_data = self._get_expense_data(request)
        queryset = report_data.get('full_queryset')

        response = HttpResponse(content_type='text/csv')
        filename = f"Expense_Report_{timezone.now().strftime('%Y%m%d')}.csv"
        response['Content-Disposition'] = f'attachment; filename="{filename}"'
        writer = csv.writer(response)
        
        headers = ['Date', 'Description', 'Category', 'Amount', 'Method', 'Recorded By']
        writer.writerow(headers)

        for expense in queryset:
            writer.writerow([
                expense.expense_date.strftime('%Y-%m-%d'),
                expense.description,
                expense.category.name if expense.category else 'N/A',
                expense.amount,
                expense.payment_method.name if expense.payment_method else 'N/A',
                expense.recorded_by.get_full_name() if expense.recorded_by else 'N/A'
            ])
        return response


class ExpenseReportExportExcelView(ExpenseReportView):
    """Handles Excel export for the Expense Report."""
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        if not openpyxl:
            messages.error(request, "Excel export library not available.")
            return redirect('reporting:expense_report')

        report_data = self._get_expense_data(request)
        queryset = report_data.get('full_queryset')
        
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "Expense Report"
        
        headers = ['Date', 'Description', 'Category', 'Amount', 'Method', 'Recorded By']
        ws.append(headers)
        header_font = Font(bold=True)
        for cell in ws[1]:
            cell.font = header_font
        
        currency_format = '#,##0.00'
        date_format = 'YYYY-MM-DD'

        for expense in queryset:
            ws.append([
                expense.expense_date,
                expense.description,
                expense.category.name if expense.category else 'N/A',
                expense.amount,
                expense.payment_method.name if expense.payment_method else 'N/A',
                expense.recorded_by.get_full_name() if expense.recorded_by else 'N/A'
            ])
            ws.cell(row=ws.max_row, column=1).number_format = date_format
            ws.cell(row=ws.max_row, column=4).number_format = currency_format

        # Auto-fit columns
        ws.column_dimensions['A'].width = 15
        ws.column_dimensions['B'].width = 40
        ws.column_dimensions['C'].width = 25
        ws.column_dimensions['D'].width = 18
        ws.column_dimensions['E'].width = 20
        ws.column_dimensions['F'].width = 25

        response = HttpResponse(content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        filename = f"Expense_Report_{timezone.now().strftime('%Y%m%d')}.xlsx"
        response['Content-Disposition'] = f'attachment; filename="{filename}"'
        wb.save(response)
        return response


class ExpenseReportExportPDFView(ExpenseReportView):
    """Handles PDF export for the Expense Report."""
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        report_data = self._get_expense_data(request)
        start, end = report_data.get('start_date'), report_data.get('end_date')
        if not all([start, end]):
            messages.error(request, "Please select a date range to generate the PDF.")
            return redirect('reporting:expense_report')
        
        pdf_context = report_data.copy()
        pdf_context['report_title'] = _(f"Expense Report from {start.strftime('%d-%b-%Y')} to {end.strftime('%d-%b-%Y')}")
        pdf_context.update({
            'school_profile': SchoolProfile.objects.first(),
            'tenant': getattr(request, 'tenant', None),
            'current_datetime': timezone.now()
        })
        
        try:
            pdf = render_to_pdf('reporting/pdf/expense_report_pdf.html', pdf_context)
            if pdf:
                response = HttpResponse(pdf, content_type='application/pdf')
                filename = f"Expense_Report_{start.strftime('%Y%m%d')}.pdf"
                response['Content-Disposition'] = f'inline; filename="{filename}"'
                return response
        except Exception as e:
            logger.error(f"Error generating Expense Report PDF: {e}", exc_info=True)
            messages.error(request, "An error occurred while generating the PDF report.")
            
        return redirect('reporting:expense_report')
    


# = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = 
#
#      BALANCE SHEET REPORT
#
# = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =


class BalanceSheetReportView(TenantPermissionRequiredMixin, BaseReportViewMixin, TemplateView):
    """
    Handles the display of the HTML Balance Sheet report page.
    """
    template_name = 'reporting/balance_sheet_report.html'
    
    # Attributes for BaseReportViewMixin
    permission_required = 'reporting.view_balance_sheet_report'
    report_title = _("Balance Sheet")
    filter_form_class = BalanceSheetFilterForm
    export_csv_url_name = 'reporting:balance_sheet_export_csv'
    export_pdf_url_name = 'reporting:balance_sheet_export_pdf'
    export_excel_url_name = 'reporting:balance_sheet_export_excel'

    def _get_balance_sheet_data(self, request):
        """
        The core logic for fetching and processing all data for the Balance Sheet.
        This helper method is used by the HTML view and all export views.
        Returns a dictionary containing all calculated data.
        """
        # 1. Initialize the data structure to be returned
        data = {
            'as_at_date': None, 'assets': [], 'total_assets': Decimal('0.00'),
            'liabilities': [], 'total_liabilities': Decimal('0.00'),
            'equity': [], 'total_equity': Decimal('0.00'),
            'total_liabilities_and_equity': Decimal('0.00'),
            'net_income': Decimal('0.00'),
            'balance_check_difference': Decimal('0.00'),
            'filter_form': self.filter_form_class(request.GET or None)
        }

        # 2. Validate filter form
        if not data['filter_form'].is_valid():
            # Fill with default date if form is unbound on initial load
            if not data['filter_form'].is_bound:
                data['filter_form'] = self.filter_form_class(initial={'as_at_date': timezone.now().date()})
                if not data['filter_form'].is_valid():
                    # If validation still fails, use default date
                    as_at_date = timezone.now().date()
                else:
                    as_at_date = data['filter_form'].cleaned_data.get('as_at_date', timezone.now().date())
            else:
                # Form is bound but invalid, use default date
                as_at_date = timezone.now().date()
        else:
            # Form is valid, get the date from cleaned_data
            as_at_date = data['filter_form'].cleaned_data.get('as_at_date', timezone.now().date())
        data['as_at_date'] = as_at_date

        # 3. Calculate Net Income for the current financial year up to the 'as_at_date'
        # This assumes a utility function or model method to find the financial year start
        try:
            fy_start_date = get_financial_year_start(as_at_date)
        except Exception as e:
            # Fallback if the utility fails or is not found
            logger.warning(f"Could not determine financial year start date: {e}. Defaulting to Jan 1st.")
            fy_start_date = as_at_date.replace(month=1, day=1)

        revenue_accounts = Account.objects.filter(account_type__classification=AccountType.ClassificationChoices.REVENUE)
        expense_accounts = Account.objects.filter(account_type__classification__in=[
            AccountType.ClassificationChoices.EXPENSE, AccountType.ClassificationChoices.COGS
        ])

        total_revenue = sum(acc.get_balance(start_date=fy_start_date, as_of_date=as_at_date) for acc in revenue_accounts)
        total_expense = sum(acc.get_balance(start_date=fy_start_date, as_of_date=as_at_date) for acc in expense_accounts)
        
        # Revenue has a credit balance (negative), so we expect total_revenue to be <= 0.
        # We negate it to make it a positive number for the calculation.
        # Expenses have a debit balance (positive).
        net_income = (-total_revenue) - total_expense
        data['net_income'] = net_income

        # 4. Process Assets, Liabilities, and Equity
        account_types = AccountType.objects.prefetch_related('accounts').order_by('classification', 'name')

        for acc_type in account_types:
            category_data = {'type_name': acc_type.name, 'accounts': [], 'subtotal': Decimal('0.00')}
            
            # Skip Revenue and Expense types as they are handled in Net Income
            if acc_type.classification in [AccountType.ClassificationChoices.REVENUE, AccountType.ClassificationChoices.EXPENSE, AccountType.ClassificationChoices.COGS]:
                continue

            for account in acc_type.accounts.filter(is_active=True):
                # For Balance Sheet accounts, balance is calculated from the beginning of time.
                balance = account.get_balance(as_of_date=as_at_date)

                # Negate Liability and Equity balances for user-friendly display (as positive numbers)
                if acc_type.classification in [AccountType.ClassificationChoices.LIABILITY, AccountType.ClassificationChoices.EQUITY]:
                    balance = -balance
                
                if balance != Decimal('0.00'):
                    category_data['accounts'].append({'name': account.name, 'balance': balance})
                    category_data['subtotal'] += balance

            if category_data['accounts']:
                if acc_type.classification == AccountType.ClassificationChoices.ASSET:
                    data['assets'].append(category_data)
                    data['total_assets'] += category_data['subtotal']
                elif acc_type.classification == AccountType.ClassificationChoices.LIABILITY:
                    data['liabilities'].append(category_data)
                    data['total_liabilities'] += category_data['subtotal']
                elif acc_type.classification == AccountType.ClassificationChoices.EQUITY:
                    data['equity'].append(category_data)
                    data['total_equity'] += category_data['subtotal']
        
        # 5. Add Net Income to Equity
        data['total_equity'] += net_income
        data['total_liabilities_and_equity'] = data['total_liabilities'] + data['total_equity']
        data['balance_check_difference'] = data['total_assets'] - data['total_liabilities_and_equity']

        # 6. Check if balance sheet balances (difference should be zero or very close)
        data['balances_check'] = abs(data['balance_check_difference']) < Decimal('0.01')

        return data

    def get_context_data(self, **kwargs):
        """Prepares context for the HTML page by calling the main data helper."""
        context = super().get_context_data(**kwargs)
        balance_sheet_data = self._get_balance_sheet_data(self.request)

        # Add the data under 'report_data' key for template compatibility
        context['report_data'] = balance_sheet_data
        context.update(balance_sheet_data)  # Also add directly for backward compatibility

        if balance_sheet_data.get('as_at_date'):
            context['report_title'] = _(f"Balance Sheet as at {balance_sheet_data['as_at_date'].strftime('%d %B %Y')}")
            context['report_data']['report_date'] = balance_sheet_data['as_at_date']

        return context


class BalanceSheetExportCSVView(BalanceSheetReportView):
    """Handles CSV export for the Balance Sheet."""
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        report_data = self._get_balance_sheet_data(request)
        as_at_date = report_data.get('as_at_date')
        if not as_at_date:
            messages.error(request, "Please select a date to generate the CSV export.")
            return redirect('reporting:balance_sheet_report')

        response = HttpResponse(content_type='text/csv')
        filename = f"Balance_Sheet_{as_at_date.strftime('%Y%m%d')}.csv"
        response['Content-Disposition'] = f'attachment; filename="{filename}"'
        writer = csv.writer(response)

        writer.writerow(['Balance Sheet'])
        writer.writerow([f"As at {as_at_date.strftime('%d %B %Y')}"])
        writer.writerow([])
        
        # Assets
        writer.writerow(['ASSETS', ''])
        for cat in report_data.get('assets', []):
            writer.writerow([cat['type_name'], ''])
            for acc in cat['accounts']:
                writer.writerow([f"  {acc['name']}", acc['balance']])
            writer.writerow([f"Total {cat['type_name']}", cat['subtotal']])
        writer.writerow(['TOTAL ASSETS', report_data['total_assets']])
        writer.writerow([])

        # Liabilities
        writer.writerow(['LIABILITIES', ''])
        for cat in report_data.get('liabilities', []):
            writer.writerow([cat['type_name'], ''])
            for acc in cat['accounts']:
                writer.writerow([f"  {acc['name']}", acc['balance']])
            writer.writerow([f"Total {cat['type_name']}", cat['subtotal']])
        writer.writerow(['TOTAL LIABILITIES', report_data['total_liabilities']])
        writer.writerow([])

        # Equity
        writer.writerow(['EQUITY', ''])
        for cat in report_data.get('equity', []):
            writer.writerow([cat['type_name'], ''])
            for acc in cat['accounts']:
                writer.writerow([f"  {acc['name']}", acc['balance']])
            writer.writerow([f"Total {cat['type_name']}", cat['subtotal']])
        writer.writerow(['Net Income', report_data['net_income']])
        writer.writerow(['TOTAL EQUITY', report_data['total_equity']])
        writer.writerow([])

        writer.writerow(['TOTAL LIABILITIES AND EQUITY', report_data['total_liabilities_and_equity']])
        
        return response


class BalanceSheetExportExcelView(BalanceSheetReportView):
    """Handles Excel export for the Balance Sheet."""
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        if not openpyxl:
            messages.error(request, "Excel export library is not available.")
            return redirect('reporting:balance_sheet_report')

        report_data = self._get_balance_sheet_data(request)
        as_at_date = report_data.get('as_at_date')
        if not as_at_date:
            messages.error(request, "Please select a date to generate the Excel export.")
            return redirect('reporting:balance_sheet_report')

        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "Balance Sheet"

        # --- Styles ---
        title_font = Font(bold=True, size=16)
        header_font = Font(bold=True, size=12, underline='single')
        bold_font = Font(bold=True)
        currency_format = '_(* #,##0.00_);_(* (#,##0.00);_(* "-"??_);_(@_)'
        
        # --- Helper for writing sections ---
        def write_excel_section(start_row, title, categories, total_label, total_value):
            ws[f'A{start_row}'] = title
            ws[f'A{start_row}'].font = title_font
            current_row = start_row + 1
            for cat in categories:
                ws[f'A{current_row}'] = cat['type_name']
                ws[f'A{current_row}'].font = header_font
                current_row += 1
                for acc in cat['accounts']:
                    ws[f'A{current_row}'] = acc['name']
                    ws[f'B{current_row}'] = acc['balance']
                    current_row += 1
            
            ws[f'A{current_row}'] = total_label
            ws[f'A{current_row}'].font = bold_font
            ws[f'B{current_row}'] = total_value
            ws[f'B{current_row}'].font = bold_font
            return current_row + 2

        # --- Write Data ---
        current_row = 1
        # Assets
        current_row = write_excel_section(current_row, "ASSETS", report_data.get('assets', []), "TOTAL ASSETS", report_data.get('total_assets'))

        # Liabilities
        current_row = write_excel_section(current_row, "LIABILITIES", report_data.get('liabilities', []), "TOTAL LIABILITIES", report_data.get('total_liabilities'))
        
        # Equity
        ws[f'A{current_row}'] = "EQUITY"
        ws[f'A{current_row}'].font = title_font
        current_row +=1
        for cat in report_data.get('equity', []):
            ws[f'A{current_row}'] = cat['type_name']
            ws[f'A{current_row}'].font = header_font
            current_row +=1
            for acc in cat['accounts']:
                ws[f'A{current_row}'] = acc['name']
                ws[f'B{current_row}'] = acc['balance']
                current_row += 1
        ws[f'A{current_row}'] = "Net Income"
        ws[f'B{current_row}'] = report_data.get('net_income')
        current_row += 1
        ws[f'A{current_row}'] = "TOTAL EQUITY"
        ws[f'A{current_row}'].font = bold_font
        ws[f'B{current_row}'] = report_data.get('total_equity')
        ws[f'B{current_row}'].font = bold_font
        current_row += 2

        ws[f'A{current_row}'] = "TOTAL LIABILITIES AND EQUITY"
        ws[f'A{current_row}'].font = bold_font
        ws[f'B{current_row}'] = report_data.get('total_liabilities_and_equity')
        ws[f'B{current_row}'].font = bold_font

        # --- Formatting ---
        ws.column_dimensions['A'].width = 40
        ws.column_dimensions['B'].width = 18
        for cell in ws['B']:
            cell.number_format = currency_format

        # --- Create Response ---
        response = HttpResponse(content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        filename = f"Balance_Sheet_{as_at_date.strftime('%Y%m%d')}.xlsx"
        response['Content-Disposition'] = f'attachment; filename="{filename}"'
        wb.save(response)
        return response


class BalanceSheetExportPDFView(BalanceSheetReportView):
    """Handles PDF export for the Balance Sheet."""
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        report_data = self._get_balance_sheet_data(request)
        as_at_date = report_data.get('as_at_date')
        if not as_at_date:
            messages.error(request, "Please select a date to generate the PDF report.")
            return redirect('reporting:balance_sheet_report')

        pdf_context = report_data.copy()
        pdf_context.update({
            'report_title': f"Balance Sheet as at {as_at_date.strftime('%d %B %Y')}",
            'school_profile': SchoolProfile.objects.first(),
            'tenant': getattr(request, 'tenant', None),
            'current_datetime': timezone.now()
        })
        
        try:
            pdf = render_to_pdf('reporting/pdf/balance_sheet_pdf.html', pdf_context)
            if pdf:
                response = HttpResponse(pdf, content_type='application/pdf')
                filename = f"Balance_Sheet_{as_at_date.strftime('%Y%m%d')}.pdf"
                response['Content-Disposition'] = f'inline; filename="{filename}"'
                return response
        except Exception as e:
            logger.error(f"Error generating Balance Sheet PDF: {e}", exc_info=True)
            messages.error(request, "An error occurred while generating the PDF report.")
            
        return redirect('reporting:balance_sheet_report')
    
    

# = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = 
#
#      CASHFLOW STATEMENT REPORT
#
# = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =


class CashFlowStatementReportView(TenantPermissionRequiredMixin, BaseReportViewMixin, TemplateView):
    """
    Handles the display of the HTML Cash Flow Statement report page.
    """
    template_name = 'reporting/cash_flow_statement_report.html'

    # Attributes for BaseReportViewMixin
    permission_required = 'reporting.view_cash_flow_statement'
    report_title = _("Cash Flow Statement")
    filter_form_class = DateRangeForm
    export_csv_url_name = 'reporting:cash_flow_export_csv'
    export_pdf_url_name = 'reporting:cash_flow_export_pdf'
    export_excel_url_name = 'reporting:cash_flow_export_excel'

    def _get_cash_flow_data(self, request):
        """
        The core logic for fetching and processing all data for the Cash Flow Statement.
        This helper method is used by the HTML view and all export views.
        Returns a dictionary containing all calculated data.
        """
        # 1. Initialize the data structure
        data = {
            'start_date': None, 'end_date': None,
            'beginning_cash_balance': Decimal('0.00'),
            'ending_cash_balance': Decimal('0.00'),
            'net_change_in_cash': Decimal('0.00'),
            'operating_activities': [], 'net_cash_from_operating': Decimal('0.00'),
            'investing_activities': [], 'net_cash_from_investing': Decimal('0.00'),
            'financing_activities': [], 'net_cash_from_financing': Decimal('0.00'),
            'filter_form': self.filter_form_class(request.GET or None)
        }

        # 2. Validate filter form
        if not data['filter_form'].is_valid():
            return data

        start_date = data['filter_form'].cleaned_data.get('start_date')
        end_date = data['filter_form'].cleaned_data.get('end_date')

        if not start_date or not end_date:
            messages.error(request, _("A valid date range (start and end date) is required."))
            return data
        
        data.update({'start_date': start_date, 'end_date': end_date})

        # 3. Identify all 'Cash and Cash Equivalent' accounts
        try:
            cash_accounts = Account.objects.filter(is_cash_equivalent=True, is_active=True)
            if not cash_accounts.exists():
                messages.warning(request, _("No accounts marked as 'Cash Equivalent' were found. Please configure your Chart of Accounts."))
                return data
        except Exception as e:
            logger.error(f"Error fetching cash equivalent accounts: {e}")
            messages.error(request, _("An error occurred while loading account configurations."))
            return data
            
        # 4. Calculate Beginning and Ending Cash Balances
        beginning_balance = Decimal('0.00')
        ending_balance = Decimal('0.00')
        for acc in cash_accounts:
            beginning_balance += acc.get_balance(as_of_date=start_date - timezone.timedelta(days=1))
            ending_balance += acc.get_balance(as_of_date=end_date)
            
        data['beginning_cash_balance'] = beginning_balance
        data['ending_cash_balance'] = ending_balance

        # 5. Analyze cash movements during the period by their classification
        # We find the *other side* of transactions involving cash accounts
        transactions = JournalEntryItem.objects.filter(
            journal_entry__date__range=(start_date, end_date)
        ).exclude(
            account__in=cash_accounts # Exclude the cash side to get the activity side
        ).select_related('account')

        # Aggregate the movements by their cash flow classification
        # Note: We reverse the sign. A debit to an expense account (outflow) is a credit to cash.
        # A credit to a revenue account (inflow) is a debit to cash.
        # The sum of debits-credits on the non-cash side equals the net movement of cash.
        activity_summary = transactions.values(
            'account__name',
            'account__cash_flow_classification' # Assumes this field exists on Account model
        ).annotate(
            net_movement=Sum('debit') - Sum('credit')
        ).order_by('account__cash_flow_classification', 'account__name')

        # 6. Categorize into Operating, Investing, Financing
        for activity in activity_summary:
            classification = activity.get('account__cash_flow_classification')
            # The net movement amount is reversed for cash flow purposes.
            # e.g., A debit to an expense of 100 (net_movement > 0) is a cash OUTFLOW of 100.
            # e.g., A credit to revenue of 500 (net_movement < 0) is a cash INFLOW of 500.
            amount = -activity.get('net_movement', Decimal('0.00'))

            if amount == Decimal('0.00'):
                continue # Skip zero-impact activities

            item = {'description': activity['account__name'], 'amount': amount}

            if classification == Account.CashFlowClassification.OPERATING:
                data['operating_activities'].append(item)
                data['net_cash_from_operating'] += amount
            elif classification == Account.CashFlowClassification.INVESTING:
                data['investing_activities'].append(item)
                data['net_cash_from_investing'] += amount
            elif classification == Account.CashFlowClassification.FINANCING:
                data['financing_activities'].append(item)
                data['net_cash_from_financing'] += amount

        # 7. Final calculation for net change in cash
        total_net_flow = (
            data['net_cash_from_operating'] + 
            data['net_cash_from_investing'] + 
            data['net_cash_from_financing']
        )
        data['net_change_in_cash'] = total_net_flow

        # Sanity Check
        if not (data['beginning_cash_balance'] + total_net_flow) == data['ending_cash_balance']:
            logger.warning("Cash flow statement reconciliation failed. Beginning + Net Change != Ending.")
            messages.warning(request, _("Warning: The cash flow statement could not be fully reconciled. The figures may be inexact."))

        return data

    def get_context_data(self, **kwargs):
        """Prepares context by calling the main data helper."""
        context = super().get_context_data(**kwargs)
        cash_flow_data = self._get_cash_flow_data(self.request)
        context.update(cash_flow_data)
        
        # Dynamically set the report title for the template
        if context.get('start_date') and context.get('end_date'):
            start, end = context['start_date'], context['end_date']
            context['report_title'] = _(f"Cash Flow Statement from {start.strftime('%d %b %Y')} to {end.strftime('%d %b %Y')}")
        
        return context


class CashFlowStatementExportCSVView(CashFlowStatementReportView):
    """Handles CSV export for the Cash Flow Statement report."""
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        report_data = self._get_cash_flow_data(request)
        if not report_data.get('start_date'):
            messages.error(request, "Please select a valid period to generate the CSV export.")
            return redirect('reporting:cash_flow_statement_report')

        response = HttpResponse(content_type='text/csv')
        filename = f"Cash_Flow_Statement_{report_data['start_date'].strftime('%Y%m%d')}.csv"
        response['Content-Disposition'] = f'attachment; filename="{filename}"'
        writer = csv.writer(response)

        # Helper function to write rows
        def write_section(title, items, net_total):
            writer.writerow([title])
            for item in items:
                writer.writerow([f"  {item['description']}", item['amount']])
            writer.writerow([f"Net Cash from {title}", net_total])
            writer.writerow([])

        # Write data
        writer.writerow(['Cash Flow Statement'])
        writer.writerow([f"For the period {report_data['start_date']} to {report_data['end_date']}"])
        writer.writerow([])
        writer.writerow(['Beginning Cash Balance', report_data['beginning_cash_balance']])
        writer.writerow([])
        
        write_section('Operating Activities', report_data['operating_activities'], report_data['net_cash_from_operating'])
        write_section('Investing Activities', report_data['investing_activities'], report_data['net_cash_from_investing'])
        write_section('Financing Activities', report_data['financing_activities'], report_data['net_cash_from_financing'])

        writer.writerow(['Net Change in Cash', report_data['net_change_in_cash']])
        writer.writerow(['Ending Cash Balance', report_data['ending_cash_balance']])
        return response


class CashFlowStatementExportExcelView(CashFlowStatementReportView):
    """Handles Excel export for the Cash Flow Statement report."""
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        if not openpyxl:
            messages.error(request, "Excel export library is not available.")
            return redirect('reporting:cash_flow_statement_report')

        report_data = self._get_cash_flow_data(request)
        if not report_data.get('start_date'):
            messages.error(request, "Please select a period to generate the Excel export.")
            return redirect('reporting:cash_flow_statement_report')

        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "Cash Flow Statement"
        
        # --- Styles ---
        title_font = Font(bold=True, size=16)
        header_font = Font(bold=True, size=12)
        bold_font = Font(bold=True)
        currency_format = '_(* #,##0.00_);_(* (#,##0.00);_(* "-"??_);_(@_)'

        # --- Header ---
        title = f"Cash Flow Statement"
        period = f"For the Period {report_data['start_date'].strftime('%d-%b-%Y')} to {report_data['end_date'].strftime('%d-%b-%Y')}"
        ws['A1'] = title
        ws['A1'].font = title_font
        ws['A2'] = period
        
        # --- Main Body ---
        row = 4
        ws[f'A{row}'] = "Beginning Cash Balance"
        ws[f'B{row}'] = report_data['beginning_cash_balance']
        ws[f'B{row}'].font = bold_font
        row += 2

        def write_excel_section(start_row, title, items, net_total):
            ws[f'A{start_row}'] = title
            ws[f'A{start_row}'].font = header_font
            current_row = start_row + 1
            for item in items:
                ws[f'A{current_row}'] = item['description']
                ws[f'B{current_row}'] = item['amount']
                ws[f'A{current_row}'].alignment = Alignment(indent=1)
                current_row += 1
            ws[f'A{current_row}'] = f"Net Cash from {title}"
            ws[f'B{current_row}'] = net_total
            ws[f'B{current_row}'].font = bold_font
            return current_row + 2
        
        row = write_excel_section(row, "Cash Flow from Operating Activities", report_data['operating_activities'], report_data['net_cash_from_operating'])
        row = write_excel_section(row, "Cash Flow from Investing Activities", report_data['investing_activities'], report_data['net_cash_from_investing'])
        row = write_excel_section(row, "Cash Flow from Financing Activities", report_data['financing_activities'], report_data['net_cash_from_financing'])

        # --- Footer ---
        ws[f'A{row}'] = "Net Change in Cash"
        ws[f'B{row}'] = report_data['net_change_in_cash']
        ws[f'B{row}'].font = bold_font
        row += 1
        ws[f'A{row}'] = "Ending Cash Balance"
        ws[f'B{row}'] = report_data['ending_cash_balance']
        ws[f'B{row}'].font = bold_font

        # --- Formatting ---
        ws.column_dimensions['A'].width = 45
        ws.column_dimensions['B'].width = 20
        for cell in ws['B']:
            cell.number_format = currency_format

        # --- Create Response ---
        response = HttpResponse(content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        filename = f"Cash_Flow_Statement_{report_data['start_date'].strftime('%Y%m%d')}.xlsx"
        response['Content-Disposition'] = f'attachment; filename="{filename}"'
        wb.save(response)
        return response


class CashFlowStatementExportPDFView(CashFlowStatementReportView):
    """Handles PDF export for the Cash Flow Statement report."""
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        report_data = self._get_cash_flow_data(request)
        if not report_data.get('start_date'):
            messages.error(request, "Please select a period to generate the PDF report.")
            return redirect('reporting:cash_flow_statement_report')

        start, end = report_data['start_date'], report_data['end_date']
        
        pdf_context = report_data.copy()
        pdf_context.update({
            'report_title': f"Cash Flow Statement ({start.strftime('%d-%b-%Y')} to {end.strftime('%d-%b-%Y')})",
            'school_profile': SchoolProfile.objects.first(),
            'tenant': getattr(request, 'tenant', None),
            'current_datetime': timezone.now()
        })
        
        try:
            pdf = render_to_pdf('reporting/pdf/cash_flow_statement_pdf.html', pdf_context)
            if pdf:
                response = HttpResponse(pdf, content_type='application/pdf')
                filename = f"Cash_Flow_{start.strftime('%Y%m%d')}_{end.strftime('%Y%m%d')}.pdf"
                response['Content-Disposition'] = f'inline; filename="{filename}"'
                return response
        except Exception as e:
            logger.error(f"Error generating Cash Flow PDF: {e}", exc_info=True)
            messages.error(request, "Failed to generate the PDF report due to an unexpected error.")
            
        return redirect('reporting:cash_flow_statement_report')
    
    

# = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = 
#
#      BUDGET VARIANCE REPORT
#
# = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =


class BudgetVarianceReportView(TenantPermissionRequiredMixin, BaseReportViewMixin, TemplateView):
    """
    Handles the display of the HTML Budget Variance report page.
    """
    template_name = 'reporting/budget_variance_report.html'
    
    # Attributes for BaseReportViewMixin
    permission_required = 'reporting.view_budget_variance_report'
    report_title = _("Budget vs Actuals (Variance Report)")
    filter_form_class = BudgetReportFilterForm
    export_csv_url_name = 'reporting:budget_variance_export_csv'
    export_pdf_url_name = 'reporting:budget_variance_export_pdf'
    export_excel_url_name = 'reporting:budget_variance_export_excel' # Added Excel export URL

    def _get_budget_variance_data(self, request):
        """
        The core logic for fetching and processing all data for the budget variance report.
        This helper method is used by the HTML view and all export views.
        Returns a dictionary containing all calculated data.
        """
        # 1. Initialize the data structure to be returned
        data = {
            'academic_year': None, 'term': None, 'budget_items_variance': [],
            'total_budgeted_income': Decimal('0.00'), 'total_actual_income': Decimal('0.00'),
            'total_variance_income': Decimal('0.00'), 'total_budgeted_expense': Decimal('0.00'),
            'total_actual_expense': Decimal('0.00'), 'total_variance_expense': Decimal('0.00'),
            'net_budgeted_profit_loss': Decimal('0.00'), 'net_actual_profit_loss': Decimal('0.00'),
            'net_variance_profit_loss': Decimal('0.00'), 'start_date': None, 'end_date': None,
            'filter_form': self.filter_form_class(request.GET or None)
        }

        # 2. Validate filter form
        if not data['filter_form'].is_valid():
            # If the form is not valid, return the default empty data.
            # The view can then display the form with its errors.
            return data

        academic_year = data['filter_form'].cleaned_data.get('academic_year')
        term = data['filter_form'].cleaned_data.get('term')

        if not academic_year:
            # Although the form might be 'valid' without a selection, the report requires it.
            messages.error(request, _("An Academic Year must be selected to generate the report."))
            return data # Return default data

        # 3. Determine date range for "Actuals"
        start_date, end_date = None, None
        if term:
            start_date, end_date = term.start_date, term.end_date
        else:
            start_date, end_date = academic_year.start_date, academic_year.end_date

        if not all([start_date, end_date]):
            messages.error(request, _("Could not determine a valid date range from the selected period."))
            return data

        data.update({
            'academic_year': academic_year,
            'term': term,
            'start_date': start_date,
            'end_date': end_date,
        })
        
        # 4. Fetch and Process Budget Data
        report_lines = []
        budget_amounts_qs = BudgetAmount.objects.filter(academic_year=academic_year)
        if term:
            budget_amounts_qs = budget_amounts_qs.filter(term=term)
        
        budget_amounts = budget_amounts_qs.select_related(
            'budget_item', 'budget_item__linked_coa_account'
        ).order_by('budget_item__budget_item_type', 'budget_item__name')

        if not budget_amounts.exists():
            messages.info(request, _("No budget entries found for the selected period."))
            return data

        for ba in budget_amounts:
            budget_item = ba.budget_item
            budgeted_amount = ba.amount
            actual_amount = Decimal('0.00')

            # Calculate actual amount from linked Chart of Accounts
            if budget_item.linked_coa_account:
                actual_amount = budget_item.linked_coa_account.get_balance(
                    start_date=start_date, as_of_date=end_date
                )
            
            variance = budgeted_amount - actual_amount
            
            # Determine if variance is favorable
            is_favorable = False
            if budget_item.budget_item_type == BudgetItem.ItemTypeChoices.EXPENSE:
                if actual_amount <= budgeted_amount: is_favorable = True 
            elif budget_item.budget_item_type == BudgetItem.ItemTypeChoices.REVENUE:
                if actual_amount >= budgeted_amount: is_favorable = True

            report_lines.append({
                'item_name': budget_item.name,
                'account_code': budget_item.linked_coa_account.account_code if budget_item.linked_coa_account else 'N/A',
                'budget_item_type': budget_item.get_budget_item_type_display(),
                'budgeted': budgeted_amount,
                'actual': actual_amount,
                'variance': variance,
                'is_favorable': is_favorable,
            })

            # Accumulate totals
            if budget_item.budget_item_type == BudgetItem.ItemTypeChoices.REVENUE:
                data['total_budgeted_income'] += budgeted_amount
                data['total_actual_income'] += actual_amount
            elif budget_item.budget_item_type == BudgetItem.ItemTypeChoices.EXPENSE:
                data['total_budgeted_expense'] += budgeted_amount
                data['total_actual_expense'] += actual_amount
        
        # 5. Calculate Final Totals and Update Data Dictionary
        data['budget_items_variance'] = report_lines
        data['total_variance_income'] = data['total_actual_income'] - data['total_budgeted_income'] # Note: Favorable is positive
        data['total_variance_expense'] = data['total_budgeted_expense'] - data['total_actual_expense'] # Note: Favorable is positive
        
        data['net_budgeted_profit_loss'] = data['total_budgeted_income'] - data['total_budgeted_expense']
        data['net_actual_profit_loss'] = data['total_actual_income'] - data['total_actual_expense']
        data['net_variance_profit_loss'] = data['net_actual_profit_loss'] - data['net_budgeted_profit_loss']

        return data

    def get_context_data(self, **kwargs):
        """
        Prepares context for the HTML page by calling the main data helper.
        """
        context = super().get_context_data(**kwargs)
        budget_data = self._get_budget_variance_data(self.request)
        context.update(budget_data)
        return context


class BudgetVarianceExportCSVView(BudgetVarianceReportView):
    """
    Handles the CSV export for the Budget Variance report.
    It inherits the data-gathering logic from BudgetVarianceReportView.
    """
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        report_data = self._get_budget_variance_data(request)
        academic_year = report_data.get('academic_year')

        if not academic_year:
            messages.error(request, "Please select a valid period to generate the CSV export.")
            return redirect('reporting:budget_variance_report')

        response = HttpResponse(content_type='text/csv')
        filename = f"Budget_Variance_{academic_year.name.replace(' ', '_')}.csv"
        response['Content-Disposition'] = f'attachment; filename="{filename}"'

        writer = csv.writer(response)

        # Write report summary
        writer.writerow([self.report_title])
        writer.writerow([f'Academic Year: {academic_year.name}'])
        if report_data.get('term'):
            writer.writerow([f'Term: {report_data["term"].name}'])
        writer.writerow([])

        # Write table headers
        writer.writerow([
            'Item', 'Type', 'Account Code', 'Budgeted Amount',
            'Actual Amount', 'Variance'
        ])

        # Write data rows
        for item in report_data.get('budget_items_variance', []):
            writer.writerow([
                item.get('item_name'), item.get('budget_item_type'), item.get('account_code'),
                item.get('budgeted'), item.get('actual'), item.get('variance')
            ])
        
        # Write summary totals
        writer.writerow([]); writer.writerow(['Summary Totals'])
        writer.writerow(['', '', 'Total Budgeted Income', report_data['total_budgeted_income']])
        writer.writerow(['', '', 'Total Actual Income', report_data['total_actual_income']])
        writer.writerow(['', '', 'Total Budgeted Expense', report_data['total_budgeted_expense']])
        writer.writerow(['', '', 'Total Actual Expense', report_data['total_actual_expense']])
        writer.writerow(['', '', 'Net Profit/Loss (Budgeted)', report_data['net_budgeted_profit_loss']])
        writer.writerow(['', '', 'Net Profit/Loss (Actual)', report_data['net_actual_profit_loss']])

        return response


class BudgetVarianceExportExcelView(BudgetVarianceReportView):
    """
    Handles the Excel (.xlsx) export for the Budget Variance report.
    """
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        if not openpyxl:
            messages.error(request, "The library required for Excel exports is not installed.")
            return redirect('reporting:budget_variance_report')

        report_data = self._get_budget_variance_data(request)
        academic_year = report_data.get('academic_year')

        if not academic_year:
            messages.error(request, "Please select a valid period to generate the Excel export.")
            return redirect('reporting:budget_variance_report')

        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "Budget Variance"

        # --- Styles ---
        header_font = Font(bold=True, size=14)
        subheader_font = Font(bold=True, size=12)
        table_header_font = Font(bold=True)
        currency_format = '#,##0.00'

        # --- Header ---
        ws['A1'] = self.report_title
        ws['A1'].font = header_font
        ws.merge_cells('A1:F1')
        ws['A2'] = f"Academic Year: {academic_year.name}"
        if report_data.get('term'):
            ws['A3'] = f"Term: {report_data['term'].name}"

        # --- Table Headers ---
        headers = ['Item', 'Type', 'Account Code', 'Budgeted', 'Actual', 'Variance']
        for col_num, header_title in enumerate(headers, 1):
            cell = ws.cell(row=5, column=col_num, value=header_title)
            cell.font = table_header_font

        # --- Data Rows ---
        row_num = 6
        for item in report_data.get('budget_items_variance', []):
            ws.cell(row=row_num, column=1, value=item.get('item_name'))
            ws.cell(row=row_num, column=2, value=item.get('budget_item_type'))
            ws.cell(row=row_num, column=3, value=item.get('account_code'))
            
            b_cell = ws.cell(row=row_num, column=4, value=item.get('budgeted'))
            a_cell = ws.cell(row=row_num, column=5, value=item.get('actual'))
            v_cell = ws.cell(row=row_num, column=6, value=item.get('variance'))
            
            for cell in [b_cell, a_cell, v_cell]:
                cell.number_format = currency_format
            row_num += 1

        # --- Totals Section ---
        row_num += 2 # Add a small gap
        ws.cell(row=row_num, column=3, value="Summary").font = subheader_font
        
        summary_items = [
            ("Total Budgeted Income", report_data['total_budgeted_income']),
            ("Total Actual Income", report_data['total_actual_income']),
            ("Total Income Variance", report_data['total_variance_income']),
            ("Total Budgeted Expense", report_data['total_budgeted_expense']),
            ("Total Actual Expense", report_data['total_actual_expense']),
            ("Total Expense Variance", report_data['total_variance_expense']),
            ("Net Profit/Loss (Budgeted)", report_data['net_budgeted_profit_loss']),
            ("Net Profit/Loss (Actual)", report_data['net_actual_profit_loss']),
        ]
        
        for i, (label, value) in enumerate(summary_items, start=row_num):
            ws.cell(row=i, column=4, value=label).alignment = Alignment(horizontal='right')
            value_cell = ws.cell(row=i, column=5, value=value)
            value_cell.number_format = currency_format
            value_cell.font = Font(bold=True)

        # Auto-fit columns
        for col in ws.columns:
            max_length = 0
            column = col[0].column_letter
            for cell in col:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(cell.value)
                except:
                    pass
            adjusted_width = (max_length + 2)
            ws.column_dimensions[column].width = adjusted_width

        # --- Create Response ---
        response = HttpResponse(
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        filename = f"Budget_Variance_{academic_year.name.replace(' ', '_')}.xlsx"
        response['Content-Disposition'] = f'attachment; filename="{filename}"'
        
        wb.save(response)
        return response


class BudgetVarianceExportPDFView(BudgetVarianceReportView):
    """
    Handles the PDF export for the Budget Variance report.
    """
    http_method_names = ['get']
    
    def get(self, request, *args, **kwargs):
        report_data = self._get_budget_variance_data(request)
        academic_year = report_data.get('academic_year')

        if not academic_year:
            messages.error(request, "Please select a period to generate the PDF report.")
            return redirect('reporting:budget_variance_report')

        # Add any other context needed specifically by the PDF template
        pdf_context = report_data.copy() # Start with all report data
        pdf_context['report_title'] = self.report_title
        pdf_context['school_profile'] = SchoolProfile.objects.first() # Example
        pdf_context['tenant'] = getattr(request, 'tenant', None)
        pdf_context['current_datetime'] = timezone.now()

        try:
            pdf = render_to_pdf('reporting/pdf/budget_variance_pdf.html', pdf_context)

            if pdf:
                response = HttpResponse(pdf, content_type='application/pdf')
                filename = f"Budget_Variance_{academic_year.name.replace(' ', '_')}.pdf"
                # Use 'inline' to display in browser, 'attachment' to force download
                response['Content-Disposition'] = f'inline; filename="{filename}"'
                return response
            else:
                messages.error(request, "The system failed to generate the PDF document.")
                return redirect('reporting:budget_variance_report')
        except Exception as e:
            logger.error(f"Error generating PDF for Budget Variance Report: {e}", exc_info=True)
            messages.error(request, f"An unexpected error occurred while creating the PDF: {e}")
            return redirect('reporting:budget_variance_report')



# = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = 
#
#      THE END OF BUDGET VARIANCE REPORT
#
# = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
from django.views import View
from django.shortcuts import render
class ReportingHomeView(TenantPermissionRequiredMixin, TemplateView):
    template_name = 'reporting/reporting_home.html'
    permission_required = 'reporting.view_reports'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Generate report links based on user permissions
        report_links = []
        user = self.request.user

        # Basic Reports
        if user.has_perm('reporting.view_outstanding_fees_report'):
            report_links.append({
                'name': 'Outstanding Fees Report',
                'url_name': 'reporting:outstanding_fees_report'
            })

        if user.has_perm('reporting.view_collection_report'):
            report_links.append({
                'name': 'Collection Report',
                'url_name': 'reporting:collection_report'
            })

        if user.has_perm('reporting.view_payment_summary_report'):
            report_links.append({
                'name': 'Payment Summary Report',
                'url_name': 'reporting:payment_summary_report'
            })

        if user.has_perm('reporting.view_student_ledger_report'):
            report_links.append({
                'name': 'Student Ledger Report',
                'url_name': 'reporting:student_ledger_report'
            })

        if user.has_perm('reporting.view_fee_projection_report'):
            report_links.append({
                'name': 'Fee Projection Report',
                'url_name': 'reporting:fee_projection_report'
            })

        # Advanced Financial Reports
        if user.has_perm('reporting.view_general_ledger_report'):
            report_links.append({
                'name': 'General Ledger Report',
                'url_name': 'reporting:general_ledger_report'
            })

        if user.has_perm('reporting.view_accounts_receivable_aging_report'):
            report_links.append({
                'name': 'Accounts Receivable Aging Report',
                'url_name': 'reporting:accounts_receivable_aging_report'
            })

        if user.has_perm('reporting.view_accounts_payable_report'):
            report_links.append({
                'name': 'Accounts Payable Report',
                'url_name': 'reporting:accounts_payable_report'
            })

        if user.has_perm('reporting.view_bank_reconciliation_report'):
            report_links.append({
                'name': 'Bank Reconciliation Report',
                'url_name': 'reporting:bank_reconciliation_report'
            })

        if user.has_perm('reporting.view_budget_vs_actual_report'):
            report_links.append({
                'name': 'Budget vs Actual Report',
                'url_name': 'reporting:budget_vs_actual_report'
            })

        if user.has_perm('reporting.view_fee_collection_analysis_report'):
            report_links.append({
                'name': 'Fee Collection Analysis Report',
                'url_name': 'reporting:fee_collection_analysis_report'
            })

        if user.has_perm('reporting.view_student_account_statement_report'):
            report_links.append({
                'name': 'Student Account Statement',
                'url_name': 'reporting:student_account_statement_report'
            })

        if user.has_perm('reporting.view_classwise_fee_collection_report'):
            report_links.append({
                'name': 'Class-wise Fee Collection Report',
                'url_name': 'reporting:classwise_fee_collection_report'
            })

        if user.has_perm('reporting.view_fee_defaulters_report'):
            report_links.append({
                'name': 'Fee Defaulters Report',
                'url_name': 'reporting:fee_defaulters_report'
            })

        if user.has_perm('reporting.view_cash_flow_forecasting_report'):
            report_links.append({
                'name': 'Cash Flow Forecasting Report',
                'url_name': 'reporting:cash_flow_forecasting_report'
            })

        if user.has_perm('reporting.view_profitability_analysis_report'):
            report_links.append({
                'name': 'Profitability Analysis Report',
                'url_name': 'reporting:profitability_analysis_report'
            })

        if user.has_perm('reporting.view_financial_ratio_analysis_report'):
            report_links.append({
                'name': 'Financial Ratio Analysis Report',
                'url_name': 'reporting:financial_ratio_analysis_report'
            })

        if user.has_perm('reporting.view_trial_balance_report'):
            report_links.append({
                'name': 'Trial Balance Report',
                'url_name': 'reporting:trial_balance_report'
            })

        if user.has_perm('reporting.view_income_statement_report') or user.has_perm('reporting.view_income_expense_report'):
            report_links.append({
                'name': 'Income Statement Report',
                'url_name': 'reporting:income_expense_report'
            })

        if user.has_perm('reporting.view_balance_sheet_report'):
            report_links.append({
                'name': 'Balance Sheet Report',
                'url_name': 'reporting:balance_sheet_report'
            })

        if user.has_perm('reporting.view_cash_flow_statement_report') or user.has_perm('reporting.view_cash_flow_statement'):
            report_links.append({
                'name': 'Cash Flow Statement Report',
                'url_name': 'reporting:cash_flow_statement_report'
            })

        if user.has_perm('reporting.view_budget_variance_report'):
            report_links.append({
                'name': 'Budget Variance Report',
                'url_name': 'reporting:budget_variance_report'
            })

        if user.has_perm('reporting.view_expense_report'):
            report_links.append({
                'name': 'Expense Report',
                'url_name': 'reporting:expense_report'
            })

        context['report_links'] = report_links
        context['view_title'] = 'Reports Dashboard'

        return context



# D:\school_fees_saas_v2\apps\reporting\views.py
from django.shortcuts import render, get_object_or_404
from django.views.generic import ListView, DetailView, TemplateView
from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin
from django.db.models import Sum, Q, F
from django.utils import timezone
import datetime

from apps.payments.models import Payment
from apps.fees.models import Invoice, StudentConcession
from apps.students.models import Student
from apps.common.utils import get_current_academic_year # Assuming you have this utility

from .forms import PaymentSummaryFilterForm, StudentLedgerFilterForm

# --- Base Report View (Optional, for common functionality) ---
class BaseReportView(LoginRequiredMixin, PermissionRequiredMixin, TemplateView):
    raise_exception = True # Or False for redirect to login

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['current_academic_year'] = get_current_academic_year(self.request)
        # Add other common report context if any
        return context

# = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = 
#
#      PAYMENT SUMMARY REPORT
#
# = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = 

class PaymentSummaryReportView(TenantPermissionRequiredMixin, BaseReportViewMixin, TemplateView):
    template_name = 'reporting/payment_summary_report.html'
    permission_required = 'reporting.view_payment_summary_report'
    report_title = _("Payment Summary Report")
    filter_form_class = PaymentReportFilter
    export_csv_url_name = 'reporting:payment_summary_export_csv'
    export_pdf_url_name = 'reporting:payment_summary_export_pdf'
    export_excel_url_name = 'reporting:payment_summary_export_excel'

    def _get_report_data(self, request):
        base_queryset = Payment.objects.filter(
            status=Payment.STATUS_COMPLETED
        ).select_related('student', 'payment_method', 'parent_payer')

        filter_form = self.filter_form_class(request.GET, queryset=base_queryset)
        filtered_queryset = filter_form.qs.order_by('-payment_date')

        summary_totals = filtered_queryset.aggregate(
            total_amount=Coalesce(Sum('amount'), Decimal('0.00')),
            total_payments=Count('id')
        )
        
        method_breakdown = filtered_queryset.values(
            'payment_method__name'
        ).annotate(
            method_total=Sum('amount'),
            method_count=Count('id')
        ).order_by('-method_total')

        data = {
            'payments': filtered_queryset,
            'filter_form': filter_form.form,
            'summary_totals': summary_totals,
            'method_breakdown': method_breakdown,
            'start_date': filter_form.form.cleaned_data.get('start_date') if filter_form.form.is_valid() else None,
            'end_date': filter_form.form.cleaned_data.get('end_date') if filter_form.form.is_valid() else None,
        }
        return data

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        report_data = self._get_report_data(self.request)
        
        paginator = Paginator(report_data['payments'], 30)
        page_number = self.request.GET.get('page')
        page_obj = paginator.get_page(page_number)

        context.update(report_data)
        context['payments'] = page_obj
        context['page_obj'] = page_obj
        context['is_paginated'] = page_obj.has_other_pages()
        
        return context


class PaymentSummaryExportCSVView(PaymentSummaryReportView):
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        report_data = self._get_report_data(request)
        payments = report_data.get('payments', [])

        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename="payment_summary_report.csv"'
        writer = csv.writer(response)

        writer.writerow([self.report_title])
        writer.writerow([f"Period: {report_data['start_date'] or 'Beginning'} to {report_data['end_date'] or 'Today'}"])
        writer.writerow([])
        writer.writerow(['Total Amount Received', report_data['summary_totals'].get('total_amount')])
        writer.writerow(['Total Number of Payments', report_data['summary_totals'].get('total_payments')])
        writer.writerow([])
        
        writer.writerow(['Payment Date', 'Student', 'Admission No', 'Payment Method', 'Reference', 'Amount'])

        for payment in payments:
            writer.writerow([
                payment.payment_date.strftime('%Y-%m-%d'),
                payment.student.get_full_name() if payment.student else 'N/A',
                payment.student.admission_number if payment.student else 'N/A',
                payment.payment_method.name if payment.payment_method else 'N/A',
                payment.reference_number or '',
                payment.amount
            ])
        return response


class PaymentSummaryExportPDFView(PaymentSummaryReportView):
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        report_data = self._get_report_data(request)
        
        report_data['school_profile'] = SchoolProfile.objects.first()
        report_data['tenant'] = request.tenant
        report_data['report_title'] = self.report_title

        pdf = render_to_pdf('reporting/pdf/payment_summary_pdf.html', report_data)
        if not pdf:
            messages.error(request, "There was an error generating the PDF report.")
            return redirect('reporting:payment_summary_report')

        response = HttpResponse(pdf, content_type='application/pdf')
        response['Content-Disposition'] = 'inline; filename="payment_summary_report.pdf"'
        return response


class PaymentSummaryExportExcelView(PaymentSummaryReportView):
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        try:
            import openpyxl
            from openpyxl.styles import Font
        except ImportError:
            messages.error(request, "The library required for Excel export (openpyxl) is not installed.")
            return redirect('reporting:payment_summary_report')

        report_data = self._get_report_data(request)
        payments = report_data.get('payments', [])

        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "Payment Summary"
        
        header_font = Font(bold=True, size=14)
        sub_header_font = Font(bold=True)
        
        ws['A1'] = self.report_title
        ws['A1'].font = header_font
        ws['A2'] = f"Period: {report_data['start_date'] or 'Beginning'} to {report_data['end_date'] or 'Today'}"
        
        ws['A4'] = "Total Amount Received:"
        ws['A4'].font = sub_header_font
        ws['B4'] = report_data['summary_totals'].get('total_amount')
        ws['B4'].number_format = '#,##0.00'

        ws['A5'] = "Total Number of Payments:"
        ws['A5'].font = sub_header_font
        ws['B5'] = report_data['summary_totals'].get('total_payments')
        
        headers = ['Payment Date', 'Student', 'Admission No', 'Payment Method', 'Reference', 'Amount']
        for col_num, header_title in enumerate(headers, 1):
            ws.cell(row=7, column=col_num, value=header_title).font = sub_header_font

        current_row = 8
        for payment in payments:
            ws.cell(row=current_row, column=1, value=payment.payment_date)
            ws.cell(row=current_row, column=2, value=payment.student.get_full_name() if payment.student else 'N/A')
            ws.cell(row=current_row, column=3, value=payment.student.admission_number if payment.student else 'N/A')
            ws.cell(row=current_row, column=4, value=payment.payment_method.name if payment.payment_method else 'N/A')
            ws.cell(row=current_row, column=5, value=payment.reference_number or '')
            amount_cell = ws.cell(row=current_row, column=6, value=payment.amount)
            amount_cell.number_format = '#,##0.00'
            current_row += 1
            
        ws.column_dimensions['A'].width = 15
        ws.column_dimensions['B'].width = 30
        ws.column_dimensions['C'].width = 15
        ws.column_dimensions['D'].width = 20
        ws.column_dimensions['E'].width = 25
        ws.column_dimensions['F'].width = 15

        response = HttpResponse(content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        response['Content-Disposition'] = 'attachment; filename="payment_summary_report.xlsx"'
        wb.save(response)
        return response




# = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = 
#
#      STUDENT LEDGER REPORT
#
# = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = 
# D:\school_fees_saas_v2\apps\reporting\views.py

class StudentLedgerReportView(TenantPermissionRequiredMixin, BaseReportViewMixin, TemplateView):
    """
    Handles the display of the HTML Student Ledger report page.
    """
    template_name = 'reporting/student_ledger_report.html'
    
    # Attributes for BaseReportViewMixin
    permission_required = 'reporting.view_student_ledger_report'
    report_title = _("Student Ledger")
    filter_form_class = StudentLedgerFilterForm
    export_csv_url_name = 'reporting:student_ledger_export_csv'
    export_pdf_url_name = 'reporting:student_ledger_export_pdf'

    def _get_ledger_data(self, request):
        """
        The core logic for fetching and processing all data for the student ledger.
        This helper method is used by the HTML view and all export views.
        Returns a dictionary containing all calculated data.
        """
        # Initialize the data structure to be returned
        data = {
            'student': None, 'transactions': [], 'opening_balance': Decimal('0.00'),
            'closing_balance': Decimal('0.00'), 'total_debits': Decimal('0.00'),
            'total_credits': Decimal('0.00'), 'start_date': None, 'end_date': None,
            'filter_form': self.filter_form_class(request.GET or None)
        }

        # Only proceed if the filter form is valid (i.e., a student is selected)
        if not data['filter_form'].is_valid():
            return data

        student = data['filter_form'].cleaned_data.get('student')
        if not student:
            return data # Return default empty data if no student is selected

        start_date = data['filter_form'].cleaned_data.get('start_date')
        end_date = data['filter_form'].cleaned_data.get('end_date') or timezone.now().date()
        
        data.update({
            'student': student,
            'start_date': start_date,
            'end_date': end_date,
        })

        # --- 1. Calculate Opening Balance ---
        opening_balance = Decimal('0.00')
        if start_date:
            # Sum of invoice balances before the start date
            # Calculate total amount as (subtotal_amount - total_concession_amount) - amount_paid
            ob_invoices = Invoice.objects.filter(
                student=student, issue_date__lt=start_date
            ).aggregate(balance=Coalesce(Sum(F('subtotal_amount') - F('total_concession_amount') - F('amount_paid')), Decimal('0.00')))['balance']

            # Sum of payments before the start date (these are credits, so we subtract)
            ob_payments = Payment.objects.filter(
                student=student, payment_date__date__lt=start_date, status='COMPLETED'
            ).aggregate(total=Coalesce(Sum('amount'), Decimal('0.00')))['total']

            # Note: We assume concessions are applied directly to invoices and reflected in their balance.
            # If you have standalone concessions, you would subtract them here as well.
            opening_balance = ob_invoices - ob_payments
        
        data['opening_balance'] = opening_balance
        
        # --- 2. Fetch Transactions for the Period ---
        transactions_data = []

        # Build date filters
        invoice_filters = {'student': student}
        payment_filters = {'student': student, 'status': 'COMPLETED'}
        concession_filters = {'student': student}

        if start_date and end_date:
            invoice_filters['issue_date__range'] = (start_date, end_date)
            payment_filters['payment_date__date__range'] = (start_date, end_date)
            concession_filters['granted_at__date__range'] = (start_date, end_date)
        elif start_date:
            invoice_filters['issue_date__gte'] = start_date
            payment_filters['payment_date__date__gte'] = start_date
            concession_filters['granted_at__date__gte'] = start_date
        elif end_date:
            invoice_filters['issue_date__lte'] = end_date
            payment_filters['payment_date__date__lte'] = end_date
            concession_filters['granted_at__date__lte'] = end_date

        # Invoices
        for inv in Invoice.objects.filter(**invoice_filters):
            # Calculate total amount (subtotal - concessions) since total_amount is a property, not a field
            total_amount = inv.subtotal_amount - inv.total_concession_amount
            transactions_data.append({
                'date': inv.issue_date,
                'description': f"Invoice #{inv.invoice_number_display}",
                'debit': total_amount,
                'credit': Decimal('0.00'),
                'type': 'Invoice',
                'reference_number': inv.invoice_number_display,
                'reference_link': inv.get_absolute_url() if hasattr(inv, 'get_absolute_url') else None
            })

        # Payments
        for pay in Payment.objects.filter(**payment_filters):
            payment_method_name = pay.payment_method.name if pay.payment_method else 'Unknown Method'
            transactions_data.append({
                'date': pay.payment_date.date(),
                'description': f"Payment Received ({payment_method_name})",
                'debit': Decimal('0.00'),
                'credit': pay.amount,
                'type': 'Payment',
                'reference_number': pay.receipt_number_display if hasattr(pay, 'receipt_number_display') else f"PAY-{pay.pk}",
                'reference_link': None  # Add payment detail URL if you have one
            })

        # Concessions (if they act as a credit outside of an invoice)
        # Note: StudentConcession doesn't have an amount field - concessions are typically applied to invoices
        # For now, we'll show concessions as informational entries without amounts
        for conc in StudentConcession.objects.filter(**concession_filters).select_related('concession_type'):
            transactions_data.append({
                'date': conc.granted_at.date(),
                'description': f"Concession Applied: {conc.concession_type.name}",
                'debit': Decimal('0.00'),
                'credit': Decimal('0.00'),
                'type': 'Concession',
                'reference_number': None,
                'reference_link': None
            })

        # --- 3. Sort transactions and calculate running balance ---
        sorted_transactions = sorted(transactions_data, key=lambda x: x['date'])
        
        running_balance = opening_balance
        total_debits_period = Decimal('0.00')
        total_credits_period = Decimal('0.00')
        
        for tx in sorted_transactions:
            debit = tx.get('debit') or Decimal('0.00')
            credit = tx.get('credit') or Decimal('0.00')
            running_balance += (debit - credit)
            tx['balance'] = running_balance
            total_debits_period += debit
            total_credits_period += credit

        data['transactions'] = sorted_transactions
        data['total_debits'] = total_debits_period
        data['total_credits'] = total_credits_period
        data['closing_balance'] = running_balance

        # Add additional context variables for the template
        data['report_period_start'] = start_date
        data['report_period_end'] = end_date

        return data

    def get_context_data(self, **kwargs):
        """
        Prepares context for the HTML page by calling the main data helper.
        """
        context = super().get_context_data(**kwargs)
        ledger_data = self._get_ledger_data(self.request)
        context.update(ledger_data)
        return context


class StudentLedgerExportCSVView(StudentLedgerReportView):
    """
    Handles the CSV export for the Student Ledger report.
    It inherits the data-gathering logic from StudentLedgerReportView.
    """
    http_method_names = ['get'] # This view only responds to GET requests

    def get(self, request, *args, **kwargs):
        # Get the report data
        report_data = self._get_ledger_data(request)
        student = report_data.get('student')
        transactions = report_data.get('transactions', [])

        # Create CSV response
        response = HttpResponse(content_type='text/csv')
        filename = f"Student_Ledger_{student.get_full_name().replace(' ', '_') if student else 'Unknown'}.csv"
        response['Content-Disposition'] = f'attachment; filename="{filename}"'

        import csv
        writer = csv.writer(response)

        # Write headers
        writer.writerow(['Student Ledger Report'])
        if student:
            writer.writerow([f'Student: {student.get_full_name()}'])
            writer.writerow([f'Admission No: {getattr(student, "admission_number", "N/A")}'])
        writer.writerow([f'Transactions: {len(transactions)}'])
        writer.writerow([])

        # Write table headers
        writer.writerow(['Date', 'Description', 'Reference', 'Debit', 'Credit', 'Balance'])

        # Write transaction data
        for tx in transactions:
            writer.writerow([
                str(tx.get('date', '')),
                str(tx.get('description', '')),
                str(tx.get('reference_number', '')),
                str(tx.get('debit', '') if tx.get('debit') else ''),
                str(tx.get('credit', '') if tx.get('credit') else ''),
                str(tx.get('balance', ''))
            ])

        return response
    

class StudentLedgerExportExcelView(StudentLedgerReportView):
    """
    Handles Excel export for Student Ledger report.
    """
    def get(self, request, *args, **kwargs):
        from django.http import HttpResponse

        # Simple test response first
        try:
            import openpyxl

            wb = openpyxl.Workbook()
            ws = wb.active
            ws.title = "Test"

            ws['A1'] = "Test Excel Export"
            ws['A2'] = "This is working"

            response = HttpResponse(
                content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )
            response['Content-Disposition'] = 'attachment; filename="test.xlsx"'

            wb.save(response)
            return response

        except ImportError:
            return HttpResponse("Excel library not available", content_type='text/plain')


class StudentLedgerExportPDFView(StudentLedgerReportView):
    """
    Handles PDF export.
    """
    def get(self, request, *args, **kwargs):
        report_data = self._get_ledger_data(request)
        student = report_data.get('student')

        if not student:
            messages.error(request, "Please select a student before exporting to PDF.")
            return redirect('reporting:student_ledger_report')

        # Add any other context needed specifically by the PDF template
        report_data['report_title'] = f"Student Ledger for {student.get_full_name()}"
        try:
            report_data['school_profile'] = SchoolProfile.objects.first()
        except:
            report_data['school_profile'] = None
        report_data['tenant'] = getattr(request, 'tenant', None)

        try:
            pdf = render_to_pdf('reporting/pdf/student_ledger_pdf.html', report_data)

            if pdf:
                response = HttpResponse(pdf, content_type='application/pdf')
                filename = f"Student_Ledger_{student.get_full_name().replace(' ', '_')}.pdf"
                response['Content-Disposition'] = f'inline; filename="{filename}"'
                return response
            else:
                messages.error(request, "Failed to generate PDF. Please try again.")
                return redirect('reporting:student_ledger_report')
        except Exception as e:
            messages.error(request, f"Error generating PDF: {str(e)}")
            return redirect('reporting:student_ledger_report')
            
        messages.error(request, "An error occurred while generating the PDF report.")
        return redirect('reporting:student_ledger_report')
    
# = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = 
#
#      FEES PROJECTION REPORT
#
# = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = 


class FeeProjectionReportView(TenantPermissionRequiredMixin, BaseReportViewMixin, TemplateView):
    """
    Handles the display of the HTML Fee Projection report page.
    """
    template_name = 'reporting/fee_projection_report.html'
    
    # Attributes for BaseReportViewMixin
    permission_required = 'reporting.view_fee_projection_report'
    report_title = _("Fee Projection Report")
    filter_form_class = None # No filters are used for this report
    export_csv_url_name = 'reporting:fee_projection_export_csv'
    export_pdf_url_name = 'reporting:fee_projection_export_pdf'
    export_excel_url_name = 'reporting:fee_projection_export_excel'

    def _get_fee_projection_data(self, request):
        """
        The core logic for calculating fee projections based on current enrollment and fee structures.
        """
        # 1. Initialize the data structure
        data = {
            'projected_items': [],
            'total_projected': Decimal('0.00'),
            'current_academic_year': None,
            'total_active_students': 0,
            'error_message': None,
            'filter_form': None # Explicitly set to None as we don't use a form
        }

        try:
            # 2. Get current academic year
            current_year = AcademicYear.objects.filter(is_current=True, is_active=True).first()
            if not current_year:
                current_year = AcademicYear.objects.filter(is_active=True).order_by('-start_date').first()

            if not current_year:
                data['error_message'] = _("No active academic year found. Cannot calculate projections.")
                return data
            
            data['current_academic_year'] = current_year
            data['total_active_students'] = Student.objects.filter(is_active=True).count()

            # 3. Iterate through active classes to calculate projections
            active_classes = SchoolClass.objects.filter(is_active=True).order_by('name')

            for class_obj in active_classes:
                student_count = Student.objects.filter(
                    current_class=class_obj,
                    is_active=True
                ).count()

                if student_count == 0:
                    continue

                # Get the total fee amount for one student in this class for the year
                # This assumes one fee structure per fee head per class.
                class_fee_structures = FeeStructure.objects.filter(
                    school_class=class_obj,
                    academic_year=current_year,
                    is_active=True
                )
                
                class_total_per_student = class_fee_structures.aggregate(
                    total=Coalesce(Sum('amount'), Decimal('0.00'))
                )['total']

                projected_for_class = class_total_per_student * student_count
                
                if projected_for_class > 0:
                    data['projected_items'].append({
                        'name': f"{class_obj.name}",
                        'student_count': student_count,
                        'amount_per_student': class_total_per_student,
                        'total_amount': projected_for_class
                    })
                    data['total_projected'] += projected_for_class
        
        except Exception as e:
            logger.error(f"Error calculating fee projections: {e}", exc_info=True)
            data['error_message'] = _("An unexpected error occurred while calculating projections.")

        return data

    def get_context_data(self, **kwargs):
        """Prepares context for the HTML page."""
        context = super().get_context_data(**kwargs)
        report_data = self._get_fee_projection_data(self.request)
        context.update(report_data)
        context['school_profile'] = SchoolProfile.objects.first()
        return context


class FeeProjectionExportCSVView(FeeProjectionReportView):
    """Handles CSV export for the Fee Projection Report."""
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        report_data = self._get_fee_projection_data(request)
        
        response = HttpResponse(content_type='text/csv')
        filename = f"Fee_Projection_{timezone.now().strftime('%Y%m%d')}.csv"
        response['Content-Disposition'] = f'attachment; filename="{filename}"'
        writer = csv.writer(response)
        
        writer.writerow(['Fee Projection Report'])
        if report_data.get('current_academic_year'):
            writer.writerow([f"For Academic Year: {report_data['current_academic_year'].name}"])
        writer.writerow([])
        
        headers = ['Class', 'Active Students', 'Fee Per Student', 'Total Projected Amount']
        writer.writerow(headers)

        for item in report_data.get('projected_items', []):
            writer.writerow([
                item['name'], item['student_count'],
                item['amount_per_student'], item['total_amount']
            ])
            
        writer.writerow([])
        writer.writerow(['', '', 'Grand Total Projected', report_data.get('total_projected')])
        
        return response


class FeeProjectionExportExcelView(FeeProjectionReportView):
    """Handles Excel export for the Fee Projection Report."""
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        if not openpyxl:
            messages.error(request, "Excel export library not available.")
            return redirect('reporting:fee_projection_report')

        report_data = self._get_fee_projection_data(request)
        
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "Fee Projection"
        
        # Styles
        title_font, header_font, bold_font = Font(bold=True, size=14), Font(bold=True), Font(bold=True)
        currency_format = '#,##0.00'

        # Headers
        ws['A1'] = "Fee Projection Report"
        ws['A1'].font = title_font
        if report_data.get('current_academic_year'):
            ws['A2'] = f"For Academic Year: {report_data['current_academic_year'].name}"
        
        headers = ['Class', 'Active Students', 'Fee Per Student', 'Total Projected Amount']
        ws.append([]) # Spacer row
        ws.append(headers)
        for cell in ws[4]: cell.font = header_font

        # Data
        for item in report_data.get('projected_items', []):
            ws.append([
                item['name'], item['student_count'],
                item['amount_per_student'], item['total_amount']
            ])
        
        # Total
        ws.append([])
        total_row = ws.max_row
        ws.cell(row=total_row, column=3, value="Grand Total Projected").font = bold_font
        ws.cell(row=total_row, column=4, value=report_data.get('total_projected')).font = bold_font

        # Formatting
        ws.column_dimensions['A'].width = 30
        ws.column_dimensions['B'].width = 18
        ws.column_dimensions['C'].width = 20
        ws.column_dimensions['D'].width = 25
        for col_letter in ['C', 'D']:
            for cell in ws[col_letter]:
                cell.number_format = currency_format

        response = HttpResponse(content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        filename = f"Fee_Projection_{timezone.now().strftime('%Y%m%d')}.xlsx"
        response['Content-Disposition'] = f'attachment; filename="{filename}"'
        wb.save(response)
        return response


class FeeProjectionExportPDFView(FeeProjectionReportView):
    """Handles PDF export for the Fee Projection Report."""
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        report_data = self._get_fee_projection_data(request)
        
        pdf_context = report_data.copy()
        pdf_context['report_title'] = "Fee Projection Report"
        if report_data.get('current_academic_year'):
            pdf_context['report_subtitle'] = f"For Academic Year: {report_data['current_academic_year'].name}"
            
        pdf_context.update({
            'school_profile': SchoolProfile.objects.first(),
            'tenant': getattr(request, 'tenant', None),
            'current_datetime': timezone.now()
        })
        
        try:
            pdf = render_to_pdf('reporting/pdf/fee_projection_report_pdf.html', pdf_context)
            if pdf:
                response = HttpResponse(pdf, content_type='application/pdf')
                filename = f"Fee_Projection_{timezone.now().strftime('%Y%m%d')}.pdf"
                response['Content-Disposition'] = f'inline; filename="{filename}"'
                return response
        except Exception as e:
            logger.error(f"Error generating Fee Projection PDF: {e}", exc_info=True)
            messages.error(request, "An error occurred while generating the PDF report.")
            
        return redirect('reporting:fee_projection_report')


# ========================================
# GENERAL LEDGER REPORT
# ========================================
class GeneralLedgerReportView(TenantPermissionRequiredMixin, BaseReportViewMixin, TemplateView):
    """
    Handles the display of the HTML General Ledger report page.
    """
    template_name = 'reporting/general_ledger_report.html'

    # Attributes for BaseReportViewMixin
    permission_required = 'reporting.view_general_ledger_report'
    report_title = _("General Ledger Report")
    filter_form_class = GeneralLedgerFilterForm
    export_csv_url_name = 'reporting:general_ledger_export_csv'
    export_pdf_url_name = 'reporting:general_ledger_export_pdf'
    export_excel_url_name = 'reporting:general_ledger_export_excel'
    paginate_by = 50

    def _get_general_ledger_data(self, request):
        """
        The core logic for fetching and processing all data for the General Ledger Report.
        """
        from apps.accounting.models import JournalLine, JournalEntry
        from decimal import Decimal

        # 1. Initialize the data structure
        data = {
            'journal_lines': [], 'accounts_summary': [], 'total_debits': Decimal('0.00'),
            'total_credits': Decimal('0.00'), 'start_date': None, 'end_date': None,
            'filter_form': self.filter_form_class(request.GET or None),
            'selected_account': None, 'selected_account_type': None
        }

        # 2. Set default date range if form is not submitted
        if not request.GET:
            today = timezone.now().date()
            start_date = today.replace(day=1)  # Default to current month start
            data['filter_form'] = self.filter_form_class(initial={'start_date': start_date, 'end_date': today})

        # 3. Process form data
        filter_form = data['filter_form']
        if filter_form.is_valid():
            start_date = filter_form.cleaned_data.get('start_date')
            end_date = filter_form.cleaned_data.get('end_date')
            selected_account = filter_form.cleaned_data.get('account')
            selected_account_type = filter_form.cleaned_data.get('account_type')
            entry_status = filter_form.cleaned_data.get('entry_status')
            include_zero_balance = filter_form.cleaned_data.get('include_zero_balance', True)

            # Set defaults if not provided
            if not start_date:
                try:
                    start_date = get_financial_year_start(end_date or timezone.now().date())
                except:
                    start_date = timezone.now().date().replace(month=1, day=1)

            if not end_date:
                end_date = timezone.now().date()

            data.update({
                'start_date': start_date, 'end_date': end_date,
                'selected_account': selected_account, 'selected_account_type': selected_account_type
            })

            # 4. Build the queryset for journal lines
            lines_queryset = JournalLine.objects.select_related(
                'journal_entry', 'account', 'account__account_type'
            ).filter(
                journal_entry__date__gte=start_date,
                journal_entry__date__lte=end_date
            )

            # Apply entry status filter
            if entry_status:
                lines_queryset = lines_queryset.filter(journal_entry__status=entry_status)
            else:
                # Default to posted entries only
                lines_queryset = lines_queryset.filter(journal_entry__status=JournalEntry.StatusChoices.POSTED)

            # Apply account filter
            if selected_account:
                lines_queryset = lines_queryset.filter(account=selected_account)
            elif selected_account_type:
                lines_queryset = lines_queryset.filter(account__account_type=selected_account_type)

            # Order by date and entry number
            lines_queryset = lines_queryset.order_by('journal_entry__date', 'journal_entry__entry_number', 'id')

            # 5. Calculate totals
            totals = lines_queryset.aggregate(
                total_debits=Coalesce(Sum('debit_amount'), Decimal('0.00')),
                total_credits=Coalesce(Sum('credit_amount'), Decimal('0.00'))
            )

            data.update({
                'journal_lines': lines_queryset,
                'total_debits': totals['total_debits'],
                'total_credits': totals['total_credits']
            })

            # 6. Generate accounts summary if no specific account is selected
            if not selected_account:
                accounts_summary = []
                account_filter = Account.objects.filter(is_active=True)

                if selected_account_type:
                    account_filter = account_filter.filter(account_type=selected_account_type)

                for account in account_filter.select_related('account_type'):
                    account_lines = lines_queryset.filter(account=account)
                    account_totals = account_lines.aggregate(
                        debits=Coalesce(Sum('debit_amount'), Decimal('0.00')),
                        credits=Coalesce(Sum('credit_amount'), Decimal('0.00'))
                    )

                    net_balance = account_totals['debits'] - account_totals['credits']

                    # Include account if it has activity or if including zero balance accounts
                    if include_zero_balance or net_balance != Decimal('0.00') or account_totals['debits'] > Decimal('0.00') or account_totals['credits'] > Decimal('0.00'):
                        accounts_summary.append({
                            'account': account,
                            'debits': account_totals['debits'],
                            'credits': account_totals['credits'],
                            'net_balance': net_balance,
                            'line_count': account_lines.count()
                        })

                data['accounts_summary'] = accounts_summary

        return data

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        report_data = self._get_general_ledger_data(self.request)
        context.update(report_data)
        return context


class GeneralLedgerExportCSVView(GeneralLedgerReportView):
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        import csv
        from django.http import HttpResponse

        report_data = self._get_general_ledger_data(request)
        journal_lines = report_data['journal_lines']

        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename="general_ledger_report.csv"'

        writer = csv.writer(response)
        writer.writerow([self.report_title])
        writer.writerow([f"Period: {report_data['start_date']} to {report_data['end_date']}"])
        writer.writerow([])
        writer.writerow(['Total Debits', report_data['total_debits']])
        writer.writerow(['Total Credits', report_data['total_credits']])
        writer.writerow([])

        writer.writerow(['Date', 'Entry Number', 'Account', 'Account Type', 'Description', 'Debit', 'Credit', 'Status'])

        for line in journal_lines:
            writer.writerow([
                line.journal_entry.date.strftime('%Y-%m-%d'),
                line.journal_entry.entry_number or f'JE-{line.journal_entry.id}',
                line.account.name,
                line.account.account_type.name,
                line.description or line.journal_entry.narration,
                line.debit_amount if line.debit_amount > 0 else '',
                line.credit_amount if line.credit_amount > 0 else '',
                line.journal_entry.get_status_display()
            ])
        return response


class GeneralLedgerExportPDFView(GeneralLedgerReportView):
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        report_data = self._get_general_ledger_data(request)

        report_data['school_profile'] = SchoolProfile.objects.first()
        report_data['tenant'] = request.tenant
        report_data['report_title'] = self.report_title

        pdf = render_to_pdf('reporting/pdf/general_ledger_pdf.html', report_data)
        if not pdf:
            messages.error(request, "There was an error generating the PDF report.")
            return redirect('reporting:general_ledger_report')

        response = HttpResponse(pdf, content_type='application/pdf')
        response['Content-Disposition'] = 'inline; filename="general_ledger_report.pdf"'
        return response


class GeneralLedgerExportExcelView(GeneralLedgerReportView):
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        try:
            import openpyxl
            from openpyxl.styles import Font
        except ImportError:
            messages.error(request, "The library required for Excel export (openpyxl) is not installed.")
            return redirect('reporting:general_ledger_report')

        report_data = self._get_general_ledger_data(request)
        journal_lines = report_data['journal_lines']

        # Create workbook and worksheet
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "General Ledger"

        # Add headers
        ws['A1'] = self.report_title
        ws['A1'].font = Font(bold=True, size=14)
        ws['A2'] = f"Period: {report_data['start_date']} to {report_data['end_date']}"

        # Add summary
        ws['A4'] = 'Total Debits:'
        ws['B4'] = float(report_data['total_debits'])
        ws['A5'] = 'Total Credits:'
        ws['B5'] = float(report_data['total_credits'])

        # Add column headers
        headers = ['Date', 'Entry Number', 'Account', 'Account Type', 'Description', 'Debit', 'Credit', 'Status']
        for col, header in enumerate(headers, 1):
            ws.cell(row=7, column=col, value=header).font = Font(bold=True)

        # Add data
        for row, line in enumerate(journal_lines, 8):
            ws.cell(row=row, column=1, value=line.journal_entry.date.strftime('%Y-%m-%d'))
            ws.cell(row=row, column=2, value=line.journal_entry.entry_number or f'JE-{line.journal_entry.id}')
            ws.cell(row=row, column=3, value=line.account.name)
            ws.cell(row=row, column=4, value=line.account.account_type.name)
            ws.cell(row=row, column=5, value=line.description or line.journal_entry.narration)
            ws.cell(row=row, column=6, value=float(line.debit_amount) if line.debit_amount > 0 else '')
            ws.cell(row=row, column=7, value=float(line.credit_amount) if line.credit_amount > 0 else '')
            ws.cell(row=row, column=8, value=line.journal_entry.get_status_display())

        # Save to response
        response = HttpResponse(content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        response['Content-Disposition'] = 'attachment; filename="general_ledger_report.xlsx"'
        wb.save(response)
        return response


# ========================================
# ACCOUNTS RECEIVABLE AGING REPORT
# ========================================
class AccountsReceivableAgingReportView(TenantPermissionRequiredMixin, BaseReportViewMixin, TemplateView):
    """
    Handles the display of the HTML Accounts Receivable Aging report page.
    """
    template_name = 'reporting/accounts_receivable_aging_report.html'

    # Attributes for BaseReportViewMixin
    permission_required = 'reporting.view_accounts_receivable_aging_report'
    report_title = _("Accounts Receivable Aging Report")
    filter_form_class = AccountsReceivableAgingFilterForm
    export_csv_url_name = 'reporting:accounts_receivable_aging_export_csv'
    export_pdf_url_name = 'reporting:accounts_receivable_aging_export_pdf'
    export_excel_url_name = 'reporting:accounts_receivable_aging_export_excel'
    paginate_by = 50

    def _get_accounts_receivable_aging_data(self, request):
        """
        The core logic for fetching and processing all data for the Accounts Receivable Aging Report.
        """
        from apps.fees.models import Invoice
        from apps.students.models import Student
        from decimal import Decimal
        from datetime import timedelta

        # 1. Initialize the data structure
        data = {
            'aging_data': [], 'summary_totals': {}, 'as_of_date': None,
            'filter_form': self.filter_form_class(request.GET or None),
            'aging_periods': [], 'total_outstanding': Decimal('0.00')
        }

        # 2. Set default date if form is not submitted
        if not request.GET:
            today = timezone.now().date()
            data['filter_form'] = self.filter_form_class(initial={'as_of_date': today})

        # 3. Process form data
        filter_form = data['filter_form']
        if filter_form.is_valid():
            as_of_date = filter_form.cleaned_data.get('as_of_date') or timezone.now().date()
            class_filter = filter_form.cleaned_data.get('class_filter')
            academic_year = filter_form.cleaned_data.get('academic_year')
            include_zero_balance = filter_form.cleaned_data.get('include_zero_balance', False)
            aging_periods_type = filter_form.cleaned_data.get('aging_periods', '30_60_90')

            data['as_of_date'] = as_of_date

            # 4. Define aging periods
            if aging_periods_type == '30_60_90_120':
                aging_periods = [
                    {'name': 'Current', 'days': 0, 'max_days': 30},
                    {'name': '31-60 Days', 'days': 31, 'max_days': 60},
                    {'name': '61-90 Days', 'days': 61, 'max_days': 90},
                    {'name': '91-120 Days', 'days': 91, 'max_days': 120},
                    {'name': 'Over 120 Days', 'days': 121, 'max_days': None},
                ]
            else:  # Default 30_60_90
                aging_periods = [
                    {'name': 'Current', 'days': 0, 'max_days': 30},
                    {'name': '31-60 Days', 'days': 31, 'max_days': 60},
                    {'name': '61-90 Days', 'days': 61, 'max_days': 90},
                    {'name': 'Over 90 Days', 'days': 91, 'max_days': None},
                ]

            data['aging_periods'] = aging_periods

            # 5. Get all students with outstanding invoices
            students_queryset = Student.objects.all()

            if class_filter:
                students_queryset = students_queryset.filter(current_class=class_filter)

            if academic_year:
                # Filter invoices by academic year, not students directly
                pass  # We'll handle this in the invoice filtering

            aging_data = []
            summary_totals = {period['name']: Decimal('0.00') for period in aging_periods}
            summary_totals['total'] = Decimal('0.00')

            for student in students_queryset.select_related('current_class'):
                # Get outstanding invoices for this student
                invoices_queryset = Invoice.objects.filter(
                    student=student,
                    status__in=['PENDING', 'PARTIALLY_PAID'],
                    due_date__lte=as_of_date
                ).select_related('academic_year', 'term')

                if academic_year:
                    invoices_queryset = invoices_queryset.filter(academic_year=academic_year)

                student_aging = {
                    'student': student,
                    'total_outstanding': Decimal('0.00'),
                    'periods': {period['name']: Decimal('0.00') for period in aging_periods}
                }

                for invoice in invoices_queryset:
                    outstanding_amount = invoice.get_outstanding_amount()
                    if outstanding_amount <= 0:
                        continue

                    # Calculate days overdue
                    days_overdue = (as_of_date - invoice.due_date).days

                    # Determine which aging period this invoice falls into
                    period_assigned = False
                    for period in aging_periods:
                        if period['max_days'] is None:
                            # This is the "over X days" period
                            if days_overdue >= period['days']:
                                student_aging['periods'][period['name']] += outstanding_amount
                                summary_totals[period['name']] += outstanding_amount
                                period_assigned = True
                                break
                        else:
                            # This is a specific range period
                            if period['days'] <= days_overdue <= period['max_days']:
                                student_aging['periods'][period['name']] += outstanding_amount
                                summary_totals[period['name']] += outstanding_amount
                                period_assigned = True
                                break

                    if not period_assigned and days_overdue < 0:
                        # Future due date - put in current
                        student_aging['periods']['Current'] += outstanding_amount
                        summary_totals['Current'] += outstanding_amount

                    student_aging['total_outstanding'] += outstanding_amount

                # Include student if they have outstanding balance or if including zero balance
                if include_zero_balance or student_aging['total_outstanding'] > 0:
                    aging_data.append(student_aging)
                    summary_totals['total'] += student_aging['total_outstanding']

            # Sort by total outstanding amount (highest first)
            aging_data.sort(key=lambda x: x['total_outstanding'], reverse=True)

            data.update({
                'aging_data': aging_data,
                'summary_totals': summary_totals,
                'total_outstanding': summary_totals['total']
            })

        return data

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        report_data = self._get_accounts_receivable_aging_data(self.request)
        context.update(report_data)
        return context


class AccountsReceivableAgingExportCSVView(AccountsReceivableAgingReportView):
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        import csv
        from django.http import HttpResponse

        report_data = self._get_accounts_receivable_aging_data(request)
        aging_data = report_data['aging_data']
        aging_periods = report_data['aging_periods']

        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename="accounts_receivable_aging_report.csv"'

        writer = csv.writer(response)
        writer.writerow([self.report_title])
        writer.writerow([f"As of Date: {report_data['as_of_date']}"])
        writer.writerow([])

        # Summary totals
        writer.writerow(['Summary Totals'])
        for period in aging_periods:
            writer.writerow([period['name'], report_data['summary_totals'][period['name']]])
        writer.writerow(['Total Outstanding', report_data['total_outstanding']])
        writer.writerow([])

        # Headers
        headers = ['Student Name', 'Admission No', 'Class', 'Total Outstanding']
        headers.extend([period['name'] for period in aging_periods])
        writer.writerow(headers)

        for student_data in aging_data:
            row = [
                student_data['student'].get_full_name(),
                student_data['student'].admission_number,
                student_data['student'].current_class.name if student_data['student'].current_class else 'N/A',
                student_data['total_outstanding']
            ]
            row.extend([student_data['periods'][period['name']] for period in aging_periods])
            writer.writerow(row)

        return response


class AccountsReceivableAgingExportPDFView(AccountsReceivableAgingReportView):
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        report_data = self._get_accounts_receivable_aging_data(request)

        report_data['school_profile'] = SchoolProfile.objects.first()
        report_data['tenant'] = request.tenant
        report_data['report_title'] = self.report_title

        pdf = render_to_pdf('reporting/pdf/accounts_receivable_aging_pdf.html', report_data)
        if not pdf:
            messages.error(request, "There was an error generating the PDF report.")
            return redirect('reporting:accounts_receivable_aging_report')

        response = HttpResponse(pdf, content_type='application/pdf')
        response['Content-Disposition'] = 'inline; filename="accounts_receivable_aging_report.pdf"'
        return response


class AccountsReceivableAgingExportExcelView(AccountsReceivableAgingReportView):
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        try:
            import openpyxl
            from openpyxl.styles import Font, PatternFill
        except ImportError:
            messages.error(request, "The library required for Excel export (openpyxl) is not installed.")
            return redirect('reporting:accounts_receivable_aging_report')

        report_data = self._get_accounts_receivable_aging_data(request)
        aging_data = report_data['aging_data']
        aging_periods = report_data['aging_periods']

        # Create workbook and worksheet
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "AR Aging"

        # Add headers
        ws['A1'] = self.report_title
        ws['A1'].font = Font(bold=True, size=14)
        ws['A2'] = f"As of Date: {report_data['as_of_date']}"

        # Add summary
        row = 4
        ws[f'A{row}'] = 'Summary Totals'
        ws[f'A{row}'].font = Font(bold=True)
        row += 1

        for period in aging_periods:
            ws[f'A{row}'] = period['name']
            ws[f'B{row}'] = float(report_data['summary_totals'][period['name']])
            row += 1

        ws[f'A{row}'] = 'Total Outstanding'
        ws[f'A{row}'].font = Font(bold=True)
        ws[f'B{row}'] = float(report_data['total_outstanding'])
        ws[f'B{row}'].font = Font(bold=True)

        # Add column headers
        row += 3
        headers = ['Student Name', 'Admission No', 'Class', 'Total Outstanding']
        headers.extend([period['name'] for period in aging_periods])

        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=row, column=col, value=header)
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")

        # Add data
        for student_data in aging_data:
            row += 1
            ws.cell(row=row, column=1, value=student_data['student'].get_full_name())
            ws.cell(row=row, column=2, value=student_data['student'].admission_number)
            ws.cell(row=row, column=3, value=student_data['student'].current_class.name if student_data['student'].current_class else 'N/A')
            ws.cell(row=row, column=4, value=float(student_data['total_outstanding']))

            for col, period in enumerate(aging_periods, 5):
                ws.cell(row=row, column=col, value=float(student_data['periods'][period['name']]))

        # Save to response
        response = HttpResponse(content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        response['Content-Disposition'] = 'attachment; filename="accounts_receivable_aging_report.xlsx"'
        wb.save(response)
        return response


# ========================================
# ACCOUNTS PAYABLE REPORT
# ========================================
class AccountsPayableReportView(TenantPermissionRequiredMixin, BaseReportViewMixin, TemplateView):
    """
    Handles the display of the HTML Accounts Payable report page.
    Shows unpaid expenses and vendor balances.
    """
    template_name = 'reporting/accounts_payable_report.html'

    # Attributes for BaseReportViewMixin
    permission_required = 'reporting.view_accounts_payable_report'
    report_title = _("Accounts Payable Report")
    filter_form_class = AccountsPayableFilterForm
    export_csv_url_name = 'reporting:accounts_payable_export_csv'
    export_pdf_url_name = 'reporting:accounts_payable_export_pdf'
    export_excel_url_name = 'reporting:accounts_payable_export_excel'
    paginate_by = 50

    def _get_accounts_payable_data(self, request):
        """
        The core logic for fetching and processing all data for the Accounts Payable Report.
        """
        from apps.finance.models import Expense, Vendor
        from decimal import Decimal
        from datetime import timedelta

        # 1. Initialize the data structure
        data = {
            'payable_data': [], 'vendor_summary': [], 'total_payable': Decimal('0.00'),
            'filter_form': self.filter_form_class(request.GET or None),
            'as_of_date': None, 'overdue_total': Decimal('0.00')
        }

        # 2. Set default date if form is not submitted
        if not request.GET:
            today = timezone.now().date()
            data['filter_form'] = self.filter_form_class(initial={'as_of_date': today})

        # 3. Process form data
        filter_form = data['filter_form']
        if filter_form.is_valid():
            as_of_date = filter_form.cleaned_data.get('as_of_date') or timezone.now().date()
            vendor_filter = filter_form.cleaned_data.get('vendor')
            due_date_from = filter_form.cleaned_data.get('due_date_from')
            due_date_to = filter_form.cleaned_data.get('due_date_to')
            overdue_only = filter_form.cleaned_data.get('overdue_only', False)

            data['as_of_date'] = as_of_date

            # 4. Get unpaid expenses (those without payment method indicating they're on account)
            # Note: In a more sophisticated system, you'd have a separate Bill/Invoice model
            # For now, we'll use expenses without payment_method as "unpaid bills"
            expenses_queryset = Expense.objects.filter(
                payment_method__isnull=True,  # Unpaid expenses
                expense_date__lte=as_of_date
            ).select_related('vendor', 'category', 'recorded_by')

            # Apply vendor filter
            if vendor_filter:
                expenses_queryset = expenses_queryset.filter(
                    vendor__name__icontains=vendor_filter
                )

            # For due date filtering, we'll assume expense_date + 30 days = due date
            # In a real system, you'd have a separate due_date field
            if due_date_from:
                # Calculate expense_date that would result in due_date >= due_date_from
                expense_date_from = due_date_from - timedelta(days=30)
                expenses_queryset = expenses_queryset.filter(expense_date__gte=expense_date_from)

            if due_date_to:
                # Calculate expense_date that would result in due_date <= due_date_to
                expense_date_to = due_date_to - timedelta(days=30)
                expenses_queryset = expenses_queryset.filter(expense_date__lte=expense_date_to)

            payable_data = []
            vendor_totals = {}
            total_payable = Decimal('0.00')
            overdue_total = Decimal('0.00')

            for expense in expenses_queryset.order_by('vendor__name', 'expense_date'):
                # Calculate assumed due date (expense_date + 30 days)
                assumed_due_date = expense.expense_date + timedelta(days=30)
                days_overdue = (as_of_date - assumed_due_date).days
                is_overdue = days_overdue > 0

                # Apply overdue filter
                if overdue_only and not is_overdue:
                    continue

                payable_item = {
                    'expense': expense,
                    'vendor_name': expense.vendor.name if expense.vendor else 'Unknown Vendor',
                    'due_date': assumed_due_date,
                    'days_overdue': days_overdue if is_overdue else 0,
                    'is_overdue': is_overdue,
                    'amount': expense.amount
                }

                payable_data.append(payable_item)
                total_payable += expense.amount

                if is_overdue:
                    overdue_total += expense.amount

                # Track vendor totals
                vendor_key = expense.vendor.name if expense.vendor else 'Unknown Vendor'
                if vendor_key not in vendor_totals:
                    vendor_totals[vendor_key] = {
                        'vendor': expense.vendor,
                        'total_amount': Decimal('0.00'),
                        'overdue_amount': Decimal('0.00'),
                        'current_amount': Decimal('0.00'),
                        'invoice_count': 0
                    }

                vendor_totals[vendor_key]['total_amount'] += expense.amount
                vendor_totals[vendor_key]['invoice_count'] += 1

                if is_overdue:
                    vendor_totals[vendor_key]['overdue_amount'] += expense.amount
                else:
                    vendor_totals[vendor_key]['current_amount'] += expense.amount

            # Convert vendor_totals to list and sort by total amount
            vendor_summary = list(vendor_totals.values())
            vendor_summary.sort(key=lambda x: x['total_amount'], reverse=True)

            data.update({
                'payable_data': payable_data,
                'vendor_summary': vendor_summary,
                'total_payable': total_payable,
                'overdue_total': overdue_total
            })

        return data

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        report_data = self._get_accounts_payable_data(self.request)
        context.update(report_data)
        return context


class AccountsPayableExportCSVView(AccountsPayableReportView):
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        import csv
        from django.http import HttpResponse

        report_data = self._get_accounts_payable_data(request)
        payable_data = report_data['payable_data']

        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename="accounts_payable_report.csv"'

        writer = csv.writer(response)
        writer.writerow([self.report_title])
        writer.writerow([f"As of Date: {report_data['as_of_date']}"])
        writer.writerow([])
        writer.writerow(['Total Payable', report_data['total_payable']])
        writer.writerow(['Overdue Amount', report_data['overdue_total']])
        writer.writerow([])

        writer.writerow(['Vendor', 'Expense Date', 'Due Date', 'Days Overdue', 'Amount', 'Category', 'Reference', 'Description'])

        for item in payable_data:
            writer.writerow([
                item['vendor_name'],
                item['expense'].expense_date.strftime('%Y-%m-%d'),
                item['due_date'].strftime('%Y-%m-%d'),
                item['days_overdue'] if item['is_overdue'] else 0,
                item['amount'],
                item['expense'].category.name,
                item['expense'].reference_number or '',
                item['expense'].description[:100]
            ])
        return response


class AccountsPayableExportPDFView(AccountsPayableReportView):
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        report_data = self._get_accounts_payable_data(request)

        report_data['school_profile'] = SchoolProfile.objects.first()
        report_data['tenant'] = request.tenant
        report_data['report_title'] = self.report_title

        pdf = render_to_pdf('reporting/pdf/accounts_payable_pdf.html', report_data)
        if not pdf:
            messages.error(request, "There was an error generating the PDF report.")
            return redirect('reporting:accounts_payable_report')

        response = HttpResponse(pdf, content_type='application/pdf')
        response['Content-Disposition'] = 'inline; filename="accounts_payable_report.pdf"'
        return response


class AccountsPayableExportExcelView(AccountsPayableReportView):
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        try:
            import openpyxl
            from openpyxl.styles import Font, PatternFill
        except ImportError:
            messages.error(request, "The library required for Excel export (openpyxl) is not installed.")
            return redirect('reporting:accounts_payable_report')

        report_data = self._get_accounts_payable_data(request)
        payable_data = report_data['payable_data']

        # Create workbook and worksheet
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "Accounts Payable"

        # Add headers
        ws['A1'] = self.report_title
        ws['A1'].font = Font(bold=True, size=14)
        ws['A2'] = f"As of Date: {report_data['as_of_date']}"

        # Add summary
        ws['A4'] = 'Total Payable:'
        ws['B4'] = float(report_data['total_payable'])
        ws['A5'] = 'Overdue Amount:'
        ws['B5'] = float(report_data['overdue_total'])

        # Add column headers
        headers = ['Vendor', 'Expense Date', 'Due Date', 'Days Overdue', 'Amount', 'Category', 'Reference', 'Description']
        for col, header in enumerate(headers, 1):
            ws.cell(row=7, column=col, value=header).font = Font(bold=True)

        # Add data
        for row, item in enumerate(payable_data, 8):
            ws.cell(row=row, column=1, value=item['vendor_name'])
            ws.cell(row=row, column=2, value=item['expense'].expense_date.strftime('%Y-%m-%d'))
            ws.cell(row=row, column=3, value=item['due_date'].strftime('%Y-%m-%d'))
            ws.cell(row=row, column=4, value=item['days_overdue'] if item['is_overdue'] else 0)
            ws.cell(row=row, column=5, value=float(item['amount']))
            ws.cell(row=row, column=6, value=item['expense'].category.name)
            ws.cell(row=row, column=7, value=item['expense'].reference_number or '')
            ws.cell(row=row, column=8, value=item['expense'].description[:100])

        # Save to response
        response = HttpResponse(content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        response['Content-Disposition'] = 'attachment; filename="accounts_payable_report.xlsx"'
        wb.save(response)
        return response


# ========================================
# BANK RECONCILIATION REPORT
# ========================================
class BankReconciliationReportView(TenantPermissionRequiredMixin, BaseReportViewMixin, TemplateView):
    """
    Handles the display of the HTML Bank Reconciliation report page.
    """
    template_name = 'reporting/bank_reconciliation_report.html'

    # Attributes for BaseReportViewMixin
    permission_required = 'reporting.view_bank_reconciliation_report'
    report_title = _("Bank Reconciliation Report")
    filter_form_class = BankReconciliationFilterForm
    export_csv_url_name = 'reporting:bank_reconciliation_export_csv'
    export_pdf_url_name = 'reporting:bank_reconciliation_export_pdf'
    export_excel_url_name = 'reporting:bank_reconciliation_export_excel'
    paginate_by = 50

    def _get_bank_reconciliation_data(self, request):
        """
        The core logic for fetching and processing all data for the Bank Reconciliation Report.
        """
        from apps.accounting.models import JournalLine, JournalEntry, Account
        from decimal import Decimal
        from datetime import timedelta

        # 1. Initialize the data structure
        data = {
            'reconciliation_items': [], 'book_balance': Decimal('0.00'), 'statement_balance': Decimal('0.00'),
            'filter_form': self.filter_form_class(request.GET or None),
            'statement_date': None, 'reconciliation_date': None, 'bank_account': None,
            'outstanding_deposits': [], 'outstanding_checks': [], 'adjustments': [],
            'reconciled_balance': Decimal('0.00'), 'difference': Decimal('0.00')
        }

        # 2. Process form data
        filter_form = data['filter_form']
        if filter_form.is_valid():
            bank_account = filter_form.cleaned_data.get('bank_account')
            statement_date = filter_form.cleaned_data.get('statement_date')
            statement_balance = filter_form.cleaned_data.get('statement_balance') or Decimal('0.00')
            reconciliation_date = filter_form.cleaned_data.get('reconciliation_date') or timezone.now().date()

            if not bank_account or not statement_date:
                return data

            data.update({
                'bank_account': bank_account,
                'statement_date': statement_date,
                'statement_balance': statement_balance,
                'reconciliation_date': reconciliation_date
            })

            # 3. Calculate book balance (account balance as of statement date)
            book_balance_lines = JournalLine.objects.filter(
                account=bank_account,
                journal_entry__date__lte=statement_date,
                journal_entry__status=JournalEntry.StatusChoices.POSTED
            ).aggregate(
                total_debits=Coalesce(Sum('debit_amount'), Decimal('0.00')),
                total_credits=Coalesce(Sum('credit_amount'), Decimal('0.00'))
            )

            # For bank accounts (assets), debits increase balance, credits decrease
            book_balance = book_balance_lines['total_debits'] - book_balance_lines['total_credits']
            data['book_balance'] = book_balance

            # 4. Get all bank transactions for the period
            # Look at transactions around the statement date (e.g., 30 days before and after)
            start_date = statement_date - timedelta(days=30)
            end_date = statement_date + timedelta(days=30)

            bank_transactions = JournalLine.objects.filter(
                account=bank_account,
                journal_entry__date__gte=start_date,
                journal_entry__date__lte=end_date,
                journal_entry__status=JournalEntry.StatusChoices.POSTED
            ).select_related('journal_entry').order_by('journal_entry__date', 'journal_entry__entry_number')

            # 5. Categorize transactions for reconciliation
            outstanding_deposits = []  # Deposits in books but not on statement
            outstanding_checks = []   # Checks/payments in books but not cleared
            reconciliation_items = []

            for line in bank_transactions:
                # Determine transaction type
                if line.debit_amount > 0:
                    transaction_type = 'Deposit'
                    amount = line.debit_amount
                else:
                    transaction_type = 'Check/Payment'
                    amount = line.credit_amount

                # For this simplified version, we'll assume:
                # - Transactions on or before statement date are cleared
                # - Transactions after statement date are outstanding
                is_cleared = line.journal_entry.date <= statement_date

                transaction_item = {
                    'journal_line': line,
                    'date': line.journal_entry.date,
                    'description': line.description or line.journal_entry.narration,
                    'type': transaction_type,
                    'amount': amount,
                    'is_cleared': is_cleared,
                    'entry_number': line.journal_entry.entry_number or f'JE-{line.journal_entry.id}'
                }

                reconciliation_items.append(transaction_item)

                # Categorize outstanding items
                if not is_cleared:
                    if transaction_type == 'Deposit':
                        outstanding_deposits.append(transaction_item)
                    else:
                        outstanding_checks.append(transaction_item)

            # 6. Calculate reconciled balance
            # Reconciled Balance = Statement Balance + Outstanding Deposits - Outstanding Checks
            outstanding_deposits_total = sum(item['amount'] for item in outstanding_deposits)
            outstanding_checks_total = sum(item['amount'] for item in outstanding_checks)

            reconciled_balance = statement_balance + outstanding_deposits_total - outstanding_checks_total
            difference = book_balance - reconciled_balance

            data.update({
                'reconciliation_items': reconciliation_items,
                'outstanding_deposits': outstanding_deposits,
                'outstanding_checks': outstanding_checks,
                'reconciled_balance': reconciled_balance,
                'difference': difference,
                'outstanding_deposits_total': outstanding_deposits_total,
                'outstanding_checks_total': outstanding_checks_total
            })

        return data

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        report_data = self._get_bank_reconciliation_data(self.request)
        context.update(report_data)
        return context


class BankReconciliationExportCSVView(BankReconciliationReportView):
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        import csv
        from django.http import HttpResponse

        report_data = self._get_bank_reconciliation_data(request)

        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename="bank_reconciliation_report.csv"'

        writer = csv.writer(response)
        writer.writerow([self.report_title])
        writer.writerow([f"Bank Account: {report_data.get('bank_account', 'N/A')}"])
        writer.writerow([f"Statement Date: {report_data.get('statement_date', 'N/A')}"])
        writer.writerow([])

        # Summary
        writer.writerow(['Bank Statement Balance', report_data.get('statement_balance', 0)])
        writer.writerow(['Book Balance', report_data.get('book_balance', 0)])
        writer.writerow(['Reconciled Balance', report_data.get('reconciled_balance', 0)])
        writer.writerow(['Difference', report_data.get('difference', 0)])
        writer.writerow([])

        # Outstanding items
        writer.writerow(['Outstanding Deposits'])
        writer.writerow(['Date', 'Description', 'Amount'])
        for item in report_data.get('outstanding_deposits', []):
            writer.writerow([item['date'], item['description'], item['amount']])

        writer.writerow([])
        writer.writerow(['Outstanding Checks/Payments'])
        writer.writerow(['Date', 'Description', 'Amount'])
        for item in report_data.get('outstanding_checks', []):
            writer.writerow([item['date'], item['description'], item['amount']])

        return response


class BankReconciliationExportPDFView(BankReconciliationReportView):
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        report_data = self._get_bank_reconciliation_data(request)

        report_data['school_profile'] = SchoolProfile.objects.first()
        report_data['tenant'] = request.tenant
        report_data['report_title'] = self.report_title

        pdf = render_to_pdf('reporting/pdf/bank_reconciliation_pdf.html', report_data)
        if not pdf:
            messages.error(request, "There was an error generating the PDF report.")
            return redirect('reporting:bank_reconciliation_report')

        response = HttpResponse(pdf, content_type='application/pdf')
        response['Content-Disposition'] = 'inline; filename="bank_reconciliation_report.pdf"'
        return response


class BankReconciliationExportExcelView(BankReconciliationReportView):
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        try:
            import openpyxl
            from openpyxl.styles import Font, PatternFill
        except ImportError:
            messages.error(request, "The library required for Excel export (openpyxl) is not installed.")
            return redirect('reporting:bank_reconciliation_report')

        report_data = self._get_bank_reconciliation_data(request)

        # Create workbook and worksheet
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "Bank Reconciliation"

        # Add headers
        ws['A1'] = self.report_title
        ws['A1'].font = Font(bold=True, size=14)
        ws['A2'] = f"Bank Account: {report_data.get('bank_account', 'N/A')}"
        ws['A3'] = f"Statement Date: {report_data.get('statement_date', 'N/A')}"

        # Add summary
        ws['A5'] = 'Bank Statement Balance:'
        ws['B5'] = float(report_data.get('statement_balance', 0))
        ws['A6'] = 'Book Balance:'
        ws['B6'] = float(report_data.get('book_balance', 0))
        ws['A7'] = 'Reconciled Balance:'
        ws['B7'] = float(report_data.get('reconciled_balance', 0))
        ws['A8'] = 'Difference:'
        ws['B8'] = float(report_data.get('difference', 0))

        # Outstanding deposits
        row = 10
        ws[f'A{row}'] = 'Outstanding Deposits'
        ws[f'A{row}'].font = Font(bold=True)
        row += 1

        headers = ['Date', 'Description', 'Amount']
        for col, header in enumerate(headers, 1):
            ws.cell(row=row, column=col, value=header).font = Font(bold=True)
        row += 1

        for item in report_data.get('outstanding_deposits', []):
            ws.cell(row=row, column=1, value=item['date'].strftime('%Y-%m-%d'))
            ws.cell(row=row, column=2, value=item['description'])
            ws.cell(row=row, column=3, value=float(item['amount']))
            row += 1

        # Outstanding checks
        row += 2
        ws[f'A{row}'] = 'Outstanding Checks/Payments'
        ws[f'A{row}'].font = Font(bold=True)
        row += 1

        for col, header in enumerate(headers, 1):
            ws.cell(row=row, column=col, value=header).font = Font(bold=True)
        row += 1

        for item in report_data.get('outstanding_checks', []):
            ws.cell(row=row, column=1, value=item['date'].strftime('%Y-%m-%d'))
            ws.cell(row=row, column=2, value=item['description'])
            ws.cell(row=row, column=3, value=float(item['amount']))
            row += 1

        # Save to response
        response = HttpResponse(content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        response['Content-Disposition'] = 'attachment; filename="bank_reconciliation_report.xlsx"'
        wb.save(response)
        return response


# ========================================
# BUDGET VS ACTUAL REPORT
# ========================================
class BudgetVsActualReportView(TenantPermissionRequiredMixin, BaseReportViewMixin, TemplateView):
    """
    Handles the display of the HTML Budget vs Actual report page.
    """
    template_name = 'reporting/budget_vs_actual_report.html'

    # Attributes for BaseReportViewMixin
    permission_required = 'reporting.view_budget_vs_actual_report'
    report_title = _("Budget vs Actual Report")
    filter_form_class = BudgetVsActualFilterForm
    export_csv_url_name = 'reporting:budget_vs_actual_export_csv'
    export_pdf_url_name = 'reporting:budget_vs_actual_export_pdf'
    export_excel_url_name = 'reporting:budget_vs_actual_export_excel'
    paginate_by = 50

    def _get_budget_vs_actual_data(self, request):
        """
        The core logic for fetching and processing all data for the Budget vs Actual Report.
        """
        from apps.finance.models import BudgetItem, BudgetAmount
        from apps.accounting.models import JournalLine, JournalEntry
        from decimal import Decimal

        # 1. Initialize the data structure
        data = {
            'budget_analysis': [], 'total_budgeted_income': Decimal('0.00'), 'total_actual_income': Decimal('0.00'),
            'total_budgeted_expense': Decimal('0.00'), 'total_actual_expense': Decimal('0.00'),
            'filter_form': self.filter_form_class(request.GET or None),
            'academic_year': None, 'term': None, 'variance_summary': {}
        }

        # 2. Process form data
        filter_form = data['filter_form']
        if filter_form.is_valid():
            academic_year = filter_form.cleaned_data.get('academic_year')
            term = filter_form.cleaned_data.get('term')
            budget_item_type = filter_form.cleaned_data.get('budget_item_type')
            variance_threshold = filter_form.cleaned_data.get('variance_threshold', 0)
            show_zero_budget = filter_form.cleaned_data.get('show_zero_budget', True)

            data.update({
                'academic_year': academic_year,
                'term': term
            })

            # 3. Get budget items
            budget_items_queryset = BudgetItem.objects.all().select_related('linked_coa_account')

            if budget_item_type:
                budget_items_queryset = budget_items_queryset.filter(budget_item_type=budget_item_type)

            budget_analysis = []
            total_budgeted_income = Decimal('0.00')
            total_actual_income = Decimal('0.00')
            total_budgeted_expense = Decimal('0.00')
            total_actual_expense = Decimal('0.00')

            for budget_item in budget_items_queryset:
                # 4. Get budgeted amounts
                budget_amounts_queryset = BudgetAmount.objects.filter(budget_item=budget_item)

                if academic_year:
                    budget_amounts_queryset = budget_amounts_queryset.filter(academic_year=academic_year)

                if term:
                    budget_amounts_queryset = budget_amounts_queryset.filter(term=term)

                budgeted_amount = budget_amounts_queryset.aggregate(
                    total=Coalesce(Sum('budgeted_amount'), Decimal('0.00'))
                )['total']

                # Skip zero budget items if not requested
                if not show_zero_budget and budgeted_amount == Decimal('0.00'):
                    continue

                # 5. Get actual amounts from journal entries
                actual_queryset = JournalLine.objects.filter(
                    account=budget_item.linked_coa_account,
                    journal_entry__status=JournalEntry.StatusChoices.POSTED
                )

                # Filter by date range based on academic year/term
                if academic_year:
                    actual_queryset = actual_queryset.filter(
                        journal_entry__date__gte=academic_year.start_date,
                        journal_entry__date__lte=academic_year.end_date
                    )

                if term:
                    actual_queryset = actual_queryset.filter(
                        journal_entry__date__gte=term.start_date,
                        journal_entry__date__lte=term.end_date
                    )

                # Calculate actual amount based on account type
                actual_totals = actual_queryset.aggregate(
                    total_debits=Coalesce(Sum('debit_amount'), Decimal('0.00')),
                    total_credits=Coalesce(Sum('credit_amount'), Decimal('0.00'))
                )

                # For income accounts, credits increase balance (normal balance is credit)
                # For expense accounts, debits increase balance (normal balance is debit)
                if budget_item.budget_item_type == 'INCOME':
                    actual_amount = actual_totals['total_credits'] - actual_totals['total_debits']
                else:  # EXPENSE
                    actual_amount = actual_totals['total_debits'] - actual_totals['total_credits']

                # 6. Calculate variance
                variance_amount = actual_amount - budgeted_amount
                variance_percentage = Decimal('0.00')

                if budgeted_amount != Decimal('0.00'):
                    variance_percentage = (variance_amount / budgeted_amount) * Decimal('100.00')

                # Apply variance threshold filter
                if variance_threshold > 0 and abs(variance_percentage) < variance_threshold:
                    continue

                budget_item_analysis = {
                    'budget_item': budget_item,
                    'budgeted_amount': budgeted_amount,
                    'actual_amount': actual_amount,
                    'variance_amount': variance_amount,
                    'variance_percentage': variance_percentage,
                    'is_favorable': (
                        (budget_item.budget_item_type == 'INCOME' and variance_amount > 0) or
                        (budget_item.budget_item_type == 'EXPENSE' and variance_amount < 0)
                    ),
                    'account_name': budget_item.linked_coa_account.name,
                    'account_code': budget_item.linked_coa_account.code
                }

                budget_analysis.append(budget_item_analysis)

                # Add to totals
                if budget_item.budget_item_type == 'INCOME':
                    total_budgeted_income += budgeted_amount
                    total_actual_income += actual_amount
                else:
                    total_budgeted_expense += budgeted_amount
                    total_actual_expense += actual_amount

            # 7. Calculate summary variances
            income_variance = total_actual_income - total_budgeted_income
            expense_variance = total_actual_expense - total_budgeted_expense
            net_budget = total_budgeted_income - total_budgeted_expense
            net_actual = total_actual_income - total_actual_expense
            net_variance = net_actual - net_budget

            variance_summary = {
                'income_variance': income_variance,
                'expense_variance': expense_variance,
                'net_variance': net_variance,
                'income_variance_pct': (income_variance / total_budgeted_income * 100) if total_budgeted_income > 0 else Decimal('0.00'),
                'expense_variance_pct': (expense_variance / total_budgeted_expense * 100) if total_budgeted_expense > 0 else Decimal('0.00'),
                'net_budget': net_budget,
                'net_actual': net_actual
            }

            # Sort by variance percentage (highest first)
            budget_analysis.sort(key=lambda x: abs(x['variance_percentage']), reverse=True)

            data.update({
                'budget_analysis': budget_analysis,
                'total_budgeted_income': total_budgeted_income,
                'total_actual_income': total_actual_income,
                'total_budgeted_expense': total_budgeted_expense,
                'total_actual_expense': total_actual_expense,
                'variance_summary': variance_summary
            })

        return data

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        report_data = self._get_budget_vs_actual_data(self.request)
        context.update(report_data)
        return context


class BudgetVsActualExportCSVView(BudgetVsActualReportView):
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        import csv
        from django.http import HttpResponse

        report_data = self._get_budget_vs_actual_data(request)
        budget_analysis = report_data['budget_analysis']

        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename="budget_vs_actual_report.csv"'

        writer = csv.writer(response)
        writer.writerow([self.report_title])

        if report_data['academic_year']:
            writer.writerow([f"Academic Year: {report_data['academic_year']}"])
        if report_data['term']:
            writer.writerow([f"Term: {report_data['term']}"])
        writer.writerow([])

        # Summary
        writer.writerow(['Summary'])
        writer.writerow(['Total Budgeted Income', report_data['total_budgeted_income']])
        writer.writerow(['Total Actual Income', report_data['total_actual_income']])
        writer.writerow(['Total Budgeted Expense', report_data['total_budgeted_expense']])
        writer.writerow(['Total Actual Expense', report_data['total_actual_expense']])
        writer.writerow([])

        # Details
        writer.writerow(['Budget Item', 'Type', 'Account', 'Budgeted Amount', 'Actual Amount', 'Variance Amount', 'Variance %', 'Status'])

        for item in budget_analysis:
            writer.writerow([
                item['budget_item'].name,
                item['budget_item'].get_budget_item_type_display(),
                item['account_name'],
                item['budgeted_amount'],
                item['actual_amount'],
                item['variance_amount'],
                f"{item['variance_percentage']:.2f}%",
                'Favorable' if item['is_favorable'] else 'Unfavorable'
            ])
        return response


class BudgetVsActualExportPDFView(BudgetVsActualReportView):
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        report_data = self._get_budget_vs_actual_data(request)

        report_data['school_profile'] = SchoolProfile.objects.first()
        report_data['tenant'] = request.tenant
        report_data['report_title'] = self.report_title

        pdf = render_to_pdf('reporting/pdf/budget_vs_actual_pdf.html', report_data)
        if not pdf:
            messages.error(request, "There was an error generating the PDF report.")
            return redirect('reporting:budget_vs_actual_report')

        response = HttpResponse(pdf, content_type='application/pdf')
        response['Content-Disposition'] = 'inline; filename="budget_vs_actual_report.pdf"'
        return response


class BudgetVsActualExportExcelView(BudgetVsActualReportView):
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        try:
            import openpyxl
            from openpyxl.styles import Font, PatternFill
        except ImportError:
            messages.error(request, "The library required for Excel export (openpyxl) is not installed.")
            return redirect('reporting:budget_vs_actual_report')

        report_data = self._get_budget_vs_actual_data(request)
        budget_analysis = report_data['budget_analysis']

        # Create workbook and worksheet
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "Budget vs Actual"

        # Add headers
        ws['A1'] = self.report_title
        ws['A1'].font = Font(bold=True, size=14)

        row = 2
        if report_data['academic_year']:
            ws[f'A{row}'] = f"Academic Year: {report_data['academic_year']}"
            row += 1
        if report_data['term']:
            ws[f'A{row}'] = f"Term: {report_data['term']}"
            row += 1

        # Add summary
        row += 1
        ws[f'A{row}'] = 'Summary'
        ws[f'A{row}'].font = Font(bold=True)
        row += 1

        summary_data = [
            ('Total Budgeted Income', float(report_data['total_budgeted_income'])),
            ('Total Actual Income', float(report_data['total_actual_income'])),
            ('Total Budgeted Expense', float(report_data['total_budgeted_expense'])),
            ('Total Actual Expense', float(report_data['total_actual_expense']))
        ]

        for label, value in summary_data:
            ws[f'A{row}'] = label
            ws[f'B{row}'] = value
            row += 1

        # Add column headers
        row += 2
        headers = ['Budget Item', 'Type', 'Account', 'Budgeted Amount', 'Actual Amount', 'Variance Amount', 'Variance %', 'Status']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=row, column=col, value=header)
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")

        # Add data
        for item in budget_analysis:
            row += 1
            ws.cell(row=row, column=1, value=item['budget_item'].name)
            ws.cell(row=row, column=2, value=item['budget_item'].get_budget_item_type_display())
            ws.cell(row=row, column=3, value=item['account_name'])
            ws.cell(row=row, column=4, value=float(item['budgeted_amount']))
            ws.cell(row=row, column=5, value=float(item['actual_amount']))
            ws.cell(row=row, column=6, value=float(item['variance_amount']))
            ws.cell(row=row, column=7, value=f"{float(item['variance_percentage']):.2f}%")
            ws.cell(row=row, column=8, value='Favorable' if item['is_favorable'] else 'Unfavorable')

        # Save to response
        response = HttpResponse(content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        response['Content-Disposition'] = 'attachment; filename="budget_vs_actual_report.xlsx"'
        wb.save(response)
        return response


# ========================================
# FEE COLLECTION ANALYSIS REPORT
# ========================================
class FeeCollectionAnalysisReportView(TenantPermissionRequiredMixin, BaseReportViewMixin, TemplateView):
    """
    Handles the display of the HTML Fee Collection Analysis report page.
    """
    template_name = 'reporting/fee_collection_analysis_report.html'

    # Attributes for BaseReportViewMixin
    permission_required = 'reporting.view_fee_collection_analysis_report'
    report_title = _("Fee Collection Analysis Report")
    filter_form_class = FeeCollectionAnalysisFilterForm
    export_csv_url_name = 'reporting:fee_collection_analysis_export_csv'
    export_pdf_url_name = 'reporting:fee_collection_analysis_export_pdf'
    export_excel_url_name = 'reporting:fee_collection_analysis_export_excel'
    paginate_by = 50

    def _get_fee_collection_analysis_data(self, request):
        """
        The core logic for fetching and processing all data for the Fee Collection Analysis Report.
        """
        from apps.payments.models import Payment
        from apps.fees.models import Invoice
        from decimal import Decimal
        from datetime import timedelta
        from django.db.models import Avg

        # 1. Initialize the data structure
        data = {
            'collection_summary': {}, 'payment_method_analysis': [], 'class_analysis': [],
            'trend_analysis': [], 'efficiency_metrics': {}, 'filter_form': self.filter_form_class(request.GET or None),
            'start_date': None, 'end_date': None, 'total_collections': Decimal('0.00')
        }

        # 2. Set default dates if form is not submitted
        if not request.GET:
            today = timezone.now().date()
            start_date = today.replace(day=1)  # Default to current month
            data['filter_form'] = self.filter_form_class(initial={'start_date': start_date, 'end_date': today})

        # 3. Process form data
        filter_form = data['filter_form']
        if filter_form.is_valid():
            start_date = filter_form.cleaned_data.get('start_date')
            end_date = filter_form.cleaned_data.get('end_date')
            academic_year = filter_form.cleaned_data.get('academic_year')
            payment_method = filter_form.cleaned_data.get('payment_method')
            class_filter = filter_form.cleaned_data.get('class_filter')
            analysis_type = filter_form.cleaned_data.get('analysis_type', 'summary')

            # Set defaults if not provided
            if not start_date:
                start_date = timezone.now().date().replace(day=1)
            if not end_date:
                end_date = timezone.now().date()

            data.update({
                'start_date': start_date,
                'end_date': end_date,
                'analysis_type': analysis_type
            })

            # 4. Build base payment queryset
            payments_queryset = Payment.objects.filter(
                payment_date__gte=start_date,
                payment_date__lte=end_date,
                status='COMPLETED'
            ).select_related('payment_method', 'invoice', 'invoice__student', 'invoice__student__current_class')

            # Apply filters
            if academic_year:
                payments_queryset = payments_queryset.filter(invoice__academic_year=academic_year)

            if payment_method:
                payments_queryset = payments_queryset.filter(payment_method=payment_method)

            if class_filter:
                payments_queryset = payments_queryset.filter(invoice__student__current_class=class_filter)

            # 5. Calculate collection summary
            total_collections = payments_queryset.aggregate(
                total=Coalesce(Sum('amount'), Decimal('0.00'))
            )['total']

            payment_count = payments_queryset.count()

            # Calculate average payment amount
            avg_payment = total_collections / payment_count if payment_count > 0 else Decimal('0.00')

            # Get total invoices issued in period for collection rate
            invoices_in_period = Invoice.objects.filter(
                issue_date__gte=start_date,
                issue_date__lte=end_date
            )

            if academic_year:
                invoices_in_period = invoices_in_period.filter(academic_year=academic_year)

            if class_filter:
                invoices_in_period = invoices_in_period.filter(student__current_class=class_filter)

            total_invoiced = invoices_in_period.aggregate(
                total=Coalesce(Sum('total_amount'), Decimal('0.00'))
            )['total']

            collection_rate = (total_collections / total_invoiced * 100) if total_invoiced > 0 else Decimal('0.00')

            collection_summary = {
                'total_collections': total_collections,
                'payment_count': payment_count,
                'avg_payment': avg_payment,
                'total_invoiced': total_invoiced,
                'collection_rate': collection_rate,
                'outstanding_amount': total_invoiced - total_collections
            }

            # 6. Payment method analysis
            payment_method_data = payments_queryset.values(
                'payment_method__name'
            ).annotate(
                total_amount=Sum('amount'),
                payment_count=Count('id'),
                avg_amount=Avg('amount')
            ).order_by('-total_amount')

            payment_method_analysis = []
            for method_data in payment_method_data:
                percentage = (method_data['total_amount'] / total_collections * 100) if total_collections > 0 else Decimal('0.00')
                payment_method_analysis.append({
                    'method_name': method_data['payment_method__name'],
                    'total_amount': method_data['total_amount'],
                    'payment_count': method_data['payment_count'],
                    'avg_amount': method_data['avg_amount'],
                    'percentage': percentage
                })

            # 7. Class-wise analysis
            class_data = payments_queryset.values(
                'invoice__student__current_class__name'
            ).annotate(
                total_amount=Sum('amount'),
                payment_count=Count('id'),
                student_count=Count('invoice__student', distinct=True)
            ).order_by('-total_amount')

            class_analysis = []
            for class_item in class_data:
                class_percentage = (class_item['total_amount'] / total_collections * 100) if total_collections > 0 else Decimal('0.00')
                avg_per_student = class_item['total_amount'] / class_item['student_count'] if class_item['student_count'] > 0 else Decimal('0.00')

                class_analysis.append({
                    'class_name': class_item['invoice__student__current_class__name'] or 'Unknown',
                    'total_amount': class_item['total_amount'],
                    'payment_count': class_item['payment_count'],
                    'student_count': class_item['student_count'],
                    'percentage': class_percentage,
                    'avg_per_student': avg_per_student
                })

            data.update({
                'collection_summary': collection_summary,
                'payment_method_analysis': payment_method_analysis,
                'class_analysis': class_analysis,
                'total_collections': total_collections
            })

        return data

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        report_data = self._get_fee_collection_analysis_data(self.request)
        context.update(report_data)
        return context


class FeeCollectionAnalysisExportCSVView(FeeCollectionAnalysisReportView):
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        import csv
        from django.http import HttpResponse

        report_data = self._get_fee_collection_analysis_data(request)

        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename="fee_collection_analysis_report.csv"'

        writer = csv.writer(response)
        writer.writerow([self.report_title])
        writer.writerow([f"Period: {report_data['start_date']} to {report_data['end_date']}"])
        writer.writerow([])

        # Collection Summary
        summary = report_data['collection_summary']
        writer.writerow(['Collection Summary'])
        writer.writerow(['Total Collections', summary.get('total_collections', 0)])
        writer.writerow(['Payment Count', summary.get('payment_count', 0)])
        writer.writerow(['Average Payment', summary.get('avg_payment', 0)])
        writer.writerow(['Collection Rate (%)', summary.get('collection_rate', 0)])
        writer.writerow([])

        # Payment Method Analysis
        writer.writerow(['Payment Method Analysis'])
        writer.writerow(['Method', 'Total Amount', 'Payment Count', 'Average Amount', 'Percentage'])
        for method in report_data['payment_method_analysis']:
            writer.writerow([
                method['method_name'],
                method['total_amount'],
                method['payment_count'],
                method['avg_amount'],
                f"{method['percentage']:.2f}%"
            ])

        writer.writerow([])

        # Class Analysis
        writer.writerow(['Class-wise Analysis'])
        writer.writerow(['Class', 'Total Amount', 'Payment Count', 'Student Count', 'Percentage', 'Avg per Student'])
        for class_item in report_data['class_analysis']:
            writer.writerow([
                class_item['class_name'],
                class_item['total_amount'],
                class_item['payment_count'],
                class_item['student_count'],
                f"{class_item['percentage']:.2f}%",
                class_item['avg_per_student']
            ])

        return response


class FeeCollectionAnalysisExportPDFView(FeeCollectionAnalysisReportView):
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        report_data = self._get_fee_collection_analysis_data(request)

        report_data['school_profile'] = SchoolProfile.objects.first()
        report_data['tenant'] = request.tenant
        report_data['report_title'] = self.report_title

        pdf = render_to_pdf('reporting/pdf/fee_collection_analysis_pdf.html', report_data)
        if not pdf:
            messages.error(request, "There was an error generating the PDF report.")
            return redirect('reporting:fee_collection_analysis_report')

        response = HttpResponse(pdf, content_type='application/pdf')
        response['Content-Disposition'] = 'inline; filename="fee_collection_analysis_report.pdf"'
        return response


class FeeCollectionAnalysisExportExcelView(FeeCollectionAnalysisReportView):
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        try:
            import openpyxl
            from openpyxl.styles import Font, PatternFill
        except ImportError:
            messages.error(request, "The library required for Excel export (openpyxl) is not installed.")
            return redirect('reporting:fee_collection_analysis_report')

        report_data = self._get_fee_collection_analysis_data(request)

        # Create workbook and worksheet
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "Collection Analysis"

        # Add headers
        ws['A1'] = self.report_title
        ws['A1'].font = Font(bold=True, size=14)
        ws['A2'] = f"Period: {report_data['start_date']} to {report_data['end_date']}"

        # Collection Summary
        row = 4
        ws[f'A{row}'] = 'Collection Summary'
        ws[f'A{row}'].font = Font(bold=True)
        row += 1

        summary = report_data['collection_summary']
        summary_data = [
            ('Total Collections', float(summary.get('total_collections', 0))),
            ('Payment Count', summary.get('payment_count', 0)),
            ('Average Payment', float(summary.get('avg_payment', 0))),
            ('Collection Rate (%)', float(summary.get('collection_rate', 0)))
        ]

        for label, value in summary_data:
            ws[f'A{row}'] = label
            ws[f'B{row}'] = value
            row += 1

        # Payment Method Analysis
        row += 2
        ws[f'A{row}'] = 'Payment Method Analysis'
        ws[f'A{row}'].font = Font(bold=True)
        row += 1

        headers = ['Method', 'Total Amount', 'Payment Count', 'Average Amount', 'Percentage']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=row, column=col, value=header)
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
        row += 1

        for method in report_data['payment_method_analysis']:
            ws.cell(row=row, column=1, value=method['method_name'])
            ws.cell(row=row, column=2, value=float(method['total_amount']))
            ws.cell(row=row, column=3, value=method['payment_count'])
            ws.cell(row=row, column=4, value=float(method['avg_amount']))
            ws.cell(row=row, column=5, value=f"{float(method['percentage']):.2f}%")
            row += 1

        # Save to response
        response = HttpResponse(content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        response['Content-Disposition'] = 'attachment; filename="fee_collection_analysis_report.xlsx"'
        wb.save(response)
        return response


# ========================================
# STUDENT ACCOUNT STATEMENT REPORT
# ========================================
class StudentAccountStatementReportView(TenantPermissionRequiredMixin, BaseReportViewMixin, TemplateView):
    """
    Handles the display of the HTML Student Account Statement report page.
    """
    template_name = 'reporting/student_account_statement_report.html'

    # Attributes for BaseReportViewMixin
    permission_required = 'reporting.view_student_account_statement_report'
    report_title = _("Student Account Statement")
    filter_form_class = StudentAccountStatementFilterForm
    export_csv_url_name = 'reporting:student_account_statement_export_csv'
    export_pdf_url_name = 'reporting:student_account_statement_export_pdf'
    export_excel_url_name = 'reporting:student_account_statement_export_excel'
    paginate_by = 50

    def _get_student_account_statement_data(self, request):
        """
        The core logic for fetching and processing all data for the Student Account Statement Report.
        """
        from apps.fees.models import Invoice
        from apps.payments.models import Payment
        from decimal import Decimal
        from datetime import datetime

        # 1. Initialize the data structure
        data = {
            'student': None, 'statement_items': [], 'summary': {},
            'filter_form': self.filter_form_class(request.GET or None),
            'start_date': None, 'end_date': None, 'statement_type': 'detailed'
        }

        # 2. Process form data
        filter_form = data['filter_form']
        if filter_form.is_valid():
            student = filter_form.cleaned_data.get('student')
            start_date = filter_form.cleaned_data.get('start_date')
            end_date = filter_form.cleaned_data.get('end_date')
            academic_year = filter_form.cleaned_data.get('academic_year')
            include_paid = filter_form.cleaned_data.get('include_paid', True)
            include_pending = filter_form.cleaned_data.get('include_pending', True)
            statement_type = filter_form.cleaned_data.get('statement_type', 'detailed')

            if not student:
                return data

            # Set defaults if not provided
            if not start_date:
                start_date = timezone.now().date().replace(month=1, day=1)  # Start of year
            if not end_date:
                end_date = timezone.now().date()

            data.update({
                'student': student,
                'start_date': start_date,
                'end_date': end_date,
                'statement_type': statement_type
            })

            # 3. Get all invoices for the student
            invoices_queryset = Invoice.objects.filter(
                student=student,
                issue_date__gte=start_date,
                issue_date__lte=end_date
            ).select_related('academic_year', 'term').prefetch_related('payments')

            if academic_year:
                invoices_queryset = invoices_queryset.filter(academic_year=academic_year)

            # 4. Get all payments for the student
            payments_queryset = Payment.objects.filter(
                invoice__student=student,
                payment_date__gte=start_date,
                payment_date__lte=end_date,
                status='COMPLETED'
            ).select_related('invoice', 'payment_method')

            if academic_year:
                payments_queryset = payments_queryset.filter(invoice__academic_year=academic_year)

            # 5. Build statement items
            statement_items = []

            # Add invoices
            for invoice in invoices_queryset:
                # Calculate paid amount for this invoice
                paid_amount = invoice.payments.filter(
                    status='COMPLETED',
                    payment_date__lte=end_date
                ).aggregate(total=Coalesce(Sum('amount'), Decimal('0.00')))['total']

                outstanding_amount = invoice.total_amount - paid_amount

                # Apply filters
                if not include_paid and outstanding_amount == 0:
                    continue
                if not include_pending and outstanding_amount > 0:
                    continue

                statement_items.append({
                    'type': 'invoice',
                    'date': invoice.issue_date,
                    'description': f"Invoice #{invoice.invoice_number}",
                    'reference': invoice.invoice_number,
                    'academic_year': invoice.academic_year.name if invoice.academic_year else 'N/A',
                    'term': invoice.term.name if invoice.term else 'N/A',
                    'debit_amount': invoice.total_amount,  # Charges increase balance
                    'credit_amount': Decimal('0.00'),
                    'balance': None,  # Will be calculated later
                    'status': 'Paid' if outstanding_amount == 0 else 'Outstanding',
                    'outstanding': outstanding_amount,
                    'invoice': invoice,
                    'due_date': invoice.due_date
                })

            # Add payments
            for payment in payments_queryset:
                statement_items.append({
                    'type': 'payment',
                    'date': payment.payment_date,
                    'description': f"Payment - {payment.payment_method.name}",
                    'reference': payment.reference_number or f"PAY-{payment.id}",
                    'academic_year': payment.invoice.academic_year.name if payment.invoice.academic_year else 'N/A',
                    'term': payment.invoice.term.name if payment.invoice.term else 'N/A',
                    'debit_amount': Decimal('0.00'),
                    'credit_amount': payment.amount,  # Payments reduce balance
                    'balance': None,  # Will be calculated later
                    'status': 'Completed',
                    'outstanding': Decimal('0.00'),
                    'payment': payment,
                    'invoice_number': payment.invoice.invoice_number
                })

            # 6. Sort by date and calculate running balance
            statement_items.sort(key=lambda x: (x['date'], x['type']))

            running_balance = Decimal('0.00')
            for item in statement_items:
                running_balance += item['debit_amount'] - item['credit_amount']
                item['balance'] = running_balance

            # 7. Calculate summary
            total_charges = sum(item['debit_amount'] for item in statement_items)
            total_payments = sum(item['credit_amount'] for item in statement_items)
            current_balance = total_charges - total_payments

            # Get opening balance (charges before start_date minus payments before start_date)
            opening_invoices = Invoice.objects.filter(
                student=student,
                issue_date__lt=start_date
            ).aggregate(total=Coalesce(Sum('total_amount'), Decimal('0.00')))['total']

            opening_payments = Payment.objects.filter(
                invoice__student=student,
                payment_date__lt=start_date,
                status='COMPLETED'
            ).aggregate(total=Coalesce(Sum('amount'), Decimal('0.00')))['total']

            opening_balance = opening_invoices - opening_payments

            # Adjust running balances to include opening balance
            for item in statement_items:
                item['balance'] += opening_balance

            summary = {
                'opening_balance': opening_balance,
                'total_charges': total_charges,
                'total_payments': total_payments,
                'closing_balance': opening_balance + current_balance,
                'outstanding_invoices': len([item for item in statement_items if item['type'] == 'invoice' and item['outstanding'] > 0]),
                'total_outstanding': sum(item['outstanding'] for item in statement_items if item['type'] == 'invoice')
            }

            # 8. Filter for summary type
            if statement_type == 'outstanding_only':
                statement_items = [item for item in statement_items if item['type'] == 'invoice' and item['outstanding'] > 0]
            elif statement_type == 'summary':
                # Group by month for summary view
                monthly_summary = {}
                for item in statement_items:
                    month_key = item['date'].strftime('%Y-%m')
                    if month_key not in monthly_summary:
                        monthly_summary[month_key] = {
                            'month': item['date'].strftime('%B %Y'),
                            'charges': Decimal('0.00'),
                            'payments': Decimal('0.00'),
                            'invoice_count': 0,
                            'payment_count': 0
                        }

                    monthly_summary[month_key]['charges'] += item['debit_amount']
                    monthly_summary[month_key]['payments'] += item['credit_amount']

                    if item['type'] == 'invoice':
                        monthly_summary[month_key]['invoice_count'] += 1
                    else:
                        monthly_summary[month_key]['payment_count'] += 1

                statement_items = list(monthly_summary.values())

            data.update({
                'statement_items': statement_items,
                'summary': summary
            })

        return data

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        report_data = self._get_student_account_statement_data(self.request)
        context.update(report_data)
        return context


class StudentAccountStatementExportCSVView(StudentAccountStatementReportView):
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        import csv
        from django.http import HttpResponse

        report_data = self._get_student_account_statement_data(request)

        if not report_data['student']:
            messages.error(request, "Please select a student for the account statement.")
            return redirect('reporting:student_account_statement_report')

        response = HttpResponse(content_type='text/csv')
        filename = f"student_account_statement_{report_data['student'].student_id}.csv"
        response['Content-Disposition'] = f'attachment; filename="{filename}"'

        writer = csv.writer(response)
        writer.writerow([f"Account Statement - {report_data['student'].get_full_name()}"])
        writer.writerow([f"Student ID: {report_data['student'].student_id}"])
        writer.writerow([f"Period: {report_data['start_date']} to {report_data['end_date']}"])
        writer.writerow([])

        # Summary
        summary = report_data['summary']
        writer.writerow(['Account Summary'])
        writer.writerow(['Opening Balance', summary.get('opening_balance', 0)])
        writer.writerow(['Total Charges', summary.get('total_charges', 0)])
        writer.writerow(['Total Payments', summary.get('total_payments', 0)])
        writer.writerow(['Closing Balance', summary.get('closing_balance', 0)])
        writer.writerow(['Outstanding Amount', summary.get('total_outstanding', 0)])
        writer.writerow([])

        # Statement details
        writer.writerow(['Date', 'Description', 'Reference', 'Academic Year', 'Term', 'Charges', 'Payments', 'Balance', 'Status'])

        for item in report_data['statement_items']:
            if isinstance(item, dict) and 'month' in item:  # Summary view
                writer.writerow([
                    item['month'], 'Monthly Summary', '', '', '',
                    item['charges'], item['payments'], '', ''
                ])
            else:  # Detailed view
                writer.writerow([
                    item['date'],
                    item['description'],
                    item['reference'],
                    item['academic_year'],
                    item['term'],
                    item['debit_amount'],
                    item['credit_amount'],
                    item['balance'],
                    item['status']
                ])

        return response


class StudentAccountStatementExportPDFView(StudentAccountStatementReportView):
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        report_data = self._get_student_account_statement_data(request)

        if not report_data['student']:
            messages.error(request, "Please select a student for the account statement.")
            return redirect('reporting:student_account_statement_report')

        report_data['school_profile'] = SchoolProfile.objects.first()
        report_data['tenant'] = request.tenant
        report_data['report_title'] = self.report_title

        pdf = render_to_pdf('reporting/pdf/student_account_statement_pdf.html', report_data)
        if not pdf:
            messages.error(request, "There was an error generating the PDF report.")
            return redirect('reporting:student_account_statement_report')

        filename = f"student_account_statement_{report_data['student'].student_id}.pdf"
        response = HttpResponse(pdf, content_type='application/pdf')
        response['Content-Disposition'] = f'inline; filename="{filename}"'
        return response


class StudentAccountStatementExportExcelView(StudentAccountStatementReportView):
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        try:
            import openpyxl
            from openpyxl.styles import Font, PatternFill
        except ImportError:
            messages.error(request, "The library required for Excel export (openpyxl) is not installed.")
            return redirect('reporting:student_account_statement_report')

        report_data = self._get_student_account_statement_data(request)

        if not report_data['student']:
            messages.error(request, "Please select a student for the account statement.")
            return redirect('reporting:student_account_statement_report')

        # Create workbook and worksheet
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "Account Statement"

        # Add headers
        ws['A1'] = f"Account Statement - {report_data['student'].get_full_name()}"
        ws['A1'].font = Font(bold=True, size=14)
        ws['A2'] = f"Student ID: {report_data['student'].student_id}"
        ws['A3'] = f"Period: {report_data['start_date']} to {report_data['end_date']}"

        # Summary
        row = 5
        ws[f'A{row}'] = 'Account Summary'
        ws[f'A{row}'].font = Font(bold=True)
        row += 1

        summary = report_data['summary']
        summary_data = [
            ('Opening Balance', float(summary.get('opening_balance', 0))),
            ('Total Charges', float(summary.get('total_charges', 0))),
            ('Total Payments', float(summary.get('total_payments', 0))),
            ('Closing Balance', float(summary.get('closing_balance', 0))),
            ('Outstanding Amount', float(summary.get('total_outstanding', 0)))
        ]

        for label, value in summary_data:
            ws[f'A{row}'] = label
            ws[f'B{row}'] = value
            row += 1

        # Statement details
        row += 2
        headers = ['Date', 'Description', 'Reference', 'Academic Year', 'Term', 'Charges', 'Payments', 'Balance', 'Status']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=row, column=col, value=header)
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
        row += 1

        for item in report_data['statement_items']:
            if isinstance(item, dict) and 'month' in item:  # Summary view
                ws.cell(row=row, column=1, value=item['month'])
                ws.cell(row=row, column=2, value='Monthly Summary')
                ws.cell(row=row, column=6, value=float(item['charges']))
                ws.cell(row=row, column=7, value=float(item['payments']))
            else:  # Detailed view
                ws.cell(row=row, column=1, value=item['date'].strftime('%Y-%m-%d'))
                ws.cell(row=row, column=2, value=item['description'])
                ws.cell(row=row, column=3, value=item['reference'])
                ws.cell(row=row, column=4, value=item['academic_year'])
                ws.cell(row=row, column=5, value=item['term'])
                ws.cell(row=row, column=6, value=float(item['debit_amount']))
                ws.cell(row=row, column=7, value=float(item['credit_amount']))
                ws.cell(row=row, column=8, value=float(item['balance']))
                ws.cell(row=row, column=9, value=item['status'])
            row += 1

        # Save to response
        filename = f"student_account_statement_{report_data['student'].student_id}.xlsx"
        response = HttpResponse(content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        response['Content-Disposition'] = f'attachment; filename="{filename}"'
        wb.save(response)
        return response


# ========================================
# CLASS-WISE FEE COLLECTION REPORT
# ========================================
class ClasswiseFeeCollectionReportView(TenantPermissionRequiredMixin, BaseReportViewMixin, TemplateView):
    """
    Handles the display of the HTML Class-wise Fee Collection report page.
    """
    template_name = 'reporting/classwise_fee_collection_report.html'

    # Attributes for BaseReportViewMixin
    permission_required = 'reporting.view_classwise_fee_collection_report'
    report_title = _("Class-wise Fee Collection Report")
    filter_form_class = ClasswiseFeeCollectionFilterForm
    export_csv_url_name = 'reporting:classwise_fee_collection_export_csv'
    export_pdf_url_name = 'reporting:classwise_fee_collection_export_pdf'
    export_excel_url_name = 'reporting:classwise_fee_collection_export_excel'
    paginate_by = 50

    def _get_classwise_fee_collection_data(self, request):
        """
        The core logic for fetching and processing all data for the Class-wise Fee Collection Report.
        """
        from apps.fees.models import Invoice
        from apps.payments.models import Payment
        from apps.students.models import Student
        from decimal import Decimal

        # 1. Initialize the data structure
        data = {
            'class_analysis': [], 'fee_type_analysis': [], 'collection_summary': {},
            'filter_form': self.filter_form_class(request.GET or None),
            'academic_year': None, 'term': None, 'as_of_date': None
        }

        # 2. Set default date if form is not submitted
        if not request.GET:
            today = timezone.now().date()
            data['filter_form'] = self.filter_form_class(initial={'as_of_date': today})

        # 3. Process form data
        filter_form = data['filter_form']
        if filter_form.is_valid():
            academic_year = filter_form.cleaned_data.get('academic_year')
            term = filter_form.cleaned_data.get('term')
            class_filter = filter_form.cleaned_data.get('class_filter')
            fee_type = filter_form.cleaned_data.get('fee_type')
            collection_status = filter_form.cleaned_data.get('collection_status')
            as_of_date = filter_form.cleaned_data.get('as_of_date') or timezone.now().date()

            data.update({
                'academic_year': academic_year,
                'term': term,
                'as_of_date': as_of_date
            })

            # 4. Build base invoice queryset
            invoices_queryset = Invoice.objects.filter(
                issue_date__lte=as_of_date
            ).select_related('student', 'student__current_class', 'academic_year', 'term').prefetch_related('payments')

            # Apply filters
            if academic_year:
                invoices_queryset = invoices_queryset.filter(academic_year=academic_year)

            if term:
                invoices_queryset = invoices_queryset.filter(term=term)

            if class_filter:
                invoices_queryset = invoices_queryset.filter(student__current_class=class_filter)

            if fee_type:
                # Assuming invoices have fee_type or we can filter through invoice items
                invoices_queryset = invoices_queryset.filter(fee_type=fee_type)

            # 5. Calculate class-wise collection data
            class_data = {}
            total_invoiced = Decimal('0.00')
            total_collected = Decimal('0.00')
            total_outstanding = Decimal('0.00')

            for invoice in invoices_queryset:
                class_name = invoice.student.current_class.name if invoice.student.current_class else 'Unassigned'

                # Calculate payments for this invoice up to as_of_date
                paid_amount = invoice.payments.filter(
                    status='COMPLETED',
                    payment_date__lte=as_of_date
                ).aggregate(total=Coalesce(Sum('amount'), Decimal('0.00')))['total']

                outstanding_amount = invoice.total_amount - paid_amount

                # Apply collection status filter
                if collection_status:
                    if collection_status == 'paid' and outstanding_amount > 0:
                        continue
                    elif collection_status == 'partial' and (outstanding_amount == 0 or outstanding_amount == invoice.total_amount):
                        continue
                    elif collection_status == 'unpaid' and outstanding_amount != invoice.total_amount:
                        continue
                    elif collection_status == 'overdue' and (outstanding_amount == 0 or invoice.due_date > as_of_date):
                        continue

                # Initialize class data if not exists
                if class_name not in class_data:
                    class_data[class_name] = {
                        'class_name': class_name,
                        'total_invoiced': Decimal('0.00'),
                        'total_collected': Decimal('0.00'),
                        'total_outstanding': Decimal('0.00'),
                        'invoice_count': 0,
                        'student_count': set(),
                        'paid_invoices': 0,
                        'partial_invoices': 0,
                        'unpaid_invoices': 0,
                        'overdue_invoices': 0,
                        'collection_rate': Decimal('0.00'),
                        'avg_per_student': Decimal('0.00')
                    }

                # Update class totals
                class_data[class_name]['total_invoiced'] += invoice.total_amount
                class_data[class_name]['total_collected'] += paid_amount
                class_data[class_name]['total_outstanding'] += outstanding_amount
                class_data[class_name]['invoice_count'] += 1
                class_data[class_name]['student_count'].add(invoice.student.id)

                # Categorize invoice status
                if outstanding_amount == 0:
                    class_data[class_name]['paid_invoices'] += 1
                elif outstanding_amount == invoice.total_amount:
                    class_data[class_name]['unpaid_invoices'] += 1
                else:
                    class_data[class_name]['partial_invoices'] += 1

                # Check if overdue
                if outstanding_amount > 0 and invoice.due_date and invoice.due_date < as_of_date:
                    class_data[class_name]['overdue_invoices'] += 1

                # Update totals
                total_invoiced += invoice.total_amount
                total_collected += paid_amount
                total_outstanding += outstanding_amount

            # 6. Calculate derived metrics for each class
            class_analysis = []
            for class_info in class_data.values():
                student_count = len(class_info['student_count'])
                class_info['student_count'] = student_count

                # Calculate collection rate
                if class_info['total_invoiced'] > 0:
                    class_info['collection_rate'] = (class_info['total_collected'] / class_info['total_invoiced'] * 100)

                # Calculate average per student
                if student_count > 0:
                    class_info['avg_per_student'] = class_info['total_invoiced'] / student_count

                # Calculate percentage of total
                class_info['percentage_of_total'] = (class_info['total_invoiced'] / total_invoiced * 100) if total_invoiced > 0 else Decimal('0.00')

                class_analysis.append(class_info)

            # Sort by total invoiced amount (descending)
            class_analysis.sort(key=lambda x: x['total_invoiced'], reverse=True)

            # 7. Calculate overall collection summary
            overall_collection_rate = (total_collected / total_invoiced * 100) if total_invoiced > 0 else Decimal('0.00')

            collection_summary = {
                'total_invoiced': total_invoiced,
                'total_collected': total_collected,
                'total_outstanding': total_outstanding,
                'overall_collection_rate': overall_collection_rate,
                'total_classes': len(class_analysis),
                'total_invoices': sum(cls['invoice_count'] for cls in class_analysis),
                'total_students': sum(cls['student_count'] for cls in class_analysis)
            }

            # 8. Fee type analysis (if no specific fee type filter)
            fee_type_analysis = []
            if not fee_type:
                # Group by fee type across all classes
                fee_type_data = {}

                for invoice in invoices_queryset:
                    fee_type_name = getattr(invoice, 'fee_type', None)
                    if fee_type_name:
                        fee_type_name = fee_type_name.name
                    else:
                        fee_type_name = 'General Fees'

                    paid_amount = invoice.payments.filter(
                        status='COMPLETED',
                        payment_date__lte=as_of_date
                    ).aggregate(total=Coalesce(Sum('amount'), Decimal('0.00')))['total']

                    if fee_type_name not in fee_type_data:
                        fee_type_data[fee_type_name] = {
                            'fee_type': fee_type_name,
                            'total_invoiced': Decimal('0.00'),
                            'total_collected': Decimal('0.00'),
                            'collection_rate': Decimal('0.00'),
                            'invoice_count': 0
                        }

                    fee_type_data[fee_type_name]['total_invoiced'] += invoice.total_amount
                    fee_type_data[fee_type_name]['total_collected'] += paid_amount
                    fee_type_data[fee_type_name]['invoice_count'] += 1

                # Calculate collection rates for fee types
                for fee_data in fee_type_data.values():
                    if fee_data['total_invoiced'] > 0:
                        fee_data['collection_rate'] = (fee_data['total_collected'] / fee_data['total_invoiced'] * 100)
                    fee_type_analysis.append(fee_data)

                fee_type_analysis.sort(key=lambda x: x['total_invoiced'], reverse=True)

            data.update({
                'class_analysis': class_analysis,
                'fee_type_analysis': fee_type_analysis,
                'collection_summary': collection_summary
            })

        return data

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        report_data = self._get_classwise_fee_collection_data(self.request)
        context.update(report_data)
        return context


class ClasswiseFeeCollectionExportCSVView(ClasswiseFeeCollectionReportView):
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        import csv
        from django.http import HttpResponse

        report_data = self._get_classwise_fee_collection_data(request)

        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename="classwise_fee_collection_report.csv"'

        writer = csv.writer(response)
        writer.writerow([self.report_title])

        if report_data['academic_year']:
            writer.writerow([f"Academic Year: {report_data['academic_year']}"])
        if report_data['term']:
            writer.writerow([f"Term: {report_data['term']}"])
        writer.writerow([f"As of Date: {report_data['as_of_date']}"])
        writer.writerow([])

        # Collection Summary
        summary = report_data['collection_summary']
        writer.writerow(['Collection Summary'])
        writer.writerow(['Total Invoiced', summary.get('total_invoiced', 0)])
        writer.writerow(['Total Collected', summary.get('total_collected', 0)])
        writer.writerow(['Total Outstanding', summary.get('total_outstanding', 0)])
        writer.writerow(['Overall Collection Rate (%)', summary.get('overall_collection_rate', 0)])
        writer.writerow(['Total Classes', summary.get('total_classes', 0)])
        writer.writerow(['Total Students', summary.get('total_students', 0)])
        writer.writerow([])

        # Class Analysis
        writer.writerow(['Class-wise Analysis'])
        writer.writerow(['Class', 'Students', 'Invoices', 'Total Invoiced', 'Total Collected', 'Outstanding', 'Collection Rate (%)', 'Avg per Student', 'Paid', 'Partial', 'Unpaid', 'Overdue'])

        for class_item in report_data['class_analysis']:
            writer.writerow([
                class_item['class_name'],
                class_item['student_count'],
                class_item['invoice_count'],
                class_item['total_invoiced'],
                class_item['total_collected'],
                class_item['total_outstanding'],
                f"{class_item['collection_rate']:.2f}",
                class_item['avg_per_student'],
                class_item['paid_invoices'],
                class_item['partial_invoices'],
                class_item['unpaid_invoices'],
                class_item['overdue_invoices']
            ])

        # Fee Type Analysis
        if report_data['fee_type_analysis']:
            writer.writerow([])
            writer.writerow(['Fee Type Analysis'])
            writer.writerow(['Fee Type', 'Invoices', 'Total Invoiced', 'Total Collected', 'Collection Rate (%)'])

            for fee_item in report_data['fee_type_analysis']:
                writer.writerow([
                    fee_item['fee_type'],
                    fee_item['invoice_count'],
                    fee_item['total_invoiced'],
                    fee_item['total_collected'],
                    f"{fee_item['collection_rate']:.2f}"
                ])

        return response


class ClasswiseFeeCollectionExportPDFView(ClasswiseFeeCollectionReportView):
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        report_data = self._get_classwise_fee_collection_data(request)

        report_data['school_profile'] = SchoolProfile.objects.first()
        report_data['tenant'] = request.tenant
        report_data['report_title'] = self.report_title

        pdf = render_to_pdf('reporting/pdf/classwise_fee_collection_pdf.html', report_data)
        if not pdf:
            messages.error(request, "There was an error generating the PDF report.")
            return redirect('reporting:classwise_fee_collection_report')

        response = HttpResponse(pdf, content_type='application/pdf')
        response['Content-Disposition'] = 'inline; filename="classwise_fee_collection_report.pdf"'
        return response


class ClasswiseFeeCollectionExportExcelView(ClasswiseFeeCollectionReportView):
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        try:
            import openpyxl
            from openpyxl.styles import Font, PatternFill
        except ImportError:
            messages.error(request, "The library required for Excel export (openpyxl) is not installed.")
            return redirect('reporting:classwise_fee_collection_report')

        report_data = self._get_classwise_fee_collection_data(request)

        # Create workbook and worksheet
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "Class-wise Collection"

        # Add headers
        ws['A1'] = self.report_title
        ws['A1'].font = Font(bold=True, size=14)

        row = 2
        if report_data['academic_year']:
            ws[f'A{row}'] = f"Academic Year: {report_data['academic_year']}"
            row += 1
        if report_data['term']:
            ws[f'A{row}'] = f"Term: {report_data['term']}"
            row += 1
        ws[f'A{row}'] = f"As of Date: {report_data['as_of_date']}"
        row += 2

        # Collection Summary
        ws[f'A{row}'] = 'Collection Summary'
        ws[f'A{row}'].font = Font(bold=True)
        row += 1

        summary = report_data['collection_summary']
        summary_data = [
            ('Total Invoiced', float(summary.get('total_invoiced', 0))),
            ('Total Collected', float(summary.get('total_collected', 0))),
            ('Total Outstanding', float(summary.get('total_outstanding', 0))),
            ('Overall Collection Rate (%)', float(summary.get('overall_collection_rate', 0))),
            ('Total Classes', summary.get('total_classes', 0)),
            ('Total Students', summary.get('total_students', 0))
        ]

        for label, value in summary_data:
            ws[f'A{row}'] = label
            ws[f'B{row}'] = value
            row += 1

        # Class Analysis
        row += 2
        ws[f'A{row}'] = 'Class-wise Analysis'
        ws[f'A{row}'].font = Font(bold=True)
        row += 1

        headers = ['Class', 'Students', 'Invoices', 'Total Invoiced', 'Total Collected', 'Outstanding', 'Collection Rate (%)', 'Avg per Student']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=row, column=col, value=header)
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
        row += 1

        for class_item in report_data['class_analysis']:
            ws.cell(row=row, column=1, value=class_item['class_name'])
            ws.cell(row=row, column=2, value=class_item['student_count'])
            ws.cell(row=row, column=3, value=class_item['invoice_count'])
            ws.cell(row=row, column=4, value=float(class_item['total_invoiced']))
            ws.cell(row=row, column=5, value=float(class_item['total_collected']))
            ws.cell(row=row, column=6, value=float(class_item['total_outstanding']))
            ws.cell(row=row, column=7, value=f"{float(class_item['collection_rate']):.2f}%")
            ws.cell(row=row, column=8, value=float(class_item['avg_per_student']))
            row += 1

        # Save to response
        response = HttpResponse(content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        response['Content-Disposition'] = 'attachment; filename="classwise_fee_collection_report.xlsx"'
        wb.save(response)
        return response


# ========================================
# FEE DEFAULTERS REPORT
# ========================================
class FeeDefaultersReportView(TenantPermissionRequiredMixin, BaseReportViewMixin, TemplateView):
    """
    Handles the display of the HTML Fee Defaulters report page.
    """
    template_name = 'reporting/fee_defaulters_report.html'

    # Attributes for BaseReportViewMixin
    permission_required = 'reporting.view_fee_defaulters_report'
    report_title = _("Fee Defaulters Report")
    filter_form_class = FeeDefaultersFilterForm
    export_csv_url_name = 'reporting:fee_defaulters_export_csv'
    export_pdf_url_name = 'reporting:fee_defaulters_export_pdf'
    export_excel_url_name = 'reporting:fee_defaulters_export_excel'
    paginate_by = 50

    def _get_fee_defaulters_data(self, request):
        """
        The core logic for fetching and processing all data for the Fee Defaulters Report.
        """
        from apps.fees.models import Invoice
        from apps.payments.models import Payment
        from apps.students.models import Student
        from decimal import Decimal
        from datetime import timedelta

        # 1. Initialize the data structure
        data = {
            'defaulters': [], 'summary': {}, 'filter_form': self.filter_form_class(request.GET or None),
            'academic_year': None, 'as_of_date': None, 'class_filter': None
        }

        # 2. Set default date if form is not submitted
        if not request.GET:
            today = timezone.now().date()
            data['filter_form'] = self.filter_form_class(initial={'as_of_date': today})

        # 3. Process form data
        filter_form = data['filter_form']
        if filter_form.is_valid():
            academic_year = filter_form.cleaned_data.get('academic_year')
            class_filter = filter_form.cleaned_data.get('class_filter')
            overdue_days = filter_form.cleaned_data.get('overdue_days')
            minimum_amount = filter_form.cleaned_data.get('minimum_amount') or Decimal('0.00')
            defaulter_status = filter_form.cleaned_data.get('defaulter_status')
            as_of_date = filter_form.cleaned_data.get('as_of_date') or timezone.now().date()
            include_contact_info = filter_form.cleaned_data.get('include_contact_info', True)
            sort_by = filter_form.cleaned_data.get('sort_by', 'amount_desc')

            data.update({
                'academic_year': academic_year,
                'as_of_date': as_of_date,
                'class_filter': class_filter,
                'include_contact_info': include_contact_info
            })

            # 4. Get all overdue invoices
            overdue_invoices = Invoice.objects.filter(
                due_date__lt=as_of_date,
                issue_date__lte=as_of_date
            ).select_related('student', 'student__current_class', 'student__parent', 'academic_year').prefetch_related('payments')

            # Apply filters
            if academic_year:
                overdue_invoices = overdue_invoices.filter(academic_year=academic_year)

            if class_filter:
                overdue_invoices = overdue_invoices.filter(student__current_class=class_filter)

            # 5. Process each overdue invoice to find defaulters
            defaulter_data = {}
            total_overdue_amount = Decimal('0.00')
            total_defaulters = 0

            for invoice in overdue_invoices:
                # Calculate payments for this invoice up to as_of_date
                paid_amount = invoice.payments.filter(
                    status='COMPLETED',
                    payment_date__lte=as_of_date
                ).aggregate(total=Coalesce(Sum('amount'), Decimal('0.00')))['total']

                outstanding_amount = invoice.total_amount - paid_amount

                # Skip if fully paid or below minimum amount
                if outstanding_amount <= minimum_amount:
                    continue

                # Calculate days overdue
                days_overdue = (as_of_date - invoice.due_date).days

                # Apply overdue days filter
                if overdue_days:
                    min_overdue_days = int(overdue_days)
                    if days_overdue < min_overdue_days:
                        continue

                # Apply defaulter status filter
                if defaulter_status:
                    if defaulter_status == 'active' and not invoice.student.is_active:
                        continue
                    elif defaulter_status == 'inactive' and invoice.student.is_active:
                        continue
                    elif defaulter_status == 'critical' and days_overdue < 90:
                        continue

                student_id = invoice.student.id

                # Initialize student defaulter data if not exists
                if student_id not in defaulter_data:
                    defaulter_data[student_id] = {
                        'student': invoice.student,
                        'class_name': invoice.student.current_class.name if invoice.student.current_class else 'Unassigned',
                        'total_outstanding': Decimal('0.00'),
                        'overdue_invoices': [],
                        'max_days_overdue': 0,
                        'min_days_overdue': float('inf'),
                        'invoice_count': 0,
                        'earliest_due_date': None,
                        'latest_due_date': None,
                        'contact_info': {},
                        'risk_level': 'Low'
                    }

                # Add invoice to defaulter data
                defaulter_info = defaulter_data[student_id]
                defaulter_info['total_outstanding'] += outstanding_amount
                defaulter_info['invoice_count'] += 1
                defaulter_info['max_days_overdue'] = max(defaulter_info['max_days_overdue'], days_overdue)
                defaulter_info['min_days_overdue'] = min(defaulter_info['min_days_overdue'], days_overdue)

                # Track date ranges
                if not defaulter_info['earliest_due_date'] or invoice.due_date < defaulter_info['earliest_due_date']:
                    defaulter_info['earliest_due_date'] = invoice.due_date
                if not defaulter_info['latest_due_date'] or invoice.due_date > defaulter_info['latest_due_date']:
                    defaulter_info['latest_due_date'] = invoice.due_date

                # Add invoice details
                defaulter_info['overdue_invoices'].append({
                    'invoice': invoice,
                    'outstanding_amount': outstanding_amount,
                    'days_overdue': days_overdue,
                    'due_date': invoice.due_date
                })

                # Add contact information if requested
                if include_contact_info and invoice.student.parent:
                    parent = invoice.student.parent
                    defaulter_info['contact_info'] = {
                        'parent_name': parent.get_full_name(),
                        'phone': getattr(parent, 'phone', ''),
                        'email': getattr(parent, 'email', ''),
                        'address': getattr(parent, 'address', '')
                    }

                total_overdue_amount += outstanding_amount

            # 6. Calculate risk levels and finalize defaulter data
            defaulters = []
            for defaulter_info in defaulter_data.values():
                # Calculate risk level
                max_days = defaulter_info['max_days_overdue']
                amount = defaulter_info['total_outstanding']

                if max_days >= 120 or amount >= 50000:
                    defaulter_info['risk_level'] = 'Critical'
                elif max_days >= 60 or amount >= 20000:
                    defaulter_info['risk_level'] = 'High'
                elif max_days >= 30 or amount >= 10000:
                    defaulter_info['risk_level'] = 'Medium'
                else:
                    defaulter_info['risk_level'] = 'Low'

                # Fix min_days_overdue if it's still infinity
                if defaulter_info['min_days_overdue'] == float('inf'):
                    defaulter_info['min_days_overdue'] = defaulter_info['max_days_overdue']

                defaulters.append(defaulter_info)

            # 7. Sort defaulters based on sort_by parameter
            if sort_by == 'amount_desc':
                defaulters.sort(key=lambda x: x['total_outstanding'], reverse=True)
            elif sort_by == 'amount_asc':
                defaulters.sort(key=lambda x: x['total_outstanding'])
            elif sort_by == 'days_desc':
                defaulters.sort(key=lambda x: x['max_days_overdue'], reverse=True)
            elif sort_by == 'days_asc':
                defaulters.sort(key=lambda x: x['max_days_overdue'])
            elif sort_by == 'student_name':
                defaulters.sort(key=lambda x: x['student'].get_full_name())
            elif sort_by == 'class_name':
                defaulters.sort(key=lambda x: x['class_name'])

            # 8. Calculate summary statistics
            total_defaulters = len(defaulters)
            risk_breakdown = {'Critical': 0, 'High': 0, 'Medium': 0, 'Low': 0}
            class_breakdown = {}

            for defaulter in defaulters:
                risk_breakdown[defaulter['risk_level']] += 1

                class_name = defaulter['class_name']
                if class_name not in class_breakdown:
                    class_breakdown[class_name] = {'count': 0, 'amount': Decimal('0.00')}
                class_breakdown[class_name]['count'] += 1
                class_breakdown[class_name]['amount'] += defaulter['total_outstanding']

            # Calculate averages
            avg_outstanding = total_overdue_amount / total_defaulters if total_defaulters > 0 else Decimal('0.00')
            avg_days_overdue = sum(d['max_days_overdue'] for d in defaulters) / total_defaulters if total_defaulters > 0 else 0

            summary = {
                'total_defaulters': total_defaulters,
                'total_overdue_amount': total_overdue_amount,
                'avg_outstanding': avg_outstanding,
                'avg_days_overdue': avg_days_overdue,
                'risk_breakdown': risk_breakdown,
                'class_breakdown': class_breakdown,
                'total_overdue_invoices': sum(d['invoice_count'] for d in defaulters)
            }

            data.update({
                'defaulters': defaulters,
                'summary': summary
            })

        return data

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        report_data = self._get_fee_defaulters_data(self.request)
        context.update(report_data)
        return context


class FeeDefaultersExportCSVView(FeeDefaultersReportView):
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        import csv
        from django.http import HttpResponse

        report_data = self._get_fee_defaulters_data(request)

        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename="fee_defaulters_report.csv"'

        writer = csv.writer(response)
        writer.writerow([self.report_title])

        if report_data['academic_year']:
            writer.writerow([f"Academic Year: {report_data['academic_year']}"])
        writer.writerow([f"As of Date: {report_data['as_of_date']}"])
        writer.writerow([])

        # Summary
        summary = report_data['summary']
        writer.writerow(['Summary'])
        writer.writerow(['Total Defaulters', summary.get('total_defaulters', 0)])
        writer.writerow(['Total Overdue Amount', summary.get('total_overdue_amount', 0)])
        writer.writerow(['Average Outstanding', summary.get('avg_outstanding', 0)])
        writer.writerow(['Average Days Overdue', summary.get('avg_days_overdue', 0)])
        writer.writerow([])

        # Risk Breakdown
        writer.writerow(['Risk Level Breakdown'])
        risk_breakdown = summary.get('risk_breakdown', {})
        for risk_level, count in risk_breakdown.items():
            writer.writerow([risk_level, count])
        writer.writerow([])

        # Defaulters Details
        writer.writerow(['Defaulter Details'])
        headers = ['Student Name', 'Student ID', 'Class', 'Outstanding Amount', 'Max Days Overdue', 'Invoice Count', 'Risk Level']
        if report_data['include_contact_info']:
            headers.extend(['Parent Name', 'Phone', 'Email'])
        writer.writerow(headers)

        for defaulter in report_data['defaulters']:
            row = [
                defaulter['student'].get_full_name(),
                defaulter['student'].student_id,
                defaulter['class_name'],
                defaulter['total_outstanding'],
                defaulter['max_days_overdue'],
                defaulter['invoice_count'],
                defaulter['risk_level']
            ]

            if report_data['include_contact_info']:
                contact = defaulter.get('contact_info', {})
                row.extend([
                    contact.get('parent_name', ''),
                    contact.get('phone', ''),
                    contact.get('email', '')
                ])

            writer.writerow(row)

        return response


class FeeDefaultersExportPDFView(FeeDefaultersReportView):
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        report_data = self._get_fee_defaulters_data(request)

        report_data['school_profile'] = SchoolProfile.objects.first()
        report_data['tenant'] = request.tenant
        report_data['report_title'] = self.report_title

        pdf = render_to_pdf('reporting/pdf/fee_defaulters_pdf.html', report_data)
        if not pdf:
            messages.error(request, "There was an error generating the PDF report.")
            return redirect('reporting:fee_defaulters_report')

        response = HttpResponse(pdf, content_type='application/pdf')
        response['Content-Disposition'] = 'inline; filename="fee_defaulters_report.pdf"'
        return response


class FeeDefaultersExportExcelView(FeeDefaultersReportView):
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        try:
            import openpyxl
            from openpyxl.styles import Font, PatternFill
        except ImportError:
            messages.error(request, "The library required for Excel export (openpyxl) is not installed.")
            return redirect('reporting:fee_defaulters_report')

        report_data = self._get_fee_defaulters_data(request)

        # Create workbook and worksheet
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "Fee Defaulters"

        # Add headers
        ws['A1'] = self.report_title
        ws['A1'].font = Font(bold=True, size=14)

        row = 2
        if report_data['academic_year']:
            ws[f'A{row}'] = f"Academic Year: {report_data['academic_year']}"
            row += 1
        ws[f'A{row}'] = f"As of Date: {report_data['as_of_date']}"
        row += 2

        # Summary
        ws[f'A{row}'] = 'Summary'
        ws[f'A{row}'].font = Font(bold=True)
        row += 1

        summary = report_data['summary']
        summary_data = [
            ('Total Defaulters', summary.get('total_defaulters', 0)),
            ('Total Overdue Amount', float(summary.get('total_overdue_amount', 0))),
            ('Average Outstanding', float(summary.get('avg_outstanding', 0))),
            ('Average Days Overdue', float(summary.get('avg_days_overdue', 0)))
        ]

        for label, value in summary_data:
            ws[f'A{row}'] = label
            ws[f'B{row}'] = value
            row += 1

        # Defaulters Details
        row += 2
        ws[f'A{row}'] = 'Defaulter Details'
        ws[f'A{row}'].font = Font(bold=True)
        row += 1

        headers = ['Student Name', 'Student ID', 'Class', 'Outstanding Amount', 'Max Days Overdue', 'Invoice Count', 'Risk Level']
        if report_data['include_contact_info']:
            headers.extend(['Parent Name', 'Phone', 'Email'])

        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=row, column=col, value=header)
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
        row += 1

        for defaulter in report_data['defaulters']:
            ws.cell(row=row, column=1, value=defaulter['student'].get_full_name())
            ws.cell(row=row, column=2, value=defaulter['student'].student_id)
            ws.cell(row=row, column=3, value=defaulter['class_name'])
            ws.cell(row=row, column=4, value=float(defaulter['total_outstanding']))
            ws.cell(row=row, column=5, value=defaulter['max_days_overdue'])
            ws.cell(row=row, column=6, value=defaulter['invoice_count'])
            ws.cell(row=row, column=7, value=defaulter['risk_level'])

            if report_data['include_contact_info']:
                contact = defaulter.get('contact_info', {})
                ws.cell(row=row, column=8, value=contact.get('parent_name', ''))
                ws.cell(row=row, column=9, value=contact.get('phone', ''))
                ws.cell(row=row, column=10, value=contact.get('email', ''))

            row += 1

        # Save to response
        response = HttpResponse(content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        response['Content-Disposition'] = 'attachment; filename="fee_defaulters_report.xlsx"'
        wb.save(response)
        return response


# ========================================
# CASH FLOW FORECASTING REPORT
# ========================================
class CashFlowForecastingReportView(TenantPermissionRequiredMixin, BaseReportViewMixin, TemplateView):
    """
    Handles the display of the HTML Cash Flow Forecasting report page.
    """
    template_name = 'reporting/cash_flow_forecasting_report.html'

    # Attributes for BaseReportViewMixin
    permission_required = 'reporting.view_cash_flow_forecasting_report'
    report_title = _("Cash Flow Forecasting Report")
    filter_form_class = CashFlowForecastingFilterForm
    export_csv_url_name = 'reporting:cash_flow_forecasting_export_csv'
    export_pdf_url_name = 'reporting:cash_flow_forecasting_export_pdf'
    export_excel_url_name = 'reporting:cash_flow_forecasting_export_excel'
    paginate_by = 50

    def _get_cash_flow_forecasting_data(self, request):
        """
        The core logic for fetching and processing all data for the Cash Flow Forecasting Report.
        """
        from apps.fees.models import Invoice
        from apps.payments.models import Payment
        from decimal import Decimal
        from datetime import timedelta
        try:
            from dateutil.relativedelta import relativedelta
        except ImportError:
            # Fallback if dateutil is not available
            def relativedelta(months=0):
                return timedelta(days=months * 30)  # Approximate

        # 1. Initialize the data structure
        data = {
            'forecast_data': [], 'historical_data': [], 'summary': {},
            'filter_form': self.filter_form_class(request.GET or None),
            'forecast_period': '12_months', 'historical_period': '12_months'
        }

        # 2. Process form data
        filter_form = data['filter_form']
        if filter_form.is_valid():
            forecast_period = filter_form.cleaned_data.get('forecast_period', '12_months')
            historical_period = filter_form.cleaned_data.get('historical_period', '12_months')
            forecast_method = filter_form.cleaned_data.get('forecast_method', 'trend_analysis')
            include_receivables = filter_form.cleaned_data.get('include_receivables', True)
            include_seasonal = filter_form.cleaned_data.get('include_seasonal', True)
            confidence_level = filter_form.cleaned_data.get('confidence_level', 'moderate')

            data.update({
                'forecast_period': forecast_period,
                'historical_period': historical_period,
                'forecast_method': forecast_method,
                'include_receivables': include_receivables,
                'include_seasonal': include_seasonal,
                'confidence_level': confidence_level
            })

            # 3. Calculate date ranges
            today = timezone.now().date()

            # Historical period
            historical_months = int(historical_period.split('_')[0])
            historical_start = today - relativedelta(months=historical_months)

            # Forecast period
            forecast_months = int(forecast_period.split('_')[0])

            # 4. Get historical cash flow data
            historical_data = []
            current_date = historical_start.replace(day=1)  # Start of month

            while current_date < today:
                month_end = current_date + relativedelta(months=1) - timedelta(days=1)

                # Get payments (cash inflows) for the month
                monthly_payments = Payment.objects.filter(
                    payment_date__gte=current_date,
                    payment_date__lte=month_end,
                    status='COMPLETED'
                ).aggregate(total=Coalesce(Sum('amount'), Decimal('0.00')))['total']

                # Get invoices issued (potential inflows) for the month
                monthly_invoices = Invoice.objects.filter(
                    issue_date__gte=current_date,
                    issue_date__lte=month_end
                ).aggregate(total=Coalesce(Sum('total_amount'), Decimal('0.00')))['total']

                # Calculate collection rate for the month
                collection_rate = (monthly_payments / monthly_invoices * 100) if monthly_invoices > 0 else Decimal('0.00')

                historical_data.append({
                    'month': current_date,
                    'month_name': current_date.strftime('%B %Y'),
                    'cash_inflow': monthly_payments,
                    'invoices_issued': monthly_invoices,
                    'collection_rate': collection_rate,
                    'net_cash_flow': monthly_payments  # Simplified - could include expenses
                })

                current_date += relativedelta(months=1)

            # 5. Calculate forecast data
            forecast_data = []

            # Calculate average monthly metrics from historical data
            if historical_data:
                avg_monthly_inflow = sum(item['cash_inflow'] for item in historical_data) / len(historical_data)
                avg_collection_rate = sum(item['collection_rate'] for item in historical_data) / len(historical_data)

                # Calculate trend (simple linear trend)
                if len(historical_data) >= 3:
                    recent_avg = sum(item['cash_inflow'] for item in historical_data[-3:]) / 3
                    older_avg = sum(item['cash_inflow'] for item in historical_data[:3]) / 3
                    trend_factor = (recent_avg / older_avg) if older_avg > 0 else Decimal('1.00')
                else:
                    trend_factor = Decimal('1.00')
            else:
                avg_monthly_inflow = Decimal('0.00')
                avg_collection_rate = Decimal('0.00')
                trend_factor = Decimal('1.00')

            # Generate forecast
            current_forecast_date = today.replace(day=1) + relativedelta(months=1)

            for i in range(forecast_months):
                forecast_month = current_forecast_date + relativedelta(months=i)

                # Base forecast on historical average
                base_forecast = avg_monthly_inflow

                # Apply forecasting method
                if forecast_method == 'trend_analysis':
                    # Apply trend factor
                    forecast_multiplier = trend_factor ** (i / 12.0)  # Compound trend over time
                    forecasted_inflow = base_forecast * forecast_multiplier
                elif forecast_method == 'seasonal_adjustment' and include_seasonal:
                    # Apply seasonal adjustment (simplified)
                    month_num = forecast_month.month
                    seasonal_factors = {
                        1: 0.9, 2: 0.85, 3: 1.1, 4: 1.05, 5: 1.0, 6: 1.15,  # School year patterns
                        7: 0.8, 8: 0.9, 9: 1.2, 10: 1.1, 11: 1.0, 12: 0.95
                    }
                    seasonal_factor = seasonal_factors.get(month_num, 1.0)
                    forecasted_inflow = base_forecast * Decimal(str(seasonal_factor))
                elif forecast_method == 'enrollment_based':
                    # Could be enhanced with actual enrollment data
                    forecasted_inflow = base_forecast * Decimal('1.02')  # Assume 2% growth
                else:  # conservative
                    forecasted_inflow = base_forecast * Decimal('0.9')  # 10% conservative reduction

                # Apply confidence level adjustments
                if confidence_level == 'conservative':
                    forecasted_inflow *= Decimal('0.8')
                elif confidence_level == 'optimistic':
                    forecasted_inflow *= Decimal('1.1')
                # moderate uses base forecast

                forecast_data.append({
                    'month': forecast_month,
                    'month_name': forecast_month.strftime('%B %Y'),
                    'forecasted_inflow': forecasted_inflow,
                    'total_expected': forecasted_inflow,
                    'confidence_level': confidence_level,
                    'method_used': forecast_method
                })

            # 6. Calculate summary statistics
            total_historical_inflow = sum(item['cash_inflow'] for item in historical_data)
            total_forecasted_inflow = sum(item['total_expected'] for item in forecast_data)

            summary = {
                'historical_months': len(historical_data),
                'forecast_months': len(forecast_data),
                'total_historical_inflow': total_historical_inflow,
                'total_forecasted_inflow': total_forecasted_inflow,
                'avg_monthly_historical': avg_monthly_inflow,
                'avg_monthly_forecast': total_forecasted_inflow / len(forecast_data) if forecast_data else Decimal('0.00'),
                'avg_collection_rate': avg_collection_rate,
                'trend_factor': trend_factor,
                'growth_rate': ((trend_factor - 1) * 100) if trend_factor > 0 else Decimal('0.00')
            }

            data.update({
                'forecast_data': forecast_data,
                'historical_data': historical_data,
                'summary': summary
            })

        return data

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        report_data = self._get_cash_flow_forecasting_data(self.request)
        context.update(report_data)
        return context


class CashFlowForecastingExportCSVView(CashFlowForecastingReportView):
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        import csv
        from django.http import HttpResponse

        report_data = self._get_cash_flow_forecasting_data(request)

        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename="cash_flow_forecasting_report.csv"'

        writer = csv.writer(response)
        writer.writerow([self.report_title])
        writer.writerow([f"Forecast Period: {report_data['forecast_period']}"])
        writer.writerow([f"Historical Period: {report_data['historical_period']}"])
        writer.writerow([f"Forecast Method: {report_data['forecast_method']}"])
        writer.writerow([])

        # Summary
        summary = report_data['summary']
        writer.writerow(['Summary'])
        writer.writerow(['Total Historical Inflow', summary.get('total_historical_inflow', 0)])
        writer.writerow(['Total Forecasted Inflow', summary.get('total_forecasted_inflow', 0)])
        writer.writerow(['Average Monthly Historical', summary.get('avg_monthly_historical', 0)])
        writer.writerow(['Average Monthly Forecast', summary.get('avg_monthly_forecast', 0)])
        writer.writerow(['Growth Rate (%)', summary.get('growth_rate', 0)])
        writer.writerow([])

        # Historical Data
        writer.writerow(['Historical Cash Flow'])
        writer.writerow(['Month', 'Cash Inflow', 'Invoices Issued', 'Collection Rate (%)', 'Net Cash Flow'])
        for item in report_data['historical_data']:
            writer.writerow([
                item['month_name'],
                item['cash_inflow'],
                item['invoices_issued'],
                f"{item['collection_rate']:.2f}",
                item['net_cash_flow']
            ])

        writer.writerow([])

        # Forecast Data
        writer.writerow(['Cash Flow Forecast'])
        writer.writerow(['Month', 'Forecasted Inflow', 'Total Expected', 'Method', 'Confidence Level'])
        for item in report_data['forecast_data']:
            writer.writerow([
                item['month_name'],
                item['forecasted_inflow'],
                item['total_expected'],
                item['method_used'],
                item['confidence_level']
            ])

        return response


class CashFlowForecastingExportPDFView(CashFlowForecastingReportView):
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        report_data = self._get_cash_flow_forecasting_data(request)

        report_data['school_profile'] = SchoolProfile.objects.first()
        report_data['tenant'] = request.tenant
        report_data['report_title'] = self.report_title

        pdf = render_to_pdf('reporting/pdf/cash_flow_forecasting_pdf.html', report_data)
        if not pdf:
            messages.error(request, "There was an error generating the PDF report.")
            return redirect('reporting:cash_flow_forecasting_report')

        response = HttpResponse(pdf, content_type='application/pdf')
        response['Content-Disposition'] = 'inline; filename="cash_flow_forecasting_report.pdf"'
        return response


class CashFlowForecastingExportExcelView(CashFlowForecastingReportView):
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        try:
            import openpyxl
            from openpyxl.styles import Font, PatternFill
        except ImportError:
            messages.error(request, "The library required for Excel export (openpyxl) is not installed.")
            return redirect('reporting:cash_flow_forecasting_report')

        report_data = self._get_cash_flow_forecasting_data(request)

        # Create workbook and worksheet
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "Cash Flow Forecast"

        # Add headers
        ws['A1'] = self.report_title
        ws['A1'].font = Font(bold=True, size=14)
        ws['A2'] = f"Forecast Period: {report_data['forecast_period']}"
        ws['A3'] = f"Historical Period: {report_data['historical_period']}"
        ws['A4'] = f"Forecast Method: {report_data['forecast_method']}"

        # Summary
        row = 6
        ws[f'A{row}'] = 'Summary'
        ws[f'A{row}'].font = Font(bold=True)
        row += 1

        summary = report_data['summary']
        summary_data = [
            ('Total Historical Inflow', float(summary.get('total_historical_inflow', 0))),
            ('Total Forecasted Inflow', float(summary.get('total_forecasted_inflow', 0))),
            ('Average Monthly Historical', float(summary.get('avg_monthly_historical', 0))),
            ('Average Monthly Forecast', float(summary.get('avg_monthly_forecast', 0))),
            ('Growth Rate (%)', float(summary.get('growth_rate', 0)))
        ]

        for label, value in summary_data:
            ws[f'A{row}'] = label
            ws[f'B{row}'] = value
            row += 1

        # Historical Data
        row += 2
        ws[f'A{row}'] = 'Historical Cash Flow'
        ws[f'A{row}'].font = Font(bold=True)
        row += 1

        headers = ['Month', 'Cash Inflow', 'Invoices Issued', 'Collection Rate (%)', 'Net Cash Flow']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=row, column=col, value=header)
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
        row += 1

        for item in report_data['historical_data']:
            ws.cell(row=row, column=1, value=item['month_name'])
            ws.cell(row=row, column=2, value=float(item['cash_inflow']))
            ws.cell(row=row, column=3, value=float(item['invoices_issued']))
            ws.cell(row=row, column=4, value=f"{float(item['collection_rate']):.2f}%")
            ws.cell(row=row, column=5, value=float(item['net_cash_flow']))
            row += 1

        # Forecast Data
        row += 2
        ws[f'A{row}'] = 'Cash Flow Forecast'
        ws[f'A{row}'].font = Font(bold=True)
        row += 1

        forecast_headers = ['Month', 'Forecasted Inflow', 'Total Expected', 'Method', 'Confidence Level']
        for col, header in enumerate(forecast_headers, 1):
            cell = ws.cell(row=row, column=col, value=header)
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color="DDDDDD", end_color="DDDDDD", fill_type="solid")
        row += 1

        for item in report_data['forecast_data']:
            ws.cell(row=row, column=1, value=item['month_name'])
            ws.cell(row=row, column=2, value=float(item['forecasted_inflow']))
            ws.cell(row=row, column=3, value=float(item['total_expected']))
            ws.cell(row=row, column=4, value=item['method_used'])
            ws.cell(row=row, column=5, value=item['confidence_level'])
            row += 1

        # Save to response
        response = HttpResponse(content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        response['Content-Disposition'] = 'attachment; filename="cash_flow_forecasting_report.xlsx"'
        wb.save(response)
        return response


# ========================================
# PROFITABILITY ANALYSIS REPORT
# ========================================
class ProfitabilityAnalysisReportView(TenantPermissionRequiredMixin, BaseReportViewMixin, TemplateView):
    """
    Handles the display of the HTML Profitability Analysis report page.
    """
    template_name = 'reporting/profitability_analysis_report.html'

    # Attributes for BaseReportViewMixin
    permission_required = 'reporting.view_profitability_analysis_report'
    report_title = _("Profitability Analysis Report")
    filter_form_class = ProfitabilityAnalysisFilterForm
    export_csv_url_name = 'reporting:profitability_analysis_export_csv'
    export_pdf_url_name = 'reporting:profitability_analysis_export_pdf'
    export_excel_url_name = 'reporting:profitability_analysis_export_excel'
    paginate_by = 50

    def _get_profitability_analysis_data(self, request):
        """
        The core logic for fetching and processing all data for the Profitability Analysis Report.
        """
        from apps.fees.models import Invoice
        from apps.payments.models import Payment
        from apps.accounting.models import JournalEntry, JournalLine
        from decimal import Decimal

        # 1. Initialize the data structure
        data = {
            'profitability_analysis': [], 'cost_breakdown': [], 'summary': {},
            'filter_form': self.filter_form_class(request.GET or None),
            'academic_year': None, 'analysis_type': 'class'
        }

        # 2. Process form data
        filter_form = data['filter_form']
        if filter_form.is_valid():
            academic_year = filter_form.cleaned_data.get('academic_year')
            analysis_type = filter_form.cleaned_data.get('analysis_type', 'class')
            cost_allocation_method = filter_form.cleaned_data.get('cost_allocation_method', 'proportional')
            include_indirect_costs = filter_form.cleaned_data.get('include_indirect_costs', True)
            profitability_threshold = filter_form.cleaned_data.get('profitability_threshold', 15)

            data.update({
                'academic_year': academic_year,
                'analysis_type': analysis_type,
                'cost_allocation_method': cost_allocation_method,
                'include_indirect_costs': include_indirect_costs,
                'profitability_threshold': profitability_threshold
            })

            # 3. Get revenue data based on analysis type
            revenue_queryset = Invoice.objects.select_related('student', 'student__current_class', 'academic_year', 'fee_type')

            if academic_year:
                revenue_queryset = revenue_queryset.filter(academic_year=academic_year)

            # 4. Calculate revenue by analysis type
            revenue_analysis = {}
            total_revenue = Decimal('0.00')

            for invoice in revenue_queryset:
                # Determine grouping key based on analysis type
                if analysis_type == 'class':
                    group_key = invoice.student.current_class.name if invoice.student.current_class else 'Unassigned'
                elif analysis_type == 'fee_type':
                    group_key = invoice.fee_type.name if invoice.fee_type else 'General Fees'
                elif analysis_type == 'program':
                    # Assuming program is related to class or fee type
                    group_key = getattr(invoice.student.current_class, 'program', 'General Program') if invoice.student.current_class else 'General Program'
                elif analysis_type == 'monthly':
                    group_key = invoice.issue_date.strftime('%Y-%m') if invoice.issue_date else 'Unknown'
                else:  # comparative
                    group_key = f"{invoice.academic_year.name}" if invoice.academic_year else 'Unknown Year'

                # Calculate actual revenue (payments received)
                paid_amount = invoice.payments.filter(status='COMPLETED').aggregate(
                    total=Coalesce(Sum('amount'), Decimal('0.00'))
                )['total']

                if group_key not in revenue_analysis:
                    revenue_analysis[group_key] = {
                        'group_name': group_key,
                        'total_revenue': Decimal('0.00'),
                        'total_invoiced': Decimal('0.00'),
                        'invoice_count': 0,
                        'student_count': set(),
                        'collection_rate': Decimal('0.00')
                    }

                revenue_analysis[group_key]['total_revenue'] += paid_amount
                revenue_analysis[group_key]['total_invoiced'] += invoice.total_amount
                revenue_analysis[group_key]['invoice_count'] += 1
                revenue_analysis[group_key]['student_count'].add(invoice.student.id)

                total_revenue += paid_amount

            # Calculate collection rates
            for group_data in revenue_analysis.values():
                if group_data['total_invoiced'] > 0:
                    group_data['collection_rate'] = (group_data['total_revenue'] / group_data['total_invoiced'] * 100)
                group_data['student_count'] = len(group_data['student_count'])

            # 5. Calculate costs based on allocation method
            cost_analysis = {}
            total_costs = Decimal('0.00')

            # Get expense accounts (assuming expense accounts have specific types)
            expense_entries = JournalEntry.objects.filter(
                status=JournalEntry.StatusChoices.POSTED
            ).prefetch_related('lines')

            if academic_year:
                expense_entries = expense_entries.filter(
                    date__gte=academic_year.start_date,
                    date__lte=academic_year.end_date
                )

            # Calculate total expenses
            for entry in expense_entries:
                for line in entry.lines.all():
                    # Assuming expense accounts have debit balances
                    if line.debit_amount > 0:
                        expense_amount = line.debit_amount
                        total_costs += expense_amount

            # 6. Allocate costs based on method
            if cost_allocation_method == 'direct':
                # Direct costs only - simplified allocation
                for group_key in revenue_analysis.keys():
                    cost_analysis[group_key] = total_costs / len(revenue_analysis) if revenue_analysis else Decimal('0.00')

            elif cost_allocation_method == 'proportional':
                # Allocate costs proportionally based on revenue
                for group_key, group_data in revenue_analysis.items():
                    if total_revenue > 0:
                        cost_proportion = group_data['total_revenue'] / total_revenue
                        cost_analysis[group_key] = total_costs * cost_proportion
                    else:
                        cost_analysis[group_key] = Decimal('0.00')

            else:  # activity_based
                # Activity-based costing - allocate based on student count
                total_students = sum(group_data['student_count'] for group_data in revenue_analysis.values())
                for group_key, group_data in revenue_analysis.items():
                    if total_students > 0:
                        cost_proportion = group_data['student_count'] / total_students
                        cost_analysis[group_key] = total_costs * cost_proportion
                    else:
                        cost_analysis[group_key] = Decimal('0.00')

            # 7. Calculate profitability metrics
            profitability_analysis = []
            total_profit = Decimal('0.00')

            for group_key, group_data in revenue_analysis.items():
                allocated_costs = cost_analysis.get(group_key, Decimal('0.00'))
                gross_profit = group_data['total_revenue'] - allocated_costs

                # Calculate profitability metrics
                profit_margin = (gross_profit / group_data['total_revenue'] * 100) if group_data['total_revenue'] > 0 else Decimal('0.00')
                revenue_per_student = group_data['total_revenue'] / group_data['student_count'] if group_data['student_count'] > 0 else Decimal('0.00')
                cost_per_student = allocated_costs / group_data['student_count'] if group_data['student_count'] > 0 else Decimal('0.00')
                profit_per_student = gross_profit / group_data['student_count'] if group_data['student_count'] > 0 else Decimal('0.00')

                # Determine profitability status
                if profit_margin >= profitability_threshold:
                    profitability_status = 'Highly Profitable'
                elif profit_margin >= profitability_threshold / 2:
                    profitability_status = 'Profitable'
                elif profit_margin >= 0:
                    profitability_status = 'Break-even'
                else:
                    profitability_status = 'Loss-making'

                profitability_item = {
                    'group_name': group_key,
                    'total_revenue': group_data['total_revenue'],
                    'allocated_costs': allocated_costs,
                    'gross_profit': gross_profit,
                    'profit_margin': profit_margin,
                    'student_count': group_data['student_count'],
                    'invoice_count': group_data['invoice_count'],
                    'collection_rate': group_data['collection_rate'],
                    'revenue_per_student': revenue_per_student,
                    'cost_per_student': cost_per_student,
                    'profit_per_student': profit_per_student,
                    'profitability_status': profitability_status,
                    'meets_threshold': profit_margin >= profitability_threshold
                }

                profitability_analysis.append(profitability_item)
                total_profit += gross_profit

            # Sort by profit margin (descending)
            profitability_analysis.sort(key=lambda x: x['profit_margin'], reverse=True)

            # 8. Calculate summary statistics
            overall_profit_margin = (total_profit / total_revenue * 100) if total_revenue > 0 else Decimal('0.00')
            profitable_groups = len([item for item in profitability_analysis if item['gross_profit'] > 0])

            summary = {
                'total_revenue': total_revenue,
                'total_costs': total_costs,
                'total_profit': total_profit,
                'overall_profit_margin': overall_profit_margin,
                'total_groups': len(profitability_analysis),
                'profitable_groups': profitable_groups,
                'loss_making_groups': len(profitability_analysis) - profitable_groups,
                'avg_profit_margin': sum(item['profit_margin'] for item in profitability_analysis) / len(profitability_analysis) if profitability_analysis else Decimal('0.00'),
                'highest_margin': max((item['profit_margin'] for item in profitability_analysis), default=Decimal('0.00')),
                'lowest_margin': min((item['profit_margin'] for item in profitability_analysis), default=Decimal('0.00'))
            }

            # 9. Cost breakdown analysis
            cost_breakdown = [
                {
                    'cost_type': 'Direct Costs',
                    'amount': total_costs * Decimal('0.7'),  # Simplified assumption
                    'percentage': Decimal('70.0')
                },
                {
                    'cost_type': 'Indirect Costs',
                    'amount': total_costs * Decimal('0.3'),  # Simplified assumption
                    'percentage': Decimal('30.0')
                }
            ] if include_indirect_costs else [
                {
                    'cost_type': 'Direct Costs',
                    'amount': total_costs,
                    'percentage': Decimal('100.0')
                }
            ]

            data.update({
                'profitability_analysis': profitability_analysis,
                'cost_breakdown': cost_breakdown,
                'summary': summary
            })

        return data

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        report_data = self._get_profitability_analysis_data(self.request)
        context.update(report_data)
        return context


class ProfitabilityAnalysisExportCSVView(ProfitabilityAnalysisReportView):
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        import csv
        from django.http import HttpResponse

        report_data = self._get_profitability_analysis_data(request)

        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename="profitability_analysis_report.csv"'

        writer = csv.writer(response)
        writer.writerow([self.report_title])

        if report_data['academic_year']:
            writer.writerow([f"Academic Year: {report_data['academic_year']}"])
        writer.writerow([f"Analysis Type: {report_data['analysis_type'].title()}"])
        writer.writerow([f"Cost Allocation: {report_data['cost_allocation_method'].title()}"])
        writer.writerow([])

        # Summary
        summary = report_data['summary']
        writer.writerow(['Summary'])
        writer.writerow(['Total Revenue', summary.get('total_revenue', 0)])
        writer.writerow(['Total Costs', summary.get('total_costs', 0)])
        writer.writerow(['Total Profit', summary.get('total_profit', 0)])
        writer.writerow(['Overall Profit Margin (%)', summary.get('overall_profit_margin', 0)])
        writer.writerow(['Profitable Groups', summary.get('profitable_groups', 0)])
        writer.writerow(['Loss-making Groups', summary.get('loss_making_groups', 0)])
        writer.writerow([])

        # Profitability Analysis
        writer.writerow(['Profitability Analysis'])
        writer.writerow(['Group', 'Revenue', 'Costs', 'Profit', 'Profit Margin (%)', 'Students', 'Revenue per Student', 'Profit per Student', 'Status'])

        for item in report_data['profitability_analysis']:
            writer.writerow([
                item['group_name'],
                item['total_revenue'],
                item['allocated_costs'],
                item['gross_profit'],
                f"{item['profit_margin']:.2f}",
                item['student_count'],
                item['revenue_per_student'],
                item['profit_per_student'],
                item['profitability_status']
            ])

        return response


class ProfitabilityAnalysisExportPDFView(ProfitabilityAnalysisReportView):
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        report_data = self._get_profitability_analysis_data(request)

        report_data['school_profile'] = SchoolProfile.objects.first()
        report_data['tenant'] = request.tenant
        report_data['report_title'] = self.report_title

        pdf = render_to_pdf('reporting/pdf/profitability_analysis_pdf.html', report_data)
        if not pdf:
            messages.error(request, "There was an error generating the PDF report.")
            return redirect('reporting:profitability_analysis_report')

        response = HttpResponse(pdf, content_type='application/pdf')
        response['Content-Disposition'] = 'inline; filename="profitability_analysis_report.pdf"'
        return response


class ProfitabilityAnalysisExportExcelView(ProfitabilityAnalysisReportView):
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        try:
            import openpyxl
            from openpyxl.styles import Font, PatternFill
        except ImportError:
            messages.error(request, "The library required for Excel export (openpyxl) is not installed.")
            return redirect('reporting:profitability_analysis_report')

        report_data = self._get_profitability_analysis_data(request)

        # Create workbook and worksheet
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "Profitability Analysis"

        # Add headers
        ws['A1'] = self.report_title
        ws['A1'].font = Font(bold=True, size=14)

        row = 2
        if report_data['academic_year']:
            ws[f'A{row}'] = f"Academic Year: {report_data['academic_year']}"
            row += 1
        ws[f'A{row}'] = f"Analysis Type: {report_data['analysis_type'].title()}"
        row += 1
        ws[f'A{row}'] = f"Cost Allocation: {report_data['cost_allocation_method'].title()}"
        row += 2

        # Summary
        ws[f'A{row}'] = 'Summary'
        ws[f'A{row}'].font = Font(bold=True)
        row += 1

        summary = report_data['summary']
        summary_data = [
            ('Total Revenue', float(summary.get('total_revenue', 0))),
            ('Total Costs', float(summary.get('total_costs', 0))),
            ('Total Profit', float(summary.get('total_profit', 0))),
            ('Overall Profit Margin (%)', float(summary.get('overall_profit_margin', 0))),
            ('Profitable Groups', summary.get('profitable_groups', 0)),
            ('Loss-making Groups', summary.get('loss_making_groups', 0))
        ]

        for label, value in summary_data:
            ws[f'A{row}'] = label
            ws[f'B{row}'] = value
            row += 1

        # Profitability Analysis
        row += 2
        ws[f'A{row}'] = 'Profitability Analysis'
        ws[f'A{row}'].font = Font(bold=True)
        row += 1

        headers = ['Group', 'Revenue', 'Costs', 'Profit', 'Profit Margin (%)', 'Students', 'Revenue per Student', 'Profit per Student', 'Status']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=row, column=col, value=header)
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
        row += 1

        for item in report_data['profitability_analysis']:
            ws.cell(row=row, column=1, value=item['group_name'])
            ws.cell(row=row, column=2, value=float(item['total_revenue']))
            ws.cell(row=row, column=3, value=float(item['allocated_costs']))
            ws.cell(row=row, column=4, value=float(item['gross_profit']))
            ws.cell(row=row, column=5, value=f"{float(item['profit_margin']):.2f}%")
            ws.cell(row=row, column=6, value=item['student_count'])
            ws.cell(row=row, column=7, value=float(item['revenue_per_student']))
            ws.cell(row=row, column=8, value=float(item['profit_per_student']))
            ws.cell(row=row, column=9, value=item['profitability_status'])
            row += 1

        # Save to response
        response = HttpResponse(content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        response['Content-Disposition'] = 'attachment; filename="profitability_analysis_report.xlsx"'
        wb.save(response)
        return response


# ========================================
# FINANCIAL RATIO ANALYSIS REPORT
# ========================================
class FinancialRatioAnalysisReportView(TenantPermissionRequiredMixin, BaseReportViewMixin, TemplateView):
    """
    Handles the display of the HTML Financial Ratio Analysis report page.
    """
    template_name = 'reporting/financial_ratio_analysis_report.html'

    # Attributes for BaseReportViewMixin
    permission_required = 'reporting.view_financial_ratio_analysis_report'
    report_title = _("Financial Ratio Analysis Report")
    filter_form_class = FinancialRatioAnalysisFilterForm
    export_csv_url_name = 'reporting:financial_ratio_analysis_export_csv'
    export_pdf_url_name = 'reporting:financial_ratio_analysis_export_pdf'
    export_excel_url_name = 'reporting:financial_ratio_analysis_export_excel'
    paginate_by = 50

    def _get_financial_ratio_analysis_data(self, request):
        """
        The core logic for fetching and processing all data for the Financial Ratio Analysis Report.
        """
        from apps.fees.models import Invoice
        from apps.payments.models import Payment
        from apps.accounting.models import Account, JournalEntry, JournalLine
        from decimal import Decimal
        import math

        # 1. Initialize the data structure
        data = {
            'ratio_analysis': {}, 'trend_data': [], 'risk_assessment': {},
            'filter_form': self.filter_form_class(request.GET or None),
            'analysis_period': 'current_year', 'ratio_categories': ['liquidity', 'profitability', 'efficiency']
        }

        # 2. Process form data
        filter_form = data['filter_form']
        if filter_form.is_valid():
            analysis_period = filter_form.cleaned_data.get('analysis_period', 'current_year')
            ratio_categories = filter_form.cleaned_data.get('ratio_categories', ['liquidity', 'profitability', 'efficiency'])
            benchmark_comparison = filter_form.cleaned_data.get('benchmark_comparison', True)
            trend_analysis = filter_form.cleaned_data.get('trend_analysis', True)
            risk_assessment = filter_form.cleaned_data.get('risk_assessment', True)

            data.update({
                'analysis_period': analysis_period,
                'ratio_categories': ratio_categories,
                'benchmark_comparison': benchmark_comparison,
                'trend_analysis': trend_analysis,
                'risk_assessment': risk_assessment
            })

            # 3. Get financial data for ratio calculations
            today = timezone.now().date()

            # Determine analysis period dates
            if analysis_period == 'current_year':
                try:
                    current_year = AcademicYear.objects.filter(
                        start_date__lte=today,
                        end_date__gte=today
                    ).first()
                    if current_year:
                        start_date = current_year.start_date
                        end_date = current_year.end_date
                    else:
                        start_date = today.replace(month=1, day=1)
                        end_date = today
                except:
                    start_date = today.replace(month=1, day=1)
                    end_date = today
            elif analysis_period == 'last_year':
                try:
                    last_year = AcademicYear.objects.filter(
                        end_date__lt=today
                    ).order_by('-end_date').first()
                    if last_year:
                        start_date = last_year.start_date
                        end_date = last_year.end_date
                    else:
                        start_date = today.replace(year=today.year-1, month=1, day=1)
                        end_date = today.replace(year=today.year-1, month=12, day=31)
                except:
                    start_date = today.replace(year=today.year-1, month=1, day=1)
                    end_date = today.replace(year=today.year-1, month=12, day=31)
            else:  # quarterly or comparative
                start_date = today.replace(month=1, day=1)
                end_date = today

            # 4. Calculate basic financial metrics
            # Revenue (from payments)
            total_revenue = Payment.objects.filter(
                payment_date__gte=start_date,
                payment_date__lte=end_date,
                status='COMPLETED'
            ).aggregate(total=Coalesce(Sum('amount'), Decimal('0.00')))['total']

            # Outstanding receivables
            outstanding_receivables = Decimal('0.00')
            invoices = Invoice.objects.filter(issue_date__lte=end_date)
            for invoice in invoices:
                paid_amount = invoice.payments.filter(
                    status='COMPLETED',
                    payment_date__lte=end_date
                ).aggregate(total=Coalesce(Sum('amount'), Decimal('0.00')))['total']
                outstanding_receivables += max(invoice.total_amount - paid_amount, Decimal('0.00'))

            # Total assets (simplified - cash + receivables)
            cash_balance = total_revenue  # Simplified assumption
            total_assets = cash_balance + outstanding_receivables

            # Total expenses (from journal entries)
            total_expenses = Decimal('0.00')
            expense_entries = JournalEntry.objects.filter(
                date__gte=start_date,
                date__lte=end_date,
                status=JournalEntry.StatusChoices.POSTED
            )
            for entry in expense_entries:
                for line in entry.lines.all():
                    if line.debit_amount > 0:  # Assuming expenses are debits
                        total_expenses += line.debit_amount

            # Net income
            net_income = total_revenue - total_expenses

            # Student count (for per-student metrics)
            student_count = Invoice.objects.filter(
                issue_date__gte=start_date,
                issue_date__lte=end_date
            ).values('student').distinct().count()

            # 5. Calculate financial ratios by category
            ratio_analysis = {}

            # Liquidity Ratios
            if 'liquidity' in ratio_categories:
                current_ratio = (cash_balance / total_expenses * 12) if total_expenses > 0 else Decimal('0.00')  # Months of expenses covered
                quick_ratio = (cash_balance / (total_expenses / 12)) if total_expenses > 0 else Decimal('0.00')  # Monthly expense coverage
                cash_ratio = (cash_balance / total_revenue) if total_revenue > 0 else Decimal('0.00')

                ratio_analysis['liquidity'] = {
                    'category_name': 'Liquidity Ratios',
                    'ratios': [
                        {
                            'name': 'Current Ratio',
                            'value': current_ratio,
                            'benchmark': Decimal('3.0'),  # 3 months of expenses
                            'description': 'Months of expenses covered by cash',
                            'interpretation': 'Higher is better - indicates ability to meet short-term obligations',
                            'status': 'Good' if current_ratio >= 3 else 'Fair' if current_ratio >= 1.5 else 'Poor'
                        },
                        {
                            'name': 'Quick Ratio',
                            'value': quick_ratio,
                            'benchmark': Decimal('1.0'),
                            'description': 'Monthly expense coverage ratio',
                            'interpretation': 'Ability to cover monthly expenses with available cash',
                            'status': 'Good' if quick_ratio >= 1 else 'Fair' if quick_ratio >= 0.5 else 'Poor'
                        },
                        {
                            'name': 'Cash Ratio',
                            'value': cash_ratio,
                            'benchmark': Decimal('0.2'),  # 20% cash to revenue
                            'description': 'Cash as percentage of revenue',
                            'interpretation': 'Cash management efficiency',
                            'status': 'Good' if cash_ratio >= 0.15 else 'Fair' if cash_ratio >= 0.1 else 'Poor'
                        }
                    ]
                }

            # Profitability Ratios
            if 'profitability' in ratio_categories:
                gross_margin = (net_income / total_revenue * 100) if total_revenue > 0 else Decimal('0.00')
                net_margin = (net_income / total_revenue * 100) if total_revenue > 0 else Decimal('0.00')
                roa = (net_income / total_assets * 100) if total_assets > 0 else Decimal('0.00')

                ratio_analysis['profitability'] = {
                    'category_name': 'Profitability Ratios',
                    'ratios': [
                        {
                            'name': 'Gross Margin (%)',
                            'value': gross_margin,
                            'benchmark': Decimal('15.0'),
                            'description': 'Net income as percentage of revenue',
                            'interpretation': 'Overall profitability of operations',
                            'status': 'Good' if gross_margin >= 15 else 'Fair' if gross_margin >= 5 else 'Poor'
                        },
                        {
                            'name': 'Net Margin (%)',
                            'value': net_margin,
                            'benchmark': Decimal('10.0'),
                            'description': 'Net profit margin after all expenses',
                            'interpretation': 'Bottom-line profitability',
                            'status': 'Good' if net_margin >= 10 else 'Fair' if net_margin >= 0 else 'Poor'
                        },
                        {
                            'name': 'Return on Assets (%)',
                            'value': roa,
                            'benchmark': Decimal('8.0'),
                            'description': 'Return generated on total assets',
                            'interpretation': 'Efficiency in using assets to generate profit',
                            'status': 'Good' if roa >= 8 else 'Fair' if roa >= 3 else 'Poor'
                        }
                    ]
                }

            # Efficiency Ratios
            if 'efficiency' in ratio_categories:
                revenue_per_student = total_revenue / student_count if student_count > 0 else Decimal('0.00')
                cost_per_student = total_expenses / student_count if student_count > 0 else Decimal('0.00')
                collection_efficiency = ((total_revenue / (total_revenue + outstanding_receivables)) * 100) if (total_revenue + outstanding_receivables) > 0 else Decimal('0.00')

                ratio_analysis['efficiency'] = {
                    'category_name': 'Efficiency Ratios',
                    'ratios': [
                        {
                            'name': 'Revenue per Student',
                            'value': revenue_per_student,
                            'benchmark': Decimal('50000.0'),  # Example benchmark
                            'description': 'Average revenue generated per student',
                            'interpretation': 'Revenue generation efficiency',
                            'status': 'Good' if revenue_per_student >= 50000 else 'Fair' if revenue_per_student >= 30000 else 'Poor'
                        },
                        {
                            'name': 'Cost per Student',
                            'value': cost_per_student,
                            'benchmark': Decimal('40000.0'),  # Example benchmark
                            'description': 'Average cost per student',
                            'interpretation': 'Cost management efficiency',
                            'status': 'Good' if cost_per_student <= 40000 else 'Fair' if cost_per_student <= 50000 else 'Poor'
                        },
                        {
                            'name': 'Collection Efficiency (%)',
                            'value': collection_efficiency,
                            'benchmark': Decimal('90.0'),
                            'description': 'Percentage of receivables collected',
                            'interpretation': 'Effectiveness of collection processes',
                            'status': 'Good' if collection_efficiency >= 90 else 'Fair' if collection_efficiency >= 75 else 'Poor'
                        }
                    ]
                }

            # 6. Risk Assessment
            if risk_assessment:
                risk_factors = []
                overall_risk_score = 0

                # Analyze each ratio for risk
                for category_data in ratio_analysis.values():
                    for ratio in category_data['ratios']:
                        if ratio['status'] == 'Poor':
                            risk_factors.append(f"Poor {ratio['name']}: {ratio['value']}")
                            overall_risk_score += 3
                        elif ratio['status'] == 'Fair':
                            overall_risk_score += 1

                # Determine overall risk level
                if overall_risk_score <= 3:
                    risk_level = 'Low'
                elif overall_risk_score <= 6:
                    risk_level = 'Medium'
                else:
                    risk_level = 'High'

                risk_assessment_data = {
                    'risk_level': risk_level,
                    'risk_score': overall_risk_score,
                    'risk_factors': risk_factors,
                    'recommendations': []
                }

                # Add recommendations based on risk factors
                if 'Poor Current Ratio' in str(risk_factors):
                    risk_assessment_data['recommendations'].append('Improve cash flow management and build reserves')
                if 'Poor Net Margin' in str(risk_factors):
                    risk_assessment_data['recommendations'].append('Review cost structure and pricing strategy')
                if 'Poor Collection Efficiency' in str(risk_factors):
                    risk_assessment_data['recommendations'].append('Strengthen fee collection processes')

                data['risk_assessment'] = risk_assessment_data

            data['ratio_analysis'] = ratio_analysis

        return data

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        report_data = self._get_financial_ratio_analysis_data(self.request)
        context.update(report_data)
        return context


class FinancialRatioAnalysisExportCSVView(FinancialRatioAnalysisReportView):
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        import csv
        from django.http import HttpResponse

        report_data = self._get_financial_ratio_analysis_data(request)

        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename="financial_ratio_analysis_report.csv"'

        writer = csv.writer(response)
        writer.writerow([self.report_title])
        writer.writerow([f"Analysis Period: {report_data['analysis_period'].title()}"])
        writer.writerow([f"Categories: {', '.join(report_data['ratio_categories'])}"])
        writer.writerow([])

        # Ratio Analysis
        for category_key, category_data in report_data['ratio_analysis'].items():
            writer.writerow([category_data['category_name']])
            writer.writerow(['Ratio Name', 'Value', 'Benchmark', 'Status', 'Description'])

            for ratio in category_data['ratios']:
                writer.writerow([
                    ratio['name'],
                    ratio['value'],
                    ratio['benchmark'],
                    ratio['status'],
                    ratio['description']
                ])
            writer.writerow([])

        # Risk Assessment
        if 'risk_assessment' in report_data and report_data['risk_assessment']:
            risk_data = report_data['risk_assessment']
            writer.writerow(['Risk Assessment'])
            writer.writerow(['Risk Level', risk_data['risk_level']])
            writer.writerow(['Risk Score', risk_data['risk_score']])
            writer.writerow([])

            if risk_data['risk_factors']:
                writer.writerow(['Risk Factors'])
                for factor in risk_data['risk_factors']:
                    writer.writerow([factor])
                writer.writerow([])

            if risk_data['recommendations']:
                writer.writerow(['Recommendations'])
                for rec in risk_data['recommendations']:
                    writer.writerow([rec])

        return response


class FinancialRatioAnalysisExportPDFView(FinancialRatioAnalysisReportView):
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        report_data = self._get_financial_ratio_analysis_data(request)

        report_data['school_profile'] = SchoolProfile.objects.first()
        report_data['tenant'] = request.tenant
        report_data['report_title'] = self.report_title

        pdf = render_to_pdf('reporting/pdf/financial_ratio_analysis_pdf.html', report_data)
        if not pdf:
            messages.error(request, "There was an error generating the PDF report.")
            return redirect('reporting:financial_ratio_analysis_report')

        response = HttpResponse(pdf, content_type='application/pdf')
        response['Content-Disposition'] = 'inline; filename="financial_ratio_analysis_report.pdf"'
        return response


class FinancialRatioAnalysisExportExcelView(FinancialRatioAnalysisReportView):
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        try:
            import openpyxl
            from openpyxl.styles import Font, PatternFill
        except ImportError:
            messages.error(request, "The library required for Excel export (openpyxl) is not installed.")
            return redirect('reporting:financial_ratio_analysis_report')

        report_data = self._get_financial_ratio_analysis_data(request)

        # Create workbook and worksheet
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "Financial Ratios"

        # Add headers
        ws['A1'] = self.report_title
        ws['A1'].font = Font(bold=True, size=14)
        ws['A2'] = f"Analysis Period: {report_data['analysis_period'].title()}"
        ws['A3'] = f"Categories: {', '.join(report_data['ratio_categories'])}"

        row = 5

        # Ratio Analysis
        for category_key, category_data in report_data['ratio_analysis'].items():
            ws[f'A{row}'] = category_data['category_name']
            ws[f'A{row}'].font = Font(bold=True)
            row += 1

            headers = ['Ratio Name', 'Value', 'Benchmark', 'Status', 'Description']
            for col, header in enumerate(headers, 1):
                cell = ws.cell(row=row, column=col, value=header)
                cell.font = Font(bold=True)
                cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
            row += 1

            for ratio in category_data['ratios']:
                ws.cell(row=row, column=1, value=ratio['name'])
                ws.cell(row=row, column=2, value=float(ratio['value']))
                ws.cell(row=row, column=3, value=float(ratio['benchmark']))
                ws.cell(row=row, column=4, value=ratio['status'])
                ws.cell(row=row, column=5, value=ratio['description'])
                row += 1

            row += 1  # Add space between categories

        # Risk Assessment
        if 'risk_assessment' in report_data and report_data['risk_assessment']:
            risk_data = report_data['risk_assessment']
            row += 1
            ws[f'A{row}'] = 'Risk Assessment'
            ws[f'A{row}'].font = Font(bold=True)
            row += 1

            ws[f'A{row}'] = 'Risk Level'
            ws[f'B{row}'] = risk_data['risk_level']
            row += 1

            ws[f'A{row}'] = 'Risk Score'
            ws[f'B{row}'] = risk_data['risk_score']
            row += 2

            if risk_data['recommendations']:
                ws[f'A{row}'] = 'Recommendations'
                ws[f'A{row}'].font = Font(bold=True)
                row += 1

                for rec in risk_data['recommendations']:
                    ws[f'A{row}'] = rec
                    row += 1

        # Save to response
        response = HttpResponse(content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        response['Content-Disposition'] = 'attachment; filename="financial_ratio_analysis_report.xlsx"'
        wb.save(response)
        return response

