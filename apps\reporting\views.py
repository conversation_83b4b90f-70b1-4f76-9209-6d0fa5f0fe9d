# Python Standard Library Imports
import csv
from decimal import Decimal, InvalidOperation
from io import BytesIO
import logging

# Django Imports
from django.core.paginator import Paginator
from django.db.models import Sum, F, Q, OuterRef, Subquery, ExpressionWrapper, Dec<PERSON>l<PERSON>ield, Count
from django.db.models.functions import Coalesce
from django.http import HttpResponse
from django.shortcuts import redirect
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django.views.generic import TemplateView
from django.contrib import messages

# Third-Party Imports
try:
    import openpyxl
    from openpyxl.styles import Font, Alignment
    from openpyxl.utils import get_column_letter
except ImportError:
    openpyxl = None

# --- CORRECTED MODEL IMPORTS FROM SPECIFIC APPS ---
# Note: Adjust these paths if your app names are different.
from apps.accounting.models import Account, AccountType, JournalEntryItem
from apps.fees.models import Invoice, InvoiceStatus, FeeStructure, StudentConcession
from apps.payments.models import Payment
from apps.finance.models import PaymentMethod, Expense, ExpenseCategory, BudgetAmount, BudgetItem
from apps.schools.models import SchoolClass, AcademicYear, SchoolProfile
from apps.students.models import Student

# --- LOCAL APP IMPORTS ---
from apps.common.mixins import TenantPermissionRequiredMixin, BaseReportViewMixin
from .forms import (
    StudentLedgerFilterForm, BudgetReportFilterForm, DateRangeForm,
    BalanceSheetFilterForm, OutstandingFeesFilterForm, CollectionReportFilterForm,
    ReportPeriodForm, IncomeExpenseReportForm, ExpenseReportFilterForm,
    FeeProjectionFilterForm
)
from .filters import PaymentReportFilter
from apps.common.utils import render_to_pdf, get_financial_year_start

logger = logging.getLogger(__name__)


# # D:\school_fees_saas_v2\apps\reporting\views.py

# from django.shortcuts import render, redirect, get_object_or_404
# from django.views.generic import ListView, TemplateView
# from django.db.models import (
#     Sum, F, Q, ExpressionWrapper, DecimalField, Case, When, Value, Count, OuterRef, Subquery
# )

# from django.db.models import Sum, Q, Case, When, Value, DecimalField, CharField
# from django.http import HttpResponse

# from django.db.models.functions import Coalesce
# from django.utils import timezone
# from django.http import HttpResponse, Http404
# from django.urls import reverse_lazy, reverse
# from django.contrib import messages
# from decimal import Decimal, InvalidOperation
# import csv
# import openpyxl
# from openpyxl.utils import get_column_letter
# from openpyxl.styles import Font, Alignment, Border, Side, PatternFill
# from io import BytesIO
# import logging
# from django.conf import settings # For accounting codes

# # --- Authentication & Permissions ---
# from django.views.generic.edit import FormMixin
# from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin

# # --- Model Imports ---
# from apps.students.models import Student
# from apps.fees.models import Invoice, AcademicYear, Term, StudentConcession
# from apps.payments.models import Payment, PaymentMethod
# from apps.finance.models import Expense, BudgetAmount, BudgetItem, ExpenseCategory, Vendor
# from apps.accounting.models import Account as ChartOfAccount, Account, JournalLine, AccountType
# from apps.schools.models import SchoolProfile, SchoolClass

# # --- Forms (from this reporting app) ---
# from .forms import (
#     DateRangeForm,
#     DateRangeClassTermForm,
#     DateRangeAccountForm,
#     IncomeExpenseReportForm,
#     BudgetVarianceReportForm,
#     ReportPeriodForm,
#     BudgetReportFilterForm
    
# )

# from apps.fees.models import Invoice
# from apps.finance.models import Expense, Budget
# from apps.payments.models import Payment
# from apps.common.utils import render_to_pdf, PDF_AVAILABLE # Ensure PDF_AVAILABLE is handled
# from .forms import (
#     BalanceSheetFilterForm,
#     IncomeStatementFilterForm,
#     TrialBalanceFilterForm,
#     AgedReceivablesFilterForm,
#     AgedPayablesFilterForm,
#     CashFlowFilterForm, # Assuming you have this
#     # ... any other report filter forms
# )

# # from django.views.generic import ListView # Assuming it's a ListView
# # from apps.common.mixins import StaffLoginRequiredMixin, TenantPermissionRequiredMixin, BaseReportViewMixin # Your mixins
# # from apps.payments.models import Payment # Example model for collection report
# # from apps.fees.models import Invoice # Example
# # from .forms import CollectionReportFilter 

# from apps.fees.models import Invoice, ConcessionType as Concession

# from apps.accounting.models import AccountType, Account, GeneralLedger

# from django.db.models.query import QuerySet

# # CHANGE THIS IMPORT
# from .forms import StudentLedgerFilterForm

# logger = logging.getLogger(__name__)

# # ==============================================================================
# # Base Report View Mixin -----MAIN ONE
# # ==============================================================================

# # D:\school_fees_saas_v2\apps\common\mixins.py (or wherever BaseReportViewMixin is)
# from django import forms
# from django.utils import timezone
# from django.utils.translation import gettext_lazy as _
# # ... other necessary imports ...
# import logging
# logger = logging.getLogger(__name__)



# = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = 
#
#      OUTSTANDING FEES REPORT
#
# = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =


# # Python Standard Library Imports
# import csv
# from decimal import Decimal, InvalidOperation
# from io import BytesIO
# import logging

# # Django Imports
# from django.core.paginator import Paginator
# from django.db.models import Sum, F, Q, OuterRef, Subquery, ExpressionWrapper, DecimalField
# from django.db.models.functions import Coalesce
# from django.http import HttpResponse
# from django.shortcuts import redirect
# from django.utils import timezone
# from django.utils.translation import gettext_lazy as _
# from django.views.generic import TemplateView
# from django.contrib import messages

# # Third-Party Imports
# try:
#     import openpyxl
#     from openpyxl.styles import Font
#     from openpyxl.utils import get_column_letter
# except ImportError:
#     openpyxl = None

# # Local Application Imports
# from apps.common.mixins import TenantPermissionRequiredMixin, BaseReportViewMixin
# from .forms import OutstandingFeesFilterForm # Assumes you create this form
# from .models import Student, Invoice, InvoiceStatus, SchoolClass # Your app's models
# from .utils import render_to_pdf
# from schools.models import SchoolProfile

# logger = logging.getLogger(__name__)


class OutstandingFeesReportView(TenantPermissionRequiredMixin, BaseReportViewMixin, TemplateView):
    """
    Handles the display of the HTML Outstanding Fees report page.
    """
    template_name = 'reporting/outstanding_fees_report.html'
    
    # Attributes for BaseReportViewMixin
    permission_required = 'reporting.view_outstanding_fees_report'
    report_title = _("Outstanding Fees Report")
    filter_form_class = OutstandingFeesFilterForm # Using a dedicated form now
    export_csv_url_name = 'reporting:outstanding_fees_export_csv'
    export_pdf_url_name = 'reporting:outstanding_fees_export_pdf'
    export_excel_url_name = 'reporting:outstanding_fees_export_excel'
    paginate_by = 25 # Define pagination size here

    def _get_outstanding_fees_data(self, request):
        """
        The core logic for fetching and processing all data for the Outstanding Fees report.
        This is used by the HTML view and all export views.
        """
        # 1. Initialize the data structure
        data = {
            'students_page': None, 'full_queryset': Student.objects.none(),
            'total_outstanding': Decimal('0.00'), 'filter_form': self.filter_form_class(request.GET or None)
        }
        
        # 2. Base queryset and filters
        base_queryset = Student.objects.filter(is_active=True).select_related(
            'current_class', 'current_section'
        ).prefetch_related('parents')

        if data['filter_form'].is_valid():
            class_filter = data['filter_form'].cleaned_data.get('school_class')
            if class_filter:
                base_queryset = base_queryset.filter(current_class=class_filter)
        
        # 3. Define outstanding statuses and perform annotations
        outstanding_statuses = [InvoiceStatus.SENT, InvoiceStatus.PARTIALLY_PAID, InvoiceStatus.OVERDUE]
        
        # Subquery to sum the calculated balance of outstanding invoices for each student
        student_outstanding_balance = Invoice.objects.filter(
            student_id=OuterRef('pk'), status__in=outstanding_statuses
        ).values('student_id').annotate(
            total_due=Sum(
                (F('subtotal_amount') - F('total_concession_amount')) - F('amount_paid')
            )
        ).values('total_due')

        annotated_queryset = base_queryset.annotate(
            outstanding_balance=Coalesce(
                Subquery(student_outstanding_balance, output_field=DecimalField()),
                Decimal('0.00')
            )
        )
        
        # 4. Filter based on calculated balance and form fields
        final_queryset = annotated_queryset.filter(outstanding_balance__gt=Decimal('0.01'))

        if data['filter_form'].is_valid():
            min_due_amount = data['filter_form'].cleaned_data.get('min_due_amount')
            if min_due_amount:
                final_queryset = final_queryset.filter(outstanding_balance__gte=min_due_amount)

        final_queryset = final_queryset.order_by('current_class__name', 'full_name').distinct()
        
        data['full_queryset'] = final_queryset

        # 5. Calculate grand total from the full (un-paginated) queryset
        aggregation = final_queryset.aggregate(total=Sum('outstanding_balance'))
        data['total_outstanding'] = aggregation.get('total') or Decimal('0.00')
        
        # 6. Paginate the results for the HTML view
        paginator = Paginator(final_queryset, self.paginate_by)
        page_number = request.GET.get('page', 1)
        data['students_page'] = paginator.get_page(page_number)
        
        return data

    def get_context_data(self, **kwargs):
        """Prepares context for the HTML page by calling the main data helper."""
        context = super().get_context_data(**kwargs)
        report_data = self._get_outstanding_fees_data(self.request)
        context.update(report_data)
        
        # Add additional context if needed
        context['school_profile'] = SchoolProfile.objects.first()
        
        return context


class OutstandingFeesExportCSVView(OutstandingFeesReportView):
    """Handles CSV export for the Outstanding Fees report."""
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        report_data = self._get_outstanding_fees_data(request)
        queryset = report_data.get('full_queryset')

        response = HttpResponse(content_type='text/csv')
        filename = f"Outstanding_Fees_{timezone.now().strftime('%Y%m%d')}.csv"
        response['Content-Disposition'] = f'attachment; filename="{filename}"'
        writer = csv.writer(response)
        
        headers = ['Admission No', 'Full Name', 'Class', 'Primary Parent', 'Parent Phone', 'Outstanding Balance']
        writer.writerow(headers)

        for student in queryset:
            parent = student.parents.first()
            writer.writerow([
                student.admission_number, student.full_name,
                student.current_class.name if student.current_class else 'N/A',
                parent.get_full_name() if parent else 'N/A',
                parent.phone_number if parent else 'N/A',
                student.outstanding_balance
            ])
        return response


class OutstandingFeesExportExcelView(OutstandingFeesReportView):
    """Handles Excel export for the Outstanding Fees report."""
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        if not openpyxl:
            messages.error(request, "Excel export library not available.")
            return redirect('reporting:outstanding_fees_report')

        report_data = self._get_outstanding_fees_data(request)
        queryset = report_data.get('full_queryset')
        school_profile = SchoolProfile.objects.first()
        currency_symbol = school_profile.currency_symbol if school_profile else '$'
        currency_format = f'"{currency_symbol}"#,##0.00'

        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "Outstanding Fees"
        
        headers = ['Admission No', 'Full Name', 'Class', 'Primary Parent', 'Parent Phone', 'Outstanding Balance']
        ws.append(headers)
        header_font = Font(bold=True)
        for cell in ws[1]:
            cell.font = header_font

        for student in queryset:
            parent = student.parents.first()
            row_data = [
                student.admission_number, student.full_name,
                student.current_class.name if student.current_class else 'N/A',
                parent.get_full_name() if parent else 'N/A',
                parent.phone_number if parent else 'N/A',
                student.outstanding_balance
            ]
            ws.append(row_data)
        
        # Formatting
        ws.column_dimensions[get_column_letter(1)].width = 15
        ws.column_dimensions[get_column_letter(2)].width = 25
        ws.column_dimensions[get_column_letter(3)].width = 20
        ws.column_dimensions[get_column_letter(4)].width = 25
        ws.column_dimensions[get_column_letter(5)].width = 20
        ws.column_dimensions[get_column_letter(6)].width = 20
        for cell in ws['F']:
            cell.number_format = currency_format

        response = HttpResponse(content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        filename = f"Outstanding_Fees_{timezone.now().strftime('%Y%m%d')}.xlsx"
        response['Content-Disposition'] = f'attachment; filename="{filename}"'
        wb.save(response)
        return response


class OutstandingFeesExportPDFView(OutstandingFeesReportView):
    """Handles PDF export for the Outstanding Fees report."""
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        report_data = self._get_outstanding_fees_data(request)
        
        pdf_context = report_data.copy()
        pdf_context.update({
            'report_title': self.report_title,
            'school_profile': SchoolProfile.objects.first(),
            'tenant': getattr(request, 'tenant', None),
            'current_datetime': timezone.now()
        })
        
        try:
            pdf = render_to_pdf('reporting/pdf/outstanding_fees_pdf.html', pdf_context)
            if pdf:
                response = HttpResponse(pdf, content_type='application/pdf')
                filename = f"Outstanding_Fees_{timezone.now().strftime('%Y%m%d')}.pdf"
                response['Content-Disposition'] = f'inline; filename="{filename}"'
                return response
        except Exception as e:
            logger.error(f"Error generating Outstanding Fees PDF: {e}", exc_info=True)
            messages.error(request, "An error occurred while generating the PDF report.")
            
        return redirect('reporting:outstanding_fees_report')
    
# # apps/reporting/views.py
# from django.shortcuts import render # Example existing import
# from django.views.generic import ListView
# from django.urls import reverse_lazy
# from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin
# from django.db.models import Sum, F, Case, When, Value, DecimalField, OuterRef, Subquery, ExpressionWrapper # Keep these
# from django.db.models.functions import Coalesce # Keep this
# from decimal import Decimal # Keep this

# from apps.students.models import Student # You already have this for self.model
# from apps.fees.models import Invoice, InvoiceDetail  # <<<< ADD THIS LINE
# # from ..fees.models import Invoice, InvoiceDetail # Alternative relative import if preferred

# from apps.common.mixins import BaseReportViewMixin # Your mixin
# from .forms import DateRangeClassTermForm # Your form


# # apps/reporting/views.py
# import logging
# from decimal import Decimal
# from django.views.generic import ListView # ListView is appropriate here
# from django.db.models import Sum, F, Q, ExpressionWrapper, DecimalField, Value, OuterRef, Subquery
# from django.db.models.functions import Coalesce
# from django.utils.translation import gettext_lazy as _

# # Assuming BaseReportViewMixin is defined (it provides the main get_context_data structure)
# # from .base_views import BaseReportViewMixin # Or from where it's defined
# from apps.students.models import Student
# from apps.fees.models import Invoice, InvoiceDetail 
# # from apps.schools.models import SchoolClass # Needed if filter form or display needs it

# # Assuming DateRangeClassTermForm is a django_filters.FilterSet
# from .filters import DateRangeClassTermForm # Or from wherever filters are defined

# logger = logging.getLogger(__name__)



# # apps/reporting/views.py
# import logging
# from decimal import Decimal
# from django.views.generic import ListView
# from django.urls import reverse_lazy
# from django.db.models import (
#     Sum, F, Q, ExpressionWrapper, DecimalField, Value, OuterRef, Subquery, Model
# ) # Added Model for isinstance check in get_queryset
# from django.db.models.functions import Coalesce
# from django.utils.translation import gettext_lazy as _

# # Assuming BaseReportViewMixin is defined (it provides the main get_context_data structure)
# # from .base_views import BaseReportViewMixin 
# from apps.students.models import Student
# from apps.fees.models import Invoice, InvoiceDetail # CRITICAL: Ensure InvoiceStatus is defined in Invoice
# # from apps.schools.models import SchoolClass # Needed if filter form or display needs it

# # Assuming DateRangeClassTermForm is a django_filters.FilterSet
# from .filters import DateRangeClassTermForm # Or from wherever filters are defined

# logger = logging.getLogger(__name__)


# # D:\school_fees_saas_v2\apps\reporting\views.py
# from django.http import HttpResponse
# from django.views.generic import ListView
# from django.utils.translation import gettext_lazy as _
# from django.utils import timezone
# from django.db.models import Sum, F, Q, ExpressionWrapper, DecimalField, Value, OuterRef, Subquery, Case, When, CharField
# from django.db.models.functions import Coalesce
# from decimal import Decimal
# from django.shortcuts import redirect
# from django.contrib import messages
# import csv
# from io import BytesIO
# import openpyxl
# from openpyxl.styles import Font
# from openpyxl.utils import get_column_letter

# from apps.common.mixins import TenantLoginRequiredMixin, TenantPermissionRequiredMixin, BaseReportViewMixin
# from apps.students.models import Student, SchoolClass, Section # Assuming SchoolClass, Section are in students or schools
# from apps.fees.models import Invoice, InvoiceDetail # Import Invoice, InvoiceDetail
# from apps.payments.models import PaymentAllocation # For more accurate paid amounts if needed
# # from .forms import DateRangeClassTermForm # Assuming your filter form is defined
# # from .filters import StudentOutstandingFeesFilterSet # Assuming your FilterSet is defined

# import logging
# logger = logging.getLogger(__name__)


# class OutstandingFeesReportView(TenantLoginRequiredMixin, TenantPermissionRequiredMixin, BaseReportViewMixin, ListView):
#     model = Student # The primary listing is of students
#     template_name = 'reporting/outstanding_fees_report.html' # Ensure this template exists
#     context_object_name = 'students_report'
#     paginate_by = 25

#     permission_required = 'reporting.view_outstanding_fees_report'
#     report_title = _("Outstanding Fees Report") # Can be set as class attribute
#     report_code = 'RPT_OUTSTANDING_FEES'
#     filter_form_class = None  # We'll use manual filtering for now

#     def get_queryset(self):
#         logger.debug(f"[{self.__class__.__name__}] get_queryset started. Request GET: {self.request.GET}")
        
#         # Base queryset of active students
#         base_queryset = Student.objects.filter(is_active=True).select_related(
#             'current_class', 'current_section'
#         ).prefetch_related(
#             'parents', 'invoices', 'invoices__details' # Prefetch invoices and their details
#         )

#         # --- Apply Manual Filters from GET parameters ---
#         queryset_after_form_filters = base_queryset

#         # Apply class filter
#         class_filter = self.request.GET.get('class', '')
#         if class_filter:
#             try:
#                 from apps.schools.models import SchoolClass
#                 class_obj = SchoolClass.objects.get(pk=class_filter)
#                 queryset_after_form_filters = queryset_after_form_filters.filter(current_class=class_obj)
#             except (SchoolClass.DoesNotExist, ValueError):
#                 pass

#         # Apply minimum due amount filter (will be applied after annotation)
        
#         # --- Define outstanding invoice statuses ---
#         try:
#             from apps.fees.models import InvoiceStatus
#             outstanding_statuses = [
#                 InvoiceStatus.SENT,
#                 InvoiceStatus.PARTIALLY_PAID,
#                 InvoiceStatus.OVERDUE
#             ]
#         except (AttributeError, ImportError):
#             logger.error(f"CRITICAL: {self.__class__.__name__} - InvoiceStatus enum not defined correctly!")
#             messages.error(self.request, _("System configuration error: Invoice statuses are not set up."))
#             return Student.objects.none()

#         # --- Annotate students with their outstanding balance ---
#         # This approach calculates balance per student by summing balances of their outstanding invoices.
#         # It calculates total from `subtotal_amount` - `total_concession_amount` and uses `amount_paid` field.
        
#         # Subquery to calculate balance for each invoice
#         invoice_balance_subquery = Invoice.objects.filter(
#             pk=OuterRef('pk'), # Relates to the invoice being annotated
#             status__in=outstanding_statuses
#         ).annotate(
#             calculated_balance=ExpressionWrapper(
#                 (Coalesce(F('subtotal_amount'), Decimal('0.00')) - Coalesce(F('total_concession_amount'), Decimal('0.00'))) - Coalesce(F('amount_paid'), Decimal('0.00')),
#                 output_field=DecimalField()
#             )
#         ).values('calculated_balance')

#         # Subquery to sum balances of all outstanding invoices for a student
#         student_outstanding_balance_subquery = Invoice.objects.filter(
#             student_id=OuterRef('pk'), # OuterRef to Student's pk
#             status__in=outstanding_statuses
#         ).annotate(
#             # Calculate balance for each invoice within this subquery scope
#             balance_per_invoice=ExpressionWrapper(
#                 (Coalesce(F('subtotal_amount'), Decimal('0.00')) - Coalesce(F('total_concession_amount'), Decimal('0.00'))) - Coalesce(F('amount_paid'), Decimal('0.00')),
#                 output_field=DecimalField()
#             )
#         ).filter(balance_per_invoice__gt=Decimal('0.00')).values('student_id').annotate(
#             total_student_outstanding=Sum('balance_per_invoice')
#         ).values('total_student_outstanding')

#         # Enhanced annotations to provide detailed financial breakdown
#         # Let's try a simpler approach first to debug

#         annotated_queryset = queryset_after_form_filters.annotate(
#             # Outstanding balance (existing)
#             outstanding_balance=Coalesce(
#                 Subquery(student_outstanding_balance_subquery[:1]),
#                 Decimal('0.00'),
#                 output_field=DecimalField()
#             )
#         )

#         # Add financial annotations using direct aggregation on the reverse relationship
#         annotated_queryset = annotated_queryset.annotate(
#             # Use direct aggregation on the reverse relationship
#             total_billed=Coalesce(
#                 Sum('invoices__subtotal_amount'),
#                 Decimal('0.00'),
#                 output_field=DecimalField()
#             ),
#             total_discount=Coalesce(
#                 Sum('invoices__total_concession_amount'),
#                 Decimal('0.00'),
#                 output_field=DecimalField()
#             ),
#             total_paid=Coalesce(
#                 Sum('invoices__amount_paid'),
#                 Decimal('0.00'),
#                 output_field=DecimalField()
#             )
#         )

#         # Filter out students with no outstanding balance
#         final_queryset = annotated_queryset.filter(outstanding_balance__gt=Decimal('0.005')).distinct() # Use small threshold for floating point

#         # Apply minimum due amount filter after annotation
#         min_due_filter = self.request.GET.get('min_due', '')
#         if min_due_filter:
#             try:
#                 min_due_amount = Decimal(min_due_filter)
#                 final_queryset = final_queryset.filter(outstanding_balance__gte=min_due_amount)
#             except (ValueError, InvalidOperation):
#                 pass
        
#         # Order the results
#         final_queryset = final_queryset.order_by('current_class__name', 'current_section__name', 'last_name', 'first_name')
        
#         logger.debug(f"[{self.__class__.__name__}] get_queryset final count: {final_queryset.count()}")
        
#         # Store the full, unfiltered (by pagination) queryset for aggregation in get_report_data
#         self.full_report_queryset = final_queryset



#         return final_queryset # This will be paginated by ListView

#     def get_report_data(self, filter_form=None):
#         """
#         Calculates summary data based on the self.full_report_queryset.
#         """
#         logger.debug(f"[{self.__class__.__name__}] get_report_data called.")
#         report_specific_data = {}

#         if hasattr(self, 'full_report_queryset') and self.full_report_queryset is not None:
#             total_outstanding_all_students = self.full_report_queryset.aggregate(
#                 total_sum=Sum('outstanding_balance') # Sum the 'outstanding_balance' annotation
#             )['total_sum'] or Decimal('0.00')
            
#             report_specific_data['total_outstanding_all_students'] = total_outstanding_all_students
#             logger.debug(f"[{self.__class__.__name__}] Total outstanding from 'get_report_data': {total_outstanding_all_students}")
#         else:
#             logger.warning(f"[{self.__class__.__name__}] full_report_queryset not available for aggregation in get_report_data.")
#             report_specific_data['total_outstanding_all_students'] = Decimal('0.00')
        
#         return report_specific_data

#     def get_context_data(self, **kwargs):
#         context = super().get_context_data(**kwargs)

#         # Add manual filter handling since we're not using a form class
#         current_class_filter = self.request.GET.get('class', '')
#         current_min_due_filter = self.request.GET.get('min_due', '')

#         # Get available classes for the filter dropdown
#         from apps.schools.models import SchoolClass
#         available_classes = SchoolClass.objects.filter(is_active=True).order_by('name')

#         # Add filter context
#         context.update({
#             'current_class_filter': current_class_filter,
#             'current_min_due_filter': current_min_due_filter,
#             'available_classes': available_classes,
#             'overall_total_due': self.get_report_data().get('total_outstanding_all_students', Decimal('0.00')),
#         })

#         # Add school profile for currency symbol
#         try:
#             from apps.schools.models import SchoolProfile
#             context['school_profile'] = SchoolProfile.objects.first()
#         except:
#             context['school_profile'] = None

#         return context

#     # --- Export Methods ---
#     # These methods use self.full_report_queryset (all filtered results, not just current page)

#     def get_export_queryset(self):
#         """Returns the full, filtered queryset for export."""
#         if not hasattr(self, 'full_report_queryset') or self.full_report_queryset is None:
#             # If exports are called directly without ListView's get path (e.g. via a separate button POST)
#             # we need to re-run get_queryset to establish self.full_report_queryset.
#             # ListView's get_queryset populates self.object_list which then gets assigned to full_report_queryset.
#             # For a direct export call, we need to ensure this setup.
#             # A simple way is to call get_queryset if it's not already set by a prior GET request processing.
#             # However, BaseReportViewMixin should ideally handle making the full_report_queryset available
#             # even if it's an export request.
#             # For now, let's assume it's available. If not, this is a point of refinement in BaseReportViewMixin.
#             logger.warning(f"[{self.__class__.__name__}] get_export_queryset: full_report_queryset not set. Attempting to generate.")
#             # This might re-run filters if not careful.
#             # A better pattern in BaseReportViewMixin might be to always build the full_report_queryset
#             # in get_context_data or a similar shared method, and ListView uses that.
#             # For now, we'll just use what get_queryset sets.
#             self.object_list = self.get_queryset() # This sets self.full_report_queryset

#         return self.full_report_queryset


#     def export_to_csv(self, queryset_for_export, request):
#         queryset = queryset_for_export or self.get_export_queryset()
#         response = HttpResponse(content_type='text/csv')
#         response['Content-Disposition'] = f'attachment; filename="{self.report_code}_{timezone.now().strftime("%Y%m%d")}.csv"'
#         writer = csv.writer(response)
        
#         headers = [
#             str(_('Admission No')), str(_('Full Name')), str(_('Class')), str(_('Section')),
#             str(_('Primary Parent')), str(_('Parent Phone')), str(_('Outstanding Balance'))
#         ]
#         writer.writerow(headers)

#         for student in queryset:
#             primary_parent = student.parents.first() # Get first parent since is_primary_guardian field doesn't exist
#             if not primary_parent and student.parents.exists():
#                 primary_parent = student.parents.first()

#             writer.writerow([
#                 student.admission_number, 
#                 student.full_name,
#                 student.current_class.name if student.current_class else 'N/A',
#                 student.current_section.name if student.current_section else 'N/A',
#                 primary_parent.get_full_name() if primary_parent else 'N/A',
#                 primary_parent.phone_number if primary_parent and primary_parent.phone_number else 'N/A',
#                 f"{student.outstanding_balance:.2f}" # Ensure outstanding_balance is available
#             ])
#         return response

#     def export_to_excel(self, queryset_for_export, request):
#         queryset = queryset_for_export or self.get_export_queryset()
#         response = HttpResponse(content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
#         response['Content-Disposition'] = f'attachment; filename="{self.report_code}_{timezone.now().strftime("%Y%m%d")}.xlsx"'
        
#         wb = openpyxl.Workbook()
#         ws = wb.active
#         ws.title = str(_("Outstanding Fees"))
        
#         headers = [
#             str(_('Admission No')), str(_('Full Name')), str(_('Class')), str(_('Section')),
#             str(_('Primary Parent')), str(_('Parent Phone')), str(_('Outstanding Balance'))
#         ]
#         ws.append(headers)
        
#         header_font = Font(bold=True)
#         currency_format = f'"{self.request.tenant.schoolprofile.currency_symbol if hasattr(self.request.tenant, "schoolprofile") and self.request.tenant.schoolprofile.currency_symbol else "$"}"#,##0.00'

#         for col_num, header_title in enumerate(headers, 1):
#             cell = ws.cell(row=1, column=col_num, value=str(header_title)) # Ensure header is string
#             cell.font = header_font
#             if "Balance" in str(header_title): # Make currency columns wider
#                 ws.column_dimensions[get_column_letter(col_num)].width = 22
#             else:
#                 ws.column_dimensions[get_column_letter(col_num)].width = 25


#         for student in queryset:
#             primary_parent = student.parents.first() # Get first parent since is_primary_guardian field doesn't exist
#             if not primary_parent and student.parents.exists():
#                 primary_parent = student.parents.first()
            
#             row_data = [
#                 student.admission_number,
#                 student.full_name,
#                 student.current_class.name if student.current_class else 'N/A',
#                 student.current_section.name if student.current_section else 'N/A',
#                 primary_parent.get_full_name() if primary_parent else 'N/A',
#                 primary_parent.phone_number if primary_parent and primary_parent.phone_number else 'N/A',
#                 student.outstanding_balance # This is already a Decimal
#             ]
#             ws.append(row_data)
#             # Apply currency format to the last cell
#             ws.cell(row=ws.max_row, column=len(headers)).number_format = currency_format
            
#         output = BytesIO()
#         wb.save(output)
#         output.seek(0)
#         response.write(output.getvalue())
#         return response

#     def get_pdf_template_name(self): # Ensure BaseReportViewMixin calls this
#         return 'reporting/pdf/outstanding_fees_report_pdf.html' # Correct template name

#     def export_to_pdf(self, queryset_for_export, request, pdf_context=None): # BaseReportViewMixin might call this with context
#         # The BaseReportViewMixin should gather context including self.object_list (paginated)
#         # For PDF, we want the full_report_queryset.
#         queryset = queryset_for_export or self.get_export_queryset()

#         # Get common context from the BaseReportViewMixin's get_context_data
#         # This is a bit of a dance; ideally BaseReportViewMixin would manage this state better.
#         # For simplicity, let's assume we can reconstruct necessary context here.
#         from apps.schools.models import SchoolProfile # Import inside method if not top-level
        
#         school_profile_instance = None
#         currency_symbol = '$'
#         # We're already in the tenant context, no need for schema_context
#         school_profile_instance = SchoolProfile.objects.first()
#         if school_profile_instance and school_profile_instance.currency_symbol:
#             currency_symbol = school_profile_instance.currency_symbol

#         filter_data_display = {}
#         if hasattr(self, 'filterset') and self.filterset and self.filterset.form.is_valid():
#             for name, value in self.filterset.form.cleaned_data.items():
#                 if value: # Only include active filters
#                     field = self.filterset.form.fields[name]
#                     if isinstance(field, forms.ModelChoiceField):
#                         filter_data_display[field.label] = str(value)
#                     elif isinstance(field, forms.ChoiceField):
#                         filter_data_display[field.label] = dict(field.choices).get(value)
#                     else:
#                         filter_data_display[field.label] = value
        
#         total_outstanding_all_students = queryset.aggregate(total_sum=Sum('outstanding_balance'))['total_sum'] or Decimal('0.00')

#         pdf_context = {
#             'report_data': queryset, # Pass the full filtered list (template expects 'report_data')
#             'report_items': queryset, # Also keep this for compatibility
#             'school_profile': school_profile_instance,
#             'report_title': self.get_report_title(),
#             'current_datetime': timezone.now(),
#             'school_currency_symbol': currency_symbol,
#             'total_outstanding_all_items': total_outstanding_all_students,
#             'total_outstanding_all_pages': total_outstanding_all_students, # Template expects this name
#             'applied_filters': filter_data_display, # Pass cleaned filter data for display
#             'request': request, # For building absolute URIs for images if needed in PDF
#         }
        
#         from apps.common.utils import render_to_pdf # Your PDF rendering utility
#         pdf = render_to_pdf(self.get_pdf_template_name(), pdf_context)
        
#         if pdf:
#             response = HttpResponse(pdf, content_type='application/pdf')
#             filename = f"{self.report_code}_{timezone.now().strftime('%Y%m%d_%H%M%S')}.pdf"
#             # To display inline:
#             response['Content-Disposition'] = f'inline; filename="{filename}"'
#             # To force download:
#             # response['Content-Disposition'] = f'attachment; filename="{filename}"'
#             return response
#         else:
#             logger.error(f"PDF generation failed for {self.report_code} (Outstanding Fees)")
#             messages.error(request, _("An error occurred while generating the PDF report. Please try again."))
#             # Redirect back to the report page or an error page
#             return redirect(request.META.get('HTTP_REFERER', reverse_lazy('reporting:outstanding_fees_report')))


# # --- NEW EXPORT VIEWS ---

# class OutstandingFeesExportCSVView(OutstandingFeesReportView):
#     """
#     A view to handle the CSV export. It inherits the filtering logic.
#     """
#     def get(self, request, *args, **kwargs):
#         # Get the filtered queryset without pagination
#         queryset = self.get_queryset()
        
#         response = HttpResponse(content_type='text/csv')
#         response['Content-Disposition'] = 'attachment; filename="outstanding_fees_report.csv"'

#         writer = csv.writer(response)
#         # Write header row
#         writer.writerow(['Invoice #', 'Student', 'Admission No', 'Class', 'Issue Date', 'Due Date', 'Total Amount', 'Amount Paid', 'Balance Due'])
        
#         # Write data rows
#         for invoice in queryset:
#             writer.writerow([
#                 invoice.invoice_number_display,
#                 invoice.student.full_name,
#                 invoice.student.admission_number,
#                 invoice.student.current_class.name if invoice.student.current_class else '',
#                 invoice.issue_date,
#                 invoice.due_date,
#                 invoice.total_amount,
#                 invoice.amount_paid,
#                 invoice.balance_due,
#             ])
#         return response


# class OutstandingFeesExportPDFView(OutstandingFeesReportView):
#     """
#     A view to handle the PDF export. It also inherits the filtering logic.
#     """
#     def get(self, request, *args, **kwargs):
#         queryset = self.get_queryset()
#         total_outstanding = queryset.aggregate(total=Sum('balance_due'))['total'] or Decimal('0.00')
#
#         context = {
#             'invoices': queryset,
#             'report_title': "Outstanding Fees Report",
#             'school_profile': SchoolProfile.objects.first(),
#             'tenant': request.tenant,
#             'total_outstanding': total_outstanding,
#             'filter_params': self.filter.form.cleaned_data,
#         }
#
#         pdf = render_to_pdf('reporting/pdf/outstanding_fees_pdf.html', context)
#
#         if pdf:
#             response = HttpResponse(pdf, content_type='application/pdf')
#             response['Content-Disposition'] = 'inline; filename="outstanding_fees_report.pdf"'
#             return response
#         return HttpResponse("Error generating PDF.", status=500)



# = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = 
#
#      COLLECTION REPORT
#
# = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =

# # Python Standard Library Imports
# import csv
# from decimal import Decimal
# from io import BytesIO
# import logging

# # Django Imports
# from django.core.paginator import Paginator
# from django.db.models import Sum, Count, F, Q
# from django.db.models.functions import Coalesce
# from django.http import HttpResponse
# from django.shortcuts import redirect
# from django.utils import timezone
# from django.utils.translation import gettext_lazy as _
# from django.views.generic import TemplateView
# from django.contrib import messages

# # Third-Party Imports
# try:
#     import openpyxl
#     from openpyxl.styles import Font
#     from openpyxl.utils import get_column_letter
# except ImportError:
#     openpyxl = None

# # Local Application Imports
# from apps.common.mixins import TenantPermissionRequiredMixin, BaseReportViewMixin
# from .forms import CollectionReportFilterForm # Assumes you create this form
# from .models import Payment, Student, SchoolClass # Your app's models
# from .utils import render_to_pdf
# from schools.models import SchoolProfile

# logger = logging.getLogger(__name__)


class CollectionReportView(TenantPermissionRequiredMixin, BaseReportViewMixin, TemplateView):
    """
    Handles the display of the HTML Fee Collection report page.
    """
    template_name = 'reporting/collection_report.html'
    
    # Attributes for BaseReportViewMixin
    permission_required = 'reporting.view_collection_report'
    report_title = _("Fee Collection Report")
    filter_form_class = CollectionReportFilterForm
    export_csv_url_name = 'reporting:collection_report_export_csv'
    export_pdf_url_name = 'reporting:collection_report_export_pdf'
    export_excel_url_name = 'reporting:collection_report_export_excel'
    paginate_by = 25 # Define pagination size here

    def _get_collection_data(self, request):
        """
        The core logic for fetching and processing all data for the Fee Collection report.
        """
        # 1. Initialize the data structure
        data = {
            'payments_page': None,
            'full_queryset': Payment.objects.none(),
            'total_collected': Decimal('0.00'),
            'transaction_count': 0,
            'collections_by_method': [],
            'start_date': None,
            'end_date': None,
            'filter_form': self.filter_form_class(request.GET or None)
        }
        
        # Set default date range if form is not submitted
        if not request.GET:
            today = timezone.now().date()
            start_date = today.replace(day=1) - timezone.timedelta(days=60) # Default to last ~2 months
            data['filter_form'] = self.filter_form_class(initial={'start_date': start_date, 'end_date': today})

        # 2. Validate form and apply filters
        base_queryset = Payment.objects.select_related(
            'student__current_class', 'payment_method', 'recorded_by', 'parent_payer'
        ).prefetch_related('allocations__invoice').order_by('-payment_date')

        filtered_queryset = base_queryset
        if data['filter_form'].is_valid():
            cd = data['filter_form'].cleaned_data
            data['start_date'] = cd.get('start_date')
            data['end_date'] = cd.get('end_date')

            if data['start_date']:
                filtered_queryset = filtered_queryset.filter(payment_date__date__gte=data['start_date'])
            if data['end_date']:
                filtered_queryset = filtered_queryset.filter(payment_date__date__lte=data['end_date'])
            if cd.get('school_class'):
                filtered_queryset = filtered_queryset.filter(student__current_class=cd['school_class'])
            if cd.get('payment_method'):
                filtered_queryset = filtered_queryset.filter(payment_method=cd['payment_method'])
            if cd.get('student_query'):
                sq = cd['student_query']
                filtered_queryset = filtered_queryset.filter(
                    Q(student__full_name__icontains=sq) | Q(student__admission_number__iexact=sq)
                )
        
        data['full_queryset'] = filtered_queryset

        # 3. Calculate summary totals from the full (un-paginated) queryset
        if filtered_queryset.exists():
            summary = filtered_queryset.aggregate(
                total=Coalesce(Sum('amount'), Decimal('0.00')),
                count=Count('id')
            )
            data['total_collected'] = summary['total']
            data['transaction_count'] = summary['count']
            
            data['collections_by_method'] = list(
                filtered_queryset.values('payment_method__name')
                .annotate(total=Sum('amount'), count=Count('id'))
                .order_by('-total')
                .filter(payment_method__name__isnull=False)
            )

        # 4. Paginate the results for the HTML view
        paginator = Paginator(filtered_queryset, self.paginate_by)
        page_number = request.GET.get('page', 1)
        data['payments_page'] = paginator.get_page(page_number)
        
        return data

    def get_context_data(self, **kwargs):
        """Prepares context for the HTML page by calling the main data helper."""
        context = super().get_context_data(**kwargs)
        report_data = self._get_collection_data(self.request)
        context.update(report_data)
        
        # Dynamically set the report title based on the date range
        if context.get('start_date') and context.get('end_date'):
            start, end = context['start_date'], context['end_date']
            context['report_title'] = _(f"Fee Collection from {start.strftime('%d-%b-%Y')} to {end.strftime('%d-%b-%Y')}")
        
        context['school_profile'] = SchoolProfile.objects.first()
        return context


class CollectionReportExportCSVView(CollectionReportView):
    """Handles CSV export for the Collection Report."""
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        report_data = self._get_collection_data(request)
        queryset = report_data.get('full_queryset')

        response = HttpResponse(content_type='text/csv')
        filename = f"Collection_Report_{timezone.now().strftime('%Y%m%d')}.csv"
        response['Content-Disposition'] = f'attachment; filename="{filename}"'
        writer = csv.writer(response)
        
        headers = ['Date', 'Receipt No', 'Student Name', 'Class', 'Amount', 'Method', 'Recorded By']
        writer.writerow(headers)

        for payment in queryset:
            writer.writerow([
                payment.payment_date.strftime('%Y-%m-%d %H:%M'),
                payment.receipt_number,
                payment.student.full_name if payment.student else 'N/A',
                payment.student.current_class.name if payment.student and payment.student.current_class else 'N/A',
                payment.amount,
                payment.payment_method.name if payment.payment_method else 'N/A',
                payment.recorded_by.get_full_name() if payment.recorded_by else 'System'
            ])
        return response


class CollectionReportExportExcelView(CollectionReportView):
    """Handles Excel export for the Collection Report."""
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        if not openpyxl:
            messages.error(request, "Excel export library not available.")
            return redirect('reporting:collection_report')

        report_data = self._get_collection_data(request)
        queryset = report_data.get('full_queryset')
        
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "Fee Collection"
        
        headers = ['Date', 'Receipt No', 'Student Name', 'Class', 'Amount', 'Method', 'Recorded By']
        ws.append(headers)
        header_font = Font(bold=True)
        for cell in ws[1]:
            cell.font = header_font
        
        currency_format = '#,##0.00'
        date_format = 'YYYY-MM-DD HH:MM'

        for payment in queryset:
            ws.append([
                payment.payment_date,
                payment.receipt_number,
                payment.student.full_name if payment.student else 'N/A',
                payment.student.current_class.name if payment.student and payment.student.current_class else 'N/A',
                payment.amount,
                payment.payment_method.name if payment.payment_method else 'N/A',
                payment.recorded_by.get_full_name() if payment.recorded_by else 'System'
            ])
            ws.cell(row=ws.max_row, column=1).number_format = date_format
            ws.cell(row=ws.max_row, column=5).number_format = currency_format

        # Auto-fit columns
        for col in ws.columns:
            max_length = 0
            column_letter = get_column_letter(col[0].column)
            for cell in col:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = (max_length + 2)
            ws.column_dimensions[column_letter].width = adjusted_width

        response = HttpResponse(content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        filename = f"Collection_Report_{timezone.now().strftime('%Y%m%d')}.xlsx"
        response['Content-Disposition'] = f'attachment; filename="{filename}"'
        wb.save(response)
        return response


class CollectionReportExportPDFView(CollectionReportView):
    """Handles PDF export for the Collection Report."""
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        report_data = self._get_collection_data(request)
        
        start = report_data.get('start_date')
        end = report_data.get('end_date')

        pdf_context = report_data.copy()
        pdf_context['report_title'] = f"Fee Collection Report"
        if start and end:
            pdf_context['report_subtitle'] = f"From {start.strftime('%d-%b-%Y')} to {end.strftime('%d-%b-%Y')}"

        pdf_context.update({
            'school_profile': SchoolProfile.objects.first(),
            'tenant': getattr(request, 'tenant', None),
            'current_datetime': timezone.now()
        })
        
        try:
            pdf = render_to_pdf('reporting/pdf/collection_report_pdf.html', pdf_context)
            if pdf:
                response = HttpResponse(pdf, content_type='application/pdf')
                filename = f"Collection_Report_{timezone.now().strftime('%Y%m%d')}.pdf"
                response['Content-Disposition'] = f'inline; filename="{filename}"'
                return response
        except Exception as e:
            logger.error(f"Error generating Collection Report PDF: {e}", exc_info=True)
            messages.error(request, "An error occurred while generating the PDF report.")
            
        return redirect('reporting:collection_report')

# # D:\school_fees_saas_v2\apps\reporting\views.py

# import csv
# import openpyxl
# from openpyxl.styles import Font
# from openpyxl.utils import get_column_letter
# from io import BytesIO
# import logging

# from django.http import HttpResponse, HttpResponseRedirect
# from django.urls import reverse_lazy, reverse
# from django.utils import timezone
# from django.utils.translation import gettext_lazy as _
# from django.views.generic import ListView
# from django.db.models import Sum, Count, Q, F # Q for complex lookups
# from django.db.models.functions import Coalesce
# from django.contrib import messages

# from apps.common.mixins import StaffLoginRequiredMixin, TenantPermissionRequiredMixin, BaseReportViewMixin
# from apps.payments.models import Payment
# from .forms import CollectionReportFilterForm # Ensure this form is well-defined
# from apps.common.utils import render_to_pdf

# logger = logging.getLogger(__name__)

# class CollectionReportView(StaffLoginRequiredMixin, TenantPermissionRequiredMixin, BaseReportViewMixin, ListView):
#     login_url = reverse_lazy('schools:staff_login')
#     permission_required = 'reporting.view_collection_report'
    
#     model = Payment # Base model for ListView
#     template_name = 'reporting/collection_report.html'
#     context_object_name = 'payments_on_page' # Paginated list
#     paginate_by = 25

#     # Attributes for BaseReportViewMixin
#     report_code = 'RPT_COLLECTION'
#     filter_form_class = CollectionReportFilterForm # Replace with your actual form class
#     report_title_text = _("Fee Collection Report")
    
#     # Internal attribute to store the full filtered queryset
#     _full_filtered_queryset = None

#     def get_report_title(self):
#         title_text = _("Fee Collection Report")
#         # self.form is instantiated in BaseReportViewMixin.get() or this view's get()
#         if hasattr(self, 'form') and self.form and self.form.is_bound and self.form.is_valid():
#             start_date = self.form.cleaned_data.get('start_date')
#             end_date = self.form.cleaned_data.get('end_date')
#             # You can add more elements to the title from other filters if desired
#             if start_date and end_date:
#                 title_text += f" (from {start_date.strftime('%d-%b-%Y')} to {end_date.strftime('%d-%b-%Y')})"
#             elif start_date:
#                 title_text += f" (from {start_date.strftime('%d-%b-%Y')})"
#             elif end_date:
#                 title_text += f" (to {end_date.strftime('%d-%b-%Y')})"
#         return title_text
    
#     def get_filter_form_initial_data(self): # Called by BaseReportViewMixin's get_filter_form_kwargs
#         # Set a wider default date range to include more payments
#         today = timezone.now().date()
#         # Start from 3 months ago to capture more payment data
#         start_date = today.replace(day=1) - timezone.timedelta(days=90)
#         return {
#             'start_date': start_date,
#             'end_date': today
#         }

#     def _apply_filters_to_queryset(self, queryset):
#         """Helper method to apply filters from self.form to a queryset."""
#         if hasattr(self, 'form') and self.form and self.form.is_valid():
#             logger.debug(f"{self.__class__.__name__}: Applying filters: {self.form.cleaned_data}")
#             cd = self.form.cleaned_data
#             if cd.get('start_date'): 
#                 queryset = queryset.filter(payment_date__date__gte=cd['start_date'])
#             if cd.get('end_date'): 
#                 queryset = queryset.filter(payment_date__date__lte=cd['end_date'])
#             if cd.get('student_query'):
#                 sq = cd['student_query']
#                 queryset = queryset.filter(
#                     Q(student__first_name__icontains=sq) | Q(student__last_name__icontains=sq) |
#                     Q(student__admission_number__iexact=sq) |
#                     Q(parent_payer__first_name__icontains=sq) | Q(parent_payer__last_name__icontains=sq) |
#                     Q(parent_payer__email__iexact=sq)
#                 )
#             if cd.get('payment_method'):
#                 queryset = queryset.filter(payment_method=cd['payment_method'])
#             if cd.get('class_obj'):
#                 queryset = queryset.filter(student__current_class=cd['class_obj'])
#             # Add other filters from CollectionReportFilterForm
#         elif hasattr(self, 'form') and self.form and not self.form.is_bound and self.form.initial:
#             # Apply initial filters if form is unbound but has initial data
#             logger.debug(f"{self.__class__.__name__}: Applying initial filters: {self.form.initial}")
#             initial = self.form.initial
#             if initial.get('start_date'): queryset = queryset.filter(payment_date__date__gte=initial['start_date'])
#             if initial.get('end_date'): queryset = queryset.filter(payment_date__date__lte=initial['end_date'])
#             # Apply other initial filters if any
#         elif hasattr(self, 'form') and self.form and self.form.is_bound and not self.form.is_valid():
#             logger.warning(f"{self.__class__.__name__}: Filter form is bound but invalid. Returning empty queryset. Errors: {self.form.errors}")
#             return queryset.none() # Or handle as per your requirement for invalid filters

#         return queryset
    
    

#     def get_queryset(self):
#         """
#         Called by ListView for pagination.
#         self.form is instantiated in self.get() before this is called by super().get().
#         """
#         base_queryset = super().get_queryset().select_related( # super().get_queryset() is Payment.objects.all()
#             'student__current_class', 'student__current_section', # student is already selected by default if model=Payment
#             'payment_method', 'processed_by_staff', 'parent_payer'
#         ).prefetch_related('allocations__invoice')
        
#         filtered_queryset = self._apply_filters_to_queryset(base_queryset)
        
#         # Store the full filtered queryset for aggregations and exports
#         self._full_filtered_queryset = filtered_queryset 
        
#         return filtered_queryset.order_by('-payment_date', '-id')
  
  
#     def get_report_data(self, processed_filter_form=None): # <<<< ADD 'processed_filter_form'
#     # Now, inside this method, use the 'processed_filter_form' argument
#         if processed_filter_form is None:
#             processed_filter_form = self.form
#     # to access .cleaned_data (if valid) or .initial data.
#     # DO NOT rely on self.form within this specific method if you take it as an argument.

#         report_specific_data = {
#             'summary_total_collected': Decimal('0.00'),
#             'summary_transaction_count': 0,
#             'collections_by_method': []
#         }
#         logger.debug(f"{self.__class__.__name__}.get_report_data received form: {type(processed_filter_form)}, bound: {processed_filter_form.is_bound if processed_filter_form else 'N/A'}, valid: {processed_filter_form.is_valid() if processed_filter_form and processed_filter_form.is_bound else 'N/A'}")

        
#         current_form = self.form # Access the form instance set by the mixin's get() method

#         # Start with a base queryset for summary calculations
#         base_queryset_for_summary = self.model.objects.select_related(
#             'student__current_class', 'student__current_section',
#             'payment_method', 'recorded_by', 'parent_payer', 
#             'invoice', 'invoice__student' 
#         )
    
#         # Get the full filtered queryset.
#         # _get_filtered_queryset should use self.form (which is the same instance as processed_filter_form here)
#         queryset_for_summary = self._apply_filters_to_queryset(base_queryset_for_summary)

#         if current_form and (current_form.is_valid() or (not current_form.is_bound and current_form.initial)):
#             # Check if queryset_for_summary (which could be .none()) has results,
#             # or if it's an initial load (unbound form with initial data)
#             if queryset_for_summary.exists() or (not current_form.is_bound and current_form.initial):
#                 summary_aggregates = queryset_for_summary.aggregate(
#                     total_collected=Coalesce(Sum('amount'), Decimal('0.00')),
#                     transaction_count=Count('id')
#                 )
#                 report_specific_data['summary_total_collected'] = summary_aggregates.get('total_collected')
#                 report_specific_data['summary_transaction_count'] = summary_aggregates.get('transaction_count')

#                 report_specific_data['collections_by_method'] = list(
#                     queryset_for_summary.values(method_name=F('payment_method__name'))
#                     .annotate(total=Coalesce(Sum('amount'), Decimal('0.00')), count=Count('id'))
#                     .order_by('-total')
#                     .filter(method_name__isnull=False)
#                 )
#             # else: defaults initialized above will be used if queryset is empty due to invalid filters
#         else:
#             logger.info(f"{self.__class__.__name__}.get_report_data: Form invalid or not present for summary calculation.")
#             # Defaults initialized above will be used.
            
#         logger.debug(f"{self.__class__.__name__}.get_report_data returning: {report_specific_data}")
#         return report_specific_data


#     def get_context_data(self, **kwargs):
#         # self.form is set in get().
#         # BaseReportViewMixin.get_context_data (called by super()) will:
#         #  - Add self.form as 'filter_form'.
#         #  - Call self.get_report_data() and merge its results.
#         #  - Add 'report_title', 'report_code', 'report_generated_at'.
#         # ListView.get_context_data (called by super()) will add paginated 'payments_on_page'.
#         context = super().get_context_data(**kwargs)
        
#         # Add date filters to context for display in template header, if form was valid
#         if hasattr(self, 'form') and self.form and self.form.is_bound and self.form.is_valid():
#             context['start_date_filter'] = self.form.cleaned_data.get('start_date')
#             context['end_date_filter'] = self.form.cleaned_data.get('end_date')
#         elif hasattr(self, 'form') and self.form and not self.form.is_bound and self.form.initial: # For initial load
#             context['start_date_filter'] = self.form.initial.get('start_date')
#             context['end_date_filter'] = self.form.initial.get('end_date')
            
#         logger.debug(f"{self.__class__.__name__}.get_context_data final keys: {list(context.keys())}")
#         return context
    
#     # --- Export Methods ---
#     def get_export_queryset(self): # Called by BaseReportViewMixin's export handling
#         # Ensure self._full_filtered_queryset is available
#         if not hasattr(self, '_full_filtered_queryset'):
#             # This might happen if export is called without get_queryset running first (e.g. direct link)
#             # So, we need to robustly generate it.
#             logger.warning(f"{self.__class__.__name__}: _full_filtered_queryset not set. Re-running get_queryset logic for export.")
#             # Instantiate form if it wasn't (e.g. if get() wasn't called)
#             if not hasattr(self, 'form') or not self.form:
#                  self.form = self.filter_form_class(self.request.GET or None, **self.get_filter_form_kwargs())

#             base_queryset = Payment.objects.select_related( # Use Payment as it's self.model
#                 'student__current_class', 'student__current_section',
#                 'payment_method', 'processed_by_staff', 'parent_payer'
#             ).prefetch_related('allocations__invoice')
#             self._full_filtered_queryset = self._apply_filters_to_queryset(base_queryset)

#         return self._full_filtered_queryset.order_by('-payment_date', '-id')


#     def export_to_csv(self, queryset_for_export, request): # queryset is from get_export_queryset
#         queryset = queryset_for_export or self.get_export_queryset()
#         response = HttpResponse(content_type='text/csv')
#         response['Content-Disposition'] = f'attachment; filename="{self.report_code}_{timezone.now().strftime("%Y%m%d")}.csv"'
#         writer = csv.writer(response)
#         headers = ['Date', 'Receipt No', 'Student Name', 'Adm. No', 'Class', 'Invoice No', 'Amount Paid', 'Method', 'Received By', 'Parent Payer']
#         writer.writerow(headers)
#         for p in queryset:
#             writer.writerow([
#                 p.payment_date.strftime('%Y-%m-%d %H:%M') if p.payment_date else '',
#                 p.receipt_number or f"PK{p.pk}",
#                 p.student.get_full_name() if p.student else (p.invoice.student.get_full_name() if p.invoice and p.invoice.student else 'N/A'),
#                 p.student.admission_number if p.student else (p.invoice.student.admission_number if p.invoice and p.invoice.student else 'N/A'),
#                 str(p.student.current_class) if p.student and p.student.current_class else (str(p.invoice.student.current_class) if p.invoice and p.invoice.student and p.invoice.student.current_class else 'N/A'),
#                 p.invoice.invoice_number if p.invoice else _('N/A (Direct)'),
#                 f"{p.amount:.2f}",
#                 p.payment_method.name if p.payment_method else _('N/A'),
#                 p.recorded_by.get_full_name() if p.recorded_by else (getattr(p, 'processed_by_staff', None).get_full_name() if hasattr(p, 'processed_by_staff') and p.processed_by_staff else _('System/N/A')),
#                 p.parent_payer.get_full_name() if p.parent_payer else _('N/A')
#             ])
#         return response

#     def export_to_excel(self, queryset_for_export, request): # queryset is from get_export_queryset
#         queryset = queryset_for_export or self.get_export_queryset()
#         response = HttpResponse(content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
#         response['Content-Disposition'] = f'attachment; filename="{self.report_code}_{timezone.now().strftime("%Y%m%d")}.xlsx"'
#         wb = openpyxl.Workbook()
#         ws = wb.active
#         ws.title = self.get_report_title()[:30]
        
#         headers = ['Date', 'Receipt No', 'Student Name', 'Adm. No', 'Class', 'Invoice No', 'Amount Paid', 'Method', 'Recorded By', 'Parent Payer']
#         ws.append(headers)
#         header_font = Font(bold=True)
#         currency_format = '#,##0.00'
#         date_format = 'yyyy-mm-dd hh:mm' # Excel format
        
#         column_widths = {'A': 20, 'B': 15, 'C': 25, 'D': 15, 'E': 20, 'F': 15, 'G': 15, 'H': 20, 'I': 25, 'J': 25}
#         for col_letter, width in column_widths.items():
#             ws.column_dimensions[col_letter].width = width
#         for cell in ws[1]: cell.font = header_font

#         for p in queryset:
#             ws.append([
#                 p.payment_date,
#                 p.receipt_number or f"PK{p.pk}",
#                 p.student.get_full_name() if p.student else (p.invoice.student.get_full_name() if p.invoice and p.invoice.student else 'N/A'),
#                 p.student.admission_number if p.student else (p.invoice.student.admission_number if p.invoice and p.invoice.student else 'N/A'),
#                 str(p.student.current_class) if p.student and p.student.current_class else (str(p.invoice.student.current_class) if p.invoice and p.invoice.student and p.invoice.student.current_class else 'N/A'),
#                 p.invoice.invoice_number if p.invoice else _('N/A (Direct)'),
#                 p.amount,
#                 p.payment_method.name if p.payment_method else _('N/A'),
#                 p.recorded_by.get_full_name() if p.recorded_by else (getattr(p, 'processed_by_staff', None).get_full_name() if hasattr(p, 'processed_by_staff') and p.processed_by_staff else _('System/N/A')),
#                 p.parent_payer.get_full_name() if p.parent_payer else _('N/A')
#             ])
#             ws.cell(row=ws.max_row, column=1).number_format = date_format # Apply date format
#             ws.cell(row=ws.max_row, column=7).number_format = currency_format # Amount Paid

#         output = BytesIO()
#         wb.save(output)
#         output.seek(0)
#         response.write(output.getvalue())
#         return response
        
#     def get_pdf_template_name(self):
#         return "reporting/pdf/collection_report_pdf.html" # Correct template name

#     def export_to_pdf(self, queryset_for_export, request, pdf_context=None): # queryset & pdf_context from BaseReportViewMixin.get()
#         queryset = queryset_for_export or self.get_export_queryset()
#         # pdf_context already contains 'report_items': queryset, 'report_title', etc. from BaseReportViewMixin
#         # We might want to add the specific summary data from this view's get_report_data
#         if pdf_context is None:
#             pdf_context = {}
#         report_data_summaries = self.get_report_data() # Call it to get summaries
#         pdf_context.update(report_data_summaries) # Add summaries like 'summary_total_collected'
#         pdf_context['report_items'] = queryset

#         pdf = render_to_pdf(self.get_pdf_template_name(), pdf_context)
#         if pdf:
#             response = HttpResponse(pdf, content_type='application/pdf')
#             filename = f"{self.report_code}_{timezone.now().strftime('%Y%m%d')}.pdf"
#             response['Content-Disposition'] = f'inline; filename="{filename}"'
#             return response
#         else:
#             logger.error(f"PDF generation failed for {self.report_code} ({self.get_report_title()})")
#             messages.error(request, _("Sorry, we could not generate the PDF report at this time."))
#             # Redirect back to the HTML report page, preserving filters
#             query_params = request.GET.copy(); query_params.pop('export', None)
#             return HttpResponseRedirect(f"{request.path}?{query_params.urlencode()}")



# = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = 
#
#     TRIAL BALANCE REPORT
#
# = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =

# # Python Standard Library Imports
# import csv
# from decimal import Decimal
# from io import BytesIO
# import logging

# # Django Imports
# from django.http import HttpResponse
# from django.shortcuts import redirect
# from django.utils import timezone
# from django.utils.translation import gettext_lazy as _
# from django.views.generic import TemplateView
# from django.contrib import messages

# # Third-Party Imports
# try:
#     import openpyxl
#     from openpyxl.styles import Font, Alignment
#     from openpyxl.utils import get_column_letter
# except ImportError:
#     openpyxl = None

# # Local Application Imports
# from .mixins import TenantPermissionRequiredMixin, BaseReportViewMixin
# from .forms import ReportPeriodForm # A simple form with one date field
# from .models import Account, AccountType # Your accounting models
# from .utils import render_to_pdf
# from schools.models import SchoolProfile

# logger = logging.getLogger(__name__)


class TrialBalanceReportView(TenantPermissionRequiredMixin, BaseReportViewMixin, TemplateView):
    """
    Handles the display of the HTML Trial Balance report page.
    """
    template_name = 'reporting/trial_balance_report.html'
    
    # Attributes for BaseReportViewMixin
    permission_required = 'reporting.view_trial_balance_report'
    report_title = _("Trial Balance")
    filter_form_class = ReportPeriodForm # A form with a single 'report_date' field
    export_csv_url_name = 'reporting:trial_balance_export_csv'
    export_pdf_url_name = 'reporting:trial_balance_export_pdf'
    export_excel_url_name = 'reporting:trial_balance_export_excel'

    def _get_trial_balance_data(self, request):
        """
        The core logic for fetching and processing all data for the Trial Balance report.
        """
        # 1. Initialize the data structure
        data = {
            'accounts_data': [], 'total_debits': Decimal('0.00'),
            'total_credits': Decimal('0.00'), 'balance_difference': Decimal('0.00'),
            'as_at_date': None, 'filter_form': self.filter_form_class(request.GET or None)
        }
        
        # 2. Validate form and get the 'as at' date
        # Use a default date if the form is unbound (initial load)
        if not data['filter_form'].is_bound:
            data['filter_form'] = self.filter_form_class(initial={'report_date': timezone.now().date()})
            data['filter_form'].is_valid() # Run validation on initial data

        if not data['filter_form'].is_valid():
            return data # Return with form errors if bound and invalid
        
        as_at_date = data['filter_form'].cleaned_data.get('report_date', timezone.now().date())
        data['as_at_date'] = as_at_date

        # 3. Fetch all active accounts and calculate their balances
        accounts = Account.objects.filter(is_active=True).select_related('account_type').order_by('code')

        for account in accounts:
            balance = account.get_balance(as_of_date=as_at_date)

            if balance == Decimal('0.00'):
                continue # Skip accounts with no balance

            debit_balance = Decimal('0.00')
            credit_balance = Decimal('0.00')

            # Determine if balance is debit or credit based on the account type's normal balance
            if account.account_type.normal_balance == AccountType.NormalBalanceChoices.DEBIT:
                debit_balance = balance
            else: # Normal balance is CREDIT
                # A positive balance for a credit-normal account is a credit.
                # A negative balance means it has flipped to a debit balance.
                credit_balance = -balance

            data['accounts_data'].append({
                'code': account.code,
                'name': account.name,
                'debit': debit_balance if debit_balance > 0 else Decimal('0.00'),
                'credit': credit_balance if credit_balance > 0 else Decimal('0.00'),
            })
            
            data['total_debits'] += debit_balance if debit_balance > 0 else Decimal('0.00')
            data['total_credits'] += credit_balance if credit_balance > 0 else Decimal('0.00')
            
        data['balance_difference'] = data['total_debits'] - data['total_credits']

        return data

    def get_context_data(self, **kwargs):
        """Prepares context for the HTML page by calling the main data helper."""
        context = super().get_context_data(**kwargs)
        report_data = self._get_trial_balance_data(self.request)
        context.update(report_data)
        
        if context.get('as_at_date'):
            context['report_title'] = _(f"Trial Balance as at {context['as_at_date'].strftime('%d %B %Y')}")
            
        context['school_profile'] = SchoolProfile.objects.first()
        return context


class TrialBalanceExportCSVView(TrialBalanceReportView):
    """Handles CSV export for the Trial Balance report."""
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        report_data = self._get_trial_balance_data(request)
        as_at_date = report_data.get('as_at_date')
        if not as_at_date:
            messages.error(request, "Please select a date to generate the CSV export.")
            return redirect('reporting:trial_balance_report')

        response = HttpResponse(content_type='text/csv')
        filename = f"Trial_Balance_{as_at_date.strftime('%Y%m%d')}.csv"
        response['Content-Disposition'] = f'attachment; filename="{filename}"'
        writer = csv.writer(response)
        
        writer.writerow([f"Trial Balance as at {as_at_date.strftime('%d %B %Y')}"])
        writer.writerow([])
        writer.writerow(['Account Code', 'Account Name', 'Debit', 'Credit'])

        for acc in report_data.get('accounts_data', []):
            writer.writerow([acc['code'], acc['name'], acc['debit'], acc['credit']])
        
        writer.writerow([])
        writer.writerow(['', 'TOTALS', report_data['total_debits'], report_data['total_credits']])
        
        return response


class TrialBalanceExportExcelView(TrialBalanceReportView):
    """Handles Excel export for the Trial Balance report."""
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        if not openpyxl:
            messages.error(request, "Excel export library not available.")
            return redirect('reporting:trial_balance_report')

        report_data = self._get_trial_balance_data(request)
        as_at_date = report_data.get('as_at_date')
        if not as_at_date:
            messages.error(request, "Please select a date to generate the Excel export.")
            return redirect('reporting:trial_balance_report')

        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "Trial Balance"
        
        # Styles
        title_font = Font(bold=True, size=14)
        header_font = Font(bold=True)
        currency_format = '#,##0.00'

        # Header
        ws.merge_cells('A1:D1')
        ws['A1'] = f"Trial Balance as at {as_at_date.strftime('%d %B %Y')}"
        ws['A1'].font = title_font
        ws['A1'].alignment = Alignment(horizontal='center')
        
        # Table Headers
        headers = ['Account Code', 'Account Name', 'Debit', 'Credit']
        ws.append(headers)
        for cell in ws[2]: cell.font = header_font

        # Data
        for acc in report_data.get('accounts_data', []):
            ws.append([acc['code'], acc['name'], acc['debit'], acc['credit']])
        
        # Totals
        ws.append(['', 'TOTALS', report_data['total_debits'], report_data['total_credits']])
        total_row = ws.max_row
        for cell in ws[total_row]: cell.font = header_font
        
        # Formatting
        ws.column_dimensions['A'].width = 15
        ws.column_dimensions['B'].width = 40
        ws.column_dimensions['C'].width = 18
        ws.column_dimensions['D'].width = 18
        for row in ws.iter_rows(min_row=3, max_col=4, min_col=3):
            for cell in row:
                cell.number_format = currency_format

        response = HttpResponse(content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        filename = f"Trial_Balance_{as_at_date.strftime('%Y%m%d')}.xlsx"
        response['Content-Disposition'] = f'attachment; filename="{filename}"'
        wb.save(response)
        return response


class TrialBalanceExportPDFView(TrialBalanceReportView):
    """Handles PDF export for the Trial Balance report."""
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        report_data = self._get_trial_balance_data(request)
        as_at_date = report_data.get('as_at_date')
        if not as_at_date:
            messages.error(request, "Please select a date to generate the PDF.")
            return redirect('reporting:trial_balance_report')
        
        pdf_context = report_data.copy()
        pdf_context.update({
            'report_title': f"Trial Balance as at {as_at_date.strftime('%d %B %Y')}",
            'school_profile': SchoolProfile.objects.first(),
            'tenant': getattr(request, 'tenant', None),
            'current_datetime': timezone.now()
        })
        
        try:
            pdf = render_to_pdf('reporting/pdf/trial_balance_pdf.html', pdf_context)
            if pdf:
                response = HttpResponse(pdf, content_type='application/pdf')
                filename = f"Trial_Balance_{as_at_date.strftime('%Y%m%d')}.pdf"
                response['Content-Disposition'] = f'inline; filename="{filename}"'
                return response
        except Exception as e:
            logger.error(f"Error generating Trial Balance PDF: {e}", exc_info=True)
            messages.error(request, "An error occurred while generating the PDF report.")
            
        return redirect('reporting:trial_balance_report')
    
# # apps/reporting/views.py
# import logging
# from decimal import Decimal
# from django.utils import timezone
# from django.views.generic import TemplateView
# from django.urls import reverse_lazy # Not strictly needed if set in Base
# from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin
# from django.contrib import messages # For user feedback if needed
# from django.db.models import Sum, Q, F
# from django.db.models.functions import Coalesce
# from django.utils.translation import gettext_lazy as _


# # Models (ensure all are correctly imported)
# from apps.accounting.models import Account as ChartOfAccount, AccountType, JournalEntryItem # Using JournalEntryItem

# # Forms
# from .forms import ReportPeriodForm # Your filter form for this report


# from django.utils import timezone
# from django.db.models import Sum, F, Q, DecimalField, Value # Value not used here yet
# from django.db.models.functions import Coalesce
# from decimal import Decimal
# from django.contrib import messages
# from django.utils.translation import gettext_lazy as _
# from django.http import HttpResponse, HttpResponseRedirect # Added HttpResponseRedirect
# from django.shortcuts import redirect # Added redirect

# # Your model imports: ChartOfAccount, JournalEntry, JournalEntryItem, AccountType, SchoolProfile
# from apps.accounting.models import Account, JournalEntry, JournalEntryItem, AccountType 
# from apps.schools.models import SchoolProfile # Assuming SchoolProfile is here

# # Your form import:
# from .forms import ReportPeriodForm 
# # Your mixin import:
# from apps.common.mixins import TenantPermissionRequiredMixin # Assuming BaseReportViewMixin is also there or imported by it
# from apps.common.mixins import BaseReportViewMixin # Explicitly if BaseReportViewMixin is separate

# # Your PDF utility:
# from apps.common.utils import render_to_pdf 

# # For Excel/CSV
# import csv
# import openpyxl # Ensure openpyxl is installed: pip install openpyxl
# from openpyxl.utils import get_column_letter
# from openpyxl.styles import Font, Alignment
# from io import BytesIO

# import logging
# logger = logging.getLogger(__name__)


# class TrialBalanceView(TenantPermissionRequiredMixin, BaseReportViewMixin, TemplateView):
#     permission_required = 'reporting.view_trial_balance_report' # Define your actual permission
#     template_name = 'reporting/trial_balance_report.html'

#     report_code = 'TB001' # Example report code
#     filter_form_class = ReportPeriodForm # This is used by BaseReportViewMixin

#     # report_title is now a property that uses the processed filter form
#     @property
#     def report_title(self):
#         # Access the processed_filter_form which should be set by BaseReportViewMixin's get method
#         form = getattr(self, 'processed_filter_form', None)
#         report_date_val = None
#         if form and form.is_bound and form.is_valid():
#             report_date_val = form.cleaned_data.get('report_date')
#         elif form and not form.is_bound and form.initial: # Unbound form, use initial
#             report_date_val = form.initial.get('report_date')
        
#         if report_date_val:
#             return _(f"Trial Balance as of {report_date_val.strftime('%d %B %Y')}")
#         return _("Trial Balance")
    
#     # get_filter_form_initial_data is called by BaseReportViewMixin
#     def get_filter_form_initial_data(self):
#         return {'report_date': timezone.localdate()}

#     def get_report_data(self, filter_form=None): # <<<< CORRECTED SIGNATURE
#     # Now, inside this method, use 'self.form' to access the filter form instance,
#     # its cleaned_data, or initial data.
#     # self.form was set by BaseReportViewMixin.get()

#         report_specific_data = {
#             'accounts_data': [],
#             'total_debits': Decimal('0.00'),
#             'total_credits': Decimal('0.00'),
#             'report_date': None, # Or a default
#         }
#         logger.debug(f"{self.__class__.__name__}.get_report_data called. self.form type: {type(self.form)}, bound: {self.form.is_bound if self.form else 'N/A'}, valid: {self.form.is_valid() if self.form and self.form.is_bound else 'N/A'}")

#         current_form = self.form # For clarity

#         if current_form and (current_form.is_valid() or (not current_form.is_bound and current_form.initial)):
#             report_date = None
#             if current_form.is_valid(): # Form was bound and valid
#                 report_date = current_form.cleaned_data.get('report_date')
#                 logger.debug(f"{self.__class__.__name__}: Using report_date from valid form: {report_date}")
#             elif not current_form.is_bound and current_form.initial: # Form unbound, use initial
#                 report_date = current_form.initial.get('report_date')
#                 logger.debug(f"{self.__class__.__name__}: Using report_date from initial data: {report_date}")

#             if not report_date: # Fallback if still no date
#                 report_date = timezone.now().date()
#                 logger.debug(f"{self.__class__.__name__}: report_date fell back to today: {report_date}")
            
#             report_specific_data['report_date'] = report_date

#             # --- Actual data fetching for Trial Balance using report_date ---
#             from apps.accounting.models import Account

#             # Get all active accounts with their balances as of the report date
#             accounts = Account.objects.filter(is_active=True).select_related('account_type').order_by('code', 'name')

#             trial_balance_data = []
#             total_debits = Decimal('0.00')
#             total_credits = Decimal('0.00')

#             for account in accounts:
#                 # Get account balance as of the report date
#                 balance = account.get_balance(as_of_date=report_date)

#                 if balance != Decimal('0.00'):  # Only include accounts with non-zero balances
#                     debit_balance = Decimal('0.00')
#                     credit_balance = Decimal('0.00')

#                     # Determine if balance should be shown as debit or credit
#                     if account.account_type and account.account_type.normal_balance == AccountType.NormalBalanceChoices.DEBIT:
#                         if balance >= 0:
#                             debit_balance = balance
#                             total_debits += balance
#                         else:
#                             credit_balance = abs(balance)
#                             total_credits += abs(balance)
#                     elif account.account_type and account.account_type.normal_balance == AccountType.NormalBalanceChoices.CREDIT:
#                         if balance >= 0:
#                             credit_balance = balance
#                             total_credits += balance
#                         else:
#                             debit_balance = abs(balance)
#                             total_debits += abs(balance)

#                     trial_balance_data.append({
#                         'account': account,
#                         'balance': balance,
#                         'debit_balance': debit_balance,
#                         'credit_balance': credit_balance,
#                     })

#             report_specific_data['trial_balance_data'] = trial_balance_data
#             report_specific_data['total_debits'] = total_debits
#             report_specific_data['total_credits'] = total_credits
#             report_specific_data['balance_difference'] = total_debits - total_credits

#             logger.info(f"Trial Balance: {len(trial_balance_data)} accounts, Total Debits: {total_debits}, Total Credits: {total_credits}")

#         else:
#             logger.warning(f"{self.__class__.__name__}.get_report_data: Form invalid or not present. Errors: {current_form.errors if current_form else 'No Form'}")
#             report_specific_data['report_date'] = timezone.now().date() # Default date for display

#         return report_specific_data

#     # Ensure your export methods are correctly defined within the class
#     def export_to_csv(self, report_data_dict, request):
#         response = HttpResponse(content_type='text/csv')
#         response['Content-Disposition'] = f'attachment; filename="{self.report_code}_{report_data_dict["report_date"].strftime("%Y%m%d")}.csv"'
#         writer = csv.writer(response)
#         writer.writerow([self.report_title.upper()]) # Use self.report_title (property)
#         writer.writerow([f'As at: {report_data_dict["report_date"].strftime("%d %B %Y")}'])
#         writer.writerow([])
#         writer.writerow(['Account Code', 'Account Name', 'Debit', 'Credit'])
#         for item in report_data_dict['trial_balance_items']: writer.writerow([item['account_code'], item['account_name'], item['debit'], item['credit']])
#         writer.writerow([])
#         writer.writerow(['', 'TOTALS', report_data_dict['total_debits'], report_data_dict['total_credits']])
#         if not report_data_dict['totals_match']: writer.writerow(['', 'DIFFERENCE', report_data_dict['difference'], ''])
#         return response

#     def export_to_excel(self, report_data_dict, request):
#         response = HttpResponse(content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
#         response['Content-Disposition'] = f'attachment; filename="{self.report_code}_{report_data_dict["report_date"].strftime("%Y%m%d")}.xlsx"'
#         wb = openpyxl.Workbook(); ws = wb.active; ws.title = "Trial Balance"; title_font = Font(bold=True, size=14); header_font = Font(bold=True); currency_format = '#,##0.00'
#         ws.merge_cells('A1:D1'); title_cell = ws['A1']; title_cell.value = self.report_title.upper(); title_cell.font = title_font; title_cell.alignment = Alignment(horizontal="center") # Use self.report_title
#         ws.merge_cells('A2:D2'); date_cell = ws['A2']; date_cell.value = f'As at: {report_data_dict["report_date"].strftime("%d %B %Y")}'; date_cell.alignment = Alignment(horizontal="center")
#         ws.append([]); headers = ['Account Code', 'Account Name', 'Debit', 'Credit']; header_row_num = ws.max_row
#         for col_num, header_title in enumerate(headers, 1):
#             cell = ws.cell(row=header_row_num, column=col_num, value=header_title); cell.font = header_font
#             ws.column_dimensions[get_column_letter(col_num)].width = 35 if col_num == 2 else 18 # Adjusted widths
#             if col_num > 2: cell.alignment = Alignment(horizontal="right")
#         for item in report_data_dict['trial_balance_items']:
#             ws.append([item['account_code'], item['account_name'], item['debit'] if item['debit']!=0 else '', item['credit'] if item['credit']!=0 else ''])
#             ws.cell(row=ws.max_row, column=3).number_format = currency_format
#             ws.cell(row=ws.max_row, column=4).number_format = currency_format
#         ws.append([]); total_row_num = ws.max_row
#         ws.cell(row=total_row_num, column=2, value="TOTALS").font = header_font; ws.cell(row=total_row_num, column=2).alignment = Alignment(horizontal="right")
#         ws.cell(row=total_row_num, column=3, value=report_data_dict['total_debits']).font = header_font; ws.cell(row=total_row_num, column=3).number_format = currency_format
#         ws.cell(row=total_row_num, column=4, value=report_data_dict['total_credits']).font = header_font; ws.cell(row=total_row_num, column=4).number_format = currency_format
#         if not report_data_dict['totals_match']:
#             ws.append([]); diff_row_num = ws.max_row
#             ws.cell(row=diff_row_num, column=2, value="DIFFERENCE").font = Font(bold=True, color="FF0000"); ws.cell(row=diff_row_num, column=2).alignment = Alignment(horizontal="right")
#             ws.cell(row=diff_row_num, column=3, value=report_data_dict['difference']).font = Font(bold=True, color="FF0000"); ws.cell(row=diff_row_num, column=3).number_format = currency_format
#         output = BytesIO(); wb.save(output); output.seek(0); response.write(output.getvalue())
#         return response

#     def get_pdf_template_name(self):
#         return 'reporting/pdf/trial_balance_pdf.html' # Or your actual PDF template path

#     def export_to_pdf(self, report_data_dict, request, html_context_for_pdf):
#         # html_context_for_pdf is prepared by BaseReportViewMixin.get_context_data_for_pdf
#         pdf = render_to_pdf(self.get_pdf_template_name(), html_context_for_pdf)
#         if pdf:
#             response = HttpResponse(pdf, content_type='application/pdf')
#             filename = f"{self.report_code}_{report_data_dict['report_date'].strftime('%Y%m%d')}.pdf"
#             # Use 'inline' for preview, 'attachment' for direct download
#             response['Content-Disposition'] = f'inline; filename="{filename}"' 
#             return response
#         else:
#             logger.error(f"PDF generation failed for {self.report_code} (Trial Balance)")
#             messages.error(request, _("Failed to generate PDF report."))
#             # Redirect back to the report page without export params
#             query_params = request.GET.copy()
#             query_params.pop('export', None)
#             return redirect(f"{request.path}?{query_params.urlencode()}")



# = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = 
#
#      INCOME / EXPENSE REPORTS (PROFIT AND LOSS)
#
# = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =

# # Python Standard Library Imports
# import csv
# from decimal import Decimal
# from io import BytesIO
# import logging

# # Django Imports
# from django.http import HttpResponse
# from django.shortcuts import redirect
# from django.utils import timezone
# from django.utils.translation import gettext_lazy as _
# from django.views.generic import TemplateView
# from django.contrib import messages

# # Third-Party Imports
# try:
#     import openpyxl
#     from openpyxl.styles import Font, Alignment
#     from openpyxl.utils import get_column_letter
# except ImportError:
#     openpyxl = None

# # Local Application Imports
# from .mixins import TenantPermissionRequiredMixin, BaseReportViewMixin
# from .forms import IncomeExpenseReportForm # A form with start_date and end_date
# from .models import Account, AccountType
# from .utils import render_to_pdf
# from schools.models import SchoolProfile

# logger = logging.getLogger(__name__)


class IncomeExpenseReportView(TenantPermissionRequiredMixin, BaseReportViewMixin, TemplateView):
    """
    Handles the display of the HTML Income & Expense (Profit & Loss) report page.
    """
    template_name = 'reporting/income_expense_report.html'
    
    # Attributes for BaseReportViewMixin
    permission_required = 'reporting.view_income_expense_report'
    report_title = _("Income Statement (Profit & Loss)")
    filter_form_class = IncomeExpenseReportForm
    export_csv_url_name = 'reporting:income_expense_export_csv'
    export_pdf_url_name = 'reporting:income_expense_export_pdf'
    export_excel_url_name = 'reporting:income_expense_export_excel'

    def _get_balances_by_classification(self, classification, start_date, end_date):
        """Helper to get accounts and total balance for a given classification."""
        accounts = Account.objects.filter(
            account_type__classification=classification,
            is_active=True
        ).select_related('account_type').order_by('code')

        items_list = []
        total_balance = Decimal('0.00')

        for account in accounts:
            balance = account.get_balance(start_date=start_date, as_of_date=end_date)
            if balance != Decimal('0.00'):
                # For display, revenues are positive, expenses are positive.
                # get_balance returns credits as negative, debits as positive.
                # So we negate revenue balances.
                display_balance = -balance if classification == AccountType.ClassificationChoices.REVENUE else balance
                items_list.append({'name': account.name, 'amount': display_balance})
                total_balance += display_balance
                
        return items_list, total_balance

    def _get_income_expense_data(self, request):
        """
        The core logic for fetching and processing all data for the Income & Expense report.
        """
        # 1. Initialize data structure
        data = {
            'start_date': None, 'end_date': None,
            'revenues': [], 'total_revenue': Decimal('0.00'),
            'cost_of_sales': [], 'total_cogs': Decimal('0.00'),
            'operating_expenses': [], 'total_expenses': Decimal('0.00'),
            'gross_profit': Decimal('0.00'), 'net_profit': Decimal('0.00'),
            'filter_form': self.filter_form_class(request.GET or None)
        }

        # 2. Handle form and dates
        if not data['filter_form'].is_bound:
            today = timezone.localdate()
            first_day_current_month = today.replace(day=1)
            last_day_previous_month = first_day_current_month - timezone.timedelta(days=1)
            first_day_previous_month = last_day_previous_month.replace(day=1)
            data['filter_form'] = self.filter_form_class(initial={
                'start_date': first_day_previous_month, 'end_date': last_day_previous_month
            })
            data['filter_form'].is_valid()

        if not data['filter_form'].is_valid():
            return data

        start_date = data['filter_form'].cleaned_data.get('start_date')
        end_date = data['filter_form'].cleaned_data.get('end_date')
        data.update({'start_date': start_date, 'end_date': end_date})

        # 3. Fetch data for each P&L section
        data['revenues'], data['total_revenue'] = self._get_balances_by_classification(
            AccountType.ClassificationChoices.REVENUE, start_date, end_date
        )
        data['cost_of_sales'], data['total_cogs'] = self._get_balances_by_classification(
            AccountType.ClassificationChoices.COGS, start_date, end_date
        )
        data['operating_expenses'], data['total_expenses'] = self._get_balances_by_classification(
            AccountType.ClassificationChoices.EXPENSE, start_date, end_date
        )

        # 4. Calculate derived totals
        data['gross_profit'] = data['total_revenue'] - data['total_cogs']
        data['net_profit'] = data['gross_profit'] - data['total_expenses']

        return data

    def get_context_data(self, **kwargs):
        """Prepares context for the HTML page."""
        context = super().get_context_data(**kwargs)
        report_data = self._get_income_expense_data(self.request)
        context.update(report_data)
        
        if context.get('start_date') and context.get('end_date'):
            start, end = context['start_date'], context['end_date']
            context['report_title'] = _(f"Income Statement from {start.strftime('%d-%b-%Y')} to {end.strftime('%d-%b-%Y')}")
            
        context['school_profile'] = SchoolProfile.objects.first()
        return context


class IncomeExpenseReportExportCSVView(IncomeExpenseReportView):
    """Handles CSV export for the Income & Expense Report."""
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        report_data = self._get_income_expense_data(request)
        start, end = report_data.get('start_date'), report_data.get('end_date')
        if not all([start, end]):
            messages.error(request, "Please select a date range to generate the CSV export.")
            return redirect('reporting:income_expense_report')

        response = HttpResponse(content_type='text/csv')
        filename = f"Income_Statement_{start.strftime('%Y%m%d')}_{end.strftime('%Y%m%d')}.csv"
        response['Content-Disposition'] = f'attachment; filename="{filename}"'
        writer = csv.writer(response)

        def write_section(title, items, total, total_label):
            writer.writerow([title, ''])
            for item in items:
                writer.writerow([f"  {item['name']}", item['amount']])
            writer.writerow([total_label, total])
            writer.writerow([])
        
        write_section('REVENUE', report_data['revenues'], report_data['total_revenue'], 'Total Revenue')
        write_section('COST OF SALES', report_data['cost_of_sales'], report_data['total_cogs'], 'Total Cost of Sales')
        writer.writerow(['GROSS PROFIT', report_data['gross_profit']])
        writer.writerow([])
        write_section('OPERATING EXPENSES', report_data['operating_expenses'], report_data['total_expenses'], 'Total Operating Expenses')
        writer.writerow(['NET PROFIT / (LOSS)', report_data['net_profit']])
        
        return response


class IncomeExpenseReportExportExcelView(IncomeExpenseReportView):
    """Handles Excel export for the Income & Expense Report."""
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        if not openpyxl:
            messages.error(request, "Excel export library not available.")
            return redirect('reporting:income_expense_report')

        report_data = self._get_income_expense_data(request)
        start, end = report_data.get('start_date'), report_data.get('end_date')
        if not all([start, end]):
            messages.error(request, "Please select a date range to generate the Excel export.")
            return redirect('reporting:income_expense_report')

        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "Income Statement"
        
        # Styles
        title_font, header_font, bold_font = Font(bold=True, size=14), Font(bold=True, size=12), Font(bold=True)
        currency_format = '#,##0.00'

        # Helper
        row = 1
        def write_excel_section(title, items, total, total_label):
            nonlocal row
            ws.cell(row=row, column=1, value=title).font = header_font
            row += 1
            for item in items:
                ws.cell(row=row, column=1, value=item['name']).alignment = Alignment(indent=1)
                ws.cell(row=row, column=2, value=item['amount'])
                row += 1
            ws.cell(row=row, column=1, value=total_label).font = bold_font
            ws.cell(row=row, column=2, value=total).font = bold_font
            row += 2

        # Write Data
        write_excel_section("REVENUE", report_data['revenues'], report_data['total_revenue'], "Total Revenue")
        write_excel_section("COST OF SALES", report_data['cost_of_sales'], report_data['total_cogs'], "Total Cost of Sales")
        ws.cell(row=row, column=1, value="GROSS PROFIT").font = header_font
        ws.cell(row=row, column=2, value=report_data['gross_profit']).font = header_font
        row += 2
        write_excel_section("OPERATING EXPENSES", report_data['operating_expenses'], report_data['total_expenses'], "Total Operating Expenses")
        ws.cell(row=row, column=1, value="NET PROFIT / (LOSS)").font = header_font
        ws.cell(row=row, column=2, value=report_data['net_profit']).font = header_font

        # Formatting
        ws.column_dimensions['A'].width = 45
        ws.column_dimensions['B'].width = 20
        for cell in ws['B']: cell.number_format = currency_format

        response = HttpResponse(content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        filename = f"Income_Statement_{start.strftime('%Y%m%d')}_{end.strftime('%Y%m%d')}.xlsx"
        response['Content-Disposition'] = f'attachment; filename="{filename}"'
        wb.save(response)
        return response


class IncomeExpenseReportExportPDFView(IncomeExpenseReportView):
    """Handles PDF export for the Income & Expense Report."""
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        report_data = self._get_income_expense_data(request)
        start, end = report_data.get('start_date'), report_data.get('end_date')
        if not all([start, end]):
            messages.error(request, "Please select a date range to generate the PDF.")
            return redirect('reporting:income_expense_report')
        
        pdf_context = report_data.copy()
        pdf_context.update({
            'report_title': _(f"Income Statement from {start.strftime('%d-%b-%Y')} to {end.strftime('%d-%b-%Y')}"),
            'school_profile': SchoolProfile.objects.first(),
            'tenant': getattr(request, 'tenant', None),
            'current_datetime': timezone.now()
        })
        
        try:
            pdf = render_to_pdf('reporting/pdf/income_expense_report_pdf.html', pdf_context)
            if pdf:
                response = HttpResponse(pdf, content_type='application/pdf')
                filename = f"Income_Statement_{start.strftime('%Y%m%d')}.pdf"
                response['Content-Disposition'] = f'inline; filename="{filename}"'
                return response
        except Exception as e:
            logger.error(f"Error generating Income Statement PDF: {e}", exc_info=True)
            messages.error(request, "An error occurred while generating the PDF report.")
            
        return redirect('reporting:income_expense_report')


# # apps/reporting/base_views.py (or in views.py if not many reports)
# import logging
# from django.utils import timezone
# from django.views.generic import TemplateView # TemplateView is a good base for reports
# from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin
# from django.urls import reverse_lazy
# from django.contrib import messages
# from django.utils.translation import gettext_lazy as _

# logger = logging.getLogger(__name__)


# # apps/reporting/views.py
# # (Keep imports from BaseReportViewMixin section: logging, Decimal, timezone, TemplateView, etc.)
# from decimal import Decimal
# from django.utils import timezone # Ensure timezone is imported if used for defaults
# from django.conf import settings # For ACCOUNTING_..._TYPE_CODE
# from django.contrib import messages # For error messages
# from django.utils.translation import gettext_lazy as _
# from apps.accounting.models import AccountType # Assuming AccountType model is here
# # Import your JournalEntryLine or relevant transaction model for _get_balances_by_account_type
# # from apps.accounting.models import JournalEntryLine 
# import logging

# logger = logging.getLogger(__name__)

# class IncomeExpenseReportView(TenantPermissionRequiredMixin, BaseReportViewMixin, TemplateView):
#     permission_required = 'reporting.view_income_expense_report' # Specific permission
#     template_name = 'reporting/income_expense_report.html'      # Your specific template

#     report_code = 'RPT_INCOME_EXPENSE'
#     filter_form_class = IncomeExpenseReportForm # The form defined above

#     def get_report_title(self): 
#         return _("Income Statement (Profit & Loss)") # Use _() for translation
    
#     def get_initial_filter_data(self):
#         # Default: Previous full calendar month
#         today = timezone.localdate()
#         first_day_current_month = today.replace(day=1)
#         last_day_previous_month = first_day_current_month - timezone.timedelta(days=1)
#         first_day_previous_month = last_day_previous_month.replace(day=1)
#         return {'start_date': first_day_previous_month, 'end_date': last_day_previous_month}

#     def _get_balances_by_account_type(self, account_type_instance, start_date, end_date):
#         """ Helper to calculate total balance for accounts of a specific type. """
#         items_list, total_balance_for_type = [], Decimal('0.00')
#         if not account_type_instance:
#             return items_list, total_balance_for_type

#         accounts_of_this_type = Account.objects.filter(
#             account_type=account_type_instance,
#             is_active=True # Only consider active accounts in CoA
#         ).order_by('code')

#         for acc in accounts_of_this_type:
#             # Use the Account model's get_balance method for period calculation
#             balance = acc.get_balance(start_date=start_date, as_of_date=end_date)

#             if balance != Decimal('0.00'): # Only list accounts with activity in the period
#                 items_list.append({
#                     'account_code': acc.code or 'N/A',
#                     'account_name': acc.name,
#                     'amount': balance
#                 })
#             total_balance_for_type += balance

#         return items_list, total_balance_for_type

#     def get_report_data(self, filter_form=None): # Signature corrected
#         """
#         Fetches and calculates data for the Income & Expense (Profit & Loss) Report.
#         Uses self.form (set by BaseReportViewMixin.get()) for filter criteria.
#         """
#         start_date_filter, end_date_filter = None, None
        
#         # Initialize the report_data dictionary with all expected keys and default values
#         report_data = {
#             'start_date_filter': None, 'end_date_filter': None, # Store the actual dates used for the report
#             'revenues': [], 'total_revenue': Decimal('0.00'),
#             'cost_of_sales': [], 'total_cost_of_sales': Decimal('0.00'),
#             'operating_expenses': [], 'total_operating_expenses': Decimal('0.00'),
#             'gross_profit': Decimal('0.00'),
#             'operating_income': Decimal('0.00'),
#             'other_income_items': [], 'total_other_income': Decimal('0.00'),
#             'other_expense_items': [], 'total_other_expenses': Decimal('0.00'),
#             'income_before_tax': Decimal('0.00'),
#             'tax_expense': Decimal('0.00'),
#             'net_profit': Decimal('0.00'),
#             'report_generated_successfully': False # Flag to indicate if data fetching was successful
#         }

#         form_instance = self.form # Access the form instance set by BaseReportViewMixin's get() method

#         if form_instance: # Check if a filter form is even being used for this report
#             if form_instance.is_valid(): # Form was bound with GET data and is valid
#                 start_date_filter = form_instance.cleaned_data.get('start_date')
#                 end_date_filter = form_instance.cleaned_data.get('end_date')
#                 logger.debug(f"{self.__class__.__name__}: Using valid filter dates: Start={start_date_filter}, End={end_date_filter}")
#             elif not form_instance.is_bound and form_instance.initial: # Form is unbound, use its initial data
#                 start_date_filter = form_instance.initial.get('start_date')
#                 end_date_filter = form_instance.initial.get('end_date')
#                 logger.debug(f"{self.__class__.__name__}: Using initial filter dates: Start={start_date_filter}, End={end_date_filter}")
#             else: # Form is bound but invalid, or unbound with no initial data relevant for date range
#                 logger.warning(f"{self.__class__.__name__}.get_report_data: Form is invalid or has no applicable date range. Errors: {form_instance.errors if form_instance.is_bound else 'Unbound with no relevant initial data'}")
#                 messages.error(self.request, _("Please provide a valid date range for the report."))
#                 return report_data # Return empty structure with report_generated_successfully = False
#         else: # No filter form was defined for this report view
#             logger.info(f"{self.__class__.__name__}: No filter form class. Attempting to run report without filters (e.g., for all time or default period).")
#             # Decide on default behavior: run for all time? Specific default period? Or error?
#             # For an Income Statement, a period is usually required.
#             # If you want to enforce filters, this branch might also return report_data.
#             # For now, let's assume it tries to run with no date filters, which might be problematic.
#             # It's better for the filter form to provide default dates if no GET params are given.
#             # The get_filter_form_initial_data() in the view can set these.
#             messages.warning(self.request, _("Date range not specified. Report may show all-time data or default period."))
#             # Fallback to a very wide range or a default if truly no form is used
#             # start_date_filter = very_old_date 
#             # end_date_filter = timezone.now().date()

#         if not (start_date_filter and end_date_filter):
#             logger.error(f"{self.__class__.__name__}: Date range (start_date or end_date) is missing after form processing.")
#             messages.error(self.request, _("A valid date range (both start and end date) is required for this report."))
#             return report_data # Return empty structure

#         report_data.update({
#             'start_date_filter': start_date_filter, 
#             'end_date_filter': end_date_filter
#         })

#         try:
#             # Get account types by classification instead of relying on settings
#             revenue_types = AccountType.objects.filter(classification=AccountType.ClassificationChoices.REVENUE)
#             expense_types = AccountType.objects.filter(classification=AccountType.ClassificationChoices.EXPENSE)
#             cogs_types = AccountType.objects.filter(classification=AccountType.ClassificationChoices.COGS)

#         except Exception as e: # Catch potential errors during AccountType fetching
#             logger.error(f"IncomeExpenseReport: Error fetching AccountTypes: {e}", exc_info=True)
#             messages.error(self.request, _("An error occurred while loading accounting configurations."))
#             return report_data

#         # Fetch balances for each classification
#         try:
#             # Calculate revenue totals
#             revenue_items = []
#             total_revenue = Decimal('0.00')
#             for revenue_type in revenue_types:
#                 items, total = self._get_balances_by_account_type(revenue_type, start_date_filter, end_date_filter)
#                 revenue_items.extend(items)
#                 total_revenue += total

#             # Calculate COGS totals
#             cogs_items = []
#             total_cogs = Decimal('0.00')
#             for cogs_type in cogs_types:
#                 items, total = self._get_balances_by_account_type(cogs_type, start_date_filter, end_date_filter)
#                 cogs_items.extend(items)
#                 total_cogs += total

#             # Calculate expense totals
#             expense_items = []
#             total_expenses = Decimal('0.00')
#             for expense_type in expense_types:
#                 items, total = self._get_balances_by_account_type(expense_type, start_date_filter, end_date_filter)
#                 expense_items.extend(items)
#                 total_expenses += total

#             # Update report data
#             report_data['revenues'] = revenue_items
#             report_data['total_revenue'] = total_revenue
#             report_data['cost_of_sales'] = cogs_items
#             report_data['total_cost_of_sales'] = total_cogs
#             report_data['operating_expenses'] = expense_items
#             report_data['total_operating_expenses'] = total_expenses

#         except Exception as e:
#             logger.error(f"IncomeExpenseReport: Error in _get_balances_by_account_type: {e}", exc_info=True)
#             messages.error(self.request, _("An error occurred while calculating account balances for the report."))
#             return report_data # Return partially filled or empty data

#         # Calculate derived values
#         report_data['gross_profit'] = report_data['total_revenue'] - report_data['total_cost_of_sales']
#         report_data['operating_income'] = report_data['gross_profit'] - report_data['total_operating_expenses']
        
#         # Further calculations for net profit
#         # report_data['income_before_tax'] = report_data['operating_income'] + report_data['total_other_income'] - report_data['total_other_expenses']
#         # report_data['net_profit'] = report_data['income_before_tax'] - report_data['tax_expense']
#         # For now, simplified:
#         report_data['net_profit'] = report_data['operating_income'] # Adjust as you add other income/expense/tax

#         report_data['report_generated_successfully'] = True # Set flag
        
#         logger.info(
#             f"Income & Expense Report generated for period {start_date_filter} to {end_date_filter}. "
#             f"Total Revenue: {report_data['total_revenue']}, Net Profit: {report_data['net_profit']}"
#         )
#         return report_data
#         # get_context_data() is inherited from BaseReportViewMixin.
#         # It will call get_report_title(), get_filter_form(), and get_report_data(form).


#     def export_to_excel(self, report_data_dict, request):
#         # ... (Excel export for P&L as you defined before, using report_data_dict) ...
#         response = HttpResponse(content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
#         filename = f"{self.report_code}_{report_data_dict['start_date'].strftime('%Y%m%d')}_to_{report_data_dict['end_date'].strftime('%Y%m%d')}.xlsx"
#         response['Content-Disposition'] = f'attachment; filename="{filename}"'
#         wb = openpyxl.Workbook(); ws = wb.active; ws.title = "Income Statement"
#         title_font=Font(bold=True, size=14); header_font=Font(bold=True,size=12); sub_header_font=Font(bold=True); currency_format='#,##0.00'
#         ws.merge_cells('A1:C1'); title_cell=ws['A1']; title_cell.value=self.get_report_title().upper(); title_cell.font=title_font; title_cell.alignment=Alignment(horizontal="center")
#         ws.merge_cells('A2:C2'); date_cell=ws['A2']; date_cell.value=f"For period: {report_data_dict['start_date'].strftime('%d %b %Y')} to {report_data_dict['end_date'].strftime('%d %b %Y')} "; date_cell.alignment=Alignment(horizontal="center")
#         row = 4
#         def write_pl_section(title, items, total_val, total_label="Total"):
#             nonlocal row
#             ws.cell(row=row, column=1, value=title).font = header_font; row +=1
#             for item in items: ws.cell(row=row, column=1, value=f"    {item['account_name']}"); ws.cell(row=row, column=2, value=item['amount']).number_format=currency_format; row +=1
#             ws.cell(row=row, column=1, value=total_label).font=sub_header_font; ws.cell(row=row, column=2, value=total_val).font=sub_header_font; ws.cell(row=row, column=2).number_format=currency_format; row +=2
        
#         write_pl_section("Revenue", report_data_dict['revenues'], report_data_dict['total_revenue'])
#         write_pl_section("Cost of Sales", report_data_dict['cost_of_sales'], report_data_dict['total_cost_of_sales'])
#         ws.cell(row=row, column=1, value="GROSS PROFIT").font=header_font; ws.cell(row=row, column=2, value=report_data_dict['gross_profit']).font=header_font; ws.cell(row=row, column=2).number_format=currency_format; row+=2
#         write_pl_section("Operating Expenses", report_data_dict['operating_expenses'], report_data_dict['total_operating_expenses'])
#         ws.cell(row=row, column=1, value="OPERATING INCOME").font=header_font; ws.cell(row=row, column=2, value=report_data_dict['operating_income']).font=header_font; ws.cell(row=row, column=2).number_format=currency_format; row+=2
#         ws.cell(row=row, column=1, value="NET PROFIT / (LOSS)").font=Font(bold=True, size=13); np_cell=ws.cell(row=row, column=2, value=report_data_dict['net_profit']); np_cell.font=Font(bold=True, size=13); np_cell.number_format=currency_format
#         ws.column_dimensions['A'].width = 45; ws.column_dimensions['B'].width = 20
#         output = BytesIO(); wb.save(output); output.seek(0); response.write(output.getvalue()); return response

#     def export_to_pdf(self, report_data_dict, request, html_context): # report_data is dict
#         pdf_context = {
#             'report_data': report_data_dict, 'school_profile': self.get_school_profile(),
#             'report_title': self.get_report_title(), 'current_datetime': timezone.now(),
#         }
#         pdf = render_to_pdf(self.get_pdf_template_name(), pdf_context)
#         if pdf:
#             response = HttpResponse(pdf, content_type='application/pdf')
#             filename = f"{self.report_code}_{report_data_dict['start_date'].strftime('%Y%m%d')}_{report_data_dict['end_date'].strftime('%Y%m%d')}.pdf"
#             response['Content-Disposition'] = f'inline; filename="{filename}"'
#             return response
#         else:
#             logger.error(f"PDF generation failed for {self.report_code} (P&L)")
#             messages.error(request, "Failed to generate PDF report.")
#             return redirect(request.path)



# = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = 
#
#      EXPENSE REPORT VIEW
#
# = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
# # D:\school_fees_saas_v2\apps\reporting\views.py
# # Python Standard Library Imports
# import csv
# from decimal import Decimal
# from io import BytesIO
# import logging

# # Django Imports
# from django.core.paginator import Paginator
# from django.db.models import Sum, Count, Q
# from django.db.models.functions import Coalesce
# from django.http import HttpResponse
# from django.shortcuts import redirect
# from django.utils import timezone
# from django.utils.translation import gettext_lazy as _
# from django.views.generic import TemplateView
# from django.contrib import messages

# # Third-Party Imports
# try:
#     import openpyxl
#     from openpyxl.styles import Font
#     from openpyxl.utils import get_column_letter
# except ImportError:
#     openpyxl = None

# # Local Application Imports
# from apps.common.mixins import TenantPermissionRequiredMixin, BaseReportViewMixin
# from .forms import ExpenseReportFilterForm # Your filter form
# from .models import Expense, ExpenseCategory, PaymentMethod # Your finance models
# from .utils import render_to_pdf
# from schools.models import SchoolProfile

# logger = logging.getLogger(__name__)


class ExpenseReportView(TenantPermissionRequiredMixin, BaseReportViewMixin, TemplateView):
    """
    Handles the display of the HTML Expense Report page.
    """
    template_name = 'reporting/expense_report.html'
    
    # Attributes for BaseReportViewMixin
    permission_required = 'reporting.view_expense_report'
    report_title = _("Expense Report")
    filter_form_class = ExpenseReportFilterForm
    export_csv_url_name = 'reporting:expense_report_export_csv'
    export_pdf_url_name = 'reporting:expense_report_export_pdf'
    export_excel_url_name = 'reporting:expense_report_export_excel'
    paginate_by = 30

    def _get_expense_data(self, request):
        """
        The core logic for fetching and processing all data for the Expense Report.
        """
        # 1. Initialize the data structure
        data = {
            'expenses_page': None, 'full_queryset': Expense.objects.none(),
            'total_expenses': Decimal('0.00'), 'expense_count': 0,
            'expenses_by_category': [], 'start_date': None, 'end_date': None,
            'filter_form': self.filter_form_class(request.GET or None)
        }
        
        # 2. Set default date range if form is not submitted
        if not request.GET:
            today = timezone.now().date()
            start_date = today.replace(day=1) # Default to current month start
            data['filter_form'] = self.filter_form_class(initial={'start_date': start_date, 'end_date': today})

        # 3. Validate form and apply filters
        base_queryset = Expense.objects.select_related(
            'category', 'payment_method', 'recorded_by'
        ).order_by('-expense_date')

        filtered_queryset = base_queryset
        if data['filter_form'].is_valid():
            cd = data['filter_form'].cleaned_data
            data['start_date'] = cd.get('start_date')
            data['end_date'] = cd.get('end_date')

            if data['start_date']:
                filtered_queryset = filtered_queryset.filter(expense_date__gte=data['start_date'])
            if data['end_date']:
                filtered_queryset = filtered_queryset.filter(expense_date__lte=data['end_date'])
            if cd.get('category'):
                filtered_queryset = filtered_queryset.filter(category=cd['category'])
            if cd.get('payment_method'):
                filtered_queryset = filtered_queryset.filter(payment_method=cd['payment_method'])
            if cd.get('description_contains'):
                filtered_queryset = filtered_queryset.filter(description__icontains=cd['description_contains'])
        
        data['full_queryset'] = filtered_queryset

        # 4. Calculate summary totals from the full (un-paginated) queryset
        if filtered_queryset.exists():
            summary = filtered_queryset.aggregate(
                total=Coalesce(Sum('amount'), Decimal('0.00')),
                count=Count('id')
            )
            data['total_expenses'] = summary['total']
            data['expense_count'] = summary['count']
            
            data['expenses_by_category'] = list(
                filtered_queryset.values('category__name')
                .annotate(total=Sum('amount'), count_items=Count('id'))
                .order_by('-total')
                .filter(category__name__isnull=False)
            )

        # 5. Paginate the results for the HTML view
        paginator = Paginator(filtered_queryset, self.paginate_by)
        page_number = request.GET.get('page', 1)
        data['expenses_page'] = paginator.get_page(page_number)
        
        return data

    def get_context_data(self, **kwargs):
        """Prepares context for the HTML page."""
        context = super().get_context_data(**kwargs)
        report_data = self._get_expense_data(self.request)
        context.update(report_data)
        
        if context.get('start_date') and context.get('end_date'):
            start, end = context['start_date'], context['end_date']
            context['report_title'] = _(f"Expense Report from {start.strftime('%d-%b-%Y')} to {end.strftime('%d-%b-%Y')}")
            
        context['school_profile'] = SchoolProfile.objects.first()
        return context


class ExpenseReportExportCSVView(ExpenseReportView):
    """Handles CSV export for the Expense Report."""
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        report_data = self._get_expense_data(request)
        queryset = report_data.get('full_queryset')

        response = HttpResponse(content_type='text/csv')
        filename = f"Expense_Report_{timezone.now().strftime('%Y%m%d')}.csv"
        response['Content-Disposition'] = f'attachment; filename="{filename}"'
        writer = csv.writer(response)
        
        headers = ['Date', 'Description', 'Category', 'Amount', 'Method', 'Recorded By']
        writer.writerow(headers)

        for expense in queryset:
            writer.writerow([
                expense.expense_date.strftime('%Y-%m-%d'),
                expense.description,
                expense.category.name if expense.category else 'N/A',
                expense.amount,
                expense.payment_method.name if expense.payment_method else 'N/A',
                expense.recorded_by.get_full_name() if expense.recorded_by else 'N/A'
            ])
        return response


class ExpenseReportExportExcelView(ExpenseReportView):
    """Handles Excel export for the Expense Report."""
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        if not openpyxl:
            messages.error(request, "Excel export library not available.")
            return redirect('reporting:expense_report')

        report_data = self._get_expense_data(request)
        queryset = report_data.get('full_queryset')
        
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "Expense Report"
        
        headers = ['Date', 'Description', 'Category', 'Amount', 'Method', 'Recorded By']
        ws.append(headers)
        header_font = Font(bold=True)
        for cell in ws[1]:
            cell.font = header_font
        
        currency_format = '#,##0.00'
        date_format = 'YYYY-MM-DD'

        for expense in queryset:
            ws.append([
                expense.expense_date,
                expense.description,
                expense.category.name if expense.category else 'N/A',
                expense.amount,
                expense.payment_method.name if expense.payment_method else 'N/A',
                expense.recorded_by.get_full_name() if expense.recorded_by else 'N/A'
            ])
            ws.cell(row=ws.max_row, column=1).number_format = date_format
            ws.cell(row=ws.max_row, column=4).number_format = currency_format

        # Auto-fit columns
        ws.column_dimensions['A'].width = 15
        ws.column_dimensions['B'].width = 40
        ws.column_dimensions['C'].width = 25
        ws.column_dimensions['D'].width = 18
        ws.column_dimensions['E'].width = 20
        ws.column_dimensions['F'].width = 25

        response = HttpResponse(content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        filename = f"Expense_Report_{timezone.now().strftime('%Y%m%d')}.xlsx"
        response['Content-Disposition'] = f'attachment; filename="{filename}"'
        wb.save(response)
        return response


class ExpenseReportExportPDFView(ExpenseReportView):
    """Handles PDF export for the Expense Report."""
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        report_data = self._get_expense_data(request)
        start, end = report_data.get('start_date'), report_data.get('end_date')
        if not all([start, end]):
            messages.error(request, "Please select a date range to generate the PDF.")
            return redirect('reporting:expense_report')
        
        pdf_context = report_data.copy()
        pdf_context['report_title'] = _(f"Expense Report from {start.strftime('%d-%b-%Y')} to {end.strftime('%d-%b-%Y')}")
        pdf_context.update({
            'school_profile': SchoolProfile.objects.first(),
            'tenant': getattr(request, 'tenant', None),
            'current_datetime': timezone.now()
        })
        
        try:
            pdf = render_to_pdf('reporting/pdf/expense_report_pdf.html', pdf_context)
            if pdf:
                response = HttpResponse(pdf, content_type='application/pdf')
                filename = f"Expense_Report_{start.strftime('%Y%m%d')}.pdf"
                response['Content-Disposition'] = f'inline; filename="{filename}"'
                return response
        except Exception as e:
            logger.error(f"Error generating Expense Report PDF: {e}", exc_info=True)
            messages.error(request, "An error occurred while generating the PDF report.")
            
        return redirect('reporting:expense_report')
    

# from django.db.models import Sum, Count, F, Q, Value, CharField, Case, When, TextField, DecimalField
# from django.db.models.functions import Coalesce
# from decimal import Decimal
# from apps.finance.models import Expense, ExpenseCategory # Your Expense model
# from .forms import ExpenseReportFilterForm 

# # ... (other report views) ...

# class ExpenseReportView(StaffLoginRequiredMixin, TenantPermissionRequiredMixin, BaseReportViewMixin, ListView):
#     login_url = reverse_lazy('schools:staff_login')
#     permission_required = 'reporting.view_expense_report' # Define this permission
    
#     model = Expense # Main model for the ListView
#     template_name = 'reporting/expense_report.html'
#     context_object_name = 'expenses_on_page' # For paginated list
#     paginate_by = 30

#     # Attributes for BaseReportViewMixin
#     report_title_text = _("Expense Report")
#     report_code = "RPT_EXPENSE"
#     filter_form_class = ExpenseReportFilterForm

#     # Internal attribute to store full filtered queryset
#     _full_filtered_queryset = None

#     def get_filter_form_initial_data(self):
#         # Default to current month
#         today = timezone.now().date()
#         return {
#             'start_date': today.replace(day=1),
#             'end_date': today
#         }
    
#     def get_report_data(self, filter_form=None): # Signature corrected
#         """
#         Calculates summary data for the expense report.
#         Uses self.form (set and validated/processed in BaseReportViewMixin.get()).
#         """
#         report_specific_data = {
#             'summary_total_expenses': Decimal('0.00'),
#             'summary_expense_count': 0,
#             'expenses_by_category': [], # Example summary
#             'report_start_date': None, # For displaying in template
#             'report_end_date': None,   # For displaying in template
#         }
        
#         current_form = self.form # Access the form instance set by BaseReportViewMixin's get()

#         logger.debug(
#             f"{self.__class__.__name__}.get_report_data called. "
#             f"self.form type: {type(current_form)}, "
#             f"bound: {current_form.is_bound if current_form else 'N/A'}, "
#             f"valid: {current_form.is_valid() if current_form and current_form.is_bound else 'N/A'}"
#         )

#         # Determine date range to use from the form
#         start_date_filter = None
#         end_date_filter = None

#         if current_form: # Check if a form instance exists
#             if current_form.is_valid():
#                 start_date_filter = current_form.cleaned_data.get('start_date')
#                 end_date_filter = current_form.cleaned_data.get('end_date')
#                 logger.debug(f"{self.__class__.__name__}: Using filter dates from valid form: Start={start_date_filter}, End={end_date_filter}")
#             elif not current_form.is_bound and current_form.initial: # Form is unbound, use its initial values
#                 start_date_filter = current_form.initial.get('start_date')
#                 end_date_filter = current_form.initial.get('end_date')
#                 logger.debug(f"{self.__class__.__name__}: Using initial filter dates: Start={start_date_filter}, End={end_date_filter}")
#             # If form is bound but invalid, filters remain None, report will be unfiltered or empty based on get_queryset
            
#         report_specific_data['report_start_date'] = start_date_filter
#         report_specific_data['report_end_date'] = end_date_filter

#         # Get the full filtered queryset for summary calculations
#         # _full_filtered_queryset is set by get_queryset calling _apply_filters_to_queryset
#         # If get_queryset hasn't run yet (e.g., if BaseReportViewMixin.get() calls this *before* super().get()),
#         # then _full_filtered_queryset might not be set.
#         # It's safer for get_report_data to use _apply_filters_to_queryset on a base queryset.
        
#         base_summary_qs = self.model.objects.all() # Start with Expense.objects.all()
#         queryset_for_summary = self._apply_filters_to_queryset(base_summary_qs) # Apply same filters

#         if queryset_for_summary.exists() or \
#             (current_form and not current_form.is_bound and current_form.initial): # Calculate even for initial load
            
#             summary_aggregates = queryset_for_summary.aggregate(
#                 total_amount=Coalesce(Sum('amount'), Decimal('0.00')),
#                 count=Count('id')
#             )
#             report_specific_data['summary_total_expenses'] = summary_aggregates.get('total_amount')
#             report_specific_data['summary_expense_count'] = summary_aggregates.get('count')

#             # Example: Expenses grouped by category
#             report_specific_data['expenses_by_category'] = list(
#                 queryset_for_summary
#                 .values('category__name') # Assuming Expense model has FK to ExpenseCategory
#                 .annotate(total=Sum('amount'), count_items=Count('id'))
#                 .order_by('-total')
#                 .filter(category__name__isnull=False) # Exclude uncategorized if category can be null
#             )
#             logger.info(f"{self.__class__.__name__}: Summarized expenses. Total: {report_specific_data['summary_total_expenses']}")
#         else:
#             logger.info(f"{self.__class__.__name__}.get_report_data: No data after filtering or form invalid for summary.")
#             # Defaults are already set in report_specific_data

#         return report_specific_data

#     def _apply_filters_to_queryset(self, queryset):
#         # self.form is instantiated in BaseReportViewMixin.get()
        
#         # Check if self.form exists before trying to access its attributes
#         if not hasattr(self, 'form') or self.form is None:
#             logger.warning(f"{self.__class__.__name__}._apply_filters_to_queryset: self.form is not set. No filters will be applied.")
#             return queryset # Return the original queryset if no form is available

#         current_form = self.form # Use a local variable for clarity

#         if current_form.is_valid(): # Form was bound with GET data and is valid
#             logger.debug(f"{self.__class__.__name__}._apply_filters_to_queryset: Applying filters from cleaned_data: {current_form.cleaned_data}")
#             cd = current_form.cleaned_data
#             if cd.get('start_date'):
#                 queryset = queryset.filter(expense_date__gte=cd['start_date'])
#             if cd.get('end_date'):
#                 queryset = queryset.filter(expense_date__lte=cd['end_date'])
#             if cd.get('category'):
#                 queryset = queryset.filter(category=cd['category'])
#             if cd.get('payment_method'):
#                 queryset = queryset.filter(payment_method=cd['payment_method'])
#             if cd.get('description_contains'):
#                 queryset = queryset.filter(description__icontains=cd['description_contains'])
#             # Add other filters based on your ExpenseReportFilterForm fields
        
#         elif not current_form.is_bound and current_form.initial: # Form is unbound, use its initial values
#             logger.debug(f"{self.__class__.__name__}._apply_filters_to_queryset: Applying initial filters: {current_form.initial}")
#             initial = current_form.initial
#             if initial.get('start_date'): 
#                 queryset = queryset.filter(expense_date__gte=initial['start_date'])
#             if initial.get('end_date'): 
#                 queryset = queryset.filter(expense_date__lte=initial['end_date'])
#             if initial.get('category'): 
#                 queryset = queryset.filter(category=initial['category'])
#             if initial.get('payment_method'): 
#                 queryset = queryset.filter(payment_method=initial['payment_method'])
#             if initial.get('description_contains'):
#                 # Typically, you wouldn't filter by description_contains on initial load
#                 # unless initial['description_contains'] has a value.
#                 pass
#             # Add other initial filters if applicable
            
#         elif current_form.is_bound and not current_form.is_valid(): # Form is bound but invalid
#             logger.warning(f"{self.__class__.__name__}._apply_filters_to_queryset: Filter form is bound but invalid. Returning empty queryset. Errors: {current_form.errors}")
#             return queryset.none() # Return an empty queryset for invalid filters
        
#         # If form is unbound and has no initial data, or if filters didn't match anything,
#         # the original (or partially filtered) queryset is returned.
#         return queryset # <<< CORRECT: Return the (potentially modified) queryset
    
    
#     def get_export_queryset(self):
#         base_queryset = self.model.objects.select_related(
#             'category', 'payment_method', 'recorded_by', 'linked_account'
#         )
#         return self._apply_filters_to_queryset(base_queryset).order_by('-expense_date', '-id')

#     # --- Implement Export Methods (CSV, Excel, PDF) ---
#     def export_to_csv(self, queryset_for_export, request):
#         queryset = queryset_for_export or self.get_export_queryset()
#         response = HttpResponse(content_type='text/csv')
#         response['Content-Disposition'] = f'attachment; filename="{self.report_code}_{timezone.now().strftime("%Y%m%d")}.csv"'
#         writer = csv.writer(response)
#         headers = ['Date', 'Description', 'Category', 'Amount', 'Payment Method', 'Recorded By']
#         writer.writerow(headers)
#         for ex in queryset:
#             writer.writerow([
#                 ex.expense_date.strftime('%Y-%m-%d') if ex.expense_date else '',
#                 ex.description,
#                 ex.category.name if ex.category else 'N/A',
#                 f"{ex.amount:.2f}",
#                 ex.payment_method.name if ex.payment_method else 'N/A',
#                 ex.recorded_by.get_full_name() if ex.recorded_by else 'N/A'
#             ])
#         return response

#     def export_to_excel(self, queryset_for_export, request):
#         queryset = queryset_for_export or self.get_export_queryset()
#         response = HttpResponse(content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
#         response['Content-Disposition'] = f'attachment; filename="{self.report_code}_{timezone.now().strftime("%Y%m%d")}.xlsx"'
#         wb = openpyxl.Workbook()
#         ws = wb.active
#         ws.title = self.get_report_title()[:30]
#         headers = ['Date', 'Description', 'Category', 'Amount', 'Payment Method', 'Recorded By']
#         ws.append(headers)
#         # ... (Apply styling and formatting as in CollectionReportView) ...
#         header_font = Font(bold=True); currency_format = '#,##0.00'; date_format = 'yyyy-mm-dd'
#         for cell in ws[1]: cell.font = header_font
#         # Basic width, adjust as needed
#         for col_letter in ['A', 'B', 'C', 'D', 'E', 'F']: ws.column_dimensions[col_letter].width = 20

#         for ex in queryset:
#             ws.append([
#                 ex.expense_date,
#                 ex.description,
#                 ex.category.name if ex.category else 'N/A',
#                 ex.amount,
#                 ex.payment_method.name if ex.payment_method else 'N/A',
#                 ex.recorded_by.get_full_name() if ex.recorded_by else 'N/A'
#             ])
#             ws.cell(row=ws.max_row, column=1).number_format = date_format
#             ws.cell(row=ws.max_row, column=4).number_format = currency_format
        
#         output = BytesIO(); wb.save(output); output.seek(0)
#         response.write(output.getvalue())
#         return response

#     def get_pdf_template_name(self):
#         return f"reporting/pdf/{self.report_code}_pdf.html" # e.g., reporting/pdf/expense_report_pdf.html

#     def export_to_pdf(self, queryset_for_export, request, pdf_context=None): # pdf_context from BaseReportViewMixin
#         queryset = queryset_for_export or self.get_export_queryset()
#         if pdf_context is None:
#             pdf_context = {}
#         pdf_context['report_items'] = queryset
#         # from apps.common.utils import render_to_pdf # Ensure imported at top of views.py
#         pdf = render_to_pdf(self.get_pdf_template_name(), pdf_context)
#         if pdf:
#             response = HttpResponse(pdf, content_type='application/pdf')
#             filename = f"{self.report_code}_{timezone.now().strftime('%Y%m%d')}.pdf"
#             response['Content-Disposition'] = f'inline; filename="{filename}"'
#             return response
#         else:
#             logger.error(f"PDF generation failed for {self.report_code} ({self.get_report_title()})")
#             messages.error(request, _("Sorry, we could not generate the PDF report."))
#             query_params = request.GET.copy(); query_params.pop('export', None)
#             return HttpResponseRedirect(f"{request.path}?{query_params.urlencode()}")



# = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = 
#
#      BALANCE SHEET REPORT
#
# = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =


# # Python Standard Library Imports
# import csv
# from decimal import Decimal
# import logging

# # Django Imports
# from django.db.models import Sum
# from django.http import HttpResponse
# from django.shortcuts import redirect
# from django.utils import timezone
# from django.utils.translation import gettext_lazy as _
# from django.views.generic import TemplateView
# from django.contrib import messages

# # Third-Party Imports
# try:
#     import openpyxl
#     from openpyxl.styles import Font, Alignment, Border, Side
#     from openpyxl.utils import get_column_letter
# except ImportError:
#     openpyxl = None

# # Local Application Imports
# # Note: Replace with your actual model and form imports
# from apps.common.mixins import TenantPermissionRequiredMixin, BaseReportViewMixin
# from .forms import BalanceSheetFilterForm # Your filter form for the 'as at' date
# from .models import Account, AccountType, FinancialYear # Your accounting models
# from .utils import render_to_pdf, get_financial_year_start # Your utilities
# from schools.models import SchoolProfile

# logger = logging.getLogger(__name__)


class BalanceSheetReportView(TenantPermissionRequiredMixin, BaseReportViewMixin, TemplateView):
    """
    Handles the display of the HTML Balance Sheet report page.
    """
    template_name = 'reporting/balance_sheet_report.html'
    
    # Attributes for BaseReportViewMixin
    permission_required = 'reporting.view_balance_sheet_report'
    report_title = _("Balance Sheet")
    filter_form_class = BalanceSheetFilterForm
    export_csv_url_name = 'reporting:balance_sheet_export_csv'
    export_pdf_url_name = 'reporting:balance_sheet_export_pdf'
    export_excel_url_name = 'reporting:balance_sheet_export_excel'

    def _get_balance_sheet_data(self, request):
        """
        The core logic for fetching and processing all data for the Balance Sheet.
        This helper method is used by the HTML view and all export views.
        Returns a dictionary containing all calculated data.
        """
        # 1. Initialize the data structure to be returned
        data = {
            'as_at_date': None, 'assets': [], 'total_assets': Decimal('0.00'),
            'liabilities': [], 'total_liabilities': Decimal('0.00'),
            'equity': [], 'total_equity': Decimal('0.00'),
            'total_liabilities_and_equity': Decimal('0.00'),
            'net_income': Decimal('0.00'),
            'balance_check_difference': Decimal('0.00'),
            'filter_form': self.filter_form_class(request.GET or None)
        }

        # 2. Validate filter form
        if not data['filter_form'].is_valid():
            # Fill with default date if form is unbound on initial load
            if not data['filter_form'].is_bound:
                data['filter_form'] = self.filter_form_class(initial={'as_at_date': timezone.now().date()})
                data['filter_form'].is_valid() # Run validation on initial data
            else:
                return data # Return with form errors if bound and invalid

        as_at_date = data['filter_form'].cleaned_data.get('as_at_date', timezone.now().date())
        data['as_at_date'] = as_at_date

        # 3. Calculate Net Income for the current financial year up to the 'as_at_date'
        # This assumes a utility function or model method to find the financial year start
        try:
            fy_start_date = get_financial_year_start(as_at_date)
        except Exception as e:
            # Fallback if the utility fails or is not found
            logger.warning(f"Could not determine financial year start date: {e}. Defaulting to Jan 1st.")
            fy_start_date = as_at_date.replace(month=1, day=1)

        revenue_accounts = Account.objects.filter(account_type__classification=AccountType.ClassificationChoices.REVENUE)
        expense_accounts = Account.objects.filter(account_type__classification__in=[
            AccountType.ClassificationChoices.EXPENSE, AccountType.ClassificationChoices.COGS
        ])

        total_revenue = sum(acc.get_balance(start_date=fy_start_date, as_of_date=as_at_date) for acc in revenue_accounts)
        total_expense = sum(acc.get_balance(start_date=fy_start_date, as_of_date=as_at_date) for acc in expense_accounts)
        
        # Revenue has a credit balance (negative), so we expect total_revenue to be <= 0.
        # We negate it to make it a positive number for the calculation.
        # Expenses have a debit balance (positive).
        net_income = (-total_revenue) - total_expense
        data['net_income'] = net_income

        # 4. Process Assets, Liabilities, and Equity
        account_types = AccountType.objects.prefetch_related('accounts').order_by('classification', 'name')

        for acc_type in account_types:
            category_data = {'type_name': acc_type.name, 'accounts': [], 'subtotal': Decimal('0.00')}
            
            # Skip Revenue and Expense types as they are handled in Net Income
            if acc_type.classification in [AccountType.ClassificationChoices.REVENUE, AccountType.ClassificationChoices.EXPENSE, AccountType.ClassificationChoices.COGS]:
                continue

            for account in acc_type.accounts.filter(is_active=True):
                # For Balance Sheet accounts, balance is calculated from the beginning of time.
                balance = account.get_balance(as_of_date=as_at_date)

                # Negate Liability and Equity balances for user-friendly display (as positive numbers)
                if acc_type.classification in [AccountType.ClassificationChoices.LIABILITY, AccountType.ClassificationChoices.EQUITY]:
                    balance = -balance
                
                if balance != Decimal('0.00'):
                    category_data['accounts'].append({'name': account.name, 'balance': balance})
                    category_data['subtotal'] += balance

            if category_data['accounts']:
                if acc_type.classification == AccountType.ClassificationChoices.ASSET:
                    data['assets'].append(category_data)
                    data['total_assets'] += category_data['subtotal']
                elif acc_type.classification == AccountType.ClassificationChoices.LIABILITY:
                    data['liabilities'].append(category_data)
                    data['total_liabilities'] += category_data['subtotal']
                elif acc_type.classification == AccountType.ClassificationChoices.EQUITY:
                    data['equity'].append(category_data)
                    data['total_equity'] += category_data['subtotal']
        
        # 5. Add Net Income to Equity
        data['total_equity'] += net_income
        data['total_liabilities_and_equity'] = data['total_liabilities'] + data['total_equity']
        data['balance_check_difference'] = data['total_assets'] - data['total_liabilities_and_equity']

        return data

    def get_context_data(self, **kwargs):
        """Prepares context for the HTML page by calling the main data helper."""
        context = super().get_context_data(**kwargs)
        balance_sheet_data = self._get_balance_sheet_data(self.request)
        context.update(balance_sheet_data)
        
        if context.get('as_at_date'):
            context['report_title'] = _(f"Balance Sheet as at {context['as_at_date'].strftime('%d %B %Y')}")
            
        return context


class BalanceSheetExportCSVView(BalanceSheetReportView):
    """Handles CSV export for the Balance Sheet."""
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        report_data = self._get_balance_sheet_data(request)
        as_at_date = report_data.get('as_at_date')
        if not as_at_date:
            messages.error(request, "Please select a date to generate the CSV export.")
            return redirect('reporting:balance_sheet_report')

        response = HttpResponse(content_type='text/csv')
        filename = f"Balance_Sheet_{as_at_date.strftime('%Y%m%d')}.csv"
        response['Content-Disposition'] = f'attachment; filename="{filename}"'
        writer = csv.writer(response)

        writer.writerow(['Balance Sheet'])
        writer.writerow([f"As at {as_at_date.strftime('%d %B %Y')}"])
        writer.writerow([])
        
        # Assets
        writer.writerow(['ASSETS', ''])
        for cat in report_data.get('assets', []):
            writer.writerow([cat['type_name'], ''])
            for acc in cat['accounts']:
                writer.writerow([f"  {acc['name']}", acc['balance']])
            writer.writerow([f"Total {cat['type_name']}", cat['subtotal']])
        writer.writerow(['TOTAL ASSETS', report_data['total_assets']])
        writer.writerow([])

        # Liabilities
        writer.writerow(['LIABILITIES', ''])
        for cat in report_data.get('liabilities', []):
            writer.writerow([cat['type_name'], ''])
            for acc in cat['accounts']:
                writer.writerow([f"  {acc['name']}", acc['balance']])
            writer.writerow([f"Total {cat['type_name']}", cat['subtotal']])
        writer.writerow(['TOTAL LIABILITIES', report_data['total_liabilities']])
        writer.writerow([])

        # Equity
        writer.writerow(['EQUITY', ''])
        for cat in report_data.get('equity', []):
            writer.writerow([cat['type_name'], ''])
            for acc in cat['accounts']:
                writer.writerow([f"  {acc['name']}", acc['balance']])
            writer.writerow([f"Total {cat['type_name']}", cat['subtotal']])
        writer.writerow(['Net Income', report_data['net_income']])
        writer.writerow(['TOTAL EQUITY', report_data['total_equity']])
        writer.writerow([])

        writer.writerow(['TOTAL LIABILITIES AND EQUITY', report_data['total_liabilities_and_equity']])
        
        return response


class BalanceSheetExportExcelView(BalanceSheetReportView):
    """Handles Excel export for the Balance Sheet."""
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        if not openpyxl:
            messages.error(request, "Excel export library is not available.")
            return redirect('reporting:balance_sheet_report')

        report_data = self._get_balance_sheet_data(request)
        as_at_date = report_data.get('as_at_date')
        if not as_at_date:
            messages.error(request, "Please select a date to generate the Excel export.")
            return redirect('reporting:balance_sheet_report')

        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "Balance Sheet"

        # --- Styles ---
        title_font = Font(bold=True, size=16)
        header_font = Font(bold=True, size=12, underline='single')
        bold_font = Font(bold=True)
        currency_format = '_(* #,##0.00_);_(* (#,##0.00);_(* "-"??_);_(@_)'
        
        # --- Helper for writing sections ---
        def write_excel_section(start_row, title, categories, total_label, total_value):
            ws[f'A{start_row}'] = title
            ws[f'A{start_row}'].font = title_font
            current_row = start_row + 1
            for cat in categories:
                ws[f'A{current_row}'] = cat['type_name']
                ws[f'A{current_row}'].font = header_font
                current_row += 1
                for acc in cat['accounts']:
                    ws[f'A{current_row}'] = acc['name']
                    ws[f'B{current_row}'] = acc['balance']
                    current_row += 1
            
            ws[f'A{current_row}'] = total_label
            ws[f'A{current_row}'].font = bold_font
            ws[f'B{current_row}'] = total_value
            ws[f'B{current_row}'].font = bold_font
            return current_row + 2

        # --- Write Data ---
        current_row = 1
        # Assets
        current_row = write_excel_section(current_row, "ASSETS", report_data.get('assets', []), "TOTAL ASSETS", report_data.get('total_assets'))

        # Liabilities
        current_row = write_excel_section(current_row, "LIABILITIES", report_data.get('liabilities', []), "TOTAL LIABILITIES", report_data.get('total_liabilities'))
        
        # Equity
        ws[f'A{current_row}'] = "EQUITY"
        ws[f'A{current_row}'].font = title_font
        current_row +=1
        for cat in report_data.get('equity', []):
            ws[f'A{current_row}'] = cat['type_name']
            ws[f'A{current_row}'].font = header_font
            current_row +=1
            for acc in cat['accounts']:
                ws[f'A{current_row}'] = acc['name']
                ws[f'B{current_row}'] = acc['balance']
                current_row += 1
        ws[f'A{current_row}'] = "Net Income"
        ws[f'B{current_row}'] = report_data.get('net_income')
        current_row += 1
        ws[f'A{current_row}'] = "TOTAL EQUITY"
        ws[f'A{current_row}'].font = bold_font
        ws[f'B{current_row}'] = report_data.get('total_equity')
        ws[f'B{current_row}'].font = bold_font
        current_row += 2

        ws[f'A{current_row}'] = "TOTAL LIABILITIES AND EQUITY"
        ws[f'A{current_row}'].font = bold_font
        ws[f'B{current_row}'] = report_data.get('total_liabilities_and_equity')
        ws[f'B{current_row}'].font = bold_font

        # --- Formatting ---
        ws.column_dimensions['A'].width = 40
        ws.column_dimensions['B'].width = 18
        for cell in ws['B']:
            cell.number_format = currency_format

        # --- Create Response ---
        response = HttpResponse(content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        filename = f"Balance_Sheet_{as_at_date.strftime('%Y%m%d')}.xlsx"
        response['Content-Disposition'] = f'attachment; filename="{filename}"'
        wb.save(response)
        return response


class BalanceSheetExportPDFView(BalanceSheetReportView):
    """Handles PDF export for the Balance Sheet."""
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        report_data = self._get_balance_sheet_data(request)
        as_at_date = report_data.get('as_at_date')
        if not as_at_date:
            messages.error(request, "Please select a date to generate the PDF report.")
            return redirect('reporting:balance_sheet_report')

        pdf_context = report_data.copy()
        pdf_context.update({
            'report_title': f"Balance Sheet as at {as_at_date.strftime('%d %B %Y')}",
            'school_profile': SchoolProfile.objects.first(),
            'tenant': getattr(request, 'tenant', None),
            'current_datetime': timezone.now()
        })
        
        try:
            pdf = render_to_pdf('reporting/pdf/balance_sheet_pdf.html', pdf_context)
            if pdf:
                response = HttpResponse(pdf, content_type='application/pdf')
                filename = f"Balance_Sheet_{as_at_date.strftime('%Y%m%d')}.pdf"
                response['Content-Disposition'] = f'inline; filename="{filename}"'
                return response
        except Exception as e:
            logger.error(f"Error generating Balance Sheet PDF: {e}", exc_info=True)
            messages.error(request, "An error occurred while generating the PDF report.")
            
        return redirect('reporting:balance_sheet_report')
    
    
# # D:\school_fees_saas_V2\apps\reporting\views.py

# from apps.accounting.models import AccountType, Account, GeneralLedger # Ensure these are imported

# class BalanceSheetView(LoginRequiredMixin, PermissionRequiredMixin, FormMixin, TemplateView):
#     template_name = 'reporting/balance_sheet.html'
#     form_class = BalanceSheetFilterForm
#     permission_required = 'reporting.view_balance_sheet_report' # Or 'accounting.view_account' # Example, adjust as needed
#     # Or a more specific reporting permission like 'reporting.view_balance_sheet'
#     login_url = '/portal/staff/login/' # Or reverse_lazy('schools:staff_login')

#     # ... (dispatch method can remain as is if you're just adding get_report_data) ...
#     # If your dispatch method from the traceback is the one you want to keep, ensure it calls super()
#     def dispatch(self, request, *args, **kwargs):
#         # Your custom dispatch logic, e.g., initializing form from GET
#         if not self.has_permission(): # From PermissionRequiredMixin
#             return self.handle_no_permission()
#         self.form = self.get_form(self.form_class) # Initialize form
#         return super().dispatch(request, *args, **kwargs)


#     def get_initial(self):
#         return {'as_at_date': timezone.now().date()}

#     def get_form_kwargs(self):
#         kwargs = super().get_form_kwargs()
#         if self.request.method == 'GET':
#             kwargs['data'] = self.request.GET or None # Populate form with GET data
#         return kwargs

#     def get_report_data(self, form):
#         as_at_date = form.cleaned_data.get('as_at_date') if form.is_valid() else timezone.now().date()

#         # Get account types by classification instead of relying on settings
#         try:
#             asset_types = AccountType.objects.filter(classification=AccountType.ClassificationChoices.ASSET)
#             liability_types = AccountType.objects.filter(classification=AccountType.ClassificationChoices.LIABILITY)
#             equity_types = AccountType.objects.filter(classification=AccountType.ClassificationChoices.EQUITY)
#             revenue_types = AccountType.objects.filter(classification=AccountType.ClassificationChoices.REVENUE)
#             expense_types = AccountType.objects.filter(classification=AccountType.ClassificationChoices.EXPENSE)
#             cogs_types = AccountType.objects.filter(classification=AccountType.ClassificationChoices.COGS)
#         except Exception as e:
#             messages.error(self.request, f"An error occurred while fetching account types: {e}")
#             return {'error': str(e)}

#         report_data = {
#             'assets': {'accounts': [], 'total_assets': Decimal('0.00')},
#             'liabilities': {'accounts': [], 'total_liabilities': Decimal('0.00')},
#             'equity': {'accounts': [], 'total_equity': Decimal('0.00')},
#             'total_liabilities_and_equity': Decimal('0.00'),
#             'net_income_for_period': Decimal('0.00'),
#             'as_at_date': as_at_date,
#             'error': None,
#         }

#         # Calculate Net Income for the current financial year (simplified)
#         # Get financial year start (assuming January 1st for simplicity)
#         current_year = as_at_date.year
#         financial_year_start = timezone.datetime(current_year, 1, 1).date()

#         # Calculate total revenue
#         total_revenue = Decimal('0.00')
#         for revenue_type in revenue_types:
#             for account in Account.objects.filter(account_type=revenue_type, is_active=True):
#                 balance = account.get_balance(start_date=financial_year_start, as_of_date=as_at_date)
#                 total_revenue += balance

#         # Calculate total expenses and COGS
#         total_expenses = Decimal('0.00')
#         for expense_type in expense_types:
#             for account in Account.objects.filter(account_type=expense_type, is_active=True):
#                 balance = account.get_balance(start_date=financial_year_start, as_of_date=as_at_date)
#                 total_expenses += balance

#         for cogs_type in cogs_types:
#             for account in Account.objects.filter(account_type=cogs_type, is_active=True):
#                 balance = account.get_balance(start_date=financial_year_start, as_of_date=as_at_date)
#                 total_expenses += balance

#         # Net Income = Revenue - Expenses (Revenue is credit balance, Expenses are debit balances)
#         net_income_for_period = total_revenue - total_expenses
#         report_data['net_income_for_period'] = net_income_for_period
        
#         # Populate Assets
#         for asset_type in asset_types:
#             for account in Account.objects.filter(account_type=asset_type, is_active=True):
#                 balance = account.get_balance(as_of_date=as_at_date)
#                 if balance != Decimal('0.00'):  # Only include accounts with non-zero balances
#                     report_data['assets']['accounts'].append({
#                         'code': account.code or 'N/A',
#                         'name': account.name,
#                         'balance': balance,
#                         'type': asset_type.name
#                     })
#                     report_data['assets']['total_assets'] += balance

#         # Populate Liabilities
#         for liability_type in liability_types:
#             for account in Account.objects.filter(account_type=liability_type, is_active=True):
#                 balance = account.get_balance(as_of_date=as_at_date)
#                 if balance != Decimal('0.00'):  # Only include accounts with non-zero balances
#                     report_data['liabilities']['accounts'].append({
#                         'code': account.code or 'N/A',
#                         'name': account.name,
#                         'balance': balance,
#                         'type': liability_type.name
#                     })
#                     report_data['liabilities']['total_liabilities'] += balance

#         # Populate Equity
#         for equity_type in equity_types:
#             for account in Account.objects.filter(account_type=equity_type, is_active=True):
#                 balance = account.get_balance(as_of_date=as_at_date)
#                 if balance != Decimal('0.00'):  # Only include accounts with non-zero balances
#                     report_data['equity']['accounts'].append({
#                         'code': account.code or 'N/A',
#                         'name': account.name,
#                         'balance': balance,
#                         'type': equity_type.name
#                     })
#                     report_data['equity']['total_equity'] += balance

#         # Add current period's net income to equity
#         if net_income_for_period != Decimal('0.00'):
#             report_data['equity']['accounts'].append({
#                 'code': 'NET_INC',
#                 'name': 'Net Income (Current Period)',
#                 'balance': net_income_for_period,
#                 'type': 'Calculated'
#             })
#             report_data['equity']['total_equity'] += net_income_for_period
        
#         report_data['total_liabilities_and_equity'] = report_data['liabilities']['total_liabilities'] + report_data['equity']['total_equity']

#         # Verification: Assets = Liabilities + Equity
#         report_data['balance_check'] = report_data['assets']['total_assets'] - report_data['total_liabilities_and_equity']
        
#         return report_data

#     def get_context_data(self, **kwargs):
#         context = super().get_context_data(**kwargs) # This calls get_form() from FormMixin
#         form = context['form'] # Form is now initialized by FormMixin or our dispatch
        
#         report_data_dict = {}
#         if form.is_valid():
#             report_data_dict = self.get_report_data(form)
#         elif self.request.method == 'GET' and not self.request.GET: # Initial load
#             # Create a default form for initial display
#             initial_form_data = self.get_initial()
#             default_form = self.form_class(initial=initial_form_data)
#             if default_form.is_valid(): # Should be valid with initial data
#                 report_data_dict = self.get_report_data(default_form)
#             else: # Should not happen with just initial data
#                 report_data_dict['error'] = "Initial form data is invalid."
#                 for field, errors in default_form.errors.items():
#                     for error in errors:
#                         messages.error(self.request, f"Form error in {field}: {error}")

#         context.update(report_data_dict)
#         context['view_title'] = "Balance Sheet"
#         context['form'] = form # Ensure form is in context for template rendering
#         return context

# # Utility function (place in common.utils or reporting.utils)
# def get_financial_year_start(date_obj):
#     # This is a placeholder. Your actual financial year logic might be tenant-configurable.
#     # Assuming financial year starts April 1st for this example.
#     if date_obj.month < 4: # Jan, Feb, Mar belong to previous financial year
#         return timezone.datetime(date_obj.year - 1, 4, 1).date()
#     else: # Apr - Dec belong to current financial year
#         return timezone.datetime(date_obj.year, 4, 1).date()



# = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = 
#
#      CASHFLOW STATEMENT REPORT
#
# = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =


# # Python Standard Library Imports
# import csv
# from decimal import Decimal
# import logging

# # Django Imports
# from django.db.models import Q, Sum
# from django.http import HttpResponse
# from django.shortcuts import redirect
# from django.template.loader import render_to_string
# from django.utils import timezone
# from django.utils.translation import gettext_lazy as _
# from django.views.generic import TemplateView
# from django.contrib import messages

# # Third-Party Imports
# try:
#     import openpyxl
#     from openpyxl.styles import Font, Alignment
#     from openpyxl.utils import get_column_letter
# except ImportError:
#     openpyxl = None

# # Local Application Imports
# # Note: Replace with your actual model and form imports
# from .mixins import TenantPermissionRequiredMixin, BaseReportViewMixin
# from .forms import DateRangeForm # Assuming a generic date range form
# from .models import Account, AccountType, JournalEntryItem # Your accounting models
# from .utils import render_to_pdf # Your PDF utility
# from schools.models import SchoolProfile

logger = logging.getLogger(__name__)


class CashFlowStatementReportView(TenantPermissionRequiredMixin, BaseReportViewMixin, TemplateView):
    """
    Handles the display of the HTML Cash Flow Statement report page.
    """
    template_name = 'reporting/cash_flow_statement_report.html'

    # Attributes for BaseReportViewMixin
    permission_required = 'reporting.view_cash_flow_statement'
    report_title = _("Cash Flow Statement")
    filter_form_class = DateRangeForm
    export_csv_url_name = 'reporting:cash_flow_export_csv'
    export_pdf_url_name = 'reporting:cash_flow_export_pdf'
    export_excel_url_name = 'reporting:cash_flow_export_excel'

    def _get_cash_flow_data(self, request):
        """
        The core logic for fetching and processing all data for the Cash Flow Statement.
        This helper method is used by the HTML view and all export views.
        Returns a dictionary containing all calculated data.
        """
        # 1. Initialize the data structure
        data = {
            'start_date': None, 'end_date': None,
            'beginning_cash_balance': Decimal('0.00'),
            'ending_cash_balance': Decimal('0.00'),
            'net_change_in_cash': Decimal('0.00'),
            'operating_activities': [], 'net_cash_from_operating': Decimal('0.00'),
            'investing_activities': [], 'net_cash_from_investing': Decimal('0.00'),
            'financing_activities': [], 'net_cash_from_financing': Decimal('0.00'),
            'filter_form': self.filter_form_class(request.GET or None)
        }

        # 2. Validate filter form
        if not data['filter_form'].is_valid():
            return data

        start_date = data['filter_form'].cleaned_data.get('start_date')
        end_date = data['filter_form'].cleaned_data.get('end_date')

        if not start_date or not end_date:
            messages.error(request, _("A valid date range (start and end date) is required."))
            return data
        
        data.update({'start_date': start_date, 'end_date': end_date})

        # 3. Identify all 'Cash and Cash Equivalent' accounts
        try:
            cash_accounts = Account.objects.filter(is_cash_equivalent=True, is_active=True)
            if not cash_accounts.exists():
                messages.warning(request, _("No accounts marked as 'Cash Equivalent' were found. Please configure your Chart of Accounts."))
                return data
        except Exception as e:
            logger.error(f"Error fetching cash equivalent accounts: {e}")
            messages.error(request, _("An error occurred while loading account configurations."))
            return data
            
        # 4. Calculate Beginning and Ending Cash Balances
        beginning_balance = Decimal('0.00')
        ending_balance = Decimal('0.00')
        for acc in cash_accounts:
            beginning_balance += acc.get_balance(as_of_date=start_date - timezone.timedelta(days=1))
            ending_balance += acc.get_balance(as_of_date=end_date)
            
        data['beginning_cash_balance'] = beginning_balance
        data['ending_cash_balance'] = ending_balance

        # 5. Analyze cash movements during the period by their classification
        # We find the *other side* of transactions involving cash accounts
        transactions = JournalEntryItem.objects.filter(
            journal_entry__date__range=(start_date, end_date)
        ).exclude(
            account__in=cash_accounts # Exclude the cash side to get the activity side
        ).select_related('account')

        # Aggregate the movements by their cash flow classification
        # Note: We reverse the sign. A debit to an expense account (outflow) is a credit to cash.
        # A credit to a revenue account (inflow) is a debit to cash.
        # The sum of debits-credits on the non-cash side equals the net movement of cash.
        activity_summary = transactions.values(
            'account__name',
            'account__cash_flow_classification' # Assumes this field exists on Account model
        ).annotate(
            net_movement=Sum('debit') - Sum('credit')
        ).order_by('account__cash_flow_classification', 'account__name')

        # 6. Categorize into Operating, Investing, Financing
        for activity in activity_summary:
            classification = activity.get('account__cash_flow_classification')
            # The net movement amount is reversed for cash flow purposes.
            # e.g., A debit to an expense of 100 (net_movement > 0) is a cash OUTFLOW of 100.
            # e.g., A credit to revenue of 500 (net_movement < 0) is a cash INFLOW of 500.
            amount = -activity.get('net_movement', Decimal('0.00'))

            if amount == Decimal('0.00'):
                continue # Skip zero-impact activities

            item = {'description': activity['account__name'], 'amount': amount}

            if classification == Account.CashFlowClassification.OPERATING:
                data['operating_activities'].append(item)
                data['net_cash_from_operating'] += amount
            elif classification == Account.CashFlowClassification.INVESTING:
                data['investing_activities'].append(item)
                data['net_cash_from_investing'] += amount
            elif classification == Account.CashFlowClassification.FINANCING:
                data['financing_activities'].append(item)
                data['net_cash_from_financing'] += amount

        # 7. Final calculation for net change in cash
        total_net_flow = (
            data['net_cash_from_operating'] + 
            data['net_cash_from_investing'] + 
            data['net_cash_from_financing']
        )
        data['net_change_in_cash'] = total_net_flow

        # Sanity Check
        if not (data['beginning_cash_balance'] + total_net_flow) == data['ending_cash_balance']:
            logger.warning("Cash flow statement reconciliation failed. Beginning + Net Change != Ending.")
            messages.warning(request, _("Warning: The cash flow statement could not be fully reconciled. The figures may be inexact."))

        return data

    def get_context_data(self, **kwargs):
        """Prepares context by calling the main data helper."""
        context = super().get_context_data(**kwargs)
        cash_flow_data = self._get_cash_flow_data(self.request)
        context.update(cash_flow_data)
        
        # Dynamically set the report title for the template
        if context.get('start_date') and context.get('end_date'):
            start, end = context['start_date'], context['end_date']
            context['report_title'] = _(f"Cash Flow Statement from {start.strftime('%d %b %Y')} to {end.strftime('%d %b %Y')}")
        
        return context


class CashFlowStatementExportCSVView(CashFlowStatementReportView):
    """Handles CSV export for the Cash Flow Statement report."""
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        report_data = self._get_cash_flow_data(request)
        if not report_data.get('start_date'):
            messages.error(request, "Please select a valid period to generate the CSV export.")
            return redirect('reporting:cash_flow_statement_report')

        response = HttpResponse(content_type='text/csv')
        filename = f"Cash_Flow_Statement_{report_data['start_date'].strftime('%Y%m%d')}.csv"
        response['Content-Disposition'] = f'attachment; filename="{filename}"'
        writer = csv.writer(response)

        # Helper function to write rows
        def write_section(title, items, net_total):
            writer.writerow([title])
            for item in items:
                writer.writerow([f"  {item['description']}", item['amount']])
            writer.writerow([f"Net Cash from {title}", net_total])
            writer.writerow([])

        # Write data
        writer.writerow(['Cash Flow Statement'])
        writer.writerow([f"For the period {report_data['start_date']} to {report_data['end_date']}"])
        writer.writerow([])
        writer.writerow(['Beginning Cash Balance', report_data['beginning_cash_balance']])
        writer.writerow([])
        
        write_section('Operating Activities', report_data['operating_activities'], report_data['net_cash_from_operating'])
        write_section('Investing Activities', report_data['investing_activities'], report_data['net_cash_from_investing'])
        write_section('Financing Activities', report_data['financing_activities'], report_data['net_cash_from_financing'])

        writer.writerow(['Net Change in Cash', report_data['net_change_in_cash']])
        writer.writerow(['Ending Cash Balance', report_data['ending_cash_balance']])
        return response


class CashFlowStatementExportExcelView(CashFlowStatementReportView):
    """Handles Excel export for the Cash Flow Statement report."""
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        if not openpyxl:
            messages.error(request, "Excel export library is not available.")
            return redirect('reporting:cash_flow_statement_report')

        report_data = self._get_cash_flow_data(request)
        if not report_data.get('start_date'):
            messages.error(request, "Please select a period to generate the Excel export.")
            return redirect('reporting:cash_flow_statement_report')

        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "Cash Flow Statement"
        
        # --- Styles ---
        title_font = Font(bold=True, size=16)
        header_font = Font(bold=True, size=12)
        bold_font = Font(bold=True)
        currency_format = '_(* #,##0.00_);_(* (#,##0.00);_(* "-"??_);_(@_)'

        # --- Header ---
        title = f"Cash Flow Statement"
        period = f"For the Period {report_data['start_date'].strftime('%d-%b-%Y')} to {report_data['end_date'].strftime('%d-%b-%Y')}"
        ws['A1'] = title
        ws['A1'].font = title_font
        ws['A2'] = period
        
        # --- Main Body ---
        row = 4
        ws[f'A{row}'] = "Beginning Cash Balance"
        ws[f'B{row}'] = report_data['beginning_cash_balance']
        ws[f'B{row}'].font = bold_font
        row += 2

        def write_excel_section(start_row, title, items, net_total):
            ws[f'A{start_row}'] = title
            ws[f'A{start_row}'].font = header_font
            current_row = start_row + 1
            for item in items:
                ws[f'A{current_row}'] = item['description']
                ws[f'B{current_row}'] = item['amount']
                ws[f'A{current_row}'].alignment = Alignment(indent=1)
                current_row += 1
            ws[f'A{current_row}'] = f"Net Cash from {title}"
            ws[f'B{current_row}'] = net_total
            ws[f'B{current_row}'].font = bold_font
            return current_row + 2
        
        row = write_excel_section(row, "Cash Flow from Operating Activities", report_data['operating_activities'], report_data['net_cash_from_operating'])
        row = write_excel_section(row, "Cash Flow from Investing Activities", report_data['investing_activities'], report_data['net_cash_from_investing'])
        row = write_excel_section(row, "Cash Flow from Financing Activities", report_data['financing_activities'], report_data['net_cash_from_financing'])

        # --- Footer ---
        ws[f'A{row}'] = "Net Change in Cash"
        ws[f'B{row}'] = report_data['net_change_in_cash']
        ws[f'B{row}'].font = bold_font
        row += 1
        ws[f'A{row}'] = "Ending Cash Balance"
        ws[f'B{row}'] = report_data['ending_cash_balance']
        ws[f'B{row}'].font = bold_font

        # --- Formatting ---
        ws.column_dimensions['A'].width = 45
        ws.column_dimensions['B'].width = 20
        for cell in ws['B']:
            cell.number_format = currency_format

        # --- Create Response ---
        response = HttpResponse(content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        filename = f"Cash_Flow_Statement_{report_data['start_date'].strftime('%Y%m%d')}.xlsx"
        response['Content-Disposition'] = f'attachment; filename="{filename}"'
        wb.save(response)
        return response


class CashFlowStatementExportPDFView(CashFlowStatementReportView):
    """Handles PDF export for the Cash Flow Statement report."""
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        report_data = self._get_cash_flow_data(request)
        if not report_data.get('start_date'):
            messages.error(request, "Please select a period to generate the PDF report.")
            return redirect('reporting:cash_flow_statement_report')

        start, end = report_data['start_date'], report_data['end_date']
        
        pdf_context = report_data.copy()
        pdf_context.update({
            'report_title': f"Cash Flow Statement ({start.strftime('%d-%b-%Y')} to {end.strftime('%d-%b-%Y')})",
            'school_profile': SchoolProfile.objects.first(),
            'tenant': getattr(request, 'tenant', None),
            'current_datetime': timezone.now()
        })
        
        try:
            pdf = render_to_pdf('reporting/pdf/cash_flow_statement_pdf.html', pdf_context)
            if pdf:
                response = HttpResponse(pdf, content_type='application/pdf')
                filename = f"Cash_Flow_{start.strftime('%Y%m%d')}_{end.strftime('%Y%m%d')}.pdf"
                response['Content-Disposition'] = f'inline; filename="{filename}"'
                return response
        except Exception as e:
            logger.error(f"Error generating Cash Flow PDF: {e}", exc_info=True)
            messages.error(request, "Failed to generate the PDF report due to an unexpected error.")
            
        return redirect('reporting:cash_flow_statement_report')
    


# # apps/reporting/views.py
# import logging
# from decimal import Decimal
# from django.utils import timezone
# from django.views.generic import TemplateView
# from django.urls import reverse_lazy
# from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin
# from django.contrib import messages
# from django.db.models import Sum, Q
# from django.db.models.functions import Coalesce
# from django.conf import settings # For settings.ACCOUNTING_..._CODE

# # Models (ensure all are correctly imported)
# from apps.accounting.models import AccountType, Account as ChartOfAccount, JournalEntryItem # Using JournalEntryItem
# # from apps.schools.models import AcademicYear # Not directly used in this report's filter data

# # Forms (your filter form for this report)
# from .forms import IncomeExpenseReportForm # Reusing this form for date range

# logger = logging.getLogger(__name__)


# # D:\school_fees_saas_v2\apps\reporting\views.py

# from django.conf import settings # For ACCOUNTING_CASH_TYPE_CODE etc.
# from django.views.generic import TemplateView
# from django.urls import reverse_lazy
# from django.utils import timezone
# from django.db.models import Sum, F, Q, DecimalField, Value # Value might not be used here
# from django.db.models.functions import Coalesce
# from decimal import Decimal
# from django.contrib import messages
# from django.utils.translation import gettext_lazy as _
# from django.http import HttpResponse, HttpResponseRedirect # Added HttpResponseRedirect
# from django.shortcuts import redirect

# # Your model imports
# from apps.accounting.models import Account, JournalEntry, JournalEntryItem, AccountType 
# from apps.schools.models import SchoolProfile

# # Your form import
# from .forms import IncomeExpenseReportForm # Using this for date range

# # Your mixin imports
# from apps.common.mixins import TenantPermissionRequiredMixin, BaseReportViewMixin # Ensure both are imported

# # Your PDF utility
# from apps.common.utils import render_to_pdf 

# # For Excel/CSV
# import csv
# import openpyxl
# from openpyxl.utils import get_column_letter
# from openpyxl.styles import Font, Alignment
# from io import BytesIO

# import logging
# logger = logging.getLogger(__name__)


# class CashFlowStatementView(TenantPermissionRequiredMixin, BaseReportViewMixin, TemplateView):
#     # login_url = reverse_lazy('schools:staff_login') # Handled by TenantPermissionRequiredMixin if it inherits from LoginRequiredMixin
#     permission_required = 'reporting.view_cash_flow_statement' # Make sure this perm exists or create it
#     template_name = 'reporting/cash_flow_statement_report.html'

#     report_code = 'cash_flow_statement_report'
#     filter_form_class = IncomeExpenseReportForm # This will be used by BaseReportViewMixin

#     report_code = 'RPT_CASH_FLOW'
#     filter_form_class = DateRangeForm # Example
#     report_title_text = _("Cash Flow Statement Analysis")

#     # get_report_title is a hook called by BaseReportViewMixin
#     @property
#     def report_title(self): # Changed to @property for dynamic title based on processed form
#         form = getattr(self, 'processed_filter_form', None) # Access form set by BaseReportViewMixin
#         start_date_val, end_date_val = None, None
#         if form:
#             if form.is_bound and form.is_valid():
#                 start_date_val = form.cleaned_data.get('start_date')
#                 end_date_val = form.cleaned_data.get('end_date')
#             elif not form.is_bound and form.initial: # Unbound form with initial data
#                 start_date_val = form.initial.get('start_date')
#                 end_date_val = form.initial.get('end_date')

#         if start_date_val and end_date_val:
#             return _(f"Cash Flow Statement from {start_date_val.strftime('%d %b %Y')} to {end_date_val.strftime('%d %b %Y')}")
#         return _("Cash Flow Statement")
    
#     # get_filter_form_initial_data is a hook called by BaseReportViewMixin
#     def get_filter_form_initial_data(self):
#         today = timezone.localdate()
#         first_day_current_month = today.replace(day=1)
#         last_day_previous_month = first_day_current_month - timezone.timedelta(days=1)
#         first_day_previous_month = last_day_previous_month.replace(day=1)
#         return {'start_date': first_day_previous_month, 'end_date': last_day_previous_month}

#     # get_report_data is the main hook called by BaseReportViewMixin
#     def get_report_data(self, filter_form=None): # Signature corrected
#         """
#         Fetches and calculates data for the Cash Flow Statement.
#         Uses self.form (set by BaseReportViewMixin.get()) for filter criteria.
#         """
#         start_date_filter, end_date_filter = None, None
        
#         report_data = {
#             'start_date_filter': None, 'end_date_filter': None, # Store actual dates used
#             'beginning_cash_balance': Decimal('0.00'),
#             'ending_cash_balance': Decimal('0.00'),
#             'net_change_in_cash': Decimal('0.00'),
#             'operating_activities_items': [], 'net_cash_from_operating': Decimal('0.00'),
#             'investing_activities_items': [], 'net_cash_from_investing': Decimal('0.00'),
#             'financing_activities_items': [], 'net_cash_from_financing': Decimal('0.00'),
#             'report_generated_successfully': False,
#             'filter_form_errors': None
#         }

#         current_form = self.form # Access the form instance set by BaseReportViewMixin's get() method

#         logger.debug(
#             f"{self.__class__.__name__}.get_report_data called. "
#             f"self.form type: {type(current_form)}, "
#             f"bound: {current_form.is_bound if current_form else 'N/A'}, "
#             f"valid: {current_form.is_valid() if current_form and current_form.is_bound else 'N/A'}"
#         )

#         if current_form:
#             if current_form.is_valid(): # Form was bound with GET data and is valid
#                 start_date_filter = current_form.cleaned_data.get('start_date')
#                 end_date_filter = current_form.cleaned_data.get('end_date')
#                 logger.debug(f"{self.__class__.__name__}: Using valid filter dates: Start={start_date_filter}, End={end_date_filter}")
#             elif not current_form.is_bound and current_form.initial: # Form is unbound, use its initial data
#                 start_date_filter = current_form.initial.get('start_date')
#                 end_date_filter = current_form.initial.get('end_date')
#                 logger.debug(f"{self.__class__.__name__}: Using initial filter dates: Start={start_date_filter}, End={end_date_filter}")
#             else: # Form is bound but invalid
#                 report_data['filter_form_errors'] = current_form.errors
#                 logger.warning(f"{self.__class__.__name__}: Filter form invalid. Not generating report data. Errors: {current_form.errors.as_json() if current_form else 'N/A'}")
#                 messages.error(self.request, _("Please correct the filter errors to generate the report."))
#                 return report_data # Return with report_generated_successfully = False
#         else: # No filter form was defined for this view
#             logger.warning(f"{self.__class__.__name__}: No filter form provided. Cannot generate cash flow statement without a date range.")
#             messages.error(self.request, _("Date range filters are required for the Cash Flow Statement."))
#             return report_data

#         if not (start_date_filter and end_date_filter):
#             logger.info(f"{self.__class__.__name__}: Start or end date missing after form processing. Not generating report data.")
#             messages.error(self.request, _("A valid date range (both start and end date) is required for this report."))
#             return report_data

#         report_data.update({
#             'start_date_filter': start_date_filter, 
#             'end_date_filter': end_date_filter
#         })
#         logger.info(f"Attempting to generate Cash Flow Statement from {start_date_filter} to {end_date_filter}")

#         try:
#             # Get cash and bank account types by classification
#             asset_types = AccountType.objects.filter(classification=AccountType.ClassificationChoices.ASSET)

#             # Filter for cash and bank accounts (typically have 'cash' or 'bank' in name)
#             cash_and_bank_accounts = Account.objects.filter(
#                 account_type__in=asset_types,
#                 is_active=True
#             ).filter(
#                 Q(name__icontains='cash') | Q(name__icontains='bank') | Q(code__startswith='1000')
#             )

#             if not cash_and_bank_accounts.exists():
#                 messages.warning(self.request, _("No active Cash or Bank accounts found in your Chart of Accounts. Cannot generate Cash Flow Statement."))
#                 return report_data

#         except Exception as e:
#             logger.error(f"Cash Flow: Error fetching cash/bank accounts: {e}", exc_info=True)
#             messages.error(self.request, _("An error occurred while loading accounting configurations for cash flow."))
#             return report_data
    

#         # Calculate beginning and ending cash balances
#         beginning_cash_balance = Decimal('0.00')
#         ending_cash_balance = Decimal('0.00')

#         for account in cash_and_bank_accounts:
#             # Beginning balance (as of start date)
#             beginning_balance = account.get_balance(as_of_date=start_date_filter - timezone.timedelta(days=1))
#             beginning_cash_balance += beginning_balance

#             # Ending balance (as of end date)
#             ending_balance = account.get_balance(as_of_date=end_date_filter)
#             ending_cash_balance += ending_balance

#         net_change_in_cash = ending_cash_balance - beginning_cash_balance

#         # Update report data
#         report_data['beginning_cash_balance'] = beginning_cash_balance
#         report_data['ending_cash_balance'] = ending_cash_balance
#         report_data['net_change_in_cash'] = net_change_in_cash
        
#         # Calculate cash flow activities (simplified implementation)
#         operating_activities = []
#         investing_activities = []
#         financing_activities = []

#         # For now, we'll create a simplified cash flow statement
#         # In a full implementation, you would analyze journal entries to categorize cash flows

#         # Operating Activities (simplified - could be expanded to analyze revenue/expense accounts)
#         if net_change_in_cash != Decimal('0.00'):
#             operating_activities.append({
#                 'description': 'Net cash flow from operations (simplified)',
#                 'amount': net_change_in_cash
#             })

#         # Calculate totals
#         net_cash_from_operating = sum(item['amount'] for item in operating_activities)
#         net_cash_from_investing = sum(item['amount'] for item in investing_activities)
#         net_cash_from_financing = sum(item['amount'] for item in financing_activities)

#         # Update report data
#         report_data.update({
#             'operating_activities_items': operating_activities,
#             'net_cash_from_operating': net_cash_from_operating,
#             'investing_activities_items': investing_activities,
#             'net_cash_from_investing': net_cash_from_investing,
#             'financing_activities_items': financing_activities,
#             'net_cash_from_financing': net_cash_from_financing,
#             'report_generated_successfully': True
#         })

#         logger.info(f"Cash Flow Statement generated for period {start_date_filter} to {end_date_filter}")
#         return report_data

#     # The get_context_data method is now inherited from BaseReportViewMixin.
#     # BaseReportViewMixin will call self.get_report_title(), self.get_filter_form_initial_data(),
#     # initialize self.filter_form_class, and then call self.get_report_data(processed_form)
#     # and put all returned keys from get_report_data into the context.

#     # --- Your Export Methods (export_to_csv, export_to_excel, export_to_pdf) ---
#     # Ensure these are correctly defined as methods of this class
#     # and that they use `self.report_title` (the property) instead of `self.get_report_title()` if called directly.
#     # BaseReportViewMixin's `get` method calls them with `report_data_dict`.
    
#     # Example for one, ensure others are similar:
#     def get_pdf_template_name(self): # Called by BaseReportViewMixin's export_to_pdf
#         return 'reporting/pdf/cash_flow_statement_pdf.html'
    

#     def export_to_excel(self, report_data_dict, request):
#         # ... (Implement Excel for Cash Flow) ...
#         messages.info(request, "Excel export for Cash Flow Statement is under development.")
#         return redirect(request.path)

#     def export_to_pdf(self, report_data_dict, request, html_context):
#         pdf_context = {
#             'report_data': report_data_dict, 'school_profile': self.get_school_profile(),
#             'report_title': self.get_report_title(), 'current_datetime': timezone.now(),
#         }
#         pdf = render_to_pdf(self.get_pdf_template_name(), pdf_context)
#         if pdf:
#             response = HttpResponse(pdf, content_type='application/pdf')
#             filename = f"{self.report_code}_{report_data_dict['start_date'].strftime('%Y%m%d')}_{report_data_dict['end_date'].strftime('%Y%m%d')}.pdf"
#             response['Content-Disposition'] = f'inline; filename="{filename}"'
#             return response
#         else:
#             logger.error(f"PDF generation failed for {self.report_code} (Cash Flow)")
#             messages.error(request, "Failed to generate PDF report.")
#             return redirect(request.path)




# = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = 
#
#      BUDGET VARIANCE REPORT
#
# = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =

# # Python Standard Library Imports
# import csv
# from decimal import Decimal
# import logging

# # Django Imports
# from django.http import HttpResponse
# from django.shortcuts import redirect
# from django.template.loader import render_to_string # Assuming a render_to_pdf helper
# from django.utils import timezone
# from django.utils.translation import gettext_lazy as _
# from django.views.generic import TemplateView
# from django.contrib import messages

# # Third-Party Imports
# # Assuming openpyxl is installed for Excel export
# try:
#     import openpyxl
#     from openpyxl.styles import Font, Alignment, Border, Side
#     from openpyxl.utils import get_column_letter
# except ImportError:
#     openpyxl = None

# # Local Application Imports
# # Note: Replace with your actual model and form imports
# from .mixins import TenantPermissionRequiredMixin, BaseReportViewMixin # Your custom mixins
# from .forms import BudgetReportFilterForm # Your filter form
# from .models import AcademicYear, Term, BudgetAmount, BudgetItem # Your models
# from .utils import render_to_pdf # Your PDF rendering utility
# from schools.models import SchoolProfile # Example for school profile

# logger = logging.getLogger(__name__)


class BudgetVarianceReportView(TenantPermissionRequiredMixin, BaseReportViewMixin, TemplateView):
    """
    Handles the display of the HTML Budget Variance report page.
    """
    template_name = 'reporting/budget_variance_report.html'
    
    # Attributes for BaseReportViewMixin
    permission_required = 'reporting.view_budget_variance_report'
    report_title = _("Budget vs Actuals (Variance Report)")
    filter_form_class = BudgetReportFilterForm
    export_csv_url_name = 'reporting:budget_variance_export_csv'
    export_pdf_url_name = 'reporting:budget_variance_export_pdf'
    export_excel_url_name = 'reporting:budget_variance_export_excel' # Added Excel export URL

    def _get_budget_variance_data(self, request):
        """
        The core logic for fetching and processing all data for the budget variance report.
        This helper method is used by the HTML view and all export views.
        Returns a dictionary containing all calculated data.
        """
        # 1. Initialize the data structure to be returned
        data = {
            'academic_year': None, 'term': None, 'budget_items_variance': [],
            'total_budgeted_income': Decimal('0.00'), 'total_actual_income': Decimal('0.00'),
            'total_variance_income': Decimal('0.00'), 'total_budgeted_expense': Decimal('0.00'),
            'total_actual_expense': Decimal('0.00'), 'total_variance_expense': Decimal('0.00'),
            'net_budgeted_profit_loss': Decimal('0.00'), 'net_actual_profit_loss': Decimal('0.00'),
            'net_variance_profit_loss': Decimal('0.00'), 'start_date': None, 'end_date': None,
            'filter_form': self.filter_form_class(request.GET or None)
        }

        # 2. Validate filter form
        if not data['filter_form'].is_valid():
            # If the form is not valid, return the default empty data.
            # The view can then display the form with its errors.
            return data

        academic_year = data['filter_form'].cleaned_data.get('academic_year')
        term = data['filter_form'].cleaned_data.get('term')

        if not academic_year:
            # Although the form might be 'valid' without a selection, the report requires it.
            messages.error(request, _("An Academic Year must be selected to generate the report."))
            return data # Return default data

        # 3. Determine date range for "Actuals"
        start_date, end_date = None, None
        if term:
            start_date, end_date = term.start_date, term.end_date
        else:
            start_date, end_date = academic_year.start_date, academic_year.end_date

        if not all([start_date, end_date]):
            messages.error(request, _("Could not determine a valid date range from the selected period."))
            return data

        data.update({
            'academic_year': academic_year,
            'term': term,
            'start_date': start_date,
            'end_date': end_date,
        })
        
        # 4. Fetch and Process Budget Data
        report_lines = []
        budget_amounts_qs = BudgetAmount.objects.filter(academic_year=academic_year)
        if term:
            budget_amounts_qs = budget_amounts_qs.filter(term=term)
        
        budget_amounts = budget_amounts_qs.select_related(
            'budget_item', 'budget_item__linked_coa_account'
        ).order_by('budget_item__budget_item_type', 'budget_item__name')

        if not budget_amounts.exists():
            messages.info(request, _("No budget entries found for the selected period."))
            return data

        for ba in budget_amounts:
            budget_item = ba.budget_item
            budgeted_amount = ba.amount
            actual_amount = Decimal('0.00')

            # Calculate actual amount from linked Chart of Accounts
            if budget_item.linked_coa_account:
                actual_amount = budget_item.linked_coa_account.get_balance(
                    start_date=start_date, as_of_date=end_date
                )
            
            variance = budgeted_amount - actual_amount
            
            # Determine if variance is favorable
            is_favorable = False
            if budget_item.budget_item_type == BudgetItem.ItemTypeChoices.EXPENSE:
                if actual_amount <= budgeted_amount: is_favorable = True 
            elif budget_item.budget_item_type == BudgetItem.ItemTypeChoices.REVENUE:
                if actual_amount >= budgeted_amount: is_favorable = True

            report_lines.append({
                'item_name': budget_item.name,
                'account_code': budget_item.linked_coa_account.account_code if budget_item.linked_coa_account else 'N/A',
                'budget_item_type': budget_item.get_budget_item_type_display(),
                'budgeted': budgeted_amount,
                'actual': actual_amount,
                'variance': variance,
                'is_favorable': is_favorable,
            })

            # Accumulate totals
            if budget_item.budget_item_type == BudgetItem.ItemTypeChoices.REVENUE:
                data['total_budgeted_income'] += budgeted_amount
                data['total_actual_income'] += actual_amount
            elif budget_item.budget_item_type == BudgetItem.ItemTypeChoices.EXPENSE:
                data['total_budgeted_expense'] += budgeted_amount
                data['total_actual_expense'] += actual_amount
        
        # 5. Calculate Final Totals and Update Data Dictionary
        data['budget_items_variance'] = report_lines
        data['total_variance_income'] = data['total_actual_income'] - data['total_budgeted_income'] # Note: Favorable is positive
        data['total_variance_expense'] = data['total_budgeted_expense'] - data['total_actual_expense'] # Note: Favorable is positive
        
        data['net_budgeted_profit_loss'] = data['total_budgeted_income'] - data['total_budgeted_expense']
        data['net_actual_profit_loss'] = data['total_actual_income'] - data['total_actual_expense']
        data['net_variance_profit_loss'] = data['net_actual_profit_loss'] - data['net_budgeted_profit_loss']

        return data

    def get_context_data(self, **kwargs):
        """
        Prepares context for the HTML page by calling the main data helper.
        """
        context = super().get_context_data(**kwargs)
        budget_data = self._get_budget_variance_data(self.request)
        context.update(budget_data)
        return context


class BudgetVarianceExportCSVView(BudgetVarianceReportView):
    """
    Handles the CSV export for the Budget Variance report.
    It inherits the data-gathering logic from BudgetVarianceReportView.
    """
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        report_data = self._get_budget_variance_data(request)
        academic_year = report_data.get('academic_year')

        if not academic_year:
            messages.error(request, "Please select a valid period to generate the CSV export.")
            return redirect('reporting:budget_variance_report')

        response = HttpResponse(content_type='text/csv')
        filename = f"Budget_Variance_{academic_year.name.replace(' ', '_')}.csv"
        response['Content-Disposition'] = f'attachment; filename="{filename}"'

        writer = csv.writer(response)

        # Write report summary
        writer.writerow([self.report_title])
        writer.writerow([f'Academic Year: {academic_year.name}'])
        if report_data.get('term'):
            writer.writerow([f'Term: {report_data["term"].name}'])
        writer.writerow([])

        # Write table headers
        writer.writerow([
            'Item', 'Type', 'Account Code', 'Budgeted Amount',
            'Actual Amount', 'Variance'
        ])

        # Write data rows
        for item in report_data.get('budget_items_variance', []):
            writer.writerow([
                item.get('item_name'), item.get('budget_item_type'), item.get('account_code'),
                item.get('budgeted'), item.get('actual'), item.get('variance')
            ])
        
        # Write summary totals
        writer.writerow([]); writer.writerow(['Summary Totals'])
        writer.writerow(['', '', 'Total Budgeted Income', report_data['total_budgeted_income']])
        writer.writerow(['', '', 'Total Actual Income', report_data['total_actual_income']])
        writer.writerow(['', '', 'Total Budgeted Expense', report_data['total_budgeted_expense']])
        writer.writerow(['', '', 'Total Actual Expense', report_data['total_actual_expense']])
        writer.writerow(['', '', 'Net Profit/Loss (Budgeted)', report_data['net_budgeted_profit_loss']])
        writer.writerow(['', '', 'Net Profit/Loss (Actual)', report_data['net_actual_profit_loss']])

        return response


class BudgetVarianceExportExcelView(BudgetVarianceReportView):
    """
    Handles the Excel (.xlsx) export for the Budget Variance report.
    """
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        if not openpyxl:
            messages.error(request, "The library required for Excel exports is not installed.")
            return redirect('reporting:budget_variance_report')

        report_data = self._get_budget_variance_data(request)
        academic_year = report_data.get('academic_year')

        if not academic_year:
            messages.error(request, "Please select a valid period to generate the Excel export.")
            return redirect('reporting:budget_variance_report')

        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "Budget Variance"

        # --- Styles ---
        header_font = Font(bold=True, size=14)
        subheader_font = Font(bold=True, size=12)
        table_header_font = Font(bold=True)
        currency_format = '#,##0.00'

        # --- Header ---
        ws['A1'] = self.report_title
        ws['A1'].font = header_font
        ws.merge_cells('A1:F1')
        ws['A2'] = f"Academic Year: {academic_year.name}"
        if report_data.get('term'):
            ws['A3'] = f"Term: {report_data['term'].name}"

        # --- Table Headers ---
        headers = ['Item', 'Type', 'Account Code', 'Budgeted', 'Actual', 'Variance']
        for col_num, header_title in enumerate(headers, 1):
            cell = ws.cell(row=5, column=col_num, value=header_title)
            cell.font = table_header_font

        # --- Data Rows ---
        row_num = 6
        for item in report_data.get('budget_items_variance', []):
            ws.cell(row=row_num, column=1, value=item.get('item_name'))
            ws.cell(row=row_num, column=2, value=item.get('budget_item_type'))
            ws.cell(row=row_num, column=3, value=item.get('account_code'))
            
            b_cell = ws.cell(row=row_num, column=4, value=item.get('budgeted'))
            a_cell = ws.cell(row=row_num, column=5, value=item.get('actual'))
            v_cell = ws.cell(row=row_num, column=6, value=item.get('variance'))
            
            for cell in [b_cell, a_cell, v_cell]:
                cell.number_format = currency_format
            row_num += 1

        # --- Totals Section ---
        row_num += 2 # Add a small gap
        ws.cell(row=row_num, column=3, value="Summary").font = subheader_font
        
        summary_items = [
            ("Total Budgeted Income", report_data['total_budgeted_income']),
            ("Total Actual Income", report_data['total_actual_income']),
            ("Total Income Variance", report_data['total_variance_income']),
            ("Total Budgeted Expense", report_data['total_budgeted_expense']),
            ("Total Actual Expense", report_data['total_actual_expense']),
            ("Total Expense Variance", report_data['total_variance_expense']),
            ("Net Profit/Loss (Budgeted)", report_data['net_budgeted_profit_loss']),
            ("Net Profit/Loss (Actual)", report_data['net_actual_profit_loss']),
        ]
        
        for i, (label, value) in enumerate(summary_items, start=row_num):
            ws.cell(row=i, column=4, value=label).alignment = Alignment(horizontal='right')
            value_cell = ws.cell(row=i, column=5, value=value)
            value_cell.number_format = currency_format
            value_cell.font = Font(bold=True)

        # Auto-fit columns
        for col in ws.columns:
            max_length = 0
            column = col[0].column_letter
            for cell in col:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(cell.value)
                except:
                    pass
            adjusted_width = (max_length + 2)
            ws.column_dimensions[column].width = adjusted_width

        # --- Create Response ---
        response = HttpResponse(
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        filename = f"Budget_Variance_{academic_year.name.replace(' ', '_')}.xlsx"
        response['Content-Disposition'] = f'attachment; filename="{filename}"'
        
        wb.save(response)
        return response


class BudgetVarianceExportPDFView(BudgetVarianceReportView):
    """
    Handles the PDF export for the Budget Variance report.
    """
    http_method_names = ['get']
    
    def get(self, request, *args, **kwargs):
        report_data = self._get_budget_variance_data(request)
        academic_year = report_data.get('academic_year')

        if not academic_year:
            messages.error(request, "Please select a period to generate the PDF report.")
            return redirect('reporting:budget_variance_report')

        # Add any other context needed specifically by the PDF template
        pdf_context = report_data.copy() # Start with all report data
        pdf_context['report_title'] = self.report_title
        pdf_context['school_profile'] = SchoolProfile.objects.first() # Example
        pdf_context['tenant'] = getattr(request, 'tenant', None)
        pdf_context['current_datetime'] = timezone.now()

        try:
            pdf = render_to_pdf('reporting/pdf/budget_variance_pdf.html', pdf_context)

            if pdf:
                response = HttpResponse(pdf, content_type='application/pdf')
                filename = f"Budget_Variance_{academic_year.name.replace(' ', '_')}.pdf"
                # Use 'inline' to display in browser, 'attachment' to force download
                response['Content-Disposition'] = f'inline; filename="{filename}"'
                return response
            else:
                messages.error(request, "The system failed to generate the PDF document.")
                return redirect('reporting:budget_variance_report')
        except Exception as e:
            logger.error(f"Error generating PDF for Budget Variance Report: {e}", exc_info=True)
            messages.error(request, f"An unexpected error occurred while creating the PDF: {e}")
            return redirect('reporting:budget_variance_report')


# # apps/reporting/views.py
# import logging
# from decimal import Decimal

# from django.views.generic import TemplateView
# from django.urls import reverse_lazy
# from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin
# from django.contrib import messages
# from django.db.models import Sum
# from django.db.models.functions import Coalesce

# # Models (ensure all are correctly imported)
# from apps.schools.models import AcademicYear, Term # Assuming these are in schools app
# from apps.finance.models import BudgetAmount, BudgetItem # Assuming these are in finance app
# from apps.accounting.models import AccountType, JournalEntryItem # Corrected import for JELine if it's JournalEntryItem
# # Note: If your JournalEntryLine is actually JournalEntryItem, use that name.

# # Forms (your filter form for this report)
# from .forms import BudgetVarianceReportForm # Or from wherever it's defined

# logger = logging.getLogger(__name__)


# from django.conf import settings
# from django.views.generic import TemplateView
# from django.urls import reverse_lazy
# from django.utils import timezone
# from django.db.models import Sum, F, Q, DecimalField
# from django.db.models.functions import Coalesce
# from decimal import Decimal
# from django.contrib import messages
# from django.utils.translation import gettext_lazy as _
# from django.http import HttpResponse # For exports
# from django.shortcuts import redirect

# # Your model imports - CRITICAL: Ensure these paths and names are correct
# from apps.accounting.models import Account, JournalEntry, JournalLine, AccountType
# from apps.finance.models import BudgetAmount, BudgetItem # Assuming BudgetItem and BudgetAmount are here
# from apps.schools.models import AcademicYear, Term, SchoolProfile # Assuming Term is here

# # Your form import
# from .forms import BudgetVarianceReportForm # Ensure this form is defined correctly

# # Your mixin imports
# from apps.common.mixins import TenantPermissionRequiredMixin, BaseReportViewMixin

# # Your PDF utility and export libraries
# from apps.common.utils import render_to_pdf
# import csv
# import openpyxl
# from openpyxl.utils import get_column_letter
# from openpyxl.styles import Font, Alignment
# from io import BytesIO

# import logging
# logger = logging.getLogger(__name__)


# class BudgetVarianceReportView(TenantPermissionRequiredMixin, BaseReportViewMixin, TemplateView):
#     permission_required = 'reporting.view_budget_variance_report'
#     template_name = 'reporting/budget_variance_report.html'

#     report_code = 'budget_variance_report'
#     filter_form_class = BudgetVarianceReportForm

#     report_code = 'RPT_BUDGET_VARIANCE'
#     filter_form_class = BudgetReportFilterForm # Example, replace with your actual form
#     report_title_text = _("Budget Performance & Variance")
    
#     @property
#     def report_title(self):
#         form = getattr(self, 'processed_filter_form', None)
#         ay_obj, term_obj = None, None
#         if form:
#             if form.is_bound and form.is_valid():
#                 ay_obj = form.cleaned_data.get('academic_year')
#                 term_obj = form.cleaned_data.get('term')
#             elif not form.is_bound and form.initial:
#                 ay_pk = form.initial.get('academic_year')
#                 term_pk = form.initial.get('term')
#                 if ay_pk:
#                     try: ay_obj = AcademicYear.objects.get(pk=ay_pk)
#                     except AcademicYear.DoesNotExist: ay_obj = None
#                 if term_pk:
#                     try: term_obj = Term.objects.get(pk=term_pk)
#                     except Term.DoesNotExist: term_obj = None
        
#         title = _("Budget vs Actuals (Variance Report)")
#         if ay_obj:
#             title += f" - AY: {ay_obj.name}"
#             if term_obj:
#                 title += f", Term: {term_obj.name}"
#         return title

#     def get_filter_form_initial_data(self):
#         current_ay = AcademicYear.objects.filter(is_active=True, is_current=True).first()
#         if not current_ay:
#             current_ay = AcademicYear.objects.filter(is_active=True).order_by('-start_date').first()
#         return {'academic_year': current_ay.pk if current_ay else None, 'term': None}

#     def get_report_data(self, filter_form=None): # Signature corrected
#         """
#         Fetches and calculates data for the Budget Variance Report.
#         Uses self.form (set by BaseReportViewMixin.get()) for filter criteria.
#         """
#         academic_year_obj, term_obj = None, None
        
#         # Initialize the report_data dictionary with all expected keys
#         report_data = {
#             'academic_year_name': None, 'term_name': None, 
#             'budget_items_variance': [],
#             'total_budgeted_income': Decimal('0.00'), 
#             'total_actual_income': Decimal('0.00'),
#             'total_variance_income': Decimal('0.00'),
#             'total_budgeted_expense': Decimal('0.00'), 
#             'total_actual_expense': Decimal('0.00'),
#             'total_variance_expense': Decimal('0.00'),
#             'net_budgeted_profit_loss': Decimal('0.00'),
#             'net_actual_profit_loss': Decimal('0.00'),
#             'net_variance_profit_loss': Decimal('0.00'),
#             'report_generated_successfully': False, # Changed from 'report_generated'
#             'filter_form_errors': None, # Store form errors if any
#             'start_date_for_actuals': None, # For clarity in template
#             'end_date_for_actuals': None,   # For clarity in template
#         }

#         filter_form = self.form # Access the form instance set by BaseReportViewMixin's get() method

#         if filter_form: # Check if a filter form is even being used
#             if filter_form.is_valid(): # Form was bound with GET data and is valid
#                 academic_year_obj = filter_form.cleaned_data.get('academic_year')
#                 term_obj = filter_form.cleaned_data.get('term') # Optional term
#                 logger.debug(f"{self.__class__.__name__}: Using valid filter data: AY={academic_year_obj}, Term={term_obj}")
#             elif not filter_form.is_bound and filter_form.initial: # Form is unbound, use its initial data
#                 ay_pk = filter_form.initial.get('academic_year')
#                 term_pk = filter_form.initial.get('term')
#                 if ay_pk:
#                     try: academic_year_obj = AcademicYear.objects.get(pk=ay_pk)
#                     except AcademicYear.DoesNotExist: logger.warning(f"Initial AY PK {ay_pk} not found.")
#                 if term_pk:
#                     try: term_obj = Term.objects.get(pk=term_pk)
#                     except Term.DoesNotExist: logger.warning(f"Initial Term PK {term_pk} not found.")
#                 logger.debug(f"{self.__class__.__name__}: Using initial filter data: AY={academic_year_obj}, Term={term_obj}")
#             else: # Form is bound but invalid
#                 report_data['filter_form_errors'] = filter_form.errors
#                 logger.warning(f"{self.__class__.__name__}.get_report_data: Filter form is bound but invalid. Errors: {filter_form.errors}")
#                 messages.error(self.request, _("Please correct the filter errors to generate the report."))
#                 return report_data # Return with report_generated_successfully = False
#         else: # No filter form was defined/used
#             logger.warning(f"{self.__class__.__name__}: No filter form provided. Cannot generate budget variance report without Academic Year.")
#             messages.error(self.request, _("Report filters are missing. Cannot generate report."))
#             return report_data
        
#         if not academic_year_obj:
#             logger.error(f"{self.__class__.__name__}: Academic Year is required but not found/selected.")
#             messages.error(self.request, _("An Academic Year must be selected to generate the budget variance report."))
#             return report_data # Return with report_generated_successfully = False

#         report_data.update({
#             'academic_year_name': academic_year_obj.name,
#             'term_name': term_obj.name if term_obj else _("Full Academic Year"),
#         })
#         logger.info(f"Generating Budget Variance for AY: {academic_year_obj.name}{', Term: ' + term_obj.name if term_obj else ' (Full Year)'}")

#         # Determine date range for actuals
#         if term_obj and hasattr(term_obj, 'start_date') and hasattr(term_obj, 'end_date') and term_obj.start_date and term_obj.end_date:
#             start_date_actuals = term_obj.start_date
#             end_date_actuals = term_obj.end_date
#         elif hasattr(academic_year_obj, 'start_date') and hasattr(academic_year_obj, 'end_date') and academic_year_obj.start_date and academic_year_obj.end_date:
#             start_date_actuals = academic_year_obj.start_date
#             end_date_actuals = academic_year_obj.end_date
#         else:
#             logger.error(f"Cannot determine valid date range for actuals for AY: {academic_year_obj.name}, Term: {term_obj.name if term_obj else 'N/A'}")
#             messages.error(self.request, _("Could not determine the date range for calculating actual amounts."))
#             return report_data
            
#         report_data['start_date_for_actuals'] = start_date_actuals
#         report_data['end_date_for_actuals'] = end_date_actuals

#         report_lines_data = []
        
#         budget_amounts_qs = BudgetAmount.objects.filter(academic_year=academic_year_obj)
#         if term_obj:
#             budget_amounts_qs = budget_amounts_qs.filter(term=term_obj)
        
#         budget_amounts_qs = budget_amounts_qs.select_related(
#             'budget_item', 
#             'budget_item__linked_coa_account',
#             'budget_item__linked_coa_account__account_type' # Account -> AccountType
#         ).order_by('budget_item__budget_item_type', 'budget_item__name')

#         for ba in budget_amounts_qs:
#             budget_item = ba.budget_item
#             budgeted_amount = ba.amount
#             actual_amount = Decimal('0.00')

#             if budget_item.linked_coa_account:
#                 account_to_check = budget_item.linked_coa_account
                
#                 # Use the Account model's get_balance method for period calculation
#                 actual_amount = account_to_check.get_balance(
#                     start_date=start_date_actuals,
#                     as_of_date=end_date_actuals
#                 )

#             else:
#                 # No linked account, actual amount remains 0.00
#                 logger.info(f"Budget item '{budget_item.name}' has no linked CoA account. Actual amount will be 0.00.")
            
#             variance = budgeted_amount - actual_amount
#             variance_percentage = (variance / budgeted_amount * 100) if budgeted_amount != Decimal('0.00') else Decimal('0.00')
            
#             is_favorable = False
#             # Ensure BudgetItem.ItemTypeChoices.EXPENSE/REVENUE match your model enum
#             if hasattr(BudgetItem, 'ItemTypeChoices'): # Check if enum exists
#                 if budget_item.budget_item_type == BudgetItem.ItemTypeChoices.EXPENSE:
#                     if actual_amount <= budgeted_amount: is_favorable = True 
#                 elif budget_item.budget_item_type == BudgetItem.ItemTypeChoices.REVENUE:
#                     if actual_amount >= budgeted_amount: is_favorable = True
#             else:
#                 logger.warning("BudgetItem.ItemTypeChoices not found on BudgetItem model. Favorable status cannot be determined.")
            
#             report_lines_data.append({
#                 'item_name': budget_item.name, 
#                 'account_code': budget_item.linked_coa_account.account_code if budget_item.linked_coa_account else 'N/A',
#                 'budget_item_type': budget_item.get_budget_item_type_display() if hasattr(budget_item, 'get_budget_item_type_display') else budget_item.budget_item_type,
#                 'budgeted': budgeted_amount, 
#                 'actual': actual_amount, 
#                 'variance': variance,
#                 'variance_percentage': variance_percentage, 
#                 'is_favorable': is_favorable
#             })

#             if hasattr(BudgetItem, 'ItemTypeChoices'):
#                 if budget_item.budget_item_type == BudgetItem.ItemTypeChoices.REVENUE:
#                     report_data['total_budgeted_income'] += budgeted_amount
#                     report_data['total_actual_income'] += actual_amount
#                 elif budget_item.budget_item_type == BudgetItem.ItemTypeChoices.EXPENSE:
#                     report_data['total_budgeted_expense'] += budgeted_amount
#                     report_data['total_actual_expense'] += actual_amount
        
#         report_data['budget_items_variance'] = report_lines_data
        
#         report_data['total_variance_income'] = report_data['total_budgeted_income'] - report_data['total_actual_income']
#         report_data['total_variance_expense'] = report_data['total_budgeted_expense'] - report_data['total_actual_expense']
        
#         report_data['net_budgeted_profit_loss'] = report_data['total_budgeted_income'] - report_data['total_budgeted_expense']
#         report_data['net_actual_profit_loss'] = report_data['total_actual_income'] - report_data['total_actual_expense']
#         report_data['net_variance_profit_loss'] = report_data['net_budgeted_profit_loss'] - report_data['net_actual_profit_loss']
        
#         report_data['report_generated_successfully'] = True
#         return report_data
    

#     def export_to_excel(self, report_data_dict, request): # report_data is dict
#         # ... (Implement Excel for Budget Variance) ...
#         messages.info(request, "Excel export for Budget Variance Report is under development.")
#         return redirect(request.path)

#     def export_to_pdf(self, report_data_dict, request, html_context): # report_data is dict
#         pdf_context = {
#             'report_data': report_data_dict, 'school_profile': self.get_school_profile(),
#             'report_title': self.get_report_title(), 'current_datetime': timezone.now(),
#         }
#         pdf = render_to_pdf(self.get_pdf_template_name(), pdf_context)
#         if pdf:
#             response = HttpResponse(pdf, content_type='application/pdf')
#             ay_name = report_data_dict['academic_year'].name.replace(" ", "_") if report_data_dict.get('academic_year') else "period"
#             term_name = f"_{report_data_dict['term'].name.replace(' ', '_')}" if report_data_dict.get('term') else ""
#             filename = f"{self.report_code}_{ay_name}{term_name}.pdf"
#             response['Content-Disposition'] = f'inline; filename="{filename}"'
#             return response
#         else:
#             logger.error(f"PDF generation failed for {self.report_code} (Budget Variance)")
#             messages.error(request, "Failed to generate PDF report.")
#             return redirect(request.path)


from django.views import View
from django.shortcuts import render


# = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = 
#
#      THE END OF BUDGET VARIANCE REPORT
#
# = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
class ReportingHomeView(TenantPermissionRequiredMixin, TemplateView):
    template_name = 'reporting/reporting_home.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Generate report links based on user permissions
        report_links = []
        user = self.request.user

        # Basic Reports
        if user.has_perm('reporting.view_outstanding_fees_report'):
            report_links.append({
                'name': 'Outstanding Fees Report',
                'url_name': 'reporting:outstanding_fees_report'
            })

        if user.has_perm('reporting.view_collection_report'):
            report_links.append({
                'name': 'Collection Report',
                'url_name': 'reporting:collection_report'
            })

        if user.has_perm('reporting.view_payment_summary_report'):
            report_links.append({
                'name': 'Payment Summary Report',
                'url_name': 'reporting:payment_summary_report'
            })

        if user.has_perm('reporting.view_student_ledger_report'):
            report_links.append({
                'name': 'Student Ledger Report',
                'url_name': 'reporting:student_ledger_report'
            })

        if user.has_perm('reporting.view_fee_projection_report'):
            report_links.append({
                'name': 'Fee Projection Report',
                'url_name': 'reporting:fee_projection_report'
            })

        # Advanced Financial Reports
        if user.has_perm('reporting.view_trial_balance_report'):
            report_links.append({
                'name': 'Trial Balance Report',
                'url_name': 'reporting:trial_balance_report'
            })

        if user.has_perm('reporting.view_income_statement_report') or user.has_perm('reporting.view_income_expense_report'):
            report_links.append({
                'name': 'Income Statement Report',
                'url_name': 'reporting:income_expense_report'
            })

        if user.has_perm('reporting.view_balance_sheet_report'):
            report_links.append({
                'name': 'Balance Sheet Report',
                'url_name': 'reporting:balance_sheet_report'
            })

        if user.has_perm('reporting.view_cash_flow_statement_report') or user.has_perm('reporting.view_cash_flow_statement'):
            report_links.append({
                'name': 'Cash Flow Statement Report',
                'url_name': 'reporting:cash_flow_statement_report'
            })

        if user.has_perm('reporting.view_budget_variance_report'):
            report_links.append({
                'name': 'Budget Variance Report',
                'url_name': 'reporting:budget_variance_report'
            })

        if user.has_perm('reporting.view_expense_report'):
            report_links.append({
                'name': 'Expense Report',
                'url_name': 'reporting:expense_report'
            })

        context['report_links'] = report_links
        context['view_title'] = 'Reports Dashboard'

        return context



# D:\school_fees_saas_v2\apps\reporting\views.py
from django.shortcuts import render, get_object_or_404
from django.views.generic import ListView, DetailView, TemplateView
from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin
from django.db.models import Sum, Q, F
from django.utils import timezone
import datetime

from apps.payments.models import Payment
from apps.fees.models import Invoice, StudentConcession
from apps.students.models import Student
from apps.common.utils import get_current_academic_year # Assuming you have this utility

from .forms import PaymentSummaryFilterForm, StudentLedgerFilterForm

# --- Base Report View (Optional, for common functionality) ---
class BaseReportView(LoginRequiredMixin, PermissionRequiredMixin, TemplateView):
    raise_exception = True # Or False for redirect to login

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['current_academic_year'] = get_current_academic_year(self.request)
        # Add other common report context if any
        return context

# = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = 
#
#      PAYMENT SUMMARY REPORT
#
# = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = 

# --- Payment Summary Report View ---

# # D:\school_fees_saas_v2\apps\reporting\views.py
# from django.views.generic import ListView
# from django.utils.translation import gettext_lazy as _
# from django.utils import timezone
# from django.db.models import Sum, F, Q, ExpressionWrapper, DecimalField, Value, OuterRef, Subquery
# from django.db.models.functions import Coalesce, TruncDate
# from decimal import Decimal
# import datetime # For string to date conversion if needed from GET params

# from apps.common.mixins import TenantLoginRequiredMixin, TenantPermissionRequiredMixin, BaseReportViewMixin
# from apps.payments.models import Payment, PaymentMethod # Your Payment model
# from apps.students.models import Student, SchoolClass, Section # For filtering potentially
# from .forms import PaymentSummaryFilterForm # Your filter form for this report

# import csv
# import logging
# from decimal import Decimal
# from django.http import HttpResponse
# from django.views.generic import TemplateView, View
# from django.db.models import Sum, Count
# from django.db.models.functions import Coalesce
# from django.shortcuts import redirect
# from django.contrib import messages
# from django.utils.translation import gettext_lazy as _
# from django.core.paginator import Paginator

# from apps.common.mixins import TenantPermissionRequiredMixin, BaseReportViewMixin
# from apps.payments.models import Payment
# from apps.schools.models import SchoolProfile
# from .filters import PaymentReportFilter
# from apps.common.utils import render_to_pdf

# logger = logging.getLogger(__name__)

class PaymentSummaryReportView(TenantPermissionRequiredMixin, BaseReportViewMixin, TemplateView):
    template_name = 'reporting/payment_summary_report.html'
    permission_required = 'reporting.view_payment_summary_report'
    report_title = _("Payment Summary Report")
    filter_form_class = PaymentReportFilter
    export_csv_url_name = 'reporting:payment_summary_export_csv'
    export_pdf_url_name = 'reporting:payment_summary_export_pdf'
    export_excel_url_name = 'reporting:payment_summary_export_excel'

    def _get_report_data(self, request):
        base_queryset = Payment.objects.filter(
            status=Payment.PaymentStatus.COMPLETED
        ).select_related('student', 'payment_method', 'parent_payer')

        filter_form = self.filter_form_class(request.GET, queryset=base_queryset)
        filtered_queryset = filter_form.qs.order_by('-payment_date')

        summary_totals = filtered_queryset.aggregate(
            total_amount=Coalesce(Sum('amount'), Decimal('0.00')),
            total_payments=Count('id')
        )
        
        method_breakdown = filtered_queryset.values(
            'payment_method__name'
        ).annotate(
            method_total=Sum('amount'),
            method_count=Count('id')
        ).order_by('-method_total')

        data = {
            'payments': filtered_queryset,
            'filter_form': filter_form.form,
            'summary_totals': summary_totals,
            'method_breakdown': method_breakdown,
            'start_date': filter_form.cleaned_data.get('start_date') if filter_form.is_valid() else None,
            'end_date': filter_form.cleaned_data.get('end_date') if filter_form.is_valid() else None,
        }
        return data

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        report_data = self._get_report_data(self.request)
        
        paginator = Paginator(report_data['payments'], 30)
        page_number = self.request.GET.get('page')
        page_obj = paginator.get_page(page_number)

        context.update(report_data)
        context['payments'] = page_obj
        context['page_obj'] = page_obj
        context['is_paginated'] = page_obj.has_other_pages()
        
        return context


class PaymentSummaryExportCSVView(PaymentSummaryReportView):
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        report_data = self._get_report_data(request)
        payments = report_data.get('payments', [])

        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename="payment_summary_report.csv"'
        writer = csv.writer(response)

        writer.writerow([self.report_title])
        writer.writerow([f"Period: {report_data['start_date'] or 'Beginning'} to {report_data['end_date'] or 'Today'}"])
        writer.writerow([])
        writer.writerow(['Total Amount Received', report_data['summary_totals'].get('total_amount')])
        writer.writerow(['Total Number of Payments', report_data['summary_totals'].get('total_payments')])
        writer.writerow([])
        
        writer.writerow(['Payment Date', 'Student', 'Admission No', 'Payment Method', 'Reference', 'Amount'])

        for payment in payments:
            writer.writerow([
                payment.payment_date.strftime('%Y-%m-%d'),
                payment.student.full_name if payment.student else 'N/A',
                payment.student.admission_number if payment.student else 'N/A',
                payment.payment_method.name if payment.payment_method else 'N/A',
                payment.reference_number or '',
                payment.amount
            ])
        return response


class PaymentSummaryExportPDFView(PaymentSummaryReportView):
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        report_data = self._get_report_data(request)
        
        report_data['school_profile'] = SchoolProfile.objects.first()
        report_data['tenant'] = request.tenant
        report_data['report_title'] = self.report_title

        pdf = render_to_pdf('reporting/pdf/payment_summary_pdf.html', report_data)
        if not pdf:
            messages.error(request, "There was an error generating the PDF report.")
            return redirect('reporting:payment_summary_report')

        response = HttpResponse(pdf, content_type='application/pdf')
        response['Content-Disposition'] = 'inline; filename="payment_summary_report.pdf"'
        return response


class PaymentSummaryExportExcelView(PaymentSummaryReportView):
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        try:
            import openpyxl
            from openpyxl.styles import Font
        except ImportError:
            messages.error(request, "The library required for Excel export (openpyxl) is not installed.")
            return redirect('reporting:payment_summary_report')

        report_data = self._get_report_data(request)
        payments = report_data.get('payments', [])

        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "Payment Summary"
        
        header_font = Font(bold=True, size=14)
        sub_header_font = Font(bold=True)
        
        ws['A1'] = self.report_title
        ws['A1'].font = header_font
        ws['A2'] = f"Period: {report_data['start_date'] or 'Beginning'} to {report_data['end_date'] or 'Today'}"
        
        ws['A4'] = "Total Amount Received:"
        ws['A4'].font = sub_header_font
        ws['B4'] = report_data['summary_totals'].get('total_amount')
        ws['B4'].number_format = '#,##0.00'

        ws['A5'] = "Total Number of Payments:"
        ws['A5'].font = sub_header_font
        ws['B5'] = report_data['summary_totals'].get('total_payments')
        
        headers = ['Payment Date', 'Student', 'Admission No', 'Payment Method', 'Reference', 'Amount']
        for col_num, header_title in enumerate(headers, 1):
            ws.cell(row=7, column=col_num, value=header_title).font = sub_header_font

        current_row = 8
        for payment in payments:
            ws.cell(row=current_row, column=1, value=payment.payment_date)
            ws.cell(row=current_row, column=2, value=payment.student.full_name if payment.student else 'N/A')
            ws.cell(row=current_row, column=3, value=payment.student.admission_number if payment.student else 'N/A')
            ws.cell(row=current_row, column=4, value=payment.payment_method.name if payment.payment_method else 'N/A')
            ws.cell(row=current_row, column=5, value=payment.reference_number or '')
            amount_cell = ws.cell(row=current_row, column=6, value=payment.amount)
            amount_cell.number_format = '#,##0.00'
            current_row += 1
            
        ws.column_dimensions['A'].width = 15
        ws.column_dimensions['B'].width = 30
        ws.column_dimensions['C'].width = 15
        ws.column_dimensions['D'].width = 20
        ws.column_dimensions['E'].width = 25
        ws.column_dimensions['F'].width = 15

        response = HttpResponse(content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        response['Content-Disposition'] = 'attachment; filename="payment_summary_report.xlsx"'
        wb.save(response)
        return response

# class PaymentSummaryReportView(TenantLoginRequiredMixin, TenantPermissionRequiredMixin, BaseReportViewMixin, ListView):
#     model = Payment # The primary listing is of payments
#     template_name = 'reporting/payment_summary_report.html' # Ensure this template exists
#     context_object_name = 'payments_list' # For the paginated list of payments
#     paginate_by = 30

#     permission_required = 'reporting.view_payment_summary_report'
#     report_title = _("Payment Summary Report")
#     report_code = 'RPT_PAYMENT_SUMMARY'
#     filter_form_class = PaymentSummaryFilterForm # Assign your Django Form for filtering

#     def get_filter_form_initial_data(self):
#         """Provides initial data for the filter form."""
#         # Default to show all payments (no date filtering)
#         return {
#             'start_date': None,  # No start date filter
#             'end_date': None     # No end date filter
#         }

#     def get_queryset(self):
#         logger.debug(f"[{self.__class__.__name__}] get_queryset started. Request GET: {self.request.GET}")
        
#         # Base queryset
#         # Corrected select_related based on actual Payment model fields.
#         queryset = Payment.objects.select_related(
#             'student',
#             'student__current_class',   # Student has current_class field
#             'payment_method',
#             'processed_by_staff',       # Links to StaffUser
#             'created_by',               # Links to AUTH_USER_MODEL
#             'parent_payer'              # Links to ParentUser
#         ).prefetch_related(
#             'allocations__invoice'      # Prefetch allocations to show which invoices a payment was allocated to
#         ).order_by('-payment_date', '-created_at')

#         # --- Filter Application ---
#         # BaseReportViewMixin should handle instantiating self.form (from filter_form_class)
#         # or self.filterset (from filterset_class) in its get_context_data or a setup method.
#         # We then use the cleaned_data or filterset.qs here.

#         self.form = None # Will be set by BaseReportViewMixin or in get_context_data if not ListView
#         start_date = None
#         end_date = None

#         if hasattr(self, 'filter_form_class') and self.filter_form_class:
#             # This assumes BaseReportViewMixin populates self.form with the instantiated form
#             # and self.processed_filter_data with cleaned_data if valid.
#             # We need to instantiate it here if BaseReportViewMixin doesn't do it before get_queryset.
#             # For ListView, get_queryset is called before get_context_data.
#             # So, let's instantiate the form here for filtering.
            
#             initial_form_data = self.get_filter_form_initial_data() # From BaseReportViewMixin
#             self.form = self.filter_form_class(self.request.GET or None, initial=initial_form_data, request=self.request)
#             context_filter_form = self.form # Store for get_context_data later

#             if self.form.is_valid():
#                 logger.debug(f"[{self.__class__.__name__}] Applying form filters: {self.form.cleaned_data}")
#                 cd = self.form.cleaned_data
#                 start_date = cd.get('start_date')
#                 end_date = cd.get('end_date')
#                 payment_method_filter = cd.get('payment_method')
#                 student_filter = cd.get('student')
#                 class_filter = cd.get('school_class') # Assuming field name in form

#                 if start_date:
#                     queryset = queryset.filter(payment_date__gte=start_date)
#                 if end_date:
#                     # Adjust end_date to include the whole day
#                     end_date_adjusted = datetime.datetime.combine(end_date, datetime.time.max)
#                     if settings.USE_TZ:
#                         end_date_adjusted = timezone.make_aware(end_date_adjusted, timezone.get_default_timezone())
#                     queryset = queryset.filter(payment_date__lte=end_date_adjusted)
                
#                 if payment_method_filter:
#                     queryset = queryset.filter(payment_method=payment_method_filter)
#                 if student_filter:
#                     queryset = queryset.filter(student=student_filter)
#                 if class_filter:
#                     queryset = queryset.filter(student__current_class=class_filter)
#                 # ... other filters ...
#             elif self.request.GET: # Form submitted but not valid
#                 logger.warning(f"[{self.__class__.__name__}] Filter form submitted but invalid: {self.form.errors}")
#         else: # No filter form class defined
#             # Show all payments without date filtering
#             logger.debug(f"[{self.__class__.__name__}] No filter form class defined, showing all payments")
#             pass  # No additional filtering - show all payments

#         # Store start_date and end_date on self for get_report_data and get_context_data
#         self.report_period_start = start_date
#         self.report_period_end = end_date
            
#         logger.debug(f"[{self.__class__.__name__}] get_queryset final count: {queryset.count()}")
#         self.full_report_queryset = queryset # Store for aggregations and exports
#         return queryset # This will be paginated by ListView

#     def get_report_data(self, filter_form=None):
#         logger.debug(f"[{self.__class__.__name__}] get_report_data called.")
#         report_specific_data = {}

#         # Ensure full_report_queryset is available by calling get_queryset if needed
#         if not hasattr(self, 'full_report_queryset') or self.full_report_queryset is None:
#             logger.debug(f"[{self.__class__.__name__}] full_report_queryset not set, calling get_queryset().")
#             self.get_queryset()

#         if hasattr(self, 'full_report_queryset') and self.full_report_queryset is not None:
#             # Aggregate total amount from the filtered payments
#             total_collected_agg = self.full_report_queryset.aggregate(
#                 total=Sum('amount') # <<< CORRECTED: Use 'amount' instead of 'amount_paid'
#             )
#             report_specific_data['total_amount_collected'] = total_collected_agg.get('total') or Decimal('0.00')

#             # Example: Payments by payment method
#             payments_by_method = self.full_report_queryset.values(
#                 'payment_method__name' # Group by payment method name
#             ).annotate(
#                 total=Sum('amount'), # <<< CORRECTED
#                 count=Count('id')
#             ).order_by('-total')
#             report_specific_data['payments_by_method_summary'] = payments_by_method

#             logger.debug(f"[{self.__class__.__name__}] Total collected: {report_specific_data['total_amount_collected']}")
#         else:
#             logger.warning(f"[{self.__class__.__name__}] full_report_queryset not available for aggregation in get_report_data.")
#             report_specific_data['total_amount_collected'] = Decimal('0.00')
#             report_specific_data['payments_by_method_summary'] = []
            
#         return report_specific_data
    

#     def get_context_data(self, **kwargs):
#         # ListView's get_context_data calls get_queryset and sets up pagination.
#         # BaseReportViewMixin's get_context_data then calls get_report_data.
#         context = super().get_context_data(**kwargs) # This calls BaseReportViewMixin's get_context_data

#         # Add report period to context for display in template
#         context['report_period_start'] = getattr(self, 'report_period_start', None)
#         context['report_period_end'] = getattr(self, 'report_period_end', None)
        
#         # Ensure filter_form is in context if not already added by BaseReportViewMixin
#         if 'filter_form' not in context and hasattr(self, 'form') and self.form:
#             context['filter_form'] = self.form
#         elif 'filter_form' not in context and hasattr(self, 'filterset') and self.filterset:
#             context['filter_form'] = self.filterset.form


#         # context['report_description'] from your original is good, can be set by BaseReportViewMixin too
#         context['report_description'] = _("This report summarizes payments received within the selected period and filters.")
        
#         logger.debug(f"[{self.__class__.__name__}] Final context keys: {list(context.keys())}")
#         return context

#     def export_to_csv(self, queryset_for_export, request):
#         queryset = queryset_for_export or self.get_export_queryset()
#         response = HttpResponse(content_type='text/csv')
#         response['Content-Disposition'] = f'attachment; filename="{self.report_code}_{timezone.now().strftime("%Y%m%d")}.csv"'

#         writer = csv.writer(response)
#         writer.writerow([
#             str(_('Date Paid')), str(_('Student Name')), str(_('Class')),
#             str(_('Invoice #')), str(_('Payment Ref #')), str(_('Payment Method')),
#             str(_('Amount Paid'))
#         ])

#         for payment in queryset:
#             # Get invoice numbers from allocations
#             invoice_numbers = ', '.join([
#                 allocation.invoice.invoice_number
#                 for allocation in payment.allocations.all()
#                 if allocation.invoice
#             ]) or 'Unallocated'

#             writer.writerow([
#                 payment.payment_date.strftime('%Y-%m-%d'),
#                 payment.student.get_full_name() if payment.student else 'N/A',
#                 payment.student.current_class_and_section if payment.student else 'N/A',
#                 invoice_numbers,
#                 payment.transaction_reference or 'N/A',
#                 payment.payment_method.name if payment.payment_method else 'N/A',
#                 payment.amount
#             ])

#         return response

#     def export_to_excel(self, queryset_for_export, request):
#         queryset = queryset_for_export or self.get_export_queryset()
#         response = HttpResponse(content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
#         response['Content-Disposition'] = f'attachment; filename="{self.report_code}_{timezone.now().strftime("%Y%m%d")}.xlsx"'

#         wb = openpyxl.Workbook()
#         ws = wb.active
#         ws.title = self.get_report_title()[:30]

#         headers = [
#             str(_('Date Paid')), str(_('Student Name')), str(_('Class')),
#             str(_('Invoice #')), str(_('Payment Ref #')), str(_('Payment Method')),
#             str(_('Amount Paid'))
#         ]
#         ws.append(headers)

#         # Apply styling
#         header_font = Font(bold=True)
#         currency_format = '#,##0.00'
#         date_format = 'yyyy-mm-dd'

#         for cell in ws[1]:
#             cell.font = header_font

#         # Set column widths
#         column_widths = [12, 25, 15, 15, 15, 20, 15]
#         for i, width in enumerate(column_widths, 1):
#             ws.column_dimensions[get_column_letter(i)].width = width

#         for payment in queryset:
#             # Get invoice numbers from allocations
#             invoice_numbers = ', '.join([
#                 allocation.invoice.invoice_number
#                 for allocation in payment.allocations.all()
#                 if allocation.invoice
#             ]) or 'Unallocated'

#             ws.append([
#                 payment.payment_date,
#                 payment.student.get_full_name() if payment.student else 'N/A',
#                 payment.student.current_class_and_section if payment.student else 'N/A',
#                 invoice_numbers,
#                 payment.transaction_reference or 'N/A',
#                 payment.payment_method.name if payment.payment_method else 'N/A',
#                 payment.amount
#             ])

#             # Apply formatting
#             ws.cell(row=ws.max_row, column=1).number_format = date_format
#             ws.cell(row=ws.max_row, column=7).number_format = currency_format

#         output = BytesIO()
#         wb.save(output)
#         output.seek(0)
#         response.write(output.getvalue())
#         return response

#     def get_pdf_template_name(self):
#         return "reporting/pdf/payment_summary_report_pdf.html"

#     def export_to_pdf(self, queryset_for_export, request, pdf_context=None):
#         queryset = queryset_for_export or self.get_export_queryset()
#         if pdf_context is None:
#             pdf_context = {}

#         # Add report data and summary
#         pdf_context['report_items'] = queryset
#         report_data_summaries = self.get_report_data()
#         pdf_context.update(report_data_summaries)

#         pdf = render_to_pdf(self.get_pdf_template_name(), pdf_context)
#         if pdf:
#             response = HttpResponse(pdf, content_type='application/pdf')
#             filename = f"{self.report_code}_{timezone.now().strftime('%Y%m%d')}.pdf"
#             response['Content-Disposition'] = f'inline; filename="{filename}"'
#             return response
#         else:
#             logger.error(f"PDF generation failed for {self.report_code} ({self.get_report_title()})")
#             messages.error(request, _("An error occurred while generating the PDF report. Please try again."))
#             return redirect(request.path)


# = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = 
#
#      STUDENT LEDGER REPORT
#
# = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = 
# D:\school_fees_saas_v2\apps\reporting\views.py

# import csv
# import logging
# import datetime
# from decimal import Decimal
# from django.utils import timezone
# from django.shortcuts import get_object_or_404
# from django.db.models import Sum, F, Q, Value, CharField
# from django.db.models.functions import Coalesce, Concat
# from apps.common.mixins import TenantPermissionRequiredMixin, BaseReportViewMixin # Assuming your BaseReportViewMixin is here
# from apps.students.models import Student
# from apps.fees.models import Invoice, StudentConcession
# from apps.payments.models import Payment
# from .filters import StudentLedgerFilter

# from django.http import HttpResponse
# from django.views.generic import TemplateView, View
# from django.utils.translation import gettext_lazy as _
# from django.contrib import messages
# from apps.common.mixins import TenantPermissionRequiredMixin, BaseReportViewMixin
# from apps.schools.models import SchoolProfile
# from apps.common.utils import render_to_pdf

# logger = logging.getLogger(__name__)

class StudentLedgerReportView(TenantPermissionRequiredMixin, BaseReportViewMixin, TemplateView):
    """
    Handles the display of the HTML Student Ledger report page.
    """
    template_name = 'reporting/student_ledger_report.html'
    
    # Attributes for BaseReportViewMixin
    permission_required = 'reporting.view_student_ledger_report'
    report_title = _("Student Ledger")
    filter_form_class = StudentLedgerFilter
    export_csv_url_name = 'reporting:student_ledger_export_csv'
    export_pdf_url_name = 'reporting:student_ledger_export_pdf'

    def _get_ledger_data(self, request):
        """
        The core logic for fetching and processing all data for the student ledger.
        This helper method is used by the HTML view and all export views.
        Returns a dictionary containing all calculated data.
        """
        # Initialize the data structure to be returned
        data = {
            'student': None, 'transactions': [], 'opening_balance': Decimal('0.00'),
            'closing_balance': Decimal('0.00'), 'total_debits': Decimal('0.00'),
            'total_credits': Decimal('0.00'), 'start_date': None, 'end_date': None,
            'filter_form': self.filter_form_class(request.GET or None)
        }

        # Only proceed if the filter form is valid (i.e., a student is selected)
        if not data['filter_form'].is_valid():
            return data

        student = data['filter_form'].cleaned_data.get('student')
        if not student:
            return data # Return default empty data if no student is selected

        start_date = data['filter_form'].cleaned_data.get('start_date')
        end_date = data['filter_form'].cleaned_data.get('end_date') or timezone.now().date()
        
        data.update({
            'student': student,
            'start_date': start_date,
            'end_date': end_date,
        })

        # --- 1. Calculate Opening Balance ---
        opening_balance = Decimal('0.00')
        if start_date:
            # Sum of invoice balances before the start date
            # Calculate total amount as (subtotal_amount - total_concession_amount) - amount_paid
            ob_invoices = Invoice.objects.filter(
                student=student, issue_date__lt=start_date
            ).aggregate(balance=Coalesce(Sum(F('subtotal_amount') - F('total_concession_amount') - F('amount_paid')), Decimal('0.00')))['balance']

            # Sum of payments before the start date (these are credits, so we subtract)
            ob_payments = Payment.objects.filter(
                student=student, payment_date__date__lt=start_date, status='COMPLETED'
            ).aggregate(total=Coalesce(Sum('amount'), Decimal('0.00')))['total']

            # Note: We assume concessions are applied directly to invoices and reflected in their balance.
            # If you have standalone concessions, you would subtract them here as well.
            opening_balance = ob_invoices - ob_payments
        
        data['opening_balance'] = opening_balance
        
        # --- 2. Fetch Transactions for the Period ---
        transactions_data = []

        # Build date filters
        invoice_filters = {'student': student}
        payment_filters = {'student': student, 'status': 'COMPLETED'}
        concession_filters = {'student': student}

        if start_date and end_date:
            invoice_filters['issue_date__range'] = (start_date, end_date)
            payment_filters['payment_date__date__range'] = (start_date, end_date)
            concession_filters['granted_at__date__range'] = (start_date, end_date)
        elif start_date:
            invoice_filters['issue_date__gte'] = start_date
            payment_filters['payment_date__date__gte'] = start_date
            concession_filters['granted_at__date__gte'] = start_date
        elif end_date:
            invoice_filters['issue_date__lte'] = end_date
            payment_filters['payment_date__date__lte'] = end_date
            concession_filters['granted_at__date__lte'] = end_date

        # Invoices
        for inv in Invoice.objects.filter(**invoice_filters):
            # Calculate total amount (subtotal - concessions) since total_amount is a property, not a field
            total_amount = inv.subtotal_amount - inv.total_concession_amount
            transactions_data.append({
                'date': inv.issue_date,
                'description': f"Invoice #{inv.invoice_number_display}",
                'debit': total_amount,
                'credit': Decimal('0.00'),
                'type': 'Invoice',
                'reference_number': inv.invoice_number_display,
                'reference_link': inv.get_absolute_url() if hasattr(inv, 'get_absolute_url') else None
            })

        # Payments
        for pay in Payment.objects.filter(**payment_filters):
            payment_method_name = pay.payment_method.name if pay.payment_method else 'Unknown Method'
            transactions_data.append({
                'date': pay.payment_date.date(),
                'description': f"Payment Received ({payment_method_name})",
                'debit': Decimal('0.00'),
                'credit': pay.amount,
                'type': 'Payment',
                'reference_number': pay.receipt_number_display if hasattr(pay, 'receipt_number_display') else f"PAY-{pay.pk}",
                'reference_link': None  # Add payment detail URL if you have one
            })

        # Concessions (if they act as a credit outside of an invoice)
        # Note: StudentConcession doesn't have an amount field - concessions are typically applied to invoices
        # For now, we'll show concessions as informational entries without amounts
        for conc in StudentConcession.objects.filter(**concession_filters).select_related('concession_type'):
            transactions_data.append({
                'date': conc.granted_at.date(),
                'description': f"Concession Applied: {conc.concession_type.name}",
                'debit': Decimal('0.00'),
                'credit': Decimal('0.00'),
                'type': 'Concession',
                'reference_number': None,
                'reference_link': None
            })

        # --- 3. Sort transactions and calculate running balance ---
        sorted_transactions = sorted(transactions_data, key=lambda x: x['date'])
        
        running_balance = opening_balance
        total_debits_period = Decimal('0.00')
        total_credits_period = Decimal('0.00')
        
        for tx in sorted_transactions:
            debit = tx.get('debit') or Decimal('0.00')
            credit = tx.get('credit') or Decimal('0.00')
            running_balance += (debit - credit)
            tx['balance'] = running_balance
            total_debits_period += debit
            total_credits_period += credit

        data['transactions'] = sorted_transactions
        data['total_debits'] = total_debits_period
        data['total_credits'] = total_credits_period
        data['closing_balance'] = running_balance

        # Add additional context variables for the template
        data['report_period_start'] = start_date
        data['report_period_end'] = end_date

        return data

    def get_context_data(self, **kwargs):
        """
        Prepares context for the HTML page by calling the main data helper.
        """
        context = super().get_context_data(**kwargs)
        ledger_data = self._get_ledger_data(self.request)
        context.update(ledger_data)
        return context


class StudentLedgerExportCSVView(StudentLedgerReportView):
    """
    Handles the CSV export for the Student Ledger report.
    It inherits the data-gathering logic from StudentLedgerReportView.
    """
    http_method_names = ['get'] # This view only responds to GET requests

    def get(self, request, *args, **kwargs):
        # Get the report data
        report_data = self._get_ledger_data(request)
        student = report_data.get('student')
        transactions = report_data.get('transactions', [])

        # Create CSV response
        response = HttpResponse(content_type='text/csv')
        filename = f"Student_Ledger_{student.full_name.replace(' ', '_') if student else 'Unknown'}.csv"
        response['Content-Disposition'] = f'attachment; filename="{filename}"'

        import csv
        writer = csv.writer(response)

        # Write headers
        writer.writerow(['Student Ledger Report'])
        if student:
            writer.writerow([f'Student: {student.full_name}'])
            writer.writerow([f'Admission No: {getattr(student, "admission_number", "N/A")}'])
        writer.writerow([f'Transactions: {len(transactions)}'])
        writer.writerow([])

        # Write table headers
        writer.writerow(['Date', 'Description', 'Reference', 'Debit', 'Credit', 'Balance'])

        # Write transaction data
        for tx in transactions:
            writer.writerow([
                str(tx.get('date', '')),
                str(tx.get('description', '')),
                str(tx.get('reference_number', '')),
                str(tx.get('debit', '') if tx.get('debit') else ''),
                str(tx.get('credit', '') if tx.get('credit') else ''),
                str(tx.get('balance', ''))
            ])

        return response
    

class StudentLedgerExportExcelView(StudentLedgerReportView):
    """
    Handles Excel export for Student Ledger report.
    """
    def get(self, request, *args, **kwargs):
        # Simple test response first
        try:
            import openpyxl

            wb = openpyxl.Workbook()
            ws = wb.active
            ws.title = "Test"

            ws['A1'] = "Test Excel Export"
            ws['A2'] = "This is working"

            response = HttpResponse(
                content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )
            response['Content-Disposition'] = 'attachment; filename="test.xlsx"'

            wb.save(response)
            return response

        except ImportError:
            from django.http import HttpResponse
            return HttpResponse("Excel library not available", content_type='text/plain')


class StudentLedgerExportPDFView(StudentLedgerReportView):
    """
    Handles PDF export.
    """
    def get(self, request, *args, **kwargs):
        report_data = self._get_ledger_data(request)
        student = report_data.get('student')

        if not student:
            messages.error(request, "Please select a student before exporting to PDF.")
            return redirect('reporting:student_ledger_report')

        # Add any other context needed specifically by the PDF template
        report_data['report_title'] = f"Student Ledger for {student.full_name}"
        try:
            report_data['school_profile'] = SchoolProfile.objects.first()
        except:
            report_data['school_profile'] = None
        report_data['tenant'] = getattr(request, 'tenant', None)

        try:
            pdf = render_to_pdf('reporting/pdf/student_ledger_pdf.html', report_data)

            if pdf:
                response = HttpResponse(pdf, content_type='application/pdf')
                filename = f"Student_Ledger_{student.full_name.replace(' ', '_')}.pdf"
                response['Content-Disposition'] = f'inline; filename="{filename}"'
                return response
            else:
                messages.error(request, "Failed to generate PDF. Please try again.")
                return redirect('reporting:student_ledger_report')
        except Exception as e:
            messages.error(request, f"Error generating PDF: {str(e)}")
            return redirect('reporting:student_ledger_report')
            
        messages.error(request, "An error occurred while generating the PDF report.")
        return redirect('reporting:student_ledger_report')
    
# = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = 
#
#      FEES PROJECTION REPORT
#
# = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = 


# # Python Standard Library Imports
# import csv
# from decimal import Decimal
# from io import BytesIO
# import logging

# # Django Imports
# from django.http import HttpResponse
# from django.shortcuts import redirect
# from django.utils import timezone
# from django.utils.translation import gettext_lazy as _
# from django.views.generic import TemplateView
# from django.contrib import messages

# # Third-Party Imports
# try:
#     import openpyxl
#     from openpyxl.styles import Font, Alignment
#     from openpyxl.utils import get_column_letter
# except ImportError:
#     openpyxl = None

# # Local Application Imports
# from .mixins import TenantPermissionRequiredMixin, BaseReportViewMixin
# # No form is needed for this report, so no form import is necessary.
# from .models import Student, SchoolClass, AcademicYear, FeeStructure # Your app's models
# from .utils import render_to_pdf
# from schools.models import SchoolProfile

# logger = logging.getLogger(__name__)


class FeeProjectionReportView(TenantPermissionRequiredMixin, BaseReportViewMixin, TemplateView):
    """
    Handles the display of the HTML Fee Projection report page.
    """
    template_name = 'reporting/fee_projection_report.html'
    
    # Attributes for BaseReportViewMixin
    permission_required = 'reporting.view_fee_projection_report'
    report_title = _("Fee Projection Report")
    filter_form_class = None # No filters are used for this report
    export_csv_url_name = 'reporting:fee_projection_export_csv'
    export_pdf_url_name = 'reporting:fee_projection_export_pdf'
    export_excel_url_name = 'reporting:fee_projection_export_excel'

    def _get_fee_projection_data(self, request):
        """
        The core logic for calculating fee projections based on current enrollment and fee structures.
        """
        # 1. Initialize the data structure
        data = {
            'projected_items': [],
            'total_projected': Decimal('0.00'),
            'current_academic_year': None,
            'total_active_students': 0,
            'error_message': None,
            'filter_form': None # Explicitly set to None as we don't use a form
        }

        try:
            # 2. Get current academic year
            current_year = AcademicYear.objects.filter(is_current=True, is_active=True).first()
            if not current_year:
                current_year = AcademicYear.objects.filter(is_active=True).order_by('-start_date').first()

            if not current_year:
                data['error_message'] = _("No active academic year found. Cannot calculate projections.")
                return data
            
            data['current_academic_year'] = current_year
            data['total_active_students'] = Student.objects.filter(is_active=True).count()

            # 3. Iterate through active classes to calculate projections
            active_classes = SchoolClass.objects.filter(is_active=True).order_by('name')

            for class_obj in active_classes:
                student_count = Student.objects.filter(
                    current_class=class_obj,
                    is_active=True
                ).count()

                if student_count == 0:
                    continue

                # Get the total fee amount for one student in this class for the year
                # This assumes one fee structure per fee head per class.
                class_fee_structures = FeeStructure.objects.filter(
                    school_class=class_obj,
                    academic_year=current_year,
                    is_active=True
                )
                
                class_total_per_student = class_fee_structures.aggregate(
                    total=Coalesce(Sum('amount'), Decimal('0.00'))
                )['total']

                projected_for_class = class_total_per_student * student_count
                
                if projected_for_class > 0:
                    data['projected_items'].append({
                        'name': f"{class_obj.name}",
                        'student_count': student_count,
                        'amount_per_student': class_total_per_student,
                        'total_amount': projected_for_class
                    })
                    data['total_projected'] += projected_for_class
        
        except Exception as e:
            logger.error(f"Error calculating fee projections: {e}", exc_info=True)
            data['error_message'] = _("An unexpected error occurred while calculating projections.")

        return data

    def get_context_data(self, **kwargs):
        """Prepares context for the HTML page."""
        context = super().get_context_data(**kwargs)
        report_data = self._get_fee_projection_data(self.request)
        context.update(report_data)
        context['school_profile'] = SchoolProfile.objects.first()
        return context


class FeeProjectionExportCSVView(FeeProjectionReportView):
    """Handles CSV export for the Fee Projection Report."""
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        report_data = self._get_fee_projection_data(request)
        
        response = HttpResponse(content_type='text/csv')
        filename = f"Fee_Projection_{timezone.now().strftime('%Y%m%d')}.csv"
        response['Content-Disposition'] = f'attachment; filename="{filename}"'
        writer = csv.writer(response)
        
        writer.writerow(['Fee Projection Report'])
        if report_data.get('current_academic_year'):
            writer.writerow([f"For Academic Year: {report_data['current_academic_year'].name}"])
        writer.writerow([])
        
        headers = ['Class', 'Active Students', 'Fee Per Student', 'Total Projected Amount']
        writer.writerow(headers)

        for item in report_data.get('projected_items', []):
            writer.writerow([
                item['name'], item['student_count'],
                item['amount_per_student'], item['total_amount']
            ])
            
        writer.writerow([])
        writer.writerow(['', '', 'Grand Total Projected', report_data.get('total_projected')])
        
        return response


class FeeProjectionExportExcelView(FeeProjectionReportView):
    """Handles Excel export for the Fee Projection Report."""
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        if not openpyxl:
            messages.error(request, "Excel export library not available.")
            return redirect('reporting:fee_projection_report')

        report_data = self._get_fee_projection_data(request)
        
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "Fee Projection"
        
        # Styles
        title_font, header_font, bold_font = Font(bold=True, size=14), Font(bold=True), Font(bold=True)
        currency_format = '#,##0.00'

        # Headers
        ws['A1'] = "Fee Projection Report"
        ws['A1'].font = title_font
        if report_data.get('current_academic_year'):
            ws['A2'] = f"For Academic Year: {report_data['current_academic_year'].name}"
        
        headers = ['Class', 'Active Students', 'Fee Per Student', 'Total Projected Amount']
        ws.append([]) # Spacer row
        ws.append(headers)
        for cell in ws[4]: cell.font = header_font

        # Data
        for item in report_data.get('projected_items', []):
            ws.append([
                item['name'], item['student_count'],
                item['amount_per_student'], item['total_amount']
            ])
        
        # Total
        ws.append([])
        total_row = ws.max_row
        ws.cell(row=total_row, column=3, value="Grand Total Projected").font = bold_font
        ws.cell(row=total_row, column=4, value=report_data.get('total_projected')).font = bold_font

        # Formatting
        ws.column_dimensions['A'].width = 30
        ws.column_dimensions['B'].width = 18
        ws.column_dimensions['C'].width = 20
        ws.column_dimensions['D'].width = 25
        for col_letter in ['C', 'D']:
            for cell in ws[col_letter]:
                cell.number_format = currency_format

        response = HttpResponse(content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        filename = f"Fee_Projection_{timezone.now().strftime('%Y%m%d')}.xlsx"
        response['Content-Disposition'] = f'attachment; filename="{filename}"'
        wb.save(response)
        return response


class FeeProjectionExportPDFView(FeeProjectionReportView):
    """Handles PDF export for the Fee Projection Report."""
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        report_data = self._get_fee_projection_data(request)
        
        pdf_context = report_data.copy()
        pdf_context['report_title'] = "Fee Projection Report"
        if report_data.get('current_academic_year'):
            pdf_context['report_subtitle'] = f"For Academic Year: {report_data['current_academic_year'].name}"
            
        pdf_context.update({
            'school_profile': SchoolProfile.objects.first(),
            'tenant': getattr(request, 'tenant', None),
            'current_datetime': timezone.now()
        })
        
        try:
            pdf = render_to_pdf('reporting/pdf/fee_projection_report_pdf.html', pdf_context)
            if pdf:
                response = HttpResponse(pdf, content_type='application/pdf')
                filename = f"Fee_Projection_{timezone.now().strftime('%Y%m%d')}.pdf"
                response['Content-Disposition'] = f'inline; filename="{filename}"'
                return response
        except Exception as e:
            logger.error(f"Error generating Fee Projection PDF: {e}", exc_info=True)
            messages.error(request, "An error occurred while generating the PDF report.")
            
        return redirect('reporting:fee_projection_report')

# class FeeProjectionReportView(TenantLoginRequiredMixin, TenantPermissionRequiredMixin, BaseReportViewMixin, TemplateView):
#     template_name = 'reporting/fee_projection_report.html'
#     permission_required = 'reporting.view_fee_projection_report'

#     report_code = 'RPT_FEE_PROJECTION'
#     report_title_text = _("Fee Projection Report")
#     filter_form_class = None  # No filters for now

#     def get_report_data(self, processed_filter_form=None):
#         """Calculate fee projections based on current enrollment and fee structures."""
#         try:
#             from apps.students.models import Student
#             from apps.schools.models import SchoolClass, AcademicYear
#             from apps.fees.models import FeeStructure, FeeHead

#             # Get current academic year
#             current_year = AcademicYear.objects.filter(is_current=True).first()
#             if not current_year:
#                 current_year = AcademicYear.objects.order_by('-start_date').first()

#             projected_items = []
#             total_projected = Decimal('0.00')

#             # Get all active classes
#             classes = SchoolClass.objects.filter(is_active=True).order_by('name')

#             for class_obj in classes:
#                 # Count active students in this class
#                 student_count = Student.objects.filter(
#                     current_class=class_obj,
#                     is_active=True
#                 ).count()

#                 if student_count > 0:
#                     # Get fee structures for this class
#                     fee_structures = FeeStructure.objects.filter(
#                         school_class=class_obj,
#                         is_active=True,
#                         academic_year=current_year
#                     ).select_related('fee_head')

#                     class_total = Decimal('0.00')
#                     for fee_structure in fee_structures:
#                         fee_amount = fee_structure.amount * student_count
#                         class_total += fee_amount

#                     if class_total > 0:
#                         projected_items.append({
#                             'name': f"{class_obj.name} ({student_count} students)",
#                             'amount': class_total,
#                             'notes': f"Based on current enrollment and fee structures"
#                         })
#                         total_projected += class_total

#             return {
#                 'projected_items': projected_items,
#                 'total_projected': total_projected,
#                 'current_academic_year': current_year,
#                 'total_active_students': Student.objects.filter(is_active=True).count()
#             }

#         except Exception as e:
#             logger.error(f"Error calculating fee projections: {e}")
#             return {
#                 'projected_items': [],
#                 'total_projected': Decimal('0.00'),
#                 'error_message': 'Unable to calculate projections at this time.'
#             }

# = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = 
#
#      THE END OF REPORTS
#
# = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = 


