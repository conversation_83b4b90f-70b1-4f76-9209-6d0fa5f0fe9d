# Generated by Django 5.1.9 on 2025-07-14 18:30
# Production-ready migration with comprehensive error handling

from django.db import migrations, connection
import logging

logger = logging.getLogger(__name__)

def check_column_exists(table_name, column_name):
    """Check if a column exists in a table"""
    with connection.cursor() as cursor:
        cursor.execute("""
            SELECT EXISTS (
                SELECT 1 FROM information_schema.columns 
                WHERE table_name = %s AND column_name = %s
            );
        """, [table_name, column_name])
        return cursor.fetchone()[0]

def check_constraint_exists(constraint_name):
    """Check if a constraint exists"""
    with connection.cursor() as cursor:
        cursor.execute("""
            SELECT EXISTS (
                SELECT 1 FROM information_schema.table_constraints 
                WHERE constraint_name = %s
            );
        """, [constraint_name])
        return cursor.fetchone()[0]

def add_leavetype_fields_production(apps, schema_editor):
    """
    Production-ready function to add LeaveType fields safely
    Handles all edge cases and provides detailed logging
    """
    logger.info("Starting LeaveType fields migration...")
    
    try:
        with connection.cursor() as cursor:
            # 1. Add max_days_per_year_grant
            if not check_column_exists('hr_leavetype', 'max_days_per_year_grant'):
                logger.info("Adding max_days_per_year_grant column...")
                cursor.execute("""
                    ALTER TABLE hr_leavetype 
                    ADD COLUMN max_days_per_year_grant DECIMAL(5,2) NULL
                """)
                logger.info("✓ max_days_per_year_grant column added")
            else:
                logger.info("✓ max_days_per_year_grant column already exists")
            
            # 2. Add accrual_frequency
            if not check_column_exists('hr_leavetype', 'accrual_frequency'):
                logger.info("Adding accrual_frequency column...")
                cursor.execute("""
                    ALTER TABLE hr_leavetype 
                    ADD COLUMN accrual_frequency VARCHAR(20) DEFAULT 'NONE' NOT NULL
                """)
                
                # Add constraint if it doesn't exist
                constraint_name = 'hr_leavetype_accrual_frequency_check'
                if not check_constraint_exists(constraint_name):
                    cursor.execute(f"""
                        ALTER TABLE hr_leavetype 
                        ADD CONSTRAINT {constraint_name}
                        CHECK (accrual_frequency IN ('NONE', 'MONTHLY', 'ANNUALLY'))
                    """)
                logger.info("✓ accrual_frequency column added with constraint")
            else:
                logger.info("✓ accrual_frequency column already exists")
            
            # 3. Add accrual_rate
            if not check_column_exists('hr_leavetype', 'accrual_rate'):
                logger.info("Adding accrual_rate column...")
                cursor.execute("""
                    ALTER TABLE hr_leavetype 
                    ADD COLUMN accrual_rate DECIMAL(5,2) DEFAULT 0.00 NOT NULL
                """)
                logger.info("✓ accrual_rate column added")
            else:
                logger.info("✓ accrual_rate column already exists")
            
            # 4. Add max_accrual_balance
            if not check_column_exists('hr_leavetype', 'max_accrual_balance'):
                logger.info("Adding max_accrual_balance column...")
                cursor.execute("""
                    ALTER TABLE hr_leavetype 
                    ADD COLUMN max_accrual_balance INTEGER NULL
                """)
                
                # Add constraint if it doesn't exist
                constraint_name = 'hr_leavetype_max_accrual_balance_check'
                if not check_constraint_exists(constraint_name):
                    cursor.execute(f"""
                        ALTER TABLE hr_leavetype 
                        ADD CONSTRAINT {constraint_name}
                        CHECK (max_accrual_balance >= 0)
                    """)
                logger.info("✓ max_accrual_balance column added with constraint")
            else:
                logger.info("✓ max_accrual_balance column already exists")
            
            # 5. Add prorate_accrual
            if not check_column_exists('hr_leavetype', 'prorate_accrual'):
                logger.info("Adding prorate_accrual column...")
                cursor.execute("""
                    ALTER TABLE hr_leavetype 
                    ADD COLUMN prorate_accrual BOOLEAN DEFAULT TRUE NOT NULL
                """)
                logger.info("✓ prorate_accrual column added")
            else:
                logger.info("✓ prorate_accrual column already exists")
        
        logger.info("LeaveType fields migration completed successfully!")
        
    except Exception as e:
        logger.error(f"Error during LeaveType fields migration: {str(e)}")
        raise

def reverse_leavetype_fields_production(apps, schema_editor):
    """
    Production-ready reverse function
    """
    logger.info("Reversing LeaveType fields migration...")
    
    try:
        with connection.cursor() as cursor:
            # Drop constraints first (ignore errors if they don't exist)
            constraints = [
                'hr_leavetype_accrual_frequency_check',
                'hr_leavetype_max_accrual_balance_check'
            ]
            
            for constraint in constraints:
                try:
                    cursor.execute(f"""
                        ALTER TABLE hr_leavetype 
                        DROP CONSTRAINT IF EXISTS {constraint}
                    """)
                    logger.info(f"✓ Dropped constraint {constraint}")
                except Exception as e:
                    logger.warning(f"Could not drop constraint {constraint}: {e}")
            
            # Drop columns (ignore errors if they don't exist)
            columns = [
                'max_days_per_year_grant',
                'accrual_frequency', 
                'accrual_rate',
                'max_accrual_balance',
                'prorate_accrual'
            ]
            
            for column in columns:
                try:
                    cursor.execute(f"""
                        ALTER TABLE hr_leavetype 
                        DROP COLUMN IF EXISTS {column}
                    """)
                    logger.info(f"✓ Dropped column {column}")
                except Exception as e:
                    logger.warning(f"Could not drop column {column}: {e}")
        
        logger.info("LeaveType fields reverse migration completed!")
        
    except Exception as e:
        logger.error(f"Error during reverse migration: {str(e)}")
        raise

class Migration(migrations.Migration):
    
    dependencies = [
        ('hr', '0011_alter_payslip_adjustments'),
    ]

    operations = [
        migrations.RunPython(
            add_leavetype_fields_production,
            reverse_leavetype_fields_production,
            elidable=True
        ),
    ]
