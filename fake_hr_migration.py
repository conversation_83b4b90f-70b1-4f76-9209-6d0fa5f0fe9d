#!/usr/bin/env python
"""
Fake HR Migration
Marks the problematic HR migration as applied since we fixed the database manually

Usage: python fake_hr_migration.py
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from django.core.management import call_command
from django_tenants.utils import schema_context
from apps.tenants.models import School
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def fake_hr_migration():
    """Fake the problematic HR migration for all tenants"""
    
    logger.info("=== FAKING HR MIGRATION ===")
    
    # Get all tenants
    tenants = School.objects.all()
    
    for tenant in tenants:
        logger.info(f"Faking HR migration for: {tenant.schema_name}")
        
        try:
            with schema_context(tenant.schema_name):
                # Fake the problematic migration
                call_command('migrate', 'hr', '0022', '--fake')
            logger.info(f"✅ Faked HR migration for {tenant.schema_name}")
        except Exception as e:
            logger.error(f"❌ Failed to fake HR migration for {tenant.schema_name}: {e}")

def apply_remaining_migrations():
    """Apply remaining migrations after faking the problematic one"""
    
    logger.info("=== APPLYING REMAINING MIGRATIONS ===")
    
    # Get all tenants
    tenants = School.objects.all()
    
    success_count = 0
    error_count = 0
    
    for tenant in tenants:
        logger.info(f"Applying remaining migrations for: {tenant.schema_name}")
        
        try:
            with schema_context(tenant.schema_name):
                call_command('migrate')
            logger.info(f"✅ Applied remaining migrations for {tenant.schema_name}")
            success_count += 1
        except Exception as e:
            logger.error(f"❌ Failed to apply migrations for {tenant.schema_name}: {e}")
            error_count += 1
    
    logger.info(f"\n=== MIGRATION SUMMARY ===")
    logger.info(f"✅ Successful: {success_count}")
    logger.info(f"❌ Failed: {error_count}")

def main():
    """Main function"""
    logger.info("=== FIXING HR MIGRATION ISSUES ===")
    
    try:
        # First fake the problematic migration
        fake_hr_migration()
        
        # Then apply remaining migrations
        apply_remaining_migrations()
        
        logger.info("✅ HR migration issues resolved!")
        return True
        
    except Exception as e:
        logger.error(f"Failed to fix HR migration issues: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
