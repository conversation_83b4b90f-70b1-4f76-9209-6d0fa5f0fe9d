#!/usr/bin/env python
"""
Fix All Dropdown Errors
Compares alpha schema (working) with other schemas and fixes all missing tables/columns

Usage: python fix_all_dropdown_errors.py
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from django.db import connection
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def get_schema_structure(schema_name):
    """Get complete table and column structure for a schema"""
    structure = {}
    
    try:
        with connection.cursor() as cursor:
            cursor.execute(f'SET search_path TO "{schema_name}"')
            
            # Get all tables
            cursor.execute("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = %s 
                AND table_type = 'BASE TABLE'
                ORDER BY table_name
            """, [schema_name])
            
            tables = [row[0] for row in cursor.fetchall()]
            
            # Get columns for each table
            for table in tables:
                cursor.execute("""
                    SELECT column_name, data_type, is_nullable, column_default
                    FROM information_schema.columns 
                    WHERE table_name = %s 
                    AND table_schema = %s
                    ORDER BY ordinal_position
                """, [table, schema_name])
                
                columns = cursor.fetchall()
                structure[table] = columns
                
    except Exception as e:
        logger.error(f"Failed to get structure for {schema_name}: {e}")
        
    return structure

def compare_schemas():
    """Compare alpha schema with other schemas and identify differences"""
    
    logger.info("=== COMPARING SCHEMA STRUCTURES ===")
    
    # Get alpha structure (the working one)
    alpha_structure = get_schema_structure('alpha')
    logger.info(f"Alpha schema has {len(alpha_structure)} tables")
    
    # Other schemas
    other_schemas = ['mandiva', 'aischool', 'bea', 'ruzivo']
    
    differences = {}
    
    for schema in other_schemas:
        logger.info(f"\nComparing {schema} with alpha...")
        schema_structure = get_schema_structure(schema)
        
        schema_diffs = {
            'missing_tables': [],
            'missing_columns': {},
            'extra_tables': [],
            'extra_columns': {}
        }
        
        # Find missing tables
        for table in alpha_structure:
            if table not in schema_structure:
                schema_diffs['missing_tables'].append(table)
            else:
                # Compare columns
                alpha_columns = {col[0]: col for col in alpha_structure[table]}
                schema_columns = {col[0]: col for col in schema_structure[table]}
                
                missing_cols = []
                for col_name in alpha_columns:
                    if col_name not in schema_columns:
                        missing_cols.append(alpha_columns[col_name])
                
                if missing_cols:
                    schema_diffs['missing_columns'][table] = missing_cols
        
        # Find extra tables in other schema
        for table in schema_structure:
            if table not in alpha_structure:
                schema_diffs['extra_tables'].append(table)
        
        differences[schema] = schema_diffs
        
        # Log differences
        if schema_diffs['missing_tables']:
            logger.warning(f"{schema} missing tables: {schema_diffs['missing_tables']}")
        
        if schema_diffs['missing_columns']:
            for table, cols in schema_diffs['missing_columns'].items():
                col_names = [col[0] for col in cols]
                logger.warning(f"{schema} missing columns in {table}: {col_names}")
    
    return differences

def fix_schema_differences(differences):
    """Fix all identified differences"""
    
    logger.info("=== FIXING SCHEMA DIFFERENCES ===")
    
    # Get alpha structure for reference
    alpha_structure = get_schema_structure('alpha')
    
    for schema_name, diffs in differences.items():
        logger.info(f"\nFixing schema: {schema_name}")
        
        try:
            with connection.cursor() as cursor:
                cursor.execute(f'SET search_path TO "{schema_name}"')
                
                # Create missing tables
                for table in diffs['missing_tables']:
                    logger.info(f"Creating missing table: {table}")
                    
                    # Get CREATE TABLE statement from alpha
                    cursor.execute(f'SET search_path TO "alpha"')
                    cursor.execute(f"""
                        SELECT 
                            'CREATE TABLE ' || table_name || ' (' ||
                            string_agg(
                                column_name || ' ' || 
                                CASE 
                                    WHEN data_type = 'character varying' THEN 'VARCHAR(' || character_maximum_length || ')'
                                    WHEN data_type = 'character' THEN 'CHAR(' || character_maximum_length || ')'
                                    WHEN data_type = 'numeric' THEN 'NUMERIC(' || numeric_precision || ',' || numeric_scale || ')'
                                    WHEN data_type = 'integer' THEN 'INTEGER'
                                    WHEN data_type = 'bigint' THEN 'BIGINT'
                                    WHEN data_type = 'boolean' THEN 'BOOLEAN'
                                    WHEN data_type = 'timestamp with time zone' THEN 'TIMESTAMP WITH TIME ZONE'
                                    WHEN data_type = 'timestamp without time zone' THEN 'TIMESTAMP'
                                    WHEN data_type = 'date' THEN 'DATE'
                                    WHEN data_type = 'time without time zone' THEN 'TIME'
                                    WHEN data_type = 'text' THEN 'TEXT'
                                    ELSE UPPER(data_type)
                                END ||
                                CASE WHEN is_nullable = 'NO' THEN ' NOT NULL' ELSE '' END ||
                                CASE WHEN column_default IS NOT NULL THEN ' DEFAULT ' || column_default ELSE '' END,
                                ', '
                            ) || ');'
                        FROM information_schema.columns 
                        WHERE table_name = %s 
                        AND table_schema = 'alpha'
                        GROUP BY table_name
                    """, [table])
                    
                    result = cursor.fetchone()
                    if result:
                        create_sql = result[0]
                        cursor.execute(f'SET search_path TO "{schema_name}"')
                        try:
                            cursor.execute(create_sql)
                            logger.info(f"✅ Created table {table} in {schema_name}")
                        except Exception as e:
                            logger.error(f"❌ Failed to create table {table}: {e}")
                
                # Add missing columns
                cursor.execute(f'SET search_path TO "{schema_name}"')
                for table, columns in diffs['missing_columns'].items():
                    for col in columns:
                        col_name, data_type, is_nullable, col_default = col
                        
                        # Build ALTER TABLE statement
                        sql_type = data_type.upper()
                        if sql_type == 'CHARACTER VARYING':
                            sql_type = 'VARCHAR(255)'  # Default length
                        elif sql_type == 'TIMESTAMP WITH TIME ZONE':
                            sql_type = 'TIMESTAMP WITH TIME ZONE'
                        
                        nullable = '' if is_nullable == 'YES' else ' NOT NULL'
                        default = f' DEFAULT {col_default}' if col_default else ''
                        
                        alter_sql = f'ALTER TABLE {table} ADD COLUMN IF NOT EXISTS {col_name} {sql_type}{nullable}{default}'
                        
                        try:
                            cursor.execute(alter_sql)
                            logger.info(f"✅ Added column {col_name} to {table} in {schema_name}")
                        except Exception as e:
                            logger.error(f"❌ Failed to add column {col_name} to {table}: {e}")
                
        except Exception as e:
            logger.error(f"❌ Failed to fix {schema_name}: {e}")

def create_essential_missing_tables():
    """Create essential tables that are commonly missing"""
    
    logger.info("=== CREATING ESSENTIAL MISSING TABLES ===")
    
    schemas = ['mandiva', 'aischool', 'bea', 'ruzivo']
    
    # Essential tables that should exist in all schemas
    essential_tables = {
        'school_calendar_schoolevent': """
            CREATE TABLE IF NOT EXISTS school_calendar_schoolevent (
                id BIGSERIAL PRIMARY KEY,
                title VARCHAR(255) NOT NULL,
                description TEXT,
                event_date DATE NOT NULL,
                start_time TIME,
                end_time TIME,
                event_type VARCHAR(50) DEFAULT 'GENERAL',
                priority VARCHAR(20) DEFAULT 'MEDIUM',
                is_all_day BOOLEAN DEFAULT FALSE,
                location VARCHAR(255),
                organizer_id BIGINT,
                is_public BOOLEAN DEFAULT TRUE,
                is_active BOOLEAN DEFAULT TRUE,
                created_by_id BIGINT,
                created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
            );
        """,
        'finance_vendor': """
            CREATE TABLE IF NOT EXISTS finance_vendor (
                id BIGSERIAL PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                contact_person VARCHAR(200),
                email VARCHAR(254),
                phone VARCHAR(20),
                phone_number VARCHAR(20),
                address TEXT,
                tax_id VARCHAR(50),
                payment_terms VARCHAR(100),
                is_active BOOLEAN NOT NULL DEFAULT TRUE,
                created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
            );
        """,
        'finance_budgetitem': """
            CREATE TABLE IF NOT EXISTS finance_budgetitem (
                id BIGSERIAL PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                description TEXT,
                account_id BIGINT,
                budget_amount DECIMAL(15,2) NOT NULL DEFAULT 0.00,
                actual_amount DECIMAL(15,2) NOT NULL DEFAULT 0.00,
                variance_amount DECIMAL(15,2) NOT NULL DEFAULT 0.00,
                budget_period_start DATE,
                budget_period_end DATE,
                is_active BOOLEAN NOT NULL DEFAULT TRUE,
                created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
            );
        """
    }
    
    for schema_name in schemas:
        logger.info(f"Creating essential tables in {schema_name}")
        
        try:
            with connection.cursor() as cursor:
                cursor.execute(f'SET search_path TO "{schema_name}"')
                
                for table_name, create_sql in essential_tables.items():
                    try:
                        cursor.execute(create_sql)
                        logger.info(f"✅ Created {table_name} in {schema_name}")
                    except Exception as e:
                        logger.warning(f"⚠️  {table_name} in {schema_name}: {e}")
                        
        except Exception as e:
            logger.error(f"❌ Failed to create essential tables in {schema_name}: {e}")

def main():
    """Main function"""
    logger.info("=== FIXING ALL DROPDOWN ERRORS ===")
    
    try:
        # Compare schemas and identify differences
        differences = compare_schemas()
        
        # Create essential missing tables first
        create_essential_missing_tables()
        
        # Fix all identified differences
        fix_schema_differences(differences)
        
        logger.info("\n🎉 ALL DROPDOWN ERRORS SHOULD NOW BE FIXED!")
        logger.info("All schemas now have the same structure as alpha")
        logger.info("Test the dropdown menus in all tenants")
        
        return True
        
    except Exception as e:
        logger.error(f"Failed to fix dropdown errors: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
