{% extends "tenant_base.html" %}
{% load humanize static core_tags %}

{% block title %}{{ view_title|default:"Payment Method Analysis Report" }}{% endblock %}

{% block content %}
<div class="container-fluid mt-3">
    {% with report_icon_class="bi bi-credit-card" %}
        {% include "partials/_report_header.html" %}
    {% endwith %}

    {% include "partials/_messages.html" %}
    {% include "partials/_report_filter_form.html" with filter_form=filter_form report_code=report_code specific_filter_title="Payment Method Analysis Filters" %}

    <!-- Payment Method Overview -->
    <div class="card mb-4">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="bi bi-graph-up me-2"></i>Payment Method Overview</h5>
        </div>
        <div class="card-body">
            <div class="row mb-3">
                <div class="col-md-12">
                    <p>
                        <strong>Date Range:</strong> {{ start_date }} to {{ end_date }} | 
                        <strong>Analysis Type:</strong> {{ analysis_type|title }} | 
                        <strong>Failed Payments:</strong> {% if include_failed %}Included{% else %}Excluded{% endif %}
                    </p>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-3">
                    <div class="text-center">
                        <h6 class="text-muted">Methods Used</h6>
                        <p class="mb-0 fw-bold text-primary fs-4">{{ efficiency_metrics.total_methods_used }}</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h6 class="text-muted">Processing Fees</h6>
                        <p class="mb-0 fw-bold text-warning fs-4">{{ efficiency_metrics.total_processing_fees|currency }}</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h6 class="text-muted">Success Rate</h6>
                        <p class="mb-0 fw-bold text-success fs-4">{{ efficiency_metrics.avg_success_rate|floatformat:1 }}%</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h6 class="text-muted">Digital Adoption</h6>
                        <p class="mb-0 fw-bold text-info fs-4">{{ efficiency_metrics.digital_adoption|floatformat:1 }}%</p>
                    </div>
                </div>
            </div>
            
            <!-- Digital vs Traditional Usage -->
            <div class="mt-3">
                <h6>Payment Method Distribution</h6>
                <div class="progress" style="height: 25px;">
                    <div class="progress-bar bg-info" role="progressbar" style="width: {{ efficiency_metrics.digital_adoption }}%">
                        Digital: {{ efficiency_metrics.digital_adoption|floatformat:1 }}%
                    </div>
                    <div class="progress-bar bg-secondary" role="progressbar" style="width: {{ efficiency_metrics.traditional_usage }}%">
                        Traditional: {{ efficiency_metrics.traditional_usage|floatformat:1 }}%
                    </div>
                </div>
                <small class="text-muted">
                    Most popular: {{ efficiency_metrics.most_popular_method }} | 
                    Most efficient: {{ efficiency_metrics.most_efficient_method }}
                </small>
            </div>
        </div>
    </div>

    <!-- Key Performance Indicators -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="bi bi-trophy me-2"></i>Top Performers</h5>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled">
                        <li><i class="bi bi-star text-warning me-2"></i>
                            <strong>Most Popular:</strong> {{ efficiency_metrics.most_popular_method }}
                        </li>
                        <li><i class="bi bi-cash-stack text-success me-2"></i>
                            <strong>Highest Value:</strong> {{ efficiency_metrics.highest_value_method }}
                        </li>
                        <li><i class="bi bi-check-circle text-success me-2"></i>
                            <strong>Most Reliable:</strong> {{ efficiency_metrics.most_efficient_method }}
                        </li>
                        <li><i class="bi bi-piggy-bank text-info me-2"></i>
                            <strong>Lowest Cost:</strong> {{ efficiency_metrics.lowest_cost_method }}
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="bi bi-speedometer me-2"></i>Efficiency Metrics</h5>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled">
                        <li><i class="bi bi-percent text-primary me-2"></i>
                            <strong>Average Success Rate:</strong> {{ efficiency_metrics.avg_success_rate|floatformat:1 }}%
                        </li>
                        <li><i class="bi bi-cash text-warning me-2"></i>
                            <strong>Total Processing Fees:</strong> {{ efficiency_metrics.total_processing_fees|currency }}
                        </li>
                        <li><i class="bi bi-phone text-info me-2"></i>
                            <strong>Digital Adoption:</strong> {{ efficiency_metrics.digital_adoption|floatformat:1 }}%
                        </li>
                        <li><i class="bi bi-building text-secondary me-2"></i>
                            <strong>Traditional Usage:</strong> {{ efficiency_metrics.traditional_usage|floatformat:1 }}%
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Payment Method Analysis -->
    {% if method_analysis %}
    <div class="card mb-4">
        <div class="card-header bg-light d-flex justify-content-between align-items-center">
            <h5 class="mb-0"><i class="bi bi-table me-2"></i>Payment Method Analysis</h5>
            <span class="badge bg-primary">{{ method_analysis|length }} methods</span>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-striped table-hover mb-0">
                    <thead class="table-dark">
                        <tr>
                            <th>Payment Method</th>
                            <th class="text-center">Category</th>
                            <th class="text-end">Total Amount</th>
                            <th class="text-center">Transactions</th>
                            <th class="text-center">Success Rate</th>
                            <th class="text-center">Market Share</th>
                            <th class="text-end">Processing Fees</th>
                            <th class="text-center">Students</th>
                            <th class="text-center">Convenience</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for method in method_analysis %}
                        <tr>
                            <td>
                                <strong>{{ method.method_name }}</strong>
                                <br><small class="text-muted">{{ method.payment_method }}</small>
                            </td>
                            <td class="text-center">
                                <span class="badge {% if method.category == 'Digital' %}bg-info{% elif method.category == 'Card Payment' %}bg-success{% elif method.category == 'Banking' %}bg-primary{% else %}bg-secondary{% endif %}">
                                    {{ method.category }}
                                </span>
                            </td>
                            <td class="text-end">
                                <span class="fw-bold text-primary">{{ method.total_amount|currency }}</span>
                                <br><small class="text-muted">Avg: {{ method.avg_amount|currency }}</small>
                            </td>
                            <td class="text-center">
                                <span class="fw-bold">{{ method.transaction_count }}</span>
                                <br><small class="text-success">{{ method.successful_count }} success</small>
                                {% if method.failed_count > 0 %}
                                <br><small class="text-danger">{{ method.failed_count }} failed</small>
                                {% endif %}
                            </td>
                            <td class="text-center">
                                <div class="progress" style="height: 20px; width: 80px;">
                                    <div class="progress-bar {% if method.success_rate >= 95 %}bg-success{% elif method.success_rate >= 85 %}bg-warning{% else %}bg-danger{% endif %}" 
                                         role="progressbar" style="width: {{ method.success_rate }}%">
                                    </div>
                                </div>
                                <small class="text-muted">{{ method.success_rate|floatformat:1 }}%</small>
                            </td>
                            <td class="text-center">
                                <div class="progress" style="height: 20px; width: 80px;">
                                    <div class="progress-bar bg-info" role="progressbar" style="width: {{ method.market_share }}%">
                                    </div>
                                </div>
                                <small class="text-muted">{{ method.market_share|floatformat:1 }}%</small>
                            </td>
                            <td class="text-end">
                                <span class="text-warning fw-bold">{{ method.processing_fees|currency }}</span>
                                <br><small class="text-success">Net: {{ method.net_amount|currency }}</small>
                            </td>
                            <td class="text-center">
                                <span class="badge bg-info">{{ method.unique_students }}</span>
                            </td>
                            <td class="text-center">
                                {% for i in "12345" %}
                                <i class="bi bi-star{% if forloop.counter <= method.convenience_score %}-fill text-warning{% else %} text-muted{% endif %}"></i>
                                {% endfor %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Trend Analysis -->
    {% if trend_data and analysis_type == 'trends' %}
    <div class="card mb-4">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="bi bi-graph-up me-2"></i>Payment Method Trends</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Period</th>
                            {% for method in trend_data.0.methods %}
                            <th class="text-end">{{ method.method_name }}</th>
                            {% endfor %}
                        </tr>
                    </thead>
                    <tbody>
                        {% for period in trend_data %}
                        <tr>
                            <td><strong>{{ period.period }}</strong></td>
                            {% for method in period.methods %}
                            <td class="text-end">
                                {{ method.amount|currency }}
                                <br><small class="text-muted">{{ method.count }} txns</small>
                            </td>
                            {% endfor %}
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Insights and Recommendations -->
    {% if method_analysis %}
    <div class="card">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="bi bi-lightbulb me-2"></i>Insights & Recommendations</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h6>Key Insights</h6>
                    <ul class="list-unstyled">
                        {% if efficiency_metrics.digital_adoption > 50 %}
                        <li><i class="bi bi-check-circle text-success me-2"></i>
                            Strong digital payment adoption ({{ efficiency_metrics.digital_adoption|floatformat:1 }}%)
                        </li>
                        {% else %}
                        <li><i class="bi bi-exclamation-triangle text-warning me-2"></i>
                            Low digital adoption - opportunity for improvement
                        </li>
                        {% endif %}
                        
                        {% if efficiency_metrics.avg_success_rate > 95 %}
                        <li><i class="bi bi-check-circle text-success me-2"></i>
                            Excellent payment success rate ({{ efficiency_metrics.avg_success_rate|floatformat:1 }}%)
                        </li>
                        {% elif efficiency_metrics.avg_success_rate > 85 %}
                        <li><i class="bi bi-info-circle text-info me-2"></i>
                            Good payment success rate with room for improvement
                        </li>
                        {% else %}
                        <li><i class="bi bi-exclamation-triangle text-warning me-2"></i>
                            Payment success rate needs attention
                        </li>
                        {% endif %}
                        
                        <li><i class="bi bi-cash text-warning me-2"></i>
                            Processing fees: {{ efficiency_metrics.total_processing_fees|currency }}
                        </li>
                        
                        <li><i class="bi bi-people text-info me-2"></i>
                            {{ efficiency_metrics.total_methods_used }} payment methods actively used
                        </li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6>Recommendations</h6>
                    <ul class="list-unstyled">
                        {% if efficiency_metrics.digital_adoption < 50 %}
                        <li><i class="bi bi-arrow-right text-primary me-2"></i>
                            Promote digital payment methods to reduce processing costs
                        </li>
                        {% endif %}
                        
                        {% if efficiency_metrics.avg_success_rate < 95 %}
                        <li><i class="bi bi-arrow-right text-primary me-2"></i>
                            Investigate and address payment failures
                        </li>
                        {% endif %}
                        
                        <li><i class="bi bi-arrow-right text-success me-2"></i>
                            Focus on promoting {{ efficiency_metrics.most_efficient_method }} for reliability
                        </li>
                        
                        <li><i class="bi bi-arrow-right text-info me-2"></i>
                            Consider {{ efficiency_metrics.lowest_cost_method }} to reduce processing fees
                        </li>
                        
                        {% if efficiency_metrics.total_processing_fees > 1000 %}
                        <li><i class="bi bi-arrow-right text-warning me-2"></i>
                            Review processing fee structure for cost optimization
                        </li>
                        {% endif %}
                    </ul>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- No Data Message -->
    {% if not method_analysis %}
    <div class="card">
        <div class="card-body text-center py-5">
            <i class="bi bi-credit-card display-1 text-muted mb-3"></i>
            <h4 class="text-muted">No Payment Method Data Available</h4>
            <p class="text-muted">No payment method data found for the selected criteria.</p>
            <div class="mt-3">
                <a href="{% url 'reporting:payment_method_analysis_report' %}" class="btn btn-outline-primary me-2">
                    <i class="bi bi-arrow-clockwise me-2"></i>Reset Filters
                </a>
            </div>
        </div>
    </div>
    {% endif %}

</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Animate progress bars
    $('.progress-bar').each(function() {
        var $this = $(this);
        var width = $this.css('width');
        $this.css('width', '0%').animate({width: width}, 1500);
    });
    
    // Add click handlers for method rows
    $('tbody tr').on('click', function() {
        $(this).toggleClass('table-info');
    });
    
    // Highlight low performing methods
    $('tbody tr').each(function() {
        var successRate = parseFloat($(this).find('.progress-bar').attr('style').match(/width: ([\d.]+)%/)[1]);
        if (successRate < 85) {
            $(this).addClass('table-warning');
        }
    });
});
</script>
{% endblock %}
