{% extends "tenant_base.html" %}
{% load static i18n widget_tweaks %}

{% block tenant_page_title %}{{ view_title }}{% endblock %}

{% block tenant_specific_content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <h1 class="h3 mb-3">{{ view_title }}</h1>
            <div class="card shadow-sm">
                <div class="card-body p-4">
                    <form method="post">
                        {% csrf_token %}
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.staff_member.id_for_label }}" class="form-label required">Staff Member</label>
                                {% render_field form.staff_member class+="form-select" %}
                                {% for error in form.staff_member.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.grade.id_for_label }}" class="form-label required">Salary Grade</label>
                                {% render_field form.grade class+="form-select" %}
                                {% for error in form.grade.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.basic_salary.id_for_label }}" class="form-label required">Basic Salary</label>
                                {% render_field form.basic_salary class+="form-control" %}
                                {% for error in form.basic_salary.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.effective_from.id_for_label }}" class="form-label required">Effective From</label>
                                {% render_field form.effective_from class+="form-control" %}
                                {% for error in form.effective_from.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                            </div>
                        </div>
                        <div class="d-flex justify-content-end mt-4">
                            <a href="{% url 'hr:staffsalary_list' %}" class="btn btn-secondary me-2">Cancel</a>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-save-fill me-1"></i> {% if object %}Update Salary{% else %}Save Salary{% endif %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}














{% comment %} {# D:\school_fees_saas_v2\apps\hr\templates\hr\staffsalary_form.html #}
{% extends "tenant_base.html" %}
{% load static i18n widget_tweaks %}

{% block tenant_page_title %}{{ view_title }}{% endblock %}

{% block tenant_specific_content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <h1 class="h3 mb-4">{{ view_title }}</h1>
            <div class="card shadow-sm">
                <div class="card-body p-4">
                    <form method="post">
                        {% csrf_token %}
                        {% if form.non_field_errors %}<div class="alert alert-danger">{{ form.non_field_errors }}</div>{% endif %}

                        <div class="mb-3">
                            <label for="{{ form.grade.id_for_label }}" class="form-label">{{ form.grade.label }}</label>
                            {% render_field form.grade class="form-select" %}
                            <div class="form-text text-muted small">{{ form.grade.help_text }}</div>
                            {% for error in form.grade.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                        </div>
                        <div class="mb-3">
                            <label for="{{ form.basic_salary.id_for_label }}" class="form-label required">{{ form.basic_salary.label }}</label>
                            {% render_field form.basic_salary class="form-control" %}
                            {% for error in form.basic_salary.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                        </div>
                        <div class="mb-3">
                            <label for="{{ form.effective_date.id_for_label }}" class="form-label required">{{ form.effective_date.label }}</label>
                            {% render_field form.effective_date class="form-control" %}
                            {% for error in form.effective_date.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                        </div>
                        
                        <div class="d-flex justify-content-end gap-2 mt-4">
                            <a href="{% url 'schools:staff_detail' pk=object.staff_user.pk %}" class="btn btn-secondary">Cancel</a>
                            <button type="submit" class="btn btn-primary">Update Salary Details</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} {% endcomment %}



