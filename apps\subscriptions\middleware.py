# D:\school_fees_saas_v2\apps\subscriptions\middleware.py
from django.shortcuts import redirect
from django.urls import reverse, NoReverseMatch, resolve, reverse_lazy
from django.utils.deprecation import MiddlewareMixin
from django.utils.translation import gettext_lazy as _
from django.contrib import messages
from django.conf import settings
from django_tenants.utils import get_public_schema_name, get_tenant_model

from .models import Subscription # Import your Subscription model

import logging
logger = logging.getLogger(__name__)

# Define a list of URL names or path prefixes that should ALWAYS be accessible 
# for a tenant, even if their subscription is not usable.
# This allows them to manage their subscription or log out.
TENANT_SUBSCRIPTION_MANAGEMENT_URL_NAMES = [
    'subscriptions:subscription_details',       # To view status
    'subscriptions:update_payment_method',    # To update card
    'subscriptions:initiate_checkout',        # To re-subscribe or choose new plan
    'subscriptions:manage_subscription_redirect', # The middleware's own redirect target
    # Add URLs for cancelling, reactivating if you have specific views for those
]

# Add URLs for tenant user logout - these must always be accessible
TENANT_USER_LOGOUT_URL_NAMES = [
    'schools:staff_logout_view',    # Staff logout
    'parent_portal:logout',       # Parent logout
    # Add other tenant-specific logout URLs if you have them
]

# URLs that are part of the public site or platform admin, always allowed.
# This can be more robustly handled by checking schema or if request.tenant is None.
PUBLIC_ALLOWED_PREFIXES = [
    '/static/',
    '/media/',
    '/admin/', # Use the string for the admin prefix
    '/api/', 
    '/platform-admin/',
    '/subscriptions/pricing/',
    '/subscriptions/webhooks/',
    '/accounts/register-school/',
    '/accounts/login/',
    '/accounts/logout/', 
    '/accounts/password_reset/',
]


class SubscriptionAccessMiddleware(MiddlewareMixin):
    def process_request(self, request):
        # 1. Bypass for Django Debug Toolbar (if used)
        if getattr(settings, 'DEBUG', False) and request.path_info.startswith('/__debug__/'):
            return None

        # 2. Bypass for requests that don't have a tenant (i.e., public schema requests)
        current_tenant = getattr(request, 'tenant', None)
        if not current_tenant or current_tenant.schema_name == get_public_schema_name():
            # Further check: If user is authenticated on public but owns a tenant with issues,
            # and tries to access a tenant-specific URL via public domain (should not happen with correct routing),
            # this might need refinement. But for now, assume public schema is fine.
            return None

        # At this point, we are in a tenant context (request.tenant exists and is not public)

        # 3. Allow platform superusers full access to tenant portals for administration
        # 3. Allow platform superusers full access to tenant portals for administration
        if request.user.is_authenticated and request.user.is_superuser and request.user.is_staff:
            user_identifier = getattr(request.user, 'email', None) # Try email first
            if not user_identifier: # Fallback if no email attribute (unlikely for user models)
                user_identifier = str(request.user) # Use string representation
            
            logger.debug(f"SubscriptionAccessMiddleware: Platform superuser '{user_identifier}' accessing tenant '{current_tenant.schema_name}'. Access granted.")
            return None

        # 4. Check if the current path is one of the always-allowed paths for subscription management or logout
        try:
            current_url_name = resolve(request.path_info).view_name # Get full view name like 'app:name'
        except Exception:
            current_url_name = None
            logger.debug(f"SubscriptionAccessMiddleware: Could not resolve view name for path '{request.path_info}' on tenant '{current_tenant.schema_name}'.")

        if current_url_name:
            if current_url_name in TENANT_SUBSCRIPTION_MANAGEMENT_URL_NAMES or \
                current_url_name in TENANT_USER_LOGOUT_URL_NAMES:
                logger.debug(f"SubscriptionAccessMiddleware: Path '{request.path_info}' (view: {current_url_name}) is an allowed subscription/logout path for tenant '{current_tenant.schema_name}'. Access granted.")
                return None
        
        # For other paths, we now check the subscription status

        # 5. Fetch the tenant's subscription
        try:
            # Assuming your School (tenant) model has a OneToOneField to Subscription
            # with related_name='subscription' on the Subscription.school field.
            # Thus, request.tenant.subscription should work.
            tenant_subscription = current_tenant.subscription
        except Subscription.DoesNotExist:
            logger.warning(f"SubscriptionAccessMiddleware: Tenant '{current_tenant.name}' (schema: {current_tenant.schema_name}) has NO Subscription record.")
            if request.user.is_authenticated:
                # Redirect authenticated tenant users to a page where they can choose/start a subscription
                messages.error(request, _("Your school's subscription is not set up. Please select a plan to continue."))
                # This URL should ideally take them to a plan selection page specific for their tenant
                # For now, redirecting to a generic subscription details which should handle "no subscription"
                return redirect(reverse_lazy('subscriptions:subscription_details')) 
            else:
                # Unauthenticated user hitting a tenant portal with no subscription setup
                messages.error(request, _("This school's portal is not yet fully configured."))
                return redirect(reverse_lazy('public_site:home')) # Redirect to public home
        except AttributeError:
            logger.error(f"SubscriptionAccessMiddleware: Tenant object '{current_tenant.name}' (schema: {current_tenant.schema_name}) does not have a 'subscription' attribute. Check model relationships.")
            messages.error(request, _("A configuration error occurred with this school's subscription. Please contact support."))
            return redirect(reverse_lazy('public_site:home'))


        # 6. Check if the subscription is usable
        if tenant_subscription and tenant_subscription.is_usable:
            # Subscription is active or trialing, allow access
            logger.debug(f"SubscriptionAccessMiddleware: Tenant '{current_tenant.name}' subscription is usable (Status: {tenant_subscription.status}). Access granted to '{request.path_info}'.")
            return None
        else:
            # Subscription is not usable (lapsed, cancelled, pending, etc.)
            logger.warning(f"SubscriptionAccessMiddleware: Tenant '{current_tenant.name}' subscription is NOT usable (Status: {tenant_subscription.status}). Access restricted for '{request.path_info}'.")
            
            # Handle different subscription statuses with appropriate messages and redirects
            if tenant_subscription and tenant_subscription.status == 'TRIALING':
                # Check if trial has expired
                from django.utils import timezone
                if tenant_subscription.trial_end_date and tenant_subscription.trial_end_date <= timezone.now():
                    denial_message = _("🚨 Your trial period has expired! Please upgrade to continue using all features.")
                    messages.error(request, denial_message)
                    # Redirect to plan selection for expired trials
                    if current_url_name not in ['subscriptions:plan_selection', 'subscriptions:subscription_details', 'subscriptions:pricing_page']:
                        return redirect(reverse_lazy('subscriptions:plan_selection'))
                else:
                    denial_message = _("Your trial subscription requires attention.")
            elif tenant_subscription and tenant_subscription.status == 'PENDING':
                denial_message = _("Your school's subscription is pending activation. Please complete any required setup or payment.")
            elif tenant_subscription and tenant_subscription.status == 'CANCELLED':
                denial_message = _("Your school's subscription has been cancelled. Please reactivate to continue.")
            elif tenant_subscription and tenant_subscription.status == 'ENDED':
                denial_message = _("Your school's subscription has ended. Please renew to continue.")
            else:
                denial_message = _("Your school's subscription requires attention. Access to some features may be limited. Please update your subscription to regain full access.")

            messages.warning(request, denial_message)

            # Redirect to the subscription details page where they can see status and take action
            # This ensures we don't get into a redirect loop if subscription_details itself is the target.
            if current_url_name not in ['subscriptions:subscription_details', 'subscriptions:plan_selection', 'subscriptions:pricing_page']:
                return redirect(reverse_lazy('subscriptions:subscription_details'))
            else:
                # If they are already on subscription management pages, let the view render.
                return None



# D:\school_fees_saas_v2\apps\subscriptions\middleware.py
import logging
from django.shortcuts import redirect
from django.urls import reverse
from django.contrib import messages
from django.utils.translation import gettext_lazy as _

from apps.students.models import Student
from apps.subscriptions.models import Subscription

logger = logging.getLogger(__name__)

class SubscriptionLimitMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response
        # A list of URL names that are restricted by the student limit.
        self.restricted_views = [
            'student_create',
            'student_bulk_import', # Add the name of your bulk import view if you have one
        ]

    def __call__(self, request):
        # Middleware should not run on non-tenant or public schema requests
        tenant = getattr(request, 'tenant', None)
        if not tenant or tenant.schema_name == 'public':
            return self.get_response(request)

        # Check if the current view is one of the restricted ones
        if request.resolver_match and request.resolver_match.url_name in self.restricted_views:
            
            logger.debug(f"SubscriptionLimitMiddleware: Running check for restricted view '{request.resolver_match.url_name}'.")
            
            try:
                subscription = tenant.subscription
                max_students = subscription.plan.max_students
                
                # A limit of None or 0 signifies 'unlimited'.
                if max_students is not None and max_students > 0:
                    # Use .count() which is efficient. Filter for active students.
                    current_student_count = Student.objects.filter(is_active=True).count()
                    
                    if current_student_count >= max_students:
                        # The limit has been reached or exceeded
                        logger.warning(f"Tenant '{tenant.schema_name}' blocked from adding new student. "
                                    f"Limit: {max_students}, Current: {current_student_count}.")
                                    
                        messages.warning(request, _(
                            f"You have reached your subscription plan's limit of {max_students} students. "
                            "Please upgrade your plan to add more students."
                        ))
                        
                        # Redirect them away from the creation page to the student list
                        return redirect(reverse('students:student_list'))

            except (Subscription.DoesNotExist, AttributeError):
                # This handles cases where the tenant has no subscription or the subscription has no plan
                logger.error(f"Tenant '{tenant.schema_name}' has no active subscription. Blocking student creation.")
                messages.error(request, _(
                    "Cannot add new students because your school does not have an active subscription plan."
                ))
                return redirect(reverse('schools:dashboard'))

        # If the check passes or the view is not restricted, continue as normal
        return self.get_response(request)
        


# from django.shortcuts import redirect
# from django.urls import reverse, resolve, Resolver404
# from django.shortcuts import redirect
# from django.urls import reverse, reverse_lazy # Make sure reverse_lazy is imported
# from django.contrib import messages
# from django.utils.translation import gettext_lazy as _


# from django.shortcuts import redirect
# from django.urls import reverse, resolve # Import resolve
# from django.contrib import messages
# # ... other imports ...
# from apps.students.models import Student
# from apps.subscriptions.models import Subscription

# class SubscriptionLimitMiddleware:
#     def __init__(self, get_response):
#         self.get_response = get_response
        
#         # --- THIS IS THE FIX ---
#         # Define a list of URL namespaces that this middleware should IGNORE.
#         # It's crucial to exempt the admin, public site, accounts/login/logout, etc.
#         self.exempt_namespaces = [
#             'admin',                 # The Django admin site
#             'public_site',           # Your main public-facing website
#             'users',                 # Your public admin/owner login/logout
#             'tenants',               # Your school registration pages
#             'debug_toolbar',         # Django Debug Toolbar (if you use it)
#             # Add any other namespaces that should not be subject to subscription checks
#         ]
#         # --- END OF FIX ---

#     def __call__(self, request):
#         # --- THIS IS THE CODE FROM YOUR TRACEBACK ---
#         # Add a check at the beginning to see if we should skip this middleware
#         try:
#             # resolve(request.path_info) gets routing info for the current URL
#             current_namespace = resolve(request.path_info).namespace
#             if current_namespace in self.exempt_namespaces:
#                 # If the current URL is in an exempt namespace, do nothing and proceed.
#                 return self.get_response(request)
#         except Exception:
#             # If resolve fails (e.g., for a URL with no namespace), proceed safely.
#             pass
#         # --- END OF CHECK ---

#         # The rest of your middleware logic
#         tenant = getattr(request, 'tenant', None)
#         # We also shouldn't run this check on the public schema
#         if not tenant or tenant.schema_name == 'public':
#             return self.get_response(request)

#         # Check only on views that could increase the student count.
#         if request.resolver_match and request.resolver_match.url_name == 'student_create':
            
#             try:
#                 subscription = tenant.subscription
#                 max_students = subscription.plan.max_students
                
#                 if max_students is not None and max_students > 0:
#                     current_student_count = Student.objects.count()
                    
#                     if current_student_count >= max_students:
#                         messages.warning(request, (
#                             f"You have reached your plan's limit of {max_students} students. "
#                             "Please upgrade your plan to add more."
#                         ))
#                         return redirect(reverse('students:student_list'))

#             except (Subscription.DoesNotExist, AttributeError):
#                 messages.error(request, (
#                     "Cannot add new students. Your school does not have an active subscription plan."
#                 ))
#                 return redirect(reverse('schools:dashboard'))

#         return self.get_response(request)
    
    