#!/usr/bin/env python
"""
Fix Fox Groups
Fix the auth_group data in fox tenant

Usage: python fix_fox_groups.py
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from django.db import connection
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def fix_fox_groups():
    """Fix auth_group data in fox tenant"""
    
    logger.info("=== FIXING FOX AUTH_GROUP DATA ===")
    
    try:
        with connection.cursor() as cursor:
            # Check current count in fox
            cursor.execute('SET search_path TO "fox"')
            cursor.execute('SELECT COUNT(*) FROM auth_group')
            count = cursor.fetchone()[0]
            logger.info(f"Current auth_group count in fox: {count}")
            
            if count < 5:
                # Get groups from alpha
                cursor.execute('SET search_path TO "alpha"')
                cursor.execute('SELECT id, name FROM auth_group ORDER BY id')
                alpha_groups = cursor.fetchall()
                
                # Get existing groups in fox
                cursor.execute('SET search_path TO "fox"')
                cursor.execute('SELECT name FROM auth_group')
                existing_groups = [row[0] for row in cursor.fetchall()]
                
                # Add missing groups
                added_count = 0
                for group_id, group_name in alpha_groups:
                    if group_name not in existing_groups:
                        try:
                            cursor.execute('INSERT INTO auth_group (id, name) VALUES (%s, %s)', [group_id, group_name])
                            logger.info(f"Added group: {group_name}")
                            added_count += 1
                        except Exception as e:
                            # Try without specific ID
                            try:
                                cursor.execute('INSERT INTO auth_group (name) VALUES (%s)', [group_name])
                                logger.info(f"Added group (auto ID): {group_name}")
                                added_count += 1
                            except Exception as e2:
                                logger.warning(f"Could not add group {group_name}: {e2}")
                
                # Check final count
                cursor.execute('SELECT COUNT(*) FROM auth_group')
                new_count = cursor.fetchone()[0]
                logger.info(f"New auth_group count in fox: {new_count}")
                logger.info(f"Added {added_count} groups")
                
                if new_count >= 5:
                    logger.info("✅ Fox auth_group data fixed successfully!")
                else:
                    logger.warning("⚠️  Fox still has insufficient auth_group data")
            else:
                logger.info("✅ Fox already has sufficient auth_group data")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to fix fox groups: {e}")
        return False

def main():
    """Main function"""
    logger.info("=== FIX FOX GROUPS ===")
    
    try:
        success = fix_fox_groups()
        
        if success:
            logger.info("\n🎉 FOX GROUPS FIXED!")
        else:
            logger.error("\n❌ FOX GROUPS FIX FAILED!")
        
        return success
        
    except Exception as e:
        logger.error(f"Fix fox groups failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
