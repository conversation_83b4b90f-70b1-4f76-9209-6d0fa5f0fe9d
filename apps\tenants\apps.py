# apps/tenants/apps.py
from django.apps import AppConfig
import logging # For ready method logging

logger = logging.getLogger(__name__) # Resolves to apps.tenants.apps

class TenantsConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'apps.tenants'

    def ready(self):
        try:
            import apps.tenants.signals
            # Use a logger that you KNOW is working (e.g., the root one for a simple test)
            # or a specific one configured like project.tenant_signals
            logging.getLogger('project.tenant_signals').info("TENANTS_APP_CONFIG: Successfully imported tenant signals from ready().")
            # OR if using the 'apps' logger and it's configured
            # logger.info("TENANTS_APP_CONFIG: Successfully imported tenant signals from ready().") 
        except ImportError:
            logging.getLogger('project.tenant_signals').error("TENANTS_APP_CONFIG: FAILED to import tenant signals.")

