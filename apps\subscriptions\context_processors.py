# apps/subscriptions/context_processors.py

from django.utils import timezone
from apps.subscriptions.models import Subscription


def trial_context(request):
    """
    Context processor to add trial and subscription information to all templates.
    """
    context = {
        'subscription': None,
        'trial_days_remaining': None,
        'is_trial_expired': False,
        'student_count': 0,
    }
    
    # Get current tenant
    tenant = getattr(request, 'tenant', None)
    if not tenant or tenant.schema_name == 'public':
        return context
    
    try:
        subscription = tenant.subscription
        context['subscription'] = subscription
        
        # Calculate trial days remaining
        if (subscription.status == Subscription.Status.TRIALING and 
            subscription.trial_end_date):
            
            days_remaining = (subscription.trial_end_date - timezone.now()).days
            context['trial_days_remaining'] = max(0, days_remaining)
            context['is_trial_expired'] = days_remaining <= 0
        
        # Get student count for usage display
        try:
            from apps.students.models import Student
            context['student_count'] = Student.objects.filter(is_active=True).count()
        except Exception:
            # In case Student model is not available in current schema
            context['student_count'] = 0
            
    except Subscription.DoesNotExist:
        # No subscription exists
        pass
    except Exception:
        # Handle any other errors gracefully
        pass
    
    return context
