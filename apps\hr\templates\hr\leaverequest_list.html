{# D:\school_fees_saas_v2\apps\hr\templates\hr\leave_request_list.html #}
{% extends "tenant_base.html" %}
{% load core_tags humanize %}

{% load static widget_tweaks %}
{% load hr_tags %} 


{% block title %}My Leave Requests{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="pagetitle mb-3">
        <h1>My Leave Requests</h1>
        <nav>
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'schools:dashboard' %}">Dashboard</a></li>
                <li class="breadcrumb-item active">My Leave Requests</li>
            </ol>
        </nav>
    </div>

    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{% if message.tags %}{{ message.tags }}{% else %}info{% endif %} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        {% endfor %}
    {% endif %}

    <div class="mb-3">
        <a href="{% url 'hr:staff_leaverequest_apply' %}" class="btn btn-primary">
            <i class="bi bi-calendar-plus me-1"></i> Apply for New Leave
        </a>
    </div>

    <div class="card">
        <div class="card-body">
            <h5 class="card-title">My Submitted Requests</h5>
            {% if leave_requests %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Leave Type</th>
                            <th>Start Date</th>
                            <th>End Date</th>
                            <th>Days</th>
                            <th>Status</th>
                            <th>Submitted On</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for lr in leave_requests %}
                        <tr>
                            <td>{{ lr.leave_type.name }}</td>
                            <td>{{ lr.start_date|date:"d M Y" }}</td>
                            <td>{{ lr.end_date|date:"d M Y" }}</td>
                            <td>{{ lr.duration|floatformat:1 }}</td>
                            <td>
                                <span class="badge bg-{{ lr.get_status_badge_class }}">{{ lr.get_status_display }}</span>
                            </td>
                            <td>{{ lr.created_at|date:"d M Y H:i" }}</td>
                            <td>
                                <a href="{% url 'hr:staff_leaverequest_detail' lr.pk %}" class="btn btn-sm btn-outline-info me-1"><i class="bi bi-eye"></i> View</a>
                                {% if lr.status == lr.LeaveStatus.PENDING %}
                                <a href="{% url 'hr:staff_leaverequest_cancel' lr.pk %}" class="btn btn-sm btn-outline-warning"><i class="bi bi-x-circle"></i> Cancel</a>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <p class="text-muted">You have not submitted any leave requests yet.</p>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

