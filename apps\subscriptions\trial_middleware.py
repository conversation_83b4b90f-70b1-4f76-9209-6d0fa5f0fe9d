# apps/subscriptions/trial_middleware.py

import logging
from django.shortcuts import redirect
from django.urls import reverse, resolve
from django.contrib import messages
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django.utils.deprecation import MiddlewareMixin
from django_tenants.utils import get_public_schema_name

from apps.subscriptions.models import Subscription

logger = logging.getLogger(__name__)

# URLs that should always be accessible even during trial expiration
TRIAL_ALLOWED_URL_NAMES = [
    # Subscription management URLs
    'subscriptions:subscription_details',
    'subscriptions:update_payment_method',
    'subscriptions:initiate_checkout',
    'subscriptions:manage_subscription_redirect',
    'subscriptions:pricing',
    'subscriptions:plan_selection',
    
    # Authentication URLs
    'schools:staff_logout_view',
    'parent_portal:logout',
    'schools:staff_login',
    'parent_portal:login',
    
    # Essential dashboard access (limited)
    'schools:dashboard',
    'portal_admin:dashboard',
    
    # Static and media files
    '/static/',
    '/media/',
]

# URLs that should be blocked during trial expiration
TRIAL_RESTRICTED_URL_PATTERNS = [
    'students:',  # Student management
    'fees:',      # Fee management
    'payments:',  # Payment processing
    'reporting:', # Reports (except basic ones)
    'hr:',        # HR management
    'finance:',   # Finance management
]


class TrialExpirationMiddleware(MiddlewareMixin):
    """
    Middleware to enforce trial period limitations and redirect expired trials to upgrade.
    """
    
    def process_request(self, request):
        # 1. Skip for debug toolbar
        if hasattr(request, 'path_info') and request.path_info.startswith('/__debug__/'):
            return None
        
        # 2. Skip for public schema requests
        current_tenant = getattr(request, 'tenant', None)
        if not current_tenant or current_tenant.schema_name == get_public_schema_name():
            return None
        
        # 3. Skip for superusers
        if request.user.is_authenticated and request.user.is_superuser and request.user.is_staff:
            return None
        
        # 4. Get current URL name
        try:
            current_url_name = resolve(request.path_info).view_name
        except Exception:
            current_url_name = None
        
        # 5. Check if current path is always allowed
        if self._is_always_allowed(request.path_info, current_url_name):
            return None
        
        # 6. Get tenant's subscription
        try:
            subscription = current_tenant.subscription
        except Subscription.DoesNotExist:
            # No subscription - redirect to plan selection
            if request.user.is_authenticated:
                messages.warning(request, _("Please select a subscription plan to continue using the platform."))
                return redirect(reverse('subscriptions:plan_selection'))
            return None
        
        # 7. Check trial status
        if subscription.status == Subscription.Status.TRIALING:
            if subscription.trial_end_date and subscription.trial_end_date <= timezone.now():
                # Trial has expired
                return self._handle_expired_trial(request, subscription, current_url_name)
            else:
                # Trial is still active - check student limit
                return self._check_trial_limits(request, subscription, current_url_name)
        
        # 8. Check if subscription is not usable (expired, cancelled, etc.)
        elif not subscription.is_usable:
            return self._handle_unusable_subscription(request, subscription, current_url_name)
        
        return None
    
    def _is_always_allowed(self, path_info, url_name):
        """Check if the current path should always be accessible."""
        # Check URL names
        if url_name and url_name in TRIAL_ALLOWED_URL_NAMES:
            return True
        
        # Check path prefixes
        for allowed_prefix in ['/static/', '/media/', '/admin/', '/api/']:
            if path_info.startswith(allowed_prefix):
                return True
        
        return False
    
    def _is_restricted_during_trial(self, url_name):
        """Check if the URL should be restricted during trial expiration."""
        if not url_name:
            return False
        
        for pattern in TRIAL_RESTRICTED_URL_PATTERNS:
            if url_name.startswith(pattern):
                return True
        
        return False
    
    def _handle_expired_trial(self, request, subscription, current_url_name):
        """Handle expired trial - redirect to upgrade page."""
        logger.warning(f"Trial expired for tenant '{request.tenant.name}'. Trial ended: {subscription.trial_end_date}")
        
        # If trying to access restricted functionality, redirect to upgrade
        if self._is_restricted_during_trial(current_url_name):
            messages.error(
                request, 
                _("Your 7-day trial has expired. Please upgrade to a paid plan to continue using all features.")
            )
            return redirect(reverse('subscriptions:plan_selection'))
        
        # For dashboard access, show warning but allow
        if current_url_name in ['schools:dashboard', 'portal_admin:dashboard']:
            messages.warning(
                request,
                _("Your trial has expired. Upgrade now to continue managing students and accessing all features.")
            )
        
        return None
    
    def _check_trial_limits(self, request, subscription, current_url_name):
        """Check trial limits (student count) during active trial."""
        # Only check limits for student-related operations
        if not current_url_name or not current_url_name.startswith('students:'):
            return None
        
        # Check student limit for trial plans
        if subscription.plan.max_students <= 10:  # This is our trial plan
            from apps.students.models import Student
            current_student_count = Student.objects.filter(is_active=True).count()
            
            # If at limit and trying to add students, block
            if (current_student_count >= subscription.plan.max_students and 
                current_url_name in ['students:student_create', 'students:student_bulk_import']):
                
                messages.error(
                    request,
                    _(f"Trial limit reached! You can only have {subscription.plan.max_students} students during your trial. "
                      f"Upgrade to a paid plan to add more students.")
                )
                return redirect(reverse('students:student_list'))
        
        return None
    
    def _handle_unusable_subscription(self, request, subscription, current_url_name):
        """Handle subscriptions that are not usable (cancelled, past due, etc.)."""
        logger.warning(f"Unusable subscription for tenant '{request.tenant.name}'. Status: {subscription.status}")
        
        if self._is_restricted_during_trial(current_url_name):
            if subscription.status == Subscription.Status.PAST_DUE:
                messages.error(
                    request,
                    _("Your subscription payment is past due. Please update your payment method to continue.")
                )
            elif subscription.status == Subscription.Status.CANCELLED:
                messages.error(
                    request,
                    _("Your subscription has been cancelled. Please reactivate or choose a new plan to continue.")
                )
            else:
                messages.error(
                    request,
                    _("Your subscription is not active. Please contact support or choose a new plan.")
                )
            
            return redirect(reverse('subscriptions:subscription_details'))
        
        return None
