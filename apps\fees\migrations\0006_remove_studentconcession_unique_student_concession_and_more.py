# Generated by Django 5.1.7 on 2025-07-12 18:27

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('fees', '0005_fix_studentconcession_table_structure'),
        ('schools', '0002_alter_staffuser_options_alter_staffuser_managers_and_more'),
        ('students', '0001_initial'),
    ]

    operations = [
        migrations.RemoveConstraint(
            model_name='studentconcession',
            name='unique_student_concession',
        ),
        migrations.AlterUniqueTogether(
            name='studentconcession',
            unique_together={('student', 'concession_type', 'academic_year', 'term')},
        ),
    ]
