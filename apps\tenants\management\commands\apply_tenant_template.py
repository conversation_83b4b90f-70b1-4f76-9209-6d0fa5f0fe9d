"""
Apply Tenant Template
Applies the comprehensive tenant template to ensure smooth tenant creation

Usage: 
python manage.py apply_tenant_template --tenant=<schema_name>
python manage.py apply_tenant_template --all
"""

from django.core.management.base import BaseCommand
from django.db import connection
from django.core.management import call_command
from apps.tenants.models import School
from django_tenants.utils import schema_context
import json
import os
import logging

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Apply comprehensive tenant template for smooth tenant creation'

    def add_arguments(self, parser):
        parser.add_argument(
            '--tenant',
            type=str,
            help='Apply template to specific tenant schema',
        )
        parser.add_argument(
            '--all',
            action='store_true',
            help='Apply template to all tenant schemas',
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force application even if tenant seems complete',
        )

    def handle(self, *args, **options):
        if options['all']:
            self.apply_to_all_tenants(options['force'])
        elif options['tenant']:
            self.apply_to_tenant(options['tenant'], options['force'])
        else:
            self.stdout.write("Use --tenant=<name> or --all")

    def apply_to_all_tenants(self, force=False):
        """Apply template to all tenants"""
        
        self.stdout.write("=== APPLYING TEMPLATE TO ALL TENANTS ===")
        
        try:
            tenants = School.objects.exclude(schema_name='public')
            
            for tenant in tenants:
                self.stdout.write(f"\n--- Processing {tenant.schema_name} ---")
                self.apply_template_to_tenant(tenant.schema_name, force)
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"Failed to apply template to all tenants: {e}")
            )

    def apply_to_tenant(self, tenant_name, force=False):
        """Apply template to specific tenant"""
        
        self.stdout.write(f"=== APPLYING TEMPLATE TO {tenant_name.upper()} ===")
        
        try:
            tenant = School.objects.get(schema_name=tenant_name)
            self.apply_template_to_tenant(tenant_name, force)
            
        except School.DoesNotExist:
            self.stdout.write(
                self.style.ERROR(f"Tenant '{tenant_name}' not found")
            )
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"Failed to apply template to {tenant_name}: {e}")
            )

    def apply_template_to_tenant(self, tenant_schema, force=False):
        """Apply comprehensive template to tenant"""
        
        try:
            # Check if template files exist
            template_sql = "tenant_template.sql"
            template_json = "tenant_template.json"
            
            if not os.path.exists(template_sql):
                self.stdout.write(
                    self.style.ERROR(f"Template file {template_sql} not found. Run create_comprehensive_tenant_template.py first.")
                )
                return
            
            # Check if tenant needs template application
            if not force and self.tenant_is_complete(tenant_schema):
                self.stdout.write(
                    self.style.SUCCESS(f"✅ {tenant_schema} appears complete, skipping (use --force to override)")
                )
                return
            
            with connection.cursor() as cursor:
                cursor.execute(f'SET search_path TO "{tenant_schema}"')
                
                self.stdout.write(f"Applying comprehensive template to {tenant_schema}...")
                
                # 1. Read and execute SQL template
                with open(template_sql, 'r', encoding='utf-8') as f:
                    sql_content = f.read()
                
                # Split into statements and execute
                statements = [stmt.strip() for stmt in sql_content.split(';') if stmt.strip() and not stmt.strip().startswith('--')]
                
                executed_count = 0
                for statement in statements:
                    if statement:
                        try:
                            cursor.execute(statement)
                            executed_count += 1
                        except Exception as e:
                            # Log but continue - some statements might conflict
                            if "already exists" not in str(e).lower():
                                self.stdout.write(f"    ⚠️  Statement warning: {e}")
                
                self.stdout.write(f"    ✅ Executed {executed_count} SQL statements")
                
                # 2. Verify essential tables exist
                essential_tables = [
                    'schools_academicyear', 'schools_term', 'schools_schoolclass', 'schools_section',
                    'fees_feehead', 'fees_feestructure', 'fees_invoice', 'payments_payment',
                    'hr_leavetype', 'payments_paymentmethod', 'fees_concessiontype',
                    'school_calendar_eventcategory', 'auth_group', 'auth_permission',
                    'django_content_type', 'students_student', 'schools_staffuser'
                ]
                
                missing_tables = []
                for table in essential_tables:
                    cursor.execute(f"""
                        SELECT EXISTS (
                            SELECT FROM information_schema.tables 
                            WHERE table_schema = '{tenant_schema}' 
                            AND table_name = '{table}'
                        )
                    """)
                    
                    if not cursor.fetchone()[0]:
                        missing_tables.append(table)
                
                if missing_tables:
                    self.stdout.write(f"    ⚠️  Missing tables: {missing_tables}")
                    # Run migrations to create missing tables
                    self.stdout.write("    Running migrations to create missing tables...")
                    with schema_context(tenant_schema):
                        call_command('migrate', verbosity=0, interactive=False)
                else:
                    self.stdout.write("    ✅ All essential tables present")
                
                # 3. Verify essential data exists
                data_tables = ['auth_group', 'auth_permission', 'django_content_type', 
                              'hr_leavetype', 'payments_paymentmethod', 'fees_concessiontype']
                
                for table in data_tables:
                    try:
                        cursor.execute(f"SELECT COUNT(*) FROM {table}")
                        count = cursor.fetchone()[0]
                        if count == 0:
                            self.stdout.write(f"    ⚠️  No data in {table}")
                        else:
                            self.stdout.write(f"    ✅ {table}: {count} records")
                    except:
                        self.stdout.write(f"    ❌ Could not check {table}")
                
                # 4. Create minimal required data if missing
                self.create_minimal_required_data(cursor, tenant_schema)
                
                self.stdout.write(
                    self.style.SUCCESS(f"✅ Template applied successfully to {tenant_schema}")
                )
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"❌ Failed to apply template to {tenant_schema}: {e}")
            )

    def tenant_is_complete(self, tenant_schema):
        """Check if tenant appears to be complete"""
        
        try:
            with connection.cursor() as cursor:
                cursor.execute(f'SET search_path TO "{tenant_schema}"')
                
                # Check for essential tables and data
                essential_checks = [
                    ('schools_academicyear', 0),  # Should have at least 1
                    ('auth_group', 5),           # Should have several groups
                    ('auth_permission', 100),    # Should have many permissions
                    ('hr_leavetype', 3),         # Should have leave types
                    ('payments_paymentmethod', 3) # Should have payment methods
                ]
                
                for table, min_count in essential_checks:
                    try:
                        cursor.execute(f"SELECT COUNT(*) FROM {table}")
                        count = cursor.fetchone()[0]
                        if count < min_count:
                            return False
                    except:
                        return False
                
                return True
                
        except:
            return False

    def create_minimal_required_data(self, cursor, tenant_schema):
        """Create minimal required data for tenant functionality"""
        
        self.stdout.write(f"    Creating minimal required data for {tenant_schema}...")
        
        try:
            # Create academic year if missing
            cursor.execute("SELECT COUNT(*) FROM schools_academicyear")
            if cursor.fetchone()[0] == 0:
                cursor.execute("""
                    INSERT INTO schools_academicyear (name, start_date, end_date, is_active, is_current, created_at, updated_at) 
                    VALUES ('2024-2025', '2024-09-01', '2025-08-31', TRUE, TRUE, NOW(), NOW())
                """)
                self.stdout.write("    ✅ Created academic year")
            
            # Create academic settings
            cursor.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_schema = %s 
                    AND table_name = 'schools_academicsetting'
                )
            """, [tenant_schema])
            
            if cursor.fetchone()[0]:
                cursor.execute("SELECT COUNT(*) FROM schools_academicsetting")
                if cursor.fetchone()[0] == 0:
                    cursor.execute("""
                        INSERT INTO schools_academicsetting (academic_year_id, created_at, updated_at) 
                        VALUES (1, NOW(), NOW())
                    """)
                    self.stdout.write("    ✅ Created academic settings")
            
            # Create invoice and receipt sequences
            for seq_table, prefix in [('schools_invoicesequence', 'INV'), ('schools_receiptsequence', 'RCP')]:
                cursor.execute(f"""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_schema = %s 
                        AND table_name = '{seq_table}'
                    )
                """, [tenant_schema])
                
                if cursor.fetchone()[0]:
                    cursor.execute(f"SELECT COUNT(*) FROM {seq_table}")
                    if cursor.fetchone()[0] == 0:
                        cursor.execute(f"""
                            INSERT INTO {seq_table} (prefix, current_number, created_at, updated_at) 
                            VALUES ('{prefix}', 1, NOW(), NOW())
                        """)
                        self.stdout.write(f"    ✅ Created {seq_table}")
            
        except Exception as e:
            self.stdout.write(f"    ⚠️  Error creating minimal data: {e}")

    def handle_success(self, tenant_schema):
        """Handle successful template application"""
        
        self.stdout.write(f"\n🎉 TEMPLATE SUCCESSFULLY APPLIED TO {tenant_schema.upper()}!")
        self.stdout.write(f"Tenant {tenant_schema} now has:")
        self.stdout.write("- Complete database structure")
        self.stdout.write("- All essential tables and columns")
        self.stdout.write("- Required reference data")
        self.stdout.write("- Working forms and dropdowns")
        self.stdout.write("- Premium formatting and features")
        self.stdout.write(f"\nTenant {tenant_schema} is ready for use!")
