# Generated by Django 5.1.9 on 2025-07-08 21:00

from django.db import migrations


def fix_unique_together_constraint(apps, schema_editor):
    """
    Manually handle the unique_together constraint change.
    The old constraint references 'year_or_period_info' which no longer exists.
    """
    db_alias = schema_editor.connection.alias

    with schema_editor.connection.cursor() as cursor:
        # Drop any existing unique constraints on hr_leavebalance
        cursor.execute("""
            SELECT constraint_name
            FROM information_schema.table_constraints
            WHERE table_name = 'hr_leavebalance'
            AND constraint_type = 'UNIQUE'
        """)

        constraints = cursor.fetchall()
        for constraint in constraints:
            constraint_name = constraint[0]
            try:
                cursor.execute(f'ALTER TABLE hr_leavebalance DROP CONSTRAINT "{constraint_name}"')
            except Exception:
                # Constraint might not exist or already dropped
                pass

        # Create the new constraint with the correct fields
        try:
            cursor.execute("""
                ALTER TABLE hr_leavebalance
                ADD CONSTRAINT hr_leavebalance_employee_leave_type_year_uniq
                UNIQUE (employee_id, leave_type_id, year)
            """)
        except Exception:
            # Constraint might already exist
            pass


def reverse_fix_unique_together_constraint(apps, schema_editor):
    """
    Reverse operation - drop the new constraint
    """
    with schema_editor.connection.cursor() as cursor:
        try:
            cursor.execute('ALTER TABLE hr_leavebalance DROP CONSTRAINT hr_leavebalance_employee_leave_type_year_uniq')
        except Exception:
            pass


class Migration(migrations.Migration):

    dependencies = [
        ('hr', '0015_alter_leavebalance_options_and_more'),
    ]

    operations = [
        migrations.RunPython(
            fix_unique_together_constraint,
            reverse_fix_unique_together_constraint,
        ),
    ]
