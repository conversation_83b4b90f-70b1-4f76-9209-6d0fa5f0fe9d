# D:\school_fees_saas_V2\apps\common\mixins.py

from django import forms
from django.urls import reverse_lazy # Ensure this is imported if used
from django.conf import settings # For LOGIN_URL, TENANT_LOGIN_URL etc.
from django.shortcuts import redirect, reverse # reverse for get_login_url in ParentLoginRequiredMixin
from django.contrib import messages
from django.utils.translation import gettext_lazy as _
from django.core.exceptions import ImproperlyConfigured
from django.contrib.auth.mixins import AccessMixin # Base for your auth mixins
from django.utils import timezone
from django.shortcuts import redirect, get_object_or_404
from django.http import Http404

# --- Import your models ---
from apps.students.models import ParentUser, Student

from django.views.generic.list import MultipleObjectMixin 

import logging
logger = logging.getLogger(__name__)


# Import your ParentUser model - CRITICAL: ADJUST THIS PATH
# Assuming ParentUser is in apps.users.models or apps.students.models
# Based on your logs, it seems to be in apps.students.models
try:
    from apps.students.models import ParentUser
except ImportError:
    # Fallback or raise an error if ParentUser is essential for these mixins
    # This helps in startup if apps are not fully loaded yet for checks but might hide issues.
    # A better approach is to ensure apps are correctly configured in settings.py
    ParentUser = None 
    # Consider logging a warning here during app loading if ParentUser is None

# Import your feature checking function - CRITICAL: ADJUST THIS PATH
try:
    from apps.common.features import tenant_has_feature_for_parent_portal
    # If you use the generic tenant_has_feature directly with PARENT_PORTAL_ACCESS_CODE:
    # from apps.common.features import tenant_has_feature, PARENT_PORTAL_ACCESS_CODE
except ImportError:
    # Fallback if the import fails during initial loading or checks
    def tenant_has_feature_for_parent_portal(tenant): return False # Default to False
    # def tenant_has_feature(tenant, code): return False
    # PARENT_PORTAL_ACCESS_CODE = "PARENT_PORTAL_ACCESS" # Define a fallback
    # Consider logging a warning here

import logging # Import logging
logger = logging.getLogger(__name__)


# --- Your Existing Mixins ---
class TenantMixinBase:
    # ... (your existing TenantMixinBase code - no changes needed here) ...
    login_url = None
    permission_denied_message = _("You do not have permission to access this page.") # Use _ for translation
    raise_exception = False

    def get_tenant_login_url(self):
        # TENANT_LOGIN_URL should be defined in settings.py
        # Example: TENANT_LOGIN_URL = 'schools:staff_login'
        # Using reverse_lazy ensures URLs are resolved when needed, not at import time
        return str(reverse_lazy(settings.TENANT_LOGIN_URL))

    def get_public_login_url(self):
        return str(settings.LOGIN_URL)


class TenantLoginRequiredMixin(TenantMixinBase, AccessMixin):
    # ... (your existing TenantLoginRequiredMixin code - no changes needed here) ...
    def get_login_url(self):
        if hasattr(self.request, 'tenant') and self.request.tenant:
            # Using MY_PUBLIC_SCHEMA_NAME from settings for robustness
            public_schema_name = getattr(settings, 'MY_PUBLIC_SCHEMA_NAME', 'public')
            if self.request.tenant.schema_name != public_schema_name:
                return self.get_tenant_login_url()
        return self.get_public_login_url()

    def dispatch(self, request, *args, **kwargs):
        if not request.user.is_authenticated:
            return self.handle_no_permission()
        return super().dispatch(request, *args, **kwargs)


class TenantPermissionRequiredMixin(TenantMixinBase, AccessMixin):
    # ... (your existing TenantPermissionRequiredMixin code - slight adjustment for redirect) ...
    permission_required = None

    def get_permission_required(self):
        if self.permission_required is None:
            raise ImproperlyConfigured(
                f"{self.__class__.__name__} is missing the permission_required attribute. "
                f"Define {self.__class__.__name__}.permission_required, or override "
                f"{self.__class__.__name__}.get_permission_required()."
            )
        if isinstance(self.permission_required, str):
            perms = (self.permission_required,)
        else:
            perms = self.permission_required
        return perms

    def has_permission(self):
        perms = self.get_permission_required()
        user_to_check = getattr(self.request, 'effective_tenant_user', self.request.user)
        # Ensure user_to_check is not None and has has_perms method
        if user_to_check and hasattr(user_to_check, 'has_perms'):
            return user_to_check.has_perms(perms)
        return False


    def get_login_url(self):
        if hasattr(self.request, 'tenant') and self.request.tenant:
            public_schema_name = getattr(settings, 'MY_PUBLIC_SCHEMA_NAME', 'public')
            if self.request.tenant.schema_name != public_schema_name:
                return self.get_tenant_login_url()
        return self.get_public_login_url()
        
    def dispatch(self, request, *args, **kwargs):
        if not request.user.is_authenticated:
            return self.handle_no_permission()
        
        if not self.has_permission():
            messages.error(request, self.permission_denied_message)
            # Determine appropriate redirect based on context
            if hasattr(request, 'tenant') and request.tenant:
                public_schema_name = getattr(settings, 'MY_PUBLIC_SCHEMA_NAME', 'public')
                if request.tenant.schema_name != public_schema_name:
                    # Tenant context, redirect to tenant dashboard or a tenant-specific access denied page
                    # Ensure 'schools:dashboard' or your tenant dashboard URL is correct
                    try:
                        # Attempt to redirect to a specific tenant dashboard if user is a staff
                        # or a parent dashboard if user is a parent
                        if hasattr(request.user, 'is_staff_member') and request.user.is_staff_member: # hypothetical attribute
                            return redirect(reverse_lazy(settings.TENANT_DASHBOARD_URL)) # e.g. 'schools:dashboard'
                        elif isinstance(request.user, ParentUser) and ParentUser is not None:
                            return redirect(reverse_lazy(settings.PARENT_DASHBOARD_URL)) # e.g. 'parent_portal:dashboard'
                        else: # Fallback tenant redirect
                            return redirect(reverse_lazy(settings.TENANT_DASHBOARD_URL))

                    except AttributeError: # If settings vars not defined, fallback
                        return redirect(reverse_lazy('public_site:home')) # Fallback further
                else:
                    # Public context, redirect to public home or a public access denied page
                    return redirect(reverse_lazy('public_site:home'))
            else:
                # No tenant context, likely public site
                return redirect(reverse_lazy('public_site:home'))
        return super().dispatch(request, *args, **kwargs)


# --- NEW ParentLoginRequiredMixin ---
class ParentLoginRequiredMixin(AccessMixin): # Inherits from AccessMixin directly
    """
    Verify that the current user is authenticated, is an instance of ParentUser,
    and that the Parent Portal feature is enabled for the current tenant.
    """
    login_url_name = 'parent_portal:login' # URL name to redirect to for login
    permission_denied_redirect_url_name = 'public_site:home' # Fallback redirect if feature denied

    def dispatch(self, request, *args, **kwargs):
        # 1. Check if user is authenticated
        if not request.user.is_authenticated:
            logger.debug(f"ParentLoginRequiredMixin: User not authenticated. Redirecting to login: {self.get_login_url()}")
            return self.handle_no_permission() # Redirects to get_login_url()

        # 2. Check if the authenticated user is a ParentUser
        # Ensure ParentUser is not None (meaning it was imported correctly)
        if ParentUser is None:
            logger.critical("ParentLoginRequiredMixin: ParentUser model is None (import failed). Denying access.")
            messages.error(request, _("Parent portal system error. Please try again later."))
            return redirect(reverse_lazy(self.permission_denied_redirect_url_name))

        if not isinstance(request.user, ParentUser):
            logger.info(f"ParentLoginRequiredMixin: User '{request.user}' is not a ParentUser (type: {type(request.user)}). Redirecting from parent portal area.")
            # Log them out from current session and redirect to parent login,
            # as they might be a staff or admin on a parent URL.
            from django.contrib.auth import logout
            logout(request)
            messages.info(request, _("Please log in with your parent credentials to access this area."))
            return redirect(self.get_login_url())

        # 3. Check for tenant context
        tenant = getattr(request, 'tenant', None)
        if not tenant:
            logger.error("ParentLoginRequiredMixin: Tenant context not found on request. Denying access to parent portal area.")
            messages.error(request, _("School context not found. Unable to access parent portal."))
            return redirect(reverse_lazy(self.permission_denied_redirect_url_name)) # Redirect to a safe public page

        # 4. Check if the Parent Portal feature is enabled for this tenant
        # This now uses the specific function from apps.common.features
        if not tenant_has_feature_for_parent_portal(tenant):
            logger.warning(f"ParentLoginRequiredMixin: PARENT_PORTAL feature disabled for tenant '{tenant.name}'. Access denied for parent '{request.user.email}'.")
            messages.error(request, _("The Parent Portal is not currently enabled for this school. Please contact school administration."))
            return redirect(reverse_lazy(self.permission_denied_redirect_url_name)) # Or a specific "feature_disabled" page for this tenant
        
        logger.debug(f"ParentLoginRequiredMixin: Access GRANTED for ParentUser '{request.user.email}' to tenant '{tenant.name}' parent portal area.")
        return super().dispatch(request, *args, **kwargs)

    def get_login_url(self):
        """
        Returns the login URL for parents.
        """
        return str(reverse(self.login_url_name)) # Use reverse here

    def handle_no_permission(self):
        """
        Handles the case where the user does not have permission.
        For unauthenticated users, it redirects to the login page.
        """
        messages.info(self.request, _("Please log in to access the parent portal.")) # More specific message
        return redirect(self.get_login_url())



# D:\school_fees_saas_v2\apps\common\mixins.py
from django import forms
from django.urls import reverse_lazy
from django.conf import settings
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django.contrib.auth.mixins import AccessMixin # For TenantPermissionRequiredMixin etc.
# For BaseReportViewMixin to interact with ListView if needed
from django.views.generic.list import MultipleObjectMixin 
# Import SchoolProfile if get_school_profile is used
# from apps.schools.models import SchoolProfile 

import logging
logger = logging.getLogger(__name__)


# D:\school_fees_saas_v2\apps\common\mixins.py

import logging
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django.http import HttpResponseRedirect
from django.urls import reverse # For redirect
from django.contrib import messages
# from django.core.exceptions import ImproperlyConfigured # If needed

logger = logging.getLogger(__name__)

# class BaseReportViewMixin:
#     report_title_text = _("Report")
#     report_code = None
#     filter_form_class = None
    
#     form = None 
#     report_data = None # This will be populated by self.get_report_data() in the get() method

#     def get_report_title(self):
#         return str(self.report_title_text)

#     def get_filter_form_initial_data(self):
#         return {}

#     def get_filter_form_kwargs(self):
#         kwargs = {'request': self.request}
#         if hasattr(self.request, 'tenant') and self.request.tenant:
#             kwargs['tenant'] = self.request.tenant
#         initial_data = self.get_filter_form_initial_data()
#         if initial_data:
#             kwargs['initial'] = initial_data
#         return kwargs

#     def get_report_data(self): # SIGNATURE: takes only self
#         """
#         Child view implements this. Uses self.form (set in get()).
#         Returns a dictionary of report-specific data.
#         """
#         raise NotImplementedError(
#             f"{self.__class__.__name__} must implement get_report_data(self). "
#             f"Access the processed filter form via self.form."
#         )

#     def get_common_report_context(self):
#         context = {}
#         if hasattr(self.request, 'tenant') and self.request.tenant:
#             context['report_tenant_name'] = self.request.tenant.name
#         return context
    
#     def get(self, request, *args, **kwargs): # <<< PRIMARY ORCHESTRATION
#         self.report_data = {} # Initialize/reset report_data for this request

#         if self.filter_form_class:
#             form_kwargs = self.get_filter_form_kwargs()
#             self.form = self.filter_form_class(request.GET or None, **form_kwargs)
            
#             if self.form.is_valid():
#                 logger.debug(f"{self.__class__.__name__}.get(): Filter form is valid. Cleaned data: {self.form.cleaned_data}")
#                 self.report_data = self.get_report_data(self.form) # CALLS CHILD'S get_report_data
#             elif not self.form.is_bound and self.form.initial:
#                 logger.debug(f"{self.__class__.__name__}.get(): Filter form unbound with initial data: {self.form.initial}")
#                 self.report_data = self.get_report_data(self.form) # CALLS CHILD'S get_report_data
#             else: # Form is bound but invalid
#                 logger.warning(f"{self.__class__.__name__}.get(): Filter form is bound but invalid. Errors: {self.form.errors}")
#                 # self.report_data remains {} or child's get_report_data handles invalid form gracefully
#                 # (e.g., by returning empty structures if self.form is invalid)
#                 # For safety, ensure get_report_data is called to allow child to provide default empty state
#                 self.report_data = self.get_report_data(self.form)
#         else:
#             self.form = None
#             self.report_data = self.get_report_data(None) # CALLS CHILD'S get_report_data (self.form will be None)
        
#         export_type = request.GET.get('export')
#         if export_type:
#             if self.form and self.form.is_bound and not self.form.is_valid():
#                 messages.warning(request, _("Invalid filters. Please correct them before exporting."))
#                 query_params = request.GET.copy(); query_params.pop('export', None)
#                 return HttpResponseRedirect(f"{request.path}?{query_params.urlencode()}")
            
#             queryset_for_export = self.get_export_queryset()
#             if export_type == 'csv': return self.export_to_csv(queryset_for_export, request)
#             if export_type == 'excel': return self.export_to_excel(queryset_for_export, request)
#             if export_type == 'pdf': 
#                 pdf_context = self.get_context_data_for_pdf(queryset_for_export)
#                 return self.export_to_pdf(queryset_for_export, request, pdf_context)
        
#         return super().get(request, *args, **kwargs) # Calls ListView/TemplateView get

#     def get_context_data(self, **kwargs): # Called by ListView.get() or TemplateView.get()
#         context = super().get_context_data(**kwargs)
        
#         context['filter_form'] = self.form 
#         context['report_title'] = self.get_report_title()
#         context.update(self.get_common_report_context())
#         context['report_code'] = getattr(self, 'report_code', self.__class__.__name__.lower().replace("view", ""))
        
#         # self.report_data was ALREADY SET in the get() method
#         if self.report_data is not None: 
#             context.update(self.report_data) # Merge the pre-calculated report data
#         else:
#             logger.warning(f"{self.__class__.__name__}.get_context_data: self.report_data is None. Check get() method logic.")

#         if self.request.GET:
#             query_params = self.request.GET.copy()
#             if 'page' in query_params: del query_params['page']
#             context['filter_params'] = query_params.urlencode()
#         else:
#             context['filter_params'] = ''
#         context.setdefault('report_generated_at', timezone.now())
#         logger.debug(f"{self.__class__.__name__}.get_context_data: Final context. Keys: {list(context.keys())}")
#         return context

#     # ... (export method stubs: get_export_queryset, export_to_csv, export_to_excel, get_pdf_template_name, get_context_data_for_pdf, export_to_pdf) ...
#     # Ensure these stubs are also consistent if they call get_report_data, or better, use self.report_data if it's already populated.
#     # For example, get_context_data_for_pdf should use self.report_data if it contains summaries.
#     def get_export_queryset(self):
#         raise NotImplementedError(...)
    
#     def export_to_csv(self, queryset, request): raise NotImplementedError(...)
    
#     def export_to_excel(self, queryset, request): raise NotImplementedError(...)
    
#     def get_context_data_for_pdf(self, queryset):
#         context = {
#             'report_items': queryset, 
#             'report_title': self.get_report_title(),
#             'report_generated_at': timezone.now(),
#             'filter_form_data': self.form.cleaned_data if self.form and self.form.is_valid() else {},
#             'request': self.request,
#             'school_profile': getattr(self.request, 'school_profile_for_reports', None),
#         }
#         if hasattr(self, 'report_data') and self.report_data: # Use already computed summary
#             context.update(self.report_data) 
#         return context
#     def export_to_pdf(self, queryset, request, pdf_context): raise NotImplementedError(...)

    

# D:\school_fees_saas_v2\apps\common\mixins.py
from django.urls import reverse_lazy
from django.utils.translation import gettext_lazy as _

class BaseReportViewMixin:
    """
    A base mixin for report views.
    Provides common context like a dynamic report title and export URLs.
    """
    report_title = _("Report")
    filter_form_class = None
    export_csv_url_name = None
    export_pdf_url_name = None
    export_excel_url_name = None

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Set the title
        context['view_title'] = self.report_title
        
        # Add the filter form instance (which should be set on self.filterset by the child view)
        if hasattr(self, 'filterset'):
            context['filter_form'] = self.filterset.form

        # Add export URLs to the context, passing current filters
        query_params = self.request.GET.urlencode()
        if self.export_csv_url_name:
            context['export_csv_url'] = f"{reverse_lazy(self.export_csv_url_name)}?{query_params}"
        if self.export_pdf_url_name:
            context['export_pdf_url'] = f"{reverse_lazy(self.export_pdf_url_name)}?{query_params}"
        if self.export_excel_url_name:
            context['export_excel_url'] = f"{reverse_lazy(self.export_excel_url_name)}?{query_params}"
            
        return context
    
    
    


# D:\school_fees_saas_v2\apps\common\mixins.py
from django.contrib.auth.mixins import AccessMixin # Use AccessMixin for more control
from django.shortcuts import redirect, reverse
from django.urls import reverse_lazy
from django.contrib import messages
from django.utils.translation import gettext_lazy as _

# --- Make sure StaffUser is imported correctly and safely ---
_StaffUser = None
try:
    from apps.schools.models import StaffUser as _SUModel
    _StaffUser = _SUModel
except ImportError:
    # This logger might not be configured yet if settings haven't fully loaded
    # logging.getLogger(__name__).error("CRITICAL: StaffUser model could not be imported in common.mixins.")
    pass # Handle the None case in dispatch

class StaffLoginRequiredMixin(AccessMixin):
    """
    Verify that the current user is authenticated and is an instance of StaffUser.
    """
    login_url = reverse_lazy('schools:staff_login') # Default staff login URL
    permission_denied_message = _("You must be logged in as staff to access this page.")

    def dispatch(self, request, *args, **kwargs):
        if _StaffUser is None: # Check if model failed to import
            messages.error(request, _("System error: Staff authentication module not available."))
            # Redirect to a generic error page or public home
            return redirect(reverse_lazy('public_site:home')) 

        if not request.user.is_authenticated:
            return self.handle_no_permission()
        
        if not isinstance(request.user, _StaffUser):
            messages.error(request, _("Access Denied. This area is for staff accounts only."))
            # Optionally log out the user if they are the wrong type
            # from django.contrib.auth import logout
            # logout(request)
            return redirect(self.get_login_url()) # Redirect to staff login
        
        if not request.user.is_active: # Check if the StaffUser is active
            messages.error(request, _("Your staff account is inactive. Please contact an administrator."))
            return redirect(self.get_login_url())

        return super().dispatch(request, *args, **kwargs)


# apps/common/decorators.py (New file or add to mixins.py)
from functools import wraps
from django.contrib.auth.decorators import user_passes_test
from django.core.exceptions import PermissionDenied
from django.shortcuts import redirect
from django.urls import reverse_lazy
from django.conf import settings
from django.contrib import messages # For messages

def tenant_permission_required(permission_required, login_url=None, raise_exception=False):
    """
    Decorator for views that checks that the user is logged in and has
    a particular permission in the tenant context.
    Uses request.effective_tenant_user if available.
    """
    if login_url is None:
        login_url = getattr(settings, 'TENANT_LOGIN_URL', reverse_lazy('schools:staff_login')) # Default

    def check_perms(user, perms):
        user_to_check = user # By default use request.user
        # If you have a convention to set effective_tenant_user on request for owners acting as staff
        if hasattr(user, '_request_for_perms_check') and \
            hasattr(user._request_for_perms_check, 'effective_tenant_user'):
            user_to_check = user._request_for_perms_check.effective_tenant_user
        
        if isinstance(perms, str):
            perms = (perms,)
        return user_to_check.has_perms(perms)

    def decorator(view_func):
        @wraps(view_func)
        def _wrapped_view(request, *args, **kwargs):
            # Attach request to user temporarily if effective_tenant_user needs it for context
            # This is a bit of a hack if effective_tenant_user relies on request attributes
            # not directly on the user object itself for permission checking.
            # A cleaner way is if effective_tenant_user itself is the fully capable user object.
            if hasattr(request.user, 'is_authenticated') and request.user.is_authenticated:
                # Attach request to user object if check_perms needs it for effective_tenant_user
                request.user._request_for_perms_check = request 
                
                if check_perms(request.user, permission_required):
                    del request.user._request_for_perms_check # Clean up
                    return view_func(request, *args, **kwargs)
                
                if hasattr(request.user, '_request_for_perms_check'): # Clean up always
                    del request.user._request_for_perms_check

                if raise_exception:
                    raise PermissionDenied("You do not have permission to access this page.")
                messages.error(request, "You do not have permission to access this page.")
                # Redirect based on tenant context
                public_schema_name = getattr(settings, 'MY_PUBLIC_SCHEMA_NAME', 'public')
                if hasattr(request, 'tenant') and request.tenant and request.tenant.schema_name != public_schema_name:
                    return redirect(getattr(settings, 'TENANT_DASHBOARD_URL', reverse_lazy('schools:dashboard')))
                else:
                    return redirect(getattr(settings, 'PUBLIC_SITE_HOME_URL', reverse_lazy('public_site:home')))
            
            # If not authenticated, redirect to login
            from django.contrib.auth.views import redirect_to_login
            path = request.build_absolute_uri()
            return redirect_to_login(path, login_url) # Uses login_url passed or default
        return _wrapped_view
    return decorator



# --- ADD THIS NEW MIXIN CLASS ---
class ParentOwnsStudentMixin:
    """
    A mixin to verify that the logged-in parent is linked to the student
    specified by a `student_pk` in the URL kwargs.

    This should be used on views that deal with a specific student,
    e.g., StudentDetailView, StudentFeesView.

    It sets `self.student` on the view instance for easy access.
    
    USAGE: Inherit from this mixin *after* ParentLoginRequiredMixin.
    class MyStudentSpecificView(ParentLoginRequiredMixin, ParentOwnsStudentMixin, DetailView):
        # ...
    """
    def dispatch(self, request, *args, **kwargs):
        # ParentLoginRequiredMixin should have already run, so request.user is an authenticated ParentUser
        
        # Get the student's primary key from the URL (e.g., /parents/my-children/<student_pk>/fees/)
        student_pk = self.kwargs.get('student_pk')
        if not student_pk:
            logger.error("ParentOwnsStudentMixin: URL pattern is missing 'student_pk' kwarg.")
            raise Http404("Student identifier not found in URL.")

        parent = request.user
        
        try:
            # This is the core check. We query for a student with the given PK
            # AND filter by students that are linked to the currently logged-in parent.
            # `parent.children` assumes the M2M related_name on Student->ParentUser is 'children'.
            self.student = parent.children.select_related(
                'current_class', 'current_section'
            ).get(pk=student_pk, is_active=True)
            
            logger.debug(f"ParentOwnsStudentMixin: Verified parent {parent.email} owns student {self.student.pk}.")
            
        except Student.DoesNotExist:
            logger.warning(f"ParentOwnsStudentMixin: Parent {parent.email} attempted to access student PK {student_pk}, but the student either does not exist, is inactive, or is not linked to this parent.")
            # Don't give away information. Just raise a 404.
            raise Http404("Student record not found or you do not have permission to view it.")
        except AttributeError:
            # This error happens if your ParentUser model doesn't have the .children manager
            logger.error(f"ParentOwnsStudentMixin: The ParentUser model for {parent.email} does not have a 'children' attribute. Check the related_name on the Student model's ManyToManyField to ParentUser.")
            messages.error(request, _("A system configuration error occurred (Code: M2M_REL)."))
            return redirect(reverse_lazy('parent_portal:dashboard'))

        # If all checks pass, continue to the view's original dispatch method
        return super().dispatch(request, *args, **kwargs)

    def get_context_data(self, **kwargs):
        """
        Adds the verified student object to the template context.
        """
        context = super().get_context_data(**kwargs)
        # self.student was set in dispatch(), so we can add it to the context
        context['student'] = self.student
        return context
    
    
