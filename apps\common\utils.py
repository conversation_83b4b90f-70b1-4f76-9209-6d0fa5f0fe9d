# D:\school_fees_saas_v2\apps\common\utils.py
from io import BytesIO
from django.http import HttpResponse # Not directly used by render_to_pdf but often in views using it
from django.template.loader import get_template
from django.conf import settings
import os
import logging

logger = logging.getLogger(__name__)

# --- PDF AVAILABILITY CHECK ---
try:
    # Try to import the necessary components from xhtml2pdf
    from xhtml2pdf import pisa
    PDF_IS_AVAILABLE = True
    PDF_AVAILABLE = True  # Alternative name for compatibility
    logger.info("PDF UTILS: xhtml2pdf (pisa) imported successfully. PDF generation is available.")
except ImportError:
    # If the import fails, set the flag to False
    pisa = None
    PDF_IS_AVAILABLE = False
    PDF_AVAILABLE = False  # Alternative name for compatibility
    logger.warning("PDF UTILS: xhtml2pdf library not found. PDF generation will be disabled.")
# --- END OF PDF AVAILABILITY CHECK ---


import os
from django.conf import settings
# ... other imports

def link_callback(uri, rel):
    """
    Convert HTML URIs to absolute system paths.
    """
    # Handle absolute Windows paths (e.g., for media files)
    if os.path.splitdrive(uri)[0]:
        return f"file://{uri}"

    # Use STATICFILES_DIRS for static files (like fonts, css)
    # This is more reliable in development than STATIC_ROOT
    if settings.STATIC_URL and uri.startswith(settings.STATIC_URL):
        path = os.path.join(settings.STATICFILES_DIRS[0], uri.replace(settings.STATIC_URL, ""))
        if os.path.exists(path):
            return path

    # Use MEDIA_ROOT for media files (like logos)
    if settings.MEDIA_URL and uri.startswith(settings.MEDIA_URL):
        path = os.path.join(settings.MEDIA_ROOT, uri.replace(settings.MEDIA_URL, ""))
        if os.path.exists(path):
            return path

    # Fallback to STATIC_ROOT for production (after collectstatic)
    static_root_path = os.path.join(settings.STATIC_ROOT, uri.replace(settings.STATIC_URL, ""))
    if os.path.exists(static_root_path):
        return static_root_path

    logger.warning(f"PDF Link Callback: Could not resolve URI: '{uri}'")
    return uri


# def link_callback(uri, rel):
#     """
#     Convert HTML URIs to absolute system paths so xhtml2pdf can access those
#     resources. Handles static files, media files, and local absolute paths
#     on different operating systems.
#     """
#     logger.debug(f"PDF Link Callback trying to resolve URI: '{uri}' with rel: '{rel}'")

#     # --- NEW: Handle absolute Windows paths (D:\...) ---
#     # Check if the URI looks like a Windows path (e.g., C:\, D:\)
#     if os.path.splitdrive(uri)[0]:
#         # It's an absolute path. Convert to a file:// URI.
#         # This is the most robust way for xhtml2pdf to handle it.
#         file_uri = f"file://{uri}"
#         logger.debug(f"  --> Windows path detected. Converted to file URI: '{file_uri}'")
#         return file_uri

#     # --- Keep the existing logic for MEDIA_URL and STATIC_URL ---
#     # Use MEDIA_ROOT for media files
#     if settings.MEDIA_URL and uri.startswith(settings.MEDIA_URL):
#         path = os.path.join(settings.MEDIA_ROOT, uri.replace(settings.MEDIA_URL, ""))
#         logger.debug(f"  --> Matched MEDIA_URL. Resolved to path: '{path}'")
#         return path

#     # Use STATIC_ROOT for static files
#     if settings.STATIC_URL and uri.startswith(settings.STATIC_URL):
#         path = os.path.join(settings.STATIC_ROOT, uri.replace(settings.STATIC_URL, ""))
#         logger.debug(f"  --> Matched STATIC_URL. Resolved to path: '{path}'")
#         return path

#     logger.debug(f"  --> URI '{uri}' not matched. Returning as is for pisa to handle.")
#     return uri


def render_to_pdf(template_src, context_dict=None):
    if not PDF_AVAILABLE:
        logger.error("render_to_pdf called but xhtml2pdf (pisa) is not available.")
        return None
    
    if context_dict is None:
        context_dict = {}

    logger.debug(f"--- RENDER_TO_PDF: Attempting to render template: {template_src} ---")
    try:
        template = get_template(template_src)
        # Add settings to context if link_callback needs them and they aren't already there
        # (though link_callback directly accesses settings.STATIC_URL etc.)
        # context_dict.update({
        #     'STATIC_URL': settings.STATIC_URL,
        #     'MEDIA_URL': settings.MEDIA_URL,
        # })
        html = template.render(context_dict)
        logger.debug(f"--- RENDER_TO_PDF: HTML rendered successfully for {template_src} ---")
    except Exception as e:
        logger.error(f"Error rendering PDF template '{template_src}': {e}", exc_info=True)
        return None

    result = BytesIO()
    
    try:
        logger.debug(f"--- RENDER_TO_PDF: Calling pisa.pisaDocument for {template_src} ---")
        # Ensure pisa is not None before calling (already handled by PDF_AVAILABLE check)
        pdf_status = pisa.pisaDocument(
            BytesIO(html.encode("UTF-8")),  # Source HTML
            result,                         # Destination PDF stream
            encoding='UTF-8',
            link_callback=link_callback     # Callback to find local files (CSS, images)
        )
        logger.debug(f"--- RENDER_TO_PDF: pisaDocument completed. Error status: {pdf_status.err} ---")
    except Exception as e:
        logger.error(f"Exception during pisa.pisaDocument for '{template_src}': {e}", exc_info=True)
        return None

    if not pdf_status.err:
        logger.info(f"--- RENDER_TO_PDF: PDF generation successful for {template_src} ---")
        return result.getvalue()  # Return PDF binary data
    
    # Log PISA errors if any
    logger.error(f"PISA error(s) generating PDF for '{template_src}': {pdf_status.err}")
    # pdf_status.log contains more detailed error messages from pisa
    if hasattr(pdf_status, 'log') and pdf_status.log:
        for msg in pdf_status.log:
            logger.error(f"  PISA Detail: {msg}")

    return None




# apps/common/utils.py
from django.utils import timezone

def get_current_academic_year(request_or_tenant=None):
    """
    Retrieves the currently active academic year for the given tenant.
    If request_or_tenant is a request object, it tries to get the tenant from it.
    If request_or_tenant is a tenant object, it uses it directly.
    If request_or_tenant is None, it might try to get from session or a global setting.
    Falls back to the year with is_current=True if multiple are marked.
    """
    # Import locally to avoid circular imports
    from apps.fees.models import AcademicYear

    # This is a simplified example; your actual logic might be more complex,
    # possibly involving session variables or a specific way you mark the "current" year.

    current_tenant = None
    if hasattr(request_or_tenant, 'tenant'): # It's a request object
        current_tenant = request_or_tenant.tenant
    elif request_or_tenant and not hasattr(request_or_tenant, 'user'): # It's likely a tenant object
        current_tenant = request_or_tenant

    if not current_tenant: # Fallback if no tenant context, or for global settings
        # This part is tricky without a tenant context.
        # You might have a global "current academic year" setting, or this function
        # might only be intended for use within a tenant context.
        # For now, let's assume it requires a tenant for proper function.
        # Or, if you have a session variable from a global filter:
        if hasattr(request_or_tenant, 'session'):
            academic_year_id = request_or_tenant.session.get('selected_global_academic_year_id')
            if academic_year_id:
                try:
                    return AcademicYear.objects.get(pk=academic_year_id)
                except AcademicYear.DoesNotExist:
                    pass # Fall through

        # If not in session, try to find the one marked is_current=True
        # This assumes only one can be current.
        try:
            return AcademicYear.objects.filter(is_current=True).order_by('-start_date').first()
        except AcademicYear.DoesNotExist:
            return None # No current academic year found
        except Exception: # Catch other potential errors
            return None 
    
    # If we have a tenant context
    # This example assumes AcademicYear is a tenant-specific model,
    # or if it's shared, it has a ForeignKey to the tenant.
    # If AcademicYear is shared and has no FK to tenant (unlikely for multi-tenant schools),
    # then the logic above for no tenant context applies.
    
    # Scenario 1: AcademicYear is a tenant-specific model (lives in tenant schema)
    # The query will automatically be scoped if this util is called from a tenant view.
    
    # Scenario 2: AcademicYear is a SHARED model but linked to tenant via FK
    # e.g., class AcademicYear(models.Model): school = models.ForeignKey(settings.TENANT_MODEL, ...)
    # try:
    #     return AcademicYear.objects.filter(school=current_tenant, is_current=True).order_by('-start_date').first()
    # except AcademicYear.DoesNotExist:
    #     return None

    # Simplified assumption: AcademicYear is tenant-specific and query is scoped
    # Or it's a global model and we pick the one marked current
    # The logic for your global filter form might set a session variable.
    if hasattr(request_or_tenant, 'session'):
        academic_year_id = request_or_tenant.session.get('selected_global_academic_year_id')
        if academic_year_id:
            try:
                return AcademicYear.objects.get(pk=academic_year_id) # Query will be scoped by tenant middleware
            except AcademicYear.DoesNotExist:
                pass # Fall through

    try:
        # This will be scoped to the current tenant if AcademicYear is a tenant model
        return AcademicYear.objects.filter(is_current=True).order_by('-start_date').first()
    except AcademicYear.DoesNotExist:
        return None
    except Exception: # To catch any other DB errors during model loading
        return None
    


def get_financial_year_start(date_obj):
    """
    Calculates the start date of the financial year for a given date.
    
    This is a placeholder. You should customize this function based on your
    school's specific financial year (e.g., April 1st, July 1st, etc.).
    
    Current implementation assumes the financial year starts on January 1st.
    """
    if not date_obj:
        date_obj = timezone.now().date()
        
    # Example: Financial year starts on January 1st.
    return date_obj.replace(month=1, day=1)