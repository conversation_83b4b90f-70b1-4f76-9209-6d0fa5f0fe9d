#!/usr/bin/env python
"""
Fix SchoolProfile Table
Creates the correct schools_schoolprofile table structure

Usage: python fix_schoolprofile_table.py
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from django.db import connection
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def fix_schoolprofile_table():
    """Fix the schools_schoolprofile table structure"""
    schema_name = 'mandiva'
    
    logger.info(f"=== FIXING SCHOOLPROFILE TABLE IN {schema_name} ===")
    
    try:
        with connection.cursor() as cursor:
            # Set search path to mandiva schema
            cursor.execute(f'SET search_path TO "{schema_name}"')
            
            # Drop the existing table if it exists
            logger.info("Dropping existing schools_schoolprofile table...")
            cursor.execute("DROP TABLE IF EXISTS schools_schoolprofile CASCADE")
            
            # Create the table with correct structure matching the Django model
            logger.info("Creating schools_schoolprofile table with correct structure...")
            cursor.execute("""
                CREATE TABLE schools_schoolprofile (
                    id BIGSERIAL PRIMARY KEY,
                    school_name_override VARCHAR(255),
                    school_motto VARCHAR(255),
                    logo VARCHAR(100),
                    school_email VARCHAR(254),
                    phone_number VARCHAR(30),
                    address_line1 VARCHAR(255),
                    address_line2 VARCHAR(255),
                    city VARCHAR(100),
                    state_province VARCHAR(100),
                    postal_code VARCHAR(20),
                    country_name VARCHAR(100),
                    default_accounts_receivable_coa_id BIGINT,
                    default_cash_coa_id BIGINT,
                    default_bank_coa_id BIGINT,
                    default_fee_income_coa_id BIGINT,
                    default_discount_given_coa_id BIGINT,
                    default_expense_coa_id BIGINT,
                    financial_year_start_month SMALLINT NOT NULL DEFAULT 1,
                    current_academic_year_id BIGINT,
                    currency_symbol VARCHAR(5) DEFAULT '$',
                    school_name_on_reports VARCHAR(255),
                    default_due_days INTEGER NOT NULL DEFAULT 15,
                    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
                    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
                );
            """)
            
            # Create indexes for performance
            cursor.execute("""
                CREATE INDEX schools_schoolprofile_current_academic_year_id_idx 
                ON schools_schoolprofile (current_academic_year_id);
            """)
            
            logger.info("✅ Created schools_schoolprofile table with correct structure")
            
            # Insert a default school profile
            cursor.execute("""
                INSERT INTO schools_schoolprofile 
                (school_name_override, school_motto, school_email, currency_symbol, 
                 school_name_on_reports, default_due_days, financial_year_start_month)
                VALUES 
                ('Mandiva College', 'Excellence in Education', '<EMAIL>', '$', 
                 'Mandiva College', 30, 1)
            """)
            
            logger.info("✅ Added default school profile data")
            
            # Verify the table structure
            cursor.execute("""
                SELECT column_name, data_type, is_nullable, column_default
                FROM information_schema.columns 
                WHERE table_name = 'schools_schoolprofile'
                ORDER BY ordinal_position
            """)
            
            columns = cursor.fetchall()
            logger.info("Table structure:")
            for col in columns:
                logger.info(f"  {col[0]} ({col[1]}) - nullable: {col[2]}")
            
            # Test the query that was failing
            cursor.execute("""
                SELECT id, school_name_override, school_motto, created_at, updated_at
                FROM schools_schoolprofile
            """)
            
            records = cursor.fetchall()
            logger.info(f"✅ Successfully queried schools_schoolprofile (found {len(records)} records)")
            
            for record in records:
                logger.info(f"  ID: {record[0]}, Name: {record[1]}, Motto: {record[2]}")
            
            logger.info(f"🎉 SCHOOLPROFILE TABLE FIXED!")
            
    except Exception as e:
        logger.error(f"Failed to fix schoolprofile table: {e}")
        raise

def test_reporting_query():
    """Test the specific query that the reporting is making"""
    schema_name = 'mandiva'
    
    logger.info("=== TESTING REPORTING SCHOOLPROFILE QUERY ===")
    
    try:
        with connection.cursor() as cursor:
            cursor.execute(f'SET search_path TO "{schema_name}"')
            
            # This is likely the query from the reporting view
            cursor.execute("""
                SELECT "schools_schoolprofile"."id", 
                       "schools_schoolprofile"."school_name_override", 
                       "schools_schoolprofile"."school_motto",
                       "schools_schoolprofile"."created_at", 
                       "schools_schoolprofile"."updated_at"
                FROM "schools_schoolprofile"
            """)
            
            records = cursor.fetchall()
            logger.info(f"✅ Reporting SchoolProfile query successful (found {len(records)} records)")
            
    except Exception as e:
        logger.error(f"Reporting SchoolProfile query test failed: {e}")
        raise

def create_additional_missing_tables():
    """Create any other tables that might be missing for reporting"""
    schema_name = 'mandiva'
    
    logger.info(f"=== CREATING ADDITIONAL TABLES FOR REPORTING ===")
    
    try:
        with connection.cursor() as cursor:
            cursor.execute(f'SET search_path TO "{schema_name}"')
            
            # Create schools_class table if it doesn't exist
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS schools_class (
                    id BIGSERIAL PRIMARY KEY,
                    name VARCHAR(100) NOT NULL,
                    description TEXT,
                    academic_year_id BIGINT,
                    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
                    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
                );
            """)
            
            # Create schools_section table if it doesn't exist
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS schools_section (
                    id BIGSERIAL PRIMARY KEY,
                    name VARCHAR(100) NOT NULL,
                    class_id BIGINT,
                    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
                    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
                );
            """)
            
            # Insert sample data
            cursor.execute("""
                INSERT INTO schools_class (name, description, academic_year_id)
                VALUES ('Grade 1', 'First Grade Class', 1)
                ON CONFLICT DO NOTHING;
            """)
            
            cursor.execute("""
                INSERT INTO schools_section (name, class_id)
                VALUES ('Section A', 1)
                ON CONFLICT DO NOTHING;
            """)
            
            logger.info("✅ Created additional schools tables")
            
    except Exception as e:
        logger.error(f"Failed to create additional tables: {e}")

def main():
    """Main function"""
    logger.info("=== FIXING SCHOOLPROFILE TABLE ===")
    
    try:
        # Fix the main table
        fix_schoolprofile_table()
        
        # Create additional tables
        create_additional_missing_tables()
        
        # Test the queries
        test_reporting_query()
        
        logger.info("\n🎉 SCHOOLPROFILE TABLE COMPLETELY FIXED!")
        logger.info("The reporting should now work without any SchoolProfile errors.")
        logger.info("Try accessing: http://mandiva.myapp.test:8000/portal/reporting/collection-report/")
        
        return True
        
    except Exception as e:
        logger.error(f"Fix failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
