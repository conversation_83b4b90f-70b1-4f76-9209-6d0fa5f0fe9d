-- Comprehensive Tenant Template SQL
-- Auto-generated from alpha schema
-- This ensures smooth tenant creation

-- Table: announcements_announcement
CREATE TABLE IF NOT EXISTS "announcements_announcement" ("id" BIGINT NOT NULL, "title" VARCHAR(255) NOT NULL, "content" TEXT NOT NULL, "publish_date" TIM<PERSON><PERSON>MP WITH TIME ZONE NOT NULL, "expiry_date" TIMESTAMP WITH TIME ZONE, "is_published" BOOLEAN NOT NULL, "is_sticky" BOOLEAN NOT NULL, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL, "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL, "author_id" BIGINT, "is_global" BOOLEAN NOT NULL, "target_all_tenant_parents" BOOLEAN NOT NULL, "target_all_tenant_staff" BOOLEAN NOT NULL, "target_global_audience_type" VARCHAR(50), "tenant_id" BIGINT);
CREATE SEQUENCE IF NOT EXISTS announcements_announcement_id_seq;
ALTER TABLE "announcements_announcement" ALTER COLUMN id SET DEFAULT nextval('announcements_announcement_id_seq');

-- Table: announcements_announcement_target_tenant_staff_groups
CREATE TABLE IF NOT EXISTS "announcements_announcement_target_tenant_staff_groups" ("id" BIGINT NOT NULL, "announcement_id" BIGINT NOT NULL, "group_id" INTEGER NOT NULL);
CREATE SEQUENCE IF NOT EXISTS announcements_announcement_target_tenant_staff_groups_id_seq;
ALTER TABLE "announcements_announcement_target_tenant_staff_groups" ALTER COLUMN id SET DEFAULT nextval('announcements_announcement_target_tenant_staff_groups_id_seq');

-- Table: auth_group
CREATE TABLE IF NOT EXISTS "auth_group" ("id" INTEGER NOT NULL, "name" VARCHAR(150) NOT NULL);
CREATE SEQUENCE IF NOT EXISTS auth_group_id_seq;
ALTER TABLE "auth_group" ALTER COLUMN id SET DEFAULT nextval('auth_group_id_seq');

-- Table: auth_group_permissions
CREATE TABLE IF NOT EXISTS "auth_group_permissions" ("id" BIGINT NOT NULL, "group_id" INTEGER NOT NULL, "permission_id" INTEGER NOT NULL);
CREATE SEQUENCE IF NOT EXISTS auth_group_permissions_id_seq;
ALTER TABLE "auth_group_permissions" ALTER COLUMN id SET DEFAULT nextval('auth_group_permissions_id_seq');

-- Table: auth_permission
CREATE TABLE IF NOT EXISTS "auth_permission" ("id" INTEGER NOT NULL, "name" VARCHAR(255) NOT NULL, "content_type_id" INTEGER NOT NULL, "codename" VARCHAR(100) NOT NULL);
CREATE SEQUENCE IF NOT EXISTS auth_permission_id_seq;
ALTER TABLE "auth_permission" ALTER COLUMN id SET DEFAULT nextval('auth_permission_id_seq');

-- Table: communication_communicationlog
CREATE TABLE IF NOT EXISTS "communication_communicationlog" ("id" BIGINT NOT NULL, "message_type" VARCHAR(30) NOT NULL, "status" VARCHAR(20) NOT NULL, "recipient_email" VARCHAR(254), "recipient_phone" VARCHAR(30), "subject" VARCHAR(255), "body_preview" TEXT, "object_id" VARCHAR(100), "sent_at" TIMESTAMP WITH TIME ZONE, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL, "error_message" TEXT, "content_type_id" INTEGER, "sent_by_id" BIGINT, "task_id" VARCHAR(255), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL);
CREATE SEQUENCE IF NOT EXISTS communication_communicationlog_id_seq;
ALTER TABLE "communication_communicationlog" ALTER COLUMN id SET DEFAULT nextval('communication_communicationlog_id_seq');

-- Table: django_admin_log
CREATE TABLE IF NOT EXISTS "django_admin_log" ("id" INTEGER NOT NULL, "action_time" TIMESTAMP WITH TIME ZONE NOT NULL, "object_id" TEXT, "object_repr" VARCHAR(200) NOT NULL, "action_flag" SMALLINT NOT NULL, "change_message" TEXT NOT NULL, "content_type_id" INTEGER, "user_id" BIGINT NOT NULL);
CREATE SEQUENCE IF NOT EXISTS django_admin_log_id_seq;
ALTER TABLE "django_admin_log" ALTER COLUMN id SET DEFAULT nextval('django_admin_log_id_seq');

-- Table: django_content_type
CREATE TABLE IF NOT EXISTS "django_content_type" ("id" INTEGER NOT NULL, "app_label" VARCHAR(100) NOT NULL, "model" VARCHAR(100) NOT NULL);
CREATE SEQUENCE IF NOT EXISTS django_content_type_id_seq;
ALTER TABLE "django_content_type" ALTER COLUMN id SET DEFAULT nextval('django_content_type_id_seq');

-- Table: django_migrations
CREATE TABLE IF NOT EXISTS "django_migrations" ("id" BIGINT NOT NULL, "app" VARCHAR(255) NOT NULL, "name" VARCHAR(255) NOT NULL, "applied" TIMESTAMP WITH TIME ZONE NOT NULL);
CREATE SEQUENCE IF NOT EXISTS django_migrations_id_seq;
ALTER TABLE "django_migrations" ALTER COLUMN id SET DEFAULT nextval('django_migrations_id_seq');

-- Table: fees_account
CREATE TABLE IF NOT EXISTS "fees_account" ("id" BIGINT NOT NULL, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL, "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL, "name" VARCHAR(100) NOT NULL, "account_number" VARCHAR(50), "account_type" VARCHAR(10) NOT NULL, "description" TEXT, "is_active" BOOLEAN NOT NULL);
CREATE SEQUENCE IF NOT EXISTS fees_account_id_seq;
ALTER TABLE "fees_account" ALTER COLUMN id SET DEFAULT nextval('fees_account_id_seq');

-- Table: fees_concessiontype
CREATE TABLE IF NOT EXISTS "fees_concessiontype" ("id" BIGINT NOT NULL, "name" VARCHAR(100) NOT NULL, "description" TEXT, "value" NUMERIC(10,2) NOT NULL, "is_active" BOOLEAN NOT NULL, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL, "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL, "type" VARCHAR(20) NOT NULL DEFAULT 'PERCENTAGE'::character varying);
CREATE SEQUENCE IF NOT EXISTS fees_concessiontype_id_seq;
ALTER TABLE "fees_concessiontype" ALTER COLUMN id SET DEFAULT nextval('fees_concessiontype_id_seq');

-- Table: fees_feehead
CREATE TABLE IF NOT EXISTS "fees_feehead" ("id" BIGINT NOT NULL, "name" VARCHAR(150) NOT NULL, "description" TEXT, "income_account_link_id" BIGINT, "is_active" BOOLEAN NOT NULL DEFAULT true, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now());
CREATE SEQUENCE IF NOT EXISTS fees_feehead_id_seq;
ALTER TABLE "fees_feehead" ALTER COLUMN id SET DEFAULT nextval('fees_feehead_id_seq');

-- Table: fees_feestructure
CREATE TABLE IF NOT EXISTS "fees_feestructure" ("id" BIGINT NOT NULL, "name" VARCHAR(200) NOT NULL, "description" TEXT, "is_active" BOOLEAN NOT NULL, "total_amount" NUMERIC(12,2) NOT NULL, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL, "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL, "academic_year_id" BIGINT NOT NULL, "term_id" BIGINT);
CREATE SEQUENCE IF NOT EXISTS fees_feestructure_id_seq;
ALTER TABLE "fees_feestructure" ALTER COLUMN id SET DEFAULT nextval('fees_feestructure_id_seq');

-- Table: fees_feestructure_applicable_classes
CREATE TABLE IF NOT EXISTS "fees_feestructure_applicable_classes" ("id" BIGINT NOT NULL, "feestructure_id" BIGINT NOT NULL, "schoolclass_id" BIGINT NOT NULL);
CREATE SEQUENCE IF NOT EXISTS fees_feestructure_applicable_classes_id_seq;
ALTER TABLE "fees_feestructure_applicable_classes" ALTER COLUMN id SET DEFAULT nextval('fees_feestructure_applicable_classes_id_seq');

-- Table: fees_feestructureitem
CREATE TABLE IF NOT EXISTS "fees_feestructureitem" ("id" BIGINT NOT NULL, "amount" NUMERIC(10,2) NOT NULL, "is_optional" BOOLEAN NOT NULL, "created_at" TIMESTAMP WITH TIME ZONE, "updated_at" TIMESTAMP WITH TIME ZONE, "concession_type_id" BIGINT, "fee_head_id" BIGINT NOT NULL, "fee_structure_id" BIGINT NOT NULL, "description" VARCHAR(255));
CREATE SEQUENCE IF NOT EXISTS fees_feestructureitem_id_seq;
ALTER TABLE "fees_feestructureitem" ALTER COLUMN id SET DEFAULT nextval('fees_feestructureitem_id_seq');

-- Table: fees_invoice
CREATE TABLE IF NOT EXISTS "fees_invoice" ("id" BIGINT NOT NULL, "invoice_number" VARCHAR(50) NOT NULL, "issue_date" DATE NOT NULL, "due_date" DATE, "subtotal_amount_calc" NUMERIC(12,2), "discount_amount_calc" NUMERIC(12,2), "total_amount" NUMERIC(12,2), "amount_paid" NUMERIC(12,2) NOT NULL, "status" VARCHAR(20) NOT NULL, "period_description_override" VARCHAR(255), "notes" TEXT, "internal_notes" TEXT, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL, "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL, "academic_year_id" BIGINT NOT NULL, "created_by_id" BIGINT, "fee_structure_id" BIGINT, "related_journal_entry_id" BIGINT, "student_id" BIGINT NOT NULL, "term_id" BIGINT, "subtotal_amount" NUMERIC(12,2) NOT NULL DEFAULT 0.00, "total_concession_amount" NUMERIC(12,2) NOT NULL DEFAULT 0.00, "notes_to_parent" TEXT);
CREATE SEQUENCE IF NOT EXISTS fees_invoice_id_seq;
ALTER TABLE "fees_invoice" ALTER COLUMN id SET DEFAULT nextval('fees_invoice_id_seq');

-- Table: fees_invoicedetail
CREATE TABLE IF NOT EXISTS "fees_invoicedetail" ("id" BIGINT NOT NULL, "line_type" VARCHAR(20) NOT NULL, "description" VARCHAR(255) NOT NULL, "quantity" NUMERIC(10,2) NOT NULL, "unit_price" NUMERIC(12,2) NOT NULL, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL, "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL, "applies_to_line_id" BIGINT, "concession_type_id" BIGINT, "fee_head_id" BIGINT, "invoice_id" BIGINT NOT NULL, "amount" NUMERIC(12,2) NOT NULL DEFAULT 0.00);
CREATE SEQUENCE IF NOT EXISTS fees_invoicedetail_id_seq;
ALTER TABLE "fees_invoicedetail" ALTER COLUMN id SET DEFAULT nextval('fees_invoicedetail_id_seq');

-- Table: fees_studentconcession
CREATE TABLE IF NOT EXISTS "fees_studentconcession" ("id" BIGINT NOT NULL, "notes" TEXT, "granted_at" TIMESTAMP WITH TIME ZONE NOT NULL, "academic_year_id" BIGINT NOT NULL, "concession_type_id" BIGINT NOT NULL, "granted_by_id" BIGINT, "student_id" BIGINT NOT NULL, "term_id" BIGINT);
CREATE SEQUENCE IF NOT EXISTS fees_studentconcession_id_seq;
ALTER TABLE "fees_studentconcession" ALTER COLUMN id SET DEFAULT nextval('fees_studentconcession_id_seq');

-- Table: fees_studentfeeallocation
CREATE TABLE IF NOT EXISTS "fees_studentfeeallocation" ("id" BIGINT NOT NULL, "effective_from" DATE NOT NULL, "effective_to" DATE, "is_active" BOOLEAN NOT NULL, "created_at" TIMESTAMP WITH TIME ZONE, "updated_at" TIMESTAMP WITH TIME ZONE, "academic_year_id" BIGINT NOT NULL, "fee_structure_id" BIGINT NOT NULL, "student_id" BIGINT NOT NULL, "term_id" BIGINT, "notes" TEXT);
CREATE SEQUENCE IF NOT EXISTS fees_studentfeeallocation_id_seq;
ALTER TABLE "fees_studentfeeallocation" ALTER COLUMN id SET DEFAULT nextval('fees_studentfeeallocation_id_seq');

-- Table: finance_budget
CREATE TABLE IF NOT EXISTS "finance_budget" ("id" BIGINT NOT NULL, "name" VARCHAR(255) NOT NULL, "financial_year_start" DATE NOT NULL, "financial_year_end" DATE NOT NULL, "description" TEXT, "is_active" BOOLEAN NOT NULL);
CREATE SEQUENCE IF NOT EXISTS finance_budget_id_seq;
ALTER TABLE "finance_budget" ALTER COLUMN id SET DEFAULT nextval('finance_budget_id_seq');

-- Table: finance_budgetamount
CREATE TABLE IF NOT EXISTS "finance_budgetamount" ("id" BIGINT NOT NULL, "created_at" TIMESTAMP WITH TIME ZONE, "updated_at" TIMESTAMP WITH TIME ZONE, "budgeted_amount" NUMERIC(12,2) NOT NULL, "notes" TEXT, "academic_year_id" BIGINT NOT NULL, "term_id" BIGINT, "budget_item_id" BIGINT NOT NULL);
CREATE SEQUENCE IF NOT EXISTS finance_budgetamount_id_seq;
ALTER TABLE "finance_budgetamount" ALTER COLUMN id SET DEFAULT nextval('finance_budgetamount_id_seq');

-- Table: finance_budgetitem
CREATE TABLE IF NOT EXISTS "finance_budgetitem" ("id" BIGINT NOT NULL, "created_at" TIMESTAMP WITH TIME ZONE, "updated_at" TIMESTAMP WITH TIME ZONE, "name" VARCHAR(150) NOT NULL, "description" TEXT, "budget_item_type" VARCHAR(10) NOT NULL, "linked_coa_account_id" BIGINT NOT NULL);
CREATE SEQUENCE IF NOT EXISTS finance_budgetitem_id_seq;
ALTER TABLE "finance_budgetitem" ALTER COLUMN id SET DEFAULT nextval('finance_budgetitem_id_seq');

-- Table: finance_expense
CREATE TABLE IF NOT EXISTS "finance_expense" ("id" BIGINT NOT NULL, "created_at" TIMESTAMP WITH TIME ZONE, "updated_at" TIMESTAMP WITH TIME ZONE, "expense_date" DATE NOT NULL, "amount" NUMERIC(12,2) NOT NULL, "description" TEXT NOT NULL, "reference_number" VARCHAR(100), "payment_method_id" BIGINT, "recorded_by_id" BIGINT, "category_id" BIGINT NOT NULL, "vendor_id" BIGINT);
CREATE SEQUENCE IF NOT EXISTS finance_expense_id_seq;
ALTER TABLE "finance_expense" ALTER COLUMN id SET DEFAULT nextval('finance_expense_id_seq');

-- Table: finance_expensecategory
CREATE TABLE IF NOT EXISTS "finance_expensecategory" ("id" BIGINT NOT NULL, "created_at" TIMESTAMP WITH TIME ZONE, "updated_at" TIMESTAMP WITH TIME ZONE, "name" VARCHAR(100) NOT NULL, "description" TEXT, "is_active" BOOLEAN NOT NULL, "expense_account_id" BIGINT NOT NULL);
CREATE SEQUENCE IF NOT EXISTS finance_expensecategory_id_seq;
ALTER TABLE "finance_expensecategory" ALTER COLUMN id SET DEFAULT nextval('finance_expensecategory_id_seq');

-- Table: finance_vendor
CREATE TABLE IF NOT EXISTS "finance_vendor" ("id" BIGINT NOT NULL, "created_at" TIMESTAMP WITH TIME ZONE, "updated_at" TIMESTAMP WITH TIME ZONE, "name" VARCHAR(150) NOT NULL, "contact_person" VARCHAR(100), "email" VARCHAR(254), "phone_number" VARCHAR(30), "address_line1" VARCHAR(255), "address_line2" VARCHAR(255), "city" VARCHAR(100), "state_province" VARCHAR(100), "postal_code" VARCHAR(20), "country" VARCHAR(100), "notes" TEXT, "is_active" BOOLEAN NOT NULL);
CREATE SEQUENCE IF NOT EXISTS finance_vendor_id_seq;
ALTER TABLE "finance_vendor" ALTER COLUMN id SET DEFAULT nextval('finance_vendor_id_seq');

-- Table: hr_employeeprofile
CREATE TABLE IF NOT EXISTS "hr_employeeprofile" ("user_id" BIGINT NOT NULL, "middle_name" VARCHAR(100) NOT NULL, "gender" VARCHAR(15), "date_of_birth" DATE, "marital_status" VARCHAR(15), "phone_number_alternate" VARCHAR(30) NOT NULL, "address_line1" VARCHAR(255) NOT NULL, "address_line2" VARCHAR(255) NOT NULL, "city" VARCHAR(100) NOT NULL, "state_province" VARCHAR(100) NOT NULL, "postal_code" VARCHAR(20) NOT NULL, "country" VARCHAR(100) NOT NULL, "employment_type" VARCHAR(20), "date_left" DATE, "photo" VARCHAR(100), "notes" TEXT NOT NULL, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL, "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL, "date_hired" DATE, "department" VARCHAR(100) NOT NULL, "designation" VARCHAR(100) NOT NULL, "employee_id" VARCHAR(50), "phone_number_primary" VARCHAR(30) NOT NULL);

-- Table: hr_graderule
CREATE TABLE IF NOT EXISTS "hr_graderule" ("id" BIGINT NOT NULL, "value" NUMERIC(10,2) NOT NULL, "component_id" BIGINT NOT NULL, "grade_id" BIGINT NOT NULL);
CREATE SEQUENCE IF NOT EXISTS hr_graderule_id_seq;
ALTER TABLE "hr_graderule" ALTER COLUMN id SET DEFAULT nextval('hr_graderule_id_seq');

-- Table: hr_leavebalance
CREATE TABLE IF NOT EXISTS "hr_leavebalance" ("id" BIGINT NOT NULL, "days_accrued" NUMERIC(5,1) NOT NULL, "last_accrual_date" DATE, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL, "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL, "employee_id" BIGINT NOT NULL, "leave_type_id" BIGINT NOT NULL, "year" INTEGER NOT NULL);
CREATE SEQUENCE IF NOT EXISTS hr_leavebalance_id_seq;
ALTER TABLE "hr_leavebalance" ALTER COLUMN id SET DEFAULT nextval('hr_leavebalance_id_seq');

-- Table: hr_leaverequest
CREATE TABLE IF NOT EXISTS "hr_leaverequest" ("id" BIGINT NOT NULL, "start_date" DATE NOT NULL, "end_date" DATE NOT NULL, "half_day_start" BOOLEAN NOT NULL, "half_day_end" BOOLEAN NOT NULL, "reason" TEXT NOT NULL, "attachment" VARCHAR(100), "status" VARCHAR(20) NOT NULL, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL, "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL, "approved_by_id" BIGINT, "employee_id" BIGINT NOT NULL, "leave_type_id" BIGINT NOT NULL, "duration" NUMERIC(5,1) NOT NULL, "status_changed_at" TIMESTAMP WITH TIME ZONE, "status_reason" TEXT);
CREATE SEQUENCE IF NOT EXISTS hr_leaverequest_id_seq;
ALTER TABLE "hr_leaverequest" ALTER COLUMN id SET DEFAULT nextval('hr_leaverequest_id_seq');

-- Table: hr_leavetype
CREATE TABLE IF NOT EXISTS "hr_leavetype" ("id" BIGINT NOT NULL, "name" VARCHAR(100) NOT NULL, "description" TEXT, "max_annual_days" NUMERIC(5,1), "is_paid" BOOLEAN NOT NULL, "requires_approval" BOOLEAN NOT NULL, "is_active" BOOLEAN NOT NULL, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL, "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL, "max_days_per_year_grant" NUMERIC(5,2), "accrual_frequency" VARCHAR(20) NOT NULL, "accrual_rate" NUMERIC(5,2) NOT NULL, "max_accrual_balance" INTEGER, "prorate_accrual" BOOLEAN NOT NULL);
CREATE SEQUENCE IF NOT EXISTS hr_leavetype_id_seq;
ALTER TABLE "hr_leavetype" ALTER COLUMN id SET DEFAULT nextval('hr_leavetype_id_seq');

-- Table: hr_payrollrun
CREATE TABLE IF NOT EXISTS "hr_payrollrun" ("id" BIGINT NOT NULL, "pay_period_start" DATE NOT NULL, "pay_period_end" DATE NOT NULL, "payment_date" DATE NOT NULL, "status" VARCHAR(20) NOT NULL, "notes" TEXT, "processed_at" TIMESTAMP WITH TIME ZONE, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL, "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL, "processed_by_id" BIGINT);
CREATE SEQUENCE IF NOT EXISTS hr_payrollrun_id_seq;
ALTER TABLE "hr_payrollrun" ALTER COLUMN id SET DEFAULT nextval('hr_payrollrun_id_seq');

-- Table: hr_payslip
CREATE TABLE IF NOT EXISTS "hr_payslip" ("id" BIGINT NOT NULL, "gross_earnings" NUMERIC(10,2) NOT NULL, "total_deductions" NUMERIC(10,2) NOT NULL, "net_pay" NUMERIC(10,2) NOT NULL, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL, "staff_member_id" BIGINT NOT NULL, "allowances" NUMERIC(10,2) NOT NULL, "basic_salary" NUMERIC(10,2) NOT NULL, "bonuses" NUMERIC(10,2) NOT NULL, "loan_repayments" NUMERIC(10,2) NOT NULL, "notes" TEXT, "other_deductions" NUMERIC(10,2) NOT NULL, "pension_deductions" NUMERIC(10,2) NOT NULL, "tax_deductions" NUMERIC(10,2) NOT NULL, "payroll_run_id" BIGINT, "adjustments" NUMERIC(10,2) NOT NULL);
CREATE SEQUENCE IF NOT EXISTS hr_payslip_id_seq;
ALTER TABLE "hr_payslip" ALTER COLUMN id SET DEFAULT nextval('hr_payslip_id_seq');

-- Table: hr_paysliplineitem
CREATE TABLE IF NOT EXISTS "hr_paysliplineitem" ("id" BIGINT NOT NULL, "name" VARCHAR(100) NOT NULL, "type" VARCHAR(10) NOT NULL, "amount" NUMERIC(12,2) NOT NULL, "payslip_id" BIGINT NOT NULL, "source_component_id" BIGINT);
CREATE SEQUENCE IF NOT EXISTS hr_paysliplineitem_id_seq;
ALTER TABLE "hr_paysliplineitem" ALTER COLUMN id SET DEFAULT nextval('hr_paysliplineitem_id_seq');

-- Table: hr_salarycomponent
CREATE TABLE IF NOT EXISTS "hr_salarycomponent" ("id" BIGINT NOT NULL, "name" VARCHAR(100) NOT NULL, "type" VARCHAR(10) NOT NULL, "description" TEXT, "is_percentage" BOOLEAN NOT NULL);
CREATE SEQUENCE IF NOT EXISTS hr_salarycomponent_id_seq;
ALTER TABLE "hr_salarycomponent" ALTER COLUMN id SET DEFAULT nextval('hr_salarycomponent_id_seq');

-- Table: hr_salarygrade
CREATE TABLE IF NOT EXISTS "hr_salarygrade" ("id" BIGINT NOT NULL, "name" VARCHAR(100) NOT NULL, "description" TEXT NOT NULL, "is_active" BOOLEAN NOT NULL);
CREATE SEQUENCE IF NOT EXISTS hr_salarygrade_id_seq;
ALTER TABLE "hr_salarygrade" ALTER COLUMN id SET DEFAULT nextval('hr_salarygrade_id_seq');

-- Table: hr_salarygradecomponent
CREATE TABLE IF NOT EXISTS "hr_salarygradecomponent" ("id" BIGINT NOT NULL, "amount" NUMERIC(10,2) NOT NULL, "component_id" BIGINT NOT NULL, "grade_id" BIGINT NOT NULL);
CREATE SEQUENCE IF NOT EXISTS hr_salarygradecomponent_id_seq;
ALTER TABLE "hr_salarygradecomponent" ALTER COLUMN id SET DEFAULT nextval('hr_salarygradecomponent_id_seq');

-- Table: hr_salarystructurecomponent
CREATE TABLE IF NOT EXISTS "hr_salarystructurecomponent" ("id" BIGINT NOT NULL, "amount" NUMERIC(10,2) NOT NULL, "component_id" BIGINT NOT NULL, "salary_structure_id" BIGINT NOT NULL);
CREATE SEQUENCE IF NOT EXISTS hr_salarystructurecomponent_id_seq;
ALTER TABLE "hr_salarystructurecomponent" ALTER COLUMN id SET DEFAULT nextval('hr_salarystructurecomponent_id_seq');

-- Table: hr_staffsalary
CREATE TABLE IF NOT EXISTS "hr_staffsalary" ("id" BIGINT NOT NULL, "basic_salary" NUMERIC(10,2) NOT NULL, "effective_from" DATE NOT NULL, "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL, "staff_member_id" BIGINT NOT NULL, "grade_id" BIGINT);
CREATE SEQUENCE IF NOT EXISTS hr_staffsalary_id_seq;
ALTER TABLE "hr_staffsalary" ALTER COLUMN id SET DEFAULT nextval('hr_staffsalary_id_seq');

-- Table: hr_staffsalarystructure
CREATE TABLE IF NOT EXISTS "hr_staffsalarystructure" ("id" BIGINT NOT NULL, "effective_date" DATE NOT NULL, "staff_user_id" BIGINT NOT NULL);
CREATE SEQUENCE IF NOT EXISTS hr_staffsalarystructure_id_seq;
ALTER TABLE "hr_staffsalarystructure" ALTER COLUMN id SET DEFAULT nextval('hr_staffsalarystructure_id_seq');

-- Table: hr_statutorydeduction
CREATE TABLE IF NOT EXISTS "hr_statutorydeduction" ("id" BIGINT NOT NULL, "name" VARCHAR(100) NOT NULL, "employee_contribution_rate" NUMERIC(5,2) NOT NULL, "employer_contribution_rate" NUMERIC(5,2) NOT NULL, "is_active" BOOLEAN NOT NULL, "payslip_label" VARCHAR(20) NOT NULL);
CREATE SEQUENCE IF NOT EXISTS hr_statutorydeduction_id_seq;
ALTER TABLE "hr_statutorydeduction" ALTER COLUMN id SET DEFAULT nextval('hr_statutorydeduction_id_seq');

-- Table: hr_taxbracket
CREATE TABLE IF NOT EXISTS "hr_taxbracket" ("id" BIGINT NOT NULL, "name" VARCHAR(100) NOT NULL, "from_amount" NUMERIC(12,2) NOT NULL, "to_amount" NUMERIC(12,2), "rate_percent" NUMERIC(5,2) NOT NULL, "deduction_amount" NUMERIC(12,2) NOT NULL, "is_active" BOOLEAN NOT NULL);
CREATE SEQUENCE IF NOT EXISTS hr_taxbracket_id_seq;
ALTER TABLE "hr_taxbracket" ALTER COLUMN id SET DEFAULT nextval('hr_taxbracket_id_seq');

-- Table: payments_payment
CREATE TABLE IF NOT EXISTS "payments_payment" ("id" BIGINT NOT NULL, "amount" NUMERIC(12,2) NOT NULL, "payment_date" TIMESTAMP WITH TIME ZONE NOT NULL, "transaction_reference" VARCHAR(150), "notes" TEXT, "payment_type" VARCHAR(20) NOT NULL, "status" VARCHAR(20) NOT NULL, "unallocated_amount" NUMERIC(12,2) NOT NULL, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL, "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL, "academic_year_id" BIGINT, "created_by_id" BIGINT, "parent_payer_id" BIGINT, "processed_by_staff_id" BIGINT, "student_id" BIGINT, "payment_method_id" BIGINT, "receipt_number" VARCHAR(20), "payment_reference" VARCHAR(100) DEFAULT ''::character varying);
CREATE SEQUENCE IF NOT EXISTS payments_payment_id_seq;
ALTER TABLE "payments_payment" ALTER COLUMN id SET DEFAULT nextval('payments_payment_id_seq');

-- Table: payments_paymentallocation
CREATE TABLE IF NOT EXISTS "payments_paymentallocation" ("id" BIGINT NOT NULL, "amount_allocated" NUMERIC(12,2) NOT NULL, "allocation_date" DATE NOT NULL, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL, "invoice_id" BIGINT, "payment_id" BIGINT);
CREATE SEQUENCE IF NOT EXISTS payments_paymentallocation_id_seq;
ALTER TABLE "payments_paymentallocation" ALTER COLUMN id SET DEFAULT nextval('payments_paymentallocation_id_seq');

-- Table: payments_paymentmethod
CREATE TABLE IF NOT EXISTS "payments_paymentmethod" ("id" BIGINT NOT NULL, "created_at" TIMESTAMP WITH TIME ZONE, "updated_at" TIMESTAMP WITH TIME ZONE, "name" VARCHAR(100) NOT NULL, "type" VARCHAR(30) NOT NULL, "description" TEXT, "is_active" BOOLEAN NOT NULL, "linked_account_id" BIGINT);
CREATE SEQUENCE IF NOT EXISTS payments_paymentmethod_id_seq;
ALTER TABLE "payments_paymentmethod" ALTER COLUMN id SET DEFAULT nextval('payments_paymentmethod_id_seq');

-- Table: portal_admin_adminactivitylog
CREATE TABLE IF NOT EXISTS "portal_admin_adminactivitylog" ("id" BIGINT NOT NULL, "action_type" VARCHAR(50) NOT NULL, "timestamp" TIMESTAMP WITH TIME ZONE NOT NULL, "actor_description" VARCHAR(255), "ip_address" INET, "user_agent" TEXT NOT NULL, "target_object_id" VARCHAR(255), "target_object_repr" VARCHAR(300), "description" TEXT NOT NULL, "staff_user_id" BIGINT, "target_content_type_id" INTEGER, "tenant_id" BIGINT, "user_id" BIGINT);
CREATE SEQUENCE IF NOT EXISTS portal_admin_adminactivitylog_id_seq;
ALTER TABLE "portal_admin_adminactivitylog" ALTER COLUMN id SET DEFAULT nextval('portal_admin_adminactivitylog_id_seq');

-- Table: school_calendar_eventattendee
CREATE TABLE IF NOT EXISTS "school_calendar_eventattendee" ("id" BIGINT NOT NULL, "rsvp_status" VARCHAR(15) NOT NULL, "rsvp_date" TIMESTAMP WITH TIME ZONE NOT NULL, "notes" TEXT NOT NULL, "user_id" BIGINT NOT NULL, "event_id" BIGINT NOT NULL);
CREATE SEQUENCE IF NOT EXISTS school_calendar_eventattendee_id_seq;
ALTER TABLE "school_calendar_eventattendee" ALTER COLUMN id SET DEFAULT nextval('school_calendar_eventattendee_id_seq');

-- Table: school_calendar_eventcategory
CREATE TABLE IF NOT EXISTS "school_calendar_eventcategory" ("id" BIGINT NOT NULL, "name" VARCHAR(100) NOT NULL, "color" VARCHAR(7) NOT NULL, "icon" VARCHAR(50) NOT NULL, "description" TEXT NOT NULL, "is_active" BOOLEAN NOT NULL, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL, "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL);
CREATE SEQUENCE IF NOT EXISTS school_calendar_eventcategory_id_seq;
ALTER TABLE "school_calendar_eventcategory" ALTER COLUMN id SET DEFAULT nextval('school_calendar_eventcategory_id_seq');

-- Table: school_calendar_schoolevent
CREATE TABLE IF NOT EXISTS "school_calendar_schoolevent" ("id" BIGINT NOT NULL, "title" VARCHAR(200) NOT NULL, "description" TEXT NOT NULL, "event_type" VARCHAR(20) NOT NULL, "priority" VARCHAR(10) NOT NULL, "start_date" DATE NOT NULL, "end_date" DATE NOT NULL, "start_time" TIME WITHOUT TIME ZONE, "end_time" TIME WITHOUT TIME ZONE, "is_all_day" BOOLEAN NOT NULL, "location" VARCHAR(200) NOT NULL, "venue_details" TEXT NOT NULL, "recurrence" VARCHAR(10) NOT NULL, "recurrence_end_date" DATE, "is_public" BOOLEAN NOT NULL, "visible_to_parents" BOOLEAN NOT NULL, "visible_to_staff" BOOLEAN NOT NULL, "visible_to_students" BOOLEAN NOT NULL, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL, "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL, "is_active" BOOLEAN NOT NULL, "requires_rsvp" BOOLEAN NOT NULL, "max_attendees" INTEGER, "contact_person" VARCHAR(100) NOT NULL, "contact_email" VARCHAR(254) NOT NULL, "contact_phone" VARCHAR(20) NOT NULL, "category_id" BIGINT, "created_by_id" BIGINT, "created_by_staff_id" BIGINT);
CREATE SEQUENCE IF NOT EXISTS school_calendar_schoolevent_id_seq;
ALTER TABLE "school_calendar_schoolevent" ALTER COLUMN id SET DEFAULT nextval('school_calendar_schoolevent_id_seq');

-- Table: schools_academicsetting
CREATE TABLE IF NOT EXISTS "schools_academicsetting" ("id" BIGINT NOT NULL, "grading_system" VARCHAR(20) NOT NULL, "current_academic_year_id" BIGINT);
CREATE SEQUENCE IF NOT EXISTS schools_academicsetting_id_seq;
ALTER TABLE "schools_academicsetting" ALTER COLUMN id SET DEFAULT nextval('schools_academicsetting_id_seq');

-- Table: schools_academicyear
CREATE TABLE IF NOT EXISTS "schools_academicyear" ("id" BIGINT NOT NULL, "name" VARCHAR(100) NOT NULL, "start_date" DATE NOT NULL, "end_date" DATE NOT NULL, "is_active" BOOLEAN NOT NULL, "is_current" BOOLEAN NOT NULL, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL, "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL);
CREATE SEQUENCE IF NOT EXISTS schools_academicyear_id_seq;
ALTER TABLE "schools_academicyear" ALTER COLUMN id SET DEFAULT nextval('schools_academicyear_id_seq');

-- Table: schools_country
CREATE TABLE IF NOT EXISTS "schools_country" ("id" BIGINT NOT NULL, "name" VARCHAR(100) NOT NULL, "code" VARCHAR(3) NOT NULL);
CREATE SEQUENCE IF NOT EXISTS schools_country_id_seq;
ALTER TABLE "schools_country" ALTER COLUMN id SET DEFAULT nextval('schools_country_id_seq');

-- Table: schools_invoicesequence
CREATE TABLE IF NOT EXISTS "schools_invoicesequence" ("id" BIGINT NOT NULL, "prefix" VARCHAR(10) NOT NULL, "last_number" INTEGER NOT NULL, "padding_digits" SMALLINT NOT NULL, "last_updated" TIMESTAMP WITH TIME ZONE NOT NULL, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL);
CREATE SEQUENCE IF NOT EXISTS schools_invoicesequence_id_seq;
ALTER TABLE "schools_invoicesequence" ALTER COLUMN id SET DEFAULT nextval('schools_invoicesequence_id_seq');

-- Table: schools_receiptsequence
CREATE TABLE IF NOT EXISTS "schools_receiptsequence" ("id" BIGINT NOT NULL, "prefix" VARCHAR(10) NOT NULL, "last_number" INTEGER NOT NULL, "padding_digits" SMALLINT NOT NULL, "last_updated" TIMESTAMP WITH TIME ZONE NOT NULL, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL);
CREATE SEQUENCE IF NOT EXISTS schools_receiptsequence_id_seq;
ALTER TABLE "schools_receiptsequence" ALTER COLUMN id SET DEFAULT nextval('schools_receiptsequence_id_seq');

-- Table: schools_schoolclass
CREATE TABLE IF NOT EXISTS "schools_schoolclass" ("id" BIGINT NOT NULL, "name" VARCHAR(100) NOT NULL, "description" TEXT, "is_active" BOOLEAN NOT NULL, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL, "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL);
CREATE SEQUENCE IF NOT EXISTS schools_schoolclass_id_seq;
ALTER TABLE "schools_schoolclass" ALTER COLUMN id SET DEFAULT nextval('schools_schoolclass_id_seq');

-- Table: schools_schoolprofile
CREATE TABLE IF NOT EXISTS "schools_schoolprofile" ("school_name_override" VARCHAR(255), "school_motto" VARCHAR(255), "logo" VARCHAR(100), "school_email" VARCHAR(254), "phone_number" VARCHAR(30), "address_line1" VARCHAR(255), "address_line2" VARCHAR(255), "city" VARCHAR(100), "state_province" VARCHAR(100), "postal_code" VARCHAR(20), "country_name" VARCHAR(100), "financial_year_start_month" SMALLINT NOT NULL, "currency_symbol" VARCHAR(5), "school_name_on_reports" VARCHAR(255), "default_due_days" INTEGER NOT NULL, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL, "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL, "current_academic_year_id" BIGINT, "default_accounts_receivable_coa_id" BIGINT, "default_bank_coa_id" BIGINT, "default_cash_coa_id" BIGINT, "default_discount_given_coa_id" BIGINT, "default_expense_coa_id" BIGINT, "default_fee_income_coa_id" BIGINT, "id" BIGINT NOT NULL);
CREATE SEQUENCE IF NOT EXISTS schools_schoolprofile_id_seq;
ALTER TABLE "schools_schoolprofile" ALTER COLUMN id SET DEFAULT nextval('schools_schoolprofile_id_seq');

-- Table: schools_section
CREATE TABLE IF NOT EXISTS "schools_section" ("id" BIGINT NOT NULL, "name" VARCHAR(50) NOT NULL, "room_number" VARCHAR(50) NOT NULL, "school_class_id" BIGINT NOT NULL, "class_teacher_id" BIGINT);
CREATE SEQUENCE IF NOT EXISTS schools_section_id_seq;
ALTER TABLE "schools_section" ALTER COLUMN id SET DEFAULT nextval('schools_section_id_seq');

-- Table: schools_staffuser
CREATE TABLE IF NOT EXISTS "schools_staffuser" ("id" BIGINT NOT NULL, "password" VARCHAR(128) NOT NULL, "last_login" TIMESTAMP WITH TIME ZONE, "is_superuser" BOOLEAN NOT NULL, "email" VARCHAR(254) NOT NULL, "first_name" VARCHAR(150) NOT NULL, "last_name" VARCHAR(150) NOT NULL, "is_active" BOOLEAN NOT NULL, "is_staff" BOOLEAN NOT NULL, "date_joined" TIMESTAMP WITH TIME ZONE NOT NULL, "employee_id" VARCHAR(50) DEFAULT ''::character varying, "designation" VARCHAR(100) DEFAULT ''::character varying);
CREATE SEQUENCE IF NOT EXISTS schools_staffuser_id_seq;
ALTER TABLE "schools_staffuser" ALTER COLUMN id SET DEFAULT nextval('schools_staffuser_id_seq');

-- Table: schools_staffuser_groups
CREATE TABLE IF NOT EXISTS "schools_staffuser_groups" ("id" BIGINT NOT NULL, "staffuser_id" BIGINT NOT NULL, "group_id" INTEGER NOT NULL);
CREATE SEQUENCE IF NOT EXISTS schools_staffuser_groups_id_seq;
ALTER TABLE "schools_staffuser_groups" ALTER COLUMN id SET DEFAULT nextval('schools_staffuser_groups_id_seq');

-- Table: schools_staffuser_user_permissions
CREATE TABLE IF NOT EXISTS "schools_staffuser_user_permissions" ("id" BIGINT NOT NULL, "staffuser_id" BIGINT NOT NULL, "permission_id" INTEGER NOT NULL);
CREATE SEQUENCE IF NOT EXISTS schools_staffuser_user_permissions_id_seq;
ALTER TABLE "schools_staffuser_user_permissions" ALTER COLUMN id SET DEFAULT nextval('schools_staffuser_user_permissions_id_seq');

-- Table: schools_term
CREATE TABLE IF NOT EXISTS "schools_term" ("id" BIGINT NOT NULL, "name" VARCHAR(100) NOT NULL, "start_date" DATE NOT NULL, "end_date" DATE NOT NULL, "is_active" BOOLEAN NOT NULL, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL, "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL, "academic_year_id" BIGINT NOT NULL);
CREATE SEQUENCE IF NOT EXISTS schools_term_id_seq;
ALTER TABLE "schools_term" ALTER COLUMN id SET DEFAULT nextval('schools_term_id_seq');

-- Table: students_parentuser
CREATE TABLE IF NOT EXISTS "students_parentuser" ("id" BIGINT NOT NULL, "password" VARCHAR(128) NOT NULL, "last_login" TIMESTAMP WITH TIME ZONE, "is_superuser" BOOLEAN NOT NULL, "email" VARCHAR(254) NOT NULL, "username" VARCHAR(150), "first_name" VARCHAR(150) NOT NULL, "last_name" VARCHAR(150) NOT NULL, "phone_number" VARCHAR(20) NOT NULL, "address_line1" VARCHAR(255), "address_line2" VARCHAR(255), "city" VARCHAR(100), "state_province" VARCHAR(100), "postal_code" VARCHAR(20), "country" VARCHAR(100), "profile_picture" VARCHAR(100), "is_active" BOOLEAN NOT NULL, "is_staff" BOOLEAN NOT NULL, "date_joined" TIMESTAMP WITH TIME ZONE NOT NULL);
CREATE SEQUENCE IF NOT EXISTS students_parentuser_id_seq;
ALTER TABLE "students_parentuser" ALTER COLUMN id SET DEFAULT nextval('students_parentuser_id_seq');

-- Table: students_parentuser_groups
CREATE TABLE IF NOT EXISTS "students_parentuser_groups" ("id" BIGINT NOT NULL, "parentuser_id" BIGINT NOT NULL, "group_id" INTEGER NOT NULL);
CREATE SEQUENCE IF NOT EXISTS students_parentuser_groups_id_seq;
ALTER TABLE "students_parentuser_groups" ALTER COLUMN id SET DEFAULT nextval('students_parentuser_groups_id_seq');

-- Table: students_parentuser_user_permissions
CREATE TABLE IF NOT EXISTS "students_parentuser_user_permissions" ("id" BIGINT NOT NULL, "parentuser_id" BIGINT NOT NULL, "permission_id" INTEGER NOT NULL);
CREATE SEQUENCE IF NOT EXISTS students_parentuser_user_permissions_id_seq;
ALTER TABLE "students_parentuser_user_permissions" ALTER COLUMN id SET DEFAULT nextval('students_parentuser_user_permissions_id_seq');

-- Table: students_student
CREATE TABLE IF NOT EXISTS "students_student" ("id" BIGINT NOT NULL, "admission_number" VARCHAR(50) NOT NULL, "first_name" VARCHAR(100) NOT NULL, "middle_name" VARCHAR(100) NOT NULL, "last_name" VARCHAR(100) NOT NULL, "date_of_birth" DATE, "gender" VARCHAR(15) NOT NULL, "photo" VARCHAR(100), "date_of_admission" DATE NOT NULL, "status" VARCHAR(20) NOT NULL, "is_active" BOOLEAN NOT NULL, "roll_number" VARCHAR(20) NOT NULL, "student_email" VARCHAR(254) NOT NULL, "student_phone" VARCHAR(30) NOT NULL, "guardian1_full_name" VARCHAR(150) NOT NULL, "guardian1_relationship" VARCHAR(50) NOT NULL, "guardian1_phone" VARCHAR(30) NOT NULL, "guardian1_email" VARCHAR(254) NOT NULL, "guardian1_occupation" VARCHAR(100) NOT NULL, "guardian2_full_name" VARCHAR(150) NOT NULL, "guardian2_relationship" VARCHAR(50) NOT NULL, "guardian2_phone" VARCHAR(30) NOT NULL, "guardian2_email" VARCHAR(254) NOT NULL, "address_line1" VARCHAR(255) NOT NULL, "address_line2" VARCHAR(255) NOT NULL, "city" VARCHAR(100) NOT NULL, "state_province" VARCHAR(100) NOT NULL, "postal_code" VARCHAR(20) NOT NULL, "country" VARCHAR(100) NOT NULL, "blood_group" VARCHAR(10) NOT NULL, "allergies" TEXT NOT NULL, "medical_conditions" TEXT NOT NULL, "previous_school" VARCHAR(200) NOT NULL, "notes" TEXT NOT NULL, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL, "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL, "current_class_id" BIGINT, "current_section_id" BIGINT, "created_by_id" BIGINT, "emergency_contact" VARCHAR(200) NOT NULL, "emergency_phone" VARCHAR(20) NOT NULL, "house" VARCHAR(100) NOT NULL, "nationality" VARCHAR(100) NOT NULL, "religion" VARCHAR(100) NOT NULL, "transport_mode" VARCHAR(50) NOT NULL);
CREATE SEQUENCE IF NOT EXISTS students_student_id_seq;
ALTER TABLE "students_student" ALTER COLUMN id SET DEFAULT nextval('students_student_id_seq');

-- Table: students_student_parents
CREATE TABLE IF NOT EXISTS "students_student_parents" ("id" BIGINT NOT NULL, "student_id" BIGINT NOT NULL, "parentuser_id" BIGINT NOT NULL);
CREATE SEQUENCE IF NOT EXISTS students_student_parents_id_seq;
ALTER TABLE "students_student_parents" ALTER COLUMN id SET DEFAULT nextval('students_student_parents_id_seq');

-- Essential Data
-- Data for auth_group
DELETE FROM auth_group;
INSERT INTO auth_group ("id", "name") VALUES (2, 'Accountant');
INSERT INTO auth_group ("id", "name") VALUES (1, 'School Administrators');
INSERT INTO auth_group ("id", "name") VALUES (3, 'School Administrator');
INSERT INTO auth_group ("id", "name") VALUES (4, 'Teacher');
INSERT INTO auth_group ("id", "name") VALUES (5, 'HR Manager');
INSERT INTO auth_group ("id", "name") VALUES (6, 'Librarian');
INSERT INTO auth_group ("id", "name") VALUES (7, 'Reception/Front Desk');
INSERT INTO auth_group ("id", "name") VALUES (8, 'Principal/Head Teacher');

-- Data for auth_permission
DELETE FROM auth_permission;
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (1, 'Can add log entry', 1, 'add_logentry');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (2, 'Can change log entry', 1, 'change_logentry');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (3, 'Can delete log entry', 1, 'delete_logentry');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (4, 'Can view log entry', 1, 'view_logentry');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (5, 'Can add permission', 2, 'add_permission');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (6, 'Can change permission', 2, 'change_permission');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (7, 'Can delete permission', 2, 'delete_permission');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (8, 'Can view permission', 2, 'view_permission');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (9, 'Can add group', 3, 'add_group');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (10, 'Can change group', 3, 'change_group');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (11, 'Can delete group', 3, 'delete_group');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (12, 'Can view group', 3, 'view_group');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (13, 'Can add content type', 4, 'add_contenttype');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (14, 'Can change content type', 4, 'change_contenttype');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (15, 'Can delete content type', 4, 'delete_contenttype');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (16, 'Can view content type', 4, 'view_contenttype');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (17, 'Can add session', 5, 'add_session');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (18, 'Can change session', 5, 'change_session');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (19, 'Can delete session', 5, 'delete_session');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (20, 'Can view session', 5, 'view_session');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (21, 'Can add School Domain', 6, 'add_domain');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (22, 'Can change School Domain', 6, 'change_domain');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (23, 'Can delete School Domain', 6, 'delete_domain');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (24, 'Can view School Domain', 6, 'view_domain');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (25, 'Can add School (Tenant)', 7, 'add_school');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (26, 'Can change School (Tenant)', 7, 'change_school');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (27, 'Can delete School (Tenant)', 7, 'delete_school');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (28, 'Can view School (Tenant)', 7, 'view_school');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (29, 'Can add Platform User', 8, 'add_user');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (30, 'Can change Platform User', 8, 'change_user');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (31, 'Can delete Platform User', 8, 'delete_user');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (32, 'Can view Platform User', 8, 'view_user');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (33, 'Can add contact inquiry', 9, 'add_contactinquiry');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (34, 'Can change contact inquiry', 9, 'change_contactinquiry');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (35, 'Can delete contact inquiry', 9, 'delete_contactinquiry');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (36, 'Can view contact inquiry', 9, 'view_contactinquiry');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (37, 'Can add Testimonial', 10, 'add_testimonial');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (38, 'Can change Testimonial', 10, 'change_testimonial');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (39, 'Can delete Testimonial', 10, 'delete_testimonial');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (40, 'Can view Testimonial', 10, 'view_testimonial');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (41, 'Can add Account Type', 11, 'add_accounttype');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (42, 'Can change Account Type', 11, 'change_accounttype');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (43, 'Can delete Account Type', 11, 'delete_accounttype');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (44, 'Can view Account Type', 11, 'view_accounttype');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (45, 'Can add General Ledger Transaction', 12, 'add_generalledger');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (46, 'Can change General Ledger Transaction', 12, 'change_generalledger');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (47, 'Can delete General Ledger Transaction', 12, 'delete_generalledger');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (48, 'Can view General Ledger Transaction', 12, 'view_generalledger');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (49, 'Can add Journal Entry', 13, 'add_journalentry');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (50, 'Can change Journal Entry', 13, 'change_journalentry');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (51, 'Can delete Journal Entry', 13, 'delete_journalentry');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (52, 'Can view Journal Entry', 13, 'view_journalentry');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (53, 'Can add Journal Entry Item', 14, 'add_journalentryitem');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (54, 'Can change Journal Entry Item', 14, 'change_journalentryitem');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (55, 'Can delete Journal Entry Item', 14, 'delete_journalentryitem');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (56, 'Can view Journal Entry Item', 14, 'view_journalentryitem');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (57, 'Can add Journal Line', 15, 'add_journalline');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (58, 'Can change Journal Line', 15, 'change_journalline');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (59, 'Can delete Journal Line', 15, 'delete_journalline');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (60, 'Can view Journal Line', 15, 'view_journalline');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (61, 'Can add Chart of Account Entry', 16, 'add_account');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (62, 'Can change Chart of Account Entry', 16, 'change_account');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (63, 'Can delete Chart of Account Entry', 16, 'delete_account');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (64, 'Can view Chart of Account Entry', 16, 'view_account');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (65, 'Can add Plan Feature', 17, 'add_feature');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (66, 'Can change Plan Feature', 17, 'change_feature');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (67, 'Can delete Plan Feature', 17, 'delete_feature');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (68, 'Can view Plan Feature', 17, 'view_feature');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (69, 'Can add School Subscription', 18, 'add_subscription');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (70, 'Can change School Subscription', 18, 'change_subscription');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (71, 'Can delete School Subscription', 18, 'delete_subscription');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (72, 'Can view School Subscription', 18, 'view_subscription');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (73, 'Can add Subscription Plan', 19, 'add_subscriptionplan');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (74, 'Can change Subscription Plan', 19, 'change_subscriptionplan');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (75, 'Can delete Subscription Plan', 19, 'delete_subscriptionplan');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (76, 'Can view Subscription Plan', 19, 'view_subscriptionplan');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (77, 'Can add crontab', 20, 'add_crontabschedule');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (78, 'Can change crontab', 20, 'change_crontabschedule');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (79, 'Can delete crontab', 20, 'delete_crontabschedule');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (80, 'Can view crontab', 20, 'view_crontabschedule');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (81, 'Can add interval', 21, 'add_intervalschedule');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (82, 'Can change interval', 21, 'change_intervalschedule');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (83, 'Can delete interval', 21, 'delete_intervalschedule');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (84, 'Can view interval', 21, 'view_intervalschedule');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (85, 'Can add periodic task', 22, 'add_periodictask');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (86, 'Can change periodic task', 22, 'change_periodictask');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (87, 'Can delete periodic task', 22, 'delete_periodictask');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (88, 'Can view periodic task', 22, 'view_periodictask');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (89, 'Can add periodic task track', 23, 'add_periodictasks');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (90, 'Can change periodic task track', 23, 'change_periodictasks');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (91, 'Can delete periodic task track', 23, 'delete_periodictasks');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (92, 'Can view periodic task track', 23, 'view_periodictasks');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (93, 'Can add solar event', 24, 'add_solarschedule');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (94, 'Can change solar event', 24, 'change_solarschedule');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (95, 'Can delete solar event', 24, 'delete_solarschedule');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (96, 'Can view solar event', 24, 'view_solarschedule');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (97, 'Can add clocked', 25, 'add_clockedschedule');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (98, 'Can change clocked', 25, 'change_clockedschedule');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (99, 'Can delete clocked', 25, 'delete_clockedschedule');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (100, 'Can view clocked', 25, 'view_clockedschedule');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (101, 'Can add Academic Setting', 26, 'add_academicsetting');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (102, 'Can change Academic Setting', 26, 'change_academicsetting');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (103, 'Can delete Academic Setting', 26, 'delete_academicsetting');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (104, 'Can view Academic Setting', 26, 'view_academicsetting');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (105, 'Can add Academic Year', 27, 'add_academicyear');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (106, 'Can change Academic Year', 27, 'change_academicyear');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (107, 'Can delete Academic Year', 27, 'delete_academicyear');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (108, 'Can view Academic Year', 27, 'view_academicyear');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (109, 'Can add country', 28, 'add_country');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (110, 'Can change country', 28, 'change_country');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (111, 'Can delete country', 28, 'delete_country');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (112, 'Can view country', 28, 'view_country');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (113, 'Can add School Class', 29, 'add_schoolclass');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (114, 'Can change School Class', 29, 'change_schoolclass');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (115, 'Can delete School Class', 29, 'delete_schoolclass');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (116, 'Can view School Class', 29, 'view_schoolclass');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (117, 'Can add School Profile', 30, 'add_schoolprofile');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (118, 'Can change School Profile', 30, 'change_schoolprofile');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (119, 'Can delete School Profile', 30, 'delete_schoolprofile');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (120, 'Can view School Profile', 30, 'view_schoolprofile');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (121, 'Can view outstanding fees report', 30, 'view_outstanding_fees_report');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (122, 'Can view fee collection report', 30, 'view_fee_collection_report');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (123, 'Can view expense report', 30, 'view_expense_report');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (124, 'Can view revenue report', 30, 'view_revenue_report');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (125, 'Can manage school profile settings', 30, 'manage_school_profile');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (126, 'Can manage academic year and term settings', 30, 'manage_academic_settings');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (127, 'Can add staff member', 31, 'add_staffuser');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (128, 'Can change staff member', 31, 'change_staffuser');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (129, 'Can delete staff member', 31, 'delete_staffuser');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (130, 'Can view staff member', 31, 'view_staffuser');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (131, 'Can add Section', 32, 'add_section');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (132, 'Can change Section', 32, 'change_section');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (133, 'Can delete Section', 32, 'delete_section');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (134, 'Can view Section', 32, 'view_section');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (135, 'Can add Term / Semester', 33, 'add_term');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (136, 'Can change Term / Semester', 33, 'change_term');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (137, 'Can delete Term / Semester', 33, 'delete_term');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (138, 'Can view Term / Semester', 33, 'view_term');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (139, 'Can add parent user', 34, 'add_parentuser');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (140, 'Can change parent user', 34, 'change_parentuser');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (141, 'Can delete parent user', 34, 'delete_parentuser');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (142, 'Can view parent user', 34, 'view_parentuser');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (143, 'Can add student', 35, 'add_student');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (144, 'Can change student', 35, 'change_student');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (145, 'Can delete student', 35, 'delete_student');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (146, 'Can view student', 35, 'view_student');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (147, 'Can add employee HR profile', 36, 'add_employeeprofile');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (148, 'Can change employee HR profile', 36, 'change_employeeprofile');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (149, 'Can delete employee HR profile', 36, 'delete_employeeprofile');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (150, 'Can view employee HR profile', 36, 'view_employeeprofile');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (151, 'Can add leave type', 37, 'add_leavetype');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (152, 'Can change leave type', 37, 'change_leavetype');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (153, 'Can delete leave type', 37, 'delete_leavetype');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (154, 'Can view leave type', 37, 'view_leavetype');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (155, 'Can add leave request', 38, 'add_leaverequest');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (156, 'Can change leave request', 38, 'change_leaverequest');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (157, 'Can delete leave request', 38, 'delete_leaverequest');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (158, 'Can view leave request', 38, 'view_leaverequest');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (159, 'Can add leave balance', 39, 'add_leavebalance');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (160, 'Can change leave balance', 39, 'change_leavebalance');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (161, 'Can delete leave balance', 39, 'delete_leavebalance');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (162, 'Can view leave balance', 39, 'view_leavebalance');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (163, 'Can add Account', 40, 'add_account');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (164, 'Can change Account', 40, 'change_account');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (165, 'Can delete Account', 40, 'delete_account');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (166, 'Can view Account', 40, 'view_account');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (167, 'Can add Concession Type', 41, 'add_concessiontype');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (168, 'Can change Concession Type', 41, 'change_concessiontype');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (169, 'Can delete Concession Type', 41, 'delete_concessiontype');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (170, 'Can view Concession Type', 41, 'view_concessiontype');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (171, 'Can add Fee Structure', 42, 'add_feestructure');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (172, 'Can change Fee Structure', 42, 'change_feestructure');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (173, 'Can delete Fee Structure', 42, 'delete_feestructure');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (174, 'Can view Fee Structure', 42, 'view_feestructure');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (175, 'Can add Fee Structure Item', 43, 'add_feestructureitem');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (176, 'Can change Fee Structure Item', 43, 'change_feestructureitem');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (177, 'Can delete Fee Structure Item', 43, 'delete_feestructureitem');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (178, 'Can view Fee Structure Item', 43, 'view_feestructureitem');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (179, 'Can add Invoice', 44, 'add_invoice');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (180, 'Can change Invoice', 44, 'change_invoice');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (181, 'Can delete Invoice', 44, 'delete_invoice');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (182, 'Can view Invoice', 44, 'view_invoice');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (183, 'Can add Invoice Detail', 45, 'add_invoicedetail');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (184, 'Can change Invoice Detail', 45, 'change_invoicedetail');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (185, 'Can delete Invoice Detail', 45, 'delete_invoicedetail');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (186, 'Can view Invoice Detail', 45, 'view_invoicedetail');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (187, 'Can add Student Specific Concession', 46, 'add_studentconcession');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (188, 'Can change Student Specific Concession', 46, 'change_studentconcession');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (189, 'Can delete Student Specific Concession', 46, 'delete_studentconcession');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (190, 'Can view Student Specific Concession', 46, 'view_studentconcession');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (191, 'Can add Student Fee Allocation', 47, 'add_studentfeeallocation');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (192, 'Can change Student Fee Allocation', 47, 'change_studentfeeallocation');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (193, 'Can delete Student Fee Allocation', 47, 'delete_studentfeeallocation');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (194, 'Can view Student Fee Allocation', 47, 'view_studentfeeallocation');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (195, 'Can add Fee Head', 48, 'add_feehead');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (196, 'Can change Fee Head', 48, 'change_feehead');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (197, 'Can delete Fee Head', 48, 'delete_feehead');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (198, 'Can view Fee Head', 48, 'view_feehead');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (199, 'Can add budget', 49, 'add_budget');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (200, 'Can change budget', 49, 'change_budget');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (201, 'Can delete budget', 49, 'delete_budget');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (202, 'Can view budget', 49, 'view_budget');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (203, 'Can add Budgeted Amount', 50, 'add_budgetamount');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (204, 'Can change Budgeted Amount', 50, 'change_budgetamount');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (205, 'Can delete Budgeted Amount', 50, 'delete_budgetamount');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (206, 'Can view Budgeted Amount', 50, 'view_budgetamount');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (207, 'Can add Budget Item Category', 51, 'add_budgetitem');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (208, 'Can change Budget Item Category', 51, 'change_budgetitem');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (209, 'Can delete Budget Item Category', 51, 'delete_budgetitem');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (210, 'Can view Budget Item Category', 51, 'view_budgetitem');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (211, 'Can add Expense Record', 52, 'add_expense');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (212, 'Can change Expense Record', 52, 'change_expense');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (213, 'Can delete Expense Record', 52, 'delete_expense');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (214, 'Can view Expense Record', 52, 'view_expense');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (215, 'Can add Expense Category', 53, 'add_expensecategory');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (216, 'Can change Expense Category', 53, 'change_expensecategory');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (217, 'Can delete Expense Category', 53, 'delete_expensecategory');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (218, 'Can view Expense Category', 53, 'view_expensecategory');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (219, 'Can add Vendor/Supplier', 54, 'add_vendor');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (220, 'Can change Vendor/Supplier', 54, 'change_vendor');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (221, 'Can delete Vendor/Supplier', 54, 'delete_vendor');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (222, 'Can view Vendor/Supplier', 54, 'view_vendor');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (223, 'Can add Payment', 55, 'add_payment');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (224, 'Can change Payment', 55, 'change_payment');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (225, 'Can delete Payment', 55, 'delete_payment');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (226, 'Can view Payment', 55, 'view_payment');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (227, 'Can add Payment Allocation', 56, 'add_paymentallocation');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (228, 'Can change Payment Allocation', 56, 'change_paymentallocation');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (229, 'Can delete Payment Allocation', 56, 'delete_paymentallocation');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (230, 'Can view Payment Allocation', 56, 'view_paymentallocation');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (231, 'Can add Payment Method', 57, 'add_paymentmethod');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (232, 'Can change Payment Method', 57, 'change_paymentmethod');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (233, 'Can delete Payment Method', 57, 'delete_paymentmethod');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (234, 'Can view Payment Method', 57, 'view_paymentmethod');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (235, 'Can add Admin Activity Log', 58, 'add_adminactivitylog');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (236, 'Can change Admin Activity Log', 58, 'change_adminactivitylog');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (237, 'Can delete Admin Activity Log', 58, 'delete_adminactivitylog');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (238, 'Can view Admin Activity Log', 58, 'view_adminactivitylog');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (239, 'Can add Announcement', 59, 'add_announcement');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (240, 'Can change Announcement', 59, 'change_announcement');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (241, 'Can delete Announcement', 59, 'delete_announcement');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (242, 'Can view Announcement', 59, 'view_announcement');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (243, 'Can add Communication Log', 60, 'add_communicationlog');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (244, 'Can change Communication Log', 60, 'change_communicationlog');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (245, 'Can delete Communication Log', 60, 'delete_communicationlog');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (246, 'Can view Communication Log', 60, 'view_communicationlog');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (247, 'Can view Collection Report', 61, 'view_collection_report');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (248, 'Can view Outstanding Fees Report', 61, 'view_outstanding_fees_report');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (249, 'Can view Student Ledger Report', 61, 'view_student_ledger_report');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (250, 'Can view Payment Summary Report', 61, 'view_payment_summary_report');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (251, 'Can view Trial Balance Report', 61, 'view_trial_balance_report');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (252, 'Can view Income Statement (P&L)', 61, 'view_income_statement_report');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (253, 'Can view Balance Sheet Report', 61, 'view_balance_sheet_report');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (254, 'Can view Cash Flow Statement Report', 61, 'view_cash_flow_statement_report');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (255, 'Can view Budget Variance Report', 61, 'view_budget_variance_report');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (256, 'Can view Expense Report', 61, 'view_expense_report');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (257, 'Can view Fee Projection Report', 61, 'view_fee_projection_report');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (258, 'Can add Platform Setting', 62, 'add_platformsetting');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (259, 'Can change Platform Setting', 62, 'change_platformsetting');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (260, 'Can delete Platform Setting', 62, 'delete_platformsetting');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (261, 'Can view Platform Setting', 62, 'view_platformsetting');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (262, 'Can add Platform Announcement', 63, 'add_platformannouncement');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (263, 'Can change Platform Announcement', 63, 'change_platformannouncement');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (264, 'Can delete Platform Announcement', 63, 'delete_platformannouncement');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (265, 'Can view Platform Announcement', 63, 'view_platformannouncement');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (266, 'Can add Maintenance Mode Setting', 64, 'add_maintenancemode');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (267, 'Can change Maintenance Mode Setting', 64, 'change_maintenancemode');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (268, 'Can delete Maintenance Mode Setting', 64, 'delete_maintenancemode');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (269, 'Can view Maintenance Mode Setting', 64, 'view_maintenancemode');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (270, 'Can add Platform Audit Log', 65, 'add_auditlog');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (271, 'Can change Platform Audit Log', 65, 'change_auditlog');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (272, 'Can delete Platform Audit Log', 65, 'delete_auditlog');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (273, 'Can view Platform Audit Log', 65, 'view_auditlog');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (274, 'Can add System Notification', 66, 'add_systemnotification');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (275, 'Can change System Notification', 66, 'change_systemnotification');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (276, 'Can delete System Notification', 66, 'delete_systemnotification');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (277, 'Can view System Notification', 66, 'view_systemnotification');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (278, 'Can add Platform Announcement', 67, 'add_platformannouncement');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (279, 'Can change Platform Announcement', 67, 'change_platformannouncement');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (280, 'Can delete Platform Announcement', 67, 'delete_platformannouncement');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (281, 'Can view Platform Announcement', 67, 'view_platformannouncement');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (282, 'Can add Academic Year', 68, 'add_academicyear');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (283, 'Can change Academic Year', 68, 'change_academicyear');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (284, 'Can delete Academic Year', 68, 'delete_academicyear');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (285, 'Can view Academic Year', 68, 'view_academicyear');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (286, 'Can add Term/Semester', 69, 'add_term');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (287, 'Can change Term/Semester', 69, 'change_term');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (288, 'Can delete Term/Semester', 69, 'delete_term');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (289, 'Can view Term/Semester', 69, 'view_term');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (290, 'Can add log entry', 70, 'add_logentry');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (291, 'Can change log entry', 70, 'change_logentry');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (292, 'Can delete log entry', 70, 'delete_logentry');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (293, 'Can view log entry', 70, 'view_logentry');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (294, 'Can add Event Category', 71, 'add_eventcategory');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (295, 'Can change Event Category', 71, 'change_eventcategory');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (296, 'Can delete Event Category', 71, 'delete_eventcategory');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (297, 'Can view Event Category', 71, 'view_eventcategory');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (298, 'Can add School Event', 72, 'add_schoolevent');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (299, 'Can change School Event', 72, 'change_schoolevent');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (300, 'Can delete School Event', 72, 'delete_schoolevent');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (301, 'Can view School Event', 72, 'view_schoolevent');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (302, 'Can add Event Attendee', 73, 'add_eventattendee');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (303, 'Can change Event Attendee', 73, 'change_eventattendee');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (304, 'Can delete Event Attendee', 73, 'delete_eventattendee');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (305, 'Can view Event Attendee', 73, 'view_eventattendee');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (306, 'Can add Invoice Sequence', 74, 'add_invoicesequence');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (307, 'Can change Invoice Sequence', 74, 'change_invoicesequence');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (308, 'Can delete Invoice Sequence', 74, 'delete_invoicesequence');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (309, 'Can view Invoice Sequence', 74, 'view_invoicesequence');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (310, 'Can view outstanding fees report', 75, 'view_outstanding_fees_report');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (311, 'Can view collection report', 75, 'view_collection_report');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (312, 'Can view student ledger report', 75, 'view_student_ledger_report');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (313, 'Can view Income & Expense Report', 61, 'view_income_expense_report');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (314, 'Can view Cash Flow Statement', 61, 'view_cash_flow_statement');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (315, 'Can view the main reports dashboard page', 61, 'view_report_dashboard');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (316, 'Can add Receipt Sequence', 76, 'add_receiptsequence');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (317, 'Can change Receipt Sequence', 76, 'change_receiptsequence');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (318, 'Can delete Receipt Sequence', 76, 'delete_receiptsequence');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (319, 'Can view Receipt Sequence', 76, 'view_receiptsequence');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (320, 'Can view the main staff dashboard summary', 77, 'view_dashboard_summary');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (321, 'Can view the main Announcements module link', 59, 'view_announcements_module');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (322, 'Can view the main Students & Parents module link', 78, 'view_students_module');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (323, 'Can view the main HR module and navbar link', 79, 'view_hr_module');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (324, 'Can create, edit, and manage staff accounts', 79, 'manage_staff_users');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (325, 'Can approve or reject leave requests', 79, 'approve_leave_requests');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (326, 'Can configure leave types', 79, 'manage_leave_types');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (327, 'Can view the main Fees Management module link', 80, 'view_fees_module');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (328, 'Can view the main Finance module and navbar link', 81, 'view_finance_module');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (329, 'Can create and manage budgets', 81, 'manage_budgets');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (330, 'Can record and approve expenses', 81, 'manage_expenses');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (331, 'Can view the main Setup & Admin module link', 82, 'view_setup_admin_module');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (332, 'Can assign staff members to roles (groups)', 82, 'assign_staff_roles');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (333, 'Can view the main Announcements module link', 73, 'view_announcements_module');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (334, 'Can view Payment Summary Report', 75, 'view_payment_summary_report');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (335, 'Can view Trial Balance Report', 75, 'view_trial_balance_report');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (336, 'Can view Income Statement (P&L)', 75, 'view_income_statement_report');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (337, 'Can view Income & Expense Report', 75, 'view_income_expense_report');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (338, 'Can view Balance Sheet Report', 75, 'view_balance_sheet_report');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (339, 'Can view Cash Flow Statement Report', 75, 'view_cash_flow_statement_report');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (340, 'Can view Cash Flow Statement', 75, 'view_cash_flow_statement');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (341, 'Can view Budget Variance Report', 75, 'view_budget_variance_report');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (342, 'Can view Expense Report', 75, 'view_expense_report');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (343, 'Can view Fee Projection Report', 75, 'view_fee_projection_report');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (344, 'Can view the main reports dashboard page', 75, 'view_report_dashboard');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (345, 'Can view the main Calendar module and navbar link', 73, 'view_calendar_module');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (346, 'Can create, edit, and delete any school event', 73, 'manage_all_events');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (347, 'Can view the General Ledger report', 81, 'view_general_ledger_report');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (348, 'Can view the Account Ledger report', 81, 'view_account_ledger_report');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (349, 'Can view the main HR module and navbar link', 37, 'view_hr_module');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (350, 'Can approve or reject leave requests for other staff', 37, 'approve_leave_requests');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (351, 'Can add Leave Balance Log', 83, 'add_leavebalancelog');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (352, 'Can change Leave Balance Log', 83, 'change_leavebalancelog');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (353, 'Can delete Leave Balance Log', 83, 'delete_leavebalancelog');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (354, 'Can view Leave Balance Log', 83, 'view_leavebalancelog');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (355, 'Can add Tax Bracket', 84, 'add_taxbracket');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (356, 'Can change Tax Bracket', 84, 'change_taxbracket');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (357, 'Can delete Tax Bracket', 84, 'delete_taxbracket');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (358, 'Can view Tax Bracket', 84, 'view_taxbracket');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (359, 'Can add Salary Component', 85, 'add_salarycomponent');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (360, 'Can change Salary Component', 85, 'change_salarycomponent');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (361, 'Can delete Salary Component', 85, 'delete_salarycomponent');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (362, 'Can view Salary Component', 85, 'view_salarycomponent');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (363, 'Can add Payslip Line Item', 86, 'add_paysliplineitem');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (364, 'Can change Payslip Line Item', 86, 'change_paysliplineitem');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (365, 'Can delete Payslip Line Item', 86, 'delete_paysliplineitem');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (366, 'Can view Payslip Line Item', 86, 'view_paysliplineitem');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (367, 'Can add Payslip', 87, 'add_payslip');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (368, 'Can change Payslip', 87, 'change_payslip');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (369, 'Can delete Payslip', 87, 'delete_payslip');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (370, 'Can view Payslip', 87, 'view_payslip');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (371, 'Can add Staff Salary Component', 88, 'add_staffsalarycomponent');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (372, 'Can change Staff Salary Component', 88, 'change_staffsalarycomponent');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (373, 'Can delete Staff Salary Component', 88, 'delete_staffsalarycomponent');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (374, 'Can view Staff Salary Component', 88, 'view_staffsalarycomponent');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (375, 'Can add Staff Salary', 89, 'add_staffsalary');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (376, 'Can change Staff Salary', 89, 'change_staffsalary');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (377, 'Can delete Staff Salary', 89, 'delete_staffsalary');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (378, 'Can view Staff Salary', 89, 'view_staffsalary');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (379, 'Can add Salary Grade', 90, 'add_salarygrade');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (380, 'Can change Salary Grade', 90, 'change_salarygrade');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (381, 'Can delete Salary Grade', 90, 'delete_salarygrade');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (382, 'Can view Salary Grade', 90, 'view_salarygrade');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (383, 'Can add Salary Grade Component', 91, 'add_salarygradecomponent');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (384, 'Can change Salary Grade Component', 91, 'change_salarygradecomponent');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (385, 'Can delete Salary Grade Component', 91, 'delete_salarygradecomponent');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (386, 'Can view Salary Grade Component', 91, 'view_salarygradecomponent');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (387, 'Can add Payroll Run', 92, 'add_payrollrun');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (388, 'Can change Payroll Run', 92, 'change_payrollrun');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (389, 'Can delete Payroll Run', 92, 'delete_payrollrun');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (390, 'Can view Payroll Run', 92, 'view_payrollrun');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (391, 'Can create and process payroll runs', 92, 'manage_payroll');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (392, 'Can add staff salary structure', 93, 'add_staffsalarystructure');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (393, 'Can change staff salary structure', 93, 'change_staffsalarystructure');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (394, 'Can delete staff salary structure', 93, 'delete_staffsalarystructure');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (395, 'Can view staff salary structure', 93, 'view_staffsalarystructure');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (396, 'Can add salary structure component', 94, 'add_salarystructurecomponent');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (397, 'Can change salary structure component', 94, 'change_salarystructurecomponent');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (398, 'Can delete salary structure component', 94, 'delete_salarystructurecomponent');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (399, 'Can view salary structure component', 94, 'view_salarystructurecomponent');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (400, 'Can add Statutory Deduction', 95, 'add_statutorydeduction');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (401, 'Can change Statutory Deduction', 95, 'change_statutorydeduction');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (402, 'Can delete Statutory Deduction', 95, 'delete_statutorydeduction');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (403, 'Can view Statutory Deduction', 95, 'view_statutorydeduction');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (404, 'Can add Grade Salary Rule', 96, 'add_graderule');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (405, 'Can change Grade Salary Rule', 96, 'change_graderule');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (406, 'Can delete Grade Salary Rule', 96, 'delete_graderule');
INSERT INTO auth_permission ("id", "name", "content_type_id", "codename") VALUES (407, 'Can view Grade Salary Rule', 96, 'view_graderule');

-- Data for django_content_type
DELETE FROM django_content_type;
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (1, 'admin', 'logentry');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (2, 'auth', 'permission');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (3, 'auth', 'group');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (4, 'contenttypes', 'contenttype');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (5, 'sessions', 'session');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (6, 'tenants', 'domain');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (7, 'tenants', 'school');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (8, 'users', 'user');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (9, 'public_site', 'contactinquiry');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (10, 'public_site', 'testimonial');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (11, 'accounting', 'accounttype');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (12, 'accounting', 'generalledger');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (13, 'accounting', 'journalentry');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (14, 'accounting', 'journalentryitem');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (15, 'accounting', 'journalline');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (16, 'accounting', 'account');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (17, 'subscriptions', 'feature');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (18, 'subscriptions', 'subscription');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (19, 'subscriptions', 'subscriptionplan');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (20, 'django_celery_beat', 'crontabschedule');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (21, 'django_celery_beat', 'intervalschedule');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (22, 'django_celery_beat', 'periodictask');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (23, 'django_celery_beat', 'periodictasks');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (24, 'django_celery_beat', 'solarschedule');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (25, 'django_celery_beat', 'clockedschedule');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (26, 'schools', 'academicsetting');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (27, 'schools', 'academicyear');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (28, 'schools', 'country');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (29, 'schools', 'schoolclass');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (30, 'schools', 'schoolprofile');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (31, 'schools', 'staffuser');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (32, 'schools', 'section');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (33, 'schools', 'term');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (34, 'students', 'parentuser');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (35, 'students', 'student');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (36, 'hr', 'employeeprofile');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (37, 'hr', 'leavetype');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (38, 'hr', 'leaverequest');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (39, 'hr', 'leavebalance');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (40, 'fees', 'account');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (41, 'fees', 'concessiontype');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (42, 'fees', 'feestructure');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (43, 'fees', 'feestructureitem');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (44, 'fees', 'invoice');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (45, 'fees', 'invoicedetail');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (46, 'fees', 'studentconcession');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (47, 'fees', 'studentfeeallocation');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (48, 'fees', 'feehead');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (49, 'finance', 'budget');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (50, 'finance', 'budgetamount');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (51, 'finance', 'budgetitem');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (52, 'finance', 'expense');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (53, 'finance', 'expensecategory');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (54, 'finance', 'vendor');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (55, 'payments', 'payment');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (56, 'payments', 'paymentallocation');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (57, 'payments', 'paymentmethod');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (58, 'portal_admin', 'adminactivitylog');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (59, 'announcements', 'announcement');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (60, 'communication', 'communicationlog');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (61, 'reporting', 'reportingpermissions');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (62, 'platform_management', 'platformsetting');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (63, 'platform_management', 'platformannouncement');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (64, 'platform_management', 'maintenancemode');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (65, 'platform_management', 'auditlog');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (66, 'platform_management', 'systemnotification');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (67, 'announcements', 'platformannouncement');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (68, 'fees', 'academicyear');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (69, 'fees', 'term');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (70, 'auditlog', 'logentry');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (71, 'school_calendar', 'eventcategory');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (72, 'school_calendar', 'schoolevent');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (73, 'school_calendar', 'eventattendee');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (74, 'schools', 'invoicesequence');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (75, 'reporting', 'reportpermissions');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (76, 'schools', 'receiptsequence');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (77, 'schools', 'schoolpermissions');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (78, 'students', 'studentpermissions');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (79, 'hr', 'hrpermissions');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (80, 'fees', 'feepermissions');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (81, 'finance', 'financepermissions');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (82, 'portal_admin', 'portaladminpermissions');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (83, 'hr', 'leavebalancelog');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (84, 'hr', 'taxbracket');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (85, 'hr', 'salarycomponent');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (86, 'hr', 'paysliplineitem');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (87, 'hr', 'payslip');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (88, 'hr', 'staffsalarycomponent');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (89, 'hr', 'staffsalary');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (90, 'hr', 'salarygrade');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (91, 'hr', 'salarygradecomponent');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (92, 'hr', 'payrollrun');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (93, 'hr', 'staffsalarystructure');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (94, 'hr', 'salarystructurecomponent');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (95, 'hr', 'statutorydeduction');
INSERT INTO django_content_type ("id", "app_label", "model") VALUES (96, 'hr', 'graderule');

-- Data for fees_concessiontype
DELETE FROM fees_concessiontype;
INSERT INTO fees_concessiontype ("id", "name", "description", "value", "is_active", "created_at", "updated_at", "type") VALUES (3, 'Test Concession', 'Test description', 10.00, TRUE, 2025-06-25 08:17:02.960478+00:00, 2025-06-25 08:17:02.960499+00:00, 'PERCENTAGE');
INSERT INTO fees_concessiontype ("id", "name", "description", "value", "is_active", "created_at", "updated_at", "type") VALUES (4, 'Test Sibling Discount', 'Test discount for siblings', 15.00, TRUE, 2025-06-25 08:17:39.162464+00:00, 2025-06-25 08:17:39.162483+00:00, 'PERCENTAGE');
INSERT INTO fees_concessiontype ("id", "name", "description", "value", "is_active", "created_at", "updated_at", "type") VALUES (5, 'Siblings Discount', 'Second child', 150.00, TRUE, 2025-06-25 08:17:44.275613+00:00, 2025-06-25 08:17:44.275623+00:00, 'FIXED_AMOUNT');

-- Data for hr_leavetype
DELETE FROM hr_leavetype;
INSERT INTO hr_leavetype ("id", "name", "description", "max_annual_days", "is_paid", "requires_approval", "is_active", "created_at", "updated_at", "max_days_per_year_grant", "accrual_frequency", "accrual_rate", "max_accrual_balance", "prorate_accrual") VALUES (1, 'Annual Leave', 'Yearly vacation leave for all staff members', NULL, TRUE, TRUE, TRUE, 2025-07-07 19:22:02.639002+00:00, 2025-07-07 19:22:02.639015+00:00, 21.00, 'MONTHLY', 1.75, 30, TRUE);
INSERT INTO hr_leavetype ("id", "name", "description", "max_annual_days", "is_paid", "requires_approval", "is_active", "created_at", "updated_at", "max_days_per_year_grant", "accrual_frequency", "accrual_rate", "max_accrual_balance", "prorate_accrual") VALUES (3, 'Maternity Leave', 'Leave for new mothers', NULL, TRUE, TRUE, TRUE, 2025-07-07 19:22:02.670193+00:00, 2025-07-07 19:22:02.670220+00:00, 90.00, 'NONE', 0.00, NULL, FALSE);
INSERT INTO hr_leavetype ("id", "name", "description", "max_annual_days", "is_paid", "requires_approval", "is_active", "created_at", "updated_at", "max_days_per_year_grant", "accrual_frequency", "accrual_rate", "max_accrual_balance", "prorate_accrual") VALUES (6, 'Study Leave', 'For Writing Exams', NULL, TRUE, TRUE, TRUE, 2025-07-08 02:07:49.089895+00:00, 2025-07-08 02:07:49.089929+00:00, 21.00, 'ANNUALLY', 0.00, 21, TRUE);
INSERT INTO hr_leavetype ("id", "name", "description", "max_annual_days", "is_paid", "requires_approval", "is_active", "created_at", "updated_at", "max_days_per_year_grant", "accrual_frequency", "accrual_rate", "max_accrual_balance", "prorate_accrual") VALUES (2, 'Sick Leave', 'Medical leave for illness or medical appointments', NULL, TRUE, FALSE, TRUE, 2025-07-07 19:22:02.665276+00:00, 2025-07-08 15:49:40.471712+00:00, 18.00, 'ANNUALLY', 10.00, 15, FALSE);
INSERT INTO hr_leavetype ("id", "name", "description", "max_annual_days", "is_paid", "requires_approval", "is_active", "created_at", "updated_at", "max_days_per_year_grant", "accrual_frequency", "accrual_rate", "max_accrual_balance", "prorate_accrual") VALUES (4, 'Paternity Leave', 'Leave for new fathers', NULL, TRUE, TRUE, TRUE, 2025-07-07 19:22:02.674347+00:00, 2025-07-08 15:49:53.067161+00:00, 30.00, 'NONE', 0.00, NULL, FALSE);
INSERT INTO hr_leavetype ("id", "name", "description", "max_annual_days", "is_paid", "requires_approval", "is_active", "created_at", "updated_at", "max_days_per_year_grant", "accrual_frequency", "accrual_rate", "max_accrual_balance", "prorate_accrual") VALUES (5, 'Emergency Leave', 'Unpaid leave for emergency situations', NULL, FALSE, TRUE, TRUE, 2025-07-07 19:22:02.682847+00:00, 2025-07-08 15:50:03.498188+00:00, 6.00, 'NONE', 0.00, NULL, FALSE);
INSERT INTO hr_leavetype ("id", "name", "description", "max_annual_days", "is_paid", "requires_approval", "is_active", "created_at", "updated_at", "max_days_per_year_grant", "accrual_frequency", "accrual_rate", "max_accrual_balance", "prorate_accrual") VALUES (7, 'Compassionate Leave', 'Bereavement leave', NULL, TRUE, TRUE, TRUE, 2025-07-09 08:58:30.611260+00:00, 2025-07-09 08:58:30.611319+00:00, 12.00, 'NONE', 0.00, 12, TRUE);

-- Data for payments_paymentmethod
DELETE FROM payments_paymentmethod;
INSERT INTO payments_paymentmethod ("id", "created_at", "updated_at", "name", "type", "description", "is_active", "linked_account_id") VALUES (1, 2025-06-27 09:27:58.854317+00:00, 2025-06-28 19:40:05.917304+00:00, 'Cash', 'CASH', '', TRUE, 5);
INSERT INTO payments_paymentmethod ("id", "created_at", "updated_at", "name", "type", "description", "is_active", "linked_account_id") VALUES (2, 2025-06-27 09:28:34.166388+00:00, 2025-06-28 19:40:21.556042+00:00, 'Online Payments (Parent)', 'ONLINE_MOCK', '', TRUE, 5);
INSERT INTO payments_paymentmethod ("id", "created_at", "updated_at", "name", "type", "description", "is_active", "linked_account_id") VALUES (3, 2025-06-27 09:29:02.952478+00:00, 2025-06-28 19:40:37.598814+00:00, 'Online Portal Payment', 'CARD_PAYMENT', '', TRUE, 5);
INSERT INTO payments_paymentmethod ("id", "created_at", "updated_at", "name", "type", "description", "is_active", "linked_account_id") VALUES (4, 2025-06-28 19:41:28.087934+00:00, 2025-06-28 19:41:28.087950+00:00, 'Bank Transfer', 'BANK_TRANSFER', '', TRUE, 7);

-- Data for school_calendar_eventcategory
DELETE FROM school_calendar_eventcategory;
INSERT INTO school_calendar_eventcategory ("id", "name", "color", "icon", "description", "is_active", "created_at", "updated_at") VALUES (1, 'Academic', '#007bff', 'bi-book', 'Academic events and activities', TRUE, 2025-06-26 15:40:57.821171+00:00, 2025-06-26 15:40:57.821187+00:00);
INSERT INTO school_calendar_eventcategory ("id", "name", "color", "icon", "description", "is_active", "created_at", "updated_at") VALUES (2, 'Sports', '#28a745', 'bi-trophy', 'Sports and athletic events', TRUE, 2025-06-26 15:40:57.825081+00:00, 2025-06-26 15:40:57.825097+00:00);
INSERT INTO school_calendar_eventcategory ("id", "name", "color", "icon", "description", "is_active", "created_at", "updated_at") VALUES (3, 'Cultural', '#ffc107', 'bi-palette', 'Cultural and artistic events', TRUE, 2025-06-26 15:40:57.827471+00:00, 2025-06-26 15:40:57.827483+00:00);
INSERT INTO school_calendar_eventcategory ("id", "name", "color", "icon", "description", "is_active", "created_at", "updated_at") VALUES (4, 'Parent Events', '#17a2b8', 'bi-people', 'Events for parents and families', TRUE, 2025-06-26 15:40:57.829134+00:00, 2025-06-26 15:40:57.829142+00:00);
INSERT INTO school_calendar_eventcategory ("id", "name", "color", "icon", "description", "is_active", "created_at", "updated_at") VALUES (5, 'Holidays', '#dc3545', 'bi-calendar-heart', 'School holidays and breaks', TRUE, 2025-06-26 15:40:57.830762+00:00, 2025-06-26 15:40:57.830770+00:00);
INSERT INTO school_calendar_eventcategory ("id", "name", "color", "icon", "description", "is_active", "created_at", "updated_at") VALUES (6, 'Examinations', '#6f42c1', 'bi-pencil-square', 'Exams and assessments', TRUE, 2025-06-26 15:40:57.833575+00:00, 2025-06-26 15:40:57.833592+00:00);

-- Data for hr_salarycomponent
DELETE FROM hr_salarycomponent;
INSERT INTO hr_salarycomponent ("id", "name", "type", "description", "is_percentage") VALUES (1, 'Basic Salary', 'EARNING', 'Basic Monthly Salary', FALSE);
INSERT INTO hr_salarycomponent ("id", "name", "type", "description", "is_percentage") VALUES (2, 'Housing Allowance', 'EARNING', 'Monthly Housing Allowance', FALSE);
INSERT INTO hr_salarycomponent ("id", "name", "type", "description", "is_percentage") VALUES (3, 'Pension Deduction', 'DEDUCTION', 'Statutory Deduction at a rate of 7.5% of the Gross Salary', FALSE);
INSERT INTO hr_salarycomponent ("id", "name", "type", "description", "is_percentage") VALUES (4, 'Transport Allowance', 'EARNING', 'Transport relief', FALSE);
INSERT INTO hr_salarycomponent ("id", "name", "type", "description", "is_percentage") VALUES (5, 'PAYE Tax', 'DEDUCTION', 'Pay As You Earn', TRUE);

-- Data for hr_statutorydeduction
DELETE FROM hr_statutorydeduction;
INSERT INTO hr_statutorydeduction ("id", "name", "employee_contribution_rate", "employer_contribution_rate", "is_active", "payslip_label") VALUES (1, 'Pension Fund', 7.50, 7.50, TRUE, '');
INSERT INTO hr_statutorydeduction ("id", "name", "employee_contribution_rate", "employer_contribution_rate", "is_active", "payslip_label") VALUES (2, 'Aids Levy', 0.01, 0.01, TRUE, '');
INSERT INTO hr_statutorydeduction ("id", "name", "employee_contribution_rate", "employer_contribution_rate", "is_active", "payslip_label") VALUES (3, 'Drought Levy', 0.01, 0.03, TRUE, '');

-- Data for hr_taxbracket
DELETE FROM hr_taxbracket;
INSERT INTO hr_taxbracket ("id", "name", "from_amount", "to_amount", "rate_percent", "deduction_amount", "is_active") VALUES (1, 'Low_income', 0.00, 50000.00, 10.00, 0.00, TRUE);
INSERT INTO hr_taxbracket ("id", "name", "from_amount", "to_amount", "rate_percent", "deduction_amount", "is_active") VALUES (3, 'high_income', 120000.01, NULL, 30.00, 19000.00, TRUE);
INSERT INTO hr_taxbracket ("id", "name", "from_amount", "to_amount", "rate_percent", "deduction_amount", "is_active") VALUES (2, 'middle_income', 50000.01, 120000.00, 20.00, 5000.00, TRUE);
