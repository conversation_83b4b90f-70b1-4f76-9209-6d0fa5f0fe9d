{% extends "tenant_base.html" %}
{% load humanize static core_tags reporting_filters %}

{% block title %}{{ view_title|default:"Refund Analysis Report" }}{% endblock %}

{% block content %}
<div class="container-fluid mt-3">
    {% with report_icon_class="bi bi-arrow-return-left" %}
        {% include "partials/_report_header.html" %}
    {% endwith %}

    {% include "partials/_messages.html" %}
    {% include "partials/_report_filter_form.html" with filter_form=filter_form report_code=report_code specific_filter_title="Refund Analysis Filters" %}

    <!-- Refund Overview -->
    <div class="card mb-4">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="bi bi-graph-down me-2"></i>Refund Overview</h5>
        </div>
        <div class="card-body">
            <div class="row mb-3">
                <div class="col-md-12">
                    <p>
                        <strong>Date Range:</strong> {{ start_date }} to {{ end_date }} | 
                        <strong>Analysis Focus:</strong> {{ analysis_focus|title }} | 
                        <strong>Impact on Revenue:</strong> {{ refund_summary.refund_impact_percentage|floatformat:2 }}%
                    </p>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-3">
                    <div class="text-center">
                        <h6 class="text-muted">Total Refunds</h6>
                        <p class="mb-0 fw-bold text-primary fs-4">{{ refund_summary.total_refunds }}</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h6 class="text-muted">Refund Amount</h6>
                        <p class="mb-0 fw-bold text-danger fs-4">{{ refund_summary.total_refund_amount|currency }}</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h6 class="text-muted">Processing Rate</h6>
                        <p class="mb-0 fw-bold text-success fs-4">{{ refund_summary.processing_rate|floatformat:1 }}%</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h6 class="text-muted">Avg Processing</h6>
                        <p class="mb-0 fw-bold text-info fs-4">{{ refund_summary.avg_processing_days|floatformat:1 }} days</p>
                    </div>
                </div>
            </div>
            
            <!-- Refund Status Distribution -->
            <div class="mt-3">
                <h6>Refund Status Distribution</h6>
                <div class="progress" style="height: 25px;">
                    {% with processed_pct=refund_summary.processed_count|percentage:refund_summary.total_refunds pending_pct=refund_summary.pending_count|percentage:refund_summary.total_refunds rejected_pct=refund_summary.rejected_count|percentage:refund_summary.total_refunds %}
                    <div class="progress-bar bg-success" role="progressbar" style="width: {{ processed_pct }}%">
                        Processed: {{ processed_pct|floatformat:0 }}%
                    </div>
                    <div class="progress-bar bg-warning" role="progressbar" style="width: {{ pending_pct }}%">
                        Pending: {{ pending_pct|floatformat:0 }}%
                    </div>
                    <div class="progress-bar bg-danger" role="progressbar" style="width: {{ rejected_pct }}%">
                        Rejected: {{ rejected_pct|floatformat:0 }}%
                    </div>
                    {% endwith %}
                </div>
                <small class="text-muted">
                    Processed: {{ refund_summary.processed_count }} ({{ refund_summary.processed_amount|currency }}) | 
                    Pending: {{ refund_summary.pending_count }} ({{ refund_summary.pending_amount|currency }}) | 
                    Rejected: {{ refund_summary.rejected_count }} ({{ refund_summary.rejected_amount|currency }})
                </small>
            </div>
        </div>
    </div>

    <!-- Financial Impact -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="bi bi-cash-stack me-2"></i>Financial Impact</h5>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled">
                        <li><i class="bi bi-arrow-up text-success me-2"></i>
                            <strong>Total Revenue:</strong> {{ refund_summary.total_revenue|currency }}
                        </li>
                        <li><i class="bi bi-arrow-down text-danger me-2"></i>
                            <strong>Total Refunds:</strong> {{ refund_summary.total_refund_amount|currency }}
                        </li>
                        <li><i class="bi bi-calculator text-info me-2"></i>
                            <strong>Net Revenue:</strong> {{ refund_summary.net_revenue|currency }}
                        </li>
                        <li><i class="bi bi-percent text-warning me-2"></i>
                            <strong>Refund Impact:</strong> {{ refund_summary.refund_impact_percentage|floatformat:2 }}%
                        </li>
                        <li><i class="bi bi-currency-dollar text-primary me-2"></i>
                            <strong>Average Refund:</strong> {{ refund_summary.avg_refund_amount|currency }}
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="bi bi-speedometer me-2"></i>Processing Metrics</h5>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled">
                        <li><i class="bi bi-check-circle text-success me-2"></i>
                            <strong>Processing Rate:</strong> {{ refund_summary.processing_rate|floatformat:1 }}%
                        </li>
                        <li><i class="bi bi-x-circle text-danger me-2"></i>
                            <strong>Rejection Rate:</strong> {{ refund_summary.rejection_rate|floatformat:1 }}%
                        </li>
                        <li><i class="bi bi-clock text-info me-2"></i>
                            <strong>Avg Processing Time:</strong> {{ refund_summary.avg_processing_days|floatformat:1 }} days
                        </li>
                        <li><i class="bi bi-hourglass text-warning me-2"></i>
                            <strong>Pending Refunds:</strong> {{ refund_summary.pending_count }}
                        </li>
                        <li><i class="bi bi-currency-dollar text-warning me-2"></i>
                            <strong>Pending Amount:</strong> {{ refund_summary.pending_amount|currency }}
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Refund Breakdown by Reason -->
    {% if refund_breakdown %}
    <div class="card mb-4">
        <div class="card-header bg-light d-flex justify-content-between align-items-center">
            <h5 class="mb-0"><i class="bi bi-pie-chart me-2"></i>Refund Breakdown by Reason</h5>
            <span class="badge bg-primary">{{ refund_breakdown|length }} reasons</span>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-striped table-hover mb-0">
                    <thead class="table-dark">
                        <tr>
                            <th>Refund Reason</th>
                            <th class="text-center">Count</th>
                            <th class="text-end">Total Amount</th>
                            <th class="text-center">Percentage</th>
                            <th class="text-end">Average Amount</th>
                            <th class="text-center">Impact Level</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for breakdown in refund_breakdown %}
                        <tr class="{% if breakdown.impact_level == 'HIGH' %}table-danger{% elif breakdown.impact_level == 'MEDIUM' %}table-warning{% endif %}">
                            <td>
                                <strong>{{ breakdown.reason_display }}</strong>
                                <br><small class="text-muted">{{ breakdown.reason }}</small>
                            </td>
                            <td class="text-center">
                                <span class="fw-bold">{{ breakdown.count }}</span>
                            </td>
                            <td class="text-end">
                                <span class="fw-bold text-danger">{{ breakdown.amount|currency }}</span>
                            </td>
                            <td class="text-center">
                                <div class="progress" style="height: 20px; width: 80px;">
                                    <div class="progress-bar {% if breakdown.impact_level == 'HIGH' %}bg-danger{% elif breakdown.impact_level == 'MEDIUM' %}bg-warning{% else %}bg-info{% endif %}" 
                                         role="progressbar" style="width: {{ breakdown.percentage }}%">
                                    </div>
                                </div>
                                <small class="text-muted">{{ breakdown.percentage|floatformat:1 }}%</small>
                            </td>
                            <td class="text-end">
                                <span class="text-primary">{{ breakdown.avg_amount|currency }}</span>
                            </td>
                            <td class="text-center">
                                <span class="badge {% if breakdown.impact_level == 'HIGH' %}bg-danger{% elif breakdown.impact_level == 'MEDIUM' %}bg-warning{% else %}bg-success{% endif %}">
                                    {{ breakdown.impact_level }}
                                </span>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                    <tfoot class="table-light">
                        <tr>
                            <th>Totals:</th>
                            <th class="text-center">{{ refund_summary.total_refunds }}</th>
                            <th class="text-end">{{ refund_summary.total_refund_amount|currency }}</th>
                            <th class="text-center">100.0%</th>
                            <th class="text-end">{{ refund_summary.avg_refund_amount|currency }}</th>
                            <th></th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Trend Analysis -->
    {% if trend_analysis and analysis_focus == 'trends' %}
    <div class="card mb-4">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="bi bi-graph-up me-2"></i>Refund Trends</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Period</th>
                            <th class="text-center">Count</th>
                            <th class="text-end">Amount</th>
                            <th class="text-end">Average</th>
                            <th>Top Reason</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for trend in trend_analysis %}
                        <tr>
                            <td><strong>{{ trend.period }}</strong></td>
                            <td class="text-center">{{ trend.count }}</td>
                            <td class="text-end">{{ trend.amount|currency }}</td>
                            <td class="text-end">{{ trend.avg_amount|currency }}</td>
                            <td>
                                <span class="badge bg-info">{{ trend.top_reason }}</span>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Insights and Recommendations -->
    {% if refund_breakdown %}
    <div class="card">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="bi bi-lightbulb me-2"></i>Insights & Recommendations</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h6>Key Insights</h6>
                    <ul class="list-unstyled">
                        {% if refund_summary.refund_impact_percentage < 2 %}
                        <li><i class="bi bi-check-circle text-success me-2"></i>
                            Low refund impact on revenue ({{ refund_summary.refund_impact_percentage|floatformat:2 }}%)
                        </li>
                        {% elif refund_summary.refund_impact_percentage < 5 %}
                        <li><i class="bi bi-info-circle text-info me-2"></i>
                            Moderate refund impact - monitor trends
                        </li>
                        {% else %}
                        <li><i class="bi bi-exclamation-triangle text-warning me-2"></i>
                            High refund impact requires attention
                        </li>
                        {% endif %}
                        
                        {% if refund_summary.processing_rate > 90 %}
                        <li><i class="bi bi-check-circle text-success me-2"></i>
                            Excellent processing rate ({{ refund_summary.processing_rate|floatformat:1 }}%)
                        </li>
                        {% elif refund_summary.processing_rate > 75 %}
                        <li><i class="bi bi-info-circle text-info me-2"></i>
                            Good processing rate with room for improvement
                        </li>
                        {% else %}
                        <li><i class="bi bi-exclamation-triangle text-warning me-2"></i>
                            Processing rate needs improvement
                        </li>
                        {% endif %}
                        
                        {% if refund_summary.avg_processing_days < 5 %}
                        <li><i class="bi bi-check-circle text-success me-2"></i>
                            Fast processing time ({{ refund_summary.avg_processing_days|floatformat:1 }} days)
                        </li>
                        {% else %}
                        <li><i class="bi bi-clock text-warning me-2"></i>
                            Processing time could be improved
                        </li>
                        {% endif %}
                        
                        <li><i class="bi bi-cash text-info me-2"></i>
                            Average refund: {{ refund_summary.avg_refund_amount|currency }}
                        </li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6>Recommendations</h6>
                    <ul class="list-unstyled">
                        {% if refund_summary.refund_impact_percentage > 3 %}
                        <li><i class="bi bi-arrow-right text-primary me-2"></i>
                            Investigate high-impact refund reasons
                        </li>
                        {% endif %}
                        
                        {% if refund_summary.rejection_rate > 10 %}
                        <li><i class="bi bi-arrow-right text-warning me-2"></i>
                            Review refund approval process
                        </li>
                        {% endif %}
                        
                        {% if refund_summary.avg_processing_days > 7 %}
                        <li><i class="bi bi-arrow-right text-info me-2"></i>
                            Streamline refund processing workflow
                        </li>
                        {% endif %}
                        
                        <li><i class="bi bi-arrow-right text-success me-2"></i>
                            Implement preventive measures for top refund reasons
                        </li>
                        
                        {% if refund_summary.pending_count > 5 %}
                        <li><i class="bi bi-arrow-right text-warning me-2"></i>
                            Address pending refunds backlog
                        </li>
                        {% endif %}
                        
                        <li><i class="bi bi-arrow-right text-primary me-2"></i>
                            Monitor refund trends for early intervention
                        </li>
                    </ul>
                </div>
            </div>
            
            <hr>
            
            <div class="alert {% if refund_summary.refund_impact_percentage < 2 %}alert-success{% elif refund_summary.refund_impact_percentage < 5 %}alert-info{% else %}alert-warning{% endif %}">
                <h6 class="alert-heading">
                    <i class="bi {% if refund_summary.refund_impact_percentage < 2 %}bi-check-circle{% elif refund_summary.refund_impact_percentage < 5 %}bi-info-circle{% else %}bi-exclamation-triangle{% endif %} me-2"></i>
                    Refund Impact Assessment
                </h6>
                <p class="mb-0">
                    {% if refund_summary.refund_impact_percentage < 2 %}
                    Refund levels are within acceptable limits. Continue monitoring and maintaining current processes.
                    {% elif refund_summary.refund_impact_percentage < 5 %}
                    Refund impact is moderate. Consider reviewing processes to identify improvement opportunities.
                    {% else %}
                    Refund impact is significant. Immediate attention required to identify and address root causes.
                    {% endif %}
                    Current impact: {{ refund_summary.refund_impact_percentage|floatformat:2 }}% of total revenue.
                </p>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- No Data Message -->
    {% if not refund_breakdown %}
    <div class="card">
        <div class="card-body text-center py-5">
            <i class="bi bi-arrow-return-left display-1 text-muted mb-3"></i>
            <h4 class="text-muted">No Refund Data Available</h4>
            <p class="text-muted">No refund data found for the selected criteria.</p>
            <div class="mt-3">
                <a href="{% url 'reporting:refund_analysis_report' %}" class="btn btn-outline-primary me-2">
                    <i class="bi bi-arrow-clockwise me-2"></i>Reset Filters
                </a>
            </div>
        </div>
    </div>
    {% endif %}

</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Animate progress bars
    $('.progress-bar').each(function() {
        var $this = $(this);
        var width = $this.css('width');
        $this.css('width', '0%').animate({width: width}, 1500);
    });
    
    // Highlight high impact refund reasons
    $('.table-danger, .table-warning').hover(
        function() {
            $(this).addClass('table-active');
        },
        function() {
            $(this).removeClass('table-active');
        }
    );
    
    // Add click handlers for refund breakdown rows
    $('tbody tr').on('click', function() {
        $(this).toggleClass('table-info');
    });
});
</script>
{% endblock %}

