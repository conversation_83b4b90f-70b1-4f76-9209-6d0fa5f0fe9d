{# D:\school_fees_saas_v2\apps\parent_portal\templates\parent_portal\mock_payment_success.html #}
{% extends "parent_portal/parent_portal_base.html" %}
{% load static i18n humanize %}

{% block parent_portal_page_title %}{% trans "Payment Successful" %}{% endblock %}

{% block parent_portal_main_content %}
<div class="container mt-5 text-center">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="card shadow-sm border-success">
                <div class="card-body p-5">
                    <i class="bi bi-check-circle-fill text-success display-1 mb-3"></i>
                    <h2 class="card-title">{% trans "Payment Successful!" %}</h2>
                    <p class="lead">
                        {% blocktrans %}Your simulated payment has been processed successfully.{% endblocktrans %}
                    </p>
                    {% if payment %} {# Assuming 'payment' object is passed to context by a future PaymentDetailView logic #}
                        <p>{% blocktrans with ref=payment.reference_number %}Payment Reference: <strong>{{ ref }}</strong>{% endblocktrans %}</p>
                        <p>{% blocktrans with amount=payment.amount_paid|floatformat:2|intcomma currency_symbol=school_profile.currency_symbol|default:'$' %}Amount Paid: <strong>{{ currency_symbol }}{{ amount }}</strong>{% endblocktrans %}</p>
                    {% endif %}
                    <hr>
                    <p>{% trans "Thank you for your prompt payment." %}</p>
                    <div class="mt-4">
                        <a href="{% url 'parent_portal:dashboard' %}" class="btn btn-primary me-2">
                            <i class="bi bi-grid-fill me-1"></i> {% trans "Back to Dashboard" %}
                        </a>
                        <a href="{% url 'parent_portal:payment_history_all' %}" class="btn btn-outline-secondary">
                            <i class="bi bi-clock-history me-1"></i> {% trans "View Payment History" %}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}



