{% extends "tenant_base.html" %}
{% load humanize static core_tags reporting_filters %}

{% block title %}{{ view_title|default:"Fee Collection Analysis Report" }}{% endblock %}

{% block content %}
<div class="container-fluid mt-3">
    {% with report_icon_class="bi bi-cash-coin" %}
        {% include "partials/_report_header.html" %}
    {% endwith %}

    {% include "partials/_messages.html" %}
    {% include "partials/_report_filter_form.html" with filter_form=filter_form report_code=report_code specific_filter_title="Collection Analysis Filters" %}

    <!-- Collection Summary Card -->
    <div class="card mb-4">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="bi bi-graph-up me-2"></i>Collection Performance Summary</h5>
        </div>
        <div class="card-body">
            {% if start_date and end_date %}
            <div class="row mb-3">
                <div class="col-md-12">
                    <p><strong>Analysis Period:</strong> {{ start_date|date:"M d, Y" }} to {{ end_date|date:"M d, Y" }}</p>
                </div>
            </div>
            {% endif %}
            
            <div class="row">
                <div class="col-md-2">
                    <div class="text-center">
                        <h6 class="text-muted">Total Collections</h6>
                        <p class="mb-0 fw-bold text-success fs-4">{{ collection_summary.total_collections|currency }}</p>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="text-center">
                        <h6 class="text-muted">Payment Count</h6>
                        <p class="mb-0 fw-bold text-primary">{{ collection_summary.payment_count|default:0 }}</p>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="text-center">
                        <h6 class="text-muted">Average Payment</h6>
                        <p class="mb-0 fw-bold text-info">{{ collection_summary.avg_payment|currency }}</p>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="text-center">
                        <h6 class="text-muted">Total Invoiced</h6>
                        <p class="mb-0 fw-bold text-warning">{{ collection_summary.total_invoiced|currency }}</p>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="text-center">
                        <h6 class="text-muted">Collection Rate</h6>
                        <p class="mb-0 fw-bold {% if collection_summary.collection_rate >= 80 %}text-success{% elif collection_summary.collection_rate >= 60 %}text-warning{% else %}text-danger{% endif %}">
                            {{ collection_summary.collection_rate|floatformat:1 }}%
                        </p>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="text-center">
                        <h6 class="text-muted">Outstanding</h6>
                        <p class="mb-0 fw-bold text-danger">{{ collection_summary.outstanding_amount|currency }}</p>
                    </div>
                </div>
            </div>
            
            <!-- Collection Rate Progress Bar -->
            <div class="mt-3">
                <h6>Collection Efficiency</h6>
                <div class="progress" style="height: 25px;">
                    <div class="progress-bar {% if collection_summary.collection_rate >= 80 %}bg-success{% elif collection_summary.collection_rate >= 60 %}bg-warning{% else %}bg-danger{% endif %}" 
                         role="progressbar" style="width: {{ collection_summary.collection_rate|floatformat:0 }}%">
                        {{ collection_summary.collection_rate|floatformat:1 }}%
                    </div>
                </div>
                <small class="text-muted">
                    {% if collection_summary.collection_rate >= 80 %}
                    Excellent collection performance
                    {% elif collection_summary.collection_rate >= 60 %}
                    Good collection performance - room for improvement
                    {% else %}
                    Collection performance needs attention
                    {% endif %}
                </small>
            </div>
        </div>
    </div>

    <!-- Payment Method Analysis -->
    {% if payment_method_analysis %}
    <div class="card mb-4">
        <div class="card-header bg-light d-flex justify-content-between align-items-center">
            <h5 class="mb-0"><i class="bi bi-credit-card me-2"></i>Payment Method Analysis</h5>
            <span class="badge bg-primary">{{ payment_method_analysis|length }} methods</span>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-striped table-hover mb-0">
                    <thead class="table-dark">
                        <tr>
                            <th>Payment Method</th>
                            <th class="text-end">Total Amount</th>
                            <th class="text-center">Payment Count</th>
                            <th class="text-end">Average Amount</th>
                            <th class="text-center">Percentage</th>
                            <th class="text-center">Usage</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for method in payment_method_analysis %}
                        <tr>
                            <td>
                                <strong>{{ method.method_name }}</strong>
                            </td>
                            <td class="text-end">
                                <span class="text-success fw-bold">{{ method.total_amount|currency }}</span>
                            </td>
                            <td class="text-center">
                                <span class="badge bg-info">{{ method.payment_count }}</span>
                            </td>
                            <td class="text-end">
                                {{ method.avg_amount|currency }}
                            </td>
                            <td class="text-center">
                                <span class="fw-bold">{{ method.percentage|floatformat:1 }}%</span>
                            </td>
                            <td class="text-center">
                                <div class="progress" style="height: 20px; width: 100px;">
                                    <div class="progress-bar bg-primary" role="progressbar" style="width: {{ method.percentage|floatformat:0 }}%">
                                    </div>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                    <tfoot class="table-light">
                        <tr>
                            <th>Total:</th>
                            <th class="text-end text-success">{{ total_collections|currency }}</th>
                            <th class="text-center">{{ collection_summary.payment_count }}</th>
                            <th class="text-end">{{ collection_summary.avg_payment|currency }}</th>
                            <th class="text-center">100.0%</th>
                            <th></th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Class-wise Analysis -->
    {% if class_analysis %}
    <div class="card mb-4">
        <div class="card-header bg-light d-flex justify-content-between align-items-center">
            <h5 class="mb-0"><i class="bi bi-people me-2"></i>Class-wise Collection Analysis</h5>
            <span class="badge bg-success">{{ class_analysis|length }} classes</span>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-striped table-hover mb-0">
                    <thead class="table-dark">
                        <tr>
                            <th>Class</th>
                            <th class="text-end">Total Amount</th>
                            <th class="text-center">Payments</th>
                            <th class="text-center">Students</th>
                            <th class="text-end">Avg per Student</th>
                            <th class="text-center">Percentage</th>
                            <th class="text-center">Performance</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for class_item in class_analysis %}
                        <tr>
                            <td>
                                <strong>{{ class_item.class_name }}</strong>
                            </td>
                            <td class="text-end">
                                <span class="text-success fw-bold">{{ class_item.total_amount|currency }}</span>
                            </td>
                            <td class="text-center">
                                <span class="badge bg-info">{{ class_item.payment_count }}</span>
                            </td>
                            <td class="text-center">
                                <span class="badge bg-secondary">{{ class_item.student_count }}</span>
                            </td>
                            <td class="text-end">
                                {{ class_item.avg_per_student|currency }}
                            </td>
                            <td class="text-center">
                                <span class="fw-bold">{{ class_item.percentage|floatformat:1 }}%</span>
                            </td>
                            <td class="text-center">
                                <div class="progress" style="height: 20px; width: 100px;">
                                    <div class="progress-bar {% if class_item.percentage >= 15 %}bg-success{% elif class_item.percentage >= 10 %}bg-warning{% else %}bg-danger{% endif %}" 
                                         role="progressbar" style="width: {{ class_item.percentage|floatformat:0 }}%">
                                    </div>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                    <tfoot class="table-light">
                        <tr>
                            <th>Total:</th>
                            <th class="text-end text-success">{{ total_collections|currency }}</th>
                            <th class="text-center">{{ collection_summary.payment_count }}</th>
                            <th class="text-center">-</th>
                            <th class="text-end">-</th>
                            <th class="text-center">100.0%</th>
                            <th></th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Key Insights -->
    {% if collection_summary %}
    <div class="card">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="bi bi-lightbulb me-2"></i>Key Insights & Recommendations</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h6>Collection Performance</h6>
                    <ul class="list-unstyled">
                        {% if collection_summary.collection_rate >= 80 %}
                        <li><i class="bi bi-check-circle text-success me-2"></i>Excellent collection rate of {{ collection_summary.collection_rate|floatformat:1 }}%</li>
                        {% elif collection_summary.collection_rate >= 60 %}
                        <li><i class="bi bi-exclamation-triangle text-warning me-2"></i>Good collection rate but can be improved ({{ collection_summary.collection_rate|floatformat:1 }}%)</li>
                        {% else %}
                        <li><i class="bi bi-x-circle text-danger me-2"></i>Collection rate needs immediate attention ({{ collection_summary.collection_rate|floatformat:1 }}%)</li>
                        {% endif %}
                        
                        {% if collection_summary.avg_payment > 5000 %}
                        <li><i class="bi bi-arrow-up text-success me-2"></i>High average payment amount ({{ collection_summary.avg_payment|currency }})</li>
                        {% else %}
                        <li><i class="bi bi-arrow-down text-info me-2"></i>Consider payment plan options for smaller amounts</li>
                        {% endif %}
                        
                        <li><i class="bi bi-cash me-2"></i>{{ collection_summary.outstanding_amount|currency }} still outstanding</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6>Recommendations</h6>
                    <ul class="list-unstyled">
                        {% if collection_summary.collection_rate < 80 %}
                        <li><i class="bi bi-arrow-right text-primary me-2"></i>Implement follow-up procedures for outstanding payments</li>
                        <li><i class="bi bi-arrow-right text-primary me-2"></i>Consider offering payment plans or discounts for early payment</li>
                        {% endif %}
                        
                        {% if payment_method_analysis|length > 1 %}
                        <li><i class="bi bi-arrow-right text-primary me-2"></i>Promote most efficient payment methods</li>
                        {% endif %}
                        
                        <li><i class="bi bi-arrow-right text-primary me-2"></i>Monitor class-wise performance for targeted interventions</li>
                        <li><i class="bi bi-arrow-right text-primary me-2"></i>Set up automated reminders for due payments</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- No Data Message -->
    {% if not collection_summary.total_collections %}
    <div class="card">
        <div class="card-body text-center py-5">
            <i class="bi bi-cash-coin display-1 text-muted mb-3"></i>
            <h4 class="text-muted">No Collection Data Found</h4>
            <p class="text-muted">No payments were recorded for the selected period and filters.</p>
            <div class="mt-3">
                <a href="{% url 'reporting:fee_collection_analysis_report' %}" class="btn btn-outline-primary me-2">
                    <i class="bi bi-arrow-clockwise me-2"></i>Reset Filters
                </a>
                <a href="#" class="btn btn-outline-success">
                    <i class="bi bi-plus-circle me-2"></i>Record Payment
                </a>
            </div>
        </div>
    </div>
    {% endif %}

</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Animate progress bars
    $('.progress-bar').each(function() {
        var $this = $(this);
        var width = $this.css('width');
        $this.css('width', '0%').animate({width: width}, 1000);
    });
    
    // Add hover effects for table rows
    $('.table tbody tr').hover(
        function() {
            $(this).addClass('table-active');
        },
        function() {
            $(this).removeClass('table-active');
        }
    );
});
</script>
{% endblock %}

