# D:\school_fees_saas_v2\apps\fees\forms.py

from django import forms
from django.forms.models import inlineformset_factory, BaseInlineFormSet
from django.utils import timezone
from django.utils.translation import gettext_lazy as _ # For translations
from decimal import Decimal
from django.conf import settings

# Import models from THIS app
from .models import (
    FeeHead, FeeStructure, FeeStructureItem,
    StudentFeeAllocation, ConcessionType, StudentConcession, Invoice, InvoiceDetail,
    LineTypeChoices, InvoiceStatus, # (if defined in models.py and needed directly here)
)
# Import models from OTHER apps
from apps.students.models import Student
from apps.schools.models import SchoolClass # If needed for filtering FeeStructure applicability
from apps.accounting.models import Account as ChartOfAccount # Alias for clarity

# Crispy Forms imports (if used consistently)
from crispy_forms.helper import FormHelper
from crispy_forms.layout import Layout, Submit, Row, Column, HTML, Field, Div

from apps.schools.models import AcademicYear, Term
from apps.students.models import Student

# --- AcademicYear Form ---
class AcademicYearForm(forms.ModelForm):
    class Meta:
        model = AcademicYear
        fields = ['name', 'start_date', 'end_date', 'is_current']
        widgets = {
            'name': forms.TextInput(attrs={'placeholder': _('e.g., 2024-2025')}),
            'start_date': forms.DateInput(attrs={'type': 'date'}),
            'end_date': forms.DateInput(attrs={'type': 'date'}),
            'is_current': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }
        labels = {'is_current': _('Set as Current Academic Year?')}

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        for field_name, field in self.fields.items():
            if not isinstance(field.widget, forms.CheckboxInput):
                field.widget.attrs.update({'class': 'form-control form-control-sm'})


# --- Term Form ---
class TermForm(forms.ModelForm):
    academic_year = forms.ModelChoiceField(
        queryset=AcademicYear.objects.all().order_by('-start_date'),
        widget=forms.Select(attrs={'class': 'form-select form-select-sm'}),
        empty_label=_("--- Select Academic Year ---")
    )

    class Meta:
        model = Term
        fields = ['name', 'academic_year', 'start_date', 'end_date']
        widgets = {
            'name': forms.TextInput(attrs={'placeholder': _('e.g., Term 1, Semester 2')}),
            'start_date': forms.DateInput(attrs={'type': 'date'}),
            'end_date': forms.DateInput(attrs={'type': 'date'}),
        }
        labels = {'name': _('Term/Semester Name')}
        help_texts = {
            'name': _('Must be unique for the selected academic year.'),
            'start_date': _('Must be within the selected Academic Year.'),
            'end_date': _('Must be within the selected Academic Year and after term start date.'),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        for field_name, field in self.fields.items():
            if not isinstance(field.widget, forms.CheckboxInput): # Keep select as form-select
                if 'form-select' not in field.widget.attrs.get('class', ''):
                    field.widget.attrs.update({'class': 'form-control form-control-sm'})


# --- FeeHead Form ---
class FeeHeadForm(forms.ModelForm):
    income_account_link = forms.ModelChoiceField( # Renamed from income_account for clarity
        queryset=ChartOfAccount.objects.filter(is_active=True).select_related('account_type').order_by('code', 'name'),
        # Queryset will be further filtered in __init__ to show only Revenue types
        required=False,
        label=_("Linked Income Account (CoA)"),
        help_text=_("Select the Revenue account this fee head posts to. Optional."),
        widget=forms.Select(attrs={'class': 'form-select form-select-sm'})
    )

    class Meta:
        model = FeeHead
        fields = ['name', 'description', 'income_account_link', 'is_active'] # Added is_active
        widgets = {
            'name': forms.TextInput(attrs={'placeholder': _('e.g., Tuition Fee, Bus Fee')}),
            'description': forms.Textarea(attrs={'rows': 3}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Filter income_account_link choices to only show Revenue accounts
        # Using the correct field name 'classification' from AccountType model
        self.fields['income_account_link'].queryset = ChartOfAccount.objects.filter(
            account_type__classification='REVENUE',
            is_active=True
        ).select_related('account_type').order_by('code', 'name')

        for field_name, field in self.fields.items():
            if not isinstance(field.widget, (forms.CheckboxInput, forms.Select)):
                field.widget.attrs.update({'class': 'form-control form-control-sm'})
            elif isinstance(field.widget, forms.Select):
                if 'form-select-sm' not in field.widget.attrs.get('class',''): # Keep existing form-select
                    field.widget.attrs['class'] = f"{field.widget.attrs.get('class','')} form-select-sm".strip()


# --- FeeStructure Form (Main Object) ---
class FeeStructureForm(forms.ModelForm):
    term = forms.ModelChoiceField(
        queryset=Term.objects.none(), # Populated by JS or in __init__ based on academic_year
        required=False,
        widget=forms.Select(attrs={'class': 'form-select form-select-sm'}),
        label=_("Specific Term (Optional)"),
        empty_label=_("--- (Applies to Full Academic Year) ---")
    )
    # applicable_classes M2M field will be rendered by default widget or can be customized
    applicable_classes = forms.ModelMultipleChoiceField(
        queryset=SchoolClass.objects.all().order_by('name'), # Scope in __init__ if tenant-specific
        widget=forms.CheckboxSelectMultiple, # Or SelectMultiple(attrs={'class': 'form-select select-multiple'})
        required=False,
        label=_("Applicable Classes")
    )

    class Meta:
        model = FeeStructure
        fields = ['name', 'academic_year', 'term', 'applicable_classes', 'description', 'is_active']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control form-control-sm'}),
            'academic_year': forms.Select(attrs={'class': 'form-select form-select-sm'}),
            'description': forms.Textarea(attrs={'rows': 3, 'class': 'form-control form-control-sm'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }

    def __init__(self, *args, **kwargs):
        # tenant = kwargs.pop('tenant', None) # If needed for SchoolClass queryset
        super().__init__(*args, **kwargs)
        self.fields['academic_year'].queryset = AcademicYear.objects.all().order_by('-start_date')
        self.fields['academic_year'].empty_label = _("--- Select Academic Year ---")

        selected_year_id = None
        if self.instance and self.instance.pk and self.instance.academic_year_id:
            selected_year_id = self.instance.academic_year_id
        elif 'academic_year' in self.initial:
            selected_year_id = self.initial['academic_year']
        elif self.data and 'academic_year' in self.data: # Handle POST/bound data
            selected_year_id = self.data.get('academic_year')

        if selected_year_id:
            try:
                self.fields['term'].queryset = Term.objects.filter(academic_year_id=selected_year_id).order_by('start_date')
            except ValueError: # Handle invalid selected_year_id if not int
                self.fields['term'].queryset = Term.objects.none()
        else:
            self.fields['term'].queryset = Term.objects.none()

        # Scope applicable_classes if SchoolClass is tenant-specific via schema (usually fine)
        # or if it has a direct FK to tenant:
        # if tenant:
        #     self.fields['applicable_classes'].queryset = SchoolClass.objects.filter(tenant=tenant).order_by('name')


# --- FeeStructureItemForm (for use in Formsets) ---
class FeeStructureItemForm(forms.ModelForm):
    fee_head = forms.ModelChoiceField(
        queryset=FeeHead.objects.all().order_by('name'), # Assuming FeeHead is shared or tenant-scoped
        widget=forms.Select(attrs={'class': 'form-select form-select-sm item-fee-head'})
    )
    amount = forms.DecimalField(
        max_digits=10, decimal_places=2, min_value=Decimal('0.00'),
        widget=forms.NumberInput(attrs={'class': 'form-control form-control-sm item-amount', 'step': '0.01'})
    )
    is_optional = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={'class': 'form-check-input item-optional'})
    )
    description = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control form-control-sm item-description', 'placeholder': _('Optional item description')})
    )

    class Meta:
        model = FeeStructureItem
        fields = ['fee_head', 'amount', 'description', 'is_optional']
        # No widgets here as fields are defined directly above for more control


FeeStructureItemInlineFormSet = inlineformset_factory(
    FeeStructure,
    FeeStructureItem,
    form=FeeStructureItemForm, # Use custom form
    fields=['fee_head', 'amount', 'description', 'is_optional'],
    extra=1,
    can_delete=True,
    min_num=1, # At least one fee item is required
    validate_min=True,
)



# --- StudentFeeAllocation Form ---
class StudentFeeAllocationForm(forms.ModelForm):
    academic_year = forms.ModelChoiceField(
        queryset=AcademicYear.objects.all().order_by('-start_date'),
        widget=forms.Select(attrs={'class': 'form-select form-select-sm'}),
        empty_label=_("--- Select Academic Year ---")
    )
    fee_structure = forms.ModelChoiceField(
        queryset=FeeStructure.objects.filter(is_active=True).select_related('academic_year').order_by('-academic_year__start_date', 'name'),
        widget=forms.Select(attrs={'class': 'form-select form-select-sm'}),
        empty_label=_("--- Select Fee Structure ---")
    )
    term = forms.ModelChoiceField(
        queryset=Term.objects.select_related('academic_year').order_by('-academic_year__start_date', 'start_date'),
        widget=forms.Select(attrs={'class': 'form-select form-select-sm'}),
        empty_label=_("--- Select Term (If Applicable) ---"),
        required=False
    )

    class Meta:
        model = StudentFeeAllocation
        fields = ['student', 'academic_year', 'fee_structure', 'term', 'notes', 'is_active']
        widgets = {
            'student': forms.HiddenInput(),
            'notes': forms.Textarea(attrs={'rows': 2, 'class': 'form-control form-control-sm'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # JS/HTMX would typically be used to filter fee_structure/term based on academic_year


# --- ConcessionType Form ---
class ConcessionTypeForm(forms.ModelForm):
    class Meta:
        model = ConcessionType
        fields = ['name', 'description', 'type', 'value', 'is_active'] # type was concession_type
        widgets = {
            'name': forms.TextInput(attrs={'placeholder': _('e.g., Sibling Discount 15%')}),
            'description': forms.Textarea(attrs={'rows': 3}),
            'type': forms.Select(), # Model 'type' uses choices
            'value': forms.NumberInput(attrs={'step': '0.01'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }
        labels = {
            'type': _('Type of Concession'), # Was concession_type
            'value': _('Value (Amount or %)'),
            'is_active': _('Is this concession type currently active?')
        }
        help_texts = {
            'value': _('Enter fixed amount if Type is Fixed Amount, or percentage (e.g., 10.5 for 10.50%) if Type is Percentage.'),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        for field_name, field in self.fields.items():
            if not isinstance(field.widget, (forms.CheckboxInput, forms.Select)):
                field.widget.attrs.update({'class': 'form-control form-control-sm'})
            elif isinstance(field.widget, forms.Select):
                field.widget.attrs['class'] = f"{field.widget.attrs.get('class','')} form-select form-select-sm".strip()

# --- StudentConcession Form (For assigning a ConcessionType to a Student) ---
class StudentConcessionForm(forms.ModelForm):
    academic_year = forms.ModelChoiceField(
        queryset=AcademicYear.objects.all().order_by('-start_date'),
        widget=forms.Select(attrs={'class': 'form-select form-select-sm'}),
        empty_label=_("--- Select Academic Year ---")
    )
    concession_type = forms.ModelChoiceField(
        queryset=ConcessionType.objects.filter(is_active=True).order_by('name'),
        widget=forms.Select(attrs={'class': 'form-select form-select-sm'}),
        empty_label=_("--- Select Concession Type ---")
    )
    term = forms.ModelChoiceField(
        queryset=Term.objects.none(), # Populate based on academic_year
        widget=forms.Select(attrs={'class': 'form-select form-select-sm'}),
        empty_label=_("--- Term (If concession is term-specific) ---"),
        required=False
    )
    # Removed specific_fee_head and override fields for simplicity; can be added back if needed.

    class Meta:
        model = StudentConcession
        fields = ['student', 'academic_year', 'concession_type', 'term', 'notes', 'granted_by']
        widgets = {
            'student': forms.HiddenInput(),
            'notes': forms.Textarea(attrs={'rows': 2, 'class': 'form-control form-control-sm'}),
            'granted_by': forms.HiddenInput(), # Usually set in the view
        }

    def __init__(self, *args, **kwargs):
        self.request = kwargs.pop('request', None) # For setting granted_by
        super().__init__(*args, **kwargs)

        selected_year_id = None
        if self.instance and self.instance.pk and self.instance.academic_year_id:
            selected_year_id = self.instance.academic_year_id
        elif 'academic_year' in self.initial:
            selected_year_id = self.initial['academic_year']
        elif self.data and 'academic_year' in self.data:
            selected_year_id = self.data.get('academic_year')

        if selected_year_id:
            try:
                self.fields['term'].queryset = Term.objects.filter(academic_year_id=selected_year_id).order_by('start_date')
            except ValueError:
                self.fields['term'].queryset = Term.objects.none()
        else:
            self.fields['term'].queryset = Term.objects.none()

        if self.request and not self.initial.get('granted_by'): # Pre-fill granted_by if creating
            self.initial['granted_by'] = self.request.user.pk


# --- Invoice Forms ---
class InvoiceForm(forms.ModelForm):
    student = forms.ModelChoiceField(
        queryset=Student.objects.filter(is_active=True).order_by('last_name', 'first_name'),
        widget=forms.Select(attrs={'class': 'form-select select2-field'}), # Example for Select2
        label=_("Student")
    )
    academic_year = forms.ModelChoiceField(
        queryset=AcademicYear.objects.all().order_by('-start_date'),
        widget=forms.Select(attrs={'class': 'form-select select2-field'}),
        label=_("Academic Year")
    )
    term = forms.ModelChoiceField(
        queryset=Term.objects.none(), # Populate based on academic_year
        required=False,
        widget=forms.Select(attrs={'class': 'form-select select2-field'}),
        label=_("Term (Optional)"),
        empty_label=_("--- (Entire Academic Year) ---")
    )

    class Meta:
        model = Invoice
        fields = [
            'student', 'academic_year', 'term', 'invoice_number',
            'issue_date', 'due_date', 'status',
            'notes_to_parent', 'internal_notes'
        ]
        # Exclude: subtotal_amount, total_concession_amount, amount_paid (calculated or updated by actions)
        # Exclude: journal_entry (set by accounting process)
        # Exclude: created_by (set in view)
        widgets = {
            'invoice_number': forms.TextInput(attrs={'placeholder': _('Leave blank to auto-generate')}),
            'issue_date': forms.DateInput(attrs={'type': 'date'}),
            'due_date': forms.DateInput(attrs={'type': 'date'}),
            'status': forms.Select(attrs={'class': 'form-select'}),
            'notes_to_parent': forms.Textarea(attrs={'rows': 3}),
            'internal_notes': forms.Textarea(attrs={'rows': 2}),
        }

    def clean_invoice_number(self):
        """Handle invoice number validation - allow blank for auto-generation."""
        invoice_number = self.cleaned_data.get('invoice_number')

        # If blank or None, it will be auto-generated, so no validation needed
        if not invoice_number or invoice_number.strip() == '':
            return None  # Return None for blank values to ensure auto-generation

        # If provided, check for uniqueness
        if Invoice.objects.filter(invoice_number=invoice_number).exclude(pk=self.instance.pk if self.instance else None).exists():
            raise forms.ValidationError(_("Invoice with this number already exists."))

        return invoice_number

    def __init__(self, *args, **kwargs):
        # tenant = kwargs.pop('tenant', None) # If needed for student queryset scoping
        super().__init__(*args, **kwargs)

        for field_name, field in self.fields.items():
            if not isinstance(field.widget, (forms.CheckboxInput, forms.Select, forms.HiddenInput)):
                if 'form-control' not in field.widget.attrs.get('class', ''):
                    field.widget.attrs['class'] = field.widget.attrs.get('class', '') + ' form-control'
            if isinstance(field.widget, forms.Select) and 'form-select' not in field.widget.attrs.get('class',''):
                field.widget.attrs['class'] = field.widget.attrs.get('class', '') + ' form-select'

        self.fields['issue_date'].initial = timezone.now().date()
        self.fields['due_date'].initial = timezone.now().date() + timezone.timedelta(days=30)

        selected_year_id = None
        if self.instance and self.instance.pk and self.instance.academic_year_id:
            selected_year_id = self.instance.academic_year_id
        elif 'academic_year' in self.initial:
            selected_year_id = self.initial['academic_year']
        elif self.data and 'academic_year' in self.data:
            selected_year_id = self.data.get('academic_year')
        else:
            # For new invoices, pre-select the active academic year
            try:
                active_year = AcademicYear.objects.filter(is_active=True).first()
                if active_year:
                    selected_year_id = active_year.pk
                    self.fields['academic_year'].initial = active_year.pk
            except Exception:
                pass  # If no active year found, continue with empty selection

        if selected_year_id:
            try:
                self.fields['term'].queryset = Term.objects.filter(academic_year_id=selected_year_id).order_by('start_date')
            except ValueError:
                # If invalid year ID, show all terms so user can see what's available
                self.fields['term'].queryset = Term.objects.select_related('academic_year').order_by('-academic_year__start_date', 'start_date')
        else:
            # If no academic year selected, show all terms so user can see what's available
            self.fields['term'].queryset = Term.objects.select_related('academic_year').order_by('-academic_year__start_date', 'start_date')

        # If instance exists and is not DRAFT, some fields might be read-only
        if self.instance and self.instance.pk and self.instance.status != InvoiceStatus.DRAFT:
            for field_name in ['student', 'academic_year', 'term', 'issue_date', 'due_date']:
                if field_name in self.fields:
                    self.fields[field_name].disabled = True


class InvoiceDetailForm(forms.ModelForm):
    # Define fields directly for more control over widgets and querysets
    line_type = forms.ChoiceField(
        choices=LineTypeChoices.choices, # Assuming LineTypeChoices is on InvoiceDetail
        required=False, # Allow empty for extra forms
        widget=forms.Select(attrs={'class': 'form-select form-select-sm item-line-type'})
    )
    fee_head = forms.ModelChoiceField(
        queryset=FeeHead.objects.all().order_by('name'),
        required=False, # Make optional, depends on line_type
        widget=forms.Select(attrs={'class': 'form-select form-select-sm item-fee-head'})
    )
    concession_type = forms.ModelChoiceField(
        queryset=ConcessionType.objects.filter(is_active=True).order_by('name'),
        required=False, # Make optional, depends on line_type
        widget=forms.Select(attrs={'class': 'form-select form-select-sm item-concession-type'})
    )
    # Description might be auto-populated or editable
    description = forms.CharField(
        required=False, # Allow empty for extra forms
        widget=forms.TextInput(attrs={'class': 'form-control form-control-sm item-description'})
    )
    quantity = forms.DecimalField(
        initial=Decimal('1.00'), max_digits=10, decimal_places=2, min_value=Decimal('0.01'),
        required=False, # Allow empty for extra forms
        widget=forms.NumberInput(attrs={'class': 'form-control form-control-sm item-quantity', 'step': '0.01'})
    )
    unit_price = forms.DecimalField(
        max_digits=10, decimal_places=2,
        required=False, # Allow empty for extra forms
        widget=forms.NumberInput(attrs={'class': 'form-control form-control-sm item-unit-price', 'step': '0.01'}),
        label=_("Unit Price / Amount"),
        help_text=_("Positive for fees, negative for concessions (e.g., -50.00)")
    )
    # 'amount' field (line total) is calculated in model's save, so not directly in form fields for editing

    class Meta:
        model = InvoiceDetail
        fields = ['line_type', 'fee_head', 'concession_type', 'description', 'quantity', 'unit_price']
        # Exclude 'amount' as it's auto-calculated in model.save()

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # JavaScript will be needed to show/hide fee_head or concession_type based on line_type
        # and to auto-populate description.
        # For example, if instance exists and has a fee_head, description might be pre-filled.
        if self.instance and self.instance.pk:
            if self.instance.line_type == LineTypeChoices.FEE_ITEM and self.instance.fee_head:
                if not self.initial.get('description'): # Only if not already set
                    self.initial['description'] = self.instance.fee_head.name
            elif self.instance.line_type == LineTypeChoices.CONCESSION and self.instance.concession_type:
                if not self.initial.get('description'):
                    self.initial['description'] = self.instance.concession_type.name

        # If creating new (no instance) or description still empty, try to set from initial fee_head/concession_type
        elif 'fee_head' in self.initial and self.initial['fee_head']:
            try:
                fh = FeeHead.objects.get(pk=self.initial['fee_head'])
                self.initial.setdefault('description', fh.name)
            except (FeeHead.DoesNotExist, TypeError): pass
        elif 'concession_type' in self.initial and self.initial['concession_type']:
            try:
                ct = ConcessionType.objects.get(pk=self.initial['concession_type'])
                self.initial.setdefault('description', ct.name)
            except (ConcessionType.DoesNotExist, TypeError): pass


    def clean(self):
        cleaned_data = super().clean()
        line_type = cleaned_data.get('line_type')
        fee_head = cleaned_data.get('fee_head')
        concession_type = cleaned_data.get('concession_type')
        unit_price = cleaned_data.get('unit_price')
        description = cleaned_data.get('description')
        quantity = cleaned_data.get('quantity')

        # Check if this is an empty form (all fields are empty)
        has_any_data = any([
            line_type,
            fee_head,
            concession_type,
            unit_price,
            description,
            quantity
        ])

        # If no data provided, this is an empty extra form - skip validation
        if not has_any_data:
            return cleaned_data

        # If any data is provided, validate required fields
        if not line_type:
            self.add_error('line_type', _("Line type is required."))

        if not description:
            self.add_error('description', _("Description is required."))

        if not quantity:
            self.add_error('quantity', _("Quantity is required."))

        if unit_price is None:
            self.add_error('unit_price', _("Unit price is required."))

        # Validate based on line type
        if line_type == LineTypeChoices.FEE_ITEM:
            if not fee_head:
                self.add_error('fee_head', _("Fee Head is required for a Fee Item line."))
            if concession_type:
                self.add_error('concession_type', _("Concession Type should not be set for a Fee Item line."))
            # Fee items should have positive unit_price
            if unit_price is not None and unit_price < 0:
                self.add_error('unit_price', _("Fee item amounts must be positive."))

        elif line_type == LineTypeChoices.CONCESSION:
            if not concession_type:
                self.add_error('concession_type', _("Concession Type is required for a Concession line."))
            if fee_head:
                self.add_error('fee_head', _("Fee Head should not be set for a Concession line."))
            # Concessions must have negative unit_price
            if unit_price is not None and unit_price >= 0:
                self.add_error('unit_price', _("Concession amounts must be negative (e.g., -50.00)."))

        # Ensure unit_price and quantity result in a valid amount (model save handles actual calculation)
        return cleaned_data


# Custom formset to handle empty forms properly
class BaseInvoiceDetailFormSet(BaseInlineFormSet):
    def save_new(self, form, commit=True):
        """Override to prevent saving empty forms"""
        # Check if form has any meaningful data
        cleaned_data = form.cleaned_data
        has_any_data = any([
            cleaned_data.get('line_type'),
            cleaned_data.get('fee_head'),
            cleaned_data.get('concession_type'),
            cleaned_data.get('unit_price'),
            cleaned_data.get('description'),
            cleaned_data.get('quantity')
        ])

        # Only save if form has data
        if has_any_data:
            return super().save_new(form, commit)
        return None

InvoiceDetailFormSet = inlineformset_factory(
    Invoice,
    InvoiceDetail,
    form=InvoiceDetailForm,
    formset=BaseInvoiceDetailFormSet,
    extra=1,
    can_delete=True,
    min_num=0, # Allow invoices without line items initially
    validate_min=False,
)

# Separate formset for updates with no extra forms
InvoiceDetailUpdateFormSet = inlineformset_factory(
    Invoice,
    InvoiceDetail,
    form=InvoiceDetailForm,
    formset=BaseInvoiceDetailFormSet,
    extra=0,  # No extra forms for updates
    can_delete=True,
    min_num=0,
    validate_min=False,
)

# --- Invoice Filter Form (for ListView) ---
class InvoiceFilterForm(forms.Form):
    student = forms.ModelChoiceField(
        queryset=Student.objects.all().order_by('last_name', 'first_name'),
        required=False, label=_("Student"),
        widget=forms.Select(attrs={'class': 'form-select form-select-sm select2-field'})
    )
    status = forms.ChoiceField(
        choices=[('', _('-- All Statuses --'))] + InvoiceStatus.choices,
        required=False, label=_("Status"),
        widget=forms.Select(attrs={'class': 'form-select form-select-sm'})
    )
    academic_year = forms.ModelChoiceField(
        queryset=AcademicYear.objects.all().order_by('-start_date'),
        required=False, label=_("Academic Year"),
        widget=forms.Select(attrs={'class':'form-select form-select-sm'})
    )
    issue_date_from = forms.DateField(required=False, label=_("Issue Date From"),
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control form-control-sm'}))
    issue_date_to = forms.DateField(required=False, label=_("Issue Date To"),
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control form-control-sm'}))
    invoice_number_query = forms.CharField(required=False, label=_("Invoice #"),
        widget=forms.TextInput(attrs={'class': 'form-control form-control-sm', 'placeholder':_('Search Invoice #')}))


    def __init__(self, *args, **kwargs):
        tenant = kwargs.pop('tenant', None) # Passed from view
        super().__init__(*args, **kwargs)
        # Scoping for student field assumes Student is tenant-scoped by schema
        # If Student has a direct FK to tenant, filter here:
        # if tenant:
        #     self.fields['student'].queryset = Student.objects.filter(tenant=tenant, is_active=True).order_by('last_name', 'first_name')
        # else: # Should not happen in a tenant view
        #     self.fields['student'].queryset = Student.objects.none()


# --- Invoice Cancellation Form ---
class InvoiceCancelForm(forms.Form): # Changed from VoidInvoiceForm for consistency
    cancellation_reason = forms.CharField(
        widget=forms.Textarea(attrs={'rows': 3, 'class': 'form-control form-control-sm'}),
        required=True,
        label=_("Reason for Voiding/Cancellation"),
        help_text=_("Provide a clear reason why this invoice is being voided.")
    )

# --- Generate Invoices from Fee Structure (for bulk action) ---
class GenerateInvoicesFromStructureForm(forms.Form):
    fee_structure = forms.ModelChoiceField(
        queryset=FeeStructure.objects.filter(is_active=True).order_by('-academic_year__start_date', 'name'),
        label=_("Select Fee Structure"),
        widget=forms.Select(attrs={'class': 'form-select'}),
        help_text=_("Invoices will be generated for all students linked to this fee structure for its academic year/term (if not already invoiced).")
    )

    # Optional class selection - if not selected, will use all classes from fee structure
    classes = forms.ModelMultipleChoiceField(
        queryset=SchoolClass.objects.filter(is_active=True).order_by('name'),
        label=_("Select Classes (Optional)"),
        widget=forms.SelectMultiple(attrs={
            'class': 'form-select',
            'size': '6',
            'style': 'height: auto;'
        }),
        required=False,
        help_text=_("Hold Ctrl/Cmd to select multiple classes. Leave empty to use all classes from the fee structure.")
    )

    # Optional student selection - if not selected, will use all students from selected classes
    students = forms.ModelMultipleChoiceField(
        queryset=Student.objects.filter(is_active=True).order_by('first_name', 'last_name'),
        label=_("Select Specific Students (Optional)"),
        widget=forms.SelectMultiple(attrs={
            'class': 'form-select',
            'size': '8',
            'style': 'height: auto;'
        }),
        required=False,
        help_text=_("Hold Ctrl/Cmd to select multiple students. Leave empty to use all students from selected classes.")
    )

    issue_date = forms.DateField(
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
        label=_("Invoice Issue Date"),
        initial=timezone.now().date
    )
    due_date = forms.DateField(
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
        label=_("Invoice Due Date")
        # Initial can be set in __init__ based on issue_date + X days
    )

    send_email_notification = forms.BooleanField(
        label=_("Send Email Notifications"),
        widget=forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        required=False,
        initial=True,
        help_text=_("Send email notifications to parents when invoices are generated.")
    )
    
    def __init__(self, *args, **kwargs):
        tenant = kwargs.pop('tenant', None) # Get the tenant passed from the view
        super().__init__(*args, **kwargs)

        # Set default due date to 30 days from issue date
        self.fields['due_date'].initial = timezone.now().date() + timezone.timedelta(days=30)

        if tenant:
            # Now you can filter querysets based on the tenant if needed
            # For example, if FeeStructure had a tenant FK (it doesn't, it's in the schema)
            # self.fields['fee_structure'].queryset = FeeStructure.objects.filter(tenant=tenant)
            pass # The default .objects.all() is already scoped by django-tenants

    # def __init__(self, *args, **kwargs):
    #     tenant = kwargs.pop('tenant', None)
    #     super().__init__(*args, **kwargs)
    #     self.fields['issue_date'].initial = timezone.now().date()
    #     self.fields['due_date'].initial = timezone.now().date() + timezone.timedelta(days=getattr(settings, 'DEFAULT_INVOICE_DUE_DAYS', 30))

    #     # Scope fee_structure choices if it's tenant-specific
    #     # if tenant and hasattr(FeeStructure, 'tenant_fk_field'):
    #     #     self.fields['fee_structure'].queryset = FeeStructure.objects.filter(tenant_fk_field=tenant, is_active=True)...
    #     # If schema-scoped, default queryset is fine.



from django import forms
from .models import Invoice, Student, AcademicYear, Term

class ManualInvoiceForm(forms.ModelForm):
    """
    A form for manually creating the main Invoice object.
    Financial details (subtotal, etc.) are excluded because they will be
    calculated from InvoiceDetail line items added in a later step.
    """
    class Meta:
        model = Invoice
        # These are the fields the user will fill out to create the invoice 'shell'
        fields = [
            'student',
            'academic_year',
            'term',
            'issue_date',
            'due_date',
            'notes_to_parent',
            'internal_notes',
        ]
        widgets = {
            'issue_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'due_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'notes_to_parent': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
            'internal_notes': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
            'student': forms.Select(attrs={'class': 'form-select'}),
            'academic_year': forms.Select(attrs={'class': 'form-select'}),
            'term': forms.Select(attrs={'class': 'form-select'}),
        }

    def __init__(self, *args, **kwargs):
        # The 'tenant' object is passed in from the view
        tenant = kwargs.pop('tenant', None)
        super().__init__(*args, **kwargs)

        # Smart Filtering: Only show students and academic years for the current tenant.
        # This is crucial for a SaaS application.
        if tenant:
            self.fields['student'].queryset = Student.objects.filter(is_active=True).order_by('first_name', 'last_name')
            self.fields['academic_year'].queryset = AcademicYear.objects.all().order_by('-start_date')
            # You might want to further filter terms based on the selected academic year,
            # which can be done with JavaScript or a library like htmx.
            self.fields['term'].queryset = Term.objects.all().order_by('academic_year__start_date', 'start_date')





