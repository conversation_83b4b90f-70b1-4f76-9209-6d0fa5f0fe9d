{% extends "tenant_base.html" %}
{% load humanize static core_tags %}

{% block title %}{{ view_title|default:"Tax Compliance Report" }}{% endblock %}

{% block content %}
<div class="container-fluid mt-3">
    {% with report_icon_class="bi bi-file-earmark-text" %}
        {% include "partials/_report_header.html" %}
    {% endwith %}

    {% include "partials/_messages.html" %}
    {% include "partials/_report_filter_form.html" with filter_form=filter_form report_code=report_code specific_filter_title="Tax Compliance Filters" %}

    <!-- Tax Compliance Overview -->
    <div class="card mb-4">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="bi bi-calculator me-2"></i>Tax Compliance Overview</h5>
        </div>
        <div class="card-body">
            <div class="row mb-3">
                <div class="col-md-12">
                    <p>
                        <strong>Period:</strong> {{ tax_summary.period_name }} | 
                        <strong>Tax Period:</strong> {{ tax_period|title }} | 
                        <strong>Compliance Level:</strong> {{ compliance_level|title }}
                    </p>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-3">
                    <div class="text-center">
                        <h6 class="text-muted">Total Revenue</h6>
                        <p class="mb-0 fw-bold text-primary fs-4">{{ tax_summary.total_revenue|currency }}</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h6 class="text-muted">Taxable Revenue</h6>
                        <p class="mb-0 fw-bold text-warning fs-4">{{ tax_summary.taxable_revenue|currency }}</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h6 class="text-muted">Exempt Revenue</h6>
                        <p class="mb-0 fw-bold text-success fs-4">{{ tax_summary.exempt_revenue|currency }}</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h6 class="text-muted">Tax Liability</h6>
                        <p class="mb-0 fw-bold {% if tax_summary.total_tax_liability > 0 %}text-danger{% else %}text-success{% endif %} fs-4">
                            {{ tax_summary.total_tax_liability|currency }}
                        </p>
                    </div>
                </div>
            </div>
            
            <!-- Tax Status Progress -->
            <div class="mt-3">
                <h6>Tax Exemption Status</h6>
                <div class="progress" style="height: 25px;">
                    <div class="progress-bar bg-success" role="progressbar" style="width: {{ tax_summary.exemption_percentage }}%">
                        Exempt: {{ tax_summary.exemption_percentage|floatformat:1 }}%
                    </div>
                    <div class="progress-bar bg-warning" role="progressbar" style="width: {% widthsubtract:100 tax_summary.exemption_percentage %}%">
                        Taxable: {% subtract:100 tax_summary.exemption_percentage|floatformat:1 %}%
                    </div>
                </div>
                <small class="text-muted">
                    {% if tax_summary.filing_required %}
                    Filing required by {{ tax_summary.next_filing_date }}
                    {% else %}
                    No filing required for this period
                    {% endif %}
                </small>
            </div>
        </div>
    </div>

    <!-- Compliance Status -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="bi bi-shield-check me-2"></i>Compliance Status</h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        <h4 class="{% if tax_summary.compliance_status == 'Compliant' %}text-success{% else %}text-warning{% endif %}">
                            <i class="bi {% if tax_summary.compliance_status == 'Compliant' %}bi-check-circle{% else %}bi-exclamation-triangle{% endif %} me-2"></i>
                            {{ tax_summary.compliance_status }}
                        </h4>
                    </div>
                    
                    {% if tax_summary.compliance_issues %}
                    <h6>Issues Requiring Attention:</h6>
                    <ul class="list-unstyled">
                        {% for issue in tax_summary.compliance_issues %}
                        <li><i class="bi bi-exclamation-triangle text-warning me-2"></i>{{ issue }}</li>
                        {% endfor %}
                    </ul>
                    {% else %}
                    <p class="text-success text-center">
                        <i class="bi bi-check-circle me-2"></i>No compliance issues identified
                    </p>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="bi bi-calendar-event me-2"></i>Filing Information</h5>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled">
                        <li><i class="bi bi-calendar text-primary me-2"></i>
                            <strong>Tax Period:</strong> {{ tax_summary.period_name }}
                        </li>
                        <li><i class="bi bi-file-earmark text-info me-2"></i>
                            <strong>Filing Required:</strong> {% if tax_summary.filing_required %}Yes{% else %}No{% endif %}
                        </li>
                        {% if tax_summary.filing_required %}
                        <li><i class="bi bi-clock text-warning me-2"></i>
                            <strong>Due Date:</strong> {{ tax_summary.next_filing_date }}
                        </li>
                        {% endif %}
                        <li><i class="bi bi-cash text-success me-2"></i>
                            <strong>Tax Liability:</strong> {{ tax_summary.total_tax_liability|currency }}
                        </li>
                        <li><i class="bi bi-shield text-success me-2"></i>
                            <strong>Exemptions:</strong> {{ tax_summary.total_exemptions|currency }}
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Tax Calculations -->
    {% if tax_summary.tax_calculations %}
    <div class="card mb-4">
        <div class="card-header bg-light d-flex justify-content-between align-items-center">
            <h5 class="mb-0"><i class="bi bi-calculator me-2"></i>Tax Calculations</h5>
            <span class="badge bg-primary">{{ tax_summary.tax_calculations|length }} tax types</span>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-striped table-hover mb-0">
                    <thead class="table-dark">
                        <tr>
                            <th>Tax Type</th>
                            <th class="text-end">Taxable Base</th>
                            <th class="text-center">Tax Rate</th>
                            <th class="text-end">Tax Liability</th>
                            <th class="text-end">Exemptions</th>
                            <th class="text-center">Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for calc in tax_summary.tax_calculations.values %}
                        <tr>
                            <td>
                                <strong>{{ calc.tax_type }}</strong>
                            </td>
                            <td class="text-end">
                                <span class="text-primary fw-bold">{{ calc.taxable_base|currency }}</span>
                            </td>
                            <td class="text-center">
                                <span class="badge bg-info">{{ calc.tax_rate|floatformat:1 }}%</span>
                            </td>
                            <td class="text-end">
                                <span class="fw-bold {% if calc.tax_liability > 0 %}text-danger{% else %}text-success{% endif %}">
                                    {{ calc.tax_liability|currency }}
                                </span>
                            </td>
                            <td class="text-end">
                                <span class="text-success">{{ calc.exemption_amount|currency }}</span>
                            </td>
                            <td class="text-center">
                                {% if calc.status == 'Exempt' %}
                                <span class="badge bg-success">{{ calc.status }}</span>
                                {% elif calc.status == 'Taxable' %}
                                <span class="badge bg-warning">{{ calc.status }}</span>
                                {% else %}
                                <span class="badge bg-secondary">{{ calc.status }}</span>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                    <tfoot class="table-light">
                        <tr>
                            <th>Totals:</th>
                            <th class="text-end">{{ tax_summary.taxable_revenue|currency }}</th>
                            <th></th>
                            <th class="text-end">{{ tax_summary.total_tax_liability|currency }}</th>
                            <th class="text-end">{{ tax_summary.total_exemptions|currency }}</th>
                            <th></th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Revenue Breakdown -->
    {% if revenue_breakdown %}
    <div class="card mb-4">
        <div class="card-header bg-light d-flex justify-content-between align-items-center">
            <h5 class="mb-0"><i class="bi bi-pie-chart me-2"></i>Revenue Breakdown by Fee Type</h5>
            <span class="badge bg-primary">{{ revenue_breakdown|length }} fee types</span>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-striped table-hover mb-0">
                    <thead class="table-dark">
                        <tr>
                            <th>Fee Type</th>
                            <th class="text-end">Total Revenue</th>
                            <th class="text-end">Taxable Revenue</th>
                            <th class="text-end">Exempt Revenue</th>
                            <th class="text-center">Tax Status</th>
                            <th class="text-center">Invoices</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for breakdown in revenue_breakdown %}
                        <tr class="{% if breakdown.is_exempt %}table-light{% endif %}">
                            <td>
                                <strong>{{ breakdown.fee_type }}</strong>
                            </td>
                            <td class="text-end">
                                <span class="fw-bold text-primary">{{ breakdown.total_revenue|currency }}</span>
                            </td>
                            <td class="text-end">
                                {% if breakdown.taxable_revenue > 0 %}
                                <span class="text-warning fw-bold">{{ breakdown.taxable_revenue|currency }}</span>
                                {% else %}
                                <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td class="text-end">
                                {% if breakdown.exempt_revenue > 0 %}
                                <span class="text-success fw-bold">{{ breakdown.exempt_revenue|currency }}</span>
                                {% else %}
                                <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td class="text-center">
                                {% if breakdown.is_exempt %}
                                <span class="badge bg-success">Exempt</span>
                                {% else %}
                                <span class="badge bg-warning">Taxable</span>
                                {% endif %}
                            </td>
                            <td class="text-center">
                                <span class="badge bg-info">{{ breakdown.invoice_count }}</span>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                    <tfoot class="table-light">
                        <tr>
                            <th>Totals:</th>
                            <th class="text-end">{{ tax_summary.total_revenue|currency }}</th>
                            <th class="text-end">{{ tax_summary.taxable_revenue|currency }}</th>
                            <th class="text-end">{{ tax_summary.exempt_revenue|currency }}</th>
                            <th colspan="2"></th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Tax Exemption Analysis -->
    {% if exemption_analysis %}
    <div class="card mb-4">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="bi bi-shield-check me-2"></i>Tax Exemption Analysis</h5>
        </div>
        <div class="card-body">
            {% for exemption in exemption_analysis %}
            <div class="mb-3">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="mb-1">{{ exemption.exemption_type }}</h6>
                        <small class="text-muted">{{ exemption.exemption_basis }}</small>
                    </div>
                    <div class="text-end">
                        <span class="fw-bold text-success">{{ exemption.exempt_amount|currency }}</span>
                        <br><small class="text-muted">Tax Saved: {{ exemption.tax_saved|currency }}</small>
                    </div>
                </div>
                <p class="mb-0 mt-2"><small class="text-info">{{ exemption.compliance_notes }}</small></p>
            </div>
            {% if not forloop.last %}<hr>{% endif %}
            {% endfor %}
        </div>
    </div>
    {% endif %}

    <!-- Tax Compliance Guidelines -->
    <div class="card">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="bi bi-info-circle me-2"></i>Tax Compliance Guidelines</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h6>Educational Institution Tax Benefits</h6>
                    <ul class="list-unstyled">
                        <li><i class="bi bi-check-circle text-success me-2"></i>Educational services typically exempt from income tax</li>
                        <li><i class="bi bi-check-circle text-success me-2"></i>Tuition and academic fees often VAT/sales tax exempt</li>
                        <li><i class="bi bi-check-circle text-success me-2"></i>Library and laboratory fees usually exempt</li>
                        <li><i class="bi bi-info-circle text-info me-2"></i>Non-educational services may be taxable</li>
                        <li><i class="bi bi-exclamation-triangle text-warning me-2"></i>Consult tax professional for specific regulations</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6>Compliance Recommendations</h6>
                    <ul class="list-unstyled">
                        <li><i class="bi bi-arrow-right text-primary me-2"></i>Maintain detailed records of all revenue sources</li>
                        <li><i class="bi bi-arrow-right text-primary me-2"></i>Clearly categorize educational vs non-educational services</li>
                        <li><i class="bi bi-arrow-right text-primary me-2"></i>Document exemption basis for all exempt revenue</li>
                        <li><i class="bi bi-arrow-right text-primary me-2"></i>File required returns even if no tax is due</li>
                        <li><i class="bi bi-arrow-right text-primary me-2"></i>Review tax status annually with qualified professional</li>
                    </ul>
                </div>
            </div>
            
            <hr>
            
            <div class="alert {% if tax_summary.compliance_status == 'Compliant' %}alert-success{% else %}alert-warning{% endif %}">
                <h6 class="alert-heading">
                    <i class="bi {% if tax_summary.compliance_status == 'Compliant' %}bi-check-circle{% else %}bi-exclamation-triangle{% endif %} me-2"></i>
                    Tax Compliance Status: {{ tax_summary.compliance_status }}
                </h6>
                <p class="mb-0">
                    {% if tax_summary.compliance_status == 'Compliant' %}
                    Your institution appears to be in compliance with tax regulations. Continue monitoring and maintaining proper documentation.
                    {% else %}
                    Some areas require attention. Review the compliance issues above and consult with a tax professional as needed.
                    {% endif %}
                    {% if tax_summary.filing_required %}
                    <strong>Important:</strong> Tax filing is required by {{ tax_summary.next_filing_date }}.
                    {% endif %}
                </p>
            </div>
        </div>
    </div>

    <!-- No Data Message -->
    {% if not tax_summary %}
    <div class="card">
        <div class="card-body text-center py-5">
            <i class="bi bi-file-earmark-text display-1 text-muted mb-3"></i>
            <h4 class="text-muted">No Tax Data Available</h4>
            <p class="text-muted">No tax compliance data found for the selected criteria.</p>
            <div class="mt-3">
                <a href="{% url 'reporting:tax_compliance_report' %}" class="btn btn-outline-primary me-2">
                    <i class="bi bi-arrow-clockwise me-2"></i>Reset Filters
                </a>
            </div>
        </div>
    </div>
    {% endif %}

</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Animate progress bars
    $('.progress-bar').each(function() {
        var $this = $(this);
        var width = $this.css('width');
        $this.css('width', '0%').animate({width: width}, 1500);
    });
    
    // Highlight exempt revenue rows
    $('.table-light').hover(
        function() {
            $(this).addClass('table-active');
        },
        function() {
            $(this).removeClass('table-active');
        }
    );
    
    // Add click handlers for revenue breakdown rows
    $('tbody tr').on('click', function() {
        $(this).toggleClass('table-info');
    });
});
</script>
{% endblock %}
