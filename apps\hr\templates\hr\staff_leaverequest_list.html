{# D:\school_fees_saas_v2\apps\hr\templates\hr\staff_leaverequest_list.html #}
{% extends "tenant_base.html" %}
{% load i18n %}

{% block title %}My Leave Requests{% endblock %}

{% block tenant_specific_content %}
<div class="container mt-4">
    <div class="pagetitle mb-3">
        <h1>My Leave Requests</h1>
        <nav>
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'schools:dashboard' %}">Dashboard</a></li>
                <li class="breadcrumb-item active">My Leave Requests</li>
            </ol>
        </nav>
    </div>

    {% include "partials/_messages.html" %}

    <div class="d-flex justify-content-between align-items-center mb-3">
        <h2>My Submitted Requests</h2>
        <a href="{% url 'hr:staff_leaverequest_apply' %}" class="btn btn-primary">
            <i class="bi bi-plus-circle me-1"></i> Apply for Leave
        </a>
    </div>

    <div class="card">
        <div class="card-body">
            {% if leave_requests %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Leave Type</th>
                            <th>Start Date</th>
                            <th>End Date</th>
                            <th>Days</th>
                            <th>Status</th>
                            <th>Submitted On</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for lr in leave_requests %}
                        <tr>
                            <td>{{ lr.leave_type.name }}</td>
                            <td>{{ lr.start_date|date:"d M Y" }}</td>
                            <td>{{ lr.end_date|date:"d M Y" }}</td>
                            <td>{{ lr.duration|floatformat:1 }}</td>
                            <td>
                                <span class="badge bg-{{ lr.get_status_badge_class }}">{{ lr.get_status_display }}</span>
                            </td>
                            <td>{{ lr.created_at|date:"d M Y H:i" }}</td>
                            <td>
                                <a href="{% url 'hr:staff_leaverequest_detail' lr.pk %}" class="btn btn-sm btn-outline-info me-1"><i class="bi bi-eye"></i> View</a>
                                {% if lr.status == 'PENDING' %}
                                <a href="{% url 'hr:staff_leaverequest_cancel' lr.pk %}" class="btn btn-sm btn-outline-warning"><i class="bi bi-x-circle"></i> Cancel</a>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            {% include "partials/_pagination.html" %}
            
            {% else %}
            <div class="alert alert-info">
                <i class="bi bi-info-circle me-2"></i>
                You have not submitted any leave requests yet.
                <a href="{% url 'hr:staff_leaverequest_apply' %}" class="btn btn-primary btn-sm ms-2">Apply for Leave</a>
            </div>
            {% endif %}
        </div>
    </div>

    <div class="mt-3">
        <a href="{% url 'schools:dashboard' %}" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left-circle me-1"></i> Back to Dashboard
        </a>
    </div>
</div>
{% endblock %}
