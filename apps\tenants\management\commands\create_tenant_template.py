"""
Create Tenant Template
Creates a proper tenant template system that ensures new tenants get correct structure

Usage: python manage.py create_tenant_template
"""

from django.core.management.base import BaseCommand
from django.db import connection
from django.core.management import call_command
from apps.tenants.models import School
from django_tenants.utils import schema_context
import logging

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Create a tenant template system for consistent new tenant creation'

    def add_arguments(self, parser):
        parser.add_argument(
            '--create-template',
            action='store_true',
            help='Create the tenant template from alpha schema',
        )
        parser.add_argument(
            '--apply-template',
            type=str,
            help='Apply template to specific tenant schema',
        )

    def handle(self, *args, **options):
        if options['create_template']:
            self.create_template_from_alpha()
        elif options['apply_template']:
            self.apply_template_to_tenant(options['apply_template'])
        else:
            self.stdout.write("Use --create-template or --apply-template=<schema_name>")

    def create_template_from_alpha(self):
        """Create tenant template from alpha schema"""
        
        self.stdout.write("=== CREATING TENANT TEMPLATE FROM ALPHA ===")
        
        try:
            with connection.cursor() as cursor:
                cursor.execute('SET search_path TO "alpha"')
                
                # Get all table structures from alpha
                cursor.execute("""
                    SELECT table_name 
                    FROM information_schema.tables 
                    WHERE table_schema = 'alpha' 
                    AND table_type = 'BASE TABLE'
                    ORDER BY table_name
                """)
                
                tables = [row[0] for row in cursor.fetchall()]
                
                # Create template SQL file
                template_sql = []
                template_sql.append("-- Tenant Template SQL")
                template_sql.append("-- Generated from alpha schema")
                template_sql.append("-- Use this to create consistent new tenants")
                template_sql.append("")
                
                for table in tables:
                    # Get CREATE TABLE statement
                    cursor.execute(f"""
                        SELECT 
                            'CREATE TABLE IF NOT EXISTS ' || table_name || ' (' ||
                            string_agg(
                                column_name || ' ' || 
                                CASE 
                                    WHEN data_type = 'character varying' THEN 
                                        CASE WHEN character_maximum_length IS NOT NULL 
                                        THEN 'VARCHAR(' || character_maximum_length || ')'
                                        ELSE 'VARCHAR(255)' END
                                    WHEN data_type = 'character' THEN 'CHAR(' || character_maximum_length || ')'
                                    WHEN data_type = 'numeric' THEN 'NUMERIC(' || numeric_precision || ',' || numeric_scale || ')'
                                    WHEN data_type = 'integer' THEN 'INTEGER'
                                    WHEN data_type = 'bigint' THEN 'BIGINT'
                                    WHEN data_type = 'boolean' THEN 'BOOLEAN'
                                    WHEN data_type = 'timestamp with time zone' THEN 'TIMESTAMP WITH TIME ZONE'
                                    WHEN data_type = 'timestamp without time zone' THEN 'TIMESTAMP'
                                    WHEN data_type = 'date' THEN 'DATE'
                                    WHEN data_type = 'time without time zone' THEN 'TIME'
                                    WHEN data_type = 'text' THEN 'TEXT'
                                    WHEN data_type = 'double precision' THEN 'DOUBLE PRECISION'
                                    WHEN data_type = 'real' THEN 'REAL'
                                    WHEN data_type = 'smallint' THEN 'SMALLINT'
                                    WHEN data_type = 'uuid' THEN 'UUID'
                                    ELSE UPPER(data_type)
                                END ||
                                CASE WHEN is_nullable = 'NO' THEN ' NOT NULL' ELSE '' END ||
                                CASE 
                                    WHEN column_default IS NOT NULL AND column_default NOT LIKE 'nextval%' 
                                    THEN ' DEFAULT ' || column_default 
                                    ELSE '' 
                                END,
                                ', ' ORDER BY ordinal_position
                            ) || ');'
                        FROM information_schema.columns 
                        WHERE table_name = %s 
                        AND table_schema = 'alpha'
                        GROUP BY table_name
                    """, [table])
                    
                    result = cursor.fetchone()
                    if result:
                        template_sql.append(f"-- Table: {table}")
                        template_sql.append(result[0])
                        
                        # Add sequence if needed
                        if any(col[0] == 'id' for col in self.get_table_columns(cursor, table)):
                            template_sql.append(f"CREATE SEQUENCE IF NOT EXISTS {table}_id_seq;")
                            template_sql.append(f"ALTER TABLE {table} ALTER COLUMN id SET DEFAULT nextval('{table}_id_seq');")
                        
                        template_sql.append("")
                
                # Add essential system data
                template_sql.append("-- Essential system data")
                template_sql.append(self.get_essential_data_sql())
                
                # Write template file
                template_file = "tenant_template.sql"
                with open(template_file, 'w') as f:
                    f.write('\n'.join(template_sql))
                
                self.stdout.write(
                    self.style.SUCCESS(f"✅ Tenant template created: {template_file}")
                )
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"❌ Failed to create template: {e}")
            )

    def get_table_columns(self, cursor, table):
        """Get columns for a table"""
        cursor.execute(f"""
            SELECT column_name, data_type
            FROM information_schema.columns 
            WHERE table_name = '{table}' 
            AND table_schema = 'alpha'
            ORDER BY ordinal_position
        """)
        return cursor.fetchall()

    def get_essential_data_sql(self):
        """Get SQL for essential system data"""
        return """
-- Essential system data for new tenants
INSERT INTO schools_academicyear (name, start_date, end_date, is_active, is_current, created_at, updated_at) 
VALUES ('Academic Year', CURRENT_DATE, CURRENT_DATE + INTERVAL '1 year', TRUE, TRUE, NOW(), NOW())
ON CONFLICT DO NOTHING;

INSERT INTO schools_academicsetting (academic_year_id, created_at, updated_at) 
VALUES (1, NOW(), NOW())
ON CONFLICT DO NOTHING;

INSERT INTO schools_invoicesequence (prefix, current_number, created_at, updated_at) 
VALUES ('INV', 1, NOW(), NOW())
ON CONFLICT DO NOTHING;

INSERT INTO schools_receiptsequence (prefix, current_number, created_at, updated_at) 
VALUES ('RCP', 1, NOW(), NOW())
ON CONFLICT DO NOTHING;

-- Essential reference data
INSERT INTO fees_concessiontype (name, description, is_percentage, default_value, is_active, created_at, updated_at) VALUES
('Sibling Discount', 'Discount for siblings', TRUE, 10.00, TRUE, NOW(), NOW()),
('Staff Child Discount', 'Discount for staff children', TRUE, 50.00, TRUE, NOW(), NOW()),
('Merit Scholarship', 'Merit-based scholarship', TRUE, 25.00, TRUE, NOW(), NOW())
ON CONFLICT DO NOTHING;

INSERT INTO hr_leavetype (name, description, max_days_per_year, is_paid, requires_approval, is_active, created_at, updated_at) VALUES
('Annual Leave', 'Annual vacation leave', 21, TRUE, TRUE, TRUE, NOW(), NOW()),
('Sick Leave', 'Medical leave', 10, TRUE, FALSE, TRUE, NOW(), NOW()),
('Maternity Leave', 'Maternity leave', 90, TRUE, TRUE, TRUE, NOW(), NOW()),
('Emergency Leave', 'Emergency leave', 3, FALSE, TRUE, TRUE, NOW(), NOW())
ON CONFLICT DO NOTHING;

INSERT INTO payments_paymentmethod (name, description, is_active, created_at, updated_at) VALUES
('Cash', 'Cash payment', TRUE, NOW(), NOW()),
('Bank Transfer', 'Bank transfer payment', TRUE, NOW(), NOW()),
('Mobile Money', 'Mobile money payment', TRUE, NOW(), NOW()),
('Cheque', 'Cheque payment', TRUE, NOW(), NOW())
ON CONFLICT DO NOTHING;

INSERT INTO school_calendar_eventcategory (name, color, icon, description, is_active, created_at, updated_at) VALUES
('Academic', '#007bff', 'bi-book', 'Academic events', TRUE, NOW(), NOW()),
('Sports', '#28a745', 'bi-trophy', 'Sports events', TRUE, NOW(), NOW()),
('Cultural', '#ffc107', 'bi-music-note', 'Cultural events', TRUE, NOW(), NOW()),
('Meeting', '#6c757d', 'bi-people', 'Meetings', TRUE, NOW(), NOW()),
('Holiday', '#dc3545', 'bi-calendar-x', 'Holidays', TRUE, NOW(), NOW())
ON CONFLICT DO NOTHING;
"""

    def apply_template_to_tenant(self, tenant_schema):
        """Apply template to a specific tenant"""
        
        self.stdout.write(f"=== APPLYING TEMPLATE TO {tenant_schema} ===")
        
        try:
            # Check if template file exists
            template_file = "tenant_template.sql"
            if not os.path.exists(template_file):
                self.stdout.write(
                    self.style.ERROR("❌ Template file not found. Run --create-template first.")
                )
                return
            
            # Apply template
            with connection.cursor() as cursor:
                cursor.execute(f'SET search_path TO "{tenant_schema}"')
                
                # Read and execute template
                with open(template_file, 'r') as f:
                    template_sql = f.read()
                
                # Split into individual statements and execute
                statements = [stmt.strip() for stmt in template_sql.split(';') if stmt.strip() and not stmt.strip().startswith('--')]
                
                for statement in statements:
                    if statement:
                        try:
                            cursor.execute(statement)
                        except Exception as e:
                            self.stdout.write(f"⚠️  Statement failed: {e}")
                
                self.stdout.write(
                    self.style.SUCCESS(f"✅ Template applied to {tenant_schema}")
                )
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"❌ Failed to apply template: {e}")
            )
