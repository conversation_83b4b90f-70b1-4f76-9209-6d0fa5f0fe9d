#!/usr/bin/env python
"""
Fix Zenith Complete
Complete fix for zenith tenant

Usage: python fix_zenith_complete.py
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from django.db import connection
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def fix_zenith_complete():
    """Complete fix for zenith tenant"""
    
    logger.info("=== FIXING ZENITH COMPLETELY ===")
    
    try:
        with connection.cursor() as cursor:
            cursor.execute('SET search_path TO "zenith"')
            
            # Fix invoice sequence table
            try:
                cursor.execute("ALTER TABLE schools_invoicesequence ADD COLUMN created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()")
                logger.info("✅ Added created_at to schools_invoicesequence")
            except Exception as e:
                if "already exists" in str(e):
                    logger.info("✅ created_at already exists in schools_invoicesequence")
                else:
                    logger.error(f"❌ Error adding created_at to schools_invoicesequence: {e}")
            
            try:
                cursor.execute("ALTER TABLE schools_invoicesequence ADD COLUMN updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()")
                logger.info("✅ Added updated_at to schools_invoicesequence")
            except Exception as e:
                if "already exists" in str(e):
                    logger.info("✅ updated_at already exists in schools_invoicesequence")
                else:
                    logger.error(f"❌ Error adding updated_at to schools_invoicesequence: {e}")
            
            # Fix receipt sequence table
            try:
                cursor.execute("ALTER TABLE schools_receiptsequence ADD COLUMN created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()")
                logger.info("✅ Added created_at to schools_receiptsequence")
            except Exception as e:
                if "already exists" in str(e):
                    logger.info("✅ created_at already exists in schools_receiptsequence")
                else:
                    logger.error(f"❌ Error adding created_at to schools_receiptsequence: {e}")
            
            try:
                cursor.execute("ALTER TABLE schools_receiptsequence ADD COLUMN updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()")
                logger.info("✅ Added updated_at to schools_receiptsequence")
            except Exception as e:
                if "already exists" in str(e):
                    logger.info("✅ updated_at already exists in schools_receiptsequence")
                else:
                    logger.error(f"❌ Error adding updated_at to schools_receiptsequence: {e}")
            
            # Fix academic settings table
            try:
                cursor.execute("ALTER TABLE schools_academicsetting ADD COLUMN academic_year_id INTEGER")
                logger.info("✅ Added academic_year_id to schools_academicsetting")
            except Exception as e:
                if "already exists" in str(e):
                    logger.info("✅ academic_year_id already exists in schools_academicsetting")
                else:
                    logger.error(f"❌ Error adding academic_year_id to schools_academicsetting: {e}")
            
            # Now insert the sequence data
            cursor.execute("SELECT COUNT(*) FROM schools_invoicesequence")
            if cursor.fetchone()[0] == 0:
                cursor.execute("INSERT INTO schools_invoicesequence (prefix, current_number, created_at, updated_at) VALUES ('INV', 1, NOW(), NOW())")
                logger.info("✅ Created invoice sequence data")
            else:
                logger.info("✅ Invoice sequence data already exists")
            
            cursor.execute("SELECT COUNT(*) FROM schools_receiptsequence")
            if cursor.fetchone()[0] == 0:
                cursor.execute("INSERT INTO schools_receiptsequence (prefix, current_number, created_at, updated_at) VALUES ('RCP', 1, NOW(), NOW())")
                logger.info("✅ Created receipt sequence data")
            else:
                logger.info("✅ Receipt sequence data already exists")
            
            cursor.execute("SELECT COUNT(*) FROM schools_academicsetting")
            if cursor.fetchone()[0] == 0:
                cursor.execute("INSERT INTO schools_academicsetting (academic_year_id, created_at, updated_at) VALUES (1, NOW(), NOW())")
                logger.info("✅ Created academic settings data")
            else:
                logger.info("✅ Academic settings data already exists")
            
            # Verify zenith is working
            logger.info("Verifying zenith tenant...")
            
            # Check essential tables
            essential_tables = [
                'schools_academicyear', 'schools_staffuser', 'students_student',
                'schools_schoolprofile', 'auth_group', 'auth_permission'
            ]
            
            missing_tables = []
            for table in essential_tables:
                cursor.execute(f"""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_schema = 'zenith' 
                        AND table_name = '{table}'
                    )
                """)
                
                if not cursor.fetchone()[0]:
                    missing_tables.append(table)
            
            if missing_tables:
                logger.warning(f"⚠️  Missing tables in zenith: {missing_tables}")
            else:
                logger.info("✅ All essential tables present in zenith")
            
            # Check essential data
            cursor.execute("SELECT COUNT(*) FROM schools_academicyear")
            ay_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM hr_leavetype")
            lt_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM payments_paymentmethod")
            pm_count = cursor.fetchone()[0]
            
            logger.info(f"✅ Data counts - Academic Years: {ay_count}, Leave Types: {lt_count}, Payment Methods: {pm_count}")
            
            if ay_count > 0 and lt_count > 0 and pm_count > 0:
                logger.info("✅ Zenith tenant is fully functional!")
            else:
                logger.warning("⚠️  Zenith tenant may have data issues")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to fix zenith completely: {e}")
        return False

def main():
    """Main function"""
    logger.info("=== FIX ZENITH COMPLETE ===")
    
    try:
        success = fix_zenith_complete()
        
        if success:
            logger.info("\n🎉 ZENITH COMPLETELY FIXED!")
            logger.info("Zenith tenant should now work without errors!")
        else:
            logger.error("\n❌ ZENITH COMPLETE FIX FAILED!")
        
        return success
        
    except Exception as e:
        logger.error(f"Fix zenith complete failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
