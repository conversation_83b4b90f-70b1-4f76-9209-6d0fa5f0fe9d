from django.contrib.auth.base_user import BaseUserManager
from django.utils.translation import gettext_lazy as _

class StaffUserManager(BaseUserManager):
    """
    Custom user manager for the StaffUser model, where email is the
    unique identifier for authentication instead of a username.
    """
    use_in_migrations = True # Good practice to include this

    def _create_user(self, email, password, **extra_fields):
        """
        Private helper to create and save a user with the given email and password.
        """
        if not email:
            raise ValueError(_('The Email must be set'))
        
        email = self.normalize_email(email)
        # The self.model() call gets the model class this manager is attached to (i.e., StaffUser)
        user = self.model(email=email, **extra_fields)
        user.set_password(password)
        user.save(using=self._db)
        return user

    def create_user(self, email, password=None, **extra_fields):
        """
        Creates and saves a regular StaffUser with the given email and password.
        """
        # Regular staff should not be superusers by default.
        extra_fields.setdefault('is_staff', True) # Staff users should be staff
        extra_fields.setdefault('is_superuser', False)
        return self._create_user(email, password, **extra_fields)

    def create_superuser(self, email, password, **extra_fields):
        """
        Creates and saves a staff SuperUser with the given email and password.
        This is used by the 'createtenantsuperuser' command.
        """
        extra_fields.setdefault('is_staff', True)
        extra_fields.setdefault('is_superuser', True)
        extra_fields.setdefault('is_active', True)

        # --- VALIDATION CHECKS ---
        if extra_fields.get('is_staff') is not True:
            raise ValueError(_('Superuser must have is_staff=True.'))
        if extra_fields.get('is_superuser') is not True:
            raise ValueError(_('Superuser must have is_superuser=True.'))
        
        return self._create_user(email, password, **extra_fields)
    
    
    