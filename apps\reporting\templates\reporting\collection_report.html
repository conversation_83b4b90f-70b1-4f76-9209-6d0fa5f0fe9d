{% extends "tenant_base.html" %}
{% load humanize static %}

{% comment %} {% block tenant_page_title %}{{ view_title }}{% endblock %} {% endcomment %}
{% block tenant_page_title %}{{ view_title|default:"Collection Report" }}{% endblock %}

{% block tenant_specific_content %}
<div class="container-fluid mt-3">
    {% with report_icon_class="bi bi-collection-fill" %} {# Define icon for this specific report #}
        {% include "partials/_report_header.html" %}
    {% endwith %}

{% comment %} <div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h1 class="h3 mb-0 text-gray-800">{{ view_title }}</h1>
    </div> {% endcomment %}

    {% include "partials/_messages.html" %}

    {% include "partials/_report_filter_form.html" with filter_form=filter_form report_code=report_code specific_filter_title="Expense Filters" %}



    <div class="card shadow-sm">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Payment Records</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered table-striped table-hover" width="100%" cellspacing="0">
                    <thead class="table-light">
                        <tr>
                            <th>Date</th>
                            <th>Receipt No.</th>
                            <th>Student Name</th>
                            <th>Adm. No.</th>
                            <th>Class</th>
                            <th>Invoice No.</th>
                            <th class="text-end">Amount Paid</th>
                            <th>Method</th>
                            <th>Received By</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for payment in payments_on_page %} {# context_object_name #}
                        <tr>
                            <td>{{ payment.payment_date|date:"d M Y" }}</td>
                            <td>{{ payment.receipt_number_display }}</td>
                            <td>
                                {% if payment.student %}
                                    {{ payment.student.full_name }}
                                {% elif payment.parent_payer %}
                                    {{ payment.parent_payer.get_full_name }}
                                {% else %}
                                    N/A
                                {% endif %}
                            </td>
                            <td>
                                {% if payment.student %}
                                    {{ payment.student.admission_number }}
                                {% else %}
                                    N/A
                                {% endif %}
                            </td>
                            <td>
                                {% if payment.student %}
                                    {{ payment.student.current_class.name|default:"N/A" }}
                                {% else %}
                                    N/A
                                {% endif %}
                            </td>
                            <td>
                                {% if payment.allocations.exists %}
                                    {% for allocation in payment.allocations.all %}
                                        <a href="#">{{ allocation.invoice.invoice_number }}</a>{% if not forloop.last %}, {% endif %}
                                    {% endfor %}
                                {% else %}
                                    Direct Payment
                                {% endif %}
                            </td>
                            <td class="text-end fw-bold">{{ payment.amount|floatformat:2|intcomma }}</td>
                            <td>{{ payment.payment_method.name|default:"N/A" }}</td>
                            <td>
                                {% if payment.processed_by_staff %}
                                    {{ payment.processed_by_staff.get_full_name|default:"Staff User" }}
                                {% elif payment.created_by %}
                                    {{ payment.created_by.get_full_name|default:"System User" }}
                                {% else %}
                                    System
                                {% endif %}
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="9" class="text-center py-4">No payments found matching your criteria.</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                    {% if payments_report %}
                    <tfoot>
                        <tr class="table-light">
                            <th colspan="6" class="text-end fw-bold">Total Collected (All Filtered Pages):</th>
                            <th class="text-end fw-bolder fs-5">{{ total_collected_all_pages|default:"0.00"|floatformat:2|intcomma }}</th>
                            <th colspan="2"></th>
                        </tr>
                    </tfoot>
                    {% endif %}
                </table>
            </div>
            {% include "partials/_pagination.html" %} {# Assuming a generic pagination partial #}
        </div>
    </div>
</div>
{% endblock %}






{% comment %} {% extends "tenant_base.html" %}
{% load static core_tags humanize %}

{% block title %}{{ view_title|default:"Collection Report" }}{% endblock %}

{% block content %}
<div class="container mt-4">
    <h1>{{ view_title|default:"Collection Report" }}</h1>
    <p class="lead">Report content will be displayed here.</p>
    {# Add filter form and table later #}
    <hr>
    <a href="{% url 'schools:dashboard' %}" class="btn btn-secondary">Back to Dashboard</a>
</div>
{% endblock %}
 {% endcomment %}
